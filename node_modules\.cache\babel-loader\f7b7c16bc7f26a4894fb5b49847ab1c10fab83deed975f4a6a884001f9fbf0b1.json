{"ast": null, "code": "import { initCustomFormatter, warn } from '@vue/runtime-dom';\nexport * from '@vue/runtime-dom';\nfunction initDev() {\n  {\n    initCustomFormatter();\n  }\n}\n\n// This entry exports the runtime only, and is built as\nif (process.env.NODE_ENV !== 'production') {\n  initDev();\n}\nvar compile = function compile() {\n  if (process.env.NODE_ENV !== 'production') {\n    warn(\"Runtime compilation is not supported in this build of Vue.\" + \" Configure your bundler to alias \\\"vue\\\" to \\\"vue/dist/vue.esm-bundler.js\\\".\" /* should not happen */);\n  }\n};\n\nexport { compile };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}