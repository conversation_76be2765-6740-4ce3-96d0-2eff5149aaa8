{"ast": null, "code": "function _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return _typeof(key) === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (_typeof(input) !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (_typeof(res) !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\nfunction _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nfunction _iterableToArrayLimit(arr, i) { var _i = null == arr ? null : \"undefined\" != typeof Symbol && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (null != _i) { var _s, _e, _x, _r, _arr = [], _n = !0, _d = !1; try { if (_x = (_i = _i.call(arr)).next, 0 === i) { if (Object(_i) !== _i) return; _n = !1; } else for (; !(_n = (_s = _x.call(_i)).done) && (_arr.push(_s.value), _arr.length !== i); _n = !0); } catch (err) { _d = !0, _e = err; } finally { try { if (!_n && null != _i.return && (_r = _i.return(), Object(_r) !== _r)) return; } finally { if (_d) throw _e; } } return _arr; } }\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\nimport { createVNode as _createVNode } from \"vue\";\nimport { ref, computed, defineComponent } from \"vue\";\nimport { isDef, truthProp, numericProp, windowHeight, makeStringProp, makeNumericProp, createNamespace, HAPTICS_FEEDBACK } from \"../utils/index.mjs\";\nimport { useId } from \"../composables/use-id.mjs\";\nimport { useRect, useChildren, useClickAway, useScrollParent, useEventListener } from \"@vant/use\";\nvar _createNamespace = createNamespace(\"dropdown-menu\"),\n  _createNamespace2 = _slicedToArray(_createNamespace, 2),\n  name = _createNamespace2[0],\n  bem = _createNamespace2[1];\nvar dropdownMenuProps = {\n  overlay: truthProp,\n  zIndex: numericProp,\n  duration: makeNumericProp(0.2),\n  direction: makeStringProp(\"down\"),\n  activeColor: String,\n  closeOnClickOutside: truthProp,\n  closeOnClickOverlay: truthProp\n};\nvar DROPDOWN_KEY = Symbol(name);\nvar stdin_default = defineComponent({\n  name: name,\n  props: dropdownMenuProps,\n  setup: function setup(props, _ref) {\n    var slots = _ref.slots;\n    var id = useId();\n    var root = ref();\n    var barRef = ref();\n    var offset = ref(0);\n    var _useChildren = useChildren(DROPDOWN_KEY),\n      children = _useChildren.children,\n      linkChildren = _useChildren.linkChildren;\n    var scrollParent = useScrollParent(root);\n    var opened = computed(function () {\n      return children.some(function (item) {\n        return item.state.showWrapper;\n      });\n    });\n    var barStyle = computed(function () {\n      if (opened.value && isDef(props.zIndex)) {\n        return {\n          zIndex: +props.zIndex + 1\n        };\n      }\n    });\n    var onClickAway = function onClickAway() {\n      if (props.closeOnClickOutside) {\n        children.forEach(function (item) {\n          item.toggle(false);\n        });\n      }\n    };\n    var updateOffset = function updateOffset() {\n      if (barRef.value) {\n        var rect = useRect(barRef);\n        if (props.direction === \"down\") {\n          offset.value = rect.bottom;\n        } else {\n          offset.value = windowHeight.value - rect.top;\n        }\n      }\n    };\n    var onScroll = function onScroll() {\n      if (opened.value) {\n        updateOffset();\n      }\n    };\n    var toggleItem = function toggleItem(active) {\n      children.forEach(function (item, index) {\n        if (index === active) {\n          updateOffset();\n          item.toggle();\n        } else if (item.state.showPopup) {\n          item.toggle(false, {\n            immediate: true\n          });\n        }\n      });\n    };\n    var renderTitle = function renderTitle(item, index) {\n      var showPopup = item.state.showPopup;\n      var disabled = item.disabled,\n        titleClass = item.titleClass;\n      return _createVNode(\"div\", {\n        \"id\": \"\".concat(id, \"-\").concat(index),\n        \"role\": \"button\",\n        \"tabindex\": disabled ? void 0 : 0,\n        \"class\": [bem(\"item\", {\n          disabled: disabled\n        }), _defineProperty({}, HAPTICS_FEEDBACK, !disabled)],\n        \"onClick\": function onClick() {\n          if (!disabled) {\n            toggleItem(index);\n          }\n        }\n      }, [_createVNode(\"span\", {\n        \"class\": [bem(\"title\", {\n          down: showPopup === (props.direction === \"down\"),\n          active: showPopup\n        }), titleClass],\n        \"style\": {\n          color: showPopup ? props.activeColor : \"\"\n        }\n      }, [_createVNode(\"div\", {\n        \"class\": \"van-ellipsis\"\n      }, [item.renderTitle()])])]);\n    };\n    linkChildren({\n      id: id,\n      props: props,\n      offset: offset\n    });\n    useClickAway(root, onClickAway);\n    useEventListener(\"scroll\", onScroll, {\n      target: scrollParent,\n      passive: true\n    });\n    return function () {\n      var _a;\n      return _createVNode(\"div\", {\n        \"ref\": root,\n        \"class\": bem()\n      }, [_createVNode(\"div\", {\n        \"ref\": barRef,\n        \"style\": barStyle.value,\n        \"class\": bem(\"bar\", {\n          opened: opened.value\n        })\n      }, [children.map(renderTitle)]), (_a = slots.default) == null ? void 0 : _a.call(slots)]);\n    };\n  }\n});\nexport { DROPDOWN_KEY, stdin_default as default };", "map": {"version": 3, "names": ["createVNode", "_createVNode", "ref", "computed", "defineComponent", "isDef", "truthProp", "numericProp", "windowHeight", "makeStringProp", "makeNumericProp", "createNamespace", "HAPTICS_FEEDBACK", "useId", "useRect", "useChildren", "useClickAway", "useScrollParent", "useEventListener", "_createNamespace", "_createNamespace2", "_slicedToArray", "name", "bem", "dropdownMenuProps", "overlay", "zIndex", "duration", "direction", "activeColor", "String", "closeOnClickOutside", "closeOnClickOverlay", "DROPDOWN_KEY", "Symbol", "stdin_default", "props", "setup", "_ref", "slots", "id", "root", "barRef", "offset", "_useChildren", "children", "linkChildren", "scrollParent", "opened", "some", "item", "state", "showWrapper", "barStyle", "value", "onClickAway", "for<PERSON>ach", "toggle", "updateOffset", "rect", "bottom", "top", "onScroll", "toggleItem", "active", "index", "showPopup", "immediate", "renderTitle", "disabled", "titleClass", "concat", "_defineProperty", "onClick", "down", "color", "target", "passive", "_a", "map", "default", "call"], "sources": ["C:/Users/<USER>/Downloads/Telegram Desktop/vue3_3.0/vue3_3.0/node_modules/vant/es/dropdown-menu/DropdownMenu.mjs"], "sourcesContent": ["import { createVNode as _createVNode } from \"vue\";\nimport { ref, computed, defineComponent } from \"vue\";\nimport { isDef, truthProp, numericProp, windowHeight, makeStringProp, makeNumericProp, createNamespace, HAPTICS_FEEDBACK } from \"../utils/index.mjs\";\nimport { useId } from \"../composables/use-id.mjs\";\nimport { useRect, useChildren, useClickAway, useScrollParent, useEventListener } from \"@vant/use\";\nconst [name, bem] = createNamespace(\"dropdown-menu\");\nconst dropdownMenuProps = {\n  overlay: truthProp,\n  zIndex: numericProp,\n  duration: makeNumericProp(0.2),\n  direction: makeStringProp(\"down\"),\n  activeColor: String,\n  closeOnClickOutside: truthProp,\n  closeOnClickOverlay: truthProp\n};\nconst DROPDOWN_KEY = Symbol(name);\nvar stdin_default = defineComponent({\n  name,\n  props: dropdownMenuProps,\n  setup(props, {\n    slots\n  }) {\n    const id = useId();\n    const root = ref();\n    const barRef = ref();\n    const offset = ref(0);\n    const {\n      children,\n      linkChildren\n    } = useChildren(DROPDOWN_KEY);\n    const scrollParent = useScrollParent(root);\n    const opened = computed(() => children.some((item) => item.state.showWrapper));\n    const barStyle = computed(() => {\n      if (opened.value && isDef(props.zIndex)) {\n        return {\n          zIndex: +props.zIndex + 1\n        };\n      }\n    });\n    const onClickAway = () => {\n      if (props.closeOnClickOutside) {\n        children.forEach((item) => {\n          item.toggle(false);\n        });\n      }\n    };\n    const updateOffset = () => {\n      if (barRef.value) {\n        const rect = useRect(barRef);\n        if (props.direction === \"down\") {\n          offset.value = rect.bottom;\n        } else {\n          offset.value = windowHeight.value - rect.top;\n        }\n      }\n    };\n    const onScroll = () => {\n      if (opened.value) {\n        updateOffset();\n      }\n    };\n    const toggleItem = (active) => {\n      children.forEach((item, index) => {\n        if (index === active) {\n          updateOffset();\n          item.toggle();\n        } else if (item.state.showPopup) {\n          item.toggle(false, {\n            immediate: true\n          });\n        }\n      });\n    };\n    const renderTitle = (item, index) => {\n      const {\n        showPopup\n      } = item.state;\n      const {\n        disabled,\n        titleClass\n      } = item;\n      return _createVNode(\"div\", {\n        \"id\": `${id}-${index}`,\n        \"role\": \"button\",\n        \"tabindex\": disabled ? void 0 : 0,\n        \"class\": [bem(\"item\", {\n          disabled\n        }), {\n          [HAPTICS_FEEDBACK]: !disabled\n        }],\n        \"onClick\": () => {\n          if (!disabled) {\n            toggleItem(index);\n          }\n        }\n      }, [_createVNode(\"span\", {\n        \"class\": [bem(\"title\", {\n          down: showPopup === (props.direction === \"down\"),\n          active: showPopup\n        }), titleClass],\n        \"style\": {\n          color: showPopup ? props.activeColor : \"\"\n        }\n      }, [_createVNode(\"div\", {\n        \"class\": \"van-ellipsis\"\n      }, [item.renderTitle()])])]);\n    };\n    linkChildren({\n      id,\n      props,\n      offset\n    });\n    useClickAway(root, onClickAway);\n    useEventListener(\"scroll\", onScroll, {\n      target: scrollParent,\n      passive: true\n    });\n    return () => {\n      var _a;\n      return _createVNode(\"div\", {\n        \"ref\": root,\n        \"class\": bem()\n      }, [_createVNode(\"div\", {\n        \"ref\": barRef,\n        \"style\": barStyle.value,\n        \"class\": bem(\"bar\", {\n          opened: opened.value\n        })\n      }, [children.map(renderTitle)]), (_a = slots.default) == null ? void 0 : _a.call(slots)]);\n    };\n  }\n});\nexport {\n  DROPDOWN_KEY,\n  stdin_default as default\n};\n"], "mappings": ";;;;;;;;;;AAAA,SAASA,WAAW,IAAIC,YAAY,QAAQ,KAAK;AACjD,SAASC,GAAG,EAAEC,QAAQ,EAAEC,eAAe,QAAQ,KAAK;AACpD,SAASC,KAAK,EAAEC,SAAS,EAAEC,WAAW,EAAEC,YAAY,EAAEC,cAAc,EAAEC,eAAe,EAAEC,eAAe,EAAEC,gBAAgB,QAAQ,oBAAoB;AACpJ,SAASC,KAAK,QAAQ,2BAA2B;AACjD,SAASC,OAAO,EAAEC,WAAW,EAAEC,YAAY,EAAEC,eAAe,EAAEC,gBAAgB,QAAQ,WAAW;AACjG,IAAAC,gBAAA,GAAoBR,eAAe,CAAC,eAAe,CAAC;EAAAS,iBAAA,GAAAC,cAAA,CAAAF,gBAAA;EAA7CG,IAAI,GAAAF,iBAAA;EAAEG,GAAG,GAAAH,iBAAA;AAChB,IAAMI,iBAAiB,GAAG;EACxBC,OAAO,EAAEnB,SAAS;EAClBoB,MAAM,EAAEnB,WAAW;EACnBoB,QAAQ,EAAEjB,eAAe,CAAC,GAAG,CAAC;EAC9BkB,SAAS,EAAEnB,cAAc,CAAC,MAAM,CAAC;EACjCoB,WAAW,EAAEC,MAAM;EACnBC,mBAAmB,EAAEzB,SAAS;EAC9B0B,mBAAmB,EAAE1B;AACvB,CAAC;AACD,IAAM2B,YAAY,GAAGC,MAAM,CAACZ,IAAI,CAAC;AACjC,IAAIa,aAAa,GAAG/B,eAAe,CAAC;EAClCkB,IAAI,EAAJA,IAAI;EACJc,KAAK,EAAEZ,iBAAiB;EACxBa,KAAK,WAAAA,MAACD,KAAK,EAAAE,IAAA,EAER;IAAA,IADDC,KAAK,GAAAD,IAAA,CAALC,KAAK;IAEL,IAAMC,EAAE,GAAG3B,KAAK,CAAC,CAAC;IAClB,IAAM4B,IAAI,GAAGvC,GAAG,CAAC,CAAC;IAClB,IAAMwC,MAAM,GAAGxC,GAAG,CAAC,CAAC;IACpB,IAAMyC,MAAM,GAAGzC,GAAG,CAAC,CAAC,CAAC;IACrB,IAAA0C,YAAA,GAGI7B,WAAW,CAACkB,YAAY,CAAC;MAF3BY,QAAQ,GAAAD,YAAA,CAARC,QAAQ;MACRC,YAAY,GAAAF,YAAA,CAAZE,YAAY;IAEd,IAAMC,YAAY,GAAG9B,eAAe,CAACwB,IAAI,CAAC;IAC1C,IAAMO,MAAM,GAAG7C,QAAQ,CAAC;MAAA,OAAM0C,QAAQ,CAACI,IAAI,CAAC,UAACC,IAAI;QAAA,OAAKA,IAAI,CAACC,KAAK,CAACC,WAAW;MAAA,EAAC;IAAA,EAAC;IAC9E,IAAMC,QAAQ,GAAGlD,QAAQ,CAAC,YAAM;MAC9B,IAAI6C,MAAM,CAACM,KAAK,IAAIjD,KAAK,CAAC+B,KAAK,CAACV,MAAM,CAAC,EAAE;QACvC,OAAO;UACLA,MAAM,EAAE,CAACU,KAAK,CAACV,MAAM,GAAG;QAC1B,CAAC;MACH;IACF,CAAC,CAAC;IACF,IAAM6B,WAAW,GAAG,SAAdA,WAAWA,CAAA,EAAS;MACxB,IAAInB,KAAK,CAACL,mBAAmB,EAAE;QAC7Bc,QAAQ,CAACW,OAAO,CAAC,UAACN,IAAI,EAAK;UACzBA,IAAI,CAACO,MAAM,CAAC,KAAK,CAAC;QACpB,CAAC,CAAC;MACJ;IACF,CAAC;IACD,IAAMC,YAAY,GAAG,SAAfA,YAAYA,CAAA,EAAS;MACzB,IAAIhB,MAAM,CAACY,KAAK,EAAE;QAChB,IAAMK,IAAI,GAAG7C,OAAO,CAAC4B,MAAM,CAAC;QAC5B,IAAIN,KAAK,CAACR,SAAS,KAAK,MAAM,EAAE;UAC9Be,MAAM,CAACW,KAAK,GAAGK,IAAI,CAACC,MAAM;QAC5B,CAAC,MAAM;UACLjB,MAAM,CAACW,KAAK,GAAG9C,YAAY,CAAC8C,KAAK,GAAGK,IAAI,CAACE,GAAG;QAC9C;MACF;IACF,CAAC;IACD,IAAMC,QAAQ,GAAG,SAAXA,QAAQA,CAAA,EAAS;MACrB,IAAId,MAAM,CAACM,KAAK,EAAE;QAChBI,YAAY,CAAC,CAAC;MAChB;IACF,CAAC;IACD,IAAMK,UAAU,GAAG,SAAbA,UAAUA,CAAIC,MAAM,EAAK;MAC7BnB,QAAQ,CAACW,OAAO,CAAC,UAACN,IAAI,EAAEe,KAAK,EAAK;QAChC,IAAIA,KAAK,KAAKD,MAAM,EAAE;UACpBN,YAAY,CAAC,CAAC;UACdR,IAAI,CAACO,MAAM,CAAC,CAAC;QACf,CAAC,MAAM,IAAIP,IAAI,CAACC,KAAK,CAACe,SAAS,EAAE;UAC/BhB,IAAI,CAACO,MAAM,CAAC,KAAK,EAAE;YACjBU,SAAS,EAAE;UACb,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;IACJ,CAAC;IACD,IAAMC,WAAW,GAAG,SAAdA,WAAWA,CAAIlB,IAAI,EAAEe,KAAK,EAAK;MACnC,IACEC,SAAS,GACPhB,IAAI,CAACC,KAAK,CADZe,SAAS;MAEX,IACEG,QAAQ,GAENnB,IAAI,CAFNmB,QAAQ;QACRC,UAAU,GACRpB,IAAI,CADNoB,UAAU;MAEZ,OAAOrE,YAAY,CAAC,KAAK,EAAE;QACzB,IAAI,KAAAsE,MAAA,CAAK/B,EAAE,OAAA+B,MAAA,CAAIN,KAAK,CAAE;QACtB,MAAM,EAAE,QAAQ;QAChB,UAAU,EAAEI,QAAQ,GAAG,KAAK,CAAC,GAAG,CAAC;QACjC,OAAO,EAAE,CAAC9C,GAAG,CAAC,MAAM,EAAE;UACpB8C,QAAQ,EAARA;QACF,CAAC,CAAC,EAAAG,eAAA,KACC5D,gBAAgB,EAAG,CAACyD,QAAQ,EAC7B;QACF,SAAS,EAAE,SAAAI,QAAA,EAAM;UACf,IAAI,CAACJ,QAAQ,EAAE;YACbN,UAAU,CAACE,KAAK,CAAC;UACnB;QACF;MACF,CAAC,EAAE,CAAChE,YAAY,CAAC,MAAM,EAAE;QACvB,OAAO,EAAE,CAACsB,GAAG,CAAC,OAAO,EAAE;UACrBmD,IAAI,EAAER,SAAS,MAAM9B,KAAK,CAACR,SAAS,KAAK,MAAM,CAAC;UAChDoC,MAAM,EAAEE;QACV,CAAC,CAAC,EAAEI,UAAU,CAAC;QACf,OAAO,EAAE;UACPK,KAAK,EAAET,SAAS,GAAG9B,KAAK,CAACP,WAAW,GAAG;QACzC;MACF,CAAC,EAAE,CAAC5B,YAAY,CAAC,KAAK,EAAE;QACtB,OAAO,EAAE;MACX,CAAC,EAAE,CAACiD,IAAI,CAACkB,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9B,CAAC;IACDtB,YAAY,CAAC;MACXN,EAAE,EAAFA,EAAE;MACFJ,KAAK,EAALA,KAAK;MACLO,MAAM,EAANA;IACF,CAAC,CAAC;IACF3B,YAAY,CAACyB,IAAI,EAAEc,WAAW,CAAC;IAC/BrC,gBAAgB,CAAC,QAAQ,EAAE4C,QAAQ,EAAE;MACnCc,MAAM,EAAE7B,YAAY;MACpB8B,OAAO,EAAE;IACX,CAAC,CAAC;IACF,OAAO,YAAM;MACX,IAAIC,EAAE;MACN,OAAO7E,YAAY,CAAC,KAAK,EAAE;QACzB,KAAK,EAAEwC,IAAI;QACX,OAAO,EAAElB,GAAG,CAAC;MACf,CAAC,EAAE,CAACtB,YAAY,CAAC,KAAK,EAAE;QACtB,KAAK,EAAEyC,MAAM;QACb,OAAO,EAAEW,QAAQ,CAACC,KAAK;QACvB,OAAO,EAAE/B,GAAG,CAAC,KAAK,EAAE;UAClByB,MAAM,EAAEA,MAAM,CAACM;QACjB,CAAC;MACH,CAAC,EAAE,CAACT,QAAQ,CAACkC,GAAG,CAACX,WAAW,CAAC,CAAC,CAAC,EAAE,CAACU,EAAE,GAAGvC,KAAK,CAACyC,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGF,EAAE,CAACG,IAAI,CAAC1C,KAAK,CAAC,CAAC,CAAC;IAC3F,CAAC;EACH;AACF,CAAC,CAAC;AACF,SACEN,YAAY,EACZE,aAAa,IAAI6C,OAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}