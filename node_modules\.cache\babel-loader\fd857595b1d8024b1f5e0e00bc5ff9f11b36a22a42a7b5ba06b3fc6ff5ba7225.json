{"ast": null, "code": "function _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return _typeof(key) === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (_typeof(input) !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (_typeof(res) !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\nfunction _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nfunction _iterableToArrayLimit(arr, i) { var _i = null == arr ? null : \"undefined\" != typeof Symbol && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (null != _i) { var _s, _e, _x, _r, _arr = [], _n = !0, _d = !1; try { if (_x = (_i = _i.call(arr)).next, 0 === i) { if (Object(_i) !== _i) return; _n = !1; } else for (; !(_n = (_s = _x.call(_i)).done) && (_arr.push(_s.value), _arr.length !== i); _n = !0); } catch (err) { _d = !0, _e = err; } finally { try { if (!_n && null != _i.return && (_r = _i.return(), Object(_r) !== _r)) return; } finally { if (_d) throw _e; } } return _arr; } }\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\nimport { createVNode as _createVNode } from \"vue\";\nimport { ref, defineComponent } from \"vue\";\nimport { truthProp, numericProp, getZIndexStyle, createNamespace, callInterceptor, makeNumericProp, BORDER_TOP_BOTTOM } from \"../utils/index.mjs\";\nimport { useChildren } from \"@vant/use\";\nimport { usePlaceholder } from \"../composables/use-placeholder.mjs\";\nvar _createNamespace = createNamespace(\"tabbar\"),\n  _createNamespace2 = _slicedToArray(_createNamespace, 2),\n  name = _createNamespace2[0],\n  bem = _createNamespace2[1];\nvar tabbarProps = {\n  route: Boolean,\n  fixed: truthProp,\n  border: truthProp,\n  zIndex: numericProp,\n  placeholder: Boolean,\n  activeColor: String,\n  beforeChange: Function,\n  inactiveColor: String,\n  modelValue: makeNumericProp(0),\n  safeAreaInsetBottom: {\n    type: Boolean,\n    default: null\n  }\n};\nvar TABBAR_KEY = Symbol(name);\nvar stdin_default = defineComponent({\n  name: name,\n  props: tabbarProps,\n  emits: [\"change\", \"update:modelValue\"],\n  setup: function setup(props, _ref) {\n    var emit = _ref.emit,\n      slots = _ref.slots;\n    var root = ref();\n    var _useChildren = useChildren(TABBAR_KEY),\n      linkChildren = _useChildren.linkChildren;\n    var renderPlaceholder = usePlaceholder(root, bem);\n    var enableSafeArea = function enableSafeArea() {\n      var _a;\n      return (_a = props.safeAreaInsetBottom) != null ? _a : props.fixed;\n    };\n    var renderTabbar = function renderTabbar() {\n      var _ref2;\n      var _a;\n      var fixed = props.fixed,\n        zIndex = props.zIndex,\n        border = props.border;\n      return _createVNode(\"div\", {\n        \"ref\": root,\n        \"role\": \"tablist\",\n        \"style\": getZIndexStyle(zIndex),\n        \"class\": [bem({\n          fixed: fixed\n        }), (_ref2 = {}, _defineProperty(_ref2, BORDER_TOP_BOTTOM, border), _defineProperty(_ref2, \"van-safe-area-bottom\", enableSafeArea()), _ref2)]\n      }, [(_a = slots.default) == null ? void 0 : _a.call(slots)]);\n    };\n    var setActive = function setActive(active, afterChange) {\n      callInterceptor(props.beforeChange, {\n        args: [active],\n        done: function done() {\n          emit(\"update:modelValue\", active);\n          emit(\"change\", active);\n          afterChange();\n        }\n      });\n    };\n    linkChildren({\n      props: props,\n      setActive: setActive\n    });\n    return function () {\n      if (props.fixed && props.placeholder) {\n        return renderPlaceholder(renderTabbar);\n      }\n      return renderTabbar();\n    };\n  }\n});\nexport { TABBAR_KEY, stdin_default as default };", "map": {"version": 3, "names": ["createVNode", "_createVNode", "ref", "defineComponent", "truthProp", "numericProp", "getZIndexStyle", "createNamespace", "callInterceptor", "makeNumericProp", "BORDER_TOP_BOTTOM", "useChildren", "usePlaceholder", "_createNamespace", "_createNamespace2", "_slicedToArray", "name", "bem", "tabbarProps", "route", "Boolean", "fixed", "border", "zIndex", "placeholder", "activeColor", "String", "beforeChange", "Function", "inactiveColor", "modelValue", "safeAreaInsetBottom", "type", "default", "TABBAR_KEY", "Symbol", "stdin_default", "props", "emits", "setup", "_ref", "emit", "slots", "root", "_useChildren", "linkChildren", "renderPlaceholder", "enableSafeArea", "_a", "renderTabbar", "_ref2", "_defineProperty", "call", "setActive", "active", "afterChange", "args", "done"], "sources": ["C:/Users/<USER>/Desktop/vue3_3.0/node_modules/vant/es/tabbar/Tabbar.mjs"], "sourcesContent": ["import { createVNode as _createVNode } from \"vue\";\nimport { ref, defineComponent } from \"vue\";\nimport { truthProp, numericProp, getZIndexStyle, createNamespace, callInterceptor, makeNumericProp, BORDER_TOP_BOTTOM } from \"../utils/index.mjs\";\nimport { useChildren } from \"@vant/use\";\nimport { usePlaceholder } from \"../composables/use-placeholder.mjs\";\nconst [name, bem] = createNamespace(\"tabbar\");\nconst tabbarProps = {\n  route: Boolean,\n  fixed: truthProp,\n  border: truthProp,\n  zIndex: numericProp,\n  placeholder: Boolean,\n  activeColor: String,\n  beforeChange: Function,\n  inactiveColor: String,\n  modelValue: makeNumericProp(0),\n  safeAreaInsetBottom: {\n    type: Boolean,\n    default: null\n  }\n};\nconst TABBAR_KEY = Symbol(name);\nvar stdin_default = defineComponent({\n  name,\n  props: tabbarProps,\n  emits: [\"change\", \"update:modelValue\"],\n  setup(props, {\n    emit,\n    slots\n  }) {\n    const root = ref();\n    const {\n      linkChildren\n    } = useChildren(TABBAR_KEY);\n    const renderPlaceholder = usePlaceholder(root, bem);\n    const enableSafeArea = () => {\n      var _a;\n      return (_a = props.safeAreaInsetBottom) != null ? _a : props.fixed;\n    };\n    const renderTabbar = () => {\n      var _a;\n      const {\n        fixed,\n        zIndex,\n        border\n      } = props;\n      return _createVNode(\"div\", {\n        \"ref\": root,\n        \"role\": \"tablist\",\n        \"style\": getZIndexStyle(zIndex),\n        \"class\": [bem({\n          fixed\n        }), {\n          [BORDER_TOP_BOTTOM]: border,\n          \"van-safe-area-bottom\": enableSafeArea()\n        }]\n      }, [(_a = slots.default) == null ? void 0 : _a.call(slots)]);\n    };\n    const setActive = (active, afterChange) => {\n      callInterceptor(props.beforeChange, {\n        args: [active],\n        done() {\n          emit(\"update:modelValue\", active);\n          emit(\"change\", active);\n          afterChange();\n        }\n      });\n    };\n    linkChildren({\n      props,\n      setActive\n    });\n    return () => {\n      if (props.fixed && props.placeholder) {\n        return renderPlaceholder(renderTabbar);\n      }\n      return renderTabbar();\n    };\n  }\n});\nexport {\n  TABBAR_KEY,\n  stdin_default as default\n};\n"], "mappings": ";;;;;;;;;;AAAA,SAASA,WAAW,IAAIC,YAAY,QAAQ,KAAK;AACjD,SAASC,GAAG,EAAEC,eAAe,QAAQ,KAAK;AAC1C,SAASC,SAAS,EAAEC,WAAW,EAAEC,cAAc,EAAEC,eAAe,EAAEC,eAAe,EAAEC,eAAe,EAAEC,iBAAiB,QAAQ,oBAAoB;AACjJ,SAASC,WAAW,QAAQ,WAAW;AACvC,SAASC,cAAc,QAAQ,oCAAoC;AACnE,IAAAC,gBAAA,GAAoBN,eAAe,CAAC,QAAQ,CAAC;EAAAO,iBAAA,GAAAC,cAAA,CAAAF,gBAAA;EAAtCG,IAAI,GAAAF,iBAAA;EAAEG,GAAG,GAAAH,iBAAA;AAChB,IAAMI,WAAW,GAAG;EAClBC,KAAK,EAAEC,OAAO;EACdC,KAAK,EAAEjB,SAAS;EAChBkB,MAAM,EAAElB,SAAS;EACjBmB,MAAM,EAAElB,WAAW;EACnBmB,WAAW,EAAEJ,OAAO;EACpBK,WAAW,EAAEC,MAAM;EACnBC,YAAY,EAAEC,QAAQ;EACtBC,aAAa,EAAEH,MAAM;EACrBI,UAAU,EAAErB,eAAe,CAAC,CAAC,CAAC;EAC9BsB,mBAAmB,EAAE;IACnBC,IAAI,EAAEZ,OAAO;IACba,OAAO,EAAE;EACX;AACF,CAAC;AACD,IAAMC,UAAU,GAAGC,MAAM,CAACnB,IAAI,CAAC;AAC/B,IAAIoB,aAAa,GAAGjC,eAAe,CAAC;EAClCa,IAAI,EAAJA,IAAI;EACJqB,KAAK,EAAEnB,WAAW;EAClBoB,KAAK,EAAE,CAAC,QAAQ,EAAE,mBAAmB,CAAC;EACtCC,KAAK,WAAAA,MAACF,KAAK,EAAAG,IAAA,EAGR;IAAA,IAFDC,IAAI,GAAAD,IAAA,CAAJC,IAAI;MACJC,KAAK,GAAAF,IAAA,CAALE,KAAK;IAEL,IAAMC,IAAI,GAAGzC,GAAG,CAAC,CAAC;IAClB,IAAA0C,YAAA,GAEIjC,WAAW,CAACuB,UAAU,CAAC;MADzBW,YAAY,GAAAD,YAAA,CAAZC,YAAY;IAEd,IAAMC,iBAAiB,GAAGlC,cAAc,CAAC+B,IAAI,EAAE1B,GAAG,CAAC;IACnD,IAAM8B,cAAc,GAAG,SAAjBA,cAAcA,CAAA,EAAS;MAC3B,IAAIC,EAAE;MACN,OAAO,CAACA,EAAE,GAAGX,KAAK,CAACN,mBAAmB,KAAK,IAAI,GAAGiB,EAAE,GAAGX,KAAK,CAAChB,KAAK;IACpE,CAAC;IACD,IAAM4B,YAAY,GAAG,SAAfA,YAAYA,CAAA,EAAS;MAAA,IAAAC,KAAA;MACzB,IAAIF,EAAE;MACN,IACE3B,KAAK,GAGHgB,KAAK,CAHPhB,KAAK;QACLE,MAAM,GAEJc,KAAK,CAFPd,MAAM;QACND,MAAM,GACJe,KAAK,CADPf,MAAM;MAER,OAAOrB,YAAY,CAAC,KAAK,EAAE;QACzB,KAAK,EAAE0C,IAAI;QACX,MAAM,EAAE,SAAS;QACjB,OAAO,EAAErC,cAAc,CAACiB,MAAM,CAAC;QAC/B,OAAO,EAAE,CAACN,GAAG,CAAC;UACZI,KAAK,EAALA;QACF,CAAC,CAAC,GAAA6B,KAAA,OAAAC,eAAA,CAAAD,KAAA,EACCxC,iBAAiB,EAAGY,MAAM,GAAA6B,eAAA,CAAAD,KAAA,EAC3B,sBAAsB,EAAEH,cAAc,CAAC,CAAC,GAAAG,KAAA;MAE5C,CAAC,EAAE,CAAC,CAACF,EAAE,GAAGN,KAAK,CAACT,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGe,EAAE,CAACI,IAAI,CAACV,KAAK,CAAC,CAAC,CAAC;IAC9D,CAAC;IACD,IAAMW,SAAS,GAAG,SAAZA,SAASA,CAAIC,MAAM,EAAEC,WAAW,EAAK;MACzC/C,eAAe,CAAC6B,KAAK,CAACV,YAAY,EAAE;QAClC6B,IAAI,EAAE,CAACF,MAAM,CAAC;QACdG,IAAI,WAAAA,KAAA,EAAG;UACLhB,IAAI,CAAC,mBAAmB,EAAEa,MAAM,CAAC;UACjCb,IAAI,CAAC,QAAQ,EAAEa,MAAM,CAAC;UACtBC,WAAW,CAAC,CAAC;QACf;MACF,CAAC,CAAC;IACJ,CAAC;IACDV,YAAY,CAAC;MACXR,KAAK,EAALA,KAAK;MACLgB,SAAS,EAATA;IACF,CAAC,CAAC;IACF,OAAO,YAAM;MACX,IAAIhB,KAAK,CAAChB,KAAK,IAAIgB,KAAK,CAACb,WAAW,EAAE;QACpC,OAAOsB,iBAAiB,CAACG,YAAY,CAAC;MACxC;MACA,OAAOA,YAAY,CAAC,CAAC;IACvB,CAAC;EACH;AACF,CAAC,CAAC;AACF,SACEf,UAAU,EACVE,aAAa,IAAIH,OAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}