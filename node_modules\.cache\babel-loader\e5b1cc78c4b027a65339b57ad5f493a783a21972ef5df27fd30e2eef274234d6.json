{"ast": null, "code": "import { raf } from \"@vant/use\";\nimport { getScrollTop, setScrollTop } from \"../utils/index.mjs\";\nfunction scrollLeftTo(scroller, to, duration) {\n  var count = 0;\n  var from = scroller.scrollLeft;\n  var frames = duration === 0 ? 1 : Math.round(duration * 1e3 / 16);\n  function animate() {\n    scroller.scrollLeft += (to - from) / frames;\n    if (++count < frames) {\n      raf(animate);\n    }\n  }\n  animate();\n}\nfunction scrollTopTo(scroller, to, duration, callback) {\n  var current = getScrollTop(scroller);\n  var isDown = current < to;\n  var frames = duration === 0 ? 1 : Math.round(duration * 1e3 / 16);\n  var step = (to - current) / frames;\n  function animate() {\n    current += step;\n    if (isDown && current > to || !isDown && current < to) {\n      current = to;\n    }\n    setScrollTop(scroller, current);\n    if (isDown && current < to || !isDown && current > to) {\n      raf(animate);\n    } else if (callback) {\n      raf(callback);\n    }\n  }\n  animate();\n}\nexport { scrollLeftTo, scrollTopTo };", "map": {"version": 3, "names": ["raf", "getScrollTop", "setScrollTop", "scrollLeftTo", "scroller", "to", "duration", "count", "from", "scrollLeft", "frames", "Math", "round", "animate", "scrollTopTo", "callback", "current", "isDown", "step"], "sources": ["C:/Users/<USER>/Downloads/Telegram Desktop/vue3_3.0/vue3_3.0/node_modules/vant/es/tabs/utils.mjs"], "sourcesContent": ["import { raf } from \"@vant/use\";\nimport { getScrollTop, setScrollTop } from \"../utils/index.mjs\";\nfunction scrollLeftTo(scroller, to, duration) {\n  let count = 0;\n  const from = scroller.scrollLeft;\n  const frames = duration === 0 ? 1 : Math.round(duration * 1e3 / 16);\n  function animate() {\n    scroller.scrollLeft += (to - from) / frames;\n    if (++count < frames) {\n      raf(animate);\n    }\n  }\n  animate();\n}\nfunction scrollTopTo(scroller, to, duration, callback) {\n  let current = getScrollTop(scroller);\n  const isDown = current < to;\n  const frames = duration === 0 ? 1 : Math.round(duration * 1e3 / 16);\n  const step = (to - current) / frames;\n  function animate() {\n    current += step;\n    if (isDown && current > to || !isDown && current < to) {\n      current = to;\n    }\n    setScrollTop(scroller, current);\n    if (isDown && current < to || !isDown && current > to) {\n      raf(animate);\n    } else if (callback) {\n      raf(callback);\n    }\n  }\n  animate();\n}\nexport {\n  scrollLeftTo,\n  scrollTopTo\n};\n"], "mappings": "AAAA,SAASA,GAAG,QAAQ,WAAW;AAC/B,SAASC,YAAY,EAAEC,YAAY,QAAQ,oBAAoB;AAC/D,SAASC,YAAYA,CAACC,QAAQ,EAAEC,EAAE,EAAEC,QAAQ,EAAE;EAC5C,IAAIC,KAAK,GAAG,CAAC;EACb,IAAMC,IAAI,GAAGJ,QAAQ,CAACK,UAAU;EAChC,IAAMC,MAAM,GAAGJ,QAAQ,KAAK,CAAC,GAAG,CAAC,GAAGK,IAAI,CAACC,KAAK,CAACN,QAAQ,GAAG,GAAG,GAAG,EAAE,CAAC;EACnE,SAASO,OAAOA,CAAA,EAAG;IACjBT,QAAQ,CAACK,UAAU,IAAI,CAACJ,EAAE,GAAGG,IAAI,IAAIE,MAAM;IAC3C,IAAI,EAAEH,KAAK,GAAGG,MAAM,EAAE;MACpBV,GAAG,CAACa,OAAO,CAAC;IACd;EACF;EACAA,OAAO,CAAC,CAAC;AACX;AACA,SAASC,WAAWA,CAACV,QAAQ,EAAEC,EAAE,EAAEC,QAAQ,EAAES,QAAQ,EAAE;EACrD,IAAIC,OAAO,GAAGf,YAAY,CAACG,QAAQ,CAAC;EACpC,IAAMa,MAAM,GAAGD,OAAO,GAAGX,EAAE;EAC3B,IAAMK,MAAM,GAAGJ,QAAQ,KAAK,CAAC,GAAG,CAAC,GAAGK,IAAI,CAACC,KAAK,CAACN,QAAQ,GAAG,GAAG,GAAG,EAAE,CAAC;EACnE,IAAMY,IAAI,GAAG,CAACb,EAAE,GAAGW,OAAO,IAAIN,MAAM;EACpC,SAASG,OAAOA,CAAA,EAAG;IACjBG,OAAO,IAAIE,IAAI;IACf,IAAID,MAAM,IAAID,OAAO,GAAGX,EAAE,IAAI,CAACY,MAAM,IAAID,OAAO,GAAGX,EAAE,EAAE;MACrDW,OAAO,GAAGX,EAAE;IACd;IACAH,YAAY,CAACE,QAAQ,EAAEY,OAAO,CAAC;IAC/B,IAAIC,MAAM,IAAID,OAAO,GAAGX,EAAE,IAAI,CAACY,MAAM,IAAID,OAAO,GAAGX,EAAE,EAAE;MACrDL,GAAG,CAACa,OAAO,CAAC;IACd,CAAC,MAAM,IAAIE,QAAQ,EAAE;MACnBf,GAAG,CAACe,QAAQ,CAAC;IACf;EACF;EACAF,OAAO,CAAC,CAAC;AACX;AACA,SACEV,YAAY,EACZW,WAAW"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}