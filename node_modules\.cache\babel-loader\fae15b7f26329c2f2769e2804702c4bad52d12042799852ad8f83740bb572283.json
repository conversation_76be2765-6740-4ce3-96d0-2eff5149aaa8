{"ast": null, "code": "'use strict';\n\nvar bind = require('function-bind');\nvar GetIntrinsic = require('get-intrinsic');\nvar $apply = GetIntrinsic('%Function.prototype.apply%');\nvar $call = GetIntrinsic('%Function.prototype.call%');\nvar $reflectApply = GetIntrinsic('%Reflect.apply%', true) || bind.call($call, $apply);\nvar $gOPD = GetIntrinsic('%Object.getOwnPropertyDescriptor%', true);\nvar $defineProperty = GetIntrinsic('%Object.defineProperty%', true);\nvar $max = GetIntrinsic('%Math.max%');\nif ($defineProperty) {\n  try {\n    $defineProperty({}, 'a', {\n      value: 1\n    });\n  } catch (e) {\n    // IE 8 has a broken defineProperty\n    $defineProperty = null;\n  }\n}\nmodule.exports = function callBind(originalFunction) {\n  var func = $reflectApply(bind, $call, arguments);\n  if ($gOPD && $defineProperty) {\n    var desc = $gOPD(func, 'length');\n    if (desc.configurable) {\n      // original length, plus the receiver, minus any additional arguments (after the receiver)\n      $defineProperty(func, 'length', {\n        value: 1 + $max(0, originalFunction.length - (arguments.length - 1))\n      });\n    }\n  }\n  return func;\n};\nvar applyBind = function applyBind() {\n  return $reflectApply(bind, $apply, arguments);\n};\nif ($defineProperty) {\n  $defineProperty(module.exports, 'apply', {\n    value: applyBind\n  });\n} else {\n  module.exports.apply = applyBind;\n}", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}