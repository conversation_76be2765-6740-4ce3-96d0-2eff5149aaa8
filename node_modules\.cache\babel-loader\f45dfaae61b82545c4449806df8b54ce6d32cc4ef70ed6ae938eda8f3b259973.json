{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _RadioGroup from \"./RadioGroup.mjs\";\nvar RadioGroup = withInstall(_RadioGroup);\nvar stdin_default = RadioGroup;\nexport { RadioGroup, stdin_default as default };", "map": {"version": 3, "names": ["withInstall", "_RadioGroup", "RadioGroup", "stdin_default", "default"], "sources": ["C:/Users/<USER>/Downloads/Telegram Desktop/vue3_3.0/vue3_3.0/node_modules/vant/es/radio-group/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _RadioGroup from \"./RadioGroup.mjs\";\nconst RadioGroup = withInstall(_RadioGroup);\nvar stdin_default = RadioGroup;\nexport {\n  RadioGroup,\n  stdin_default as default\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,WAAW,MAAM,kBAAkB;AAC1C,IAAMC,UAAU,GAAGF,WAAW,CAACC,WAAW,CAAC;AAC3C,IAAIE,aAAa,GAAGD,UAAU;AAC9B,SACEA,UAAU,EACVC,aAAa,IAAIC,OAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}