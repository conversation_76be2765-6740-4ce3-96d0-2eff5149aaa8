{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString, createElementVNode as _createElementVNode, createCommentVNode as _createCommentVNode, normalizeClass as _normalizeClass, pushScopeId as _pushScopeId, popScopeId as _popScopeId } from \"vue\";\nvar _withScopeId = function _withScopeId(n) {\n  return _pushScopeId(\"data-v-50676800\"), n = n(), _popScopeId(), n;\n};\nvar _hoisted_1 = {\n  class: \"tel home\"\n};\nvar _hoisted_2 = {\n  class: \"l\"\n};\nvar _hoisted_3 = {\n  class: \"time\"\n};\nvar _hoisted_4 = {\n  class: \"tag\"\n};\nvar _hoisted_5 = {\n  class: \"c\"\n};\nvar _hoisted_6 = {\n  class: \"time\"\n};\nvar _hoisted_7 = {\n  class: \"tag\"\n};\nvar _hoisted_8 = [\"onClick\"];\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_van_icon = _resolveComponent(\"van-icon\");\n  var _component_van_nav_bar = _resolveComponent(\"van-nav-bar\");\n  var _component_van_list = _resolveComponent(\"van-list\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_van_nav_bar, {\n    title: _ctx.$t('msg.zrjl'),\n    \"left-arrow\": \"\",\n    onClickLeft: _cache[0] || (_cache[0] = function ($event) {\n      return _ctx.$router.go(-1);\n    }),\n    onClickRight: $setup.clickRight\n  }, {\n    right: _withCtx(function () {\n      return [_createVNode(_component_van_icon, {\n        name: \"comment-o\",\n        size: \"18\"\n      })];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"title\", \"onClickRight\"]), _createVNode(_component_van_list, {\n    loading: $setup.loading,\n    \"onUpdate:loading\": _cache[1] || (_cache[1] = function ($event) {\n      return $setup.loading = $event;\n    }),\n    finished: $setup.finished,\n    \"finished-text\": _ctx.$t('msg.not_move'),\n    onLoad: $setup.getCW\n  }, {\n    default: _withCtx(function () {\n      return [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.list, function (item, index) {\n        return _openBlock(), _createElementBlock(\"div\", {\n          class: _normalizeClass([\"address\", item.is_qu && 'mb30']),\n          key: index\n        }, [_createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, _toDisplayString(_ctx.$t('msg.zrlxb') + item.day + _ctx.$t('msg.day') + item.bili * 100 + '%'), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_4, _toDisplayString(item.num), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"div\", _hoisted_6, _toDisplayString(_ctx.$t('msg.crsj')) + \"：\" + _toDisplayString($setup.formatTime('', item.addtime)), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_7, _toDisplayString(_ctx.$t('msg.zrlxb')), 1 /* TEXT */)]), !item.is_qu ? (_openBlock(), _createElementBlock(\"div\", {\n          key: 0,\n          class: \"r\",\n          onClick: function onClick($event) {\n            return $setup.qu_money(item);\n          }\n        }, _toDisplayString(_ctx.$t('msg.out_money')), 9 /* TEXT, PROPS */, _hoisted_8)) : _createCommentVNode(\"v-if\", true)], 2 /* CLASS */);\n      }), 128 /* KEYED_FRAGMENT */))];\n    }),\n\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"loading\", \"finished\", \"finished-text\", \"onLoad\"])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_van_nav_bar", "title", "_ctx", "$t", "onClickLeft", "_cache", "$event", "$router", "go", "onClickRight", "$setup", "clickRight", "right", "_withCtx", "_component_van_icon", "name", "size", "_component_van_list", "loading", "finished", "onLoad", "getCW", "_Fragment", "_renderList", "list", "item", "index", "_normalizeClass", "is_qu", "key", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_toDisplayString", "day", "bili", "_hoisted_4", "num", "_hoisted_5", "_hoisted_6", "formatTime", "addtime", "_hoisted_7", "onClick", "qu_money", "_hoisted_8"], "sources": ["C:\\Users\\<USER>\\Downloads\\Telegram Desktop\\vue3_3.0\\vue3_3.0\\src\\views\\index\\components\\libao_jl.vue"], "sourcesContent": ["<template>\r\n    <div class=\"tel home\">\r\n        <van-nav-bar :title=\"$t('msg.zrjl')\" left-arrow @click-left=\"$router.go(-1)\" @click-right=\"clickRight\">\r\n            <template #right>\r\n                <van-icon name=\"comment-o\" size=\"18\"/>\r\n            </template>\r\n        </van-nav-bar>\r\n        <van-list\r\n            v-model:loading=\"loading\"\r\n            :finished=\"finished\"\r\n            :finished-text=\"$t('msg.not_move')\"\r\n            @load=\"getCW\"\r\n            >\r\n                <div class=\"address\" :class=\"item.is_qu&&'mb30'\" v-for=\"(item,index) in list\" :key=\"index\">\r\n                    <div class=\"l\">\r\n                        <div class=\"time\">{{$t('msg.zrlxb') + item.day + $t('msg.day') + item.bili*100 + '%'}}</div>\r\n                        <div class=\"tag\">{{item.num}}</div>\r\n                    </div>\r\n                    <div class=\"c\">\r\n                        <div class=\"time\">{{$t('msg.crsj')}}：{{formatTime('',item.addtime)}}</div>\r\n                        <div class=\"tag\">{{$t('msg.zrlxb')}}</div>\r\n                    </div>\r\n                    <div class=\"r\" v-if=\"!item.is_qu\" @click=\"qu_money(item)\">\r\n                       {{$t('msg.out_money')}}\r\n                    </div>\r\n                </div>\r\n        </van-list>\r\n    </div>\r\n</template>\r\n<script>\r\nimport { ref,getCurrentInstance} from 'vue';\r\nimport {lixibao_chu,get_lixibao_chu} from '@/api/home/<USER>'\r\nimport { useRouter } from 'vue-router';\r\nimport { useI18n } from 'vue-i18n'\r\nimport store from '@/store/index'\r\nimport {formatTime} from '@/api/format.js'\r\nexport default {\r\n    setup(){\r\n    const {proxy} = getCurrentInstance()\r\n        const { push } = useRouter();\r\n        const { t } = useI18n()\r\n        \r\n        const currency = ref(store.state.baseInfo?.currency)\r\n        const list = ref([])\r\n        const page = ref(1)\r\n        const type = ref('all')\r\n\r\n        const loading = ref(true);\r\n        const finished = ref(false);\r\n\r\n        const tabs = ref([\r\n            {label: t('msg.xdcg'),value: 1},\r\n            {label: t('msg.czcg'),value: 2},\r\n            {label: t('msg.czsb'),value: 3},\r\n        ])\r\n\r\n        const clickLeft = () => {\r\n            push('/self')\r\n        }\r\n        const clickRight = () => {\r\n            push('/message')\r\n        }\r\n        const getCW = () => {\r\n            let json = {\r\n                page: 1,\r\n                size: 10,\r\n            }\r\n            lixibao_chu(json).then(res => {\r\n                if(res.code === 0) {\r\n                    finished.value = !res.data?.paging\r\n                    list.value = res.data?.list\r\n                }\r\n            })\r\n        }\r\n        const qu_money = (row) => {\r\n            proxy.$dialog.confirm({\r\n                title: t('msg.wxts'),\r\n                message: t('msg.sure_qc'),\r\n                confirmButtonText:t('msg.queren'),\r\n                cancelButtonText:t('msg.quxiao')\r\n            })\r\n            .then(() => {\r\n                get_lixibao_chu({id:row.id}).then(res => {\r\n                    if(res.code === 0) {\r\n                        proxy.$Message({ type: 'success', message:res.info});\r\n                        getCW()\r\n                    } else {\r\n                        proxy.$Message({ type: 'error', message:res.info});\r\n                    }\r\n                }).catch(rr => {\r\n                    console.log(rr)\r\n                })\r\n            })\r\n            .catch(() => {\r\n                // on cancel\r\n            });\r\n        }\r\n        return {formatTime,list,clickLeft,clickRight,tabs,getCW,loading,finished,currency,qu_money}\r\n    }\r\n}\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.tel{\r\n    overflow: auto;\r\n    :deep(.van-nav-bar){\r\n        background-color: $theme;\r\n        margin-bottom: 40px;\r\n        color: #fff;\r\n        .van-nav-bar__left{\r\n            .van-icon{\r\n                color: #fff;\r\n            }\r\n        }\r\n        .van-nav-bar__title{\r\n            color: #fff;\r\n        }\r\n        .van-nav-bar__right{\r\n            img{\r\n                height: 42px;\r\n            }\r\n            .van-icon{\r\n                color: #fff;\r\n            }\r\n        }\r\n    }\r\n    :deep(.van-list){\r\n        .van-loading{\r\n            background: initial;\r\n            color: #666;\r\n            .van-loading__text{\r\n                color: #666;\r\n            }\r\n        }\r\n        .address{\r\n            box-shadow: $shadow;\r\n            border-radius: 12px;\r\n            padding: 30px 30px 120px;\r\n            margin: 0 30px 40px;\r\n            // background-image: url('~@/assets/images/self/address/bg.png');\r\n            // background-size: 100% 100%;\r\n            text-align: left;\r\n            background-color: #fff;\r\n            &.mb30{\r\n                padding-bottom: 30px;\r\n            }\r\n            .l{\r\n                display: flex;\r\n                justify-content: space-between;\r\n                margin-bottom: 30px;\r\n                .time{\r\n                    font-size: 30px;\r\n                    font-weight: 600;\r\n                    color: #333;\r\n                }\r\n                .tag{\r\n                    font-size: 18px;\r\n                    font-weight: 600;\r\n                    color: $theme;\r\n                }\r\n            }\r\n            .c{\r\n                display: flex;\r\n                justify-content: space-between;\r\n                margin-bottom: 30px;\r\n                .time{\r\n                    font-size: 22px;\r\n                    font-weight: 600;\r\n                    color: #999;\r\n                }\r\n                .tag{\r\n                    font-size: 22px;\r\n                    font-weight: 600;\r\n                    color: #333;\r\n                }\r\n            }\r\n            .r{\r\n                width: 200px;\r\n                height: 60px;\r\n                text-align: center;\r\n                line-height: 60px;\r\n                background-color: $theme;\r\n                color: #fff;\r\n                border-radius: 50px;\r\n                float: right;\r\n                font-size: 20px;\r\n            }\r\n        }\r\n    }\r\n}\r\n</style>"], "mappings": ";;;;;EACSA,KAAK,EAAC;AAAU;;EAaAA,KAAK,EAAC;AAAG;;EACLA,KAAK,EAAC;AAAM;;EACZA,KAAK,EAAC;AAAK;;EAEfA,KAAK,EAAC;AAAG;;EACLA,KAAK,EAAC;AAAM;;EACZA,KAAK,EAAC;AAAK;;;;;;uBAnBpCC,mBAAA,CA0BM,OA1BNC,UA0BM,GAzBFC,YAAA,CAIcC,sBAAA;IAJAC,KAAK,EAAEC,IAAA,CAAAC,EAAE;IAAc,YAAU,EAAV,EAAU;IAAEC,WAAU,EAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAAEJ,IAAA,CAAAK,OAAO,CAACC,EAAE;IAAA;IAAOC,YAAW,EAAEC,MAAA,CAAAC;;IAC5EC,KAAK,EAAAC,QAAA,CACZ;MAAA,OAAsC,CAAtCd,YAAA,CAAsCe,mBAAA;QAA5BC,IAAI,EAAC,WAAW;QAACC,IAAI,EAAC;;;;gDAGxCjB,YAAA,CAmBWkB,mBAAA;IAlBCC,OAAO,EAAER,MAAA,CAAAQ,OAAO;;aAAPR,MAAA,CAAAQ,OAAO,GAAAZ,MAAA;IAAA;IACvBa,QAAQ,EAAET,MAAA,CAAAS,QAAQ;IAClB,eAAa,EAAEjB,IAAA,CAAAC,EAAE;IACjBiB,MAAI,EAAEV,MAAA,CAAAW;;sBAE8C;MAAA,OAA4B,E,kBAA7ExB,mBAAA,CAYMyB,SAAA,QAAAC,WAAA,CAZkEb,MAAA,CAAAc,IAAI,YAAnBC,IAAI,EAACC,KAAK;6BAAnE7B,mBAAA,CAYM;UAZDD,KAAK,EAAA+B,eAAA,EAAC,SAAS,EAASF,IAAI,CAACG,KAAK;UAAwCC,GAAG,EAAEH;YAChFI,mBAAA,CAGM,OAHNC,UAGM,GAFFD,mBAAA,CAA4F,OAA5FE,UAA4F,EAAAC,gBAAA,CAAxE/B,IAAA,CAAAC,EAAE,gBAAgBsB,IAAI,CAACS,GAAG,GAAGhC,IAAA,CAAAC,EAAE,cAAcsB,IAAI,CAACU,IAAI,8BAC1EL,mBAAA,CAAmC,OAAnCM,UAAmC,EAAAH,gBAAA,CAAhBR,IAAI,CAACY,GAAG,iB,GAE/BP,mBAAA,CAGM,OAHNQ,UAGM,GAFFR,mBAAA,CAA0E,OAA1ES,UAA0E,EAAAN,gBAAA,CAAtD/B,IAAA,CAAAC,EAAE,gBAAc,GAAC,GAAA8B,gBAAA,CAAEvB,MAAA,CAAA8B,UAAU,KAAIf,IAAI,CAACgB,OAAO,mBACjEX,mBAAA,CAA0C,OAA1CY,UAA0C,EAAAT,gBAAA,CAAvB/B,IAAA,CAAAC,EAAE,8B,IAEHsB,IAAI,CAACG,KAAK,I,cAAhC/B,mBAAA,CAEM;;UAFDD,KAAK,EAAC,GAAG;UAAqB+C,OAAK,WAAAA,QAAArC,MAAA;YAAA,OAAEI,MAAA,CAAAkC,QAAQ,CAACnB,IAAI;UAAA;4BAClDvB,IAAA,CAAAC,EAAE,yCAAA0C,UAAA,K"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}