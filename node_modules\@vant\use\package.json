{"name": "@vant/use", "version": "1.5.2", "description": "Vant Composition API", "main": "dist/index.cjs.js", "module": "dist/index.esm.mjs", "typings": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.esm.mjs", "require": "./dist/index.cjs.js"}}, "sideEffects": false, "files": ["dist"], "scripts": {"clean": "rimraf ./dist", "dev": "node ./build.js -w", "build:types": "tsc -p ./tsconfig.json --emitDeclarationOnly", "build:bundle": "node ./build.js", "build": "pnpm clean && pnpm build:bundle && pnpm build:types", "release": "pnpm build && release-it", "prepare": "pnpm build"}, "publishConfig": {"access": "public", "registry": "https://registry.npmjs.org/"}, "repository": {"type": "git", "url": "https://github.com/vant-ui/vant.git", "directory": "packages/vant-use"}, "bugs": "https://github.com/vant-ui/vant/issues", "author": "<PERSON><PERSON><PERSON><PERSON>", "license": "MIT", "devDependencies": {"esbuild": "^0.17.12", "release-it": "^15.4.1", "rimraf": "^5.0.0", "typescript": "^5.0.4", "vue": "^3.3.4"}, "peerDependencies": {"vue": "^3.0.0"}, "release-it": {"git": {"tag": false, "commitMessage": "release: @vant/use v${version}"}}}