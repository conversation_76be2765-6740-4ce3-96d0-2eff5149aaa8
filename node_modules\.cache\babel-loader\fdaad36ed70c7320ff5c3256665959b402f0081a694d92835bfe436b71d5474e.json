{"ast": null, "code": "'use strict';\n\nvar bind = require('function-bind');\nmodule.exports = bind.call(Function.call, Object.prototype.hasOwnProperty);", "map": {"version": 3, "names": ["bind", "require", "module", "exports", "call", "Function", "Object", "prototype", "hasOwnProperty"], "sources": ["C:/Users/<USER>/Desktop/vue3_3.0/node_modules/has/src/index.js"], "sourcesContent": ["'use strict';\n\nvar bind = require('function-bind');\n\nmodule.exports = bind.call(Function.call, Object.prototype.hasOwnProperty);\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,IAAI,GAAGC,OAAO,CAAC,eAAe,CAAC;AAEnCC,MAAM,CAACC,OAAO,GAAGH,IAAI,CAACI,IAAI,CAACC,QAAQ,CAACD,IAAI,EAAEE,MAAM,CAACC,SAAS,CAACC,cAAc,CAAC"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}