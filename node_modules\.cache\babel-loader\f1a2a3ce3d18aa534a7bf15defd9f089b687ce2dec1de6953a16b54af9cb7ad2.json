{"ast": null, "code": "function _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor); } }\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, \"prototype\", { writable: false }); return Constructor; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return _typeof(key) === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (_typeof(input) !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (_typeof(res) !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\nimport { useRect } from \"@vant/use\";\nimport { loadImageAsync } from \"./util.mjs\";\nimport { noop } from \"../../utils/index.mjs\";\nvar ReactiveListener = /*#__PURE__*/function () {\n  function ReactiveListener(_ref) {\n    var el = _ref.el,\n      src = _ref.src,\n      error = _ref.error,\n      loading = _ref.loading,\n      bindType = _ref.bindType,\n      $parent = _ref.$parent,\n      options = _ref.options,\n      cors = _ref.cors,\n      elRenderer = _ref.elRenderer,\n      imageCache = _ref.imageCache;\n    _classCallCheck(this, ReactiveListener);\n    this.el = el;\n    this.src = src;\n    this.error = error;\n    this.loading = loading;\n    this.bindType = bindType;\n    this.attempt = 0;\n    this.cors = cors;\n    this.naturalHeight = 0;\n    this.naturalWidth = 0;\n    this.options = options;\n    this.$parent = $parent;\n    this.elRenderer = elRenderer;\n    this.imageCache = imageCache;\n    this.performanceData = {\n      loadStart: 0,\n      loadEnd: 0\n    };\n    this.filter();\n    this.initState();\n    this.render(\"loading\", false);\n  }\n  _createClass(ReactiveListener, [{\n    key: \"initState\",\n    value: function initState() {\n      if (\"dataset\" in this.el) {\n        this.el.dataset.src = this.src;\n      } else {\n        this.el.setAttribute(\"data-src\", this.src);\n      }\n      this.state = {\n        loading: false,\n        error: false,\n        loaded: false,\n        rendered: false\n      };\n    }\n  }, {\n    key: \"record\",\n    value: function record(event) {\n      this.performanceData[event] = Date.now();\n    }\n  }, {\n    key: \"update\",\n    value: function update(_ref2) {\n      var src = _ref2.src,\n        loading = _ref2.loading,\n        error = _ref2.error;\n      var oldSrc = this.src;\n      this.src = src;\n      this.loading = loading;\n      this.error = error;\n      this.filter();\n      if (oldSrc !== this.src) {\n        this.attempt = 0;\n        this.initState();\n      }\n    }\n  }, {\n    key: \"checkInView\",\n    value: function checkInView() {\n      var rect = useRect(this.el);\n      return rect.top < window.innerHeight * this.options.preLoad && rect.bottom > this.options.preLoadTop && rect.left < window.innerWidth * this.options.preLoad && rect.right > 0;\n    }\n  }, {\n    key: \"filter\",\n    value: function filter() {\n      var _this = this;\n      Object.keys(this.options.filter).forEach(function (key) {\n        _this.options.filter[key](_this, _this.options);\n      });\n    }\n  }, {\n    key: \"renderLoading\",\n    value: function renderLoading(cb) {\n      var _this2 = this;\n      this.state.loading = true;\n      loadImageAsync({\n        src: this.loading,\n        cors: this.cors\n      }, function () {\n        _this2.render(\"loading\", false);\n        _this2.state.loading = false;\n        cb();\n      }, function () {\n        cb();\n        _this2.state.loading = false;\n        if (process.env.NODE_ENV !== \"production\" && !_this2.options.silent) {}\n      });\n    }\n  }, {\n    key: \"load\",\n    value: function load() {\n      var _this3 = this;\n      var onFinish = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : noop;\n      if (this.attempt > this.options.attempt - 1 && this.state.error) {\n        if (process.env.NODE_ENV !== \"production\" && !this.options.silent) {}\n        onFinish();\n        return;\n      }\n      if (this.state.rendered && this.state.loaded) return;\n      if (this.imageCache.has(this.src)) {\n        this.state.loaded = true;\n        this.render(\"loaded\", true);\n        this.state.rendered = true;\n        return onFinish();\n      }\n      this.renderLoading(function () {\n        var _a, _b;\n        _this3.attempt++;\n        (_b = (_a = _this3.options.adapter).beforeLoad) == null ? void 0 : _b.call(_a, _this3, _this3.options);\n        _this3.record(\"loadStart\");\n        loadImageAsync({\n          src: _this3.src,\n          cors: _this3.cors\n        }, function (data) {\n          _this3.naturalHeight = data.naturalHeight;\n          _this3.naturalWidth = data.naturalWidth;\n          _this3.state.loaded = true;\n          _this3.state.error = false;\n          _this3.record(\"loadEnd\");\n          _this3.render(\"loaded\", false);\n          _this3.state.rendered = true;\n          _this3.imageCache.add(_this3.src);\n          onFinish();\n        }, function (err) {\n          !_this3.options.silent && void 0;\n          _this3.state.error = true;\n          _this3.state.loaded = false;\n          _this3.render(\"error\", false);\n        });\n      });\n    }\n  }, {\n    key: \"render\",\n    value: function render(state, cache) {\n      this.elRenderer(this, state, cache);\n    }\n  }, {\n    key: \"performance\",\n    value: function performance() {\n      var state = \"loading\";\n      var time = 0;\n      if (this.state.loaded) {\n        state = \"loaded\";\n        time = (this.performanceData.loadEnd - this.performanceData.loadStart) / 1e3;\n      }\n      if (this.state.error) state = \"error\";\n      return {\n        src: this.src,\n        state: state,\n        time: time\n      };\n    }\n  }, {\n    key: \"$destroy\",\n    value: function $destroy() {\n      this.el = null;\n      this.src = null;\n      this.error = null;\n      this.loading = null;\n      this.bindType = null;\n      this.attempt = 0;\n    }\n  }]);\n  return ReactiveListener;\n}();\nexport { ReactiveListener as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}