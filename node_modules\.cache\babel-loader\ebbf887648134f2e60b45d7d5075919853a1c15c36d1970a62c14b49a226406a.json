{"ast": null, "code": "import { createVNode as _createVNode } from \"vue\";\nimport { ref, watch, computed, reactive, defineComponent } from \"vue\";\nimport { clamp, numericProp, preventDefault, createNamespace, makeRequiredProp } from \"../utils/index.mjs\";\nimport { useTouch } from \"../composables/use-touch.mjs\";\nimport { useEventListener } from \"@vant/use\";\nimport { Image } from \"../image/index.mjs\";\nimport { Loading } from \"../loading/index.mjs\";\nimport { SwipeItem } from \"../swipe-item/index.mjs\";\nvar getDistance = function getDistance(touches) {\n  return Math.sqrt(Math.pow(touches[0].clientX - touches[1].clientX, 2) + Math.pow(touches[0].clientY - touches[1].clientY, 2));\n};\nvar bem = createNamespace(\"image-preview\")[1];\nvar stdin_default = defineComponent({\n  props: {\n    src: String,\n    show: <PERSON><PERSON><PERSON>,\n    active: Number,\n    minZoom: makeRequiredProp(numericProp),\n    maxZoom: makeRequiredProp(numericProp),\n    rootWidth: makeRequiredProp(Number),\n    rootHeight: makeRequiredProp(Number)\n  },\n  emits: [\"scale\", \"close\"],\n  setup: function setup(props, _ref) {\n    var emit = _ref.emit,\n      slots = _ref.slots;\n    var state = reactive({\n      scale: 1,\n      moveX: 0,\n      moveY: 0,\n      moving: false,\n      zooming: false,\n      imageRatio: 0,\n      displayWidth: 0,\n      displayHeight: 0\n    });\n    var touch = useTouch();\n    var swipeItem = ref();\n    var vertical = computed(function () {\n      var rootWidth = props.rootWidth,\n        rootHeight = props.rootHeight;\n      var rootRatio = rootHeight / rootWidth;\n      return state.imageRatio > rootRatio;\n    });\n    var imageStyle = computed(function () {\n      var scale = state.scale,\n        moveX = state.moveX,\n        moveY = state.moveY,\n        moving = state.moving,\n        zooming = state.zooming;\n      var style = {\n        transitionDuration: zooming || moving ? \"0s\" : \".3s\"\n      };\n      if (scale !== 1) {\n        var offsetX = moveX / scale;\n        var offsetY = moveY / scale;\n        style.transform = \"scale(\".concat(scale, \", \").concat(scale, \") translate(\").concat(offsetX, \"px, \").concat(offsetY, \"px)\");\n      }\n      return style;\n    });\n    var maxMoveX = computed(function () {\n      if (state.imageRatio) {\n        var rootWidth = props.rootWidth,\n          rootHeight = props.rootHeight;\n        var displayWidth = vertical.value ? rootHeight / state.imageRatio : rootWidth;\n        return Math.max(0, (state.scale * displayWidth - rootWidth) / 2);\n      }\n      return 0;\n    });\n    var maxMoveY = computed(function () {\n      if (state.imageRatio) {\n        var rootWidth = props.rootWidth,\n          rootHeight = props.rootHeight;\n        var displayHeight = vertical.value ? rootHeight : rootWidth * state.imageRatio;\n        return Math.max(0, (state.scale * displayHeight - rootHeight) / 2);\n      }\n      return 0;\n    });\n    var setScale = function setScale(scale) {\n      scale = clamp(scale, +props.minZoom, +props.maxZoom + 1);\n      if (scale !== state.scale) {\n        state.scale = scale;\n        emit(\"scale\", {\n          scale: scale,\n          index: props.active\n        });\n      }\n    };\n    var resetScale = function resetScale() {\n      setScale(1);\n      state.moveX = 0;\n      state.moveY = 0;\n    };\n    var toggleScale = function toggleScale() {\n      var scale = state.scale > 1 ? 1 : 2;\n      setScale(scale);\n      state.moveX = 0;\n      state.moveY = 0;\n    };\n    var fingerNum;\n    var startMoveX;\n    var startMoveY;\n    var startScale;\n    var startDistance;\n    var doubleTapTimer;\n    var touchStartTime;\n    var onTouchStart = function onTouchStart(event) {\n      var touches = event.touches;\n      var offsetX = touch.offsetX;\n      touch.start(event);\n      fingerNum = touches.length;\n      startMoveX = state.moveX;\n      startMoveY = state.moveY;\n      touchStartTime = Date.now();\n      state.moving = fingerNum === 1 && state.scale !== 1;\n      state.zooming = fingerNum === 2 && !offsetX.value;\n      if (state.zooming) {\n        startScale = state.scale;\n        startDistance = getDistance(event.touches);\n      }\n    };\n    var onTouchMove = function onTouchMove(event) {\n      var touches = event.touches;\n      touch.move(event);\n      if (state.moving || state.zooming) {\n        preventDefault(event, true);\n      }\n      if (state.moving) {\n        var deltaX = touch.deltaX,\n          deltaY = touch.deltaY;\n        var moveX = deltaX.value + startMoveX;\n        var moveY = deltaY.value + startMoveY;\n        state.moveX = clamp(moveX, -maxMoveX.value, maxMoveX.value);\n        state.moveY = clamp(moveY, -maxMoveY.value, maxMoveY.value);\n      }\n      if (state.zooming && touches.length === 2) {\n        var distance = getDistance(touches);\n        var scale = startScale * distance / startDistance;\n        setScale(scale);\n      }\n    };\n    var checkTap = function checkTap() {\n      if (fingerNum > 1) {\n        return;\n      }\n      var offsetX = touch.offsetX,\n        offsetY = touch.offsetY;\n      var deltaTime = Date.now() - touchStartTime;\n      var TAP_TIME = 250;\n      var TAP_OFFSET = 5;\n      if (offsetX.value < TAP_OFFSET && offsetY.value < TAP_OFFSET && deltaTime < TAP_TIME) {\n        if (doubleTapTimer) {\n          clearTimeout(doubleTapTimer);\n          doubleTapTimer = null;\n          toggleScale();\n        } else {\n          doubleTapTimer = setTimeout(function () {\n            emit(\"close\");\n            doubleTapTimer = null;\n          }, TAP_TIME);\n        }\n      }\n    };\n    var onTouchEnd = function onTouchEnd(event) {\n      var stopPropagation = false;\n      if (state.moving || state.zooming) {\n        stopPropagation = true;\n        if (state.moving && startMoveX === state.moveX && startMoveY === state.moveY) {\n          stopPropagation = false;\n        }\n        if (!event.touches.length) {\n          if (state.zooming) {\n            state.moveX = clamp(state.moveX, -maxMoveX.value, maxMoveX.value);\n            state.moveY = clamp(state.moveY, -maxMoveY.value, maxMoveY.value);\n            state.zooming = false;\n          }\n          state.moving = false;\n          startMoveX = 0;\n          startMoveY = 0;\n          startScale = 1;\n          if (state.scale < 1) {\n            resetScale();\n          }\n          if (state.scale > props.maxZoom) {\n            state.scale = +props.maxZoom;\n          }\n        }\n      }\n      preventDefault(event, stopPropagation);\n      checkTap();\n      touch.reset();\n    };\n    var onLoad = function onLoad(event) {\n      var _event$target = event.target,\n        naturalWidth = _event$target.naturalWidth,\n        naturalHeight = _event$target.naturalHeight;\n      state.imageRatio = naturalHeight / naturalWidth;\n    };\n    watch(function () {\n      return props.active;\n    }, resetScale);\n    watch(function () {\n      return props.show;\n    }, function (value) {\n      if (!value) {\n        resetScale();\n      }\n    });\n    useEventListener(\"touchmove\", onTouchMove, {\n      target: computed(function () {\n        var _a;\n        return (_a = swipeItem.value) == null ? void 0 : _a.$el;\n      })\n    });\n    return function () {\n      var imageSlots = {\n        loading: function loading() {\n          return _createVNode(Loading, {\n            \"type\": \"spinner\"\n          }, null);\n        }\n      };\n      return _createVNode(SwipeItem, {\n        \"ref\": swipeItem,\n        \"class\": bem(\"swipe-item\"),\n        \"onTouchstartPassive\": onTouchStart,\n        \"onTouchend\": onTouchEnd,\n        \"onTouchcancel\": onTouchEnd\n      }, {\n        default: function _default() {\n          return [slots.image ? _createVNode(\"div\", {\n            \"class\": bem(\"image-wrap\")\n          }, [slots.image({\n            src: props.src\n          })]) : _createVNode(Image, {\n            \"src\": props.src,\n            \"fit\": \"contain\",\n            \"class\": bem(\"image\", {\n              vertical: vertical.value\n            }),\n            \"style\": imageStyle.value,\n            \"onLoad\": onLoad\n          }, imageSlots)];\n        }\n      });\n    };\n  }\n});\nexport { stdin_default as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}