{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _ActionBar from \"./ActionBar.mjs\";\nvar ActionBar = withInstall(_ActionBar);\nvar stdin_default = ActionBar;\nexport { ActionBar, stdin_default as default };", "map": {"version": 3, "names": ["withInstall", "_ActionBar", "ActionBar", "stdin_default", "default"], "sources": ["C:/Users/<USER>/Desktop/vue3_3.0/node_modules/vant/es/action-bar/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _ActionBar from \"./ActionBar.mjs\";\nconst ActionBar = withInstall(_ActionBar);\nvar stdin_default = ActionBar;\nexport {\n  ActionBar,\n  stdin_default as default\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,UAAU,MAAM,iBAAiB;AACxC,IAAMC,SAAS,GAAGF,WAAW,CAACC,UAAU,CAAC;AACzC,IAAIE,aAAa,GAAGD,SAAS;AAC7B,SACEA,SAAS,EACTC,aAAa,IAAIC,OAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}