{"ast": null, "code": "import { inject } from \"vue\";\nvar TAB_STATUS_KEY = Symbol();\nvar useTabStatus = function useTabStatus() {\n  return inject(TAB_STATUS_KEY, null);\n};\nexport { TAB_STATUS_KEY, useTabStatus };", "map": {"version": 3, "names": ["inject", "TAB_STATUS_KEY", "Symbol", "useTabStatus"], "sources": ["C:/Users/<USER>/Downloads/Telegram Desktop/vue3_3.0/vue3_3.0/node_modules/vant/es/composables/use-tab-status.mjs"], "sourcesContent": ["import { inject } from \"vue\";\nconst TAB_STATUS_KEY = Symbol();\nconst useTabStatus = () => inject(TAB_STATUS_KEY, null);\nexport {\n  TAB_STATUS_KEY,\n  useTabStatus\n};\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,KAAK;AAC5B,IAAMC,cAAc,GAAGC,MAAM,CAAC,CAAC;AAC/B,IAAMC,YAAY,GAAG,SAAfA,YAAYA,CAAA;EAAA,OAASH,MAAM,CAACC,cAAc,EAAE,IAAI,CAAC;AAAA;AACvD,SACEA,cAAc,EACdE,YAAY"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}