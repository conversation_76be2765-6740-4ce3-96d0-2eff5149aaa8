{"ast": null, "code": "import { reactive, ref, getCurrentInstance } from 'vue';\nimport store from '@/store/index';\nimport { uploadImg, bank_recharge } from '@/api/home/<USER>';\nimport { useRouter, useRoute } from 'vue-router';\nimport { useI18n } from 'vue-i18n';\n// 复制函数\nimport useClipboard from 'vue-clipboard3';\nexport default {\n  name: 'HomeView',\n  setup: function setup() {\n    var _store$state$baseInfo;\n    var _useClipboard = useClipboard(),\n      toClipboard = _useClipboard.toClipboard;\n    var _useI18n = useI18n(),\n      locale = _useI18n.locale,\n      t = _useI18n.t;\n    var _useRouter = useRouter(),\n      push = _useRouter.push,\n      back = _useRouter.back;\n    var route = useRoute();\n    var _getCurrentInstance = getCurrentInstance(),\n      proxy = _getCurrentInstance.proxy;\n    var info = ref(route.query);\n    var currency = ref((_store$state$baseInfo = store.state.baseInfo) === null || _store$state$baseInfo === void 0 ? void 0 : _store$state$baseInfo.currency);\n    var pay = ref([]);\n    var checked = ref('');\n    var url = ref('');\n    var fileList = ref([]);\n    var isNext = ref(false);\n    var lang = ref(locale.value);\n    var clickLeft = function clickLeft() {\n      push('/self');\n    };\n    var clickRight = function clickRight() {\n      push('/tel');\n    };\n    var copy = function copy(value) {\n      try {\n        toClipboard(value);\n        proxy.$Message({\n          type: 'success',\n          message: t('msg.copy_s')\n        });\n      } catch (e) {\n        proxy.$Message({\n          type: 'error',\n          message: t('msg.copy_b')\n        });\n      }\n    };\n    var afterRead = function afterRead(file) {\n      file.status = 'uploading';\n      file.message = t('msg.scz');\n      var formData = new FormData();\n      formData.append('file', file.file);\n      isNext.value = true;\n      uploadImg(formData).then(function (res) {\n        file.status = 'success';\n        isNext.value = false;\n        url.value = res.url || '';\n        if (!res.url) {\n          fileList.value = [];\n          return false;\n        }\n      });\n    };\n    // next_cz checked\n\n    var onSubmit = function onSubmit() {\n      if (!url.value) {\n        proxy.$Message({\n          type: 'error',\n          message: t('msg.qscfkjt')\n        });\n      } else {\n        var _info$value, _route$query, _route$query2;\n        var json = {\n          num: (_info$value = info.value) === null || _info$value === void 0 ? void 0 : _info$value.num,\n          url: url.value,\n          vip_id: (_route$query = route.query) === null || _route$query === void 0 ? void 0 : _route$query.vip_id,\n          payId: (_route$query2 = route.query) === null || _route$query2 === void 0 ? void 0 : _route$query2.id\n        };\n        bank_recharge(json).then(function (res) {\n          if (res.code === 0) {\n            proxy.$Message({\n              type: 'success',\n              message: res.info\n            });\n            back(2);\n          } else {\n            proxy.$Message({\n              type: 'error',\n              message: res.info\n            });\n          }\n        });\n      }\n    };\n    return {\n      afterRead: afterRead,\n      copy: copy,\n      checked: checked,\n      pay: pay,\n      isNext: isNext,\n      onSubmit: onSubmit,\n      clickLeft: clickLeft,\n      clickRight: clickRight,\n      info: info,\n      currency: currency,\n      fileList: fileList,\n      lang: lang\n    };\n  }\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}