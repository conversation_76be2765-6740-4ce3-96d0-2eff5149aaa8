{"ast": null, "code": "'use strict';\n\nvar test = {\n  foo: {}\n};\nvar $Object = Object;\nmodule.exports = function hasProto() {\n  return {\n    __proto__: test\n  }.foo === test.foo && !({\n    __proto__: null\n  } instanceof $Object);\n};", "map": {"version": 3, "names": ["test", "foo", "$Object", "Object", "module", "exports", "hasProto", "__proto__"], "sources": ["C:/Users/<USER>/Downloads/Telegram Desktop/vue3_3.0/vue3_3.0/node_modules/has-proto/index.js"], "sourcesContent": ["'use strict';\n\nvar test = {\n\tfoo: {}\n};\n\nvar $Object = Object;\n\nmodule.exports = function hasProto() {\n\treturn { __proto__: test }.foo === test.foo && !({ __proto__: null } instanceof $Object);\n};\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,IAAI,GAAG;EACVC,GAAG,EAAE,CAAC;AACP,CAAC;AAED,IAAIC,OAAO,GAAGC,MAAM;AAEpBC,MAAM,CAACC,OAAO,GAAG,SAASC,QAAQA,CAAA,EAAG;EACpC,OAAO;IAAEC,SAAS,EAAEP;EAAK,CAAC,CAACC,GAAG,KAAKD,IAAI,CAACC,GAAG,IAAI,EAAE;IAAEM,SAAS,EAAE;EAAK,CAAC,YAAYL,OAAO,CAAC;AACzF,CAAC"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}