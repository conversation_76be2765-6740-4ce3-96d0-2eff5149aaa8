{"ast": null, "code": "import { createVNode as _createVNode, mergeProps as _mergeProps } from \"vue\";\nimport { ref, watch, getCurrentInstance } from \"vue\";\nimport { extend, isObject, inBrowser, withInstall } from \"../utils/index.mjs\";\nimport { mountComponent, usePopupState } from \"../utils/mount-component.mjs\";\nimport VanToast from \"./Toast.mjs\";\nvar defaultOptions = {\n  icon: \"\",\n  type: \"text\",\n  message: \"\",\n  className: \"\",\n  overlay: false,\n  onClose: void 0,\n  onOpened: void 0,\n  duration: 2e3,\n  teleport: \"body\",\n  iconSize: void 0,\n  iconPrefix: void 0,\n  position: \"middle\",\n  transition: \"van-fade\",\n  forbidClick: false,\n  loadingType: void 0,\n  overlayClass: \"\",\n  overlayStyle: void 0,\n  closeOnClick: false,\n  closeOnClickOverlay: false\n};\nvar queue = [];\nvar allowMultiple = false;\nvar currentOptions = extend({}, defaultOptions);\nvar defaultOptionsMap = /* @__PURE__ */new Map();\nfunction parseOptions(message) {\n  if (isObject(message)) {\n    return message;\n  }\n  return {\n    message: message\n  };\n}\nfunction createInstance() {\n  var _mountComponent = mountComponent({\n      setup: function setup() {\n        var message = ref(\"\");\n        var _usePopupState = usePopupState(),\n          open = _usePopupState.open,\n          state = _usePopupState.state,\n          close = _usePopupState.close,\n          toggle = _usePopupState.toggle;\n        var onClosed = function onClosed() {\n          if (allowMultiple) {\n            queue = queue.filter(function (item) {\n              return item !== instance;\n            });\n            unmount();\n          }\n        };\n        var render = function render() {\n          var attrs = {\n            onClosed: onClosed,\n            \"onUpdate:show\": toggle\n          };\n          return _createVNode(VanToast, _mergeProps(state, attrs), null);\n        };\n        watch(message, function (val) {\n          state.message = val;\n        });\n        getCurrentInstance().render = render;\n        return {\n          open: open,\n          clear: close,\n          message: message\n        };\n      }\n    }),\n    instance = _mountComponent.instance,\n    unmount = _mountComponent.unmount;\n  return instance;\n}\nfunction getInstance() {\n  if (!queue.length || allowMultiple) {\n    var instance = createInstance();\n    queue.push(instance);\n  }\n  return queue[queue.length - 1];\n}\nfunction Toast() {\n  var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  if (!inBrowser) {\n    return {};\n  }\n  var toast = getInstance();\n  var parsedOptions = parseOptions(options);\n  toast.open(extend({}, currentOptions, defaultOptionsMap.get(parsedOptions.type || currentOptions.type), parsedOptions));\n  return toast;\n}\nvar createMethod = function createMethod(type) {\n  return function (options) {\n    return Toast(extend({\n      type: type\n    }, parseOptions(options)));\n  };\n};\nToast.loading = createMethod(\"loading\");\nToast.success = createMethod(\"success\");\nToast.fail = createMethod(\"fail\");\nToast.clear = function (all) {\n  var _a;\n  if (queue.length) {\n    if (all) {\n      queue.forEach(function (toast) {\n        toast.clear();\n      });\n      queue = [];\n    } else if (!allowMultiple) {\n      queue[0].clear();\n    } else {\n      (_a = queue.shift()) == null ? void 0 : _a.clear();\n    }\n  }\n};\nfunction setDefaultOptions(type, options) {\n  if (typeof type === \"string\") {\n    defaultOptionsMap.set(type, options);\n  } else {\n    extend(currentOptions, type);\n  }\n}\nToast.setDefaultOptions = setDefaultOptions;\nToast.resetDefaultOptions = function (type) {\n  if (typeof type === \"string\") {\n    defaultOptionsMap.delete(type);\n  } else {\n    currentOptions = extend({}, defaultOptions);\n    defaultOptionsMap.clear();\n  }\n};\nToast.allowMultiple = function () {\n  var value = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : true;\n  allowMultiple = value;\n};\nToast.install = function (app) {\n  app.use(withInstall(VanToast));\n  app.config.globalProperties.$toast = Toast;\n};\nexport { Toast };", "map": {"version": 3, "names": ["createVNode", "_createVNode", "mergeProps", "_mergeProps", "ref", "watch", "getCurrentInstance", "extend", "isObject", "inBrowser", "withInstall", "mountComponent", "usePopupState", "VanToast", "defaultOptions", "icon", "type", "message", "className", "overlay", "onClose", "onOpened", "duration", "teleport", "iconSize", "iconPrefix", "position", "transition", "forbidClick", "loadingType", "overlayClass", "overlayStyle", "closeOnClick", "closeOnClickOverlay", "queue", "allowMultiple", "currentOptions", "defaultOptionsMap", "Map", "parseOptions", "createInstance", "_mountComponent", "setup", "_usePopupState", "open", "state", "close", "toggle", "onClosed", "filter", "item", "instance", "unmount", "render", "attrs", "val", "clear", "getInstance", "length", "push", "Toast", "options", "arguments", "undefined", "toast", "parsedOptions", "get", "createMethod", "loading", "success", "fail", "all", "_a", "for<PERSON>ach", "shift", "setDefaultOptions", "set", "resetDefaultOptions", "delete", "value", "install", "app", "use", "config", "globalProperties", "$toast"], "sources": ["C:/Users/<USER>/Downloads/Telegram Desktop/vue3_3.0/vue3_3.0/node_modules/vant/es/toast/function-call.mjs"], "sourcesContent": ["import { createVNode as _createVNode, mergeProps as _mergeProps } from \"vue\";\nimport { ref, watch, getCurrentInstance } from \"vue\";\nimport { extend, isObject, inBrowser, withInstall } from \"../utils/index.mjs\";\nimport { mountComponent, usePopupState } from \"../utils/mount-component.mjs\";\nimport VanToast from \"./Toast.mjs\";\nconst defaultOptions = {\n  icon: \"\",\n  type: \"text\",\n  message: \"\",\n  className: \"\",\n  overlay: false,\n  onClose: void 0,\n  onOpened: void 0,\n  duration: 2e3,\n  teleport: \"body\",\n  iconSize: void 0,\n  iconPrefix: void 0,\n  position: \"middle\",\n  transition: \"van-fade\",\n  forbidClick: false,\n  loadingType: void 0,\n  overlayClass: \"\",\n  overlayStyle: void 0,\n  closeOnClick: false,\n  closeOnClickOverlay: false\n};\nlet queue = [];\nlet allowMultiple = false;\nlet currentOptions = extend({}, defaultOptions);\nconst defaultOptionsMap = /* @__PURE__ */ new Map();\nfunction parseOptions(message) {\n  if (isObject(message)) {\n    return message;\n  }\n  return {\n    message\n  };\n}\nfunction createInstance() {\n  const {\n    instance,\n    unmount\n  } = mountComponent({\n    setup() {\n      const message = ref(\"\");\n      const {\n        open,\n        state,\n        close,\n        toggle\n      } = usePopupState();\n      const onClosed = () => {\n        if (allowMultiple) {\n          queue = queue.filter((item) => item !== instance);\n          unmount();\n        }\n      };\n      const render = () => {\n        const attrs = {\n          onClosed,\n          \"onUpdate:show\": toggle\n        };\n        return _createVNode(VanToast, _mergeProps(state, attrs), null);\n      };\n      watch(message, (val) => {\n        state.message = val;\n      });\n      getCurrentInstance().render = render;\n      return {\n        open,\n        clear: close,\n        message\n      };\n    }\n  });\n  return instance;\n}\nfunction getInstance() {\n  if (!queue.length || allowMultiple) {\n    const instance = createInstance();\n    queue.push(instance);\n  }\n  return queue[queue.length - 1];\n}\nfunction Toast(options = {}) {\n  if (!inBrowser) {\n    return {};\n  }\n  const toast = getInstance();\n  const parsedOptions = parseOptions(options);\n  toast.open(extend({}, currentOptions, defaultOptionsMap.get(parsedOptions.type || currentOptions.type), parsedOptions));\n  return toast;\n}\nconst createMethod = (type) => (options) => Toast(extend({\n  type\n}, parseOptions(options)));\nToast.loading = createMethod(\"loading\");\nToast.success = createMethod(\"success\");\nToast.fail = createMethod(\"fail\");\nToast.clear = (all) => {\n  var _a;\n  if (queue.length) {\n    if (all) {\n      queue.forEach((toast) => {\n        toast.clear();\n      });\n      queue = [];\n    } else if (!allowMultiple) {\n      queue[0].clear();\n    } else {\n      (_a = queue.shift()) == null ? void 0 : _a.clear();\n    }\n  }\n};\nfunction setDefaultOptions(type, options) {\n  if (typeof type === \"string\") {\n    defaultOptionsMap.set(type, options);\n  } else {\n    extend(currentOptions, type);\n  }\n}\nToast.setDefaultOptions = setDefaultOptions;\nToast.resetDefaultOptions = (type) => {\n  if (typeof type === \"string\") {\n    defaultOptionsMap.delete(type);\n  } else {\n    currentOptions = extend({}, defaultOptions);\n    defaultOptionsMap.clear();\n  }\n};\nToast.allowMultiple = (value = true) => {\n  allowMultiple = value;\n};\nToast.install = (app) => {\n  app.use(withInstall(VanToast));\n  app.config.globalProperties.$toast = Toast;\n};\nexport {\n  Toast\n};\n"], "mappings": "AAAA,SAASA,WAAW,IAAIC,YAAY,EAAEC,UAAU,IAAIC,WAAW,QAAQ,KAAK;AAC5E,SAASC,GAAG,EAAEC,KAAK,EAAEC,kBAAkB,QAAQ,KAAK;AACpD,SAASC,MAAM,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,oBAAoB;AAC7E,SAASC,cAAc,EAAEC,aAAa,QAAQ,8BAA8B;AAC5E,OAAOC,QAAQ,MAAM,aAAa;AAClC,IAAMC,cAAc,GAAG;EACrBC,IAAI,EAAE,EAAE;EACRC,IAAI,EAAE,MAAM;EACZC,OAAO,EAAE,EAAE;EACXC,SAAS,EAAE,EAAE;EACbC,OAAO,EAAE,KAAK;EACdC,OAAO,EAAE,KAAK,CAAC;EACfC,QAAQ,EAAE,KAAK,CAAC;EAChBC,QAAQ,EAAE,GAAG;EACbC,QAAQ,EAAE,MAAM;EAChBC,QAAQ,EAAE,KAAK,CAAC;EAChBC,UAAU,EAAE,KAAK,CAAC;EAClBC,QAAQ,EAAE,QAAQ;EAClBC,UAAU,EAAE,UAAU;EACtBC,WAAW,EAAE,KAAK;EAClBC,WAAW,EAAE,KAAK,CAAC;EACnBC,YAAY,EAAE,EAAE;EAChBC,YAAY,EAAE,KAAK,CAAC;EACpBC,YAAY,EAAE,KAAK;EACnBC,mBAAmB,EAAE;AACvB,CAAC;AACD,IAAIC,KAAK,GAAG,EAAE;AACd,IAAIC,aAAa,GAAG,KAAK;AACzB,IAAIC,cAAc,GAAG7B,MAAM,CAAC,CAAC,CAAC,EAAEO,cAAc,CAAC;AAC/C,IAAMuB,iBAAiB,GAAG,eAAgB,IAAIC,GAAG,CAAC,CAAC;AACnD,SAASC,YAAYA,CAACtB,OAAO,EAAE;EAC7B,IAAIT,QAAQ,CAACS,OAAO,CAAC,EAAE;IACrB,OAAOA,OAAO;EAChB;EACA,OAAO;IACLA,OAAO,EAAPA;EACF,CAAC;AACH;AACA,SAASuB,cAAcA,CAAA,EAAG;EACxB,IAAAC,eAAA,GAGI9B,cAAc,CAAC;MACjB+B,KAAK,WAAAA,MAAA,EAAG;QACN,IAAMzB,OAAO,GAAGb,GAAG,CAAC,EAAE,CAAC;QACvB,IAAAuC,cAAA,GAKI/B,aAAa,CAAC,CAAC;UAJjBgC,IAAI,GAAAD,cAAA,CAAJC,IAAI;UACJC,KAAK,GAAAF,cAAA,CAALE,KAAK;UACLC,KAAK,GAAAH,cAAA,CAALG,KAAK;UACLC,MAAM,GAAAJ,cAAA,CAANI,MAAM;QAER,IAAMC,QAAQ,GAAG,SAAXA,QAAQA,CAAA,EAAS;UACrB,IAAIb,aAAa,EAAE;YACjBD,KAAK,GAAGA,KAAK,CAACe,MAAM,CAAC,UAACC,IAAI;cAAA,OAAKA,IAAI,KAAKC,QAAQ;YAAA,EAAC;YACjDC,OAAO,CAAC,CAAC;UACX;QACF,CAAC;QACD,IAAMC,MAAM,GAAG,SAATA,MAAMA,CAAA,EAAS;UACnB,IAAMC,KAAK,GAAG;YACZN,QAAQ,EAARA,QAAQ;YACR,eAAe,EAAED;UACnB,CAAC;UACD,OAAO9C,YAAY,CAACY,QAAQ,EAAEV,WAAW,CAAC0C,KAAK,EAAES,KAAK,CAAC,EAAE,IAAI,CAAC;QAChE,CAAC;QACDjD,KAAK,CAACY,OAAO,EAAE,UAACsC,GAAG,EAAK;UACtBV,KAAK,CAAC5B,OAAO,GAAGsC,GAAG;QACrB,CAAC,CAAC;QACFjD,kBAAkB,CAAC,CAAC,CAAC+C,MAAM,GAAGA,MAAM;QACpC,OAAO;UACLT,IAAI,EAAJA,IAAI;UACJY,KAAK,EAAEV,KAAK;UACZ7B,OAAO,EAAPA;QACF,CAAC;MACH;IACF,CAAC,CAAC;IAlCAkC,QAAQ,GAAAV,eAAA,CAARU,QAAQ;IACRC,OAAO,GAAAX,eAAA,CAAPW,OAAO;EAkCT,OAAOD,QAAQ;AACjB;AACA,SAASM,WAAWA,CAAA,EAAG;EACrB,IAAI,CAACvB,KAAK,CAACwB,MAAM,IAAIvB,aAAa,EAAE;IAClC,IAAMgB,QAAQ,GAAGX,cAAc,CAAC,CAAC;IACjCN,KAAK,CAACyB,IAAI,CAACR,QAAQ,CAAC;EACtB;EACA,OAAOjB,KAAK,CAACA,KAAK,CAACwB,MAAM,GAAG,CAAC,CAAC;AAChC;AACA,SAASE,KAAKA,CAAA,EAAe;EAAA,IAAdC,OAAO,GAAAC,SAAA,CAAAJ,MAAA,QAAAI,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,CAAC,CAAC;EACzB,IAAI,CAACrD,SAAS,EAAE;IACd,OAAO,CAAC,CAAC;EACX;EACA,IAAMuD,KAAK,GAAGP,WAAW,CAAC,CAAC;EAC3B,IAAMQ,aAAa,GAAG1B,YAAY,CAACsB,OAAO,CAAC;EAC3CG,KAAK,CAACpB,IAAI,CAACrC,MAAM,CAAC,CAAC,CAAC,EAAE6B,cAAc,EAAEC,iBAAiB,CAAC6B,GAAG,CAACD,aAAa,CAACjD,IAAI,IAAIoB,cAAc,CAACpB,IAAI,CAAC,EAAEiD,aAAa,CAAC,CAAC;EACvH,OAAOD,KAAK;AACd;AACA,IAAMG,YAAY,GAAG,SAAfA,YAAYA,CAAInD,IAAI;EAAA,OAAK,UAAC6C,OAAO;IAAA,OAAKD,KAAK,CAACrD,MAAM,CAAC;MACvDS,IAAI,EAAJA;IACF,CAAC,EAAEuB,YAAY,CAACsB,OAAO,CAAC,CAAC,CAAC;EAAA;AAAA;AAC1BD,KAAK,CAACQ,OAAO,GAAGD,YAAY,CAAC,SAAS,CAAC;AACvCP,KAAK,CAACS,OAAO,GAAGF,YAAY,CAAC,SAAS,CAAC;AACvCP,KAAK,CAACU,IAAI,GAAGH,YAAY,CAAC,MAAM,CAAC;AACjCP,KAAK,CAACJ,KAAK,GAAG,UAACe,GAAG,EAAK;EACrB,IAAIC,EAAE;EACN,IAAItC,KAAK,CAACwB,MAAM,EAAE;IAChB,IAAIa,GAAG,EAAE;MACPrC,KAAK,CAACuC,OAAO,CAAC,UAACT,KAAK,EAAK;QACvBA,KAAK,CAACR,KAAK,CAAC,CAAC;MACf,CAAC,CAAC;MACFtB,KAAK,GAAG,EAAE;IACZ,CAAC,MAAM,IAAI,CAACC,aAAa,EAAE;MACzBD,KAAK,CAAC,CAAC,CAAC,CAACsB,KAAK,CAAC,CAAC;IAClB,CAAC,MAAM;MACL,CAACgB,EAAE,GAAGtC,KAAK,CAACwC,KAAK,CAAC,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGF,EAAE,CAAChB,KAAK,CAAC,CAAC;IACpD;EACF;AACF,CAAC;AACD,SAASmB,iBAAiBA,CAAC3D,IAAI,EAAE6C,OAAO,EAAE;EACxC,IAAI,OAAO7C,IAAI,KAAK,QAAQ,EAAE;IAC5BqB,iBAAiB,CAACuC,GAAG,CAAC5D,IAAI,EAAE6C,OAAO,CAAC;EACtC,CAAC,MAAM;IACLtD,MAAM,CAAC6B,cAAc,EAAEpB,IAAI,CAAC;EAC9B;AACF;AACA4C,KAAK,CAACe,iBAAiB,GAAGA,iBAAiB;AAC3Cf,KAAK,CAACiB,mBAAmB,GAAG,UAAC7D,IAAI,EAAK;EACpC,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;IAC5BqB,iBAAiB,CAACyC,MAAM,CAAC9D,IAAI,CAAC;EAChC,CAAC,MAAM;IACLoB,cAAc,GAAG7B,MAAM,CAAC,CAAC,CAAC,EAAEO,cAAc,CAAC;IAC3CuB,iBAAiB,CAACmB,KAAK,CAAC,CAAC;EAC3B;AACF,CAAC;AACDI,KAAK,CAACzB,aAAa,GAAG,YAAkB;EAAA,IAAjB4C,KAAK,GAAAjB,SAAA,CAAAJ,MAAA,QAAAI,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,IAAI;EACjC3B,aAAa,GAAG4C,KAAK;AACvB,CAAC;AACDnB,KAAK,CAACoB,OAAO,GAAG,UAACC,GAAG,EAAK;EACvBA,GAAG,CAACC,GAAG,CAACxE,WAAW,CAACG,QAAQ,CAAC,CAAC;EAC9BoE,GAAG,CAACE,MAAM,CAACC,gBAAgB,CAACC,MAAM,GAAGzB,KAAK;AAC5C,CAAC;AACD,SACEA,KAAK"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}