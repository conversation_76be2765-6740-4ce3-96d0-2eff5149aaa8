{"ast": null, "code": "import { createVNode as _createVNode } from \"vue\";\nimport { useHeight } from \"./use-height.mjs\";\nfunction usePlaceholder(contentRef, bem) {\n  var height = useHeight(contentRef, true);\n  return function (renderContent) {\n    return _createVNode(\"div\", {\n      \"class\": bem(\"placeholder\"),\n      \"style\": {\n        height: height.value ? \"\".concat(height.value, \"px\") : void 0\n      }\n    }, [renderContent()]);\n  };\n}\nexport { usePlaceholder };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}