{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _DropdownMenu from \"./DropdownMenu.mjs\";\nvar DropdownMenu = withInstall(_DropdownMenu);\nvar stdin_default = DropdownMenu;\nexport { DropdownMenu, stdin_default as default };", "map": {"version": 3, "names": ["withInstall", "_DropdownMenu", "DropdownMenu", "stdin_default", "default"], "sources": ["C:/Users/<USER>/Downloads/Telegram Desktop/vue3_3.0/vue3_3.0/node_modules/vant/es/dropdown-menu/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _DropdownMenu from \"./DropdownMenu.mjs\";\nconst DropdownMenu = withInstall(_DropdownMenu);\nvar stdin_default = DropdownMenu;\nexport {\n  DropdownMenu,\n  stdin_default as default\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,aAAa,MAAM,oBAAoB;AAC9C,IAAMC,YAAY,GAAGF,WAAW,CAACC,aAAa,CAAC;AAC/C,IAAIE,aAAa,GAAGD,YAAY;AAChC,SACEA,YAAY,EACZC,aAAa,IAAIC,OAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}