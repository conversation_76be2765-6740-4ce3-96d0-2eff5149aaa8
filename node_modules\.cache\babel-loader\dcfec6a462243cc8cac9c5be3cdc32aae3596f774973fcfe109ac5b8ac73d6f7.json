{"ast": null, "code": "function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nfunction _iterableToArrayLimit(arr, i) { var _i = null == arr ? null : \"undefined\" != typeof Symbol && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (null != _i) { var _s, _e, _x, _r, _arr = [], _n = !0, _d = !1; try { if (_x = (_i = _i.call(arr)).next, 0 === i) { if (Object(_i) !== _i) return; _n = !1; } else for (; !(_n = (_s = _x.call(_i)).done) && (_arr.push(_s.value), _arr.length !== i); _n = !0); } catch (err) { _d = !0, _e = err; } finally { try { if (!_n && null != _i.return && (_r = _i.return(), Object(_r) !== _r)) return; } finally { if (_d) throw _e; } } return _arr; } }\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\nimport { toArray, createNamespace, isFunction } from \"../utils/index.mjs\";\nvar _createNamespace = createNamespace(\"uploader\"),\n  _createNamespace2 = _slicedToArray(_createNamespace, 3),\n  name = _createNamespace2[0],\n  bem = _createNamespace2[1],\n  t = _createNamespace2[2];\nfunction readFileContent(file, resultType) {\n  return new Promise(function (resolve) {\n    if (resultType === \"file\") {\n      resolve();\n      return;\n    }\n    var reader = new FileReader();\n    reader.onload = function (event) {\n      resolve(event.target.result);\n    };\n    if (resultType === \"dataUrl\") {\n      reader.readAsDataURL(file);\n    } else if (resultType === \"text\") {\n      reader.readAsText(file);\n    }\n  });\n}\nfunction isOversize(items, maxSize) {\n  return toArray(items).some(function (item) {\n    if (item.file) {\n      if (isFunction(maxSize)) {\n        return maxSize(item.file);\n      }\n      return item.file.size > maxSize;\n    }\n    return false;\n  });\n}\nfunction filterFiles(items, maxSize) {\n  var valid = [];\n  var invalid = [];\n  items.forEach(function (item) {\n    if (isOversize(item, maxSize)) {\n      invalid.push(item);\n    } else {\n      valid.push(item);\n    }\n  });\n  return {\n    valid: valid,\n    invalid: invalid\n  };\n}\nvar IMAGE_REGEXP = /\\.(jpeg|jpg|gif|png|svg|webp|jfif|bmp|dpg)/i;\nvar isImageUrl = function isImageUrl(url) {\n  return IMAGE_REGEXP.test(url);\n};\nfunction isImageFile(item) {\n  if (item.isImage) {\n    return true;\n  }\n  if (item.file && item.file.type) {\n    return item.file.type.indexOf(\"image\") === 0;\n  }\n  if (item.url) {\n    return isImageUrl(item.url);\n  }\n  if (typeof item.content === \"string\") {\n    return item.content.indexOf(\"data:image\") === 0;\n  }\n  return false;\n}\nexport { bem, filterFiles, isImageFile, isImageUrl, isOversize, name, readFileContent, t };", "map": {"version": 3, "names": ["toArray", "createNamespace", "isFunction", "_createNamespace", "_createNamespace2", "_slicedToArray", "name", "bem", "t", "readFileContent", "file", "resultType", "Promise", "resolve", "reader", "FileReader", "onload", "event", "target", "result", "readAsDataURL", "readAsText", "isOversize", "items", "maxSize", "some", "item", "size", "filterFiles", "valid", "invalid", "for<PERSON>ach", "push", "IMAGE_REGEXP", "isImageUrl", "url", "test", "isImageFile", "isImage", "type", "indexOf", "content"], "sources": ["C:/Users/<USER>/Downloads/Telegram Desktop/vue3_3.0/vue3_3.0/node_modules/vant/es/uploader/utils.mjs"], "sourcesContent": ["import { toArray, createNamespace, isFunction } from \"../utils/index.mjs\";\nconst [name, bem, t] = createNamespace(\"uploader\");\nfunction readFileContent(file, resultType) {\n  return new Promise((resolve) => {\n    if (resultType === \"file\") {\n      resolve();\n      return;\n    }\n    const reader = new FileReader();\n    reader.onload = (event) => {\n      resolve(event.target.result);\n    };\n    if (resultType === \"dataUrl\") {\n      reader.readAsDataURL(file);\n    } else if (resultType === \"text\") {\n      reader.readAsText(file);\n    }\n  });\n}\nfunction isOversize(items, maxSize) {\n  return toArray(items).some((item) => {\n    if (item.file) {\n      if (isFunction(maxSize)) {\n        return maxSize(item.file);\n      }\n      return item.file.size > maxSize;\n    }\n    return false;\n  });\n}\nfunction filterFiles(items, maxSize) {\n  const valid = [];\n  const invalid = [];\n  items.forEach((item) => {\n    if (isOversize(item, maxSize)) {\n      invalid.push(item);\n    } else {\n      valid.push(item);\n    }\n  });\n  return { valid, invalid };\n}\nconst IMAGE_REGEXP = /\\.(jpeg|jpg|gif|png|svg|webp|jfif|bmp|dpg)/i;\nconst isImageUrl = (url) => IMAGE_REGEXP.test(url);\nfunction isImageFile(item) {\n  if (item.isImage) {\n    return true;\n  }\n  if (item.file && item.file.type) {\n    return item.file.type.indexOf(\"image\") === 0;\n  }\n  if (item.url) {\n    return isImageUrl(item.url);\n  }\n  if (typeof item.content === \"string\") {\n    return item.content.indexOf(\"data:image\") === 0;\n  }\n  return false;\n}\nexport {\n  bem,\n  filterFiles,\n  isImageFile,\n  isImageUrl,\n  isOversize,\n  name,\n  readFileContent,\n  t\n};\n"], "mappings": ";;;;;;AAAA,SAASA,OAAO,EAAEC,eAAe,EAAEC,UAAU,QAAQ,oBAAoB;AACzE,IAAAC,gBAAA,GAAuBF,eAAe,CAAC,UAAU,CAAC;EAAAG,iBAAA,GAAAC,cAAA,CAAAF,gBAAA;EAA3CG,IAAI,GAAAF,iBAAA;EAAEG,GAAG,GAAAH,iBAAA;EAAEI,CAAC,GAAAJ,iBAAA;AACnB,SAASK,eAAeA,CAACC,IAAI,EAAEC,UAAU,EAAE;EACzC,OAAO,IAAIC,OAAO,CAAC,UAACC,OAAO,EAAK;IAC9B,IAAIF,UAAU,KAAK,MAAM,EAAE;MACzBE,OAAO,CAAC,CAAC;MACT;IACF;IACA,IAAMC,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;IAC/BD,MAAM,CAACE,MAAM,GAAG,UAACC,KAAK,EAAK;MACzBJ,OAAO,CAACI,KAAK,CAACC,MAAM,CAACC,MAAM,CAAC;IAC9B,CAAC;IACD,IAAIR,UAAU,KAAK,SAAS,EAAE;MAC5BG,MAAM,CAACM,aAAa,CAACV,IAAI,CAAC;IAC5B,CAAC,MAAM,IAAIC,UAAU,KAAK,MAAM,EAAE;MAChCG,MAAM,CAACO,UAAU,CAACX,IAAI,CAAC;IACzB;EACF,CAAC,CAAC;AACJ;AACA,SAASY,UAAUA,CAACC,KAAK,EAAEC,OAAO,EAAE;EAClC,OAAOxB,OAAO,CAACuB,KAAK,CAAC,CAACE,IAAI,CAAC,UAACC,IAAI,EAAK;IACnC,IAAIA,IAAI,CAAChB,IAAI,EAAE;MACb,IAAIR,UAAU,CAACsB,OAAO,CAAC,EAAE;QACvB,OAAOA,OAAO,CAACE,IAAI,CAAChB,IAAI,CAAC;MAC3B;MACA,OAAOgB,IAAI,CAAChB,IAAI,CAACiB,IAAI,GAAGH,OAAO;IACjC;IACA,OAAO,KAAK;EACd,CAAC,CAAC;AACJ;AACA,SAASI,WAAWA,CAACL,KAAK,EAAEC,OAAO,EAAE;EACnC,IAAMK,KAAK,GAAG,EAAE;EAChB,IAAMC,OAAO,GAAG,EAAE;EAClBP,KAAK,CAACQ,OAAO,CAAC,UAACL,IAAI,EAAK;IACtB,IAAIJ,UAAU,CAACI,IAAI,EAAEF,OAAO,CAAC,EAAE;MAC7BM,OAAO,CAACE,IAAI,CAACN,IAAI,CAAC;IACpB,CAAC,MAAM;MACLG,KAAK,CAACG,IAAI,CAACN,IAAI,CAAC;IAClB;EACF,CAAC,CAAC;EACF,OAAO;IAAEG,KAAK,EAALA,KAAK;IAAEC,OAAO,EAAPA;EAAQ,CAAC;AAC3B;AACA,IAAMG,YAAY,GAAG,6CAA6C;AAClE,IAAMC,UAAU,GAAG,SAAbA,UAAUA,CAAIC,GAAG;EAAA,OAAKF,YAAY,CAACG,IAAI,CAACD,GAAG,CAAC;AAAA;AAClD,SAASE,WAAWA,CAACX,IAAI,EAAE;EACzB,IAAIA,IAAI,CAACY,OAAO,EAAE;IAChB,OAAO,IAAI;EACb;EACA,IAAIZ,IAAI,CAAChB,IAAI,IAAIgB,IAAI,CAAChB,IAAI,CAAC6B,IAAI,EAAE;IAC/B,OAAOb,IAAI,CAAChB,IAAI,CAAC6B,IAAI,CAACC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC;EAC9C;EACA,IAAId,IAAI,CAACS,GAAG,EAAE;IACZ,OAAOD,UAAU,CAACR,IAAI,CAACS,GAAG,CAAC;EAC7B;EACA,IAAI,OAAOT,IAAI,CAACe,OAAO,KAAK,QAAQ,EAAE;IACpC,OAAOf,IAAI,CAACe,OAAO,CAACD,OAAO,CAAC,YAAY,CAAC,KAAK,CAAC;EACjD;EACA,OAAO,KAAK;AACd;AACA,SACEjC,GAAG,EACHqB,WAAW,EACXS,WAAW,EACXH,UAAU,EACVZ,UAAU,EACVhB,IAAI,EACJG,eAAe,EACfD,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}