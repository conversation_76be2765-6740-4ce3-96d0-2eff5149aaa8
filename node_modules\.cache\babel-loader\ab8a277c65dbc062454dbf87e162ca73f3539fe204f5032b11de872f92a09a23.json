{"ast": null, "code": "'use strict';\n\nfunction _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\nimport bind from './helpers/bind.js';\n\n// utils is a library of generic helper functions non-specific to axios\n\nvar toString = Object.prototype.toString;\nvar getPrototypeOf = Object.getPrototypeOf;\nvar kindOf = function (cache) {\n  return function (thing) {\n    var str = toString.call(thing);\n    return cache[str] || (cache[str] = str.slice(8, -1).toLowerCase());\n  };\n}(Object.create(null));\nvar kindOfTest = function kindOfTest(type) {\n  type = type.toLowerCase();\n  return function (thing) {\n    return kindOf(thing) === type;\n  };\n};\nvar typeOfTest = function typeOfTest(type) {\n  return function (thing) {\n    return _typeof(thing) === type;\n  };\n};\n\n/**\n * Determine if a value is an Array\n *\n * @param {Object} val The value to test\n *\n * @returns {boolean} True if value is an Array, otherwise false\n */\nvar isArray = Array.isArray;\n\n/**\n * Determine if a value is undefined\n *\n * @param {*} val The value to test\n *\n * @returns {boolean} True if the value is undefined, otherwise false\n */\nvar isUndefined = typeOfTest('undefined');\n\n/**\n * Determine if a value is a Buffer\n *\n * @param {*} val The value to test\n *\n * @returns {boolean} True if value is a Buffer, otherwise false\n */\nfunction isBuffer(val) {\n  return val !== null && !isUndefined(val) && val.constructor !== null && !isUndefined(val.constructor) && isFunction(val.constructor.isBuffer) && val.constructor.isBuffer(val);\n}\n\n/**\n * Determine if a value is an ArrayBuffer\n *\n * @param {*} val The value to test\n *\n * @returns {boolean} True if value is an ArrayBuffer, otherwise false\n */\nvar isArrayBuffer = kindOfTest('ArrayBuffer');\n\n/**\n * Determine if a value is a view on an ArrayBuffer\n *\n * @param {*} val The value to test\n *\n * @returns {boolean} True if value is a view on an ArrayBuffer, otherwise false\n */\nfunction isArrayBufferView(val) {\n  var result;\n  if (typeof ArrayBuffer !== 'undefined' && ArrayBuffer.isView) {\n    result = ArrayBuffer.isView(val);\n  } else {\n    result = val && val.buffer && isArrayBuffer(val.buffer);\n  }\n  return result;\n}\n\n/**\n * Determine if a value is a String\n *\n * @param {*} val The value to test\n *\n * @returns {boolean} True if value is a String, otherwise false\n */\nvar isString = typeOfTest('string');\n\n/**\n * Determine if a value is a Function\n *\n * @param {*} val The value to test\n * @returns {boolean} True if value is a Function, otherwise false\n */\nvar isFunction = typeOfTest('function');\n\n/**\n * Determine if a value is a Number\n *\n * @param {*} val The value to test\n *\n * @returns {boolean} True if value is a Number, otherwise false\n */\nvar isNumber = typeOfTest('number');\n\n/**\n * Determine if a value is an Object\n *\n * @param {*} thing The value to test\n *\n * @returns {boolean} True if value is an Object, otherwise false\n */\nvar isObject = function isObject(thing) {\n  return thing !== null && _typeof(thing) === 'object';\n};\n\n/**\n * Determine if a value is a Boolean\n *\n * @param {*} thing The value to test\n * @returns {boolean} True if value is a Boolean, otherwise false\n */\nvar isBoolean = function isBoolean(thing) {\n  return thing === true || thing === false;\n};\n\n/**\n * Determine if a value is a plain Object\n *\n * @param {*} val The value to test\n *\n * @returns {boolean} True if value is a plain Object, otherwise false\n */\nvar isPlainObject = function isPlainObject(val) {\n  if (kindOf(val) !== 'object') {\n    return false;\n  }\n  var prototype = getPrototypeOf(val);\n  return (prototype === null || prototype === Object.prototype || Object.getPrototypeOf(prototype) === null) && !(Symbol.toStringTag in val) && !(Symbol.iterator in val);\n};\n\n/**\n * Determine if a value is a Date\n *\n * @param {*} val The value to test\n *\n * @returns {boolean} True if value is a Date, otherwise false\n */\nvar isDate = kindOfTest('Date');\n\n/**\n * Determine if a value is a File\n *\n * @param {*} val The value to test\n *\n * @returns {boolean} True if value is a File, otherwise false\n */\nvar isFile = kindOfTest('File');\n\n/**\n * Determine if a value is a Blob\n *\n * @param {*} val The value to test\n *\n * @returns {boolean} True if value is a Blob, otherwise false\n */\nvar isBlob = kindOfTest('Blob');\n\n/**\n * Determine if a value is a FileList\n *\n * @param {*} val The value to test\n *\n * @returns {boolean} True if value is a File, otherwise false\n */\nvar isFileList = kindOfTest('FileList');\n\n/**\n * Determine if a value is a Stream\n *\n * @param {*} val The value to test\n *\n * @returns {boolean} True if value is a Stream, otherwise false\n */\nvar isStream = function isStream(val) {\n  return isObject(val) && isFunction(val.pipe);\n};\n\n/**\n * Determine if a value is a FormData\n *\n * @param {*} thing The value to test\n *\n * @returns {boolean} True if value is an FormData, otherwise false\n */\nvar isFormData = function isFormData(thing) {\n  var kind;\n  return thing && (typeof FormData === 'function' && thing instanceof FormData || isFunction(thing.append) && ((kind = kindOf(thing)) === 'formdata' ||\n  // detect form-data instance\n  kind === 'object' && isFunction(thing.toString) && thing.toString() === '[object FormData]'));\n};\n\n/**\n * Determine if a value is a URLSearchParams object\n *\n * @param {*} val The value to test\n *\n * @returns {boolean} True if value is a URLSearchParams object, otherwise false\n */\nvar isURLSearchParams = kindOfTest('URLSearchParams');\n\n/**\n * Trim excess whitespace off the beginning and end of a string\n *\n * @param {String} str The String to trim\n *\n * @returns {String} The String freed of excess whitespace\n */\nvar trim = function trim(str) {\n  return str.trim ? str.trim() : str.replace(/^[\\s\\uFEFF\\xA0]+|[\\s\\uFEFF\\xA0]+$/g, '');\n};\n\n/**\n * Iterate over an Array or an Object invoking a function for each item.\n *\n * If `obj` is an Array callback will be called passing\n * the value, index, and complete array for each item.\n *\n * If 'obj' is an Object callback will be called passing\n * the value, key, and complete object for each property.\n *\n * @param {Object|Array} obj The object to iterate\n * @param {Function} fn The callback to invoke for each item\n *\n * @param {Boolean} [allOwnKeys = false]\n * @returns {any}\n */\nfunction forEach(obj, fn) {\n  var _ref = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {},\n    _ref$allOwnKeys = _ref.allOwnKeys,\n    allOwnKeys = _ref$allOwnKeys === void 0 ? false : _ref$allOwnKeys;\n  // Don't bother if no value provided\n  if (obj === null || typeof obj === 'undefined') {\n    return;\n  }\n  var i;\n  var l;\n\n  // Force an array if not already something iterable\n  if (_typeof(obj) !== 'object') {\n    /*eslint no-param-reassign:0*/\n    obj = [obj];\n  }\n  if (isArray(obj)) {\n    // Iterate over array values\n    for (i = 0, l = obj.length; i < l; i++) {\n      fn.call(null, obj[i], i, obj);\n    }\n  } else {\n    // Iterate over object keys\n    var keys = allOwnKeys ? Object.getOwnPropertyNames(obj) : Object.keys(obj);\n    var len = keys.length;\n    var key;\n    for (i = 0; i < len; i++) {\n      key = keys[i];\n      fn.call(null, obj[key], key, obj);\n    }\n  }\n}\nfunction findKey(obj, key) {\n  key = key.toLowerCase();\n  var keys = Object.keys(obj);\n  var i = keys.length;\n  var _key;\n  while (i-- > 0) {\n    _key = keys[i];\n    if (key === _key.toLowerCase()) {\n      return _key;\n    }\n  }\n  return null;\n}\nvar _global = function () {\n  /*eslint no-undef:0*/\n  if (typeof globalThis !== \"undefined\") return globalThis;\n  return typeof self !== \"undefined\" ? self : typeof window !== 'undefined' ? window : global;\n}();\nvar isContextDefined = function isContextDefined(context) {\n  return !isUndefined(context) && context !== _global;\n};\n\n/**\n * Accepts varargs expecting each argument to be an object, then\n * immutably merges the properties of each object and returns result.\n *\n * When multiple objects contain the same key the later object in\n * the arguments list will take precedence.\n *\n * Example:\n *\n * ```js\n * var result = merge({foo: 123}, {foo: 456});\n * console.log(result.foo); // outputs 456\n * ```\n *\n * @param {Object} obj1 Object to merge\n *\n * @returns {Object} Result of all merge properties\n */\nfunction merge( /* obj1, obj2, obj3, ... */\n) {\n  var _ref2 = isContextDefined(this) && this || {},\n    caseless = _ref2.caseless;\n  var result = {};\n  var assignValue = function assignValue(val, key) {\n    var targetKey = caseless && findKey(result, key) || key;\n    if (isPlainObject(result[targetKey]) && isPlainObject(val)) {\n      result[targetKey] = merge(result[targetKey], val);\n    } else if (isPlainObject(val)) {\n      result[targetKey] = merge({}, val);\n    } else if (isArray(val)) {\n      result[targetKey] = val.slice();\n    } else {\n      result[targetKey] = val;\n    }\n  };\n  for (var i = 0, l = arguments.length; i < l; i++) {\n    arguments[i] && forEach(arguments[i], assignValue);\n  }\n  return result;\n}\n\n/**\n * Extends object a by mutably adding to it the properties of object b.\n *\n * @param {Object} a The object to be extended\n * @param {Object} b The object to copy properties from\n * @param {Object} thisArg The object to bind function to\n *\n * @param {Boolean} [allOwnKeys]\n * @returns {Object} The resulting value of object a\n */\nvar extend = function extend(a, b, thisArg) {\n  var _ref3 = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : {},\n    allOwnKeys = _ref3.allOwnKeys;\n  forEach(b, function (val, key) {\n    if (thisArg && isFunction(val)) {\n      a[key] = bind(val, thisArg);\n    } else {\n      a[key] = val;\n    }\n  }, {\n    allOwnKeys: allOwnKeys\n  });\n  return a;\n};\n\n/**\n * Remove byte order marker. This catches EF BB BF (the UTF-8 BOM)\n *\n * @param {string} content with BOM\n *\n * @returns {string} content value without BOM\n */\nvar stripBOM = function stripBOM(content) {\n  if (content.charCodeAt(0) === 0xFEFF) {\n    content = content.slice(1);\n  }\n  return content;\n};\n\n/**\n * Inherit the prototype methods from one constructor into another\n * @param {function} constructor\n * @param {function} superConstructor\n * @param {object} [props]\n * @param {object} [descriptors]\n *\n * @returns {void}\n */\nvar inherits = function inherits(constructor, superConstructor, props, descriptors) {\n  constructor.prototype = Object.create(superConstructor.prototype, descriptors);\n  constructor.prototype.constructor = constructor;\n  Object.defineProperty(constructor, 'super', {\n    value: superConstructor.prototype\n  });\n  props && Object.assign(constructor.prototype, props);\n};\n\n/**\n * Resolve object with deep prototype chain to a flat object\n * @param {Object} sourceObj source object\n * @param {Object} [destObj]\n * @param {Function|Boolean} [filter]\n * @param {Function} [propFilter]\n *\n * @returns {Object}\n */\nvar toFlatObject = function toFlatObject(sourceObj, destObj, filter, propFilter) {\n  var props;\n  var i;\n  var prop;\n  var merged = {};\n  destObj = destObj || {};\n  // eslint-disable-next-line no-eq-null,eqeqeq\n  if (sourceObj == null) return destObj;\n  do {\n    props = Object.getOwnPropertyNames(sourceObj);\n    i = props.length;\n    while (i-- > 0) {\n      prop = props[i];\n      if ((!propFilter || propFilter(prop, sourceObj, destObj)) && !merged[prop]) {\n        destObj[prop] = sourceObj[prop];\n        merged[prop] = true;\n      }\n    }\n    sourceObj = filter !== false && getPrototypeOf(sourceObj);\n  } while (sourceObj && (!filter || filter(sourceObj, destObj)) && sourceObj !== Object.prototype);\n  return destObj;\n};\n\n/**\n * Determines whether a string ends with the characters of a specified string\n *\n * @param {String} str\n * @param {String} searchString\n * @param {Number} [position= 0]\n *\n * @returns {boolean}\n */\nvar endsWith = function endsWith(str, searchString, position) {\n  str = String(str);\n  if (position === undefined || position > str.length) {\n    position = str.length;\n  }\n  position -= searchString.length;\n  var lastIndex = str.indexOf(searchString, position);\n  return lastIndex !== -1 && lastIndex === position;\n};\n\n/**\n * Returns new array from array like object or null if failed\n *\n * @param {*} [thing]\n *\n * @returns {?Array}\n */\nvar toArray = function toArray(thing) {\n  if (!thing) return null;\n  if (isArray(thing)) return thing;\n  var i = thing.length;\n  if (!isNumber(i)) return null;\n  var arr = new Array(i);\n  while (i-- > 0) {\n    arr[i] = thing[i];\n  }\n  return arr;\n};\n\n/**\n * Checking if the Uint8Array exists and if it does, it returns a function that checks if the\n * thing passed in is an instance of Uint8Array\n *\n * @param {TypedArray}\n *\n * @returns {Array}\n */\n// eslint-disable-next-line func-names\nvar isTypedArray = function (TypedArray) {\n  // eslint-disable-next-line func-names\n  return function (thing) {\n    return TypedArray && thing instanceof TypedArray;\n  };\n}(typeof Uint8Array !== 'undefined' && getPrototypeOf(Uint8Array));\n\n/**\n * For each entry in the object, call the function with the key and value.\n *\n * @param {Object<any, any>} obj - The object to iterate over.\n * @param {Function} fn - The function to call for each entry.\n *\n * @returns {void}\n */\nvar forEachEntry = function forEachEntry(obj, fn) {\n  var generator = obj && obj[Symbol.iterator];\n  var iterator = generator.call(obj);\n  var result;\n  while ((result = iterator.next()) && !result.done) {\n    var pair = result.value;\n    fn.call(obj, pair[0], pair[1]);\n  }\n};\n\n/**\n * It takes a regular expression and a string, and returns an array of all the matches\n *\n * @param {string} regExp - The regular expression to match against.\n * @param {string} str - The string to search.\n *\n * @returns {Array<boolean>}\n */\nvar matchAll = function matchAll(regExp, str) {\n  var matches;\n  var arr = [];\n  while ((matches = regExp.exec(str)) !== null) {\n    arr.push(matches);\n  }\n  return arr;\n};\n\n/* Checking if the kindOfTest function returns true when passed an HTMLFormElement. */\nvar isHTMLForm = kindOfTest('HTMLFormElement');\nvar toCamelCase = function toCamelCase(str) {\n  return str.toLowerCase().replace(/[-_\\s]([a-z\\d])(\\w*)/g, function replacer(m, p1, p2) {\n    return p1.toUpperCase() + p2;\n  });\n};\n\n/* Creating a function that will check if an object has a property. */\nvar hasOwnProperty = function (_ref4) {\n  var hasOwnProperty = _ref4.hasOwnProperty;\n  return function (obj, prop) {\n    return hasOwnProperty.call(obj, prop);\n  };\n}(Object.prototype);\n\n/**\n * Determine if a value is a RegExp object\n *\n * @param {*} val The value to test\n *\n * @returns {boolean} True if value is a RegExp object, otherwise false\n */\nvar isRegExp = kindOfTest('RegExp');\nvar reduceDescriptors = function reduceDescriptors(obj, reducer) {\n  var descriptors = Object.getOwnPropertyDescriptors(obj);\n  var reducedDescriptors = {};\n  forEach(descriptors, function (descriptor, name) {\n    if (reducer(descriptor, name, obj) !== false) {\n      reducedDescriptors[name] = descriptor;\n    }\n  });\n  Object.defineProperties(obj, reducedDescriptors);\n};\n\n/**\n * Makes all methods read-only\n * @param {Object} obj\n */\n\nvar freezeMethods = function freezeMethods(obj) {\n  reduceDescriptors(obj, function (descriptor, name) {\n    // skip restricted props in strict mode\n    if (isFunction(obj) && ['arguments', 'caller', 'callee'].indexOf(name) !== -1) {\n      return false;\n    }\n    var value = obj[name];\n    if (!isFunction(value)) return;\n    descriptor.enumerable = false;\n    if ('writable' in descriptor) {\n      descriptor.writable = false;\n      return;\n    }\n    if (!descriptor.set) {\n      descriptor.set = function () {\n        throw Error('Can not rewrite read-only method \\'' + name + '\\'');\n      };\n    }\n  });\n};\nvar toObjectSet = function toObjectSet(arrayOrString, delimiter) {\n  var obj = {};\n  var define = function define(arr) {\n    arr.forEach(function (value) {\n      obj[value] = true;\n    });\n  };\n  isArray(arrayOrString) ? define(arrayOrString) : define(String(arrayOrString).split(delimiter));\n  return obj;\n};\nvar noop = function noop() {};\nvar toFiniteNumber = function toFiniteNumber(value, defaultValue) {\n  value = +value;\n  return Number.isFinite(value) ? value : defaultValue;\n};\nvar ALPHA = 'abcdefghijklmnopqrstuvwxyz';\nvar DIGIT = '0123456789';\nvar ALPHABET = {\n  DIGIT: DIGIT,\n  ALPHA: ALPHA,\n  ALPHA_DIGIT: ALPHA + ALPHA.toUpperCase() + DIGIT\n};\nvar generateString = function generateString() {\n  var size = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 16;\n  var alphabet = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : ALPHABET.ALPHA_DIGIT;\n  var str = '';\n  var length = alphabet.length;\n  while (size--) {\n    str += alphabet[Math.random() * length | 0];\n  }\n  return str;\n};\n\n/**\n * If the thing is a FormData object, return true, otherwise return false.\n *\n * @param {unknown} thing - The thing to check.\n *\n * @returns {boolean}\n */\nfunction isSpecCompliantForm(thing) {\n  return !!(thing && isFunction(thing.append) && thing[Symbol.toStringTag] === 'FormData' && thing[Symbol.iterator]);\n}\nvar toJSONObject = function toJSONObject(obj) {\n  var stack = new Array(10);\n  var visit = function visit(source, i) {\n    if (isObject(source)) {\n      if (stack.indexOf(source) >= 0) {\n        return;\n      }\n      if (!('toJSON' in source)) {\n        stack[i] = source;\n        var target = isArray(source) ? [] : {};\n        forEach(source, function (value, key) {\n          var reducedValue = visit(value, i + 1);\n          !isUndefined(reducedValue) && (target[key] = reducedValue);\n        });\n        stack[i] = undefined;\n        return target;\n      }\n    }\n    return source;\n  };\n  return visit(obj, 0);\n};\nvar isAsyncFn = kindOfTest('AsyncFunction');\nvar isThenable = function isThenable(thing) {\n  return thing && (isObject(thing) || isFunction(thing)) && isFunction(thing.then) && isFunction(thing.catch);\n};\nexport default {\n  isArray: isArray,\n  isArrayBuffer: isArrayBuffer,\n  isBuffer: isBuffer,\n  isFormData: isFormData,\n  isArrayBufferView: isArrayBufferView,\n  isString: isString,\n  isNumber: isNumber,\n  isBoolean: isBoolean,\n  isObject: isObject,\n  isPlainObject: isPlainObject,\n  isUndefined: isUndefined,\n  isDate: isDate,\n  isFile: isFile,\n  isBlob: isBlob,\n  isRegExp: isRegExp,\n  isFunction: isFunction,\n  isStream: isStream,\n  isURLSearchParams: isURLSearchParams,\n  isTypedArray: isTypedArray,\n  isFileList: isFileList,\n  forEach: forEach,\n  merge: merge,\n  extend: extend,\n  trim: trim,\n  stripBOM: stripBOM,\n  inherits: inherits,\n  toFlatObject: toFlatObject,\n  kindOf: kindOf,\n  kindOfTest: kindOfTest,\n  endsWith: endsWith,\n  toArray: toArray,\n  forEachEntry: forEachEntry,\n  matchAll: matchAll,\n  isHTMLForm: isHTMLForm,\n  hasOwnProperty: hasOwnProperty,\n  hasOwnProp: hasOwnProperty,\n  // an alias to avoid ESLint no-prototype-builtins detection\n  reduceDescriptors: reduceDescriptors,\n  freezeMethods: freezeMethods,\n  toObjectSet: toObjectSet,\n  toCamelCase: toCamelCase,\n  noop: noop,\n  toFiniteNumber: toFiniteNumber,\n  findKey: findKey,\n  global: _global,\n  isContextDefined: isContextDefined,\n  ALPHABET: ALPHABET,\n  generateString: generateString,\n  isSpecCompliantForm: isSpecCompliantForm,\n  toJSONObject: toJSONObject,\n  isAsyncFn: isAsyncFn,\n  isThenable: isThenable\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}