{"ast": null, "code": "import { createVNode as _createVNode } from \"vue\";\nimport { ref, computed, defineComponent } from \"vue\";\nimport { extend, addUnit, truthProp, numericProp, unknownProp, makeStringProp, makeRequiredProp } from \"../utils/index.mjs\";\nimport { Icon } from \"../icon/index.mjs\";\nvar checkerProps = {\n  name: unknownProp,\n  shape: makeStringProp(\"round\"),\n  disabled: Boolean,\n  iconSize: numericProp,\n  modelValue: unknownProp,\n  checkedColor: String,\n  labelPosition: String,\n  labelDisabled: Boolean\n};\nvar stdin_default = defineComponent({\n  props: extend({}, checkerProps, {\n    bem: makeRequiredProp(Function),\n    role: String,\n    parent: Object,\n    checked: Boolean,\n    bindGroup: truthProp\n  }),\n  emits: [\"click\", \"toggle\"],\n  setup: function setup(props, _ref) {\n    var emit = _ref.emit,\n      slots = _ref.slots;\n    var iconRef = ref();\n    var getParentProp = function getParentProp(name) {\n      if (props.parent && props.bindGroup) {\n        return props.parent.props[name];\n      }\n    };\n    var disabled = computed(function () {\n      return getParentProp(\"disabled\") || props.disabled;\n    });\n    var direction = computed(function () {\n      return getParentProp(\"direction\");\n    });\n    var iconStyle = computed(function () {\n      var checkedColor = props.checkedColor || getParentProp(\"checkedColor\");\n      if (checkedColor && props.checked && !disabled.value) {\n        return {\n          borderColor: checkedColor,\n          backgroundColor: checkedColor\n        };\n      }\n    });\n    var onClick = function onClick(event) {\n      var target = event.target;\n      var icon = iconRef.value;\n      var iconClicked = icon === target || (icon == null ? void 0 : icon.contains(target));\n      if (!disabled.value && (iconClicked || !props.labelDisabled)) {\n        emit(\"toggle\");\n      }\n      emit(\"click\", event);\n    };\n    var renderIcon = function renderIcon() {\n      var bem = props.bem,\n        shape = props.shape,\n        checked = props.checked;\n      var iconSize = props.iconSize || getParentProp(\"iconSize\");\n      return _createVNode(\"div\", {\n        \"ref\": iconRef,\n        \"class\": bem(\"icon\", [shape, {\n          disabled: disabled.value,\n          checked: checked\n        }]),\n        \"style\": {\n          fontSize: addUnit(iconSize)\n        }\n      }, [slots.icon ? slots.icon({\n        checked: checked,\n        disabled: disabled.value\n      }) : _createVNode(Icon, {\n        \"name\": \"success\",\n        \"style\": iconStyle.value\n      }, null)]);\n    };\n    var renderLabel = function renderLabel() {\n      if (slots.default) {\n        return _createVNode(\"span\", {\n          \"class\": props.bem(\"label\", [props.labelPosition, {\n            disabled: disabled.value\n          }])\n        }, [slots.default()]);\n      }\n    };\n    return function () {\n      var nodes = props.labelPosition === \"left\" ? [renderLabel(), renderIcon()] : [renderIcon(), renderLabel()];\n      return _createVNode(\"div\", {\n        \"role\": props.role,\n        \"class\": props.bem([{\n          disabled: disabled.value,\n          \"label-disabled\": props.labelDisabled\n        }, direction.value]),\n        \"tabindex\": disabled.value ? void 0 : 0,\n        \"aria-checked\": props.checked,\n        \"onClick\": onClick\n      }, [nodes]);\n    };\n  }\n});\nexport { checkerProps, stdin_default as default };", "map": {"version": 3, "names": ["createVNode", "_createVNode", "ref", "computed", "defineComponent", "extend", "addUnit", "truthProp", "numericProp", "unknownProp", "makeStringProp", "makeRequiredProp", "Icon", "checkerProps", "name", "shape", "disabled", "Boolean", "iconSize", "modelValue", "checkedColor", "String", "labelPosition", "labelDisabled", "stdin_default", "props", "bem", "Function", "role", "parent", "Object", "checked", "bindGroup", "emits", "setup", "_ref", "emit", "slots", "iconRef", "getParentProp", "direction", "iconStyle", "value", "borderColor", "backgroundColor", "onClick", "event", "target", "icon", "iconClicked", "contains", "renderIcon", "fontSize", "renderLabel", "default", "nodes"], "sources": ["C:/Users/<USER>/Desktop/vue3_3.0/node_modules/vant/es/checkbox/Checker.mjs"], "sourcesContent": ["import { createVNode as _createVNode } from \"vue\";\nimport { ref, computed, defineComponent } from \"vue\";\nimport { extend, addUnit, truthProp, numericProp, unknownProp, makeStringProp, makeRequiredProp } from \"../utils/index.mjs\";\nimport { Icon } from \"../icon/index.mjs\";\nconst checkerProps = {\n  name: unknownProp,\n  shape: makeStringProp(\"round\"),\n  disabled: Boolean,\n  iconSize: numericProp,\n  modelValue: unknownProp,\n  checkedColor: String,\n  labelPosition: String,\n  labelDisabled: Boolean\n};\nvar stdin_default = defineComponent({\n  props: extend({}, checkerProps, {\n    bem: makeRequiredProp(Function),\n    role: String,\n    parent: Object,\n    checked: Boolean,\n    bindGroup: truthProp\n  }),\n  emits: [\"click\", \"toggle\"],\n  setup(props, {\n    emit,\n    slots\n  }) {\n    const iconRef = ref();\n    const getParentProp = (name) => {\n      if (props.parent && props.bindGroup) {\n        return props.parent.props[name];\n      }\n    };\n    const disabled = computed(() => getParentProp(\"disabled\") || props.disabled);\n    const direction = computed(() => getParentProp(\"direction\"));\n    const iconStyle = computed(() => {\n      const checkedColor = props.checkedColor || getParentProp(\"checkedColor\");\n      if (checkedColor && props.checked && !disabled.value) {\n        return {\n          borderColor: checkedColor,\n          backgroundColor: checkedColor\n        };\n      }\n    });\n    const onClick = (event) => {\n      const {\n        target\n      } = event;\n      const icon = iconRef.value;\n      const iconClicked = icon === target || (icon == null ? void 0 : icon.contains(target));\n      if (!disabled.value && (iconClicked || !props.labelDisabled)) {\n        emit(\"toggle\");\n      }\n      emit(\"click\", event);\n    };\n    const renderIcon = () => {\n      const {\n        bem,\n        shape,\n        checked\n      } = props;\n      const iconSize = props.iconSize || getParentProp(\"iconSize\");\n      return _createVNode(\"div\", {\n        \"ref\": iconRef,\n        \"class\": bem(\"icon\", [shape, {\n          disabled: disabled.value,\n          checked\n        }]),\n        \"style\": {\n          fontSize: addUnit(iconSize)\n        }\n      }, [slots.icon ? slots.icon({\n        checked,\n        disabled: disabled.value\n      }) : _createVNode(Icon, {\n        \"name\": \"success\",\n        \"style\": iconStyle.value\n      }, null)]);\n    };\n    const renderLabel = () => {\n      if (slots.default) {\n        return _createVNode(\"span\", {\n          \"class\": props.bem(\"label\", [props.labelPosition, {\n            disabled: disabled.value\n          }])\n        }, [slots.default()]);\n      }\n    };\n    return () => {\n      const nodes = props.labelPosition === \"left\" ? [renderLabel(), renderIcon()] : [renderIcon(), renderLabel()];\n      return _createVNode(\"div\", {\n        \"role\": props.role,\n        \"class\": props.bem([{\n          disabled: disabled.value,\n          \"label-disabled\": props.labelDisabled\n        }, direction.value]),\n        \"tabindex\": disabled.value ? void 0 : 0,\n        \"aria-checked\": props.checked,\n        \"onClick\": onClick\n      }, [nodes]);\n    };\n  }\n});\nexport {\n  checkerProps,\n  stdin_default as default\n};\n"], "mappings": "AAAA,SAASA,WAAW,IAAIC,YAAY,QAAQ,KAAK;AACjD,SAASC,GAAG,EAAEC,QAAQ,EAAEC,eAAe,QAAQ,KAAK;AACpD,SAASC,MAAM,EAAEC,OAAO,EAAEC,SAAS,EAAEC,WAAW,EAAEC,WAAW,EAAEC,cAAc,EAAEC,gBAAgB,QAAQ,oBAAoB;AAC3H,SAASC,IAAI,QAAQ,mBAAmB;AACxC,IAAMC,YAAY,GAAG;EACnBC,IAAI,EAAEL,WAAW;EACjBM,KAAK,EAAEL,cAAc,CAAC,OAAO,CAAC;EAC9BM,QAAQ,EAAEC,OAAO;EACjBC,QAAQ,EAAEV,WAAW;EACrBW,UAAU,EAAEV,WAAW;EACvBW,YAAY,EAAEC,MAAM;EACpBC,aAAa,EAAED,MAAM;EACrBE,aAAa,EAAEN;AACjB,CAAC;AACD,IAAIO,aAAa,GAAGpB,eAAe,CAAC;EAClCqB,KAAK,EAAEpB,MAAM,CAAC,CAAC,CAAC,EAAEQ,YAAY,EAAE;IAC9Ba,GAAG,EAAEf,gBAAgB,CAACgB,QAAQ,CAAC;IAC/BC,IAAI,EAAEP,MAAM;IACZQ,MAAM,EAAEC,MAAM;IACdC,OAAO,EAAEd,OAAO;IAChBe,SAAS,EAAEzB;EACb,CAAC,CAAC;EACF0B,KAAK,EAAE,CAAC,OAAO,EAAE,QAAQ,CAAC;EAC1BC,KAAK,WAAAA,MAACT,KAAK,EAAAU,IAAA,EAGR;IAAA,IAFDC,IAAI,GAAAD,IAAA,CAAJC,IAAI;MACJC,KAAK,GAAAF,IAAA,CAALE,KAAK;IAEL,IAAMC,OAAO,GAAGpC,GAAG,CAAC,CAAC;IACrB,IAAMqC,aAAa,GAAG,SAAhBA,aAAaA,CAAIzB,IAAI,EAAK;MAC9B,IAAIW,KAAK,CAACI,MAAM,IAAIJ,KAAK,CAACO,SAAS,EAAE;QACnC,OAAOP,KAAK,CAACI,MAAM,CAACJ,KAAK,CAACX,IAAI,CAAC;MACjC;IACF,CAAC;IACD,IAAME,QAAQ,GAAGb,QAAQ,CAAC;MAAA,OAAMoC,aAAa,CAAC,UAAU,CAAC,IAAId,KAAK,CAACT,QAAQ;IAAA,EAAC;IAC5E,IAAMwB,SAAS,GAAGrC,QAAQ,CAAC;MAAA,OAAMoC,aAAa,CAAC,WAAW,CAAC;IAAA,EAAC;IAC5D,IAAME,SAAS,GAAGtC,QAAQ,CAAC,YAAM;MAC/B,IAAMiB,YAAY,GAAGK,KAAK,CAACL,YAAY,IAAImB,aAAa,CAAC,cAAc,CAAC;MACxE,IAAInB,YAAY,IAAIK,KAAK,CAACM,OAAO,IAAI,CAACf,QAAQ,CAAC0B,KAAK,EAAE;QACpD,OAAO;UACLC,WAAW,EAAEvB,YAAY;UACzBwB,eAAe,EAAExB;QACnB,CAAC;MACH;IACF,CAAC,CAAC;IACF,IAAMyB,OAAO,GAAG,SAAVA,OAAOA,CAAIC,KAAK,EAAK;MACzB,IACEC,MAAM,GACJD,KAAK,CADPC,MAAM;MAER,IAAMC,IAAI,GAAGV,OAAO,CAACI,KAAK;MAC1B,IAAMO,WAAW,GAAGD,IAAI,KAAKD,MAAM,KAAKC,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACE,QAAQ,CAACH,MAAM,CAAC,CAAC;MACtF,IAAI,CAAC/B,QAAQ,CAAC0B,KAAK,KAAKO,WAAW,IAAI,CAACxB,KAAK,CAACF,aAAa,CAAC,EAAE;QAC5Da,IAAI,CAAC,QAAQ,CAAC;MAChB;MACAA,IAAI,CAAC,OAAO,EAAEU,KAAK,CAAC;IACtB,CAAC;IACD,IAAMK,UAAU,GAAG,SAAbA,UAAUA,CAAA,EAAS;MACvB,IACEzB,GAAG,GAGDD,KAAK,CAHPC,GAAG;QACHX,KAAK,GAEHU,KAAK,CAFPV,KAAK;QACLgB,OAAO,GACLN,KAAK,CADPM,OAAO;MAET,IAAMb,QAAQ,GAAGO,KAAK,CAACP,QAAQ,IAAIqB,aAAa,CAAC,UAAU,CAAC;MAC5D,OAAOtC,YAAY,CAAC,KAAK,EAAE;QACzB,KAAK,EAAEqC,OAAO;QACd,OAAO,EAAEZ,GAAG,CAAC,MAAM,EAAE,CAACX,KAAK,EAAE;UAC3BC,QAAQ,EAAEA,QAAQ,CAAC0B,KAAK;UACxBX,OAAO,EAAPA;QACF,CAAC,CAAC,CAAC;QACH,OAAO,EAAE;UACPqB,QAAQ,EAAE9C,OAAO,CAACY,QAAQ;QAC5B;MACF,CAAC,EAAE,CAACmB,KAAK,CAACW,IAAI,GAAGX,KAAK,CAACW,IAAI,CAAC;QAC1BjB,OAAO,EAAPA,OAAO;QACPf,QAAQ,EAAEA,QAAQ,CAAC0B;MACrB,CAAC,CAAC,GAAGzC,YAAY,CAACW,IAAI,EAAE;QACtB,MAAM,EAAE,SAAS;QACjB,OAAO,EAAE6B,SAAS,CAACC;MACrB,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;IACZ,CAAC;IACD,IAAMW,WAAW,GAAG,SAAdA,WAAWA,CAAA,EAAS;MACxB,IAAIhB,KAAK,CAACiB,OAAO,EAAE;QACjB,OAAOrD,YAAY,CAAC,MAAM,EAAE;UAC1B,OAAO,EAAEwB,KAAK,CAACC,GAAG,CAAC,OAAO,EAAE,CAACD,KAAK,CAACH,aAAa,EAAE;YAChDN,QAAQ,EAAEA,QAAQ,CAAC0B;UACrB,CAAC,CAAC;QACJ,CAAC,EAAE,CAACL,KAAK,CAACiB,OAAO,CAAC,CAAC,CAAC,CAAC;MACvB;IACF,CAAC;IACD,OAAO,YAAM;MACX,IAAMC,KAAK,GAAG9B,KAAK,CAACH,aAAa,KAAK,MAAM,GAAG,CAAC+B,WAAW,CAAC,CAAC,EAAEF,UAAU,CAAC,CAAC,CAAC,GAAG,CAACA,UAAU,CAAC,CAAC,EAAEE,WAAW,CAAC,CAAC,CAAC;MAC5G,OAAOpD,YAAY,CAAC,KAAK,EAAE;QACzB,MAAM,EAAEwB,KAAK,CAACG,IAAI;QAClB,OAAO,EAAEH,KAAK,CAACC,GAAG,CAAC,CAAC;UAClBV,QAAQ,EAAEA,QAAQ,CAAC0B,KAAK;UACxB,gBAAgB,EAAEjB,KAAK,CAACF;QAC1B,CAAC,EAAEiB,SAAS,CAACE,KAAK,CAAC,CAAC;QACpB,UAAU,EAAE1B,QAAQ,CAAC0B,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC;QACvC,cAAc,EAAEjB,KAAK,CAACM,OAAO;QAC7B,SAAS,EAAEc;MACb,CAAC,EAAE,CAACU,KAAK,CAAC,CAAC;IACb,CAAC;EACH;AACF,CAAC,CAAC;AACF,SACE1C,YAAY,EACZW,aAAa,IAAI8B,OAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}