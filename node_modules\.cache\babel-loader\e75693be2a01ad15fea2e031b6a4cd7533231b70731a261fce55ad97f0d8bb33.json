{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _Switch from \"./Switch.mjs\";\nvar Switch = withInstall(_Switch);\nvar stdin_default = Switch;\nexport { Switch, stdin_default as default };", "map": {"version": 3, "names": ["withInstall", "_Switch", "Switch", "stdin_default", "default"], "sources": ["C:/Users/<USER>/Desktop/vue3_3.0/node_modules/vant/es/switch/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _Switch from \"./Switch.mjs\";\nconst Switch = withInstall(_Switch);\nvar stdin_default = Switch;\nexport {\n  Switch,\n  stdin_default as default\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,OAAO,MAAM,cAAc;AAClC,IAAMC,MAAM,GAAGF,WAAW,CAACC,OAAO,CAAC;AACnC,IAAIE,aAAa,GAAGD,MAAM;AAC1B,SACEA,MAAM,EACNC,aAAa,IAAIC,OAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}