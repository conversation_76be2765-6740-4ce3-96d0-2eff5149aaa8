{"name": "@types/sockjs", "version": "0.3.33", "description": "TypeScript definitions for sockjs", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/sockjs", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/pmccloghrylaing", "githubUsername": "pmccloghrylaing"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/sockjs"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "60705ce98e42b180403cdab479820d2e483d02749f85cb6ef9bbd39ee05102af", "typeScriptVersion": "3.6"}