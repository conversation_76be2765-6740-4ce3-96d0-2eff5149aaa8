{"ast": null, "code": "import axios from './lib/axios.js';\n\n// This module is intended to unwrap Axios default export as named.\n// Keep top-level export same with static properties\n// so that it can keep same with es module or cjs\nvar Axios = axios.Axios,\n  AxiosError = axios.AxiosError,\n  CanceledError = axios.CanceledError,\n  isCancel = axios.isCancel,\n  CancelToken = axios.CancelToken,\n  VERSION = axios.VERSION,\n  all = axios.all,\n  Cancel = axios.Cancel,\n  isAxiosError = axios.isAxiosError,\n  spread = axios.spread,\n  toFormData = axios.toFormData,\n  AxiosHeaders = axios.AxiosHeaders,\n  HttpStatusCode = axios.HttpStatusCode,\n  formToJSON = axios.formToJSON,\n  mergeConfig = axios.mergeConfig;\nexport { axios as default, Axios, AxiosError, CanceledError, isCancel, CancelToken, VERSION, all, Cancel, isAxiosError, spread, toFormData, AxiosHeaders, HttpStatusCode, formToJSON, mergeConfig };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}