{"ast": null, "code": "function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nfunction _iterableToArrayLimit(arr, i) { var _i = null == arr ? null : \"undefined\" != typeof Symbol && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (null != _i) { var _s, _e, _x, _r, _arr = [], _n = !0, _d = !1; try { if (_x = (_i = _i.call(arr)).next, 0 === i) { if (Object(_i) !== _i) return; _n = !1; } else for (; !(_n = (_s = _x.call(_i)).done) && (_arr.push(_s.value), _arr.length !== i); _n = !0); } catch (err) { _d = !0, _e = err; } finally { try { if (!_n && null != _i.return && (_r = _i.return(), Object(_r) !== _r)) return; } finally { if (_d) throw _e; } } return _arr; } }\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\nimport { mergeProps as _mergeProps, createVNode as _createVNode } from \"vue\";\nimport { defineComponent } from \"vue\";\nimport { addUnit, truthProp, numericProp, getSizeStyle, makeStringProp, makeNumericProp, createNamespace } from \"../utils/index.mjs\";\nvar _createNamespace = createNamespace(\"skeleton\"),\n  _createNamespace2 = _slicedToArray(_createNamespace, 2),\n  name = _createNamespace2[0],\n  bem = _createNamespace2[1];\nvar DEFAULT_ROW_WIDTH = \"100%\";\nvar DEFAULT_LAST_ROW_WIDTH = \"60%\";\nvar skeletonProps = {\n  row: makeNumericProp(0),\n  title: Boolean,\n  round: Boolean,\n  avatar: Boolean,\n  loading: truthProp,\n  animate: truthProp,\n  avatarSize: numericProp,\n  titleWidth: numericProp,\n  avatarShape: makeStringProp(\"round\"),\n  rowWidth: {\n    type: [Number, String, Array],\n    default: DEFAULT_ROW_WIDTH\n  }\n};\nvar stdin_default = defineComponent({\n  name: name,\n  inheritAttrs: false,\n  props: skeletonProps,\n  setup: function setup(props, _ref) {\n    var slots = _ref.slots,\n      attrs = _ref.attrs;\n    var renderAvatar = function renderAvatar() {\n      if (props.avatar) {\n        return _createVNode(\"div\", {\n          \"class\": bem(\"avatar\", props.avatarShape),\n          \"style\": getSizeStyle(props.avatarSize)\n        }, null);\n      }\n    };\n    var renderTitle = function renderTitle() {\n      if (props.title) {\n        return _createVNode(\"h3\", {\n          \"class\": bem(\"title\"),\n          \"style\": {\n            width: addUnit(props.titleWidth)\n          }\n        }, null);\n      }\n    };\n    var getRowWidth = function getRowWidth(index) {\n      var rowWidth = props.rowWidth;\n      if (rowWidth === DEFAULT_ROW_WIDTH && index === +props.row - 1) {\n        return DEFAULT_LAST_ROW_WIDTH;\n      }\n      if (Array.isArray(rowWidth)) {\n        return rowWidth[index];\n      }\n      return rowWidth;\n    };\n    var renderRows = function renderRows() {\n      return Array(+props.row).fill(\"\").map(function (_, i) {\n        return _createVNode(\"div\", {\n          \"class\": bem(\"row\"),\n          \"style\": {\n            width: addUnit(getRowWidth(i))\n          }\n        }, null);\n      });\n    };\n    return function () {\n      var _a;\n      if (!props.loading) {\n        return (_a = slots.default) == null ? void 0 : _a.call(slots);\n      }\n      return _createVNode(\"div\", _mergeProps({\n        \"class\": bem({\n          animate: props.animate,\n          round: props.round\n        })\n      }, attrs), [renderAvatar(), _createVNode(\"div\", {\n        \"class\": bem(\"content\")\n      }, [renderTitle(), renderRows()])]);\n    };\n  }\n});\nexport { stdin_default as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}