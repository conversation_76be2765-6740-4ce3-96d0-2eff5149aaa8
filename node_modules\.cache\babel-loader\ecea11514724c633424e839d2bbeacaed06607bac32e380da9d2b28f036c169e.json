{"ast": null, "code": "function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nfunction _iterableToArrayLimit(arr, i) { var _i = null == arr ? null : \"undefined\" != typeof Symbol && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (null != _i) { var _s, _e, _x, _r, _arr = [], _n = !0, _d = !1; try { if (_x = (_i = _i.call(arr)).next, 0 === i) { if (Object(_i) !== _i) return; _n = !1; } else for (; !(_n = (_s = _x.call(_i)).done) && (_arr.push(_s.value), _arr.length !== i); _n = !0); } catch (err) { _d = !0, _e = err; } finally { try { if (!_n && null != _i.return && (_r = _i.return(), Object(_r) !== _r)) return; } finally { if (_d) throw _e; } } return _arr; } }\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\nimport { createVNode as _createVNode } from \"vue\";\nimport { defineComponent } from \"vue\";\nimport { useId } from \"../composables/use-id.mjs\";\nimport { getSizeStyle, makeStringProp, createNamespace } from \"../utils/index.mjs\";\nvar _createNamespace = createNamespace(\"empty\"),\n  _createNamespace2 = _slicedToArray(_createNamespace, 2),\n  name = _createNamespace2[0],\n  bem = _createNamespace2[1];\nvar emptyProps = {\n  image: makeStringProp(\"default\"),\n  imageSize: [Number, String, Array],\n  description: String\n};\nvar stdin_default = defineComponent({\n  name: name,\n  props: emptyProps,\n  setup: function setup(props, _ref) {\n    var slots = _ref.slots;\n    var renderDescription = function renderDescription() {\n      var description = slots.description ? slots.description() : props.description;\n      if (description) {\n        return _createVNode(\"p\", {\n          \"class\": bem(\"description\")\n        }, [description]);\n      }\n    };\n    var renderBottom = function renderBottom() {\n      if (slots.default) {\n        return _createVNode(\"div\", {\n          \"class\": bem(\"bottom\")\n        }, [slots.default()]);\n      }\n    };\n    var baseId = useId();\n    var getId = function getId(num) {\n      return \"\".concat(baseId, \"-\").concat(num);\n    };\n    var getUrlById = function getUrlById(num) {\n      return \"url(#\".concat(getId(num), \")\");\n    };\n    var renderStop = function renderStop(color, offset, opacity) {\n      return _createVNode(\"stop\", {\n        \"stop-color\": color,\n        \"offset\": \"\".concat(offset, \"%\"),\n        \"stop-opacity\": opacity\n      }, null);\n    };\n    var renderStops = function renderStops(fromColor, toColor) {\n      return [renderStop(fromColor, 0), renderStop(toColor, 100)];\n    };\n    var renderShadow = function renderShadow(id) {\n      return [_createVNode(\"defs\", null, [_createVNode(\"radialGradient\", {\n        \"id\": getId(id),\n        \"cx\": \"50%\",\n        \"cy\": \"54%\",\n        \"fx\": \"50%\",\n        \"fy\": \"54%\",\n        \"r\": \"297%\",\n        \"gradientTransform\": \"matrix(-.16 0 0 -.33 .58 .72)\"\n      }, [renderStop(\"#EBEDF0\", 0), renderStop(\"#F2F3F5\", 100, 0.3)])]), _createVNode(\"ellipse\", {\n        \"fill\": getUrlById(id),\n        \"opacity\": \".8\",\n        \"cx\": \"80\",\n        \"cy\": \"140\",\n        \"rx\": \"46\",\n        \"ry\": \"8\"\n      }, null)];\n    };\n    var renderBuilding = function renderBuilding() {\n      return [_createVNode(\"defs\", null, [_createVNode(\"linearGradient\", {\n        \"id\": getId(\"a\"),\n        \"x1\": \"64%\",\n        \"y1\": \"100%\",\n        \"x2\": \"64%\"\n      }, [renderStop(\"#FFF\", 0, 0.5), renderStop(\"#F2F3F5\", 100)])]), _createVNode(\"g\", {\n        \"opacity\": \".8\"\n      }, [_createVNode(\"path\", {\n        \"d\": \"M36 131V53H16v20H2v58h34z\",\n        \"fill\": getUrlById(\"a\")\n      }, null), _createVNode(\"path\", {\n        \"d\": \"M123 15h22v14h9v77h-31V15z\",\n        \"fill\": getUrlById(\"a\")\n      }, null)])];\n    };\n    var renderCloud = function renderCloud() {\n      return [_createVNode(\"defs\", null, [_createVNode(\"linearGradient\", {\n        \"id\": getId(\"b\"),\n        \"x1\": \"64%\",\n        \"y1\": \"97%\",\n        \"x2\": \"64%\",\n        \"y2\": \"0%\"\n      }, [renderStop(\"#F2F3F5\", 0, 0.3), renderStop(\"#F2F3F5\", 100)])]), _createVNode(\"g\", {\n        \"opacity\": \".8\"\n      }, [_createVNode(\"path\", {\n        \"d\": \"M87 6c3 0 7 3 8 6a8 8 0 1 1-1 16H80a7 7 0 0 1-8-6c0-4 3-7 6-7 0-5 4-9 9-9Z\",\n        \"fill\": getUrlById(\"b\")\n      }, null), _createVNode(\"path\", {\n        \"d\": \"M19 23c2 0 3 1 4 3 2 0 4 2 4 4a4 4 0 0 1-4 3v1h-7v-1l-1 1c-2 0-3-2-3-4 0-1 1-3 3-3 0-2 2-4 4-4Z\",\n        \"fill\": getUrlById(\"b\")\n      }, null)])];\n    };\n    var renderNetwork = function renderNetwork() {\n      return _createVNode(\"svg\", {\n        \"viewBox\": \"0 0 160 160\"\n      }, [_createVNode(\"defs\", null, [_createVNode(\"linearGradient\", {\n        \"id\": getId(1),\n        \"x1\": \"64%\",\n        \"y1\": \"100%\",\n        \"x2\": \"64%\"\n      }, [renderStop(\"#FFF\", 0, 0.5), renderStop(\"#F2F3F5\", 100)]), _createVNode(\"linearGradient\", {\n        \"id\": getId(2),\n        \"x1\": \"50%\",\n        \"x2\": \"50%\",\n        \"y2\": \"84%\"\n      }, [renderStop(\"#EBEDF0\", 0), renderStop(\"#DCDEE0\", 100, 0)]), _createVNode(\"linearGradient\", {\n        \"id\": getId(3),\n        \"x1\": \"100%\",\n        \"x2\": \"100%\",\n        \"y2\": \"100%\"\n      }, [renderStops(\"#EAEDF0\", \"#DCDEE0\")]), _createVNode(\"radialGradient\", {\n        \"id\": getId(4),\n        \"cx\": \"50%\",\n        \"cy\": \"0%\",\n        \"fx\": \"50%\",\n        \"fy\": \"0%\",\n        \"r\": \"100%\",\n        \"gradientTransform\": \"matrix(0 1 -.54 0 .5 -.5)\"\n      }, [renderStop(\"#EBEDF0\", 0), renderStop(\"#FFF\", 100, 0)])]), _createVNode(\"g\", {\n        \"fill\": \"none\"\n      }, [renderBuilding(), _createVNode(\"path\", {\n        \"fill\": getUrlById(4),\n        \"d\": \"M0 139h160v21H0z\"\n      }, null), _createVNode(\"path\", {\n        \"d\": \"M80 54a7 7 0 0 1 3 13v27l-2 2h-2a2 2 0 0 1-2-2V67a7 7 0 0 1 3-13z\",\n        \"fill\": getUrlById(2)\n      }, null), _createVNode(\"g\", {\n        \"opacity\": \".6\",\n        \"stroke-linecap\": \"round\",\n        \"stroke-width\": \"7\"\n      }, [_createVNode(\"path\", {\n        \"d\": \"M64 47a19 19 0 0 0-5 13c0 5 2 10 5 13\",\n        \"stroke\": getUrlById(3)\n      }, null), _createVNode(\"path\", {\n        \"d\": \"M53 36a34 34 0 0 0 0 48\",\n        \"stroke\": getUrlById(3)\n      }, null), _createVNode(\"path\", {\n        \"d\": \"M95 73a19 19 0 0 0 6-13c0-5-2-9-6-13\",\n        \"stroke\": getUrlById(3)\n      }, null), _createVNode(\"path\", {\n        \"d\": \"M106 84a34 34 0 0 0 0-48\",\n        \"stroke\": getUrlById(3)\n      }, null)]), _createVNode(\"g\", {\n        \"transform\": \"translate(31 105)\"\n      }, [_createVNode(\"rect\", {\n        \"fill\": \"#EBEDF0\",\n        \"width\": \"98\",\n        \"height\": \"34\",\n        \"rx\": \"2\"\n      }, null), _createVNode(\"rect\", {\n        \"fill\": \"#FFF\",\n        \"x\": \"9\",\n        \"y\": \"8\",\n        \"width\": \"80\",\n        \"height\": \"18\",\n        \"rx\": \"1.1\"\n      }, null), _createVNode(\"rect\", {\n        \"fill\": \"#EBEDF0\",\n        \"x\": \"15\",\n        \"y\": \"12\",\n        \"width\": \"18\",\n        \"height\": \"6\",\n        \"rx\": \"1.1\"\n      }, null)])])]);\n    };\n    var renderMaterial = function renderMaterial() {\n      return _createVNode(\"svg\", {\n        \"viewBox\": \"0 0 160 160\"\n      }, [_createVNode(\"defs\", null, [_createVNode(\"linearGradient\", {\n        \"x1\": \"50%\",\n        \"x2\": \"50%\",\n        \"y2\": \"100%\",\n        \"id\": getId(5)\n      }, [renderStops(\"#F2F3F5\", \"#DCDEE0\")]), _createVNode(\"linearGradient\", {\n        \"x1\": \"95%\",\n        \"y1\": \"48%\",\n        \"x2\": \"5.5%\",\n        \"y2\": \"51%\",\n        \"id\": getId(6)\n      }, [renderStops(\"#EAEDF1\", \"#DCDEE0\")]), _createVNode(\"linearGradient\", {\n        \"y1\": \"45%\",\n        \"x2\": \"100%\",\n        \"y2\": \"54%\",\n        \"id\": getId(7)\n      }, [renderStops(\"#EAEDF1\", \"#DCDEE0\")])]), renderBuilding(), renderCloud(), _createVNode(\"g\", {\n        \"transform\": \"translate(36 50)\",\n        \"fill\": \"none\"\n      }, [_createVNode(\"g\", {\n        \"transform\": \"translate(8)\"\n      }, [_createVNode(\"rect\", {\n        \"fill\": \"#EBEDF0\",\n        \"opacity\": \".6\",\n        \"x\": \"38\",\n        \"y\": \"13\",\n        \"width\": \"36\",\n        \"height\": \"53\",\n        \"rx\": \"2\"\n      }, null), _createVNode(\"rect\", {\n        \"fill\": getUrlById(5),\n        \"width\": \"64\",\n        \"height\": \"66\",\n        \"rx\": \"2\"\n      }, null), _createVNode(\"rect\", {\n        \"fill\": \"#FFF\",\n        \"x\": \"6\",\n        \"y\": \"6\",\n        \"width\": \"52\",\n        \"height\": \"55\",\n        \"rx\": \"1\"\n      }, null), _createVNode(\"g\", {\n        \"transform\": \"translate(15 17)\",\n        \"fill\": getUrlById(6)\n      }, [_createVNode(\"rect\", {\n        \"width\": \"34\",\n        \"height\": \"6\",\n        \"rx\": \"1\"\n      }, null), _createVNode(\"path\", {\n        \"d\": \"M0 14h34v6H0z\"\n      }, null), _createVNode(\"rect\", {\n        \"y\": \"28\",\n        \"width\": \"34\",\n        \"height\": \"6\",\n        \"rx\": \"1\"\n      }, null)])]), _createVNode(\"rect\", {\n        \"fill\": getUrlById(7),\n        \"y\": \"61\",\n        \"width\": \"88\",\n        \"height\": \"28\",\n        \"rx\": \"1\"\n      }, null), _createVNode(\"rect\", {\n        \"fill\": \"#F7F8FA\",\n        \"x\": \"29\",\n        \"y\": \"72\",\n        \"width\": \"30\",\n        \"height\": \"6\",\n        \"rx\": \"1\"\n      }, null)])]);\n    };\n    var renderError = function renderError() {\n      return _createVNode(\"svg\", {\n        \"viewBox\": \"0 0 160 160\"\n      }, [_createVNode(\"defs\", null, [_createVNode(\"linearGradient\", {\n        \"x1\": \"50%\",\n        \"x2\": \"50%\",\n        \"y2\": \"100%\",\n        \"id\": getId(8)\n      }, [renderStops(\"#EAEDF1\", \"#DCDEE0\")])]), renderBuilding(), renderCloud(), renderShadow(\"c\"), _createVNode(\"path\", {\n        \"d\": \"m59 60 21 21 21-21h3l9 9v3L92 93l21 21v3l-9 9h-3l-21-21-21 21h-3l-9-9v-3l21-21-21-21v-3l9-9h3Z\",\n        \"fill\": getUrlById(8)\n      }, null)]);\n    };\n    var renderSearch = function renderSearch() {\n      return _createVNode(\"svg\", {\n        \"viewBox\": \"0 0 160 160\"\n      }, [_createVNode(\"defs\", null, [_createVNode(\"linearGradient\", {\n        \"x1\": \"50%\",\n        \"y1\": \"100%\",\n        \"x2\": \"50%\",\n        \"id\": getId(9)\n      }, [renderStops(\"#EEE\", \"#D8D8D8\")]), _createVNode(\"linearGradient\", {\n        \"x1\": \"100%\",\n        \"y1\": \"50%\",\n        \"y2\": \"50%\",\n        \"id\": getId(10)\n      }, [renderStops(\"#F2F3F5\", \"#DCDEE0\")]), _createVNode(\"linearGradient\", {\n        \"x1\": \"50%\",\n        \"x2\": \"50%\",\n        \"y2\": \"100%\",\n        \"id\": getId(11)\n      }, [renderStops(\"#F2F3F5\", \"#DCDEE0\")]), _createVNode(\"linearGradient\", {\n        \"x1\": \"50%\",\n        \"x2\": \"50%\",\n        \"y2\": \"100%\",\n        \"id\": getId(12)\n      }, [renderStops(\"#FFF\", \"#F7F8FA\")])]), renderBuilding(), renderCloud(), renderShadow(\"d\"), _createVNode(\"g\", {\n        \"transform\": \"rotate(-45 113 -4)\",\n        \"fill\": \"none\"\n      }, [_createVNode(\"rect\", {\n        \"fill\": getUrlById(9),\n        \"x\": \"24\",\n        \"y\": \"52.8\",\n        \"width\": \"5.8\",\n        \"height\": \"19\",\n        \"rx\": \"1\"\n      }, null), _createVNode(\"rect\", {\n        \"fill\": getUrlById(10),\n        \"x\": \"22.1\",\n        \"y\": \"67.3\",\n        \"width\": \"9.9\",\n        \"height\": \"28\",\n        \"rx\": \"1\"\n      }, null), _createVNode(\"circle\", {\n        \"stroke\": getUrlById(11),\n        \"stroke-width\": \"8\",\n        \"cx\": \"27\",\n        \"cy\": \"27\",\n        \"r\": \"27\"\n      }, null), _createVNode(\"circle\", {\n        \"fill\": getUrlById(12),\n        \"cx\": \"27\",\n        \"cy\": \"27\",\n        \"r\": \"16\"\n      }, null), _createVNode(\"path\", {\n        \"d\": \"M37 7c-8 0-15 5-16 12\",\n        \"stroke\": getUrlById(11),\n        \"stroke-width\": \"3\",\n        \"opacity\": \".5\",\n        \"stroke-linecap\": \"round\",\n        \"transform\": \"rotate(45 29 13)\"\n      }, null)])]);\n    };\n    var renderImage = function renderImage() {\n      var _a;\n      if (slots.image) {\n        return slots.image();\n      }\n      var PRESET_IMAGES = {\n        error: renderError,\n        search: renderSearch,\n        network: renderNetwork,\n        default: renderMaterial\n      };\n      return ((_a = PRESET_IMAGES[props.image]) == null ? void 0 : _a.call(PRESET_IMAGES)) || _createVNode(\"img\", {\n        \"src\": props.image\n      }, null);\n    };\n    return function () {\n      return _createVNode(\"div\", {\n        \"class\": bem()\n      }, [_createVNode(\"div\", {\n        \"class\": bem(\"image\"),\n        \"style\": getSizeStyle(props.imageSize)\n      }, [renderImage()]), renderDescription(), renderBottom()]);\n    };\n  }\n});\nexport { stdin_default as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}