{"ast": null, "code": "function _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor); } }\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, \"prototype\", { writable: false }); return Constructor; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return _typeof(key) === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (_typeof(input) !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (_typeof(res) !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\nimport { remove } from \"./util.mjs\";\nvar defaultOptions = {\n  selector: \"img\"\n};\nvar LazyContainer = /*#__PURE__*/function () {\n  function LazyContainer(_ref) {\n    var el = _ref.el,\n      binding = _ref.binding,\n      vnode = _ref.vnode,\n      lazy = _ref.lazy;\n    _classCallCheck(this, LazyContainer);\n    this.el = null;\n    this.vnode = vnode;\n    this.binding = binding;\n    this.options = {};\n    this.lazy = lazy;\n    this.queue = [];\n    this.update({\n      el: el,\n      binding: binding\n    });\n  }\n  _createClass(LazyContainer, [{\n    key: \"update\",\n    value: function update(_ref2) {\n      var _this = this;\n      var el = _ref2.el,\n        binding = _ref2.binding;\n      this.el = el;\n      this.options = Object.assign({}, defaultOptions, binding.value);\n      var imgs = this.getImgs();\n      imgs.forEach(function (el2) {\n        _this.lazy.add(el2, Object.assign({}, _this.binding, {\n          value: {\n            src: \"dataset\" in el2 ? el2.dataset.src : el2.getAttribute(\"data-src\"),\n            error: (\"dataset\" in el2 ? el2.dataset.error : el2.getAttribute(\"data-error\")) || _this.options.error,\n            loading: (\"dataset\" in el2 ? el2.dataset.loading : el2.getAttribute(\"data-loading\")) || _this.options.loading\n          }\n        }), _this.vnode);\n      });\n    }\n  }, {\n    key: \"getImgs\",\n    value: function getImgs() {\n      return Array.from(this.el.querySelectorAll(this.options.selector));\n    }\n  }, {\n    key: \"clear\",\n    value: function clear() {\n      var _this2 = this;\n      var imgs = this.getImgs();\n      imgs.forEach(function (el) {\n        return _this2.lazy.remove(el);\n      });\n      this.vnode = null;\n      this.binding = null;\n      this.lazy = null;\n    }\n  }]);\n  return LazyContainer;\n}();\nvar LazyContainerManager = /*#__PURE__*/function () {\n  function LazyContainerManager(_ref3) {\n    var lazy = _ref3.lazy;\n    _classCallCheck(this, LazyContainerManager);\n    this.lazy = lazy;\n    this.queue = [];\n  }\n  _createClass(LazyContainerManager, [{\n    key: \"bind\",\n    value: function bind(el, binding, vnode) {\n      var container = new LazyContainer({\n        el: el,\n        binding: binding,\n        vnode: vnode,\n        lazy: this.lazy\n      });\n      this.queue.push(container);\n    }\n  }, {\n    key: \"update\",\n    value: function update(el, binding, vnode) {\n      var container = this.queue.find(function (item) {\n        return item.el === el;\n      });\n      if (!container) return;\n      container.update({\n        el: el,\n        binding: binding,\n        vnode: vnode\n      });\n    }\n  }, {\n    key: \"unbind\",\n    value: function unbind(el) {\n      var container = this.queue.find(function (item) {\n        return item.el === el;\n      });\n      if (!container) return;\n      container.clear();\n      remove(this.queue, container);\n    }\n  }]);\n  return LazyContainerManager;\n}();\nexport { LazyContainerManager as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}