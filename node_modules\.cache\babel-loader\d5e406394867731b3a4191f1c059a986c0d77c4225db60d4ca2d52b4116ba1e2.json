{"ast": null, "code": "function _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\n/**\n * @fileoverview\n * - Using the 'QRCode for Javascript library'\n * - Fixed dataset of 'QRCode for Javascript library' for support full-spec.\n * - this library has no dependencies.\n *\n * <AUTHOR>\n * @see <a href=\"http://www.d-project.com/\" target=\"_blank\">http://www.d-project.com/</a>\n * @see <a href=\"http://jeromeetienne.github.com/jquery-qrcode/\" target=\"_blank\">http://jeromeetienne.github.com/jquery-qrcode/</a>\n */\nvar QRCode;\n(function (root, factory) {\n  /* CommonJS */\n  if ((typeof exports === \"undefined\" ? \"undefined\" : _typeof(exports)) == 'object') module.exports = factory();\n\n  /* AMD module */else if (typeof define == 'function' && define.amd) define(factory);\n\n  /* Global */else root.QRCode = factory();\n})(this, function () {\n  //---------------------------------------------------------------------\n  // QRCode for JavaScript\n  //\n  // Copyright (c) 2009 Kazuhiko Arase\n  //\n  // URL: http://www.d-project.com/\n  //\n  // Licensed under the MIT license:\n  //   http://www.opensource.org/licenses/mit-license.php\n  //\n  // The word \"QR Code\" is registered trademark of\n  // DENSO WAVE INCORPORATED\n  //   http://www.denso-wave.com/qrcode/faqpatent-e.html\n  //\n  //---------------------------------------------------------------------\n  function QR8bitByte(data) {\n    this.mode = QRMode.MODE_8BIT_BYTE;\n    this.data = data;\n    this.parsedData = [];\n\n    // Added to support UTF-8 Characters\n    for (var i = 0, l = this.data.length; i < l; i++) {\n      var byteArray = [];\n      var code = this.data.charCodeAt(i);\n      if (code > 0x10000) {\n        byteArray[0] = 0xF0 | (code & 0x1C0000) >>> 18;\n        byteArray[1] = 0x80 | (code & 0x3F000) >>> 12;\n        byteArray[2] = 0x80 | (code & 0xFC0) >>> 6;\n        byteArray[3] = 0x80 | code & 0x3F;\n      } else if (code > 0x800) {\n        byteArray[0] = 0xE0 | (code & 0xF000) >>> 12;\n        byteArray[1] = 0x80 | (code & 0xFC0) >>> 6;\n        byteArray[2] = 0x80 | code & 0x3F;\n      } else if (code > 0x80) {\n        byteArray[0] = 0xC0 | (code & 0x7C0) >>> 6;\n        byteArray[1] = 0x80 | code & 0x3F;\n      } else {\n        byteArray[0] = code;\n      }\n      this.parsedData.push(byteArray);\n    }\n    this.parsedData = Array.prototype.concat.apply([], this.parsedData);\n    if (this.parsedData.length != this.data.length) {\n      this.parsedData.unshift(191);\n      this.parsedData.unshift(187);\n      this.parsedData.unshift(239);\n    }\n  }\n  QR8bitByte.prototype = {\n    getLength: function getLength(buffer) {\n      return this.parsedData.length;\n    },\n    write: function write(buffer) {\n      for (var i = 0, l = this.parsedData.length; i < l; i++) {\n        buffer.put(this.parsedData[i], 8);\n      }\n    }\n  };\n  function QRCodeModel(typeNumber, errorCorrectLevel) {\n    this.typeNumber = typeNumber;\n    this.errorCorrectLevel = errorCorrectLevel;\n    this.modules = null;\n    this.moduleCount = 0;\n    this.dataCache = null;\n    this.dataList = [];\n  }\n  QRCodeModel.prototype = {\n    addData: function addData(data) {\n      var newData = new QR8bitByte(data);\n      this.dataList.push(newData);\n      this.dataCache = null;\n    },\n    isDark: function isDark(row, col) {\n      if (row < 0 || this.moduleCount <= row || col < 0 || this.moduleCount <= col) {\n        throw new Error(row + \",\" + col);\n      }\n      return this.modules[row][col];\n    },\n    getModuleCount: function getModuleCount() {\n      return this.moduleCount;\n    },\n    make: function make() {\n      this.makeImpl(false, this.getBestMaskPattern());\n    },\n    makeImpl: function makeImpl(test, maskPattern) {\n      this.moduleCount = this.typeNumber * 4 + 17;\n      this.modules = new Array(this.moduleCount);\n      for (var row = 0; row < this.moduleCount; row++) {\n        this.modules[row] = new Array(this.moduleCount);\n        for (var col = 0; col < this.moduleCount; col++) {\n          this.modules[row][col] = null;\n        }\n      }\n      this.setupPositionProbePattern(0, 0);\n      this.setupPositionProbePattern(this.moduleCount - 7, 0);\n      this.setupPositionProbePattern(0, this.moduleCount - 7);\n      this.setupPositionAdjustPattern();\n      this.setupTimingPattern();\n      this.setupTypeInfo(test, maskPattern);\n      if (this.typeNumber >= 7) {\n        this.setupTypeNumber(test);\n      }\n      if (this.dataCache == null) {\n        this.dataCache = QRCodeModel.createData(this.typeNumber, this.errorCorrectLevel, this.dataList);\n      }\n      this.mapData(this.dataCache, maskPattern);\n    },\n    setupPositionProbePattern: function setupPositionProbePattern(row, col) {\n      for (var r = -1; r <= 7; r++) {\n        if (row + r <= -1 || this.moduleCount <= row + r) continue;\n        for (var c = -1; c <= 7; c++) {\n          if (col + c <= -1 || this.moduleCount <= col + c) continue;\n          if (0 <= r && r <= 6 && (c == 0 || c == 6) || 0 <= c && c <= 6 && (r == 0 || r == 6) || 2 <= r && r <= 4 && 2 <= c && c <= 4) {\n            this.modules[row + r][col + c] = true;\n          } else {\n            this.modules[row + r][col + c] = false;\n          }\n        }\n      }\n    },\n    getBestMaskPattern: function getBestMaskPattern() {\n      var minLostPoint = 0;\n      var pattern = 0;\n      for (var i = 0; i < 8; i++) {\n        this.makeImpl(true, i);\n        var lostPoint = QRUtil.getLostPoint(this);\n        if (i == 0 || minLostPoint > lostPoint) {\n          minLostPoint = lostPoint;\n          pattern = i;\n        }\n      }\n      return pattern;\n    },\n    createMovieClip: function createMovieClip(target_mc, instance_name, depth) {\n      var qr_mc = target_mc.createEmptyMovieClip(instance_name, depth);\n      var cs = 1;\n      this.make();\n      for (var row = 0; row < this.modules.length; row++) {\n        var y = row * cs;\n        for (var col = 0; col < this.modules[row].length; col++) {\n          var x = col * cs;\n          var dark = this.modules[row][col];\n          if (dark) {\n            qr_mc.beginFill(0, 100);\n            qr_mc.moveTo(x, y);\n            qr_mc.lineTo(x + cs, y);\n            qr_mc.lineTo(x + cs, y + cs);\n            qr_mc.lineTo(x, y + cs);\n            qr_mc.endFill();\n          }\n        }\n      }\n      return qr_mc;\n    },\n    setupTimingPattern: function setupTimingPattern() {\n      for (var r = 8; r < this.moduleCount - 8; r++) {\n        if (this.modules[r][6] != null) {\n          continue;\n        }\n        this.modules[r][6] = r % 2 == 0;\n      }\n      for (var c = 8; c < this.moduleCount - 8; c++) {\n        if (this.modules[6][c] != null) {\n          continue;\n        }\n        this.modules[6][c] = c % 2 == 0;\n      }\n    },\n    setupPositionAdjustPattern: function setupPositionAdjustPattern() {\n      var pos = QRUtil.getPatternPosition(this.typeNumber);\n      for (var i = 0; i < pos.length; i++) {\n        for (var j = 0; j < pos.length; j++) {\n          var row = pos[i];\n          var col = pos[j];\n          if (this.modules[row][col] != null) {\n            continue;\n          }\n          for (var r = -2; r <= 2; r++) {\n            for (var c = -2; c <= 2; c++) {\n              if (r == -2 || r == 2 || c == -2 || c == 2 || r == 0 && c == 0) {\n                this.modules[row + r][col + c] = true;\n              } else {\n                this.modules[row + r][col + c] = false;\n              }\n            }\n          }\n        }\n      }\n    },\n    setupTypeNumber: function setupTypeNumber(test) {\n      var bits = QRUtil.getBCHTypeNumber(this.typeNumber);\n      for (var i = 0; i < 18; i++) {\n        var mod = !test && (bits >> i & 1) == 1;\n        this.modules[Math.floor(i / 3)][i % 3 + this.moduleCount - 8 - 3] = mod;\n      }\n      for (var i = 0; i < 18; i++) {\n        var mod = !test && (bits >> i & 1) == 1;\n        this.modules[i % 3 + this.moduleCount - 8 - 3][Math.floor(i / 3)] = mod;\n      }\n    },\n    setupTypeInfo: function setupTypeInfo(test, maskPattern) {\n      var data = this.errorCorrectLevel << 3 | maskPattern;\n      var bits = QRUtil.getBCHTypeInfo(data);\n      for (var i = 0; i < 15; i++) {\n        var mod = !test && (bits >> i & 1) == 1;\n        if (i < 6) {\n          this.modules[i][8] = mod;\n        } else if (i < 8) {\n          this.modules[i + 1][8] = mod;\n        } else {\n          this.modules[this.moduleCount - 15 + i][8] = mod;\n        }\n      }\n      for (var i = 0; i < 15; i++) {\n        var mod = !test && (bits >> i & 1) == 1;\n        if (i < 8) {\n          this.modules[8][this.moduleCount - i - 1] = mod;\n        } else if (i < 9) {\n          this.modules[8][15 - i - 1 + 1] = mod;\n        } else {\n          this.modules[8][15 - i - 1] = mod;\n        }\n      }\n      this.modules[this.moduleCount - 8][8] = !test;\n    },\n    mapData: function mapData(data, maskPattern) {\n      var inc = -1;\n      var row = this.moduleCount - 1;\n      var bitIndex = 7;\n      var byteIndex = 0;\n      for (var col = this.moduleCount - 1; col > 0; col -= 2) {\n        if (col == 6) col--;\n        while (true) {\n          for (var c = 0; c < 2; c++) {\n            if (this.modules[row][col - c] == null) {\n              var dark = false;\n              if (byteIndex < data.length) {\n                dark = (data[byteIndex] >>> bitIndex & 1) == 1;\n              }\n              var mask = QRUtil.getMask(maskPattern, row, col - c);\n              if (mask) {\n                dark = !dark;\n              }\n              this.modules[row][col - c] = dark;\n              bitIndex--;\n              if (bitIndex == -1) {\n                byteIndex++;\n                bitIndex = 7;\n              }\n            }\n          }\n          row += inc;\n          if (row < 0 || this.moduleCount <= row) {\n            row -= inc;\n            inc = -inc;\n            break;\n          }\n        }\n      }\n    }\n  };\n  QRCodeModel.PAD0 = 0xEC;\n  QRCodeModel.PAD1 = 0x11;\n  QRCodeModel.createData = function (typeNumber, errorCorrectLevel, dataList) {\n    var rsBlocks = QRRSBlock.getRSBlocks(typeNumber, errorCorrectLevel);\n    var buffer = new QRBitBuffer();\n    for (var i = 0; i < dataList.length; i++) {\n      var data = dataList[i];\n      buffer.put(data.mode, 4);\n      buffer.put(data.getLength(), QRUtil.getLengthInBits(data.mode, typeNumber));\n      data.write(buffer);\n    }\n    var totalDataCount = 0;\n    for (var i = 0; i < rsBlocks.length; i++) {\n      totalDataCount += rsBlocks[i].dataCount;\n    }\n    if (buffer.getLengthInBits() > totalDataCount * 8) {\n      throw new Error(\"code length overflow. (\" + buffer.getLengthInBits() + \">\" + totalDataCount * 8 + \")\");\n    }\n    if (buffer.getLengthInBits() + 4 <= totalDataCount * 8) {\n      buffer.put(0, 4);\n    }\n    while (buffer.getLengthInBits() % 8 != 0) {\n      buffer.putBit(false);\n    }\n    while (true) {\n      if (buffer.getLengthInBits() >= totalDataCount * 8) {\n        break;\n      }\n      buffer.put(QRCodeModel.PAD0, 8);\n      if (buffer.getLengthInBits() >= totalDataCount * 8) {\n        break;\n      }\n      buffer.put(QRCodeModel.PAD1, 8);\n    }\n    return QRCodeModel.createBytes(buffer, rsBlocks);\n  };\n  QRCodeModel.createBytes = function (buffer, rsBlocks) {\n    var offset = 0;\n    var maxDcCount = 0;\n    var maxEcCount = 0;\n    var dcdata = new Array(rsBlocks.length);\n    var ecdata = new Array(rsBlocks.length);\n    for (var r = 0; r < rsBlocks.length; r++) {\n      var dcCount = rsBlocks[r].dataCount;\n      var ecCount = rsBlocks[r].totalCount - dcCount;\n      maxDcCount = Math.max(maxDcCount, dcCount);\n      maxEcCount = Math.max(maxEcCount, ecCount);\n      dcdata[r] = new Array(dcCount);\n      for (var i = 0; i < dcdata[r].length; i++) {\n        dcdata[r][i] = 0xff & buffer.buffer[i + offset];\n      }\n      offset += dcCount;\n      var rsPoly = QRUtil.getErrorCorrectPolynomial(ecCount);\n      var rawPoly = new QRPolynomial(dcdata[r], rsPoly.getLength() - 1);\n      var modPoly = rawPoly.mod(rsPoly);\n      ecdata[r] = new Array(rsPoly.getLength() - 1);\n      for (var i = 0; i < ecdata[r].length; i++) {\n        var modIndex = i + modPoly.getLength() - ecdata[r].length;\n        ecdata[r][i] = modIndex >= 0 ? modPoly.get(modIndex) : 0;\n      }\n    }\n    var totalCodeCount = 0;\n    for (var i = 0; i < rsBlocks.length; i++) {\n      totalCodeCount += rsBlocks[i].totalCount;\n    }\n    var data = new Array(totalCodeCount);\n    var index = 0;\n    for (var i = 0; i < maxDcCount; i++) {\n      for (var r = 0; r < rsBlocks.length; r++) {\n        if (i < dcdata[r].length) {\n          data[index++] = dcdata[r][i];\n        }\n      }\n    }\n    for (var i = 0; i < maxEcCount; i++) {\n      for (var r = 0; r < rsBlocks.length; r++) {\n        if (i < ecdata[r].length) {\n          data[index++] = ecdata[r][i];\n        }\n      }\n    }\n    return data;\n  };\n  var QRMode = {\n    MODE_NUMBER: 1 << 0,\n    MODE_ALPHA_NUM: 1 << 1,\n    MODE_8BIT_BYTE: 1 << 2,\n    MODE_KANJI: 1 << 3\n  };\n  var QRErrorCorrectLevel = {\n    L: 1,\n    M: 0,\n    Q: 3,\n    H: 2\n  };\n  var QRMaskPattern = {\n    PATTERN000: 0,\n    PATTERN001: 1,\n    PATTERN010: 2,\n    PATTERN011: 3,\n    PATTERN100: 4,\n    PATTERN101: 5,\n    PATTERN110: 6,\n    PATTERN111: 7\n  };\n  var QRUtil = {\n    PATTERN_POSITION_TABLE: [[], [6, 18], [6, 22], [6, 26], [6, 30], [6, 34], [6, 22, 38], [6, 24, 42], [6, 26, 46], [6, 28, 50], [6, 30, 54], [6, 32, 58], [6, 34, 62], [6, 26, 46, 66], [6, 26, 48, 70], [6, 26, 50, 74], [6, 30, 54, 78], [6, 30, 56, 82], [6, 30, 58, 86], [6, 34, 62, 90], [6, 28, 50, 72, 94], [6, 26, 50, 74, 98], [6, 30, 54, 78, 102], [6, 28, 54, 80, 106], [6, 32, 58, 84, 110], [6, 30, 58, 86, 114], [6, 34, 62, 90, 118], [6, 26, 50, 74, 98, 122], [6, 30, 54, 78, 102, 126], [6, 26, 52, 78, 104, 130], [6, 30, 56, 82, 108, 134], [6, 34, 60, 86, 112, 138], [6, 30, 58, 86, 114, 142], [6, 34, 62, 90, 118, 146], [6, 30, 54, 78, 102, 126, 150], [6, 24, 50, 76, 102, 128, 154], [6, 28, 54, 80, 106, 132, 158], [6, 32, 58, 84, 110, 136, 162], [6, 26, 54, 82, 110, 138, 166], [6, 30, 58, 86, 114, 142, 170]],\n    G15: 1 << 10 | 1 << 8 | 1 << 5 | 1 << 4 | 1 << 2 | 1 << 1 | 1 << 0,\n    G18: 1 << 12 | 1 << 11 | 1 << 10 | 1 << 9 | 1 << 8 | 1 << 5 | 1 << 2 | 1 << 0,\n    G15_MASK: 1 << 14 | 1 << 12 | 1 << 10 | 1 << 4 | 1 << 1,\n    getBCHTypeInfo: function getBCHTypeInfo(data) {\n      var d = data << 10;\n      while (QRUtil.getBCHDigit(d) - QRUtil.getBCHDigit(QRUtil.G15) >= 0) {\n        d ^= QRUtil.G15 << QRUtil.getBCHDigit(d) - QRUtil.getBCHDigit(QRUtil.G15);\n      }\n      return (data << 10 | d) ^ QRUtil.G15_MASK;\n    },\n    getBCHTypeNumber: function getBCHTypeNumber(data) {\n      var d = data << 12;\n      while (QRUtil.getBCHDigit(d) - QRUtil.getBCHDigit(QRUtil.G18) >= 0) {\n        d ^= QRUtil.G18 << QRUtil.getBCHDigit(d) - QRUtil.getBCHDigit(QRUtil.G18);\n      }\n      return data << 12 | d;\n    },\n    getBCHDigit: function getBCHDigit(data) {\n      var digit = 0;\n      while (data != 0) {\n        digit++;\n        data >>>= 1;\n      }\n      return digit;\n    },\n    getPatternPosition: function getPatternPosition(typeNumber) {\n      return QRUtil.PATTERN_POSITION_TABLE[typeNumber - 1];\n    },\n    getMask: function getMask(maskPattern, i, j) {\n      switch (maskPattern) {\n        case QRMaskPattern.PATTERN000:\n          return (i + j) % 2 == 0;\n        case QRMaskPattern.PATTERN001:\n          return i % 2 == 0;\n        case QRMaskPattern.PATTERN010:\n          return j % 3 == 0;\n        case QRMaskPattern.PATTERN011:\n          return (i + j) % 3 == 0;\n        case QRMaskPattern.PATTERN100:\n          return (Math.floor(i / 2) + Math.floor(j / 3)) % 2 == 0;\n        case QRMaskPattern.PATTERN101:\n          return i * j % 2 + i * j % 3 == 0;\n        case QRMaskPattern.PATTERN110:\n          return (i * j % 2 + i * j % 3) % 2 == 0;\n        case QRMaskPattern.PATTERN111:\n          return (i * j % 3 + (i + j) % 2) % 2 == 0;\n        default:\n          throw new Error(\"bad maskPattern:\" + maskPattern);\n      }\n    },\n    getErrorCorrectPolynomial: function getErrorCorrectPolynomial(errorCorrectLength) {\n      var a = new QRPolynomial([1], 0);\n      for (var i = 0; i < errorCorrectLength; i++) {\n        a = a.multiply(new QRPolynomial([1, QRMath.gexp(i)], 0));\n      }\n      return a;\n    },\n    getLengthInBits: function getLengthInBits(mode, type) {\n      if (1 <= type && type < 10) {\n        switch (mode) {\n          case QRMode.MODE_NUMBER:\n            return 10;\n          case QRMode.MODE_ALPHA_NUM:\n            return 9;\n          case QRMode.MODE_8BIT_BYTE:\n            return 8;\n          case QRMode.MODE_KANJI:\n            return 8;\n          default:\n            throw new Error(\"mode:\" + mode);\n        }\n      } else if (type < 27) {\n        switch (mode) {\n          case QRMode.MODE_NUMBER:\n            return 12;\n          case QRMode.MODE_ALPHA_NUM:\n            return 11;\n          case QRMode.MODE_8BIT_BYTE:\n            return 16;\n          case QRMode.MODE_KANJI:\n            return 10;\n          default:\n            throw new Error(\"mode:\" + mode);\n        }\n      } else if (type < 41) {\n        switch (mode) {\n          case QRMode.MODE_NUMBER:\n            return 14;\n          case QRMode.MODE_ALPHA_NUM:\n            return 13;\n          case QRMode.MODE_8BIT_BYTE:\n            return 16;\n          case QRMode.MODE_KANJI:\n            return 12;\n          default:\n            throw new Error(\"mode:\" + mode);\n        }\n      } else {\n        throw new Error(\"type:\" + type);\n      }\n    },\n    getLostPoint: function getLostPoint(qrCode) {\n      var moduleCount = qrCode.getModuleCount();\n      var lostPoint = 0;\n      for (var row = 0; row < moduleCount; row++) {\n        for (var col = 0; col < moduleCount; col++) {\n          var sameCount = 0;\n          var dark = qrCode.isDark(row, col);\n          for (var r = -1; r <= 1; r++) {\n            if (row + r < 0 || moduleCount <= row + r) {\n              continue;\n            }\n            for (var c = -1; c <= 1; c++) {\n              if (col + c < 0 || moduleCount <= col + c) {\n                continue;\n              }\n              if (r == 0 && c == 0) {\n                continue;\n              }\n              if (dark == qrCode.isDark(row + r, col + c)) {\n                sameCount++;\n              }\n            }\n          }\n          if (sameCount > 5) {\n            lostPoint += 3 + sameCount - 5;\n          }\n        }\n      }\n      for (var row = 0; row < moduleCount - 1; row++) {\n        for (var col = 0; col < moduleCount - 1; col++) {\n          var count = 0;\n          if (qrCode.isDark(row, col)) count++;\n          if (qrCode.isDark(row + 1, col)) count++;\n          if (qrCode.isDark(row, col + 1)) count++;\n          if (qrCode.isDark(row + 1, col + 1)) count++;\n          if (count == 0 || count == 4) {\n            lostPoint += 3;\n          }\n        }\n      }\n      for (var row = 0; row < moduleCount; row++) {\n        for (var col = 0; col < moduleCount - 6; col++) {\n          if (qrCode.isDark(row, col) && !qrCode.isDark(row, col + 1) && qrCode.isDark(row, col + 2) && qrCode.isDark(row, col + 3) && qrCode.isDark(row, col + 4) && !qrCode.isDark(row, col + 5) && qrCode.isDark(row, col + 6)) {\n            lostPoint += 40;\n          }\n        }\n      }\n      for (var col = 0; col < moduleCount; col++) {\n        for (var row = 0; row < moduleCount - 6; row++) {\n          if (qrCode.isDark(row, col) && !qrCode.isDark(row + 1, col) && qrCode.isDark(row + 2, col) && qrCode.isDark(row + 3, col) && qrCode.isDark(row + 4, col) && !qrCode.isDark(row + 5, col) && qrCode.isDark(row + 6, col)) {\n            lostPoint += 40;\n          }\n        }\n      }\n      var darkCount = 0;\n      for (var col = 0; col < moduleCount; col++) {\n        for (var row = 0; row < moduleCount; row++) {\n          if (qrCode.isDark(row, col)) {\n            darkCount++;\n          }\n        }\n      }\n      var ratio = Math.abs(100 * darkCount / moduleCount / moduleCount - 50) / 5;\n      lostPoint += ratio * 10;\n      return lostPoint;\n    }\n  };\n  var QRMath = {\n    glog: function glog(n) {\n      if (n < 1) {\n        throw new Error(\"glog(\" + n + \")\");\n      }\n      return QRMath.LOG_TABLE[n];\n    },\n    gexp: function gexp(n) {\n      while (n < 0) {\n        n += 255;\n      }\n      while (n >= 256) {\n        n -= 255;\n      }\n      return QRMath.EXP_TABLE[n];\n    },\n    EXP_TABLE: new Array(256),\n    LOG_TABLE: new Array(256)\n  };\n  for (var i = 0; i < 8; i++) {\n    QRMath.EXP_TABLE[i] = 1 << i;\n  }\n  for (var i = 8; i < 256; i++) {\n    QRMath.EXP_TABLE[i] = QRMath.EXP_TABLE[i - 4] ^ QRMath.EXP_TABLE[i - 5] ^ QRMath.EXP_TABLE[i - 6] ^ QRMath.EXP_TABLE[i - 8];\n  }\n  for (var i = 0; i < 255; i++) {\n    QRMath.LOG_TABLE[QRMath.EXP_TABLE[i]] = i;\n  }\n  function QRPolynomial(num, shift) {\n    if (num.length == undefined) {\n      throw new Error(num.length + \"/\" + shift);\n    }\n    var offset = 0;\n    while (offset < num.length && num[offset] == 0) {\n      offset++;\n    }\n    this.num = new Array(num.length - offset + shift);\n    for (var i = 0; i < num.length - offset; i++) {\n      this.num[i] = num[i + offset];\n    }\n  }\n  QRPolynomial.prototype = {\n    get: function get(index) {\n      return this.num[index];\n    },\n    getLength: function getLength() {\n      return this.num.length;\n    },\n    multiply: function multiply(e) {\n      var num = new Array(this.getLength() + e.getLength() - 1);\n      for (var i = 0; i < this.getLength(); i++) {\n        for (var j = 0; j < e.getLength(); j++) {\n          num[i + j] ^= QRMath.gexp(QRMath.glog(this.get(i)) + QRMath.glog(e.get(j)));\n        }\n      }\n      return new QRPolynomial(num, 0);\n    },\n    mod: function mod(e) {\n      if (this.getLength() - e.getLength() < 0) {\n        return this;\n      }\n      var ratio = QRMath.glog(this.get(0)) - QRMath.glog(e.get(0));\n      var num = new Array(this.getLength());\n      for (var i = 0; i < this.getLength(); i++) {\n        num[i] = this.get(i);\n      }\n      for (var i = 0; i < e.getLength(); i++) {\n        num[i] ^= QRMath.gexp(QRMath.glog(e.get(i)) + ratio);\n      }\n      return new QRPolynomial(num, 0).mod(e);\n    }\n  };\n  function QRRSBlock(totalCount, dataCount) {\n    this.totalCount = totalCount;\n    this.dataCount = dataCount;\n  }\n  QRRSBlock.RS_BLOCK_TABLE = [[1, 26, 19], [1, 26, 16], [1, 26, 13], [1, 26, 9], [1, 44, 34], [1, 44, 28], [1, 44, 22], [1, 44, 16], [1, 70, 55], [1, 70, 44], [2, 35, 17], [2, 35, 13], [1, 100, 80], [2, 50, 32], [2, 50, 24], [4, 25, 9], [1, 134, 108], [2, 67, 43], [2, 33, 15, 2, 34, 16], [2, 33, 11, 2, 34, 12], [2, 86, 68], [4, 43, 27], [4, 43, 19], [4, 43, 15], [2, 98, 78], [4, 49, 31], [2, 32, 14, 4, 33, 15], [4, 39, 13, 1, 40, 14], [2, 121, 97], [2, 60, 38, 2, 61, 39], [4, 40, 18, 2, 41, 19], [4, 40, 14, 2, 41, 15], [2, 146, 116], [3, 58, 36, 2, 59, 37], [4, 36, 16, 4, 37, 17], [4, 36, 12, 4, 37, 13], [2, 86, 68, 2, 87, 69], [4, 69, 43, 1, 70, 44], [6, 43, 19, 2, 44, 20], [6, 43, 15, 2, 44, 16], [4, 101, 81], [1, 80, 50, 4, 81, 51], [4, 50, 22, 4, 51, 23], [3, 36, 12, 8, 37, 13], [2, 116, 92, 2, 117, 93], [6, 58, 36, 2, 59, 37], [4, 46, 20, 6, 47, 21], [7, 42, 14, 4, 43, 15], [4, 133, 107], [8, 59, 37, 1, 60, 38], [8, 44, 20, 4, 45, 21], [12, 33, 11, 4, 34, 12], [3, 145, 115, 1, 146, 116], [4, 64, 40, 5, 65, 41], [11, 36, 16, 5, 37, 17], [11, 36, 12, 5, 37, 13], [5, 109, 87, 1, 110, 88], [5, 65, 41, 5, 66, 42], [5, 54, 24, 7, 55, 25], [11, 36, 12], [5, 122, 98, 1, 123, 99], [7, 73, 45, 3, 74, 46], [15, 43, 19, 2, 44, 20], [3, 45, 15, 13, 46, 16], [1, 135, 107, 5, 136, 108], [10, 74, 46, 1, 75, 47], [1, 50, 22, 15, 51, 23], [2, 42, 14, 17, 43, 15], [5, 150, 120, 1, 151, 121], [9, 69, 43, 4, 70, 44], [17, 50, 22, 1, 51, 23], [2, 42, 14, 19, 43, 15], [3, 141, 113, 4, 142, 114], [3, 70, 44, 11, 71, 45], [17, 47, 21, 4, 48, 22], [9, 39, 13, 16, 40, 14], [3, 135, 107, 5, 136, 108], [3, 67, 41, 13, 68, 42], [15, 54, 24, 5, 55, 25], [15, 43, 15, 10, 44, 16], [4, 144, 116, 4, 145, 117], [17, 68, 42], [17, 50, 22, 6, 51, 23], [19, 46, 16, 6, 47, 17], [2, 139, 111, 7, 140, 112], [17, 74, 46], [7, 54, 24, 16, 55, 25], [34, 37, 13], [4, 151, 121, 5, 152, 122], [4, 75, 47, 14, 76, 48], [11, 54, 24, 14, 55, 25], [16, 45, 15, 14, 46, 16], [6, 147, 117, 4, 148, 118], [6, 73, 45, 14, 74, 46], [11, 54, 24, 16, 55, 25], [30, 46, 16, 2, 47, 17], [8, 132, 106, 4, 133, 107], [8, 75, 47, 13, 76, 48], [7, 54, 24, 22, 55, 25], [22, 45, 15, 13, 46, 16], [10, 142, 114, 2, 143, 115], [19, 74, 46, 4, 75, 47], [28, 50, 22, 6, 51, 23], [33, 46, 16, 4, 47, 17], [8, 152, 122, 4, 153, 123], [22, 73, 45, 3, 74, 46], [8, 53, 23, 26, 54, 24], [12, 45, 15, 28, 46, 16], [3, 147, 117, 10, 148, 118], [3, 73, 45, 23, 74, 46], [4, 54, 24, 31, 55, 25], [11, 45, 15, 31, 46, 16], [7, 146, 116, 7, 147, 117], [21, 73, 45, 7, 74, 46], [1, 53, 23, 37, 54, 24], [19, 45, 15, 26, 46, 16], [5, 145, 115, 10, 146, 116], [19, 75, 47, 10, 76, 48], [15, 54, 24, 25, 55, 25], [23, 45, 15, 25, 46, 16], [13, 145, 115, 3, 146, 116], [2, 74, 46, 29, 75, 47], [42, 54, 24, 1, 55, 25], [23, 45, 15, 28, 46, 16], [17, 145, 115], [10, 74, 46, 23, 75, 47], [10, 54, 24, 35, 55, 25], [19, 45, 15, 35, 46, 16], [17, 145, 115, 1, 146, 116], [14, 74, 46, 21, 75, 47], [29, 54, 24, 19, 55, 25], [11, 45, 15, 46, 46, 16], [13, 145, 115, 6, 146, 116], [14, 74, 46, 23, 75, 47], [44, 54, 24, 7, 55, 25], [59, 46, 16, 1, 47, 17], [12, 151, 121, 7, 152, 122], [12, 75, 47, 26, 76, 48], [39, 54, 24, 14, 55, 25], [22, 45, 15, 41, 46, 16], [6, 151, 121, 14, 152, 122], [6, 75, 47, 34, 76, 48], [46, 54, 24, 10, 55, 25], [2, 45, 15, 64, 46, 16], [17, 152, 122, 4, 153, 123], [29, 74, 46, 14, 75, 47], [49, 54, 24, 10, 55, 25], [24, 45, 15, 46, 46, 16], [4, 152, 122, 18, 153, 123], [13, 74, 46, 32, 75, 47], [48, 54, 24, 14, 55, 25], [42, 45, 15, 32, 46, 16], [20, 147, 117, 4, 148, 118], [40, 75, 47, 7, 76, 48], [43, 54, 24, 22, 55, 25], [10, 45, 15, 67, 46, 16], [19, 148, 118, 6, 149, 119], [18, 75, 47, 31, 76, 48], [34, 54, 24, 34, 55, 25], [20, 45, 15, 61, 46, 16]];\n  QRRSBlock.getRSBlocks = function (typeNumber, errorCorrectLevel) {\n    var rsBlock = QRRSBlock.getRsBlockTable(typeNumber, errorCorrectLevel);\n    if (rsBlock == undefined) {\n      throw new Error(\"bad rs block @ typeNumber:\" + typeNumber + \"/errorCorrectLevel:\" + errorCorrectLevel);\n    }\n    var length = rsBlock.length / 3;\n    var list = [];\n    for (var i = 0; i < length; i++) {\n      var count = rsBlock[i * 3 + 0];\n      var totalCount = rsBlock[i * 3 + 1];\n      var dataCount = rsBlock[i * 3 + 2];\n      for (var j = 0; j < count; j++) {\n        list.push(new QRRSBlock(totalCount, dataCount));\n      }\n    }\n    return list;\n  };\n  QRRSBlock.getRsBlockTable = function (typeNumber, errorCorrectLevel) {\n    switch (errorCorrectLevel) {\n      case QRErrorCorrectLevel.L:\n        return QRRSBlock.RS_BLOCK_TABLE[(typeNumber - 1) * 4 + 0];\n      case QRErrorCorrectLevel.M:\n        return QRRSBlock.RS_BLOCK_TABLE[(typeNumber - 1) * 4 + 1];\n      case QRErrorCorrectLevel.Q:\n        return QRRSBlock.RS_BLOCK_TABLE[(typeNumber - 1) * 4 + 2];\n      case QRErrorCorrectLevel.H:\n        return QRRSBlock.RS_BLOCK_TABLE[(typeNumber - 1) * 4 + 3];\n      default:\n        return undefined;\n    }\n  };\n  function QRBitBuffer() {\n    this.buffer = [];\n    this.length = 0;\n  }\n  QRBitBuffer.prototype = {\n    get: function get(index) {\n      var bufIndex = Math.floor(index / 8);\n      return (this.buffer[bufIndex] >>> 7 - index % 8 & 1) == 1;\n    },\n    put: function put(num, length) {\n      for (var i = 0; i < length; i++) {\n        this.putBit((num >>> length - i - 1 & 1) == 1);\n      }\n    },\n    getLengthInBits: function getLengthInBits() {\n      return this.length;\n    },\n    putBit: function putBit(bit) {\n      var bufIndex = Math.floor(this.length / 8);\n      if (this.buffer.length <= bufIndex) {\n        this.buffer.push(0);\n      }\n      if (bit) {\n        this.buffer[bufIndex] |= 0x80 >>> this.length % 8;\n      }\n      this.length++;\n    }\n  };\n  var QRCodeLimitLength = [[17, 14, 11, 7], [32, 26, 20, 14], [53, 42, 32, 24], [78, 62, 46, 34], [106, 84, 60, 44], [134, 106, 74, 58], [154, 122, 86, 64], [192, 152, 108, 84], [230, 180, 130, 98], [271, 213, 151, 119], [321, 251, 177, 137], [367, 287, 203, 155], [425, 331, 241, 177], [458, 362, 258, 194], [520, 412, 292, 220], [586, 450, 322, 250], [644, 504, 364, 280], [718, 560, 394, 310], [792, 624, 442, 338], [858, 666, 482, 382], [929, 711, 509, 403], [1003, 779, 565, 439], [1091, 857, 611, 461], [1171, 911, 661, 511], [1273, 997, 715, 535], [1367, 1059, 751, 593], [1465, 1125, 805, 625], [1528, 1190, 868, 658], [1628, 1264, 908, 698], [1732, 1370, 982, 742], [1840, 1452, 1030, 790], [1952, 1538, 1112, 842], [2068, 1628, 1168, 898], [2188, 1722, 1228, 958], [2303, 1809, 1283, 983], [2431, 1911, 1351, 1051], [2563, 1989, 1423, 1093], [2699, 2099, 1499, 1139], [2809, 2213, 1579, 1219], [2953, 2331, 1663, 1273]];\n  function _isSupportCanvas() {\n    return typeof CanvasRenderingContext2D != \"undefined\";\n  }\n\n  // android 2.x doesn't support Data-URI spec\n  function _getAndroid() {\n    var android = false;\n    var sAgent = navigator.userAgent;\n    if (/android/i.test(sAgent)) {\n      // android\n      android = true;\n      var aMat = sAgent.toString().match(/android ([0-9]\\.[0-9])/i);\n      if (aMat && aMat[1]) {\n        android = parseFloat(aMat[1]);\n      }\n    }\n    return android;\n  }\n  var svgDrawer = function () {\n    var Drawing = function Drawing(el, htOption) {\n      this._el = el;\n      this._htOption = htOption;\n    };\n    Drawing.prototype.draw = function (oQRCode) {\n      var _htOption = this._htOption;\n      var _el = this._el;\n      var nCount = oQRCode.getModuleCount();\n      var nWidth = Math.floor(_htOption.width / nCount);\n      var nHeight = Math.floor(_htOption.height / nCount);\n      this.clear();\n      function makeSVG(tag, attrs) {\n        var el = document.createElementNS('http://www.w3.org/2000/svg', tag);\n        for (var k in attrs) if (attrs.hasOwnProperty(k)) el.setAttribute(k, attrs[k]);\n        return el;\n      }\n      var svg = makeSVG(\"svg\", {\n        'viewBox': '0 0 ' + String(nCount) + \" \" + String(nCount),\n        'width': '100%',\n        'height': '100%',\n        'fill': _htOption.colorLight\n      });\n      svg.setAttributeNS(\"http://www.w3.org/2000/xmlns/\", \"xmlns:xlink\", \"http://www.w3.org/1999/xlink\");\n      _el.appendChild(svg);\n      svg.appendChild(makeSVG(\"rect\", {\n        \"fill\": _htOption.colorLight,\n        \"width\": \"100%\",\n        \"height\": \"100%\"\n      }));\n      svg.appendChild(makeSVG(\"rect\", {\n        \"fill\": _htOption.colorDark,\n        \"width\": \"1\",\n        \"height\": \"1\",\n        \"id\": \"template\"\n      }));\n      for (var row = 0; row < nCount; row++) {\n        for (var col = 0; col < nCount; col++) {\n          if (oQRCode.isDark(row, col)) {\n            var child = makeSVG(\"use\", {\n              \"x\": String(col),\n              \"y\": String(row)\n            });\n            child.setAttributeNS(\"http://www.w3.org/1999/xlink\", \"href\", \"#template\");\n            svg.appendChild(child);\n          }\n        }\n      }\n    };\n    Drawing.prototype.clear = function () {\n      while (this._el.hasChildNodes()) this._el.removeChild(this._el.lastChild);\n    };\n    return Drawing;\n  }();\n  var useSVG = document.documentElement.tagName.toLowerCase() === \"svg\";\n\n  // Drawing in DOM by using Table tag\n  var Drawing = useSVG ? svgDrawer : !_isSupportCanvas() ? function () {\n    var Drawing = function Drawing(el, htOption) {\n      this._el = el;\n      this._htOption = htOption;\n    };\n\n    /**\n     * Draw the QRCode\n     *\n     * @param {QRCode} oQRCode\n     */\n    Drawing.prototype.draw = function (oQRCode) {\n      var _htOption = this._htOption;\n      var _el = this._el;\n      var nCount = oQRCode.getModuleCount();\n      var nWidth = Math.floor(_htOption.width / nCount);\n      var nHeight = Math.floor(_htOption.height / nCount);\n      var aHTML = ['<table style=\"border:0;border-collapse:collapse;\">'];\n      for (var row = 0; row < nCount; row++) {\n        aHTML.push('<tr>');\n        for (var col = 0; col < nCount; col++) {\n          aHTML.push('<td style=\"border:0;border-collapse:collapse;padding:0;margin:0;width:' + nWidth + 'px;height:' + nHeight + 'px;background-color:' + (oQRCode.isDark(row, col) ? _htOption.colorDark : _htOption.colorLight) + ';\"></td>');\n        }\n        aHTML.push('</tr>');\n      }\n      aHTML.push('</table>');\n      _el.innerHTML = aHTML.join('');\n\n      // Fix the margin values as real size.\n      var elTable = _el.childNodes[0];\n      var nLeftMarginTable = (_htOption.width - elTable.offsetWidth) / 2;\n      var nTopMarginTable = (_htOption.height - elTable.offsetHeight) / 2;\n      if (nLeftMarginTable > 0 && nTopMarginTable > 0) {\n        elTable.style.margin = nTopMarginTable + \"px \" + nLeftMarginTable + \"px\";\n      }\n    };\n\n    /**\n     * Clear the QRCode\n     */\n    Drawing.prototype.clear = function () {\n      this._el.innerHTML = '';\n    };\n    return Drawing;\n  }() : function () {\n    // Drawing in Canvas\n    function _onMakeImage() {\n      this._elImage.src = this._elCanvas.toDataURL(\"image/png\");\n      this._elImage.style.display = \"block\";\n      this._elCanvas.style.display = \"none\";\n    }\n\n    // Android 2.1 bug workaround\n    // http://code.google.com/p/android/issues/detail?id=5141\n    if (this._android && this._android <= 2.1) {\n      var factor = 1 / window.devicePixelRatio;\n      var drawImage = CanvasRenderingContext2D.prototype.drawImage;\n      CanvasRenderingContext2D.prototype.drawImage = function (image, sx, sy, sw, sh, dx, dy, dw, dh) {\n        if (\"nodeName\" in image && /img/i.test(image.nodeName)) {\n          for (var i = arguments.length - 1; i >= 1; i--) {\n            arguments[i] = arguments[i] * factor;\n          }\n        } else if (typeof dw == \"undefined\") {\n          arguments[1] *= factor;\n          arguments[2] *= factor;\n          arguments[3] *= factor;\n          arguments[4] *= factor;\n        }\n        drawImage.apply(this, arguments);\n      };\n    }\n\n    /**\n     * Check whether the user's browser supports Data URI or not\n     *\n     * @private\n     * @param {Function} fSuccess Occurs if it supports Data URI\n     * @param {Function} fFail Occurs if it doesn't support Data URI\n     */\n    function _safeSetDataURI(fSuccess, fFail) {\n      var self = this;\n      self._fFail = fFail;\n      self._fSuccess = fSuccess;\n\n      // Check it just once\n      if (self._bSupportDataURI === null) {\n        var el = document.createElement(\"img\");\n        var fOnError = function fOnError() {\n          self._bSupportDataURI = false;\n          if (self._fFail) {\n            self._fFail.call(self);\n          }\n        };\n        var fOnSuccess = function fOnSuccess() {\n          self._bSupportDataURI = true;\n          if (self._fSuccess) {\n            self._fSuccess.call(self);\n          }\n        };\n        el.onabort = fOnError;\n        el.onerror = fOnError;\n        el.onload = fOnSuccess;\n        el.src = \"data:image/gif;base64,iVBORw0KGgoAAAANSUhEUgAAAAUAAAAFCAYAAACNbyblAAAAHElEQVQI12P4//8/w38GIAXDIBKE0DHxgljNBAAO9TXL0Y4OHwAAAABJRU5ErkJggg==\"; // the Image contains 1px data.\n        return;\n      } else if (self._bSupportDataURI === true && self._fSuccess) {\n        self._fSuccess.call(self);\n      } else if (self._bSupportDataURI === false && self._fFail) {\n        self._fFail.call(self);\n      }\n    }\n    ;\n\n    /**\n     * Drawing QRCode by using canvas\n     *\n     * @constructor\n     * @param {HTMLElement} el\n     * @param {Object} htOption QRCode Options\n     */\n    var Drawing = function Drawing(el, htOption) {\n      this._bIsPainted = false;\n      this._android = _getAndroid();\n      this._htOption = htOption;\n      this._elCanvas = document.createElement(\"canvas\");\n      this._elCanvas.width = htOption.width;\n      this._elCanvas.height = htOption.height;\n      el.appendChild(this._elCanvas);\n      this._el = el;\n      this._oContext = this._elCanvas.getContext(\"2d\");\n      this._bIsPainted = false;\n      this._elImage = document.createElement(\"img\");\n      this._elImage.alt = \"Scan me!\";\n      this._elImage.style.display = \"none\";\n      this._el.appendChild(this._elImage);\n      this._bSupportDataURI = null;\n    };\n\n    /**\n     * Draw the QRCode\n     *\n     * @param {QRCode} oQRCode\n     */\n    Drawing.prototype.draw = function (oQRCode) {\n      var _elImage = this._elImage;\n      var _oContext = this._oContext;\n      var _htOption = this._htOption;\n      var nCount = oQRCode.getModuleCount();\n      var nWidth = _htOption.width / nCount;\n      var nHeight = _htOption.height / nCount;\n      var nRoundedWidth = Math.round(nWidth);\n      var nRoundedHeight = Math.round(nHeight);\n      _elImage.style.display = \"none\";\n      this.clear();\n      for (var row = 0; row < nCount; row++) {\n        for (var col = 0; col < nCount; col++) {\n          var bIsDark = oQRCode.isDark(row, col);\n          var nLeft = col * nWidth;\n          var nTop = row * nHeight;\n          _oContext.strokeStyle = bIsDark ? _htOption.colorDark : _htOption.colorLight;\n          _oContext.lineWidth = 1;\n          _oContext.fillStyle = bIsDark ? _htOption.colorDark : _htOption.colorLight;\n          _oContext.fillRect(nLeft, nTop, nWidth, nHeight);\n\n          // 안티 앨리어싱 방지 처리\n          _oContext.strokeRect(Math.floor(nLeft) + 0.5, Math.floor(nTop) + 0.5, nRoundedWidth, nRoundedHeight);\n          _oContext.strokeRect(Math.ceil(nLeft) - 0.5, Math.ceil(nTop) - 0.5, nRoundedWidth, nRoundedHeight);\n        }\n      }\n      this._bIsPainted = true;\n    };\n\n    /**\n     * Make the image from Canvas if the browser supports Data URI.\n     */\n    Drawing.prototype.makeImage = function () {\n      if (this._bIsPainted) {\n        _safeSetDataURI.call(this, _onMakeImage);\n      }\n    };\n\n    /**\n     * Return whether the QRCode is painted or not\n     *\n     * @return {Boolean}\n     */\n    Drawing.prototype.isPainted = function () {\n      return this._bIsPainted;\n    };\n\n    /**\n     * Clear the QRCode\n     */\n    Drawing.prototype.clear = function () {\n      this._oContext.clearRect(0, 0, this._elCanvas.width, this._elCanvas.height);\n      this._bIsPainted = false;\n    };\n\n    /**\n     * @private\n     * @param {Number} nNumber\n     */\n    Drawing.prototype.round = function (nNumber) {\n      if (!nNumber) {\n        return nNumber;\n      }\n      return Math.floor(nNumber * 1000) / 1000;\n    };\n    return Drawing;\n  }();\n\n  /**\n   * Get the type by string length\n   *\n   * @private\n   * @param {String} sText\n   * @param {Number} nCorrectLevel\n   * @return {Number} type\n   */\n  function _getTypeNumber(sText, nCorrectLevel) {\n    var nType = 1;\n    var length = _getUTF8Length(sText);\n    for (var i = 0, len = QRCodeLimitLength.length; i <= len; i++) {\n      var nLimit = 0;\n      switch (nCorrectLevel) {\n        case QRErrorCorrectLevel.L:\n          nLimit = QRCodeLimitLength[i][0];\n          break;\n        case QRErrorCorrectLevel.M:\n          nLimit = QRCodeLimitLength[i][1];\n          break;\n        case QRErrorCorrectLevel.Q:\n          nLimit = QRCodeLimitLength[i][2];\n          break;\n        case QRErrorCorrectLevel.H:\n          nLimit = QRCodeLimitLength[i][3];\n          break;\n      }\n      if (length <= nLimit) {\n        break;\n      } else {\n        nType++;\n      }\n    }\n    if (nType > QRCodeLimitLength.length) {\n      throw new Error(\"Too long data\");\n    }\n    return nType;\n  }\n  function _getUTF8Length(sText) {\n    var replacedText = encodeURI(sText).toString().replace(/\\%[0-9a-fA-F]{2}/g, 'a');\n    return replacedText.length + (replacedText.length != sText ? 3 : 0);\n  }\n\n  /**\n   * @class QRCode\n   * @constructor\n   * @example\n   * new QRCode(document.getElementById(\"test\"), \"http://jindo.dev.naver.com/collie\");\n   *\n   * @example\n   * var oQRCode = new QRCode(\"test\", {\n   *    text : \"http://naver.com\",\n   *    width : 128,\n   *    height : 128\n   * });\n   *\n   * oQRCode.clear(); // Clear the QRCode.\n   * oQRCode.makeCode(\"http://map.naver.com\"); // Re-create the QRCode.\n   *\n   * @param {HTMLElement|String} el target element or 'id' attribute of element.\n   * @param {Object|String} vOption\n   * @param {String} vOption.text QRCode link data\n   * @param {Number} [vOption.width=256]\n   * @param {Number} [vOption.height=256]\n   * @param {String} [vOption.colorDark=\"#000000\"]\n   * @param {String} [vOption.colorLight=\"#ffffff\"]\n   * @param {QRCode.CorrectLevel} [vOption.correctLevel=QRCode.CorrectLevel.H] [L|M|Q|H]\n   */\n  QRCode = function QRCode(el, vOption) {\n    this._htOption = {\n      width: 256,\n      height: 256,\n      typeNumber: 4,\n      colorDark: \"#000000\",\n      colorLight: \"#ffffff\",\n      correctLevel: QRErrorCorrectLevel.H\n    };\n    if (typeof vOption === 'string') {\n      vOption = {\n        text: vOption\n      };\n    }\n\n    // Overwrites options\n    if (vOption) {\n      for (var i in vOption) {\n        this._htOption[i] = vOption[i];\n      }\n    }\n    if (typeof el == \"string\") {\n      el = document.getElementById(el);\n    }\n    if (this._htOption.useSVG) {\n      Drawing = svgDrawer;\n    }\n    this._android = _getAndroid();\n    this._el = el;\n    this._oQRCode = null;\n    this._oDrawing = new Drawing(this._el, this._htOption);\n    if (this._htOption.text) {\n      this.makeCode(this._htOption.text);\n    }\n  };\n\n  /**\n   * Make the QRCode\n   *\n   * @param {String} sText link data\n   */\n  QRCode.prototype.makeCode = function (sText) {\n    this._oQRCode = new QRCodeModel(_getTypeNumber(sText, this._htOption.correctLevel), this._htOption.correctLevel);\n    this._oQRCode.addData(sText);\n    this._oQRCode.make();\n    this._el.title = sText;\n    this._oDrawing.draw(this._oQRCode);\n    this.makeImage();\n  };\n\n  /**\n   * Make the Image from Canvas element\n   * - It occurs automatically\n   * - Android below 3 doesn't support Data-URI spec.\n   *\n   * @private\n   */\n  QRCode.prototype.makeImage = function () {\n    if (typeof this._oDrawing.makeImage == \"function\" && (!this._android || this._android >= 3)) {\n      this._oDrawing.makeImage();\n    }\n  };\n\n  /**\n   * Clear the QRCode\n   */\n  QRCode.prototype.clear = function () {\n    this._oDrawing.clear();\n  };\n\n  /**\n   * @name QRCode.CorrectLevel\n   */\n  QRCode.CorrectLevel = QRErrorCorrectLevel;\n  return QRCode;\n});", "map": {"version": 3, "names": ["QRCode", "root", "factory", "exports", "_typeof", "module", "define", "amd", "QR8bitByte", "data", "mode", "QRMode", "MODE_8BIT_BYTE", "parsedData", "i", "l", "length", "byteArray", "code", "charCodeAt", "push", "Array", "prototype", "concat", "apply", "unshift", "<PERSON><PERSON><PERSON><PERSON>", "buffer", "write", "put", "QRCodeModel", "typeNumber", "errorCorrectLevel", "modules", "moduleCount", "dataCache", "dataList", "addData", "newData", "isDark", "row", "col", "Error", "getModuleCount", "make", "makeImpl", "getBestMaskPattern", "test", "maskPattern", "setupPositionProbePattern", "setupPositionAdjustPattern", "setupTimingPattern", "setupTypeInfo", "setupTypeNumber", "createData", "mapData", "r", "c", "minLostPoint", "pattern", "lostPoint", "QRUtil", "getLostPoint", "createMovieClip", "target_mc", "instance_name", "depth", "qr_mc", "createEmptyMovieClip", "cs", "y", "x", "dark", "beginFill", "moveTo", "lineTo", "endFill", "pos", "getPatternPosition", "j", "bits", "getBCHTypeNumber", "mod", "Math", "floor", "getBCHTypeInfo", "inc", "bitIndex", "byteIndex", "mask", "getMask", "PAD0", "PAD1", "rsBlocks", "QRRSBlock", "getRSBlocks", "QRBitBuffer", "getLengthInBits", "totalDataCount", "dataCount", "putBit", "createBytes", "offset", "maxDcCount", "maxEcCount", "dcdata", "ecdata", "dcCount", "ecCount", "totalCount", "max", "rsPoly", "getErrorCorrectPolynomial", "rawPoly", "QRPolynomial", "modPoly", "modIndex", "get", "totalCodeCount", "index", "MODE_NUMBER", "MODE_ALPHA_NUM", "MODE_KANJI", "QRErrorCorrectLevel", "L", "M", "Q", "H", "QRMaskPattern", "PATTERN000", "PATTERN001", "PATTERN010", "PATTERN011", "PATTERN100", "PATTERN101", "PATTERN110", "PATTERN111", "PATTERN_POSITION_TABLE", "G15", "G18", "G15_MASK", "d", "getBCHDigit", "digit", "errorCorrectLength", "a", "multiply", "QRMath", "gexp", "type", "qrCode", "sameCount", "count", "darkCount", "ratio", "abs", "glog", "n", "LOG_TABLE", "EXP_TABLE", "num", "shift", "undefined", "e", "RS_BLOCK_TABLE", "rsBlock", "getRsBlockTable", "list", "bufIndex", "bit", "QRCodeLimitLength", "_isSupportCanvas", "CanvasRenderingContext2D", "_getAndroid", "android", "sAgent", "navigator", "userAgent", "aMat", "toString", "match", "parseFloat", "svgDrawer", "Drawing", "el", "htOption", "_el", "_htOption", "draw", "oQRCode", "nCount", "nWidth", "width", "nHeight", "height", "clear", "makeSVG", "tag", "attrs", "document", "createElementNS", "k", "hasOwnProperty", "setAttribute", "svg", "String", "colorLight", "setAttributeNS", "append<PERSON><PERSON><PERSON>", "colorDark", "child", "hasChildNodes", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "useSVG", "documentElement", "tagName", "toLowerCase", "aHTML", "innerHTML", "join", "elTable", "childNodes", "nLeftMarginTable", "offsetWidth", "nTopMarginTable", "offsetHeight", "style", "margin", "_onMakeImage", "_elImage", "src", "_elCanvas", "toDataURL", "display", "_android", "factor", "window", "devicePixelRatio", "drawImage", "image", "sx", "sy", "sw", "sh", "dx", "dy", "dw", "dh", "nodeName", "arguments", "_safeSetDataURI", "fSuccess", "fFail", "self", "_fFail", "_fSuccess", "_bSupportDataURI", "createElement", "fOnError", "call", "fOnSuccess", "<PERSON>ab<PERSON>", "onerror", "onload", "_bIsPainted", "_oContext", "getContext", "alt", "nRoundedWidth", "round", "nRoundedHeight", "bIsDark", "nLeft", "nTop", "strokeStyle", "lineWidth", "fillStyle", "fillRect", "strokeRect", "ceil", "makeImage", "isPainted", "clearRect", "nNumber", "_getTypeNumber", "sText", "nCorrectLevel", "nType", "_getUTF8Length", "len", "nLimit", "replacedText", "encodeURI", "replace", "vOption", "correctLevel", "text", "getElementById", "_oQRCode", "_oDrawing", "makeCode", "title", "CorrectLevel"], "sources": ["C:/Users/<USER>/Downloads/Telegram Desktop/vue3_3.0/vue3_3.0/node_modules/qrcodejs2/qrcode.js"], "sourcesContent": ["/**\n * @fileoverview\n * - Using the 'QRCode for Javascript library'\n * - Fixed dataset of 'QRCode for Javascript library' for support full-spec.\n * - this library has no dependencies.\n *\n * <AUTHOR>\n * @see <a href=\"http://www.d-project.com/\" target=\"_blank\">http://www.d-project.com/</a>\n * @see <a href=\"http://jeromeetienne.github.com/jquery-qrcode/\" target=\"_blank\">http://jeromeetienne.github.com/jquery-qrcode/</a>\n */\nvar QRCode;\n\n(function (root, factory) {\n\n\t/* CommonJS */\n  if (typeof exports == 'object') module.exports = factory()\n\n  /* AMD module */\n  else if (typeof define == 'function' && define.amd) define(factory)\n\n  /* Global */\n  else root.QRCode = factory()\n\n}(this, function () {\t//---------------------------------------------------------------------\n\t// QRCode for JavaScript\n\t//\n\t// Copyright (c) 2009 <PERSON><PERSON><PERSON>\n\t//\n\t// URL: http://www.d-project.com/\n\t//\n\t// Licensed under the MIT license:\n\t//   http://www.opensource.org/licenses/mit-license.php\n\t//\n\t// The word \"QR Code\" is registered trademark of\n\t// DENSO WAVE INCORPORATED\n\t//   http://www.denso-wave.com/qrcode/faqpatent-e.html\n\t//\n\t//---------------------------------------------------------------------\n\tfunction QR8bitByte(data) {\n\t\tthis.mode = QRMode.MODE_8BIT_BYTE;\n\t\tthis.data = data;\n\t\tthis.parsedData = [];\n\n\t\t// Added to support UTF-8 Characters\n\t\tfor (var i = 0, l = this.data.length; i < l; i++) {\n\t\t\tvar byteArray = [];\n\t\t\tvar code = this.data.charCodeAt(i);\n\n\t\t\tif (code > 0x10000) {\n\t\t\t\tbyteArray[0] = 0xF0 | ((code & 0x1C0000) >>> 18);\n\t\t\t\tbyteArray[1] = 0x80 | ((code & 0x3F000) >>> 12);\n\t\t\t\tbyteArray[2] = 0x80 | ((code & 0xFC0) >>> 6);\n\t\t\t\tbyteArray[3] = 0x80 | (code & 0x3F);\n\t\t\t} else if (code > 0x800) {\n\t\t\t\tbyteArray[0] = 0xE0 | ((code & 0xF000) >>> 12);\n\t\t\t\tbyteArray[1] = 0x80 | ((code & 0xFC0) >>> 6);\n\t\t\t\tbyteArray[2] = 0x80 | (code & 0x3F);\n\t\t\t} else if (code > 0x80) {\n\t\t\t\tbyteArray[0] = 0xC0 | ((code & 0x7C0) >>> 6);\n\t\t\t\tbyteArray[1] = 0x80 | (code & 0x3F);\n\t\t\t} else {\n\t\t\t\tbyteArray[0] = code;\n\t\t\t}\n\n\t\t\tthis.parsedData.push(byteArray);\n\t\t}\n\n\t\tthis.parsedData = Array.prototype.concat.apply([], this.parsedData);\n\n\t\tif (this.parsedData.length != this.data.length) {\n\t\t\tthis.parsedData.unshift(191);\n\t\t\tthis.parsedData.unshift(187);\n\t\t\tthis.parsedData.unshift(239);\n\t\t}\n\t}\n\n\tQR8bitByte.prototype = {\n\t\tgetLength: function (buffer) {\n\t\t\treturn this.parsedData.length;\n\t\t},\n\t\twrite: function (buffer) {\n\t\t\tfor (var i = 0, l = this.parsedData.length; i < l; i++) {\n\t\t\t\tbuffer.put(this.parsedData[i], 8);\n\t\t\t}\n\t\t}\n\t};\n\n\tfunction QRCodeModel(typeNumber, errorCorrectLevel) {\n\t\tthis.typeNumber = typeNumber;\n\t\tthis.errorCorrectLevel = errorCorrectLevel;\n\t\tthis.modules = null;\n\t\tthis.moduleCount = 0;\n\t\tthis.dataCache = null;\n\t\tthis.dataList = [];\n\t}\n\n\tQRCodeModel.prototype={addData:function(data){var newData=new QR8bitByte(data);this.dataList.push(newData);this.dataCache=null;},isDark:function(row,col){if(row<0||this.moduleCount<=row||col<0||this.moduleCount<=col){throw new Error(row+\",\"+col);}\n\treturn this.modules[row][col];},getModuleCount:function(){return this.moduleCount;},make:function(){this.makeImpl(false,this.getBestMaskPattern());},makeImpl:function(test,maskPattern){this.moduleCount=this.typeNumber*4+17;this.modules=new Array(this.moduleCount);for(var row=0;row<this.moduleCount;row++){this.modules[row]=new Array(this.moduleCount);for(var col=0;col<this.moduleCount;col++){this.modules[row][col]=null;}}\n\tthis.setupPositionProbePattern(0,0);this.setupPositionProbePattern(this.moduleCount-7,0);this.setupPositionProbePattern(0,this.moduleCount-7);this.setupPositionAdjustPattern();this.setupTimingPattern();this.setupTypeInfo(test,maskPattern);if(this.typeNumber>=7){this.setupTypeNumber(test);}\n\tif(this.dataCache==null){this.dataCache=QRCodeModel.createData(this.typeNumber,this.errorCorrectLevel,this.dataList);}\n\tthis.mapData(this.dataCache,maskPattern);},setupPositionProbePattern:function(row,col){for(var r=-1;r<=7;r++){if(row+r<=-1||this.moduleCount<=row+r)continue;for(var c=-1;c<=7;c++){if(col+c<=-1||this.moduleCount<=col+c)continue;if((0<=r&&r<=6&&(c==0||c==6))||(0<=c&&c<=6&&(r==0||r==6))||(2<=r&&r<=4&&2<=c&&c<=4)){this.modules[row+r][col+c]=true;}else{this.modules[row+r][col+c]=false;}}}},getBestMaskPattern:function(){var minLostPoint=0;var pattern=0;for(var i=0;i<8;i++){this.makeImpl(true,i);var lostPoint=QRUtil.getLostPoint(this);if(i==0||minLostPoint>lostPoint){minLostPoint=lostPoint;pattern=i;}}\n\treturn pattern;},createMovieClip:function(target_mc,instance_name,depth){var qr_mc=target_mc.createEmptyMovieClip(instance_name,depth);var cs=1;this.make();for(var row=0;row<this.modules.length;row++){var y=row*cs;for(var col=0;col<this.modules[row].length;col++){var x=col*cs;var dark=this.modules[row][col];if(dark){qr_mc.beginFill(0,100);qr_mc.moveTo(x,y);qr_mc.lineTo(x+cs,y);qr_mc.lineTo(x+cs,y+cs);qr_mc.lineTo(x,y+cs);qr_mc.endFill();}}}\n\treturn qr_mc;},setupTimingPattern:function(){for(var r=8;r<this.moduleCount-8;r++){if(this.modules[r][6]!=null){continue;}\n\tthis.modules[r][6]=(r%2==0);}\n\tfor(var c=8;c<this.moduleCount-8;c++){if(this.modules[6][c]!=null){continue;}\n\tthis.modules[6][c]=(c%2==0);}},setupPositionAdjustPattern:function(){var pos=QRUtil.getPatternPosition(this.typeNumber);for(var i=0;i<pos.length;i++){for(var j=0;j<pos.length;j++){var row=pos[i];var col=pos[j];if(this.modules[row][col]!=null){continue;}\n\tfor(var r=-2;r<=2;r++){for(var c=-2;c<=2;c++){if(r==-2||r==2||c==-2||c==2||(r==0&&c==0)){this.modules[row+r][col+c]=true;}else{this.modules[row+r][col+c]=false;}}}}}},setupTypeNumber:function(test){var bits=QRUtil.getBCHTypeNumber(this.typeNumber);for(var i=0;i<18;i++){var mod=(!test&&((bits>>i)&1)==1);this.modules[Math.floor(i/3)][i%3+this.moduleCount-8-3]=mod;}\n\tfor(var i=0;i<18;i++){var mod=(!test&&((bits>>i)&1)==1);this.modules[i%3+this.moduleCount-8-3][Math.floor(i/3)]=mod;}},setupTypeInfo:function(test,maskPattern){var data=(this.errorCorrectLevel<<3)|maskPattern;var bits=QRUtil.getBCHTypeInfo(data);for(var i=0;i<15;i++){var mod=(!test&&((bits>>i)&1)==1);if(i<6){this.modules[i][8]=mod;}else if(i<8){this.modules[i+1][8]=mod;}else{this.modules[this.moduleCount-15+i][8]=mod;}}\n\tfor(var i=0;i<15;i++){var mod=(!test&&((bits>>i)&1)==1);if(i<8){this.modules[8][this.moduleCount-i-1]=mod;}else if(i<9){this.modules[8][15-i-1+1]=mod;}else{this.modules[8][15-i-1]=mod;}}\n\tthis.modules[this.moduleCount-8][8]=(!test);},mapData:function(data,maskPattern){var inc=-1;var row=this.moduleCount-1;var bitIndex=7;var byteIndex=0;for(var col=this.moduleCount-1;col>0;col-=2){if(col==6)col--;while(true){for(var c=0;c<2;c++){if(this.modules[row][col-c]==null){var dark=false;if(byteIndex<data.length){dark=(((data[byteIndex]>>>bitIndex)&1)==1);}\n\tvar mask=QRUtil.getMask(maskPattern,row,col-c);if(mask){dark=!dark;}\n\tthis.modules[row][col-c]=dark;bitIndex--;if(bitIndex==-1){byteIndex++;bitIndex=7;}}}\n\trow+=inc;if(row<0||this.moduleCount<=row){row-=inc;inc=-inc;break;}}}}};QRCodeModel.PAD0=0xEC;QRCodeModel.PAD1=0x11;QRCodeModel.createData=function(typeNumber,errorCorrectLevel,dataList){var rsBlocks=QRRSBlock.getRSBlocks(typeNumber,errorCorrectLevel);var buffer=new QRBitBuffer();for(var i=0;i<dataList.length;i++){var data=dataList[i];buffer.put(data.mode,4);buffer.put(data.getLength(),QRUtil.getLengthInBits(data.mode,typeNumber));data.write(buffer);}\n\tvar totalDataCount=0;for(var i=0;i<rsBlocks.length;i++){totalDataCount+=rsBlocks[i].dataCount;}\n\tif(buffer.getLengthInBits()>totalDataCount*8){throw new Error(\"code length overflow. (\"\n\t+buffer.getLengthInBits()\n\t+\">\"\n\t+totalDataCount*8\n\t+\")\");}\n\tif(buffer.getLengthInBits()+4<=totalDataCount*8){buffer.put(0,4);}\n\twhile(buffer.getLengthInBits()%8!=0){buffer.putBit(false);}\n\twhile(true){if(buffer.getLengthInBits()>=totalDataCount*8){break;}\n\tbuffer.put(QRCodeModel.PAD0,8);if(buffer.getLengthInBits()>=totalDataCount*8){break;}\n\tbuffer.put(QRCodeModel.PAD1,8);}\n\treturn QRCodeModel.createBytes(buffer,rsBlocks);};QRCodeModel.createBytes=function(buffer,rsBlocks){var offset=0;var maxDcCount=0;var maxEcCount=0;var dcdata=new Array(rsBlocks.length);var ecdata=new Array(rsBlocks.length);for(var r=0;r<rsBlocks.length;r++){var dcCount=rsBlocks[r].dataCount;var ecCount=rsBlocks[r].totalCount-dcCount;maxDcCount=Math.max(maxDcCount,dcCount);maxEcCount=Math.max(maxEcCount,ecCount);dcdata[r]=new Array(dcCount);for(var i=0;i<dcdata[r].length;i++){dcdata[r][i]=0xff&buffer.buffer[i+offset];}\n\toffset+=dcCount;var rsPoly=QRUtil.getErrorCorrectPolynomial(ecCount);var rawPoly=new QRPolynomial(dcdata[r],rsPoly.getLength()-1);var modPoly=rawPoly.mod(rsPoly);ecdata[r]=new Array(rsPoly.getLength()-1);for(var i=0;i<ecdata[r].length;i++){var modIndex=i+modPoly.getLength()-ecdata[r].length;ecdata[r][i]=(modIndex>=0)?modPoly.get(modIndex):0;}}\n\tvar totalCodeCount=0;for(var i=0;i<rsBlocks.length;i++){totalCodeCount+=rsBlocks[i].totalCount;}\n\tvar data=new Array(totalCodeCount);var index=0;for(var i=0;i<maxDcCount;i++){for(var r=0;r<rsBlocks.length;r++){if(i<dcdata[r].length){data[index++]=dcdata[r][i];}}}\n\tfor(var i=0;i<maxEcCount;i++){for(var r=0;r<rsBlocks.length;r++){if(i<ecdata[r].length){data[index++]=ecdata[r][i];}}}\n\treturn data;};var QRMode={MODE_NUMBER:1<<0,MODE_ALPHA_NUM:1<<1,MODE_8BIT_BYTE:1<<2,MODE_KANJI:1<<3};var QRErrorCorrectLevel={L:1,M:0,Q:3,H:2};var QRMaskPattern={PATTERN000:0,PATTERN001:1,PATTERN010:2,PATTERN011:3,PATTERN100:4,PATTERN101:5,PATTERN110:6,PATTERN111:7};var QRUtil={PATTERN_POSITION_TABLE:[[],[6,18],[6,22],[6,26],[6,30],[6,34],[6,22,38],[6,24,42],[6,26,46],[6,28,50],[6,30,54],[6,32,58],[6,34,62],[6,26,46,66],[6,26,48,70],[6,26,50,74],[6,30,54,78],[6,30,56,82],[6,30,58,86],[6,34,62,90],[6,28,50,72,94],[6,26,50,74,98],[6,30,54,78,102],[6,28,54,80,106],[6,32,58,84,110],[6,30,58,86,114],[6,34,62,90,118],[6,26,50,74,98,122],[6,30,54,78,102,126],[6,26,52,78,104,130],[6,30,56,82,108,134],[6,34,60,86,112,138],[6,30,58,86,114,142],[6,34,62,90,118,146],[6,30,54,78,102,126,150],[6,24,50,76,102,128,154],[6,28,54,80,106,132,158],[6,32,58,84,110,136,162],[6,26,54,82,110,138,166],[6,30,58,86,114,142,170]],G15:(1<<10)|(1<<8)|(1<<5)|(1<<4)|(1<<2)|(1<<1)|(1<<0),G18:(1<<12)|(1<<11)|(1<<10)|(1<<9)|(1<<8)|(1<<5)|(1<<2)|(1<<0),G15_MASK:(1<<14)|(1<<12)|(1<<10)|(1<<4)|(1<<1),getBCHTypeInfo:function(data){var d=data<<10;while(QRUtil.getBCHDigit(d)-QRUtil.getBCHDigit(QRUtil.G15)>=0){d^=(QRUtil.G15<<(QRUtil.getBCHDigit(d)-QRUtil.getBCHDigit(QRUtil.G15)));}\n\treturn((data<<10)|d)^QRUtil.G15_MASK;},getBCHTypeNumber:function(data){var d=data<<12;while(QRUtil.getBCHDigit(d)-QRUtil.getBCHDigit(QRUtil.G18)>=0){d^=(QRUtil.G18<<(QRUtil.getBCHDigit(d)-QRUtil.getBCHDigit(QRUtil.G18)));}\n\treturn(data<<12)|d;},getBCHDigit:function(data){var digit=0;while(data!=0){digit++;data>>>=1;}\n\treturn digit;},getPatternPosition:function(typeNumber){return QRUtil.PATTERN_POSITION_TABLE[typeNumber-1];},getMask:function(maskPattern,i,j){switch(maskPattern){case QRMaskPattern.PATTERN000:return(i+j)%2==0;case QRMaskPattern.PATTERN001:return i%2==0;case QRMaskPattern.PATTERN010:return j%3==0;case QRMaskPattern.PATTERN011:return(i+j)%3==0;case QRMaskPattern.PATTERN100:return(Math.floor(i/2)+Math.floor(j/3))%2==0;case QRMaskPattern.PATTERN101:return(i*j)%2+(i*j)%3==0;case QRMaskPattern.PATTERN110:return((i*j)%2+(i*j)%3)%2==0;case QRMaskPattern.PATTERN111:return((i*j)%3+(i+j)%2)%2==0;default:throw new Error(\"bad maskPattern:\"+maskPattern);}},getErrorCorrectPolynomial:function(errorCorrectLength){var a=new QRPolynomial([1],0);for(var i=0;i<errorCorrectLength;i++){a=a.multiply(new QRPolynomial([1,QRMath.gexp(i)],0));}\n\treturn a;},getLengthInBits:function(mode,type){if(1<=type&&type<10){switch(mode){case QRMode.MODE_NUMBER:return 10;case QRMode.MODE_ALPHA_NUM:return 9;case QRMode.MODE_8BIT_BYTE:return 8;case QRMode.MODE_KANJI:return 8;default:throw new Error(\"mode:\"+mode);}}else if(type<27){switch(mode){case QRMode.MODE_NUMBER:return 12;case QRMode.MODE_ALPHA_NUM:return 11;case QRMode.MODE_8BIT_BYTE:return 16;case QRMode.MODE_KANJI:return 10;default:throw new Error(\"mode:\"+mode);}}else if(type<41){switch(mode){case QRMode.MODE_NUMBER:return 14;case QRMode.MODE_ALPHA_NUM:return 13;case QRMode.MODE_8BIT_BYTE:return 16;case QRMode.MODE_KANJI:return 12;default:throw new Error(\"mode:\"+mode);}}else{throw new Error(\"type:\"+type);}},getLostPoint:function(qrCode){var moduleCount=qrCode.getModuleCount();var lostPoint=0;for(var row=0;row<moduleCount;row++){for(var col=0;col<moduleCount;col++){var sameCount=0;var dark=qrCode.isDark(row,col);for(var r=-1;r<=1;r++){if(row+r<0||moduleCount<=row+r){continue;}\n\tfor(var c=-1;c<=1;c++){if(col+c<0||moduleCount<=col+c){continue;}\n\tif(r==0&&c==0){continue;}\n\tif(dark==qrCode.isDark(row+r,col+c)){sameCount++;}}}\n\tif(sameCount>5){lostPoint+=(3+sameCount-5);}}}\n\tfor(var row=0;row<moduleCount-1;row++){for(var col=0;col<moduleCount-1;col++){var count=0;if(qrCode.isDark(row,col))count++;if(qrCode.isDark(row+1,col))count++;if(qrCode.isDark(row,col+1))count++;if(qrCode.isDark(row+1,col+1))count++;if(count==0||count==4){lostPoint+=3;}}}\n\tfor(var row=0;row<moduleCount;row++){for(var col=0;col<moduleCount-6;col++){if(qrCode.isDark(row,col)&&!qrCode.isDark(row,col+1)&&qrCode.isDark(row,col+2)&&qrCode.isDark(row,col+3)&&qrCode.isDark(row,col+4)&&!qrCode.isDark(row,col+5)&&qrCode.isDark(row,col+6)){lostPoint+=40;}}}\n\tfor(var col=0;col<moduleCount;col++){for(var row=0;row<moduleCount-6;row++){if(qrCode.isDark(row,col)&&!qrCode.isDark(row+1,col)&&qrCode.isDark(row+2,col)&&qrCode.isDark(row+3,col)&&qrCode.isDark(row+4,col)&&!qrCode.isDark(row+5,col)&&qrCode.isDark(row+6,col)){lostPoint+=40;}}}\n\tvar darkCount=0;for(var col=0;col<moduleCount;col++){for(var row=0;row<moduleCount;row++){if(qrCode.isDark(row,col)){darkCount++;}}}\n\tvar ratio=Math.abs(100*darkCount/moduleCount/moduleCount-50)/5;lostPoint+=ratio*10;return lostPoint;}};var QRMath={glog:function(n){if(n<1){throw new Error(\"glog(\"+n+\")\");}\n\treturn QRMath.LOG_TABLE[n];},gexp:function(n){while(n<0){n+=255;}\n\twhile(n>=256){n-=255;}\n\treturn QRMath.EXP_TABLE[n];},EXP_TABLE:new Array(256),LOG_TABLE:new Array(256)};for(var i=0;i<8;i++){QRMath.EXP_TABLE[i]=1<<i;}\n\tfor(var i=8;i<256;i++){QRMath.EXP_TABLE[i]=QRMath.EXP_TABLE[i-4]^QRMath.EXP_TABLE[i-5]^QRMath.EXP_TABLE[i-6]^QRMath.EXP_TABLE[i-8];}\n\tfor(var i=0;i<255;i++){QRMath.LOG_TABLE[QRMath.EXP_TABLE[i]]=i;}\n\tfunction QRPolynomial(num,shift){if(num.length==undefined){throw new Error(num.length+\"/\"+shift);}\n\tvar offset=0;while(offset<num.length&&num[offset]==0){offset++;}\n\tthis.num=new Array(num.length-offset+shift);for(var i=0;i<num.length-offset;i++){this.num[i]=num[i+offset];}}\n\tQRPolynomial.prototype={get:function(index){return this.num[index];},getLength:function(){return this.num.length;},multiply:function(e){var num=new Array(this.getLength()+e.getLength()-1);for(var i=0;i<this.getLength();i++){for(var j=0;j<e.getLength();j++){num[i+j]^=QRMath.gexp(QRMath.glog(this.get(i))+QRMath.glog(e.get(j)));}}\n\treturn new QRPolynomial(num,0);},mod:function(e){if(this.getLength()-e.getLength()<0){return this;}\n\tvar ratio=QRMath.glog(this.get(0))-QRMath.glog(e.get(0));var num=new Array(this.getLength());for(var i=0;i<this.getLength();i++){num[i]=this.get(i);}\n\tfor(var i=0;i<e.getLength();i++){num[i]^=QRMath.gexp(QRMath.glog(e.get(i))+ratio);}\n\treturn new QRPolynomial(num,0).mod(e);}};function QRRSBlock(totalCount,dataCount){this.totalCount=totalCount;this.dataCount=dataCount;}\n\tQRRSBlock.RS_BLOCK_TABLE=[[1,26,19],[1,26,16],[1,26,13],[1,26,9],[1,44,34],[1,44,28],[1,44,22],[1,44,16],[1,70,55],[1,70,44],[2,35,17],[2,35,13],[1,100,80],[2,50,32],[2,50,24],[4,25,9],[1,134,108],[2,67,43],[2,33,15,2,34,16],[2,33,11,2,34,12],[2,86,68],[4,43,27],[4,43,19],[4,43,15],[2,98,78],[4,49,31],[2,32,14,4,33,15],[4,39,13,1,40,14],[2,121,97],[2,60,38,2,61,39],[4,40,18,2,41,19],[4,40,14,2,41,15],[2,146,116],[3,58,36,2,59,37],[4,36,16,4,37,17],[4,36,12,4,37,13],[2,86,68,2,87,69],[4,69,43,1,70,44],[6,43,19,2,44,20],[6,43,15,2,44,16],[4,101,81],[1,80,50,4,81,51],[4,50,22,4,51,23],[3,36,12,8,37,13],[2,116,92,2,117,93],[6,58,36,2,59,37],[4,46,20,6,47,21],[7,42,14,4,43,15],[4,133,107],[8,59,37,1,60,38],[8,44,20,4,45,21],[12,33,11,4,34,12],[3,145,115,1,146,116],[4,64,40,5,65,41],[11,36,16,5,37,17],[11,36,12,5,37,13],[5,109,87,1,110,88],[5,65,41,5,66,42],[5,54,24,7,55,25],[11,36,12],[5,122,98,1,123,99],[7,73,45,3,74,46],[15,43,19,2,44,20],[3,45,15,13,46,16],[1,135,107,5,136,108],[10,74,46,1,75,47],[1,50,22,15,51,23],[2,42,14,17,43,15],[5,150,120,1,151,121],[9,69,43,4,70,44],[17,50,22,1,51,23],[2,42,14,19,43,15],[3,141,113,4,142,114],[3,70,44,11,71,45],[17,47,21,4,48,22],[9,39,13,16,40,14],[3,135,107,5,136,108],[3,67,41,13,68,42],[15,54,24,5,55,25],[15,43,15,10,44,16],[4,144,116,4,145,117],[17,68,42],[17,50,22,6,51,23],[19,46,16,6,47,17],[2,139,111,7,140,112],[17,74,46],[7,54,24,16,55,25],[34,37,13],[4,151,121,5,152,122],[4,75,47,14,76,48],[11,54,24,14,55,25],[16,45,15,14,46,16],[6,147,117,4,148,118],[6,73,45,14,74,46],[11,54,24,16,55,25],[30,46,16,2,47,17],[8,132,106,4,133,107],[8,75,47,13,76,48],[7,54,24,22,55,25],[22,45,15,13,46,16],[10,142,114,2,143,115],[19,74,46,4,75,47],[28,50,22,6,51,23],[33,46,16,4,47,17],[8,152,122,4,153,123],[22,73,45,3,74,46],[8,53,23,26,54,24],[12,45,15,28,46,16],[3,147,117,10,148,118],[3,73,45,23,74,46],[4,54,24,31,55,25],[11,45,15,31,46,16],[7,146,116,7,147,117],[21,73,45,7,74,46],[1,53,23,37,54,24],[19,45,15,26,46,16],[5,145,115,10,146,116],[19,75,47,10,76,48],[15,54,24,25,55,25],[23,45,15,25,46,16],[13,145,115,3,146,116],[2,74,46,29,75,47],[42,54,24,1,55,25],[23,45,15,28,46,16],[17,145,115],[10,74,46,23,75,47],[10,54,24,35,55,25],[19,45,15,35,46,16],[17,145,115,1,146,116],[14,74,46,21,75,47],[29,54,24,19,55,25],[11,45,15,46,46,16],[13,145,115,6,146,116],[14,74,46,23,75,47],[44,54,24,7,55,25],[59,46,16,1,47,17],[12,151,121,7,152,122],[12,75,47,26,76,48],[39,54,24,14,55,25],[22,45,15,41,46,16],[6,151,121,14,152,122],[6,75,47,34,76,48],[46,54,24,10,55,25],[2,45,15,64,46,16],[17,152,122,4,153,123],[29,74,46,14,75,47],[49,54,24,10,55,25],[24,45,15,46,46,16],[4,152,122,18,153,123],[13,74,46,32,75,47],[48,54,24,14,55,25],[42,45,15,32,46,16],[20,147,117,4,148,118],[40,75,47,7,76,48],[43,54,24,22,55,25],[10,45,15,67,46,16],[19,148,118,6,149,119],[18,75,47,31,76,48],[34,54,24,34,55,25],[20,45,15,61,46,16]];QRRSBlock.getRSBlocks=function(typeNumber,errorCorrectLevel){var rsBlock=QRRSBlock.getRsBlockTable(typeNumber,errorCorrectLevel);if(rsBlock==undefined){throw new Error(\"bad rs block @ typeNumber:\"+typeNumber+\"/errorCorrectLevel:\"+errorCorrectLevel);}\n\tvar length=rsBlock.length/3;var list=[];for(var i=0;i<length;i++){var count=rsBlock[i*3+0];var totalCount=rsBlock[i*3+1];var dataCount=rsBlock[i*3+2];for(var j=0;j<count;j++){list.push(new QRRSBlock(totalCount,dataCount));}}\n\treturn list;};QRRSBlock.getRsBlockTable=function(typeNumber,errorCorrectLevel){switch(errorCorrectLevel){case QRErrorCorrectLevel.L:return QRRSBlock.RS_BLOCK_TABLE[(typeNumber-1)*4+0];case QRErrorCorrectLevel.M:return QRRSBlock.RS_BLOCK_TABLE[(typeNumber-1)*4+1];case QRErrorCorrectLevel.Q:return QRRSBlock.RS_BLOCK_TABLE[(typeNumber-1)*4+2];case QRErrorCorrectLevel.H:return QRRSBlock.RS_BLOCK_TABLE[(typeNumber-1)*4+3];default:return undefined;}};function QRBitBuffer(){this.buffer=[];this.length=0;}\n\tQRBitBuffer.prototype={get:function(index){var bufIndex=Math.floor(index/8);return((this.buffer[bufIndex]>>>(7-index%8))&1)==1;},put:function(num,length){for(var i=0;i<length;i++){this.putBit(((num>>>(length-i-1))&1)==1);}},getLengthInBits:function(){return this.length;},putBit:function(bit){var bufIndex=Math.floor(this.length/8);if(this.buffer.length<=bufIndex){this.buffer.push(0);}\n\tif(bit){this.buffer[bufIndex]|=(0x80>>>(this.length%8));}\n\tthis.length++;}};var QRCodeLimitLength=[[17,14,11,7],[32,26,20,14],[53,42,32,24],[78,62,46,34],[106,84,60,44],[134,106,74,58],[154,122,86,64],[192,152,108,84],[230,180,130,98],[271,213,151,119],[321,251,177,137],[367,287,203,155],[425,331,241,177],[458,362,258,194],[520,412,292,220],[586,450,322,250],[644,504,364,280],[718,560,394,310],[792,624,442,338],[858,666,482,382],[929,711,509,403],[1003,779,565,439],[1091,857,611,461],[1171,911,661,511],[1273,997,715,535],[1367,1059,751,593],[1465,1125,805,625],[1528,1190,868,658],[1628,1264,908,698],[1732,1370,982,742],[1840,1452,1030,790],[1952,1538,1112,842],[2068,1628,1168,898],[2188,1722,1228,958],[2303,1809,1283,983],[2431,1911,1351,1051],[2563,1989,1423,1093],[2699,2099,1499,1139],[2809,2213,1579,1219],[2953,2331,1663,1273]];\n\n\tfunction _isSupportCanvas() {\n\t\treturn typeof CanvasRenderingContext2D != \"undefined\";\n\t}\n\n\t// android 2.x doesn't support Data-URI spec\n\tfunction _getAndroid() {\n\t\tvar android = false;\n\t\tvar sAgent = navigator.userAgent;\n\n\t\tif (/android/i.test(sAgent)) { // android\n\t\t\tandroid = true;\n\t\t\tvar aMat = sAgent.toString().match(/android ([0-9]\\.[0-9])/i);\n\n\t\t\tif (aMat && aMat[1]) {\n\t\t\t\tandroid = parseFloat(aMat[1]);\n\t\t\t}\n\t\t}\n\n\t\treturn android;\n\t}\n\n\tvar svgDrawer = (function() {\n\n\t\tvar Drawing = function (el, htOption) {\n\t\t\tthis._el = el;\n\t\t\tthis._htOption = htOption;\n\t\t};\n\n\t\tDrawing.prototype.draw = function (oQRCode) {\n\t\t\tvar _htOption = this._htOption;\n\t\t\tvar _el = this._el;\n\t\t\tvar nCount = oQRCode.getModuleCount();\n\t\t\tvar nWidth = Math.floor(_htOption.width / nCount);\n\t\t\tvar nHeight = Math.floor(_htOption.height / nCount);\n\n\t\t\tthis.clear();\n\n\t\t\tfunction makeSVG(tag, attrs) {\n\t\t\t\tvar el = document.createElementNS('http://www.w3.org/2000/svg', tag);\n\t\t\t\tfor (var k in attrs)\n\t\t\t\t\tif (attrs.hasOwnProperty(k)) el.setAttribute(k, attrs[k]);\n\t\t\t\treturn el;\n\t\t\t}\n\n\t\t\tvar svg = makeSVG(\"svg\" , {'viewBox': '0 0 ' + String(nCount) + \" \" + String(nCount), 'width': '100%', 'height': '100%', 'fill': _htOption.colorLight});\n\t\t\tsvg.setAttributeNS(\"http://www.w3.org/2000/xmlns/\", \"xmlns:xlink\", \"http://www.w3.org/1999/xlink\");\n\t\t\t_el.appendChild(svg);\n\n\t\t\tsvg.appendChild(makeSVG(\"rect\", {\"fill\": _htOption.colorLight, \"width\": \"100%\", \"height\": \"100%\"}));\n\t\t\tsvg.appendChild(makeSVG(\"rect\", {\"fill\": _htOption.colorDark, \"width\": \"1\", \"height\": \"1\", \"id\": \"template\"}));\n\n\t\t\tfor (var row = 0; row < nCount; row++) {\n\t\t\t\tfor (var col = 0; col < nCount; col++) {\n\t\t\t\t\tif (oQRCode.isDark(row, col)) {\n\t\t\t\t\t\tvar child = makeSVG(\"use\", {\"x\": String(col), \"y\": String(row)});\n\t\t\t\t\t\tchild.setAttributeNS(\"http://www.w3.org/1999/xlink\", \"href\", \"#template\")\n\t\t\t\t\t\tsvg.appendChild(child);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t};\n\t\tDrawing.prototype.clear = function () {\n\t\t\twhile (this._el.hasChildNodes())\n\t\t\t\tthis._el.removeChild(this._el.lastChild);\n\t\t};\n\t\treturn Drawing;\n\t})();\n\n\tvar useSVG = document.documentElement.tagName.toLowerCase() === \"svg\";\n\n\t// Drawing in DOM by using Table tag\n\tvar Drawing = useSVG ? svgDrawer : !_isSupportCanvas() ? (function () {\n\t\tvar Drawing = function (el, htOption) {\n\t\t\tthis._el = el;\n\t\t\tthis._htOption = htOption;\n\t\t};\n\n\t\t/**\n\t\t * Draw the QRCode\n\t\t *\n\t\t * @param {QRCode} oQRCode\n\t\t */\n\t\tDrawing.prototype.draw = function (oQRCode) {\n            var _htOption = this._htOption;\n            var _el = this._el;\n\t\t\tvar nCount = oQRCode.getModuleCount();\n\t\t\tvar nWidth = Math.floor(_htOption.width / nCount);\n\t\t\tvar nHeight = Math.floor(_htOption.height / nCount);\n\t\t\tvar aHTML = ['<table style=\"border:0;border-collapse:collapse;\">'];\n\n\t\t\tfor (var row = 0; row < nCount; row++) {\n\t\t\t\taHTML.push('<tr>');\n\n\t\t\t\tfor (var col = 0; col < nCount; col++) {\n\t\t\t\t\taHTML.push('<td style=\"border:0;border-collapse:collapse;padding:0;margin:0;width:' + nWidth + 'px;height:' + nHeight + 'px;background-color:' + (oQRCode.isDark(row, col) ? _htOption.colorDark : _htOption.colorLight) + ';\"></td>');\n\t\t\t\t}\n\n\t\t\t\taHTML.push('</tr>');\n\t\t\t}\n\n\t\t\taHTML.push('</table>');\n\t\t\t_el.innerHTML = aHTML.join('');\n\n\t\t\t// Fix the margin values as real size.\n\t\t\tvar elTable = _el.childNodes[0];\n\t\t\tvar nLeftMarginTable = (_htOption.width - elTable.offsetWidth) / 2;\n\t\t\tvar nTopMarginTable = (_htOption.height - elTable.offsetHeight) / 2;\n\n\t\t\tif (nLeftMarginTable > 0 && nTopMarginTable > 0) {\n\t\t\t\telTable.style.margin = nTopMarginTable + \"px \" + nLeftMarginTable + \"px\";\n\t\t\t}\n\t\t};\n\n\t\t/**\n\t\t * Clear the QRCode\n\t\t */\n\t\tDrawing.prototype.clear = function () {\n\t\t\tthis._el.innerHTML = '';\n\t\t};\n\n\t\treturn Drawing;\n\t})() : (function () { // Drawing in Canvas\n\t\tfunction _onMakeImage() {\n\t\t\tthis._elImage.src = this._elCanvas.toDataURL(\"image/png\");\n\t\t\tthis._elImage.style.display = \"block\";\n\t\t\tthis._elCanvas.style.display = \"none\";\n\t\t}\n\n\t\t// Android 2.1 bug workaround\n\t\t// http://code.google.com/p/android/issues/detail?id=5141\n\t\tif (this._android && this._android <= 2.1) {\n\t    \tvar factor = 1 / window.devicePixelRatio;\n\t        var drawImage = CanvasRenderingContext2D.prototype.drawImage;\n\t    \tCanvasRenderingContext2D.prototype.drawImage = function (image, sx, sy, sw, sh, dx, dy, dw, dh) {\n\t    \t\tif ((\"nodeName\" in image) && /img/i.test(image.nodeName)) {\n\t\t        \tfor (var i = arguments.length - 1; i >= 1; i--) {\n\t\t            \targuments[i] = arguments[i] * factor;\n\t\t        \t}\n\t    \t\t} else if (typeof dw == \"undefined\") {\n\t    \t\t\targuments[1] *= factor;\n\t    \t\t\targuments[2] *= factor;\n\t    \t\t\targuments[3] *= factor;\n\t    \t\t\targuments[4] *= factor;\n\t    \t\t}\n\n\t        \tdrawImage.apply(this, arguments);\n\t    \t};\n\t\t}\n\n\t\t/**\n\t\t * Check whether the user's browser supports Data URI or not\n\t\t *\n\t\t * @private\n\t\t * @param {Function} fSuccess Occurs if it supports Data URI\n\t\t * @param {Function} fFail Occurs if it doesn't support Data URI\n\t\t */\n\t\tfunction _safeSetDataURI(fSuccess, fFail) {\n            var self = this;\n            self._fFail = fFail;\n            self._fSuccess = fSuccess;\n\n            // Check it just once\n            if (self._bSupportDataURI === null) {\n                var el = document.createElement(\"img\");\n                var fOnError = function() {\n                    self._bSupportDataURI = false;\n\n                    if (self._fFail) {\n                        self._fFail.call(self);\n                    }\n                };\n                var fOnSuccess = function() {\n                    self._bSupportDataURI = true;\n\n                    if (self._fSuccess) {\n                        self._fSuccess.call(self);\n                    }\n                };\n\n                el.onabort = fOnError;\n                el.onerror = fOnError;\n                el.onload = fOnSuccess;\n                el.src = \"data:image/gif;base64,iVBORw0KGgoAAAANSUhEUgAAAAUAAAAFCAYAAACNbyblAAAAHElEQVQI12P4//8/w38GIAXDIBKE0DHxgljNBAAO9TXL0Y4OHwAAAABJRU5ErkJggg==\"; // the Image contains 1px data.\n                return;\n            } else if (self._bSupportDataURI === true && self._fSuccess) {\n                self._fSuccess.call(self);\n            } else if (self._bSupportDataURI === false && self._fFail) {\n                self._fFail.call(self);\n            }\n\t\t};\n\n\t\t/**\n\t\t * Drawing QRCode by using canvas\n\t\t *\n\t\t * @constructor\n\t\t * @param {HTMLElement} el\n\t\t * @param {Object} htOption QRCode Options\n\t\t */\n\t\tvar Drawing = function (el, htOption) {\n    \t\tthis._bIsPainted = false;\n    \t\tthis._android = _getAndroid();\n\n\t\t\tthis._htOption = htOption;\n\t\t\tthis._elCanvas = document.createElement(\"canvas\");\n\t\t\tthis._elCanvas.width = htOption.width;\n\t\t\tthis._elCanvas.height = htOption.height;\n\t\t\tel.appendChild(this._elCanvas);\n\t\t\tthis._el = el;\n\t\t\tthis._oContext = this._elCanvas.getContext(\"2d\");\n\t\t\tthis._bIsPainted = false;\n\t\t\tthis._elImage = document.createElement(\"img\");\n\t\t\tthis._elImage.alt = \"Scan me!\";\n\t\t\tthis._elImage.style.display = \"none\";\n\t\t\tthis._el.appendChild(this._elImage);\n\t\t\tthis._bSupportDataURI = null;\n\t\t};\n\n\t\t/**\n\t\t * Draw the QRCode\n\t\t *\n\t\t * @param {QRCode} oQRCode\n\t\t */\n\t\tDrawing.prototype.draw = function (oQRCode) {\n            var _elImage = this._elImage;\n            var _oContext = this._oContext;\n            var _htOption = this._htOption;\n\n\t\t\tvar nCount = oQRCode.getModuleCount();\n\t\t\tvar nWidth = _htOption.width / nCount;\n\t\t\tvar nHeight = _htOption.height / nCount;\n\t\t\tvar nRoundedWidth = Math.round(nWidth);\n\t\t\tvar nRoundedHeight = Math.round(nHeight);\n\n\t\t\t_elImage.style.display = \"none\";\n\t\t\tthis.clear();\n\n\t\t\tfor (var row = 0; row < nCount; row++) {\n\t\t\t\tfor (var col = 0; col < nCount; col++) {\n\t\t\t\t\tvar bIsDark = oQRCode.isDark(row, col);\n\t\t\t\t\tvar nLeft = col * nWidth;\n\t\t\t\t\tvar nTop = row * nHeight;\n\t\t\t\t\t_oContext.strokeStyle = bIsDark ? _htOption.colorDark : _htOption.colorLight;\n\t\t\t\t\t_oContext.lineWidth = 1;\n\t\t\t\t\t_oContext.fillStyle = bIsDark ? _htOption.colorDark : _htOption.colorLight;\n\t\t\t\t\t_oContext.fillRect(nLeft, nTop, nWidth, nHeight);\n\n\t\t\t\t\t// 안티 앨리어싱 방지 처리\n\t\t\t\t\t_oContext.strokeRect(\n\t\t\t\t\t\tMath.floor(nLeft) + 0.5,\n\t\t\t\t\t\tMath.floor(nTop) + 0.5,\n\t\t\t\t\t\tnRoundedWidth,\n\t\t\t\t\t\tnRoundedHeight\n\t\t\t\t\t);\n\n\t\t\t\t\t_oContext.strokeRect(\n\t\t\t\t\t\tMath.ceil(nLeft) - 0.5,\n\t\t\t\t\t\tMath.ceil(nTop) - 0.5,\n\t\t\t\t\t\tnRoundedWidth,\n\t\t\t\t\t\tnRoundedHeight\n\t\t\t\t\t);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tthis._bIsPainted = true;\n\t\t};\n\n\t\t/**\n\t\t * Make the image from Canvas if the browser supports Data URI.\n\t\t */\n\t\tDrawing.prototype.makeImage = function () {\n\t\t\tif (this._bIsPainted) {\n\t\t\t\t_safeSetDataURI.call(this, _onMakeImage);\n\t\t\t}\n\t\t};\n\n\t\t/**\n\t\t * Return whether the QRCode is painted or not\n\t\t *\n\t\t * @return {Boolean}\n\t\t */\n\t\tDrawing.prototype.isPainted = function () {\n\t\t\treturn this._bIsPainted;\n\t\t};\n\n\t\t/**\n\t\t * Clear the QRCode\n\t\t */\n\t\tDrawing.prototype.clear = function () {\n\t\t\tthis._oContext.clearRect(0, 0, this._elCanvas.width, this._elCanvas.height);\n\t\t\tthis._bIsPainted = false;\n\t\t};\n\n\t\t/**\n\t\t * @private\n\t\t * @param {Number} nNumber\n\t\t */\n\t\tDrawing.prototype.round = function (nNumber) {\n\t\t\tif (!nNumber) {\n\t\t\t\treturn nNumber;\n\t\t\t}\n\n\t\t\treturn Math.floor(nNumber * 1000) / 1000;\n\t\t};\n\n\t\treturn Drawing;\n\t})();\n\n\t/**\n\t * Get the type by string length\n\t *\n\t * @private\n\t * @param {String} sText\n\t * @param {Number} nCorrectLevel\n\t * @return {Number} type\n\t */\n\tfunction _getTypeNumber(sText, nCorrectLevel) {\n\t\tvar nType = 1;\n\t\tvar length = _getUTF8Length(sText);\n\n\t\tfor (var i = 0, len = QRCodeLimitLength.length; i <= len; i++) {\n\t\t\tvar nLimit = 0;\n\n\t\t\tswitch (nCorrectLevel) {\n\t\t\t\tcase QRErrorCorrectLevel.L :\n\t\t\t\t\tnLimit = QRCodeLimitLength[i][0];\n\t\t\t\t\tbreak;\n\t\t\t\tcase QRErrorCorrectLevel.M :\n\t\t\t\t\tnLimit = QRCodeLimitLength[i][1];\n\t\t\t\t\tbreak;\n\t\t\t\tcase QRErrorCorrectLevel.Q :\n\t\t\t\t\tnLimit = QRCodeLimitLength[i][2];\n\t\t\t\t\tbreak;\n\t\t\t\tcase QRErrorCorrectLevel.H :\n\t\t\t\t\tnLimit = QRCodeLimitLength[i][3];\n\t\t\t\t\tbreak;\n\t\t\t}\n\n\t\t\tif (length <= nLimit) {\n\t\t\t\tbreak;\n\t\t\t} else {\n\t\t\t\tnType++;\n\t\t\t}\n\t\t}\n\n\t\tif (nType > QRCodeLimitLength.length) {\n\t\t\tthrow new Error(\"Too long data\");\n\t\t}\n\n\t\treturn nType;\n\t}\n\n\tfunction _getUTF8Length(sText) {\n\t\tvar replacedText = encodeURI(sText).toString().replace(/\\%[0-9a-fA-F]{2}/g, 'a');\n\t\treturn replacedText.length + (replacedText.length != sText ? 3 : 0);\n\t}\n\n\t/**\n\t * @class QRCode\n\t * @constructor\n\t * @example\n\t * new QRCode(document.getElementById(\"test\"), \"http://jindo.dev.naver.com/collie\");\n\t *\n\t * @example\n\t * var oQRCode = new QRCode(\"test\", {\n\t *    text : \"http://naver.com\",\n\t *    width : 128,\n\t *    height : 128\n\t * });\n\t *\n\t * oQRCode.clear(); // Clear the QRCode.\n\t * oQRCode.makeCode(\"http://map.naver.com\"); // Re-create the QRCode.\n\t *\n\t * @param {HTMLElement|String} el target element or 'id' attribute of element.\n\t * @param {Object|String} vOption\n\t * @param {String} vOption.text QRCode link data\n\t * @param {Number} [vOption.width=256]\n\t * @param {Number} [vOption.height=256]\n\t * @param {String} [vOption.colorDark=\"#000000\"]\n\t * @param {String} [vOption.colorLight=\"#ffffff\"]\n\t * @param {QRCode.CorrectLevel} [vOption.correctLevel=QRCode.CorrectLevel.H] [L|M|Q|H]\n\t */\n\tQRCode = function (el, vOption) {\n\t\tthis._htOption = {\n\t\t\twidth : 256,\n\t\t\theight : 256,\n\t\t\ttypeNumber : 4,\n\t\t\tcolorDark : \"#000000\",\n\t\t\tcolorLight : \"#ffffff\",\n\t\t\tcorrectLevel : QRErrorCorrectLevel.H\n\t\t};\n\n\t\tif (typeof vOption === 'string') {\n\t\t\tvOption\t= {\n\t\t\t\ttext : vOption\n\t\t\t};\n\t\t}\n\n\t\t// Overwrites options\n\t\tif (vOption) {\n\t\t\tfor (var i in vOption) {\n\t\t\t\tthis._htOption[i] = vOption[i];\n\t\t\t}\n\t\t}\n\n\t\tif (typeof el == \"string\") {\n\t\t\tel = document.getElementById(el);\n\t\t}\n\n\t\tif (this._htOption.useSVG) {\n\t\t\tDrawing = svgDrawer;\n\t\t}\n\n\t\tthis._android = _getAndroid();\n\t\tthis._el = el;\n\t\tthis._oQRCode = null;\n\t\tthis._oDrawing = new Drawing(this._el, this._htOption);\n\n\t\tif (this._htOption.text) {\n\t\t\tthis.makeCode(this._htOption.text);\n\t\t}\n\t};\n\n\t/**\n\t * Make the QRCode\n\t *\n\t * @param {String} sText link data\n\t */\n\tQRCode.prototype.makeCode = function (sText) {\n\t\tthis._oQRCode = new QRCodeModel(_getTypeNumber(sText, this._htOption.correctLevel), this._htOption.correctLevel);\n\t\tthis._oQRCode.addData(sText);\n\t\tthis._oQRCode.make();\n\t\tthis._el.title = sText;\n\t\tthis._oDrawing.draw(this._oQRCode);\n\t\tthis.makeImage();\n\t};\n\n\t/**\n\t * Make the Image from Canvas element\n\t * - It occurs automatically\n\t * - Android below 3 doesn't support Data-URI spec.\n\t *\n\t * @private\n\t */\n\tQRCode.prototype.makeImage = function () {\n\t\tif (typeof this._oDrawing.makeImage == \"function\" && (!this._android || this._android >= 3)) {\n\t\t\tthis._oDrawing.makeImage();\n\t\t}\n\t};\n\n\t/**\n\t * Clear the QRCode\n\t */\n\tQRCode.prototype.clear = function () {\n\t\tthis._oDrawing.clear();\n\t};\n\n\t/**\n\t * @name QRCode.CorrectLevel\n\t */\n\tQRCode.CorrectLevel = QRErrorCorrectLevel;\n\t\n\treturn QRCode;\n\t\n}));\n"], "mappings": ";AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,MAAM;AAET,WAAUC,IAAI,EAAEC,OAAO,EAAE;EAEzB;EACC,IAAI,QAAOC,OAAO,iCAAAC,OAAA,CAAPD,OAAO,MAAI,QAAQ,EAAEE,MAAM,CAACF,OAAO,GAAGD,OAAO,CAAC,CAAC;;EAE1D,qBACK,IAAI,OAAOI,MAAM,IAAI,UAAU,IAAIA,MAAM,CAACC,GAAG,EAAED,MAAM,CAACJ,OAAO,CAAC;;EAEnE,iBACKD,IAAI,CAACD,MAAM,GAAGE,OAAO,CAAC,CAAC;AAE9B,CAAC,EAAC,IAAI,EAAE,YAAY;EAAE;EACrB;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,SAASM,UAAUA,CAACC,IAAI,EAAE;IACzB,IAAI,CAACC,IAAI,GAAGC,MAAM,CAACC,cAAc;IACjC,IAAI,CAACH,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACI,UAAU,GAAG,EAAE;;IAEpB;IACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAG,IAAI,CAACN,IAAI,CAACO,MAAM,EAAEF,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;MACjD,IAAIG,SAAS,GAAG,EAAE;MAClB,IAAIC,IAAI,GAAG,IAAI,CAACT,IAAI,CAACU,UAAU,CAACL,CAAC,CAAC;MAElC,IAAII,IAAI,GAAG,OAAO,EAAE;QACnBD,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,GAAI,CAACC,IAAI,GAAG,QAAQ,MAAM,EAAG;QAChDD,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,GAAI,CAACC,IAAI,GAAG,OAAO,MAAM,EAAG;QAC/CD,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,GAAI,CAACC,IAAI,GAAG,KAAK,MAAM,CAAE;QAC5CD,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,GAAIC,IAAI,GAAG,IAAK;MACpC,CAAC,MAAM,IAAIA,IAAI,GAAG,KAAK,EAAE;QACxBD,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,GAAI,CAACC,IAAI,GAAG,MAAM,MAAM,EAAG;QAC9CD,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,GAAI,CAACC,IAAI,GAAG,KAAK,MAAM,CAAE;QAC5CD,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,GAAIC,IAAI,GAAG,IAAK;MACpC,CAAC,MAAM,IAAIA,IAAI,GAAG,IAAI,EAAE;QACvBD,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,GAAI,CAACC,IAAI,GAAG,KAAK,MAAM,CAAE;QAC5CD,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,GAAIC,IAAI,GAAG,IAAK;MACpC,CAAC,MAAM;QACND,SAAS,CAAC,CAAC,CAAC,GAAGC,IAAI;MACpB;MAEA,IAAI,CAACL,UAAU,CAACO,IAAI,CAACH,SAAS,CAAC;IAChC;IAEA,IAAI,CAACJ,UAAU,GAAGQ,KAAK,CAACC,SAAS,CAACC,MAAM,CAACC,KAAK,CAAC,EAAE,EAAE,IAAI,CAACX,UAAU,CAAC;IAEnE,IAAI,IAAI,CAACA,UAAU,CAACG,MAAM,IAAI,IAAI,CAACP,IAAI,CAACO,MAAM,EAAE;MAC/C,IAAI,CAACH,UAAU,CAACY,OAAO,CAAC,GAAG,CAAC;MAC5B,IAAI,CAACZ,UAAU,CAACY,OAAO,CAAC,GAAG,CAAC;MAC5B,IAAI,CAACZ,UAAU,CAACY,OAAO,CAAC,GAAG,CAAC;IAC7B;EACD;EAEAjB,UAAU,CAACc,SAAS,GAAG;IACtBI,SAAS,EAAE,SAAAA,UAAUC,MAAM,EAAE;MAC5B,OAAO,IAAI,CAACd,UAAU,CAACG,MAAM;IAC9B,CAAC;IACDY,KAAK,EAAE,SAAAA,MAAUD,MAAM,EAAE;MACxB,KAAK,IAAIb,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAG,IAAI,CAACF,UAAU,CAACG,MAAM,EAAEF,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;QACvDa,MAAM,CAACE,GAAG,CAAC,IAAI,CAAChB,UAAU,CAACC,CAAC,CAAC,EAAE,CAAC,CAAC;MAClC;IACD;EACD,CAAC;EAED,SAASgB,WAAWA,CAACC,UAAU,EAAEC,iBAAiB,EAAE;IACnD,IAAI,CAACD,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACC,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,WAAW,GAAG,CAAC;IACpB,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,QAAQ,GAAG,EAAE;EACnB;EAEAN,WAAW,CAACR,SAAS,GAAC;IAACe,OAAO,EAAC,SAAAA,QAAS5B,IAAI,EAAC;MAAC,IAAI6B,OAAO,GAAC,IAAI9B,UAAU,CAACC,IAAI,CAAC;MAAC,IAAI,CAAC2B,QAAQ,CAAChB,IAAI,CAACkB,OAAO,CAAC;MAAC,IAAI,CAACH,SAAS,GAAC,IAAI;IAAC,CAAC;IAACI,MAAM,EAAC,SAAAA,OAASC,GAAG,EAACC,GAAG,EAAC;MAAC,IAAGD,GAAG,GAAC,CAAC,IAAE,IAAI,CAACN,WAAW,IAAEM,GAAG,IAAEC,GAAG,GAAC,CAAC,IAAE,IAAI,CAACP,WAAW,IAAEO,GAAG,EAAC;QAAC,MAAM,IAAIC,KAAK,CAACF,GAAG,GAAC,GAAG,GAACC,GAAG,CAAC;MAAC;MACtP,OAAO,IAAI,CAACR,OAAO,CAACO,GAAG,CAAC,CAACC,GAAG,CAAC;IAAC,CAAC;IAACE,cAAc,EAAC,SAAAA,eAAA,EAAU;MAAC,OAAO,IAAI,CAACT,WAAW;IAAC,CAAC;IAACU,IAAI,EAAC,SAAAA,KAAA,EAAU;MAAC,IAAI,CAACC,QAAQ,CAAC,KAAK,EAAC,IAAI,CAACC,kBAAkB,CAAC,CAAC,CAAC;IAAC,CAAC;IAACD,QAAQ,EAAC,SAAAA,SAASE,IAAI,EAACC,WAAW,EAAC;MAAC,IAAI,CAACd,WAAW,GAAC,IAAI,CAACH,UAAU,GAAC,CAAC,GAAC,EAAE;MAAC,IAAI,CAACE,OAAO,GAAC,IAAIZ,KAAK,CAAC,IAAI,CAACa,WAAW,CAAC;MAAC,KAAI,IAAIM,GAAG,GAAC,CAAC,EAACA,GAAG,GAAC,IAAI,CAACN,WAAW,EAACM,GAAG,EAAE,EAAC;QAAC,IAAI,CAACP,OAAO,CAACO,GAAG,CAAC,GAAC,IAAInB,KAAK,CAAC,IAAI,CAACa,WAAW,CAAC;QAAC,KAAI,IAAIO,GAAG,GAAC,CAAC,EAACA,GAAG,GAAC,IAAI,CAACP,WAAW,EAACO,GAAG,EAAE,EAAC;UAAC,IAAI,CAACR,OAAO,CAACO,GAAG,CAAC,CAACC,GAAG,CAAC,GAAC,IAAI;QAAC;MAAC;MACva,IAAI,CAACQ,yBAAyB,CAAC,CAAC,EAAC,CAAC,CAAC;MAAC,IAAI,CAACA,yBAAyB,CAAC,IAAI,CAACf,WAAW,GAAC,CAAC,EAAC,CAAC,CAAC;MAAC,IAAI,CAACe,yBAAyB,CAAC,CAAC,EAAC,IAAI,CAACf,WAAW,GAAC,CAAC,CAAC;MAAC,IAAI,CAACgB,0BAA0B,CAAC,CAAC;MAAC,IAAI,CAACC,kBAAkB,CAAC,CAAC;MAAC,IAAI,CAACC,aAAa,CAACL,IAAI,EAACC,WAAW,CAAC;MAAC,IAAG,IAAI,CAACjB,UAAU,IAAE,CAAC,EAAC;QAAC,IAAI,CAACsB,eAAe,CAACN,IAAI,CAAC;MAAC;MACjS,IAAG,IAAI,CAACZ,SAAS,IAAE,IAAI,EAAC;QAAC,IAAI,CAACA,SAAS,GAACL,WAAW,CAACwB,UAAU,CAAC,IAAI,CAACvB,UAAU,EAAC,IAAI,CAACC,iBAAiB,EAAC,IAAI,CAACI,QAAQ,CAAC;MAAC;MACrH,IAAI,CAACmB,OAAO,CAAC,IAAI,CAACpB,SAAS,EAACa,WAAW,CAAC;IAAC,CAAC;IAACC,yBAAyB,EAAC,SAAAA,0BAAST,GAAG,EAACC,GAAG,EAAC;MAAC,KAAI,IAAIe,CAAC,GAAC,CAAC,CAAC,EAACA,CAAC,IAAE,CAAC,EAACA,CAAC,EAAE,EAAC;QAAC,IAAGhB,GAAG,GAACgB,CAAC,IAAE,CAAC,CAAC,IAAE,IAAI,CAACtB,WAAW,IAAEM,GAAG,GAACgB,CAAC,EAAC;QAAS,KAAI,IAAIC,CAAC,GAAC,CAAC,CAAC,EAACA,CAAC,IAAE,CAAC,EAACA,CAAC,EAAE,EAAC;UAAC,IAAGhB,GAAG,GAACgB,CAAC,IAAE,CAAC,CAAC,IAAE,IAAI,CAACvB,WAAW,IAAEO,GAAG,GAACgB,CAAC,EAAC;UAAS,IAAI,CAAC,IAAED,CAAC,IAAEA,CAAC,IAAE,CAAC,KAAGC,CAAC,IAAE,CAAC,IAAEA,CAAC,IAAE,CAAC,CAAC,IAAI,CAAC,IAAEA,CAAC,IAAEA,CAAC,IAAE,CAAC,KAAGD,CAAC,IAAE,CAAC,IAAEA,CAAC,IAAE,CAAC,CAAE,IAAG,CAAC,IAAEA,CAAC,IAAEA,CAAC,IAAE,CAAC,IAAE,CAAC,IAAEC,CAAC,IAAEA,CAAC,IAAE,CAAE,EAAC;YAAC,IAAI,CAACxB,OAAO,CAACO,GAAG,GAACgB,CAAC,CAAC,CAACf,GAAG,GAACgB,CAAC,CAAC,GAAC,IAAI;UAAC,CAAC,MAAI;YAAC,IAAI,CAACxB,OAAO,CAACO,GAAG,GAACgB,CAAC,CAAC,CAACf,GAAG,GAACgB,CAAC,CAAC,GAAC,KAAK;UAAC;QAAC;MAAC;IAAC,CAAC;IAACX,kBAAkB,EAAC,SAAAA,mBAAA,EAAU;MAAC,IAAIY,YAAY,GAAC,CAAC;MAAC,IAAIC,OAAO,GAAC,CAAC;MAAC,KAAI,IAAI7C,CAAC,GAAC,CAAC,EAACA,CAAC,GAAC,CAAC,EAACA,CAAC,EAAE,EAAC;QAAC,IAAI,CAAC+B,QAAQ,CAAC,IAAI,EAAC/B,CAAC,CAAC;QAAC,IAAI8C,SAAS,GAACC,MAAM,CAACC,YAAY,CAAC,IAAI,CAAC;QAAC,IAAGhD,CAAC,IAAE,CAAC,IAAE4C,YAAY,GAACE,SAAS,EAAC;UAACF,YAAY,GAACE,SAAS;UAACD,OAAO,GAAC7C,CAAC;QAAC;MAAC;MACzlB,OAAO6C,OAAO;IAAC,CAAC;IAACI,eAAe,EAAC,SAAAA,gBAASC,SAAS,EAACC,aAAa,EAACC,KAAK,EAAC;MAAC,IAAIC,KAAK,GAACH,SAAS,CAACI,oBAAoB,CAACH,aAAa,EAACC,KAAK,CAAC;MAAC,IAAIG,EAAE,GAAC,CAAC;MAAC,IAAI,CAACzB,IAAI,CAAC,CAAC;MAAC,KAAI,IAAIJ,GAAG,GAAC,CAAC,EAACA,GAAG,GAAC,IAAI,CAACP,OAAO,CAACjB,MAAM,EAACwB,GAAG,EAAE,EAAC;QAAC,IAAI8B,CAAC,GAAC9B,GAAG,GAAC6B,EAAE;QAAC,KAAI,IAAI5B,GAAG,GAAC,CAAC,EAACA,GAAG,GAAC,IAAI,CAACR,OAAO,CAACO,GAAG,CAAC,CAACxB,MAAM,EAACyB,GAAG,EAAE,EAAC;UAAC,IAAI8B,CAAC,GAAC9B,GAAG,GAAC4B,EAAE;UAAC,IAAIG,IAAI,GAAC,IAAI,CAACvC,OAAO,CAACO,GAAG,CAAC,CAACC,GAAG,CAAC;UAAC,IAAG+B,IAAI,EAAC;YAACL,KAAK,CAACM,SAAS,CAAC,CAAC,EAAC,GAAG,CAAC;YAACN,KAAK,CAACO,MAAM,CAACH,CAAC,EAACD,CAAC,CAAC;YAACH,KAAK,CAACQ,MAAM,CAACJ,CAAC,GAACF,EAAE,EAACC,CAAC,CAAC;YAACH,KAAK,CAACQ,MAAM,CAACJ,CAAC,GAACF,EAAE,EAACC,CAAC,GAACD,EAAE,CAAC;YAACF,KAAK,CAACQ,MAAM,CAACJ,CAAC,EAACD,CAAC,GAACD,EAAE,CAAC;YAACF,KAAK,CAACS,OAAO,CAAC,CAAC;UAAC;QAAC;MAAC;MAC3b,OAAOT,KAAK;IAAC,CAAC;IAAChB,kBAAkB,EAAC,SAAAA,mBAAA,EAAU;MAAC,KAAI,IAAIK,CAAC,GAAC,CAAC,EAACA,CAAC,GAAC,IAAI,CAACtB,WAAW,GAAC,CAAC,EAACsB,CAAC,EAAE,EAAC;QAAC,IAAG,IAAI,CAACvB,OAAO,CAACuB,CAAC,CAAC,CAAC,CAAC,CAAC,IAAE,IAAI,EAAC;UAAC;QAAS;QACzH,IAAI,CAACvB,OAAO,CAACuB,CAAC,CAAC,CAAC,CAAC,CAAC,GAAEA,CAAC,GAAC,CAAC,IAAE,CAAE;MAAC;MAC5B,KAAI,IAAIC,CAAC,GAAC,CAAC,EAACA,CAAC,GAAC,IAAI,CAACvB,WAAW,GAAC,CAAC,EAACuB,CAAC,EAAE,EAAC;QAAC,IAAG,IAAI,CAACxB,OAAO,CAAC,CAAC,CAAC,CAACwB,CAAC,CAAC,IAAE,IAAI,EAAC;UAAC;QAAS;QAC5E,IAAI,CAACxB,OAAO,CAAC,CAAC,CAAC,CAACwB,CAAC,CAAC,GAAEA,CAAC,GAAC,CAAC,IAAE,CAAE;MAAC;IAAC,CAAC;IAACP,0BAA0B,EAAC,SAAAA,2BAAA,EAAU;MAAC,IAAI2B,GAAG,GAAChB,MAAM,CAACiB,kBAAkB,CAAC,IAAI,CAAC/C,UAAU,CAAC;MAAC,KAAI,IAAIjB,CAAC,GAAC,CAAC,EAACA,CAAC,GAAC+D,GAAG,CAAC7D,MAAM,EAACF,CAAC,EAAE,EAAC;QAAC,KAAI,IAAIiE,CAAC,GAAC,CAAC,EAACA,CAAC,GAACF,GAAG,CAAC7D,MAAM,EAAC+D,CAAC,EAAE,EAAC;UAAC,IAAIvC,GAAG,GAACqC,GAAG,CAAC/D,CAAC,CAAC;UAAC,IAAI2B,GAAG,GAACoC,GAAG,CAACE,CAAC,CAAC;UAAC,IAAG,IAAI,CAAC9C,OAAO,CAACO,GAAG,CAAC,CAACC,GAAG,CAAC,IAAE,IAAI,EAAC;YAAC;UAAS;UAC5P,KAAI,IAAIe,CAAC,GAAC,CAAC,CAAC,EAACA,CAAC,IAAE,CAAC,EAACA,CAAC,EAAE,EAAC;YAAC,KAAI,IAAIC,CAAC,GAAC,CAAC,CAAC,EAACA,CAAC,IAAE,CAAC,EAACA,CAAC,EAAE,EAAC;cAAC,IAAGD,CAAC,IAAE,CAAC,CAAC,IAAEA,CAAC,IAAE,CAAC,IAAEC,CAAC,IAAE,CAAC,CAAC,IAAEA,CAAC,IAAE,CAAC,IAAGD,CAAC,IAAE,CAAC,IAAEC,CAAC,IAAE,CAAE,EAAC;gBAAC,IAAI,CAACxB,OAAO,CAACO,GAAG,GAACgB,CAAC,CAAC,CAACf,GAAG,GAACgB,CAAC,CAAC,GAAC,IAAI;cAAC,CAAC,MAAI;gBAAC,IAAI,CAACxB,OAAO,CAACO,GAAG,GAACgB,CAAC,CAAC,CAACf,GAAG,GAACgB,CAAC,CAAC,GAAC,KAAK;cAAC;YAAC;UAAC;QAAC;MAAC;IAAC,CAAC;IAACJ,eAAe,EAAC,SAAAA,gBAASN,IAAI,EAAC;MAAC,IAAIiC,IAAI,GAACnB,MAAM,CAACoB,gBAAgB,CAAC,IAAI,CAAClD,UAAU,CAAC;MAAC,KAAI,IAAIjB,CAAC,GAAC,CAAC,EAACA,CAAC,GAAC,EAAE,EAACA,CAAC,EAAE,EAAC;QAAC,IAAIoE,GAAG,GAAE,CAACnC,IAAI,IAAE,CAAEiC,IAAI,IAAElE,CAAC,GAAE,CAAC,KAAG,CAAE;QAAC,IAAI,CAACmB,OAAO,CAACkD,IAAI,CAACC,KAAK,CAACtE,CAAC,GAAC,CAAC,CAAC,CAAC,CAACA,CAAC,GAAC,CAAC,GAAC,IAAI,CAACoB,WAAW,GAAC,CAAC,GAAC,CAAC,CAAC,GAACgD,GAAG;MAAC;MAC5W,KAAI,IAAIpE,CAAC,GAAC,CAAC,EAACA,CAAC,GAAC,EAAE,EAACA,CAAC,EAAE,EAAC;QAAC,IAAIoE,GAAG,GAAE,CAACnC,IAAI,IAAE,CAAEiC,IAAI,IAAElE,CAAC,GAAE,CAAC,KAAG,CAAE;QAAC,IAAI,CAACmB,OAAO,CAACnB,CAAC,GAAC,CAAC,GAAC,IAAI,CAACoB,WAAW,GAAC,CAAC,GAAC,CAAC,CAAC,CAACiD,IAAI,CAACC,KAAK,CAACtE,CAAC,GAAC,CAAC,CAAC,CAAC,GAACoE,GAAG;MAAC;IAAC,CAAC;IAAC9B,aAAa,EAAC,SAAAA,cAASL,IAAI,EAACC,WAAW,EAAC;MAAC,IAAIvC,IAAI,GAAE,IAAI,CAACuB,iBAAiB,IAAE,CAAC,GAAEgB,WAAW;MAAC,IAAIgC,IAAI,GAACnB,MAAM,CAACwB,cAAc,CAAC5E,IAAI,CAAC;MAAC,KAAI,IAAIK,CAAC,GAAC,CAAC,EAACA,CAAC,GAAC,EAAE,EAACA,CAAC,EAAE,EAAC;QAAC,IAAIoE,GAAG,GAAE,CAACnC,IAAI,IAAE,CAAEiC,IAAI,IAAElE,CAAC,GAAE,CAAC,KAAG,CAAE;QAAC,IAAGA,CAAC,GAAC,CAAC,EAAC;UAAC,IAAI,CAACmB,OAAO,CAACnB,CAAC,CAAC,CAAC,CAAC,CAAC,GAACoE,GAAG;QAAC,CAAC,MAAK,IAAGpE,CAAC,GAAC,CAAC,EAAC;UAAC,IAAI,CAACmB,OAAO,CAACnB,CAAC,GAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAACoE,GAAG;QAAC,CAAC,MAAI;UAAC,IAAI,CAACjD,OAAO,CAAC,IAAI,CAACC,WAAW,GAAC,EAAE,GAACpB,CAAC,CAAC,CAAC,CAAC,CAAC,GAACoE,GAAG;QAAC;MAAC;MACta,KAAI,IAAIpE,CAAC,GAAC,CAAC,EAACA,CAAC,GAAC,EAAE,EAACA,CAAC,EAAE,EAAC;QAAC,IAAIoE,GAAG,GAAE,CAACnC,IAAI,IAAE,CAAEiC,IAAI,IAAElE,CAAC,GAAE,CAAC,KAAG,CAAE;QAAC,IAAGA,CAAC,GAAC,CAAC,EAAC;UAAC,IAAI,CAACmB,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAACC,WAAW,GAACpB,CAAC,GAAC,CAAC,CAAC,GAACoE,GAAG;QAAC,CAAC,MAAK,IAAGpE,CAAC,GAAC,CAAC,EAAC;UAAC,IAAI,CAACmB,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,GAACnB,CAAC,GAAC,CAAC,GAAC,CAAC,CAAC,GAACoE,GAAG;QAAC,CAAC,MAAI;UAAC,IAAI,CAACjD,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,GAACnB,CAAC,GAAC,CAAC,CAAC,GAACoE,GAAG;QAAC;MAAC;MACzL,IAAI,CAACjD,OAAO,CAAC,IAAI,CAACC,WAAW,GAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAE,CAACa,IAAK;IAAC,CAAC;IAACQ,OAAO,EAAC,SAAAA,QAAS9C,IAAI,EAACuC,WAAW,EAAC;MAAC,IAAIsC,GAAG,GAAC,CAAC,CAAC;MAAC,IAAI9C,GAAG,GAAC,IAAI,CAACN,WAAW,GAAC,CAAC;MAAC,IAAIqD,QAAQ,GAAC,CAAC;MAAC,IAAIC,SAAS,GAAC,CAAC;MAAC,KAAI,IAAI/C,GAAG,GAAC,IAAI,CAACP,WAAW,GAAC,CAAC,EAACO,GAAG,GAAC,CAAC,EAACA,GAAG,IAAE,CAAC,EAAC;QAAC,IAAGA,GAAG,IAAE,CAAC,EAACA,GAAG,EAAE;QAAC,OAAM,IAAI,EAAC;UAAC,KAAI,IAAIgB,CAAC,GAAC,CAAC,EAACA,CAAC,GAAC,CAAC,EAACA,CAAC,EAAE,EAAC;YAAC,IAAG,IAAI,CAACxB,OAAO,CAACO,GAAG,CAAC,CAACC,GAAG,GAACgB,CAAC,CAAC,IAAE,IAAI,EAAC;cAAC,IAAIe,IAAI,GAAC,KAAK;cAAC,IAAGgB,SAAS,GAAC/E,IAAI,CAACO,MAAM,EAAC;gBAACwD,IAAI,GAAE,CAAE/D,IAAI,CAAC+E,SAAS,CAAC,KAAGD,QAAQ,GAAE,CAAC,KAAG,CAAE;cAAC;cAC3W,IAAIE,IAAI,GAAC5B,MAAM,CAAC6B,OAAO,CAAC1C,WAAW,EAACR,GAAG,EAACC,GAAG,GAACgB,CAAC,CAAC;cAAC,IAAGgC,IAAI,EAAC;gBAACjB,IAAI,GAAC,CAACA,IAAI;cAAC;cACnE,IAAI,CAACvC,OAAO,CAACO,GAAG,CAAC,CAACC,GAAG,GAACgB,CAAC,CAAC,GAACe,IAAI;cAACe,QAAQ,EAAE;cAAC,IAAGA,QAAQ,IAAE,CAAC,CAAC,EAAC;gBAACC,SAAS,EAAE;gBAACD,QAAQ,GAAC,CAAC;cAAC;YAAC;UAAC;UACnF/C,GAAG,IAAE8C,GAAG;UAAC,IAAG9C,GAAG,GAAC,CAAC,IAAE,IAAI,CAACN,WAAW,IAAEM,GAAG,EAAC;YAACA,GAAG,IAAE8C,GAAG;YAACA,GAAG,GAAC,CAACA,GAAG;YAAC;UAAM;QAAC;MAAC;IAAC;EAAC,CAAC;EAACxD,WAAW,CAAC6D,IAAI,GAAC,IAAI;EAAC7D,WAAW,CAAC8D,IAAI,GAAC,IAAI;EAAC9D,WAAW,CAACwB,UAAU,GAAC,UAASvB,UAAU,EAACC,iBAAiB,EAACI,QAAQ,EAAC;IAAC,IAAIyD,QAAQ,GAACC,SAAS,CAACC,WAAW,CAAChE,UAAU,EAACC,iBAAiB,CAAC;IAAC,IAAIL,MAAM,GAAC,IAAIqE,WAAW,CAAC,CAAC;IAAC,KAAI,IAAIlF,CAAC,GAAC,CAAC,EAACA,CAAC,GAACsB,QAAQ,CAACpB,MAAM,EAACF,CAAC,EAAE,EAAC;MAAC,IAAIL,IAAI,GAAC2B,QAAQ,CAACtB,CAAC,CAAC;MAACa,MAAM,CAACE,GAAG,CAACpB,IAAI,CAACC,IAAI,EAAC,CAAC,CAAC;MAACiB,MAAM,CAACE,GAAG,CAACpB,IAAI,CAACiB,SAAS,CAAC,CAAC,EAACmC,MAAM,CAACoC,eAAe,CAACxF,IAAI,CAACC,IAAI,EAACqB,UAAU,CAAC,CAAC;MAACtB,IAAI,CAACmB,KAAK,CAACD,MAAM,CAAC;IAAC;IACtc,IAAIuE,cAAc,GAAC,CAAC;IAAC,KAAI,IAAIpF,CAAC,GAAC,CAAC,EAACA,CAAC,GAAC+E,QAAQ,CAAC7E,MAAM,EAACF,CAAC,EAAE,EAAC;MAACoF,cAAc,IAAEL,QAAQ,CAAC/E,CAAC,CAAC,CAACqF,SAAS;IAAC;IAC9F,IAAGxE,MAAM,CAACsE,eAAe,CAAC,CAAC,GAACC,cAAc,GAAC,CAAC,EAAC;MAAC,MAAM,IAAIxD,KAAK,CAAC,yBAAyB,GACtFf,MAAM,CAACsE,eAAe,CAAC,CAAC,GACxB,GAAG,GACHC,cAAc,GAAC,CAAC,GAChB,GAAG,CAAC;IAAC;IACN,IAAGvE,MAAM,CAACsE,eAAe,CAAC,CAAC,GAAC,CAAC,IAAEC,cAAc,GAAC,CAAC,EAAC;MAACvE,MAAM,CAACE,GAAG,CAAC,CAAC,EAAC,CAAC,CAAC;IAAC;IACjE,OAAMF,MAAM,CAACsE,eAAe,CAAC,CAAC,GAAC,CAAC,IAAE,CAAC,EAAC;MAACtE,MAAM,CAACyE,MAAM,CAAC,KAAK,CAAC;IAAC;IAC1D,OAAM,IAAI,EAAC;MAAC,IAAGzE,MAAM,CAACsE,eAAe,CAAC,CAAC,IAAEC,cAAc,GAAC,CAAC,EAAC;QAAC;MAAM;MACjEvE,MAAM,CAACE,GAAG,CAACC,WAAW,CAAC6D,IAAI,EAAC,CAAC,CAAC;MAAC,IAAGhE,MAAM,CAACsE,eAAe,CAAC,CAAC,IAAEC,cAAc,GAAC,CAAC,EAAC;QAAC;MAAM;MACpFvE,MAAM,CAACE,GAAG,CAACC,WAAW,CAAC8D,IAAI,EAAC,CAAC,CAAC;IAAC;IAC/B,OAAO9D,WAAW,CAACuE,WAAW,CAAC1E,MAAM,EAACkE,QAAQ,CAAC;EAAC,CAAC;EAAC/D,WAAW,CAACuE,WAAW,GAAC,UAAS1E,MAAM,EAACkE,QAAQ,EAAC;IAAC,IAAIS,MAAM,GAAC,CAAC;IAAC,IAAIC,UAAU,GAAC,CAAC;IAAC,IAAIC,UAAU,GAAC,CAAC;IAAC,IAAIC,MAAM,GAAC,IAAIpF,KAAK,CAACwE,QAAQ,CAAC7E,MAAM,CAAC;IAAC,IAAI0F,MAAM,GAAC,IAAIrF,KAAK,CAACwE,QAAQ,CAAC7E,MAAM,CAAC;IAAC,KAAI,IAAIwC,CAAC,GAAC,CAAC,EAACA,CAAC,GAACqC,QAAQ,CAAC7E,MAAM,EAACwC,CAAC,EAAE,EAAC;MAAC,IAAImD,OAAO,GAACd,QAAQ,CAACrC,CAAC,CAAC,CAAC2C,SAAS;MAAC,IAAIS,OAAO,GAACf,QAAQ,CAACrC,CAAC,CAAC,CAACqD,UAAU,GAACF,OAAO;MAACJ,UAAU,GAACpB,IAAI,CAAC2B,GAAG,CAACP,UAAU,EAACI,OAAO,CAAC;MAACH,UAAU,GAACrB,IAAI,CAAC2B,GAAG,CAACN,UAAU,EAACI,OAAO,CAAC;MAACH,MAAM,CAACjD,CAAC,CAAC,GAAC,IAAInC,KAAK,CAACsF,OAAO,CAAC;MAAC,KAAI,IAAI7F,CAAC,GAAC,CAAC,EAACA,CAAC,GAAC2F,MAAM,CAACjD,CAAC,CAAC,CAACxC,MAAM,EAACF,CAAC,EAAE,EAAC;QAAC2F,MAAM,CAACjD,CAAC,CAAC,CAAC1C,CAAC,CAAC,GAAC,IAAI,GAACa,MAAM,CAACA,MAAM,CAACb,CAAC,GAACwF,MAAM,CAAC;MAAC;MAC1gBA,MAAM,IAAEK,OAAO;MAAC,IAAII,MAAM,GAAClD,MAAM,CAACmD,yBAAyB,CAACJ,OAAO,CAAC;MAAC,IAAIK,OAAO,GAAC,IAAIC,YAAY,CAACT,MAAM,CAACjD,CAAC,CAAC,EAACuD,MAAM,CAACrF,SAAS,CAAC,CAAC,GAAC,CAAC,CAAC;MAAC,IAAIyF,OAAO,GAACF,OAAO,CAAC/B,GAAG,CAAC6B,MAAM,CAAC;MAACL,MAAM,CAAClD,CAAC,CAAC,GAAC,IAAInC,KAAK,CAAC0F,MAAM,CAACrF,SAAS,CAAC,CAAC,GAAC,CAAC,CAAC;MAAC,KAAI,IAAIZ,CAAC,GAAC,CAAC,EAACA,CAAC,GAAC4F,MAAM,CAAClD,CAAC,CAAC,CAACxC,MAAM,EAACF,CAAC,EAAE,EAAC;QAAC,IAAIsG,QAAQ,GAACtG,CAAC,GAACqG,OAAO,CAACzF,SAAS,CAAC,CAAC,GAACgF,MAAM,CAAClD,CAAC,CAAC,CAACxC,MAAM;QAAC0F,MAAM,CAAClD,CAAC,CAAC,CAAC1C,CAAC,CAAC,GAAEsG,QAAQ,IAAE,CAAC,GAAED,OAAO,CAACE,GAAG,CAACD,QAAQ,CAAC,GAAC,CAAC;MAAC;IAAC;IACxV,IAAIE,cAAc,GAAC,CAAC;IAAC,KAAI,IAAIxG,CAAC,GAAC,CAAC,EAACA,CAAC,GAAC+E,QAAQ,CAAC7E,MAAM,EAACF,CAAC,EAAE,EAAC;MAACwG,cAAc,IAAEzB,QAAQ,CAAC/E,CAAC,CAAC,CAAC+F,UAAU;IAAC;IAC/F,IAAIpG,IAAI,GAAC,IAAIY,KAAK,CAACiG,cAAc,CAAC;IAAC,IAAIC,KAAK,GAAC,CAAC;IAAC,KAAI,IAAIzG,CAAC,GAAC,CAAC,EAACA,CAAC,GAACyF,UAAU,EAACzF,CAAC,EAAE,EAAC;MAAC,KAAI,IAAI0C,CAAC,GAAC,CAAC,EAACA,CAAC,GAACqC,QAAQ,CAAC7E,MAAM,EAACwC,CAAC,EAAE,EAAC;QAAC,IAAG1C,CAAC,GAAC2F,MAAM,CAACjD,CAAC,CAAC,CAACxC,MAAM,EAAC;UAACP,IAAI,CAAC8G,KAAK,EAAE,CAAC,GAACd,MAAM,CAACjD,CAAC,CAAC,CAAC1C,CAAC,CAAC;QAAC;MAAC;IAAC;IACpK,KAAI,IAAIA,CAAC,GAAC,CAAC,EAACA,CAAC,GAAC0F,UAAU,EAAC1F,CAAC,EAAE,EAAC;MAAC,KAAI,IAAI0C,CAAC,GAAC,CAAC,EAACA,CAAC,GAACqC,QAAQ,CAAC7E,MAAM,EAACwC,CAAC,EAAE,EAAC;QAAC,IAAG1C,CAAC,GAAC4F,MAAM,CAAClD,CAAC,CAAC,CAACxC,MAAM,EAAC;UAACP,IAAI,CAAC8G,KAAK,EAAE,CAAC,GAACb,MAAM,CAAClD,CAAC,CAAC,CAAC1C,CAAC,CAAC;QAAC;MAAC;IAAC;IACrH,OAAOL,IAAI;EAAC,CAAC;EAAC,IAAIE,MAAM,GAAC;IAAC6G,WAAW,EAAC,CAAC,IAAE,CAAC;IAACC,cAAc,EAAC,CAAC,IAAE,CAAC;IAAC7G,cAAc,EAAC,CAAC,IAAE,CAAC;IAAC8G,UAAU,EAAC,CAAC,IAAE;EAAC,CAAC;EAAC,IAAIC,mBAAmB,GAAC;IAACC,CAAC,EAAC,CAAC;IAACC,CAAC,EAAC,CAAC;IAACC,CAAC,EAAC,CAAC;IAACC,CAAC,EAAC;EAAC,CAAC;EAAC,IAAIC,aAAa,GAAC;IAACC,UAAU,EAAC,CAAC;IAACC,UAAU,EAAC,CAAC;IAACC,UAAU,EAAC,CAAC;IAACC,UAAU,EAAC,CAAC;IAACC,UAAU,EAAC,CAAC;IAACC,UAAU,EAAC,CAAC;IAACC,UAAU,EAAC,CAAC;IAACC,UAAU,EAAC;EAAC,CAAC;EAAC,IAAI3E,MAAM,GAAC;IAAC4E,sBAAsB,EAAC,CAAC,EAAE,EAAC,CAAC,CAAC,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,GAAG,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,GAAG,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,GAAG,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,GAAG,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,GAAG,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,GAAG,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,CAAC;IAACC,GAAG,EAAE,CAAC,IAAE,EAAE,GAAG,CAAC,IAAE,CAAE,GAAE,CAAC,IAAE,CAAE,GAAE,CAAC,IAAE,CAAE,GAAE,CAAC,IAAE,CAAE,GAAE,CAAC,IAAE,CAAE,GAAE,CAAC,IAAE,CAAE;IAACC,GAAG,EAAE,CAAC,IAAE,EAAE,GAAG,CAAC,IAAE,EAAG,GAAE,CAAC,IAAE,EAAG,GAAE,CAAC,IAAE,CAAE,GAAE,CAAC,IAAE,CAAE,GAAE,CAAC,IAAE,CAAE,GAAE,CAAC,IAAE,CAAE,GAAE,CAAC,IAAE,CAAE;IAACC,QAAQ,EAAE,CAAC,IAAE,EAAE,GAAG,CAAC,IAAE,EAAG,GAAE,CAAC,IAAE,EAAG,GAAE,CAAC,IAAE,CAAE,GAAE,CAAC,IAAE,CAAE;IAACvD,cAAc,EAAC,SAAAA,eAAS5E,IAAI,EAAC;MAAC,IAAIoI,CAAC,GAACpI,IAAI,IAAE,EAAE;MAAC,OAAMoD,MAAM,CAACiF,WAAW,CAACD,CAAC,CAAC,GAAChF,MAAM,CAACiF,WAAW,CAACjF,MAAM,CAAC6E,GAAG,CAAC,IAAE,CAAC,EAAC;QAACG,CAAC,IAAGhF,MAAM,CAAC6E,GAAG,IAAG7E,MAAM,CAACiF,WAAW,CAACD,CAAC,CAAC,GAAChF,MAAM,CAACiF,WAAW,CAACjF,MAAM,CAAC6E,GAAG,CAAG;MAAC;MAC3uC,OAAM,CAAEjI,IAAI,IAAE,EAAE,GAAEoI,CAAC,IAAEhF,MAAM,CAAC+E,QAAQ;IAAC,CAAC;IAAC3D,gBAAgB,EAAC,SAAAA,iBAASxE,IAAI,EAAC;MAAC,IAAIoI,CAAC,GAACpI,IAAI,IAAE,EAAE;MAAC,OAAMoD,MAAM,CAACiF,WAAW,CAACD,CAAC,CAAC,GAAChF,MAAM,CAACiF,WAAW,CAACjF,MAAM,CAAC8E,GAAG,CAAC,IAAE,CAAC,EAAC;QAACE,CAAC,IAAGhF,MAAM,CAAC8E,GAAG,IAAG9E,MAAM,CAACiF,WAAW,CAACD,CAAC,CAAC,GAAChF,MAAM,CAACiF,WAAW,CAACjF,MAAM,CAAC8E,GAAG,CAAG;MAAC;MAC7N,OAAOlI,IAAI,IAAE,EAAE,GAAEoI,CAAC;IAAC,CAAC;IAACC,WAAW,EAAC,SAAAA,YAASrI,IAAI,EAAC;MAAC,IAAIsI,KAAK,GAAC,CAAC;MAAC,OAAMtI,IAAI,IAAE,CAAC,EAAC;QAACsI,KAAK,EAAE;QAACtI,IAAI,MAAI,CAAC;MAAC;MAC7F,OAAOsI,KAAK;IAAC,CAAC;IAACjE,kBAAkB,EAAC,SAAAA,mBAAS/C,UAAU,EAAC;MAAC,OAAO8B,MAAM,CAAC4E,sBAAsB,CAAC1G,UAAU,GAAC,CAAC,CAAC;IAAC,CAAC;IAAC2D,OAAO,EAAC,SAAAA,QAAS1C,WAAW,EAAClC,CAAC,EAACiE,CAAC,EAAC;MAAC,QAAO/B,WAAW;QAAE,KAAKgF,aAAa,CAACC,UAAU;UAAC,OAAM,CAACnH,CAAC,GAACiE,CAAC,IAAE,CAAC,IAAE,CAAC;QAAC,KAAKiD,aAAa,CAACE,UAAU;UAAC,OAAOpH,CAAC,GAAC,CAAC,IAAE,CAAC;QAAC,KAAKkH,aAAa,CAACG,UAAU;UAAC,OAAOpD,CAAC,GAAC,CAAC,IAAE,CAAC;QAAC,KAAKiD,aAAa,CAACI,UAAU;UAAC,OAAM,CAACtH,CAAC,GAACiE,CAAC,IAAE,CAAC,IAAE,CAAC;QAAC,KAAKiD,aAAa,CAACK,UAAU;UAAC,OAAM,CAAClD,IAAI,CAACC,KAAK,CAACtE,CAAC,GAAC,CAAC,CAAC,GAACqE,IAAI,CAACC,KAAK,CAACL,CAAC,GAAC,CAAC,CAAC,IAAE,CAAC,IAAE,CAAC;QAAC,KAAKiD,aAAa,CAACM,UAAU;UAAC,OAAOxH,CAAC,GAACiE,CAAC,GAAE,CAAC,GAAEjE,CAAC,GAACiE,CAAC,GAAE,CAAC,IAAE,CAAC;QAAC,KAAKiD,aAAa,CAACO,UAAU;UAAC,OAAM,CAAEzH,CAAC,GAACiE,CAAC,GAAE,CAAC,GAAEjE,CAAC,GAACiE,CAAC,GAAE,CAAC,IAAE,CAAC,IAAE,CAAC;QAAC,KAAKiD,aAAa,CAACQ,UAAU;UAAC,OAAM,CAAE1H,CAAC,GAACiE,CAAC,GAAE,CAAC,GAAC,CAACjE,CAAC,GAACiE,CAAC,IAAE,CAAC,IAAE,CAAC,IAAE,CAAC;QAAC;UAAQ,MAAM,IAAIrC,KAAK,CAAC,kBAAkB,GAACM,WAAW,CAAC;MAAC;IAAC,CAAC;IAACgE,yBAAyB,EAAC,SAAAA,0BAASgC,kBAAkB,EAAC;MAAC,IAAIC,CAAC,GAAC,IAAI/B,YAAY,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC;MAAC,KAAI,IAAIpG,CAAC,GAAC,CAAC,EAACA,CAAC,GAACkI,kBAAkB,EAAClI,CAAC,EAAE,EAAC;QAACmI,CAAC,GAACA,CAAC,CAACC,QAAQ,CAAC,IAAIhC,YAAY,CAAC,CAAC,CAAC,EAACiC,MAAM,CAACC,IAAI,CAACtI,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC;MAAC;MAC3zB,OAAOmI,CAAC;IAAC,CAAC;IAAChD,eAAe,EAAC,SAAAA,gBAASvF,IAAI,EAAC2I,IAAI,EAAC;MAAC,IAAG,CAAC,IAAEA,IAAI,IAAEA,IAAI,GAAC,EAAE,EAAC;QAAC,QAAO3I,IAAI;UAAE,KAAKC,MAAM,CAAC6G,WAAW;YAAC,OAAO,EAAE;UAAC,KAAK7G,MAAM,CAAC8G,cAAc;YAAC,OAAO,CAAC;UAAC,KAAK9G,MAAM,CAACC,cAAc;YAAC,OAAO,CAAC;UAAC,KAAKD,MAAM,CAAC+G,UAAU;YAAC,OAAO,CAAC;UAAC;YAAQ,MAAM,IAAIhF,KAAK,CAAC,OAAO,GAAChC,IAAI,CAAC;QAAC;MAAC,CAAC,MAAK,IAAG2I,IAAI,GAAC,EAAE,EAAC;QAAC,QAAO3I,IAAI;UAAE,KAAKC,MAAM,CAAC6G,WAAW;YAAC,OAAO,EAAE;UAAC,KAAK7G,MAAM,CAAC8G,cAAc;YAAC,OAAO,EAAE;UAAC,KAAK9G,MAAM,CAACC,cAAc;YAAC,OAAO,EAAE;UAAC,KAAKD,MAAM,CAAC+G,UAAU;YAAC,OAAO,EAAE;UAAC;YAAQ,MAAM,IAAIhF,KAAK,CAAC,OAAO,GAAChC,IAAI,CAAC;QAAC;MAAC,CAAC,MAAK,IAAG2I,IAAI,GAAC,EAAE,EAAC;QAAC,QAAO3I,IAAI;UAAE,KAAKC,MAAM,CAAC6G,WAAW;YAAC,OAAO,EAAE;UAAC,KAAK7G,MAAM,CAAC8G,cAAc;YAAC,OAAO,EAAE;UAAC,KAAK9G,MAAM,CAACC,cAAc;YAAC,OAAO,EAAE;UAAC,KAAKD,MAAM,CAAC+G,UAAU;YAAC,OAAO,EAAE;UAAC;YAAQ,MAAM,IAAIhF,KAAK,CAAC,OAAO,GAAChC,IAAI,CAAC;QAAC;MAAC,CAAC,MAAI;QAAC,MAAM,IAAIgC,KAAK,CAAC,OAAO,GAAC2G,IAAI,CAAC;MAAC;IAAC,CAAC;IAACvF,YAAY,EAAC,SAAAA,aAASwF,MAAM,EAAC;MAAC,IAAIpH,WAAW,GAACoH,MAAM,CAAC3G,cAAc,CAAC,CAAC;MAAC,IAAIiB,SAAS,GAAC,CAAC;MAAC,KAAI,IAAIpB,GAAG,GAAC,CAAC,EAACA,GAAG,GAACN,WAAW,EAACM,GAAG,EAAE,EAAC;QAAC,KAAI,IAAIC,GAAG,GAAC,CAAC,EAACA,GAAG,GAACP,WAAW,EAACO,GAAG,EAAE,EAAC;UAAC,IAAI8G,SAAS,GAAC,CAAC;UAAC,IAAI/E,IAAI,GAAC8E,MAAM,CAAC/G,MAAM,CAACC,GAAG,EAACC,GAAG,CAAC;UAAC,KAAI,IAAIe,CAAC,GAAC,CAAC,CAAC,EAACA,CAAC,IAAE,CAAC,EAACA,CAAC,EAAE,EAAC;YAAC,IAAGhB,GAAG,GAACgB,CAAC,GAAC,CAAC,IAAEtB,WAAW,IAAEM,GAAG,GAACgB,CAAC,EAAC;cAAC;YAAS;YAC/9B,KAAI,IAAIC,CAAC,GAAC,CAAC,CAAC,EAACA,CAAC,IAAE,CAAC,EAACA,CAAC,EAAE,EAAC;cAAC,IAAGhB,GAAG,GAACgB,CAAC,GAAC,CAAC,IAAEvB,WAAW,IAAEO,GAAG,GAACgB,CAAC,EAAC;gBAAC;cAAS;cAChE,IAAGD,CAAC,IAAE,CAAC,IAAEC,CAAC,IAAE,CAAC,EAAC;gBAAC;cAAS;cACxB,IAAGe,IAAI,IAAE8E,MAAM,CAAC/G,MAAM,CAACC,GAAG,GAACgB,CAAC,EAACf,GAAG,GAACgB,CAAC,CAAC,EAAC;gBAAC8F,SAAS,EAAE;cAAC;YAAC;UAAC;UACnD,IAAGA,SAAS,GAAC,CAAC,EAAC;YAAC3F,SAAS,IAAG,CAAC,GAAC2F,SAAS,GAAC,CAAE;UAAC;QAAC;MAAC;MAC7C,KAAI,IAAI/G,GAAG,GAAC,CAAC,EAACA,GAAG,GAACN,WAAW,GAAC,CAAC,EAACM,GAAG,EAAE,EAAC;QAAC,KAAI,IAAIC,GAAG,GAAC,CAAC,EAACA,GAAG,GAACP,WAAW,GAAC,CAAC,EAACO,GAAG,EAAE,EAAC;UAAC,IAAI+G,KAAK,GAAC,CAAC;UAAC,IAAGF,MAAM,CAAC/G,MAAM,CAACC,GAAG,EAACC,GAAG,CAAC,EAAC+G,KAAK,EAAE;UAAC,IAAGF,MAAM,CAAC/G,MAAM,CAACC,GAAG,GAAC,CAAC,EAACC,GAAG,CAAC,EAAC+G,KAAK,EAAE;UAAC,IAAGF,MAAM,CAAC/G,MAAM,CAACC,GAAG,EAACC,GAAG,GAAC,CAAC,CAAC,EAAC+G,KAAK,EAAE;UAAC,IAAGF,MAAM,CAAC/G,MAAM,CAACC,GAAG,GAAC,CAAC,EAACC,GAAG,GAAC,CAAC,CAAC,EAAC+G,KAAK,EAAE;UAAC,IAAGA,KAAK,IAAE,CAAC,IAAEA,KAAK,IAAE,CAAC,EAAC;YAAC5F,SAAS,IAAE,CAAC;UAAC;QAAC;MAAC;MAChR,KAAI,IAAIpB,GAAG,GAAC,CAAC,EAACA,GAAG,GAACN,WAAW,EAACM,GAAG,EAAE,EAAC;QAAC,KAAI,IAAIC,GAAG,GAAC,CAAC,EAACA,GAAG,GAACP,WAAW,GAAC,CAAC,EAACO,GAAG,EAAE,EAAC;UAAC,IAAG6G,MAAM,CAAC/G,MAAM,CAACC,GAAG,EAACC,GAAG,CAAC,IAAE,CAAC6G,MAAM,CAAC/G,MAAM,CAACC,GAAG,EAACC,GAAG,GAAC,CAAC,CAAC,IAAE6G,MAAM,CAAC/G,MAAM,CAACC,GAAG,EAACC,GAAG,GAAC,CAAC,CAAC,IAAE6G,MAAM,CAAC/G,MAAM,CAACC,GAAG,EAACC,GAAG,GAAC,CAAC,CAAC,IAAE6G,MAAM,CAAC/G,MAAM,CAACC,GAAG,EAACC,GAAG,GAAC,CAAC,CAAC,IAAE,CAAC6G,MAAM,CAAC/G,MAAM,CAACC,GAAG,EAACC,GAAG,GAAC,CAAC,CAAC,IAAE6G,MAAM,CAAC/G,MAAM,CAACC,GAAG,EAACC,GAAG,GAAC,CAAC,CAAC,EAAC;YAACmB,SAAS,IAAE,EAAE;UAAC;QAAC;MAAC;MACrR,KAAI,IAAInB,GAAG,GAAC,CAAC,EAACA,GAAG,GAACP,WAAW,EAACO,GAAG,EAAE,EAAC;QAAC,KAAI,IAAID,GAAG,GAAC,CAAC,EAACA,GAAG,GAACN,WAAW,GAAC,CAAC,EAACM,GAAG,EAAE,EAAC;UAAC,IAAG8G,MAAM,CAAC/G,MAAM,CAACC,GAAG,EAACC,GAAG,CAAC,IAAE,CAAC6G,MAAM,CAAC/G,MAAM,CAACC,GAAG,GAAC,CAAC,EAACC,GAAG,CAAC,IAAE6G,MAAM,CAAC/G,MAAM,CAACC,GAAG,GAAC,CAAC,EAACC,GAAG,CAAC,IAAE6G,MAAM,CAAC/G,MAAM,CAACC,GAAG,GAAC,CAAC,EAACC,GAAG,CAAC,IAAE6G,MAAM,CAAC/G,MAAM,CAACC,GAAG,GAAC,CAAC,EAACC,GAAG,CAAC,IAAE,CAAC6G,MAAM,CAAC/G,MAAM,CAACC,GAAG,GAAC,CAAC,EAACC,GAAG,CAAC,IAAE6G,MAAM,CAAC/G,MAAM,CAACC,GAAG,GAAC,CAAC,EAACC,GAAG,CAAC,EAAC;YAACmB,SAAS,IAAE,EAAE;UAAC;QAAC;MAAC;MACrR,IAAI6F,SAAS,GAAC,CAAC;MAAC,KAAI,IAAIhH,GAAG,GAAC,CAAC,EAACA,GAAG,GAACP,WAAW,EAACO,GAAG,EAAE,EAAC;QAAC,KAAI,IAAID,GAAG,GAAC,CAAC,EAACA,GAAG,GAACN,WAAW,EAACM,GAAG,EAAE,EAAC;UAAC,IAAG8G,MAAM,CAAC/G,MAAM,CAACC,GAAG,EAACC,GAAG,CAAC,EAAC;YAACgH,SAAS,EAAE;UAAC;QAAC;MAAC;MACnI,IAAIC,KAAK,GAACvE,IAAI,CAACwE,GAAG,CAAC,GAAG,GAACF,SAAS,GAACvH,WAAW,GAACA,WAAW,GAAC,EAAE,CAAC,GAAC,CAAC;MAAC0B,SAAS,IAAE8F,KAAK,GAAC,EAAE;MAAC,OAAO9F,SAAS;IAAC;EAAC,CAAC;EAAC,IAAIuF,MAAM,GAAC;IAACS,IAAI,EAAC,SAAAA,KAASC,CAAC,EAAC;MAAC,IAAGA,CAAC,GAAC,CAAC,EAAC;QAAC,MAAM,IAAInH,KAAK,CAAC,OAAO,GAACmH,CAAC,GAAC,GAAG,CAAC;MAAC;MAC3K,OAAOV,MAAM,CAACW,SAAS,CAACD,CAAC,CAAC;IAAC,CAAC;IAACT,IAAI,EAAC,SAAAA,KAASS,CAAC,EAAC;MAAC,OAAMA,CAAC,GAAC,CAAC,EAAC;QAACA,CAAC,IAAE,GAAG;MAAC;MAChE,OAAMA,CAAC,IAAE,GAAG,EAAC;QAACA,CAAC,IAAE,GAAG;MAAC;MACrB,OAAOV,MAAM,CAACY,SAAS,CAACF,CAAC,CAAC;IAAC,CAAC;IAACE,SAAS,EAAC,IAAI1I,KAAK,CAAC,GAAG,CAAC;IAACyI,SAAS,EAAC,IAAIzI,KAAK,CAAC,GAAG;EAAC,CAAC;EAAC,KAAI,IAAIP,CAAC,GAAC,CAAC,EAACA,CAAC,GAAC,CAAC,EAACA,CAAC,EAAE,EAAC;IAACqI,MAAM,CAACY,SAAS,CAACjJ,CAAC,CAAC,GAAC,CAAC,IAAEA,CAAC;EAAC;EAC9H,KAAI,IAAIA,CAAC,GAAC,CAAC,EAACA,CAAC,GAAC,GAAG,EAACA,CAAC,EAAE,EAAC;IAACqI,MAAM,CAACY,SAAS,CAACjJ,CAAC,CAAC,GAACqI,MAAM,CAACY,SAAS,CAACjJ,CAAC,GAAC,CAAC,CAAC,GAACqI,MAAM,CAACY,SAAS,CAACjJ,CAAC,GAAC,CAAC,CAAC,GAACqI,MAAM,CAACY,SAAS,CAACjJ,CAAC,GAAC,CAAC,CAAC,GAACqI,MAAM,CAACY,SAAS,CAACjJ,CAAC,GAAC,CAAC,CAAC;EAAC;EACnI,KAAI,IAAIA,CAAC,GAAC,CAAC,EAACA,CAAC,GAAC,GAAG,EAACA,CAAC,EAAE,EAAC;IAACqI,MAAM,CAACW,SAAS,CAACX,MAAM,CAACY,SAAS,CAACjJ,CAAC,CAAC,CAAC,GAACA,CAAC;EAAC;EAC/D,SAASoG,YAAYA,CAAC8C,GAAG,EAACC,KAAK,EAAC;IAAC,IAAGD,GAAG,CAAChJ,MAAM,IAAEkJ,SAAS,EAAC;MAAC,MAAM,IAAIxH,KAAK,CAACsH,GAAG,CAAChJ,MAAM,GAAC,GAAG,GAACiJ,KAAK,CAAC;IAAC;IACjG,IAAI3D,MAAM,GAAC,CAAC;IAAC,OAAMA,MAAM,GAAC0D,GAAG,CAAChJ,MAAM,IAAEgJ,GAAG,CAAC1D,MAAM,CAAC,IAAE,CAAC,EAAC;MAACA,MAAM,EAAE;IAAC;IAC/D,IAAI,CAAC0D,GAAG,GAAC,IAAI3I,KAAK,CAAC2I,GAAG,CAAChJ,MAAM,GAACsF,MAAM,GAAC2D,KAAK,CAAC;IAAC,KAAI,IAAInJ,CAAC,GAAC,CAAC,EAACA,CAAC,GAACkJ,GAAG,CAAChJ,MAAM,GAACsF,MAAM,EAACxF,CAAC,EAAE,EAAC;MAAC,IAAI,CAACkJ,GAAG,CAAClJ,CAAC,CAAC,GAACkJ,GAAG,CAAClJ,CAAC,GAACwF,MAAM,CAAC;IAAC;EAAC;EAC5GY,YAAY,CAAC5F,SAAS,GAAC;IAAC+F,GAAG,EAAC,SAAAA,IAASE,KAAK,EAAC;MAAC,OAAO,IAAI,CAACyC,GAAG,CAACzC,KAAK,CAAC;IAAC,CAAC;IAAC7F,SAAS,EAAC,SAAAA,UAAA,EAAU;MAAC,OAAO,IAAI,CAACsI,GAAG,CAAChJ,MAAM;IAAC,CAAC;IAACkI,QAAQ,EAAC,SAAAA,SAASiB,CAAC,EAAC;MAAC,IAAIH,GAAG,GAAC,IAAI3I,KAAK,CAAC,IAAI,CAACK,SAAS,CAAC,CAAC,GAACyI,CAAC,CAACzI,SAAS,CAAC,CAAC,GAAC,CAAC,CAAC;MAAC,KAAI,IAAIZ,CAAC,GAAC,CAAC,EAACA,CAAC,GAAC,IAAI,CAACY,SAAS,CAAC,CAAC,EAACZ,CAAC,EAAE,EAAC;QAAC,KAAI,IAAIiE,CAAC,GAAC,CAAC,EAACA,CAAC,GAACoF,CAAC,CAACzI,SAAS,CAAC,CAAC,EAACqD,CAAC,EAAE,EAAC;UAACiF,GAAG,CAAClJ,CAAC,GAACiE,CAAC,CAAC,IAAEoE,MAAM,CAACC,IAAI,CAACD,MAAM,CAACS,IAAI,CAAC,IAAI,CAACvC,GAAG,CAACvG,CAAC,CAAC,CAAC,GAACqI,MAAM,CAACS,IAAI,CAACO,CAAC,CAAC9C,GAAG,CAACtC,CAAC,CAAC,CAAC,CAAC;QAAC;MAAC;MACxU,OAAO,IAAImC,YAAY,CAAC8C,GAAG,EAAC,CAAC,CAAC;IAAC,CAAC;IAAC9E,GAAG,EAAC,SAAAA,IAASiF,CAAC,EAAC;MAAC,IAAG,IAAI,CAACzI,SAAS,CAAC,CAAC,GAACyI,CAAC,CAACzI,SAAS,CAAC,CAAC,GAAC,CAAC,EAAC;QAAC,OAAO,IAAI;MAAC;MAClG,IAAIgI,KAAK,GAACP,MAAM,CAACS,IAAI,CAAC,IAAI,CAACvC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAC8B,MAAM,CAACS,IAAI,CAACO,CAAC,CAAC9C,GAAG,CAAC,CAAC,CAAC,CAAC;MAAC,IAAI2C,GAAG,GAAC,IAAI3I,KAAK,CAAC,IAAI,CAACK,SAAS,CAAC,CAAC,CAAC;MAAC,KAAI,IAAIZ,CAAC,GAAC,CAAC,EAACA,CAAC,GAAC,IAAI,CAACY,SAAS,CAAC,CAAC,EAACZ,CAAC,EAAE,EAAC;QAACkJ,GAAG,CAAClJ,CAAC,CAAC,GAAC,IAAI,CAACuG,GAAG,CAACvG,CAAC,CAAC;MAAC;MACpJ,KAAI,IAAIA,CAAC,GAAC,CAAC,EAACA,CAAC,GAACqJ,CAAC,CAACzI,SAAS,CAAC,CAAC,EAACZ,CAAC,EAAE,EAAC;QAACkJ,GAAG,CAAClJ,CAAC,CAAC,IAAEqI,MAAM,CAACC,IAAI,CAACD,MAAM,CAACS,IAAI,CAACO,CAAC,CAAC9C,GAAG,CAACvG,CAAC,CAAC,CAAC,GAAC4I,KAAK,CAAC;MAAC;MAClF,OAAO,IAAIxC,YAAY,CAAC8C,GAAG,EAAC,CAAC,CAAC,CAAC9E,GAAG,CAACiF,CAAC,CAAC;IAAC;EAAC,CAAC;EAAC,SAASrE,SAASA,CAACe,UAAU,EAACV,SAAS,EAAC;IAAC,IAAI,CAACU,UAAU,GAACA,UAAU;IAAC,IAAI,CAACV,SAAS,GAACA,SAAS;EAAC;EACtIL,SAAS,CAACsE,cAAc,GAAC,CAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,GAAG,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,GAAG,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,GAAG,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,GAAG,EAAC,EAAE,EAAC,CAAC,EAAC,GAAG,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,GAAG,EAAC,GAAG,EAAC,CAAC,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,GAAG,EAAC,EAAE,EAAC,CAAC,EAAC,GAAG,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,GAAG,EAAC,EAAE,EAAC,CAAC,EAAC,GAAG,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,GAAG,EAAC,GAAG,EAAC,CAAC,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,GAAG,EAAC,GAAG,EAAC,CAAC,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,GAAG,EAAC,GAAG,EAAC,CAAC,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,GAAG,EAAC,GAAG,EAAC,CAAC,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,GAAG,EAAC,GAAG,EAAC,CAAC,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,GAAG,EAAC,GAAG,EAAC,CAAC,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,GAAG,EAAC,GAAG,EAAC,CAAC,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,GAAG,EAAC,GAAG,EAAC,CAAC,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,GAAG,EAAC,GAAG,EAAC,CAAC,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,GAAG,EAAC,GAAG,EAAC,CAAC,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,GAAG,EAAC,GAAG,EAAC,CAAC,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,GAAG,EAAC,GAAG,EAAC,EAAE,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,GAAG,EAAC,GAAG,EAAC,CAAC,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,GAAG,EAAC,GAAG,EAAC,EAAE,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,GAAG,EAAC,GAAG,EAAC,CAAC,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,GAAG,EAAC,GAAG,EAAC,CAAC,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,GAAG,EAAC,GAAG,EAAC,CAAC,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,GAAG,EAAC,GAAG,EAAC,CAAC,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,GAAG,EAAC,GAAG,EAAC,EAAE,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,GAAG,EAAC,GAAG,EAAC,CAAC,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,GAAG,EAAC,GAAG,EAAC,EAAE,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,GAAG,EAAC,GAAG,EAAC,CAAC,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,GAAG,EAAC,GAAG,EAAC,CAAC,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,CAAC;EAACtE,SAAS,CAACC,WAAW,GAAC,UAAShE,UAAU,EAACC,iBAAiB,EAAC;IAAC,IAAIqI,OAAO,GAACvE,SAAS,CAACwE,eAAe,CAACvI,UAAU,EAACC,iBAAiB,CAAC;IAAC,IAAGqI,OAAO,IAAEH,SAAS,EAAC;MAAC,MAAM,IAAIxH,KAAK,CAAC,4BAA4B,GAACX,UAAU,GAAC,qBAAqB,GAACC,iBAAiB,CAAC;IAAC;IACvjG,IAAIhB,MAAM,GAACqJ,OAAO,CAACrJ,MAAM,GAAC,CAAC;IAAC,IAAIuJ,IAAI,GAAC,EAAE;IAAC,KAAI,IAAIzJ,CAAC,GAAC,CAAC,EAACA,CAAC,GAACE,MAAM,EAACF,CAAC,EAAE,EAAC;MAAC,IAAI0I,KAAK,GAACa,OAAO,CAACvJ,CAAC,GAAC,CAAC,GAAC,CAAC,CAAC;MAAC,IAAI+F,UAAU,GAACwD,OAAO,CAACvJ,CAAC,GAAC,CAAC,GAAC,CAAC,CAAC;MAAC,IAAIqF,SAAS,GAACkE,OAAO,CAACvJ,CAAC,GAAC,CAAC,GAAC,CAAC,CAAC;MAAC,KAAI,IAAIiE,CAAC,GAAC,CAAC,EAACA,CAAC,GAACyE,KAAK,EAACzE,CAAC,EAAE,EAAC;QAACwF,IAAI,CAACnJ,IAAI,CAAC,IAAI0E,SAAS,CAACe,UAAU,EAACV,SAAS,CAAC,CAAC;MAAC;IAAC;IAC/N,OAAOoE,IAAI;EAAC,CAAC;EAACzE,SAAS,CAACwE,eAAe,GAAC,UAASvI,UAAU,EAACC,iBAAiB,EAAC;IAAC,QAAOA,iBAAiB;MAAE,KAAK2F,mBAAmB,CAACC,CAAC;QAAC,OAAO9B,SAAS,CAACsE,cAAc,CAAC,CAACrI,UAAU,GAAC,CAAC,IAAE,CAAC,GAAC,CAAC,CAAC;MAAC,KAAK4F,mBAAmB,CAACE,CAAC;QAAC,OAAO/B,SAAS,CAACsE,cAAc,CAAC,CAACrI,UAAU,GAAC,CAAC,IAAE,CAAC,GAAC,CAAC,CAAC;MAAC,KAAK4F,mBAAmB,CAACG,CAAC;QAAC,OAAOhC,SAAS,CAACsE,cAAc,CAAC,CAACrI,UAAU,GAAC,CAAC,IAAE,CAAC,GAAC,CAAC,CAAC;MAAC,KAAK4F,mBAAmB,CAACI,CAAC;QAAC,OAAOjC,SAAS,CAACsE,cAAc,CAAC,CAACrI,UAAU,GAAC,CAAC,IAAE,CAAC,GAAC,CAAC,CAAC;MAAC;QAAQ,OAAOmI,SAAS;IAAC;EAAC,CAAC;EAAC,SAASlE,WAAWA,CAAA,EAAE;IAAC,IAAI,CAACrE,MAAM,GAAC,EAAE;IAAC,IAAI,CAACX,MAAM,GAAC,CAAC;EAAC;EACrfgF,WAAW,CAAC1E,SAAS,GAAC;IAAC+F,GAAG,EAAC,SAAAA,IAASE,KAAK,EAAC;MAAC,IAAIiD,QAAQ,GAACrF,IAAI,CAACC,KAAK,CAACmC,KAAK,GAAC,CAAC,CAAC;MAAC,OAAM,CAAE,IAAI,CAAC5F,MAAM,CAAC6I,QAAQ,CAAC,KAAI,CAAC,GAACjD,KAAK,GAAC,CAAE,GAAE,CAAC,KAAG,CAAC;IAAC,CAAC;IAAC1F,GAAG,EAAC,SAAAA,IAASmI,GAAG,EAAChJ,MAAM,EAAC;MAAC,KAAI,IAAIF,CAAC,GAAC,CAAC,EAACA,CAAC,GAACE,MAAM,EAACF,CAAC,EAAE,EAAC;QAAC,IAAI,CAACsF,MAAM,CAAC,CAAE4D,GAAG,KAAIhJ,MAAM,GAACF,CAAC,GAAC,CAAE,GAAE,CAAC,KAAG,CAAC,CAAC;MAAC;IAAC,CAAC;IAACmF,eAAe,EAAC,SAAAA,gBAAA,EAAU;MAAC,OAAO,IAAI,CAACjF,MAAM;IAAC,CAAC;IAACoF,MAAM,EAAC,SAAAA,OAASqE,GAAG,EAAC;MAAC,IAAID,QAAQ,GAACrF,IAAI,CAACC,KAAK,CAAC,IAAI,CAACpE,MAAM,GAAC,CAAC,CAAC;MAAC,IAAG,IAAI,CAACW,MAAM,CAACX,MAAM,IAAEwJ,QAAQ,EAAC;QAAC,IAAI,CAAC7I,MAAM,CAACP,IAAI,CAAC,CAAC,CAAC;MAAC;MACjY,IAAGqJ,GAAG,EAAC;QAAC,IAAI,CAAC9I,MAAM,CAAC6I,QAAQ,CAAC,IAAG,IAAI,KAAI,IAAI,CAACxJ,MAAM,GAAC,CAAG;MAAC;MACxD,IAAI,CAACA,MAAM,EAAE;IAAC;EAAC,CAAC;EAAC,IAAI0J,iBAAiB,GAAC,CAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,CAAC,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,GAAG,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,EAAE,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,EAAE,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,IAAI,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,IAAI,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,IAAI,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,IAAI,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,GAAG,CAAC,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,GAAG,CAAC,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,GAAG,CAAC,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,GAAG,CAAC,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,GAAG,CAAC,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,CAAC,CAAC;EAE/wB,SAASC,gBAAgBA,CAAA,EAAG;IAC3B,OAAO,OAAOC,wBAAwB,IAAI,WAAW;EACtD;;EAEA;EACA,SAASC,WAAWA,CAAA,EAAG;IACtB,IAAIC,OAAO,GAAG,KAAK;IACnB,IAAIC,MAAM,GAAGC,SAAS,CAACC,SAAS;IAEhC,IAAI,UAAU,CAAClI,IAAI,CAACgI,MAAM,CAAC,EAAE;MAAE;MAC9BD,OAAO,GAAG,IAAI;MACd,IAAII,IAAI,GAAGH,MAAM,CAACI,QAAQ,CAAC,CAAC,CAACC,KAAK,CAAC,yBAAyB,CAAC;MAE7D,IAAIF,IAAI,IAAIA,IAAI,CAAC,CAAC,CAAC,EAAE;QACpBJ,OAAO,GAAGO,UAAU,CAACH,IAAI,CAAC,CAAC,CAAC,CAAC;MAC9B;IACD;IAEA,OAAOJ,OAAO;EACf;EAEA,IAAIQ,SAAS,GAAI,YAAW;IAE3B,IAAIC,OAAO,GAAG,SAAVA,OAAOA,CAAaC,EAAE,EAAEC,QAAQ,EAAE;MACrC,IAAI,CAACC,GAAG,GAAGF,EAAE;MACb,IAAI,CAACG,SAAS,GAAGF,QAAQ;IAC1B,CAAC;IAEDF,OAAO,CAACjK,SAAS,CAACsK,IAAI,GAAG,UAAUC,OAAO,EAAE;MAC3C,IAAIF,SAAS,GAAG,IAAI,CAACA,SAAS;MAC9B,IAAID,GAAG,GAAG,IAAI,CAACA,GAAG;MAClB,IAAII,MAAM,GAAGD,OAAO,CAAClJ,cAAc,CAAC,CAAC;MACrC,IAAIoJ,MAAM,GAAG5G,IAAI,CAACC,KAAK,CAACuG,SAAS,CAACK,KAAK,GAAGF,MAAM,CAAC;MACjD,IAAIG,OAAO,GAAG9G,IAAI,CAACC,KAAK,CAACuG,SAAS,CAACO,MAAM,GAAGJ,MAAM,CAAC;MAEnD,IAAI,CAACK,KAAK,CAAC,CAAC;MAEZ,SAASC,OAAOA,CAACC,GAAG,EAAEC,KAAK,EAAE;QAC5B,IAAId,EAAE,GAAGe,QAAQ,CAACC,eAAe,CAAC,4BAA4B,EAAEH,GAAG,CAAC;QACpE,KAAK,IAAII,CAAC,IAAIH,KAAK,EAClB,IAAIA,KAAK,CAACI,cAAc,CAACD,CAAC,CAAC,EAAEjB,EAAE,CAACmB,YAAY,CAACF,CAAC,EAAEH,KAAK,CAACG,CAAC,CAAC,CAAC;QAC1D,OAAOjB,EAAE;MACV;MAEA,IAAIoB,GAAG,GAAGR,OAAO,CAAC,KAAK,EAAG;QAAC,SAAS,EAAE,MAAM,GAAGS,MAAM,CAACf,MAAM,CAAC,GAAG,GAAG,GAAGe,MAAM,CAACf,MAAM,CAAC;QAAE,OAAO,EAAE,MAAM;QAAE,QAAQ,EAAE,MAAM;QAAE,MAAM,EAAEH,SAAS,CAACmB;MAAU,CAAC,CAAC;MACvJF,GAAG,CAACG,cAAc,CAAC,+BAA+B,EAAE,aAAa,EAAE,8BAA8B,CAAC;MAClGrB,GAAG,CAACsB,WAAW,CAACJ,GAAG,CAAC;MAEpBA,GAAG,CAACI,WAAW,CAACZ,OAAO,CAAC,MAAM,EAAE;QAAC,MAAM,EAAET,SAAS,CAACmB,UAAU;QAAE,OAAO,EAAE,MAAM;QAAE,QAAQ,EAAE;MAAM,CAAC,CAAC,CAAC;MACnGF,GAAG,CAACI,WAAW,CAACZ,OAAO,CAAC,MAAM,EAAE;QAAC,MAAM,EAAET,SAAS,CAACsB,SAAS;QAAE,OAAO,EAAE,GAAG;QAAE,QAAQ,EAAE,GAAG;QAAE,IAAI,EAAE;MAAU,CAAC,CAAC,CAAC;MAE9G,KAAK,IAAIzK,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGsJ,MAAM,EAAEtJ,GAAG,EAAE,EAAE;QACtC,KAAK,IAAIC,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGqJ,MAAM,EAAErJ,GAAG,EAAE,EAAE;UACtC,IAAIoJ,OAAO,CAACtJ,MAAM,CAACC,GAAG,EAAEC,GAAG,CAAC,EAAE;YAC7B,IAAIyK,KAAK,GAAGd,OAAO,CAAC,KAAK,EAAE;cAAC,GAAG,EAAES,MAAM,CAACpK,GAAG,CAAC;cAAE,GAAG,EAAEoK,MAAM,CAACrK,GAAG;YAAC,CAAC,CAAC;YAChE0K,KAAK,CAACH,cAAc,CAAC,8BAA8B,EAAE,MAAM,EAAE,WAAW,CAAC;YACzEH,GAAG,CAACI,WAAW,CAACE,KAAK,CAAC;UACvB;QACD;MACD;IACD,CAAC;IACD3B,OAAO,CAACjK,SAAS,CAAC6K,KAAK,GAAG,YAAY;MACrC,OAAO,IAAI,CAACT,GAAG,CAACyB,aAAa,CAAC,CAAC,EAC9B,IAAI,CAACzB,GAAG,CAAC0B,WAAW,CAAC,IAAI,CAAC1B,GAAG,CAAC2B,SAAS,CAAC;IAC1C,CAAC;IACD,OAAO9B,OAAO;EACf,CAAC,CAAE,CAAC;EAEJ,IAAI+B,MAAM,GAAGf,QAAQ,CAACgB,eAAe,CAACC,OAAO,CAACC,WAAW,CAAC,CAAC,KAAK,KAAK;;EAErE;EACA,IAAIlC,OAAO,GAAG+B,MAAM,GAAGhC,SAAS,GAAG,CAACX,gBAAgB,CAAC,CAAC,GAAI,YAAY;IACrE,IAAIY,OAAO,GAAG,SAAVA,OAAOA,CAAaC,EAAE,EAAEC,QAAQ,EAAE;MACrC,IAAI,CAACC,GAAG,GAAGF,EAAE;MACb,IAAI,CAACG,SAAS,GAAGF,QAAQ;IAC1B,CAAC;;IAED;AACF;AACA;AACA;AACA;IACEF,OAAO,CAACjK,SAAS,CAACsK,IAAI,GAAG,UAAUC,OAAO,EAAE;MAClC,IAAIF,SAAS,GAAG,IAAI,CAACA,SAAS;MAC9B,IAAID,GAAG,GAAG,IAAI,CAACA,GAAG;MAC3B,IAAII,MAAM,GAAGD,OAAO,CAAClJ,cAAc,CAAC,CAAC;MACrC,IAAIoJ,MAAM,GAAG5G,IAAI,CAACC,KAAK,CAACuG,SAAS,CAACK,KAAK,GAAGF,MAAM,CAAC;MACjD,IAAIG,OAAO,GAAG9G,IAAI,CAACC,KAAK,CAACuG,SAAS,CAACO,MAAM,GAAGJ,MAAM,CAAC;MACnD,IAAI4B,KAAK,GAAG,CAAC,oDAAoD,CAAC;MAElE,KAAK,IAAIlL,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGsJ,MAAM,EAAEtJ,GAAG,EAAE,EAAE;QACtCkL,KAAK,CAACtM,IAAI,CAAC,MAAM,CAAC;QAElB,KAAK,IAAIqB,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGqJ,MAAM,EAAErJ,GAAG,EAAE,EAAE;UACtCiL,KAAK,CAACtM,IAAI,CAAC,wEAAwE,GAAG2K,MAAM,GAAG,YAAY,GAAGE,OAAO,GAAG,sBAAsB,IAAIJ,OAAO,CAACtJ,MAAM,CAACC,GAAG,EAAEC,GAAG,CAAC,GAAGkJ,SAAS,CAACsB,SAAS,GAAGtB,SAAS,CAACmB,UAAU,CAAC,GAAG,UAAU,CAAC;QACvO;QAEAY,KAAK,CAACtM,IAAI,CAAC,OAAO,CAAC;MACpB;MAEAsM,KAAK,CAACtM,IAAI,CAAC,UAAU,CAAC;MACtBsK,GAAG,CAACiC,SAAS,GAAGD,KAAK,CAACE,IAAI,CAAC,EAAE,CAAC;;MAE9B;MACA,IAAIC,OAAO,GAAGnC,GAAG,CAACoC,UAAU,CAAC,CAAC,CAAC;MAC/B,IAAIC,gBAAgB,GAAG,CAACpC,SAAS,CAACK,KAAK,GAAG6B,OAAO,CAACG,WAAW,IAAI,CAAC;MAClE,IAAIC,eAAe,GAAG,CAACtC,SAAS,CAACO,MAAM,GAAG2B,OAAO,CAACK,YAAY,IAAI,CAAC;MAEnE,IAAIH,gBAAgB,GAAG,CAAC,IAAIE,eAAe,GAAG,CAAC,EAAE;QAChDJ,OAAO,CAACM,KAAK,CAACC,MAAM,GAAGH,eAAe,GAAG,KAAK,GAAGF,gBAAgB,GAAG,IAAI;MACzE;IACD,CAAC;;IAED;AACF;AACA;IACExC,OAAO,CAACjK,SAAS,CAAC6K,KAAK,GAAG,YAAY;MACrC,IAAI,CAACT,GAAG,CAACiC,SAAS,GAAG,EAAE;IACxB,CAAC;IAED,OAAOpC,OAAO;EACf,CAAC,CAAE,CAAC,GAAI,YAAY;IAAE;IACrB,SAAS8C,YAAYA,CAAA,EAAG;MACvB,IAAI,CAACC,QAAQ,CAACC,GAAG,GAAG,IAAI,CAACC,SAAS,CAACC,SAAS,CAAC,WAAW,CAAC;MACzD,IAAI,CAACH,QAAQ,CAACH,KAAK,CAACO,OAAO,GAAG,OAAO;MACrC,IAAI,CAACF,SAAS,CAACL,KAAK,CAACO,OAAO,GAAG,MAAM;IACtC;;IAEA;IACA;IACA,IAAI,IAAI,CAACC,QAAQ,IAAI,IAAI,CAACA,QAAQ,IAAI,GAAG,EAAE;MACvC,IAAIC,MAAM,GAAG,CAAC,GAAGC,MAAM,CAACC,gBAAgB;MACrC,IAAIC,SAAS,GAAGnE,wBAAwB,CAACtJ,SAAS,CAACyN,SAAS;MAC/DnE,wBAAwB,CAACtJ,SAAS,CAACyN,SAAS,GAAG,UAAUC,KAAK,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE;QAC/F,IAAK,UAAU,IAAIR,KAAK,IAAK,MAAM,CAACjM,IAAI,CAACiM,KAAK,CAACS,QAAQ,CAAC,EAAE;UACtD,KAAK,IAAI3O,CAAC,GAAG4O,SAAS,CAAC1O,MAAM,GAAG,CAAC,EAAEF,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;YAC5C4O,SAAS,CAAC5O,CAAC,CAAC,GAAG4O,SAAS,CAAC5O,CAAC,CAAC,GAAG8N,MAAM;UACxC;QACJ,CAAC,MAAM,IAAI,OAAOW,EAAE,IAAI,WAAW,EAAE;UACpCG,SAAS,CAAC,CAAC,CAAC,IAAId,MAAM;UACtBc,SAAS,CAAC,CAAC,CAAC,IAAId,MAAM;UACtBc,SAAS,CAAC,CAAC,CAAC,IAAId,MAAM;UACtBc,SAAS,CAAC,CAAC,CAAC,IAAId,MAAM;QACvB;QAEGG,SAAS,CAACvN,KAAK,CAAC,IAAI,EAAEkO,SAAS,CAAC;MACpC,CAAC;IACL;;IAEA;AACF;AACA;AACA;AACA;AACA;AACA;IACE,SAASC,eAAeA,CAACC,QAAQ,EAAEC,KAAK,EAAE;MAChC,IAAIC,IAAI,GAAG,IAAI;MACfA,IAAI,CAACC,MAAM,GAAGF,KAAK;MACnBC,IAAI,CAACE,SAAS,GAAGJ,QAAQ;;MAEzB;MACA,IAAIE,IAAI,CAACG,gBAAgB,KAAK,IAAI,EAAE;QAChC,IAAIzE,EAAE,GAAGe,QAAQ,CAAC2D,aAAa,CAAC,KAAK,CAAC;QACtC,IAAIC,QAAQ,GAAG,SAAXA,QAAQA,CAAA,EAAc;UACtBL,IAAI,CAACG,gBAAgB,GAAG,KAAK;UAE7B,IAAIH,IAAI,CAACC,MAAM,EAAE;YACbD,IAAI,CAACC,MAAM,CAACK,IAAI,CAACN,IAAI,CAAC;UAC1B;QACJ,CAAC;QACD,IAAIO,UAAU,GAAG,SAAbA,UAAUA,CAAA,EAAc;UACxBP,IAAI,CAACG,gBAAgB,GAAG,IAAI;UAE5B,IAAIH,IAAI,CAACE,SAAS,EAAE;YAChBF,IAAI,CAACE,SAAS,CAACI,IAAI,CAACN,IAAI,CAAC;UAC7B;QACJ,CAAC;QAEDtE,EAAE,CAAC8E,OAAO,GAAGH,QAAQ;QACrB3E,EAAE,CAAC+E,OAAO,GAAGJ,QAAQ;QACrB3E,EAAE,CAACgF,MAAM,GAAGH,UAAU;QACtB7E,EAAE,CAAC+C,GAAG,GAAG,4IAA4I,CAAC,CAAC;QACvJ;MACJ,CAAC,MAAM,IAAIuB,IAAI,CAACG,gBAAgB,KAAK,IAAI,IAAIH,IAAI,CAACE,SAAS,EAAE;QACzDF,IAAI,CAACE,SAAS,CAACI,IAAI,CAACN,IAAI,CAAC;MAC7B,CAAC,MAAM,IAAIA,IAAI,CAACG,gBAAgB,KAAK,KAAK,IAAIH,IAAI,CAACC,MAAM,EAAE;QACvDD,IAAI,CAACC,MAAM,CAACK,IAAI,CAACN,IAAI,CAAC;MAC1B;IACV;IAAC;;IAED;AACF;AACA;AACA;AACA;AACA;AACA;IACE,IAAIvE,OAAO,GAAG,SAAVA,OAAOA,CAAaC,EAAE,EAAEC,QAAQ,EAAE;MAClC,IAAI,CAACgF,WAAW,GAAG,KAAK;MACxB,IAAI,CAAC9B,QAAQ,GAAG9D,WAAW,CAAC,CAAC;MAEhC,IAAI,CAACc,SAAS,GAAGF,QAAQ;MACzB,IAAI,CAAC+C,SAAS,GAAGjC,QAAQ,CAAC2D,aAAa,CAAC,QAAQ,CAAC;MACjD,IAAI,CAAC1B,SAAS,CAACxC,KAAK,GAAGP,QAAQ,CAACO,KAAK;MACrC,IAAI,CAACwC,SAAS,CAACtC,MAAM,GAAGT,QAAQ,CAACS,MAAM;MACvCV,EAAE,CAACwB,WAAW,CAAC,IAAI,CAACwB,SAAS,CAAC;MAC9B,IAAI,CAAC9C,GAAG,GAAGF,EAAE;MACb,IAAI,CAACkF,SAAS,GAAG,IAAI,CAAClC,SAAS,CAACmC,UAAU,CAAC,IAAI,CAAC;MAChD,IAAI,CAACF,WAAW,GAAG,KAAK;MACxB,IAAI,CAACnC,QAAQ,GAAG/B,QAAQ,CAAC2D,aAAa,CAAC,KAAK,CAAC;MAC7C,IAAI,CAAC5B,QAAQ,CAACsC,GAAG,GAAG,UAAU;MAC9B,IAAI,CAACtC,QAAQ,CAACH,KAAK,CAACO,OAAO,GAAG,MAAM;MACpC,IAAI,CAAChD,GAAG,CAACsB,WAAW,CAAC,IAAI,CAACsB,QAAQ,CAAC;MACnC,IAAI,CAAC2B,gBAAgB,GAAG,IAAI;IAC7B,CAAC;;IAED;AACF;AACA;AACA;AACA;IACE1E,OAAO,CAACjK,SAAS,CAACsK,IAAI,GAAG,UAAUC,OAAO,EAAE;MAClC,IAAIyC,QAAQ,GAAG,IAAI,CAACA,QAAQ;MAC5B,IAAIoC,SAAS,GAAG,IAAI,CAACA,SAAS;MAC9B,IAAI/E,SAAS,GAAG,IAAI,CAACA,SAAS;MAEvC,IAAIG,MAAM,GAAGD,OAAO,CAAClJ,cAAc,CAAC,CAAC;MACrC,IAAIoJ,MAAM,GAAGJ,SAAS,CAACK,KAAK,GAAGF,MAAM;MACrC,IAAIG,OAAO,GAAGN,SAAS,CAACO,MAAM,GAAGJ,MAAM;MACvC,IAAI+E,aAAa,GAAG1L,IAAI,CAAC2L,KAAK,CAAC/E,MAAM,CAAC;MACtC,IAAIgF,cAAc,GAAG5L,IAAI,CAAC2L,KAAK,CAAC7E,OAAO,CAAC;MAExCqC,QAAQ,CAACH,KAAK,CAACO,OAAO,GAAG,MAAM;MAC/B,IAAI,CAACvC,KAAK,CAAC,CAAC;MAEZ,KAAK,IAAI3J,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGsJ,MAAM,EAAEtJ,GAAG,EAAE,EAAE;QACtC,KAAK,IAAIC,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGqJ,MAAM,EAAErJ,GAAG,EAAE,EAAE;UACtC,IAAIuO,OAAO,GAAGnF,OAAO,CAACtJ,MAAM,CAACC,GAAG,EAAEC,GAAG,CAAC;UACtC,IAAIwO,KAAK,GAAGxO,GAAG,GAAGsJ,MAAM;UACxB,IAAImF,IAAI,GAAG1O,GAAG,GAAGyJ,OAAO;UACxByE,SAAS,CAACS,WAAW,GAAGH,OAAO,GAAGrF,SAAS,CAACsB,SAAS,GAAGtB,SAAS,CAACmB,UAAU;UAC5E4D,SAAS,CAACU,SAAS,GAAG,CAAC;UACvBV,SAAS,CAACW,SAAS,GAAGL,OAAO,GAAGrF,SAAS,CAACsB,SAAS,GAAGtB,SAAS,CAACmB,UAAU;UAC1E4D,SAAS,CAACY,QAAQ,CAACL,KAAK,EAAEC,IAAI,EAAEnF,MAAM,EAAEE,OAAO,CAAC;;UAEhD;UACAyE,SAAS,CAACa,UAAU,CACnBpM,IAAI,CAACC,KAAK,CAAC6L,KAAK,CAAC,GAAG,GAAG,EACvB9L,IAAI,CAACC,KAAK,CAAC8L,IAAI,CAAC,GAAG,GAAG,EACtBL,aAAa,EACbE,cACD,CAAC;UAEDL,SAAS,CAACa,UAAU,CACnBpM,IAAI,CAACqM,IAAI,CAACP,KAAK,CAAC,GAAG,GAAG,EACtB9L,IAAI,CAACqM,IAAI,CAACN,IAAI,CAAC,GAAG,GAAG,EACrBL,aAAa,EACbE,cACD,CAAC;QACF;MACD;MAEA,IAAI,CAACN,WAAW,GAAG,IAAI;IACxB,CAAC;;IAED;AACF;AACA;IACElF,OAAO,CAACjK,SAAS,CAACmQ,SAAS,GAAG,YAAY;MACzC,IAAI,IAAI,CAAChB,WAAW,EAAE;QACrBd,eAAe,CAACS,IAAI,CAAC,IAAI,EAAE/B,YAAY,CAAC;MACzC;IACD,CAAC;;IAED;AACF;AACA;AACA;AACA;IACE9C,OAAO,CAACjK,SAAS,CAACoQ,SAAS,GAAG,YAAY;MACzC,OAAO,IAAI,CAACjB,WAAW;IACxB,CAAC;;IAED;AACF;AACA;IACElF,OAAO,CAACjK,SAAS,CAAC6K,KAAK,GAAG,YAAY;MACrC,IAAI,CAACuE,SAAS,CAACiB,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAACnD,SAAS,CAACxC,KAAK,EAAE,IAAI,CAACwC,SAAS,CAACtC,MAAM,CAAC;MAC3E,IAAI,CAACuE,WAAW,GAAG,KAAK;IACzB,CAAC;;IAED;AACF;AACA;AACA;IACElF,OAAO,CAACjK,SAAS,CAACwP,KAAK,GAAG,UAAUc,OAAO,EAAE;MAC5C,IAAI,CAACA,OAAO,EAAE;QACb,OAAOA,OAAO;MACf;MAEA,OAAOzM,IAAI,CAACC,KAAK,CAACwM,OAAO,GAAG,IAAI,CAAC,GAAG,IAAI;IACzC,CAAC;IAED,OAAOrG,OAAO;EACf,CAAC,CAAE,CAAC;;EAEJ;AACD;AACA;AACA;AACA;AACA;AACA;AACA;EACC,SAASsG,cAAcA,CAACC,KAAK,EAAEC,aAAa,EAAE;IAC7C,IAAIC,KAAK,GAAG,CAAC;IACb,IAAIhR,MAAM,GAAGiR,cAAc,CAACH,KAAK,CAAC;IAElC,KAAK,IAAIhR,CAAC,GAAG,CAAC,EAAEoR,GAAG,GAAGxH,iBAAiB,CAAC1J,MAAM,EAAEF,CAAC,IAAIoR,GAAG,EAAEpR,CAAC,EAAE,EAAE;MAC9D,IAAIqR,MAAM,GAAG,CAAC;MAEd,QAAQJ,aAAa;QACpB,KAAKpK,mBAAmB,CAACC,CAAC;UACzBuK,MAAM,GAAGzH,iBAAiB,CAAC5J,CAAC,CAAC,CAAC,CAAC,CAAC;UAChC;QACD,KAAK6G,mBAAmB,CAACE,CAAC;UACzBsK,MAAM,GAAGzH,iBAAiB,CAAC5J,CAAC,CAAC,CAAC,CAAC,CAAC;UAChC;QACD,KAAK6G,mBAAmB,CAACG,CAAC;UACzBqK,MAAM,GAAGzH,iBAAiB,CAAC5J,CAAC,CAAC,CAAC,CAAC,CAAC;UAChC;QACD,KAAK6G,mBAAmB,CAACI,CAAC;UACzBoK,MAAM,GAAGzH,iBAAiB,CAAC5J,CAAC,CAAC,CAAC,CAAC,CAAC;UAChC;MACF;MAEA,IAAIE,MAAM,IAAImR,MAAM,EAAE;QACrB;MACD,CAAC,MAAM;QACNH,KAAK,EAAE;MACR;IACD;IAEA,IAAIA,KAAK,GAAGtH,iBAAiB,CAAC1J,MAAM,EAAE;MACrC,MAAM,IAAI0B,KAAK,CAAC,eAAe,CAAC;IACjC;IAEA,OAAOsP,KAAK;EACb;EAEA,SAASC,cAAcA,CAACH,KAAK,EAAE;IAC9B,IAAIM,YAAY,GAAGC,SAAS,CAACP,KAAK,CAAC,CAAC3G,QAAQ,CAAC,CAAC,CAACmH,OAAO,CAAC,mBAAmB,EAAE,GAAG,CAAC;IAChF,OAAOF,YAAY,CAACpR,MAAM,IAAIoR,YAAY,CAACpR,MAAM,IAAI8Q,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC;EACpE;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACC9R,MAAM,GAAG,SAAAA,OAAUwL,EAAE,EAAE+G,OAAO,EAAE;IAC/B,IAAI,CAAC5G,SAAS,GAAG;MAChBK,KAAK,EAAG,GAAG;MACXE,MAAM,EAAG,GAAG;MACZnK,UAAU,EAAG,CAAC;MACdkL,SAAS,EAAG,SAAS;MACrBH,UAAU,EAAG,SAAS;MACtB0F,YAAY,EAAG7K,mBAAmB,CAACI;IACpC,CAAC;IAED,IAAI,OAAOwK,OAAO,KAAK,QAAQ,EAAE;MAChCA,OAAO,GAAG;QACTE,IAAI,EAAGF;MACR,CAAC;IACF;;IAEA;IACA,IAAIA,OAAO,EAAE;MACZ,KAAK,IAAIzR,CAAC,IAAIyR,OAAO,EAAE;QACtB,IAAI,CAAC5G,SAAS,CAAC7K,CAAC,CAAC,GAAGyR,OAAO,CAACzR,CAAC,CAAC;MAC/B;IACD;IAEA,IAAI,OAAO0K,EAAE,IAAI,QAAQ,EAAE;MAC1BA,EAAE,GAAGe,QAAQ,CAACmG,cAAc,CAAClH,EAAE,CAAC;IACjC;IAEA,IAAI,IAAI,CAACG,SAAS,CAAC2B,MAAM,EAAE;MAC1B/B,OAAO,GAAGD,SAAS;IACpB;IAEA,IAAI,CAACqD,QAAQ,GAAG9D,WAAW,CAAC,CAAC;IAC7B,IAAI,CAACa,GAAG,GAAGF,EAAE;IACb,IAAI,CAACmH,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACC,SAAS,GAAG,IAAIrH,OAAO,CAAC,IAAI,CAACG,GAAG,EAAE,IAAI,CAACC,SAAS,CAAC;IAEtD,IAAI,IAAI,CAACA,SAAS,CAAC8G,IAAI,EAAE;MACxB,IAAI,CAACI,QAAQ,CAAC,IAAI,CAAClH,SAAS,CAAC8G,IAAI,CAAC;IACnC;EACD,CAAC;;EAED;AACD;AACA;AACA;AACA;EACCzS,MAAM,CAACsB,SAAS,CAACuR,QAAQ,GAAG,UAAUf,KAAK,EAAE;IAC5C,IAAI,CAACa,QAAQ,GAAG,IAAI7Q,WAAW,CAAC+P,cAAc,CAACC,KAAK,EAAE,IAAI,CAACnG,SAAS,CAAC6G,YAAY,CAAC,EAAE,IAAI,CAAC7G,SAAS,CAAC6G,YAAY,CAAC;IAChH,IAAI,CAACG,QAAQ,CAACtQ,OAAO,CAACyP,KAAK,CAAC;IAC5B,IAAI,CAACa,QAAQ,CAAC/P,IAAI,CAAC,CAAC;IACpB,IAAI,CAAC8I,GAAG,CAACoH,KAAK,GAAGhB,KAAK;IACtB,IAAI,CAACc,SAAS,CAAChH,IAAI,CAAC,IAAI,CAAC+G,QAAQ,CAAC;IAClC,IAAI,CAAClB,SAAS,CAAC,CAAC;EACjB,CAAC;;EAED;AACD;AACA;AACA;AACA;AACA;AACA;EACCzR,MAAM,CAACsB,SAAS,CAACmQ,SAAS,GAAG,YAAY;IACxC,IAAI,OAAO,IAAI,CAACmB,SAAS,CAACnB,SAAS,IAAI,UAAU,KAAK,CAAC,IAAI,CAAC9C,QAAQ,IAAI,IAAI,CAACA,QAAQ,IAAI,CAAC,CAAC,EAAE;MAC5F,IAAI,CAACiE,SAAS,CAACnB,SAAS,CAAC,CAAC;IAC3B;EACD,CAAC;;EAED;AACD;AACA;EACCzR,MAAM,CAACsB,SAAS,CAAC6K,KAAK,GAAG,YAAY;IACpC,IAAI,CAACyG,SAAS,CAACzG,KAAK,CAAC,CAAC;EACvB,CAAC;;EAED;AACD;AACA;EACCnM,MAAM,CAAC+S,YAAY,GAAGpL,mBAAmB;EAEzC,OAAO3H,MAAM;AAEd,CAAC,CAAC"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}