{"name": "@types/serve-static", "version": "1.15.1", "description": "TypeScript definitions for serve-static", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/serve-static", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/urossmolnik", "githubUsername": "urossmolnik"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LinusU", "githubUsername": "LinusU"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/devanshj", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/serve-static"}, "scripts": {}, "dependencies": {"@types/mime": "*", "@types/node": "*"}, "typesPublisherContentHash": "a5245bfbae31bb734209d348df3855b5fae08859f07b3de412d264f25a0b0232", "typeScriptVersion": "4.2"}