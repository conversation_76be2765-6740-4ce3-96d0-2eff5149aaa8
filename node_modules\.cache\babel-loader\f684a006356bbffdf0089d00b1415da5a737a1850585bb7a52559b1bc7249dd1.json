{"ast": null, "code": "function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nfunction _iterableToArrayLimit(arr, i) { var _i = null == arr ? null : \"undefined\" != typeof Symbol && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (null != _i) { var _s, _e, _x, _r, _arr = [], _n = !0, _d = !1; try { if (_x = (_i = _i.call(arr)).next, 0 === i) { if (Object(_i) !== _i) return; _n = !1; } else for (; !(_n = (_s = _x.call(_i)).done) && (_arr.push(_s.value), _arr.length !== i); _n = !0); } catch (err) { _d = !0, _e = err; } finally { try { if (!_n && null != _i.return && (_r = _i.return(), Object(_r) !== _r)) return; } finally { if (_d) throw _e; } } return _arr; } }\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\nimport { createVNode as _createVNode } from \"vue\";\nimport { ref, watch, computed, nextTick, Teleport, onMounted, defineComponent } from \"vue\";\nimport { isDef, isHidden, truthProp, numericProp, getScrollTop, preventDefault, makeNumberProp, createNamespace, getRootScrollTop, setRootScrollTop } from \"../utils/index.mjs\";\nimport { useRect, useChildren, useScrollParent, useEventListener } from \"@vant/use\";\nimport { useTouch } from \"../composables/use-touch.mjs\";\nimport { useExpose } from \"../composables/use-expose.mjs\";\nfunction genAlphabet() {\n  var charCodeOfA = \"A\".charCodeAt(0);\n  var indexList = Array(26).fill(\"\").map(function (_, i) {\n    return String.fromCharCode(charCodeOfA + i);\n  });\n  return indexList;\n}\nvar _createNamespace = createNamespace(\"index-bar\"),\n  _createNamespace2 = _slicedToArray(_createNamespace, 2),\n  name = _createNamespace2[0],\n  bem = _createNamespace2[1];\nvar indexBarProps = {\n  sticky: truthProp,\n  zIndex: numericProp,\n  teleport: [String, Object],\n  highlightColor: String,\n  stickyOffsetTop: makeNumberProp(0),\n  indexList: {\n    type: Array,\n    default: genAlphabet\n  }\n};\nvar INDEX_BAR_KEY = Symbol(name);\nvar stdin_default = defineComponent({\n  name: name,\n  props: indexBarProps,\n  emits: [\"select\", \"change\"],\n  setup: function setup(props, _ref) {\n    var emit = _ref.emit,\n      slots = _ref.slots;\n    var root = ref();\n    var sidebar = ref();\n    var activeAnchor = ref(\"\");\n    var touch = useTouch();\n    var scrollParent = useScrollParent(root);\n    var _useChildren = useChildren(INDEX_BAR_KEY),\n      children = _useChildren.children,\n      linkChildren = _useChildren.linkChildren;\n    var selectActiveIndex;\n    linkChildren({\n      props: props\n    });\n    var sidebarStyle = computed(function () {\n      if (isDef(props.zIndex)) {\n        return {\n          zIndex: +props.zIndex + 1\n        };\n      }\n    });\n    var highlightStyle = computed(function () {\n      if (props.highlightColor) {\n        return {\n          color: props.highlightColor\n        };\n      }\n    });\n    var getActiveAnchor = function getActiveAnchor(scrollTop, rects) {\n      for (var i = children.length - 1; i >= 0; i--) {\n        var prevHeight = i > 0 ? rects[i - 1].height : 0;\n        var reachTop = props.sticky ? prevHeight + props.stickyOffsetTop : 0;\n        if (scrollTop + reachTop >= rects[i].top) {\n          return i;\n        }\n      }\n      return -1;\n    };\n    var getMatchAnchor = function getMatchAnchor(index) {\n      return children.find(function (item) {\n        return String(item.index) === index;\n      });\n    };\n    var onScroll = function onScroll() {\n      if (isHidden(root)) {\n        return;\n      }\n      var sticky = props.sticky,\n        indexList = props.indexList;\n      var scrollTop = getScrollTop(scrollParent.value);\n      var scrollParentRect = useRect(scrollParent);\n      var rects = children.map(function (item) {\n        return item.getRect(scrollParent.value, scrollParentRect);\n      });\n      var active = -1;\n      if (selectActiveIndex) {\n        var match = getMatchAnchor(selectActiveIndex);\n        if (match) {\n          var rect = match.getRect(scrollParent.value, scrollParentRect);\n          active = getActiveAnchor(rect.top, rects);\n        }\n      } else {\n        active = getActiveAnchor(scrollTop, rects);\n      }\n      activeAnchor.value = indexList[active];\n      if (sticky) {\n        children.forEach(function (item, index) {\n          var state = item.state,\n            $el = item.$el;\n          if (index === active || index === active - 1) {\n            var _rect = $el.getBoundingClientRect();\n            state.left = _rect.left;\n            state.width = _rect.width;\n          } else {\n            state.left = null;\n            state.width = null;\n          }\n          if (index === active) {\n            state.active = true;\n            state.top = Math.max(props.stickyOffsetTop, rects[index].top - scrollTop) + scrollParentRect.top;\n          } else if (index === active - 1 && selectActiveIndex === \"\") {\n            var activeItemTop = rects[active].top - scrollTop;\n            state.active = activeItemTop > 0;\n            state.top = activeItemTop + scrollParentRect.top - rects[index].height;\n          } else {\n            state.active = false;\n          }\n        });\n      }\n      selectActiveIndex = \"\";\n    };\n    var init = function init() {\n      nextTick(onScroll);\n    };\n    useEventListener(\"scroll\", onScroll, {\n      target: scrollParent,\n      passive: true\n    });\n    onMounted(init);\n    watch(function () {\n      return props.indexList;\n    }, init);\n    watch(activeAnchor, function (value) {\n      if (value) {\n        emit(\"change\", value);\n      }\n    });\n    var renderIndexes = function renderIndexes() {\n      return props.indexList.map(function (index) {\n        var active = index === activeAnchor.value;\n        return _createVNode(\"span\", {\n          \"class\": bem(\"index\", {\n            active: active\n          }),\n          \"style\": active ? highlightStyle.value : void 0,\n          \"data-index\": index\n        }, [index]);\n      });\n    };\n    var scrollTo = function scrollTo(index) {\n      selectActiveIndex = String(index);\n      var match = getMatchAnchor(selectActiveIndex);\n      if (match) {\n        var scrollTop = getScrollTop(scrollParent.value);\n        var scrollParentRect = useRect(scrollParent);\n        var offsetHeight = document.documentElement.offsetHeight;\n        match.$el.scrollIntoView();\n        if (scrollTop === offsetHeight - scrollParentRect.height) {\n          onScroll();\n          return;\n        }\n        if (props.sticky && props.stickyOffsetTop) {\n          setRootScrollTop(getRootScrollTop() - props.stickyOffsetTop);\n        }\n        emit(\"select\", match.index);\n      }\n    };\n    var scrollToElement = function scrollToElement(element) {\n      var index = element.dataset.index;\n      if (index) {\n        scrollTo(index);\n      }\n    };\n    var onClickSidebar = function onClickSidebar(event) {\n      scrollToElement(event.target);\n    };\n    var touchActiveIndex;\n    var onTouchMove = function onTouchMove(event) {\n      touch.move(event);\n      if (touch.isVertical()) {\n        preventDefault(event);\n        var _event$touches$ = event.touches[0],\n          clientX = _event$touches$.clientX,\n          clientY = _event$touches$.clientY;\n        var target = document.elementFromPoint(clientX, clientY);\n        if (target) {\n          var index = target.dataset.index;\n          if (index && touchActiveIndex !== index) {\n            touchActiveIndex = index;\n            scrollToElement(target);\n          }\n        }\n      }\n    };\n    var renderSidebar = function renderSidebar() {\n      return _createVNode(\"div\", {\n        \"ref\": sidebar,\n        \"class\": bem(\"sidebar\"),\n        \"style\": sidebarStyle.value,\n        \"onClick\": onClickSidebar,\n        \"onTouchstartPassive\": touch.start\n      }, [renderIndexes()]);\n    };\n    useExpose({\n      scrollTo: scrollTo\n    });\n    useEventListener(\"touchmove\", onTouchMove, {\n      target: sidebar\n    });\n    return function () {\n      var _a;\n      return _createVNode(\"div\", {\n        \"ref\": root,\n        \"class\": bem()\n      }, [props.teleport ? _createVNode(Teleport, {\n        \"to\": props.teleport\n      }, {\n        default: function _default() {\n          return [renderSidebar()];\n        }\n      }) : renderSidebar(), (_a = slots.default) == null ? void 0 : _a.call(slots)]);\n    };\n  }\n});\nexport { INDEX_BAR_KEY, stdin_default as default };", "map": {"version": 3, "names": ["createVNode", "_createVNode", "ref", "watch", "computed", "nextTick", "Teleport", "onMounted", "defineComponent", "isDef", "isHidden", "truthProp", "numericProp", "getScrollTop", "preventDefault", "makeNumberProp", "createNamespace", "getRootScrollTop", "setRootScrollTop", "useRect", "useChildren", "useScrollParent", "useEventListener", "useTouch", "useExpose", "gen<PERSON><PERSON><PERSON><PERSON>", "charCodeOfA", "charCodeAt", "indexList", "Array", "fill", "map", "_", "i", "String", "fromCharCode", "_createNamespace", "_createNamespace2", "_slicedToArray", "name", "bem", "indexBarProps", "sticky", "zIndex", "teleport", "Object", "highlightColor", "stickyOffsetTop", "type", "default", "INDEX_BAR_KEY", "Symbol", "stdin_default", "props", "emits", "setup", "_ref", "emit", "slots", "root", "sidebar", "activeAnchor", "touch", "scrollParent", "_useChildren", "children", "linkChildren", "selectActiveIndex", "sidebarStyle", "highlightStyle", "color", "getActiveAnchor", "scrollTop", "rects", "length", "prevHeight", "height", "reachTop", "top", "getMatchAnchor", "index", "find", "item", "onScroll", "value", "scrollParentRect", "getRect", "active", "match", "rect", "for<PERSON>ach", "state", "$el", "getBoundingClientRect", "left", "width", "Math", "max", "activeItemTop", "init", "target", "passive", "renderIndexes", "scrollTo", "offsetHeight", "document", "documentElement", "scrollIntoView", "scrollToElement", "element", "dataset", "onClickSidebar", "event", "touchActiveIndex", "onTouchMove", "move", "isVertical", "_event$touches$", "touches", "clientX", "clientY", "elementFromPoint", "renderSidebar", "start", "_a", "_default", "call"], "sources": ["C:/Users/<USER>/Downloads/Telegram Desktop/vue3_3.0/vue3_3.0/node_modules/vant/es/index-bar/IndexBar.mjs"], "sourcesContent": ["import { createVNode as _createVNode } from \"vue\";\nimport { ref, watch, computed, nextTick, Teleport, onMounted, defineComponent } from \"vue\";\nimport { isDef, isHidden, truthProp, numericProp, getScrollTop, preventDefault, makeNumberProp, createNamespace, getRootScrollTop, setRootScrollTop } from \"../utils/index.mjs\";\nimport { useRect, useChildren, useScrollParent, useEventListener } from \"@vant/use\";\nimport { useTouch } from \"../composables/use-touch.mjs\";\nimport { useExpose } from \"../composables/use-expose.mjs\";\nfunction genAlphabet() {\n  const charCodeOfA = \"A\".charCodeAt(0);\n  const indexList = Array(26).fill(\"\").map((_, i) => String.fromCharCode(charCodeOfA + i));\n  return indexList;\n}\nconst [name, bem] = createNamespace(\"index-bar\");\nconst indexBarProps = {\n  sticky: truthProp,\n  zIndex: numericProp,\n  teleport: [String, Object],\n  highlightColor: String,\n  stickyOffsetTop: makeNumberProp(0),\n  indexList: {\n    type: Array,\n    default: genAlphabet\n  }\n};\nconst INDEX_BAR_KEY = Symbol(name);\nvar stdin_default = defineComponent({\n  name,\n  props: indexBarProps,\n  emits: [\"select\", \"change\"],\n  setup(props, {\n    emit,\n    slots\n  }) {\n    const root = ref();\n    const sidebar = ref();\n    const activeAnchor = ref(\"\");\n    const touch = useTouch();\n    const scrollParent = useScrollParent(root);\n    const {\n      children,\n      linkChildren\n    } = useChildren(INDEX_BAR_KEY);\n    let selectActiveIndex;\n    linkChildren({\n      props\n    });\n    const sidebarStyle = computed(() => {\n      if (isDef(props.zIndex)) {\n        return {\n          zIndex: +props.zIndex + 1\n        };\n      }\n    });\n    const highlightStyle = computed(() => {\n      if (props.highlightColor) {\n        return {\n          color: props.highlightColor\n        };\n      }\n    });\n    const getActiveAnchor = (scrollTop, rects) => {\n      for (let i = children.length - 1; i >= 0; i--) {\n        const prevHeight = i > 0 ? rects[i - 1].height : 0;\n        const reachTop = props.sticky ? prevHeight + props.stickyOffsetTop : 0;\n        if (scrollTop + reachTop >= rects[i].top) {\n          return i;\n        }\n      }\n      return -1;\n    };\n    const getMatchAnchor = (index) => children.find((item) => String(item.index) === index);\n    const onScroll = () => {\n      if (isHidden(root)) {\n        return;\n      }\n      const {\n        sticky,\n        indexList\n      } = props;\n      const scrollTop = getScrollTop(scrollParent.value);\n      const scrollParentRect = useRect(scrollParent);\n      const rects = children.map((item) => item.getRect(scrollParent.value, scrollParentRect));\n      let active = -1;\n      if (selectActiveIndex) {\n        const match = getMatchAnchor(selectActiveIndex);\n        if (match) {\n          const rect = match.getRect(scrollParent.value, scrollParentRect);\n          active = getActiveAnchor(rect.top, rects);\n        }\n      } else {\n        active = getActiveAnchor(scrollTop, rects);\n      }\n      activeAnchor.value = indexList[active];\n      if (sticky) {\n        children.forEach((item, index) => {\n          const {\n            state,\n            $el\n          } = item;\n          if (index === active || index === active - 1) {\n            const rect = $el.getBoundingClientRect();\n            state.left = rect.left;\n            state.width = rect.width;\n          } else {\n            state.left = null;\n            state.width = null;\n          }\n          if (index === active) {\n            state.active = true;\n            state.top = Math.max(props.stickyOffsetTop, rects[index].top - scrollTop) + scrollParentRect.top;\n          } else if (index === active - 1 && selectActiveIndex === \"\") {\n            const activeItemTop = rects[active].top - scrollTop;\n            state.active = activeItemTop > 0;\n            state.top = activeItemTop + scrollParentRect.top - rects[index].height;\n          } else {\n            state.active = false;\n          }\n        });\n      }\n      selectActiveIndex = \"\";\n    };\n    const init = () => {\n      nextTick(onScroll);\n    };\n    useEventListener(\"scroll\", onScroll, {\n      target: scrollParent,\n      passive: true\n    });\n    onMounted(init);\n    watch(() => props.indexList, init);\n    watch(activeAnchor, (value) => {\n      if (value) {\n        emit(\"change\", value);\n      }\n    });\n    const renderIndexes = () => props.indexList.map((index) => {\n      const active = index === activeAnchor.value;\n      return _createVNode(\"span\", {\n        \"class\": bem(\"index\", {\n          active\n        }),\n        \"style\": active ? highlightStyle.value : void 0,\n        \"data-index\": index\n      }, [index]);\n    });\n    const scrollTo = (index) => {\n      selectActiveIndex = String(index);\n      const match = getMatchAnchor(selectActiveIndex);\n      if (match) {\n        const scrollTop = getScrollTop(scrollParent.value);\n        const scrollParentRect = useRect(scrollParent);\n        const {\n          offsetHeight\n        } = document.documentElement;\n        match.$el.scrollIntoView();\n        if (scrollTop === offsetHeight - scrollParentRect.height) {\n          onScroll();\n          return;\n        }\n        if (props.sticky && props.stickyOffsetTop) {\n          setRootScrollTop(getRootScrollTop() - props.stickyOffsetTop);\n        }\n        emit(\"select\", match.index);\n      }\n    };\n    const scrollToElement = (element) => {\n      const {\n        index\n      } = element.dataset;\n      if (index) {\n        scrollTo(index);\n      }\n    };\n    const onClickSidebar = (event) => {\n      scrollToElement(event.target);\n    };\n    let touchActiveIndex;\n    const onTouchMove = (event) => {\n      touch.move(event);\n      if (touch.isVertical()) {\n        preventDefault(event);\n        const {\n          clientX,\n          clientY\n        } = event.touches[0];\n        const target = document.elementFromPoint(clientX, clientY);\n        if (target) {\n          const {\n            index\n          } = target.dataset;\n          if (index && touchActiveIndex !== index) {\n            touchActiveIndex = index;\n            scrollToElement(target);\n          }\n        }\n      }\n    };\n    const renderSidebar = () => _createVNode(\"div\", {\n      \"ref\": sidebar,\n      \"class\": bem(\"sidebar\"),\n      \"style\": sidebarStyle.value,\n      \"onClick\": onClickSidebar,\n      \"onTouchstartPassive\": touch.start\n    }, [renderIndexes()]);\n    useExpose({\n      scrollTo\n    });\n    useEventListener(\"touchmove\", onTouchMove, {\n      target: sidebar\n    });\n    return () => {\n      var _a;\n      return _createVNode(\"div\", {\n        \"ref\": root,\n        \"class\": bem()\n      }, [props.teleport ? _createVNode(Teleport, {\n        \"to\": props.teleport\n      }, {\n        default: () => [renderSidebar()]\n      }) : renderSidebar(), (_a = slots.default) == null ? void 0 : _a.call(slots)]);\n    };\n  }\n});\nexport {\n  INDEX_BAR_KEY,\n  stdin_default as default\n};\n"], "mappings": ";;;;;;AAAA,SAASA,WAAW,IAAIC,YAAY,QAAQ,KAAK;AACjD,SAASC,GAAG,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,eAAe,QAAQ,KAAK;AAC1F,SAASC,KAAK,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,YAAY,EAAEC,cAAc,EAAEC,cAAc,EAAEC,eAAe,EAAEC,gBAAgB,EAAEC,gBAAgB,QAAQ,oBAAoB;AAC/K,SAASC,OAAO,EAAEC,WAAW,EAAEC,eAAe,EAAEC,gBAAgB,QAAQ,WAAW;AACnF,SAASC,QAAQ,QAAQ,8BAA8B;AACvD,SAASC,SAAS,QAAQ,+BAA+B;AACzD,SAASC,WAAWA,CAAA,EAAG;EACrB,IAAMC,WAAW,GAAG,GAAG,CAACC,UAAU,CAAC,CAAC,CAAC;EACrC,IAAMC,SAAS,GAAGC,KAAK,CAAC,EAAE,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC,CAACC,GAAG,CAAC,UAACC,CAAC,EAAEC,CAAC;IAAA,OAAKC,MAAM,CAACC,YAAY,CAACT,WAAW,GAAGO,CAAC,CAAC;EAAA,EAAC;EACxF,OAAOL,SAAS;AAClB;AACA,IAAAQ,gBAAA,GAAoBpB,eAAe,CAAC,WAAW,CAAC;EAAAqB,iBAAA,GAAAC,cAAA,CAAAF,gBAAA;EAAzCG,IAAI,GAAAF,iBAAA;EAAEG,GAAG,GAAAH,iBAAA;AAChB,IAAMI,aAAa,GAAG;EACpBC,MAAM,EAAE/B,SAAS;EACjBgC,MAAM,EAAE/B,WAAW;EACnBgC,QAAQ,EAAE,CAACV,MAAM,EAAEW,MAAM,CAAC;EAC1BC,cAAc,EAAEZ,MAAM;EACtBa,eAAe,EAAEhC,cAAc,CAAC,CAAC,CAAC;EAClCa,SAAS,EAAE;IACToB,IAAI,EAAEnB,KAAK;IACXoB,OAAO,EAAExB;EACX;AACF,CAAC;AACD,IAAMyB,aAAa,GAAGC,MAAM,CAACZ,IAAI,CAAC;AAClC,IAAIa,aAAa,GAAG5C,eAAe,CAAC;EAClC+B,IAAI,EAAJA,IAAI;EACJc,KAAK,EAAEZ,aAAa;EACpBa,KAAK,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC;EAC3BC,KAAK,WAAAA,MAACF,KAAK,EAAAG,IAAA,EAGR;IAAA,IAFDC,IAAI,GAAAD,IAAA,CAAJC,IAAI;MACJC,KAAK,GAAAF,IAAA,CAALE,KAAK;IAEL,IAAMC,IAAI,GAAGzD,GAAG,CAAC,CAAC;IAClB,IAAM0D,OAAO,GAAG1D,GAAG,CAAC,CAAC;IACrB,IAAM2D,YAAY,GAAG3D,GAAG,CAAC,EAAE,CAAC;IAC5B,IAAM4D,KAAK,GAAGvC,QAAQ,CAAC,CAAC;IACxB,IAAMwC,YAAY,GAAG1C,eAAe,CAACsC,IAAI,CAAC;IAC1C,IAAAK,YAAA,GAGI5C,WAAW,CAAC8B,aAAa,CAAC;MAF5Be,QAAQ,GAAAD,YAAA,CAARC,QAAQ;MACRC,YAAY,GAAAF,YAAA,CAAZE,YAAY;IAEd,IAAIC,iBAAiB;IACrBD,YAAY,CAAC;MACXb,KAAK,EAALA;IACF,CAAC,CAAC;IACF,IAAMe,YAAY,GAAGhE,QAAQ,CAAC,YAAM;MAClC,IAAIK,KAAK,CAAC4C,KAAK,CAACV,MAAM,CAAC,EAAE;QACvB,OAAO;UACLA,MAAM,EAAE,CAACU,KAAK,CAACV,MAAM,GAAG;QAC1B,CAAC;MACH;IACF,CAAC,CAAC;IACF,IAAM0B,cAAc,GAAGjE,QAAQ,CAAC,YAAM;MACpC,IAAIiD,KAAK,CAACP,cAAc,EAAE;QACxB,OAAO;UACLwB,KAAK,EAAEjB,KAAK,CAACP;QACf,CAAC;MACH;IACF,CAAC,CAAC;IACF,IAAMyB,eAAe,GAAG,SAAlBA,eAAeA,CAAIC,SAAS,EAAEC,KAAK,EAAK;MAC5C,KAAK,IAAIxC,CAAC,GAAGgC,QAAQ,CAACS,MAAM,GAAG,CAAC,EAAEzC,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;QAC7C,IAAM0C,UAAU,GAAG1C,CAAC,GAAG,CAAC,GAAGwC,KAAK,CAACxC,CAAC,GAAG,CAAC,CAAC,CAAC2C,MAAM,GAAG,CAAC;QAClD,IAAMC,QAAQ,GAAGxB,KAAK,CAACX,MAAM,GAAGiC,UAAU,GAAGtB,KAAK,CAACN,eAAe,GAAG,CAAC;QACtE,IAAIyB,SAAS,GAAGK,QAAQ,IAAIJ,KAAK,CAACxC,CAAC,CAAC,CAAC6C,GAAG,EAAE;UACxC,OAAO7C,CAAC;QACV;MACF;MACA,OAAO,CAAC,CAAC;IACX,CAAC;IACD,IAAM8C,cAAc,GAAG,SAAjBA,cAAcA,CAAIC,KAAK;MAAA,OAAKf,QAAQ,CAACgB,IAAI,CAAC,UAACC,IAAI;QAAA,OAAKhD,MAAM,CAACgD,IAAI,CAACF,KAAK,CAAC,KAAKA,KAAK;MAAA,EAAC;IAAA;IACvF,IAAMG,QAAQ,GAAG,SAAXA,QAAQA,CAAA,EAAS;MACrB,IAAIzE,QAAQ,CAACiD,IAAI,CAAC,EAAE;QAClB;MACF;MACA,IACEjB,MAAM,GAEJW,KAAK,CAFPX,MAAM;QACNd,SAAS,GACPyB,KAAK,CADPzB,SAAS;MAEX,IAAM4C,SAAS,GAAG3D,YAAY,CAACkD,YAAY,CAACqB,KAAK,CAAC;MAClD,IAAMC,gBAAgB,GAAGlE,OAAO,CAAC4C,YAAY,CAAC;MAC9C,IAAMU,KAAK,GAAGR,QAAQ,CAAClC,GAAG,CAAC,UAACmD,IAAI;QAAA,OAAKA,IAAI,CAACI,OAAO,CAACvB,YAAY,CAACqB,KAAK,EAAEC,gBAAgB,CAAC;MAAA,EAAC;MACxF,IAAIE,MAAM,GAAG,CAAC,CAAC;MACf,IAAIpB,iBAAiB,EAAE;QACrB,IAAMqB,KAAK,GAAGT,cAAc,CAACZ,iBAAiB,CAAC;QAC/C,IAAIqB,KAAK,EAAE;UACT,IAAMC,IAAI,GAAGD,KAAK,CAACF,OAAO,CAACvB,YAAY,CAACqB,KAAK,EAAEC,gBAAgB,CAAC;UAChEE,MAAM,GAAGhB,eAAe,CAACkB,IAAI,CAACX,GAAG,EAAEL,KAAK,CAAC;QAC3C;MACF,CAAC,MAAM;QACLc,MAAM,GAAGhB,eAAe,CAACC,SAAS,EAAEC,KAAK,CAAC;MAC5C;MACAZ,YAAY,CAACuB,KAAK,GAAGxD,SAAS,CAAC2D,MAAM,CAAC;MACtC,IAAI7C,MAAM,EAAE;QACVuB,QAAQ,CAACyB,OAAO,CAAC,UAACR,IAAI,EAAEF,KAAK,EAAK;UAChC,IACEW,KAAK,GAEHT,IAAI,CAFNS,KAAK;YACLC,GAAG,GACDV,IAAI,CADNU,GAAG;UAEL,IAAIZ,KAAK,KAAKO,MAAM,IAAIP,KAAK,KAAKO,MAAM,GAAG,CAAC,EAAE;YAC5C,IAAME,KAAI,GAAGG,GAAG,CAACC,qBAAqB,CAAC,CAAC;YACxCF,KAAK,CAACG,IAAI,GAAGL,KAAI,CAACK,IAAI;YACtBH,KAAK,CAACI,KAAK,GAAGN,KAAI,CAACM,KAAK;UAC1B,CAAC,MAAM;YACLJ,KAAK,CAACG,IAAI,GAAG,IAAI;YACjBH,KAAK,CAACI,KAAK,GAAG,IAAI;UACpB;UACA,IAAIf,KAAK,KAAKO,MAAM,EAAE;YACpBI,KAAK,CAACJ,MAAM,GAAG,IAAI;YACnBI,KAAK,CAACb,GAAG,GAAGkB,IAAI,CAACC,GAAG,CAAC5C,KAAK,CAACN,eAAe,EAAE0B,KAAK,CAACO,KAAK,CAAC,CAACF,GAAG,GAAGN,SAAS,CAAC,GAAGa,gBAAgB,CAACP,GAAG;UAClG,CAAC,MAAM,IAAIE,KAAK,KAAKO,MAAM,GAAG,CAAC,IAAIpB,iBAAiB,KAAK,EAAE,EAAE;YAC3D,IAAM+B,aAAa,GAAGzB,KAAK,CAACc,MAAM,CAAC,CAACT,GAAG,GAAGN,SAAS;YACnDmB,KAAK,CAACJ,MAAM,GAAGW,aAAa,GAAG,CAAC;YAChCP,KAAK,CAACb,GAAG,GAAGoB,aAAa,GAAGb,gBAAgB,CAACP,GAAG,GAAGL,KAAK,CAACO,KAAK,CAAC,CAACJ,MAAM;UACxE,CAAC,MAAM;YACLe,KAAK,CAACJ,MAAM,GAAG,KAAK;UACtB;QACF,CAAC,CAAC;MACJ;MACApB,iBAAiB,GAAG,EAAE;IACxB,CAAC;IACD,IAAMgC,IAAI,GAAG,SAAPA,IAAIA,CAAA,EAAS;MACjB9F,QAAQ,CAAC8E,QAAQ,CAAC;IACpB,CAAC;IACD7D,gBAAgB,CAAC,QAAQ,EAAE6D,QAAQ,EAAE;MACnCiB,MAAM,EAAErC,YAAY;MACpBsC,OAAO,EAAE;IACX,CAAC,CAAC;IACF9F,SAAS,CAAC4F,IAAI,CAAC;IACfhG,KAAK,CAAC;MAAA,OAAMkD,KAAK,CAACzB,SAAS;IAAA,GAAEuE,IAAI,CAAC;IAClChG,KAAK,CAAC0D,YAAY,EAAE,UAACuB,KAAK,EAAK;MAC7B,IAAIA,KAAK,EAAE;QACT3B,IAAI,CAAC,QAAQ,EAAE2B,KAAK,CAAC;MACvB;IACF,CAAC,CAAC;IACF,IAAMkB,aAAa,GAAG,SAAhBA,aAAaA,CAAA;MAAA,OAASjD,KAAK,CAACzB,SAAS,CAACG,GAAG,CAAC,UAACiD,KAAK,EAAK;QACzD,IAAMO,MAAM,GAAGP,KAAK,KAAKnB,YAAY,CAACuB,KAAK;QAC3C,OAAOnF,YAAY,CAAC,MAAM,EAAE;UAC1B,OAAO,EAAEuC,GAAG,CAAC,OAAO,EAAE;YACpB+C,MAAM,EAANA;UACF,CAAC,CAAC;UACF,OAAO,EAAEA,MAAM,GAAGlB,cAAc,CAACe,KAAK,GAAG,KAAK,CAAC;UAC/C,YAAY,EAAEJ;QAChB,CAAC,EAAE,CAACA,KAAK,CAAC,CAAC;MACb,CAAC,CAAC;IAAA;IACF,IAAMuB,QAAQ,GAAG,SAAXA,QAAQA,CAAIvB,KAAK,EAAK;MAC1Bb,iBAAiB,GAAGjC,MAAM,CAAC8C,KAAK,CAAC;MACjC,IAAMQ,KAAK,GAAGT,cAAc,CAACZ,iBAAiB,CAAC;MAC/C,IAAIqB,KAAK,EAAE;QACT,IAAMhB,SAAS,GAAG3D,YAAY,CAACkD,YAAY,CAACqB,KAAK,CAAC;QAClD,IAAMC,gBAAgB,GAAGlE,OAAO,CAAC4C,YAAY,CAAC;QAC9C,IACEyC,YAAY,GACVC,QAAQ,CAACC,eAAe,CAD1BF,YAAY;QAEdhB,KAAK,CAACI,GAAG,CAACe,cAAc,CAAC,CAAC;QAC1B,IAAInC,SAAS,KAAKgC,YAAY,GAAGnB,gBAAgB,CAACT,MAAM,EAAE;UACxDO,QAAQ,CAAC,CAAC;UACV;QACF;QACA,IAAI9B,KAAK,CAACX,MAAM,IAAIW,KAAK,CAACN,eAAe,EAAE;UACzC7B,gBAAgB,CAACD,gBAAgB,CAAC,CAAC,GAAGoC,KAAK,CAACN,eAAe,CAAC;QAC9D;QACAU,IAAI,CAAC,QAAQ,EAAE+B,KAAK,CAACR,KAAK,CAAC;MAC7B;IACF,CAAC;IACD,IAAM4B,eAAe,GAAG,SAAlBA,eAAeA,CAAIC,OAAO,EAAK;MACnC,IACE7B,KAAK,GACH6B,OAAO,CAACC,OAAO,CADjB9B,KAAK;MAEP,IAAIA,KAAK,EAAE;QACTuB,QAAQ,CAACvB,KAAK,CAAC;MACjB;IACF,CAAC;IACD,IAAM+B,cAAc,GAAG,SAAjBA,cAAcA,CAAIC,KAAK,EAAK;MAChCJ,eAAe,CAACI,KAAK,CAACZ,MAAM,CAAC;IAC/B,CAAC;IACD,IAAIa,gBAAgB;IACpB,IAAMC,WAAW,GAAG,SAAdA,WAAWA,CAAIF,KAAK,EAAK;MAC7BlD,KAAK,CAACqD,IAAI,CAACH,KAAK,CAAC;MACjB,IAAIlD,KAAK,CAACsD,UAAU,CAAC,CAAC,EAAE;QACtBtG,cAAc,CAACkG,KAAK,CAAC;QACrB,IAAAK,eAAA,GAGIL,KAAK,CAACM,OAAO,CAAC,CAAC,CAAC;UAFlBC,OAAO,GAAAF,eAAA,CAAPE,OAAO;UACPC,OAAO,GAAAH,eAAA,CAAPG,OAAO;QAET,IAAMpB,MAAM,GAAGK,QAAQ,CAACgB,gBAAgB,CAACF,OAAO,EAAEC,OAAO,CAAC;QAC1D,IAAIpB,MAAM,EAAE;UACV,IACEpB,KAAK,GACHoB,MAAM,CAACU,OAAO,CADhB9B,KAAK;UAEP,IAAIA,KAAK,IAAIiC,gBAAgB,KAAKjC,KAAK,EAAE;YACvCiC,gBAAgB,GAAGjC,KAAK;YACxB4B,eAAe,CAACR,MAAM,CAAC;UACzB;QACF;MACF;IACF,CAAC;IACD,IAAMsB,aAAa,GAAG,SAAhBA,aAAaA,CAAA;MAAA,OAASzH,YAAY,CAAC,KAAK,EAAE;QAC9C,KAAK,EAAE2D,OAAO;QACd,OAAO,EAAEpB,GAAG,CAAC,SAAS,CAAC;QACvB,OAAO,EAAE4B,YAAY,CAACgB,KAAK;QAC3B,SAAS,EAAE2B,cAAc;QACzB,qBAAqB,EAAEjD,KAAK,CAAC6D;MAC/B,CAAC,EAAE,CAACrB,aAAa,CAAC,CAAC,CAAC,CAAC;IAAA;IACrB9E,SAAS,CAAC;MACR+E,QAAQ,EAARA;IACF,CAAC,CAAC;IACFjF,gBAAgB,CAAC,WAAW,EAAE4F,WAAW,EAAE;MACzCd,MAAM,EAAExC;IACV,CAAC,CAAC;IACF,OAAO,YAAM;MACX,IAAIgE,EAAE;MACN,OAAO3H,YAAY,CAAC,KAAK,EAAE;QACzB,KAAK,EAAE0D,IAAI;QACX,OAAO,EAAEnB,GAAG,CAAC;MACf,CAAC,EAAE,CAACa,KAAK,CAACT,QAAQ,GAAG3C,YAAY,CAACK,QAAQ,EAAE;QAC1C,IAAI,EAAE+C,KAAK,CAACT;MACd,CAAC,EAAE;QACDK,OAAO,EAAE,SAAA4E,SAAA;UAAA,OAAM,CAACH,aAAa,CAAC,CAAC,CAAC;QAAA;MAClC,CAAC,CAAC,GAAGA,aAAa,CAAC,CAAC,EAAE,CAACE,EAAE,GAAGlE,KAAK,CAACT,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG2E,EAAE,CAACE,IAAI,CAACpE,KAAK,CAAC,CAAC,CAAC;IAChF,CAAC;EACH;AACF,CAAC,CAAC;AACF,SACER,aAAa,EACbE,aAAa,IAAIH,OAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}