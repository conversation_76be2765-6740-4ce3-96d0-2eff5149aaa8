var VueCompilerDOM=function(e){"use strict";function t(e,t){const n=Object.create(null),o=e.split(",");for(let r=0;r<o.length;r++)n[o[r]]=!0;return t?e=>!!n[e.toLowerCase()]:e=>!!n[e]}const n=/;(?![^(]*\))/g,o=/:(.+)/;const r=t("html,body,base,head,link,meta,style,title,address,article,aside,footer,header,h1,h2,h3,h4,h5,h6,nav,section,div,dd,dl,dt,figcaption,figure,picture,hr,img,li,main,ol,p,pre,ul,a,b,abbr,bdi,bdo,br,cite,code,data,dfn,em,i,kbd,mark,q,rp,rt,ruby,s,samp,small,span,strong,sub,sup,time,u,var,wbr,area,audio,map,track,video,embed,object,param,source,canvas,script,noscript,del,ins,caption,col,colgroup,table,thead,tbody,td,th,tr,button,datalist,fieldset,form,input,label,legend,meter,optgroup,option,output,progress,select,textarea,details,dialog,menu,summary,template,blockquote,iframe,tfoot"),s=t("svg,animate,animateMotion,animateTransform,circle,clipPath,color-profile,defs,desc,discard,ellipse,feBlend,feColorMatrix,feComponentTransfer,feComposite,feConvolveMatrix,feDiffuseLighting,feDisplacementMap,feDistanceLight,feDropShadow,feFlood,feFuncA,feFuncB,feFuncG,feFuncR,feGaussianBlur,feImage,feMerge,feMergeNode,feMorphology,feOffset,fePointLight,feSpecularLighting,feSpotLight,feTile,feTurbulence,filter,foreignObject,g,hatch,hatchpath,image,line,linearGradient,marker,mask,mesh,meshgradient,meshpatch,meshrow,metadata,mpath,path,pattern,polygon,polyline,radialGradient,rect,set,solidcolor,stop,switch,symbol,text,textPath,title,tspan,unknown,use,view"),i=t("area,base,br,col,embed,hr,img,input,link,meta,param,source,track,wbr"),c={},l=()=>{},a=()=>!1,p=/^on[^a-z]/,u=e=>p.test(e),f=Object.assign,d=Array.isArray,h=e=>"string"==typeof e,m=e=>"symbol"==typeof e,g=e=>null!==e&&"object"==typeof e,y=t(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),v=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},S=/-(\w)/g,b=v((e=>e.replace(S,((e,t)=>t?t.toUpperCase():"")))),E=/\B([A-Z])/g,N=v((e=>e.replace(E,"-$1").toLowerCase())),_=v((e=>e.charAt(0).toUpperCase()+e.slice(1))),x=v((e=>e?`on${_(e)}`:""));function T(e){throw e}function O(e){}function k(e,t,n,o){const r=new SyntaxError(String(e));return r.code=e,r.loc=t,r}const C=Symbol(""),I=Symbol(""),M=Symbol(""),R=Symbol(""),P=Symbol(""),w=Symbol(""),$=Symbol(""),L=Symbol(""),V=Symbol(""),A=Symbol(""),D=Symbol(""),B=Symbol(""),F=Symbol(""),j=Symbol(""),H=Symbol(""),W=Symbol(""),K=Symbol(""),U=Symbol(""),J=Symbol(""),G=Symbol(""),z=Symbol(""),Y=Symbol(""),q=Symbol(""),Z=Symbol(""),X=Symbol(""),Q=Symbol(""),ee=Symbol(""),te=Symbol(""),ne=Symbol(""),oe=Symbol(""),re=Symbol(""),se=Symbol(""),ie=Symbol(""),ce=Symbol(""),le=Symbol(""),ae=Symbol(""),pe=Symbol(""),ue=Symbol(""),fe=Symbol(""),de={[C]:"Fragment",[I]:"Teleport",[M]:"Suspense",[R]:"KeepAlive",[P]:"BaseTransition",[w]:"openBlock",[$]:"createBlock",[L]:"createElementBlock",[V]:"createVNode",[A]:"createElementVNode",[D]:"createCommentVNode",[B]:"createTextVNode",[F]:"createStaticVNode",[j]:"resolveComponent",[H]:"resolveDynamicComponent",[W]:"resolveDirective",[K]:"resolveFilter",[U]:"withDirectives",[J]:"renderList",[G]:"renderSlot",[z]:"createSlots",[Y]:"toDisplayString",[q]:"mergeProps",[Z]:"normalizeClass",[X]:"normalizeStyle",[Q]:"normalizeProps",[ee]:"guardReactiveProps",[te]:"toHandlers",[ne]:"camelize",[oe]:"capitalize",[re]:"toHandlerKey",[se]:"setBlockTracking",[ie]:"pushScopeId",[ce]:"popScopeId",[le]:"withCtx",[ae]:"unref",[pe]:"isRef",[ue]:"withMemo",[fe]:"isMemoSame"};function he(e){Object.getOwnPropertySymbols(e).forEach((t=>{de[t]=e[t]}))}const me={source:"",start:{line:1,column:1,offset:0},end:{line:1,column:1,offset:0}};function ge(e,t=me){return{type:0,children:e,helpers:[],components:[],directives:[],hoists:[],imports:[],cached:0,temps:0,codegenNode:void 0,loc:t}}function ye(e,t,n,o,r,s,i,c=!1,l=!1,a=!1,p=me){return e&&(c?(e.helper(w),e.helper(Ze(e.inSSR,a))):e.helper(qe(e.inSSR,a)),i&&e.helper(U)),{type:13,tag:t,props:n,children:o,patchFlag:r,dynamicProps:s,directives:i,isBlock:c,disableTracking:l,isComponent:a,loc:p}}function ve(e,t=me){return{type:17,loc:t,elements:e}}function Se(e,t=me){return{type:15,loc:t,properties:e}}function be(e,t){return{type:16,loc:me,key:h(e)?Ee(e,!0):e,value:t}}function Ee(e,t=!1,n=me,o=0){return{type:4,loc:n,content:e,isStatic:t,constType:t?3:o}}function Ne(e,t=me){return{type:8,loc:t,children:e}}function _e(e,t=[],n=me){return{type:14,loc:n,callee:e,arguments:t}}function xe(e,t,n=!1,o=!1,r=me){return{type:18,params:e,returns:t,newline:n,isSlot:o,loc:r}}function Te(e,t,n,o=!0){return{type:19,test:e,consequent:t,alternate:n,newline:o,loc:me}}function Oe(e,t,n=!1){return{type:20,index:e,value:t,isVNode:n,loc:me}}function ke(e){return{type:21,body:e,loc:me}}const Ce=e=>4===e.type&&e.isStatic,Ie=(e,t)=>e===t||e===N(t);function Me(e){return Ie(e,"Teleport")?I:Ie(e,"Suspense")?M:Ie(e,"KeepAlive")?R:Ie(e,"BaseTransition")?P:void 0}const Re=/^\d|[^\$\w]/,Pe=e=>!Re.test(e),we=/[A-Za-z_$\xA0-\uFFFF]/,$e=/[\.\?\w$\xA0-\uFFFF]/,Le=/\s+[.[]\s*|\s*[.[]\s+/g,Ve=e=>{e=e.trim().replace(Le,(e=>e.trim()));let t=0,n=[],o=0,r=0,s=null;for(let i=0;i<e.length;i++){const c=e.charAt(i);switch(t){case 0:if("["===c)n.push(t),t=1,o++;else if("("===c)n.push(t),t=2,r++;else if(!(0===i?we:$e).test(c))return!1;break;case 1:"'"===c||'"'===c||"`"===c?(n.push(t),t=3,s=c):"["===c?o++:"]"===c&&(--o||(t=n.pop()));break;case 2:if("'"===c||'"'===c||"`"===c)n.push(t),t=3,s=c;else if("("===c)r++;else if(")"===c){if(i===e.length-1)return!1;--r||(t=n.pop())}break;case 3:c===s&&(t=n.pop(),s=null)}}return!o&&!r},Ae=l,De=Ve;function Be(e,t,n){const o={source:e.source.slice(t,t+n),start:Fe(e.start,e.source,t),end:e.end};return null!=n&&(o.end=Fe(e.start,e.source,t+n)),o}function Fe(e,t,n=t.length){return je(f({},e),t,n)}function je(e,t,n=t.length){let o=0,r=-1;for(let s=0;s<n;s++)10===t.charCodeAt(s)&&(o++,r=s);return e.offset+=n,e.line+=o,e.column=-1===r?e.column+n:n-r,e}function He(e,t,n=!1){for(let o=0;o<e.props.length;o++){const r=e.props[o];if(7===r.type&&(n||r.exp)&&(h(t)?r.name===t:t.test(r.name)))return r}}function We(e,t,n=!1,o=!1){for(let r=0;r<e.props.length;r++){const s=e.props[r];if(6===s.type){if(n)continue;if(s.name===t&&(s.value||o))return s}else if("bind"===s.name&&(s.exp||o)&&Ke(s.arg,t))return s}}function Ke(e,t){return!(!e||!Ce(e)||e.content!==t)}function Ue(e){return e.props.some((e=>!(7!==e.type||"bind"!==e.name||e.arg&&4===e.arg.type&&e.arg.isStatic)))}function Je(e){return 5===e.type||2===e.type}function Ge(e){return 7===e.type&&"slot"===e.name}function ze(e){return 1===e.type&&3===e.tagType}function Ye(e){return 1===e.type&&2===e.tagType}function qe(e,t){return e||t?V:A}function Ze(e,t){return e||t?$:L}const Xe=new Set([Q,ee]);function Qe(e,t=[]){if(e&&!h(e)&&14===e.type){const n=e.callee;if(!h(n)&&Xe.has(n))return Qe(e.arguments[0],t.concat(e))}return[e,t]}function et(e,t,n){let o,r,s=13===e.type?e.props:e.arguments[2],i=[];if(s&&!h(s)&&14===s.type){const e=Qe(s);s=e[0],i=e[1],r=i[i.length-1]}if(null==s||h(s))o=Se([t]);else if(14===s.type){const e=s.arguments[0];h(e)||15!==e.type?s.callee===te?o=_e(n.helper(q),[Se([t]),s]):s.arguments.unshift(Se([t])):e.properties.unshift(t),!o&&(o=s)}else if(15===s.type){let e=!1;if(4===t.key.type){const n=t.key.content;e=s.properties.some((e=>4===e.key.type&&e.key.content===n))}e||s.properties.unshift(t),o=s}else o=_e(n.helper(q),[Se([t]),s]),r&&r.callee===ee&&(r=i[i.length-2]);13===e.type?r?r.arguments[0]=o:e.props=o:r?r.arguments[0]=o:e.arguments[2]=o}function tt(e,t){return`_${t}_${e.replace(/[^\w]/g,((t,n)=>"-"===t?"_":e.charCodeAt(n).toString()))}`}function nt(e){return 14===e.type&&e.callee===ue?e.arguments[1].returns:e}function ot(e,{helper:t,removeHelper:n,inSSR:o}){e.isBlock||(e.isBlock=!0,n(qe(o,e.isComponent)),t(w),t(Ze(o,e.isComponent)))}const rt={COMPILER_IS_ON_ELEMENT:{message:'Platform-native elements with "is" prop will no longer be treated as components in Vue 3 unless the "is" value is explicitly prefixed with "vue:".',link:"https://v3.vuejs.org/guide/migration/custom-elements-interop.html"},COMPILER_V_BIND_SYNC:{message:e=>`.sync modifier for v-bind has been removed. Use v-model with argument instead. \`v-bind:${e}.sync\` should be changed to \`v-model:${e}\`.`,link:"https://v3.vuejs.org/guide/migration/v-model.html"},COMPILER_V_BIND_PROP:{message:".prop modifier for v-bind has been removed and no longer necessary. Vue 3 will automatically set a binding as DOM property when appropriate."},COMPILER_V_BIND_OBJECT_ORDER:{message:'v-bind="obj" usage is now order sensitive and behaves like JavaScript object spread: it will now overwrite an existing non-mergeable attribute that appears before v-bind in the case of conflict. To retain 2.x behavior, move v-bind to make it the first attribute. You can also suppress this warning if the usage is intended.',link:"https://v3.vuejs.org/guide/migration/v-bind.html"},COMPILER_V_ON_NATIVE:{message:".native modifier for v-on has been removed as is no longer necessary.",link:"https://v3.vuejs.org/guide/migration/v-on-native-modifier-removed.html"},COMPILER_V_IF_V_FOR_PRECEDENCE:{message:"v-if / v-for precedence when used on the same element has changed in Vue 3: v-if now takes higher precedence and will no longer have access to v-for scope variables. It is best to avoid the ambiguity with <template> tags or use a computed property that filters v-for data source.",link:"https://v3.vuejs.org/guide/migration/v-if-v-for.html"},COMPILER_NATIVE_TEMPLATE:{message:"<template> with no special directives will render as a native template element instead of its inner content in Vue 3."},COMPILER_INLINE_TEMPLATE:{message:'"inline-template" has been removed in Vue 3.',link:"https://v3.vuejs.org/guide/migration/inline-template-attribute.html"},COMPILER_FILTER:{message:'filters have been removed in Vue 3. The "|" symbol will be treated as native JavaScript bitwise OR operator. Use method calls or computed properties instead.',link:"https://v3.vuejs.org/guide/migration/filters.html"}};function st(e,t){const n=t.options?t.options.compatConfig:t.compatConfig,o=n&&n[e];return"MODE"===e?o||3:o}function it(e,t){const n=st("MODE",t),o=st(e,t);return 3===n?!0===o:!1!==o}function ct(e,t,n,...o){return it(e,t)}const lt=/&(gt|lt|amp|apos|quot);/g,at={gt:">",lt:"<",amp:"&",apos:"'",quot:'"'},pt={delimiters:["{{","}}"],getNamespace:()=>0,getTextMode:()=>0,isVoidTag:a,isPreTag:a,isCustomElement:a,decodeEntities:e=>e.replace(lt,((e,t)=>at[t])),onError:T,onWarn:O,comments:!1};function ut(e,t={}){const n=function(e,t){const n=f({},pt);let o;for(o in t)n[o]=void 0===t[o]?pt[o]:t[o];return{options:n,column:1,line:1,offset:0,originalSource:e,source:e,inPre:!1,inVPre:!1,onWarn:n.onWarn}}(e,t),o=Tt(n);return ge(ft(n,0,[]),Ot(n,o))}function ft(e,t,n){const o=kt(n),r=o?o.ns:0,s=[];for(;!Pt(e,t,n);){const i=e.source;let c;if(0===t||1===t)if(!e.inVPre&&Ct(i,e.options.delimiters[0]))c=Nt(e,t);else if(0===t&&"<"===i[0])if(1===i.length);else if("!"===i[1])c=Ct(i,"\x3c!--")?mt(e):Ct(i,"<!DOCTYPE")?gt(e):Ct(i,"<![CDATA[")&&0!==r?ht(e,n):gt(e);else if("/"===i[1])if(2===i.length);else{if(">"===i[2]){It(e,3);continue}if(/[a-z]/i.test(i[2])){St(e,1,o);continue}c=gt(e)}else/[a-z]/i.test(i[1])?(c=yt(e,n),it("COMPILER_NATIVE_TEMPLATE",e)&&c&&"template"===c.tag&&!c.props.some((e=>7===e.type&&vt(e.name)))&&(c=c.children)):"?"===i[1]&&(c=gt(e));if(c||(c=_t(e,t)),d(c))for(let e=0;e<c.length;e++)dt(s,c[e]);else dt(s,c)}let i=!1;if(2!==t&&1!==t){const t="preserve"!==e.options.whitespace;for(let n=0;n<s.length;n++){const o=s[n];if(e.inPre||2!==o.type)3!==o.type||e.options.comments||(i=!0,s[n]=null);else if(/[^\t\r\n\f ]/.test(o.content))t&&(o.content=o.content.replace(/[\t\r\n\f ]+/g," "));else{const e=s[n-1],r=s[n+1];!e||!r||t&&(3===e.type||3===r.type||1===e.type&&1===r.type&&/[\r\n]/.test(o.content))?(i=!0,s[n]=null):o.content=" "}}if(e.inPre&&o&&e.options.isPreTag(o.tag)){const e=s[0];e&&2===e.type&&(e.content=e.content.replace(/^\r?\n/,""))}}return i?s.filter(Boolean):s}function dt(e,t){if(2===t.type){const n=kt(e);if(n&&2===n.type&&n.loc.end.offset===t.loc.start.offset)return n.content+=t.content,n.loc.end=t.loc.end,void(n.loc.source+=t.loc.source)}e.push(t)}function ht(e,t){It(e,9);const n=ft(e,3,t);return 0===e.source.length||It(e,3),n}function mt(e){const t=Tt(e);let n;const o=/--(\!)?>/.exec(e.source);if(o){n=e.source.slice(4,o.index);const t=e.source.slice(0,o.index);let r=1,s=0;for(;-1!==(s=t.indexOf("\x3c!--",r));)It(e,s-r+1),r=s+1;It(e,o.index+o[0].length-r+1)}else n=e.source.slice(4),It(e,e.source.length);return{type:3,content:n,loc:Ot(e,t)}}function gt(e){const t=Tt(e),n="?"===e.source[1]?1:2;let o;const r=e.source.indexOf(">");return-1===r?(o=e.source.slice(n),It(e,e.source.length)):(o=e.source.slice(n,r),It(e,r+1)),{type:3,content:o,loc:Ot(e,t)}}function yt(e,t){const n=e.inPre,o=e.inVPre,r=kt(t),s=St(e,0,r),i=e.inPre&&!n,c=e.inVPre&&!o;if(s.isSelfClosing||e.options.isVoidTag(s.tag))return i&&(e.inPre=!1),c&&(e.inVPre=!1),s;t.push(s);const l=e.options.getTextMode(s,r),a=ft(e,l,t);t.pop();{const t=s.props.find((e=>6===e.type&&"inline-template"===e.name));if(t&&ct("COMPILER_INLINE_TEMPLATE",e)){const n=Ot(e,s.loc.end);t.value={type:2,content:n.source,loc:n}}}if(s.children=a,wt(e.source,s.tag))St(e,1,r);else if(0===e.source.length&&"script"===s.tag.toLowerCase()){const e=a[0];e&&Ct(e.loc.source,"\x3c!--")}return s.loc=Ot(e,s.loc.start),i&&(e.inPre=!1),c&&(e.inVPre=!1),s}const vt=t("if,else,else-if,for,slot");function St(e,t,n){const o=Tt(e),r=/^<\/?([a-z][^\t\r\n\f />]*)/i.exec(e.source),s=r[1],i=e.options.getNamespace(s,n);It(e,r[0].length),Mt(e);const c=Tt(e),l=e.source;e.options.isPreTag(s)&&(e.inPre=!0);let a=bt(e,t);0===t&&!e.inVPre&&a.some((e=>7===e.type&&"pre"===e.name))&&(e.inVPre=!0,f(e,c),e.source=l,a=bt(e,t).filter((e=>"v-pre"!==e.name)));let p=!1;if(0===e.source.length||(p=Ct(e.source,"/>"),It(e,p?2:1)),1===t)return;let u=0;return e.inVPre||("slot"===s?u=2:"template"===s?a.some((e=>7===e.type&&vt(e.name)))&&(u=3):function(e,t,n){const o=n.options;if(o.isCustomElement(e))return!1;if("component"===e||/^[A-Z]/.test(e)||Me(e)||o.isBuiltInComponent&&o.isBuiltInComponent(e)||o.isNativeTag&&!o.isNativeTag(e))return!0;for(let r=0;r<t.length;r++){const e=t[r];if(6===e.type){if("is"===e.name&&e.value){if(e.value.content.startsWith("vue:"))return!0;if(ct("COMPILER_IS_ON_ELEMENT",n))return!0}}else{if("is"===e.name)return!0;if("bind"===e.name&&Ke(e.arg,"is")&&ct("COMPILER_IS_ON_ELEMENT",n))return!0}}}(s,a,e)&&(u=1)),{type:1,ns:i,tag:s,tagType:u,props:a,isSelfClosing:p,children:[],loc:Ot(e,o),codegenNode:void 0}}function bt(e,t){const n=[],o=new Set;for(;e.source.length>0&&!Ct(e.source,">")&&!Ct(e.source,"/>");){if(Ct(e.source,"/")){It(e,1),Mt(e);continue}const r=Et(e,o);6===r.type&&r.value&&"class"===r.name&&(r.value.content=r.value.content.replace(/\s+/g," ").trim()),0===t&&n.push(r),/^[^\t\r\n\f />]/.test(e.source),Mt(e)}return n}function Et(e,t){const n=Tt(e),o=/^[^\t\r\n\f />][^\t\r\n\f />=]*/.exec(e.source)[0];t.has(o),t.add(o);{const e=/["'<]/g;let t;for(;t=e.exec(o););}let r;It(e,o.length),/^[\t\r\n\f ]*=/.test(e.source)&&(Mt(e),It(e,1),Mt(e),r=function(e){const t=Tt(e);let n;const o=e.source[0],r='"'===o||"'"===o;if(r){It(e,1);const t=e.source.indexOf(o);-1===t?n=xt(e,e.source.length,4):(n=xt(e,t,4),It(e,1))}else{const t=/^[^\t\r\n\f >]+/.exec(e.source);if(!t)return;const o=/["'<=`]/g;let r;for(;r=o.exec(t[0]););n=xt(e,t[0].length,4)}return{content:n,isQuoted:r,loc:Ot(e,t)}}(e));const s=Ot(e,n);if(!e.inVPre&&/^(v-[A-Za-z0-9-]|:|\.|@|#)/.test(o)){const t=/(?:^v-([a-z0-9-]+))?(?:(?::|^\.|^@|^#)(\[[^\]]+\]|[^\.]+))?(.+)?$/i.exec(o);let i,c=Ct(o,"."),l=t[1]||(c||Ct(o,":")?"bind":Ct(o,"@")?"on":"slot");if(t[2]){const r="slot"===l,s=o.lastIndexOf(t[2]),c=Ot(e,Rt(e,n,s),Rt(e,n,s+t[2].length+(r&&t[3]||"").length));let a=t[2],p=!0;a.startsWith("[")?(p=!1,a=a.endsWith("]")?a.slice(1,a.length-1):a.slice(1)):r&&(a+=t[3]||""),i={type:4,content:a,isStatic:p,constType:p?3:0,loc:c}}if(r&&r.isQuoted){const e=r.loc;e.start.offset++,e.start.column++,e.end=Fe(e.start,r.content),e.source=e.source.slice(1,-1)}const a=t[3]?t[3].slice(1).split("."):[];return c&&a.push("prop"),"bind"===l&&i&&a.includes("sync")&&ct("COMPILER_V_BIND_SYNC",e,0)&&(l="model",a.splice(a.indexOf("sync"),1)),{type:7,name:l,exp:r&&{type:4,content:r.content,isStatic:!1,constType:0,loc:r.loc},arg:i,modifiers:a,loc:s}}return!e.inVPre&&Ct(o,"v-"),{type:6,name:o,value:r&&{type:2,content:r.content,loc:r.loc},loc:s}}function Nt(e,t){const[n,o]=e.options.delimiters,r=e.source.indexOf(o,n.length);if(-1===r)return;const s=Tt(e);It(e,n.length);const i=Tt(e),c=Tt(e),l=r-n.length,a=e.source.slice(0,l),p=xt(e,l,t),u=p.trim(),f=p.indexOf(u);f>0&&je(i,a,f);return je(c,a,l-(p.length-u.length-f)),It(e,o.length),{type:5,content:{type:4,isStatic:!1,constType:0,content:u,loc:Ot(e,i,c)},loc:Ot(e,s)}}function _t(e,t){const n=3===t?["]]>"]:["<",e.options.delimiters[0]];let o=e.source.length;for(let s=0;s<n.length;s++){const t=e.source.indexOf(n[s],1);-1!==t&&o>t&&(o=t)}const r=Tt(e);return{type:2,content:xt(e,o,t),loc:Ot(e,r)}}function xt(e,t,n){const o=e.source.slice(0,t);return It(e,t),2===n||3===n||-1===o.indexOf("&")?o:e.options.decodeEntities(o,4===n)}function Tt(e){const{column:t,line:n,offset:o}=e;return{column:t,line:n,offset:o}}function Ot(e,t,n){return{start:t,end:n=n||Tt(e),source:e.originalSource.slice(t.offset,n.offset)}}function kt(e){return e[e.length-1]}function Ct(e,t){return e.startsWith(t)}function It(e,t){const{source:n}=e;je(e,n,t),e.source=n.slice(t)}function Mt(e){const t=/^[\t\r\n\f ]+/.exec(e.source);t&&It(e,t[0].length)}function Rt(e,t,n){return Fe(t,e.originalSource.slice(t.offset,n),n)}function Pt(e,t,n){const o=e.source;switch(t){case 0:if(Ct(o,"</"))for(let e=n.length-1;e>=0;--e)if(wt(o,n[e].tag))return!0;break;case 1:case 2:{const e=kt(n);if(e&&wt(o,e.tag))return!0;break}case 3:if(Ct(o,"]]>"))return!0}return!o}function wt(e,t){return Ct(e,"</")&&e.slice(2,2+t.length).toLowerCase()===t.toLowerCase()&&/[\t\r\n\f />]/.test(e[2+t.length]||">")}function $t(e,t){Vt(e,t,Lt(e,e.children[0]))}function Lt(e,t){const{children:n}=e;return 1===n.length&&1===t.type&&!Ye(t)}function Vt(e,t,n=!1){const{children:o}=e,r=o.length;let s=0;for(let i=0;i<o.length;i++){const e=o[i];if(1===e.type&&0===e.tagType){const o=n?0:At(e,t);if(o>0){if(o>=2){e.codegenNode.patchFlag="-1",e.codegenNode=t.hoist(e.codegenNode),s++;continue}}else{const n=e.codegenNode;if(13===n.type){const o=Ht(n);if((!o||512===o||1===o)&&Ft(e,t)>=2){const o=jt(e);o&&(n.props=t.hoist(o))}n.dynamicProps&&(n.dynamicProps=t.hoist(n.dynamicProps))}}}else 12===e.type&&At(e.content,t)>=2&&(e.codegenNode=t.hoist(e.codegenNode),s++);if(1===e.type){const n=1===e.tagType;n&&t.scopes.vSlot++,Vt(e,t),n&&t.scopes.vSlot--}else if(11===e.type)Vt(e,t,1===e.children.length);else if(9===e.type)for(let n=0;n<e.branches.length;n++)Vt(e.branches[n],t,1===e.branches[n].children.length)}s&&t.transformHoist&&t.transformHoist(o,t,e),s&&s===r&&1===e.type&&0===e.tagType&&e.codegenNode&&13===e.codegenNode.type&&d(e.codegenNode.children)&&(e.codegenNode.children=t.hoist(ve(e.codegenNode.children)))}function At(e,t){const{constantCache:n}=t;switch(e.type){case 1:if(0!==e.tagType)return 0;const o=n.get(e);if(void 0!==o)return o;const r=e.codegenNode;if(13!==r.type)return 0;if(r.isBlock&&"svg"!==e.tag&&"foreignObject"!==e.tag)return 0;if(Ht(r))return n.set(e,0),0;{let o=3;const s=Ft(e,t);if(0===s)return n.set(e,0),0;s<o&&(o=s);for(let r=0;r<e.children.length;r++){const s=At(e.children[r],t);if(0===s)return n.set(e,0),0;s<o&&(o=s)}if(o>1)for(let r=0;r<e.props.length;r++){const s=e.props[r];if(7===s.type&&"bind"===s.name&&s.exp){const r=At(s.exp,t);if(0===r)return n.set(e,0),0;r<o&&(o=r)}}return r.isBlock&&(t.removeHelper(w),t.removeHelper(Ze(t.inSSR,r.isComponent)),r.isBlock=!1,t.helper(qe(t.inSSR,r.isComponent))),n.set(e,o),o}case 2:case 3:return 3;case 9:case 11:case 10:default:return 0;case 5:case 12:return At(e.content,t);case 4:return e.constType;case 8:let s=3;for(let n=0;n<e.children.length;n++){const o=e.children[n];if(h(o)||m(o))continue;const r=At(o,t);if(0===r)return 0;r<s&&(s=r)}return s}}const Dt=new Set([Z,X,Q,ee]);function Bt(e,t){if(14===e.type&&!h(e.callee)&&Dt.has(e.callee)){const n=e.arguments[0];if(4===n.type)return At(n,t);if(14===n.type)return Bt(n,t)}return 0}function Ft(e,t){let n=3;const o=jt(e);if(o&&15===o.type){const{properties:e}=o;for(let o=0;o<e.length;o++){const{key:r,value:s}=e[o],i=At(r,t);if(0===i)return i;let c;if(i<n&&(n=i),c=4===s.type?At(s,t):14===s.type?Bt(s,t):0,0===c)return c;c<n&&(n=c)}}return n}function jt(e){const t=e.codegenNode;if(13===t.type)return t.props}function Ht(e){const t=e.patchFlag;return t?parseInt(t,10):void 0}function Wt(e,{filename:t="",prefixIdentifiers:n=!1,hoistStatic:o=!1,cacheHandlers:r=!1,nodeTransforms:s=[],directiveTransforms:i={},transformHoist:a=null,isBuiltInComponent:p=l,isCustomElement:u=l,expressionPlugins:f=[],scopeId:d=null,slotted:m=!0,ssr:g=!1,inSSR:y=!1,ssrCssVars:v="",bindingMetadata:S=c,inline:E=!1,isTS:N=!1,onError:x=T,onWarn:k=O,compatConfig:C}){const I=t.replace(/\?.*$/,"").match(/([^/\\]+)\.\w+$/),M={selfName:I&&_(b(I[1])),prefixIdentifiers:n,hoistStatic:o,cacheHandlers:r,nodeTransforms:s,directiveTransforms:i,transformHoist:a,isBuiltInComponent:p,isCustomElement:u,expressionPlugins:f,scopeId:d,slotted:m,ssr:g,inSSR:y,ssrCssVars:v,bindingMetadata:S,inline:E,isTS:N,onError:x,onWarn:k,compatConfig:C,root:e,helpers:new Map,components:new Set,directives:new Set,hoists:[],imports:[],constantCache:new Map,temps:0,cached:0,identifiers:Object.create(null),scopes:{vFor:0,vSlot:0,vPre:0,vOnce:0},parent:null,currentNode:e,childIndex:0,inVOnce:!1,helper(e){const t=M.helpers.get(e)||0;return M.helpers.set(e,t+1),e},removeHelper(e){const t=M.helpers.get(e);if(t){const n=t-1;n?M.helpers.set(e,n):M.helpers.delete(e)}},helperString:e=>`_${de[M.helper(e)]}`,replaceNode(e){M.parent.children[M.childIndex]=M.currentNode=e},removeNode(e){const t=e?M.parent.children.indexOf(e):M.currentNode?M.childIndex:-1;e&&e!==M.currentNode?M.childIndex>t&&(M.childIndex--,M.onNodeRemoved()):(M.currentNode=null,M.onNodeRemoved()),M.parent.children.splice(t,1)},onNodeRemoved:()=>{},addIdentifiers(e){},removeIdentifiers(e){},hoist(e){h(e)&&(e=Ee(e)),M.hoists.push(e);const t=Ee(`_hoisted_${M.hoists.length}`,!1,e.loc,2);return t.hoisted=e,t},cache:(e,t=!1)=>Oe(M.cached++,e,t)};return M.filters=new Set,M}function Kt(e,t){const n=Wt(e,t);Ut(e,n),t.hoistStatic&&$t(e,n),t.ssr||function(e,t){const{helper:n}=t,{children:o}=e;if(1===o.length){const n=o[0];if(Lt(e,n)&&n.codegenNode){const o=n.codegenNode;13===o.type&&ot(o,t),e.codegenNode=o}else e.codegenNode=n}else if(o.length>1){let o=64;e.codegenNode=ye(t,n(C),void 0,e.children,o+"",void 0,void 0,!0,void 0,!1)}}(e,n),e.helpers=[...n.helpers.keys()],e.components=[...n.components],e.directives=[...n.directives],e.imports=n.imports,e.hoists=n.hoists,e.temps=n.temps,e.cached=n.cached,e.filters=[...n.filters]}function Ut(e,t){t.currentNode=e;const{nodeTransforms:n}=t,o=[];for(let s=0;s<n.length;s++){const r=n[s](e,t);if(r&&(d(r)?o.push(...r):o.push(r)),!t.currentNode)return;e=t.currentNode}switch(e.type){case 3:t.ssr||t.helper(D);break;case 5:t.ssr||t.helper(Y);break;case 9:for(let n=0;n<e.branches.length;n++)Ut(e.branches[n],t);break;case 10:case 11:case 1:case 0:!function(e,t){let n=0;const o=()=>{n--};for(;n<e.children.length;n++){const r=e.children[n];h(r)||(t.parent=e,t.childIndex=n,t.onNodeRemoved=o,Ut(r,t))}}(e,t)}t.currentNode=e;let r=o.length;for(;r--;)o[r]()}function Jt(e,t){const n=h(e)?t=>t===e:t=>e.test(t);return(e,o)=>{if(1===e.type){const{props:r}=e;if(3===e.tagType&&r.some(Ge))return;const s=[];for(let i=0;i<r.length;i++){const c=r[i];if(7===c.type&&n(c.name)){r.splice(i,1),i--;const n=t(e,c,o);n&&s.push(n)}}return s}}}const Gt="/*#__PURE__*/";function zt(e,t={}){const n=function(e,{mode:t="function",prefixIdentifiers:n="module"===t,sourceMap:o=!1,filename:r="template.vue.html",scopeId:s=null,optimizeImports:i=!1,runtimeGlobalName:c="Vue",runtimeModuleName:l="vue",ssrRuntimeModuleName:a="vue/server-renderer",ssr:p=!1,isTS:u=!1,inSSR:f=!1}){const d={mode:t,prefixIdentifiers:n,sourceMap:o,filename:r,scopeId:s,optimizeImports:i,runtimeGlobalName:c,runtimeModuleName:l,ssrRuntimeModuleName:a,ssr:p,isTS:u,inSSR:f,source:e.loc.source,code:"",column:1,line:1,offset:0,indentLevel:0,pure:!1,map:void 0,helper:e=>`_${de[e]}`,push(e,t){d.code+=e},indent(){h(++d.indentLevel)},deindent(e=!1){e?--d.indentLevel:h(--d.indentLevel)},newline(){h(d.indentLevel)}};function h(e){d.push("\n"+"  ".repeat(e))}return d}(e,t);t.onContextCreated&&t.onContextCreated(n);const{mode:o,push:r,prefixIdentifiers:s,indent:i,deindent:c,newline:l,ssr:a}=n,p=e.helpers.length>0,u=!s&&"module"!==o;!function(e,t){const{push:n,newline:o,runtimeGlobalName:r}=t,s=r,i=e=>`${de[e]}: _${de[e]}`;if(e.helpers.length>0&&(n(`const _Vue = ${s}\n`),e.hoists.length)){n(`const { ${[V,A,D,B,F].filter((t=>e.helpers.includes(t))).map(i).join(", ")} } = _Vue\n`)}(function(e,t){if(!e.length)return;t.pure=!0;const{push:n,newline:o}=t;o();for(let r=0;r<e.length;r++){const s=e[r];s&&(n(`const _hoisted_${r+1} = `),Xt(s,t),o())}t.pure=!1})(e.hoists,t),o(),n("return ")}(e,n);if(r(`function ${a?"ssrRender":"render"}(${(a?["_ctx","_push","_parent","_attrs"]:["_ctx","_cache"]).join(", ")}) {`),i(),u&&(r("with (_ctx) {"),i(),p&&(r(`const { ${e.helpers.map((e=>`${de[e]}: _${de[e]}`)).join(", ")} } = _Vue`),r("\n"),l())),e.components.length&&(Yt(e.components,"component",n),(e.directives.length||e.temps>0)&&l()),e.directives.length&&(Yt(e.directives,"directive",n),e.temps>0&&l()),e.filters&&e.filters.length&&(l(),Yt(e.filters,"filter",n),l()),e.temps>0){r("let ");for(let t=0;t<e.temps;t++)r(`${t>0?", ":""}_temp${t}`)}return(e.components.length||e.directives.length||e.temps)&&(r("\n"),l()),a||r("return "),e.codegenNode?Xt(e.codegenNode,n):r("null"),u&&(c(),r("}")),c(),r("}"),{ast:e,code:n.code,preamble:"",map:n.map?n.map.toJSON():void 0}}function Yt(e,t,{helper:n,push:o,newline:r,isTS:s}){const i=n("filter"===t?K:"component"===t?j:W);for(let c=0;c<e.length;c++){let n=e[c];const l=n.endsWith("__self");l&&(n=n.slice(0,-6)),o(`const ${tt(n,t)} = ${i}(${JSON.stringify(n)}${l?", true":""})${s?"!":""}`),c<e.length-1&&r()}}function qt(e,t){const n=e.length>3||!1;t.push("["),n&&t.indent(),Zt(e,t,n),n&&t.deindent(),t.push("]")}function Zt(e,t,n=!1,o=!0){const{push:r,newline:s}=t;for(let i=0;i<e.length;i++){const c=e[i];h(c)?r(c):d(c)?qt(c,t):Xt(c,t),i<e.length-1&&(n?(o&&r(","),s()):o&&r(", "))}}function Xt(e,t){if(h(e))t.push(e);else if(m(e))t.push(t.helper(e));else switch(e.type){case 1:case 9:case 11:case 12:Xt(e.codegenNode,t);break;case 2:!function(e,t){t.push(JSON.stringify(e.content),e)}(e,t);break;case 4:Qt(e,t);break;case 5:!function(e,t){const{push:n,helper:o,pure:r}=t;r&&n(Gt);n(`${o(Y)}(`),Xt(e.content,t),n(")")}(e,t);break;case 8:en(e,t);break;case 3:!function(e,t){const{push:n,helper:o,pure:r}=t;r&&n(Gt);n(`${o(D)}(${JSON.stringify(e.content)})`,e)}(e,t);break;case 13:!function(e,t){const{push:n,helper:o,pure:r}=t,{tag:s,props:i,children:c,patchFlag:l,dynamicProps:a,directives:p,isBlock:u,disableTracking:f,isComponent:d}=e;p&&n(o(U)+"(");u&&n(`(${o(w)}(${f?"true":""}), `);r&&n(Gt);const h=u?Ze(t.inSSR,d):qe(t.inSSR,d);n(o(h)+"(",e),Zt(function(e){let t=e.length;for(;t--&&null==e[t];);return e.slice(0,t+1).map((e=>e||"null"))}([s,i,c,l,a]),t),n(")"),u&&n(")");p&&(n(", "),Xt(p,t),n(")"))}(e,t);break;case 14:!function(e,t){const{push:n,helper:o,pure:r}=t,s=h(e.callee)?e.callee:o(e.callee);r&&n(Gt);n(s+"(",e),Zt(e.arguments,t),n(")")}(e,t);break;case 15:!function(e,t){const{push:n,indent:o,deindent:r,newline:s}=t,{properties:i}=e;if(!i.length)return void n("{}",e);const c=i.length>1||!1;n(c?"{":"{ "),c&&o();for(let l=0;l<i.length;l++){const{key:e,value:o}=i[l];tn(e,t),n(": "),Xt(o,t),l<i.length-1&&(n(","),s())}c&&r(),n(c?"}":" }")}(e,t);break;case 17:!function(e,t){qt(e.elements,t)}(e,t);break;case 18:!function(e,t){const{push:n,indent:o,deindent:r}=t,{params:s,returns:i,body:c,newline:l,isSlot:a}=e;a&&n(`_${de[le]}(`);n("(",e),d(s)?Zt(s,t):s&&Xt(s,t);n(") => "),(l||c)&&(n("{"),o());i?(l&&n("return "),d(i)?qt(i,t):Xt(i,t)):c&&Xt(c,t);(l||c)&&(r(),n("}"));a&&(e.isNonScopedSlot&&n(", undefined, true"),n(")"))}(e,t);break;case 19:!function(e,t){const{test:n,consequent:o,alternate:r,newline:s}=e,{push:i,indent:c,deindent:l,newline:a}=t;if(4===n.type){const e=!Pe(n.content);e&&i("("),Qt(n,t),e&&i(")")}else i("("),Xt(n,t),i(")");s&&c(),t.indentLevel++,s||i(" "),i("? "),Xt(o,t),t.indentLevel--,s&&a(),s||i(" "),i(": ");const p=19===r.type;p||t.indentLevel++;Xt(r,t),p||t.indentLevel--;s&&l(!0)}(e,t);break;case 20:!function(e,t){const{push:n,helper:o,indent:r,deindent:s,newline:i}=t;n(`_cache[${e.index}] || (`),e.isVNode&&(r(),n(`${o(se)}(-1),`),i());n(`_cache[${e.index}] = `),Xt(e.value,t),e.isVNode&&(n(","),i(),n(`${o(se)}(1),`),i(),n(`_cache[${e.index}]`),s());n(")")}(e,t);break;case 21:Zt(e.body,t,!0,!1)}}function Qt(e,t){const{content:n,isStatic:o}=e;t.push(o?JSON.stringify(n):n,e)}function en(e,t){for(let n=0;n<e.children.length;n++){const o=e.children[n];h(o)?t.push(o):Xt(o,t)}}function tn(e,t){const{push:n}=t;if(8===e.type)n("["),en(e,t),n("]");else if(e.isStatic){n(Pe(e.content)?e.content:JSON.stringify(e.content),e)}else n(`[${e.content}]`,e)}function nn(e,t=[]){switch(e.type){case"Identifier":t.push(e);break;case"MemberExpression":let n=e;for(;"MemberExpression"===n.type;)n=n.object;t.push(n);break;case"ObjectPattern":for(const o of e.properties)nn("RestElement"===o.type?o.argument:o.value,t);break;case"ArrayPattern":e.elements.forEach((e=>{e&&nn(e,t)}));break;case"RestElement":nn(e.argument,t);break;case"AssignmentPattern":nn(e.left,t)}return t}const on=e=>e&&("ObjectProperty"===e.type||"ObjectMethod"===e.type)&&!e.computed;function rn(e,t,n=!1,o=!1,r=Object.create(t.identifiers)){return e}const sn=Jt(/^(if|else|else-if)$/,((e,t,n)=>cn(e,t,n,((e,t,o)=>{const r=n.parent.children;let s=r.indexOf(e),i=0;for(;s-- >=0;){const e=r[s];e&&9===e.type&&(i+=e.branches.length)}return()=>{if(o)e.codegenNode=an(t,i,n);else{const o=function(e){for(;;)if(19===e.type){if(19!==e.alternate.type)return e;e=e.alternate}else 20===e.type&&(e=e.value)}(e.codegenNode);o.alternate=an(t,i+e.branches.length-1,n)}}}))));function cn(e,t,n,o){if(!("else"===t.name||t.exp&&t.exp.content.trim())){t.exp=Ee("true",!1,t.exp?t.exp.loc:e.loc)}if("if"===t.name){const r=ln(e,t),s={type:9,loc:e.loc,branches:[r]};if(n.replaceNode(s),o)return o(s,r,!0)}else{const r=n.parent.children;let s=r.indexOf(e);for(;s-- >=-1;){const i=r[s];if(!i||2!==i.type||i.content.trim().length){if(i&&9===i.type){n.removeNode();const r=ln(e,t);i.branches.push(r);const s=o&&o(i,r,!1);Ut(r,n),s&&s(),n.currentNode=null}break}n.removeNode(i)}}}function ln(e,t){return{type:10,loc:e.loc,condition:"else"===t.name?void 0:t.exp,children:3!==e.tagType||He(e,"for")?[e]:e.children,userKey:We(e,"key")}}function an(e,t,n){return e.condition?Te(e.condition,pn(e,t,n),_e(n.helper(D),['""',"true"])):pn(e,t,n)}function pn(e,t,n){const{helper:o}=n,r=be("key",Ee(`${t}`,!1,me,2)),{children:s}=e,i=s[0];if(1!==s.length||1!==i.type){if(1===s.length&&11===i.type){const e=i.codegenNode;return et(e,r,n),e}{let t=64;return ye(n,o(C),Se([r]),s,t+"",void 0,void 0,!0,!1,!1,e.loc)}}{const e=i.codegenNode,t=nt(e);return 13===t.type&&ot(t,n),et(t,r,n),e}}const un=Jt("for",((e,t,n)=>{const{helper:o,removeHelper:r}=n;return fn(e,t,n,(t=>{const s=_e(o(J),[t.source]),i=He(e,"memo"),c=We(e,"key"),l=c&&(6===c.type?Ee(c.value.content,!0):c.exp),a=c?be("key",l):null,p=4===t.source.type&&t.source.constType>0,u=p?64:c?128:256;return t.codegenNode=ye(n,o(C),void 0,s,u+"",void 0,void 0,!0,!p,!1,e.loc),()=>{let c;const u=ze(e),{children:f}=t,d=1!==f.length||1!==f[0].type,h=Ye(e)?e:u&&1===e.children.length&&Ye(e.children[0])?e.children[0]:null;if(h?(c=h.codegenNode,u&&a&&et(c,a,n)):d?c=ye(n,o(C),a?Se([a]):void 0,e.children,"64",void 0,void 0,!0,void 0,!1):(c=f[0].codegenNode,u&&a&&et(c,a,n),c.isBlock!==!p&&(c.isBlock?(r(w),r(Ze(n.inSSR,c.isComponent))):r(qe(n.inSSR,c.isComponent))),c.isBlock=!p,c.isBlock?(o(w),o(Ze(n.inSSR,c.isComponent))):o(qe(n.inSSR,c.isComponent))),i){const e=xe(vn(t.parseResult,[Ee("_cached")]));e.body=ke([Ne(["const _memo = (",i.exp,")"]),Ne(["if (_cached",...l?[" && _cached.key === ",l]:[],` && ${n.helperString(fe)}(_cached, _memo)) return _cached`]),Ne(["const _item = ",c]),Ee("_item.memo = _memo"),Ee("return _item")]),s.arguments.push(e,Ee("_cache"),Ee(String(n.cached++)))}else s.arguments.push(xe(vn(t.parseResult),c,!0))}}))}));function fn(e,t,n,o){if(!t.exp)return;const r=gn(t.exp);if(!r)return;const{scopes:s}=n,{source:i,value:c,key:l,index:a}=r,p={type:11,loc:t.loc,source:i,valueAlias:c,keyAlias:l,objectIndexAlias:a,parseResult:r,children:ze(e)?e.children:[e]};n.replaceNode(p),s.vFor++;const u=o&&o(p);return()=>{s.vFor--,u&&u()}}const dn=/([\s\S]*?)\s+(?:in|of)\s+([\s\S]*)/,hn=/,([^,\}\]]*)(?:,([^,\}\]]*))?$/,mn=/^\(|\)$/g;function gn(e,t){const n=e.loc,o=e.content,r=o.match(dn);if(!r)return;const[,s,i]=r,c={source:yn(n,i.trim(),o.indexOf(i,s.length)),value:void 0,key:void 0,index:void 0};let l=s.trim().replace(mn,"").trim();const a=s.indexOf(l),p=l.match(hn);if(p){l=l.replace(hn,"").trim();const e=p[1].trim();let t;if(e&&(t=o.indexOf(e,a+l.length),c.key=yn(n,e,t)),p[2]){const r=p[2].trim();r&&(c.index=yn(n,r,o.indexOf(r,c.key?t+e.length:a+l.length)))}}return l&&(c.value=yn(n,l,a)),c}function yn(e,t,n){return Ee(t,!1,Be(e,n,t.length))}function vn({value:e,key:t,index:n},o=[]){return function(e){let t=e.length;for(;t--&&!e[t];);return e.slice(0,t+1).map(((e,t)=>e||Ee("_".repeat(t+1),!1)))}([e,t,n,...o])}const Sn=Ee("undefined",!1),bn=(e,t)=>{if(1===e.type&&(1===e.tagType||3===e.tagType)){const n=He(e,"slot");if(n)return t.scopes.vSlot++,()=>{t.scopes.vSlot--}}},En=(e,t,n)=>xe(e,t,!1,!0,t.length?t[0].loc:n);function Nn(e,t,n=En){t.helper(le);const{children:o,loc:r}=e,s=[],i=[];let c=t.scopes.vSlot>0||t.scopes.vFor>0;const l=He(e,"slot",!0);if(l){const{arg:e,exp:t}=l;e&&!Ce(e)&&(c=!0),s.push(be(e||Ee("default",!0),n(t,o,r)))}let a=!1,p=!1;const u=[],f=new Set;for(let m=0;m<o.length;m++){const e=o[m];let r;if(!ze(e)||!(r=He(e,"slot",!0))){3!==e.type&&u.push(e);continue}if(l)break;a=!0;const{children:d,loc:h}=e,{arg:g=Ee("default",!0),exp:y}=r;let v;Ce(g)?v=g?g.content:"default":c=!0;const S=n(y,d,h);let b,E,N;if(b=He(e,"if"))c=!0,i.push(Te(b.exp,_n(g,S),Sn));else if(E=He(e,/^else(-if)?$/,!0)){let e,t=m;for(;t--&&(e=o[t],3===e.type););if(e&&ze(e)&&He(e,"if")){o.splice(m,1),m--;let e=i[i.length-1];for(;19===e.alternate.type;)e=e.alternate;e.alternate=E.exp?Te(E.exp,_n(g,S),Sn):_n(g,S)}}else if(N=He(e,"for")){c=!0;const e=N.parseResult||gn(N.exp);e&&i.push(_e(t.helper(J),[e.source,xe(vn(e),_n(g,S),!0)]))}else{if(v){if(f.has(v))continue;f.add(v),"default"===v&&(p=!0)}s.push(be(g,S))}}if(!l){const e=(e,o)=>{const s=n(e,o,r);return t.compatConfig&&(s.isNonScopedSlot=!0),be("default",s)};a?u.length&&u.some((e=>Tn(e)))&&(p||s.push(e(void 0,u))):s.push(e(void 0,o))}const d=c?2:xn(e.children)?3:1;let h=Se(s.concat(be("_",Ee(d+"",!1))),r);return i.length&&(h=_e(t.helper(z),[h,ve(i)])),{slots:h,hasDynamicSlots:c}}function _n(e,t){return Se([be("name",e),be("fn",t)])}function xn(e){for(let t=0;t<e.length;t++){const n=e[t];switch(n.type){case 1:if(2===n.tagType||xn(n.children))return!0;break;case 9:if(xn(n.branches))return!0;break;case 10:case 11:if(xn(n.children))return!0}}return!1}function Tn(e){return 2!==e.type&&12!==e.type||(2===e.type?!!e.content.trim():Tn(e.content))}const On=new WeakMap,kn=(e,t)=>function(){if(1!==(e=t.currentNode).type||0!==e.tagType&&1!==e.tagType)return;const{tag:n,props:o}=e,r=1===e.tagType;let s=r?Cn(e,t):`"${n}"`;let i,c,l,a,p,u,f=0,d=g(s)&&s.callee===H||s===I||s===M||!r&&("svg"===n||"foreignObject"===n);if(o.length>0){const n=In(e,t);i=n.props,f=n.patchFlag,p=n.dynamicPropNames;const o=n.directives;u=o&&o.length?ve(o.map((e=>function(e,t){const n=[],o=On.get(e);o?n.push(t.helperString(o)):(t.helper(W),t.directives.add(e.name),n.push(tt(e.name,"directive")));const{loc:r}=e;e.exp&&n.push(e.exp);e.arg&&(e.exp||n.push("void 0"),n.push(e.arg));if(Object.keys(e.modifiers).length){e.arg||(e.exp||n.push("void 0"),n.push("void 0"));const t=Ee("true",!1,r);n.push(Se(e.modifiers.map((e=>be(e,t))),r))}return ve(n,e.loc)}(e,t)))):void 0,n.shouldUseBlock&&(d=!0)}if(e.children.length>0){s===R&&(d=!0,f|=1024);if(r&&s!==I&&s!==R){const{slots:n,hasDynamicSlots:o}=Nn(e,t);c=n,o&&(f|=1024)}else if(1===e.children.length&&s!==I){const n=e.children[0],o=n.type,r=5===o||8===o;r&&0===At(n,t)&&(f|=1),c=r||2===o?n:e.children}else c=e.children}0!==f&&(l=String(f),p&&p.length&&(a=function(e){let t="[";for(let n=0,o=e.length;n<o;n++)t+=JSON.stringify(e[n]),n<o-1&&(t+=", ");return t+"]"}(p))),e.codegenNode=ye(t,s,i,c,l,a,u,!!d,!1,r,e.loc)};function Cn(e,t,n=!1){let{tag:o}=e;const r=Pn(o),s=We(e,"is");if(s)if(r||it("COMPILER_IS_ON_ELEMENT",t)){const e=6===s.type?s.value&&Ee(s.value.content,!0):s.exp;if(e)return _e(t.helper(H),[e])}else 6===s.type&&s.value.content.startsWith("vue:")&&(o=s.value.content.slice(4));const i=!r&&He(e,"is");if(i&&i.exp)return _e(t.helper(H),[i.exp]);const c=Me(o)||t.isBuiltInComponent(o);return c?(n||t.helper(c),c):(t.helper(j),t.components.add(o),tt(o,"component"))}function In(e,t,n=e.props,o=!1){const{tag:r,loc:s,children:i}=e,c=1===e.tagType;let l=[];const a=[],p=[],f=i.length>0;let d=!1,h=0,g=!1,v=!1,S=!1,b=!1,E=!1,N=!1;const _=[],x=({key:e,value:n})=>{if(Ce(e)){const o=e.content,r=u(o);if(c||!r||"onclick"===o.toLowerCase()||"onUpdate:modelValue"===o||y(o)||(b=!0),r&&y(o)&&(N=!0),20===n.type||(4===n.type||8===n.type)&&At(n,t)>0)return;"ref"===o?g=!0:"class"===o?v=!0:"style"===o?S=!0:"key"===o||_.includes(o)||_.push(o),!c||"class"!==o&&"style"!==o||_.includes(o)||_.push(o)}else E=!0};for(let u=0;u<n.length;u++){const i=n[u];if(6===i.type){const{loc:e,name:n,value:o}=i;let s=!0;if("ref"===n&&(g=!0,t.scopes.vFor>0&&l.push(be(Ee("ref_for",!0),Ee("true")))),"is"===n&&(Pn(r)||o&&o.content.startsWith("vue:")||it("COMPILER_IS_ON_ELEMENT",t)))continue;l.push(be(Ee(n,!0,Be(e,0,n.length)),Ee(o?o.content:"",s,o?o.loc:e)))}else{const{name:n,arg:c,exp:u,loc:h}=i,g="bind"===n,y="on"===n;if("slot"===n)continue;if("once"===n||"memo"===n)continue;if("is"===n||g&&Ke(c,"is")&&(Pn(r)||it("COMPILER_IS_ON_ELEMENT",t)))continue;if(y&&o)continue;if((g&&Ke(c,"key")||y&&f&&Ke(c,"vue:before-update"))&&(d=!0),g&&Ke(c,"ref")&&t.scopes.vFor>0&&l.push(be(Ee("ref_for",!0),Ee("true"))),!c&&(g||y)){if(E=!0,u)if(l.length&&(a.push(Se(Mn(l),s)),l=[]),g){if(it("COMPILER_V_BIND_OBJECT_ORDER",t)){a.unshift(u);continue}a.push(u)}else a.push({type:14,loc:h,callee:t.helper(te),arguments:[u]});continue}const v=t.directiveTransforms[n];if(v){const{props:n,needRuntime:r}=v(i,e,t);!o&&n.forEach(x),l.push(...n),r&&(p.push(i),m(r)&&On.set(i,r))}else p.push(i),f&&(d=!0)}}let T;if(a.length?(l.length&&a.push(Se(Mn(l),s)),T=a.length>1?_e(t.helper(q),a,s):a[0]):l.length&&(T=Se(Mn(l),s)),E?h|=16:(v&&!c&&(h|=2),S&&!c&&(h|=4),_.length&&(h|=8),b&&(h|=32)),d||0!==h&&32!==h||!(g||N||p.length>0)||(h|=512),!t.inSSR&&T)switch(T.type){case 15:let e=-1,n=-1,o=!1;for(let t=0;t<T.properties.length;t++){const r=T.properties[t].key;Ce(r)?"class"===r.content?e=t:"style"===r.content&&(n=t):r.isHandlerKey||(o=!0)}const r=T.properties[e],s=T.properties[n];o?T=_e(t.helper(Q),[T]):(r&&!Ce(r.value)&&(r.value=_e(t.helper(Z),[r.value])),!s||Ce(s.value)||!S&&17!==s.value.type||(s.value=_e(t.helper(X),[s.value])));break;case 14:break;default:T=_e(t.helper(Q),[_e(t.helper(ee),[T])])}return{props:T,directives:p,patchFlag:h,dynamicPropNames:_,shouldUseBlock:d}}function Mn(e){const t=new Map,n=[];for(let o=0;o<e.length;o++){const r=e[o];if(8===r.key.type||!r.key.isStatic){n.push(r);continue}const s=r.key.content,i=t.get(s);i?("style"===s||"class"===s||u(s))&&Rn(i,r):(t.set(s,r),n.push(r))}return n}function Rn(e,t){17===e.value.type?e.value.elements.push(t.value):e.value=ve([e.value,t.value],e.loc)}function Pn(e){return"component"===e||"Component"===e}const wn=(e,t)=>{if(Ye(e)){const{children:n,loc:o}=e,{slotName:r,slotProps:s}=$n(e,t),i=[t.prefixIdentifiers?"_ctx.$slots":"$slots",r,"{}","undefined","true"];let c=2;s&&(i[2]=s,c=3),n.length&&(i[3]=xe([],n,!1,!1,o),c=4),t.scopeId&&!t.slotted&&(c=5),i.splice(c),e.codegenNode=_e(t.helper(G),i,o)}};function $n(e,t){let n,o='"default"';const r=[];for(let s=0;s<e.props.length;s++){const t=e.props[s];6===t.type?t.value&&("name"===t.name?o=JSON.stringify(t.value.content):(t.name=b(t.name),r.push(t))):"bind"===t.name&&Ke(t.arg,"name")?t.exp&&(o=t.exp):("bind"===t.name&&t.arg&&Ce(t.arg)&&(t.arg.content=b(t.arg.content)),r.push(t))}if(r.length>0){const{props:o,directives:s}=In(e,t,r);n=o}return{slotName:o,slotProps:n}}const Ln=/^\s*([\w$_]+|(async\s*)?\([^)]*?\))\s*=>|^\s*(async\s+)?function(?:\s+[\w$]+)?\s*\(/,Vn=(e,t,n,o)=>{const{loc:r,modifiers:s,arg:i}=e;let c;if(4===i.type)if(i.isStatic){let e=i.content;e.startsWith("vue:")&&(e=`vnode-${e.slice(4)}`),c=Ee(x(b(e)),!0,i.loc)}else c=Ne([`${n.helperString(re)}(`,i,")"]);else c=i,c.children.unshift(`${n.helperString(re)}(`),c.children.push(")");let l=e.exp;l&&!l.content.trim()&&(l=void 0);let a=n.cacheHandlers&&!l&&!n.inVOnce;if(l){const e=De(l.content),t=!(e||Ln.test(l.content)),n=l.content.includes(";");(t||a&&e)&&(l=Ne([`${t?"$event":"(...args)"} => ${n?"{":"("}`,l,n?"}":")"]))}let p={props:[be(c,l||Ee("() => {}",!1,r))]};return o&&(p=o(p)),a&&(p.props[0].value=n.cache(p.props[0].value)),p.props.forEach((e=>e.key.isHandlerKey=!0)),p},An=(e,t,n)=>{const{exp:o,modifiers:r,loc:s}=e,i=e.arg;return 4!==i.type?(i.children.unshift("("),i.children.push(') || ""')):i.isStatic||(i.content=`${i.content} || ""`),r.includes("camel")&&(4===i.type?i.content=i.isStatic?b(i.content):`${n.helperString(ne)}(${i.content})`:(i.children.unshift(`${n.helperString(ne)}(`),i.children.push(")"))),n.inSSR||(r.includes("prop")&&Dn(i,"."),r.includes("attr")&&Dn(i,"^")),!o||4===o.type&&!o.content.trim()?{props:[be(i,Ee("",!0,s))]}:{props:[be(i,o)]}},Dn=(e,t)=>{4===e.type?e.content=e.isStatic?t+e.content:`\`${t}\${${e.content}}\``:(e.children.unshift(`'${t}' + (`),e.children.push(")"))},Bn=(e,t)=>{if(0===e.type||1===e.type||11===e.type||10===e.type)return()=>{const n=e.children;let o,r=!1;for(let e=0;e<n.length;e++){const t=n[e];if(Je(t)){r=!0;for(let r=e+1;r<n.length;r++){const s=n[r];if(!Je(s)){o=void 0;break}o||(o=n[e]={type:8,loc:t.loc,children:[t]}),o.children.push(" + ",s),n.splice(r,1),r--}}}if(r&&(1!==n.length||0!==e.type&&(1!==e.type||0!==e.tagType||e.props.find((e=>7===e.type&&!t.directiveTransforms[e.name]))||"template"===e.tag)))for(let e=0;e<n.length;e++){const o=n[e];if(Je(o)||8===o.type){const r=[];2===o.type&&" "===o.content||r.push(o),t.ssr||0!==At(o,t)||r.push("1"),n[e]={type:12,content:o,loc:o.loc,codegenNode:_e(t.helper(B),r)}}}}},Fn=new WeakSet,jn=(e,t)=>{if(1===e.type&&He(e,"once",!0)){if(Fn.has(e)||t.inVOnce)return;return Fn.add(e),t.inVOnce=!0,t.helper(se),()=>{t.inVOnce=!1;const e=t.currentNode;e.codegenNode&&(e.codegenNode=t.cache(e.codegenNode,!0))}}},Hn=(e,t,n)=>{const{exp:o,arg:r}=e;if(!o)return Wn();const s=o.loc.source,i=4===o.type?o.content:s;if(!i.trim()||!De(i))return Wn();const c=r||Ee("modelValue",!0),l=r?Ce(r)?`onUpdate:${r.content}`:Ne(['"onUpdate:" + ',r]):"onUpdate:modelValue";let a;a=Ne([`${n.isTS?"($event: any)":"$event"} => ((`,o,") = $event)"]);const p=[be(c,e.exp),be(l,a)];if(e.modifiers.length&&1===t.tagType){const t=e.modifiers.map((e=>(Pe(e)?e:JSON.stringify(e))+": true")).join(", "),n=r?Ce(r)?`${r.content}Modifiers`:Ne([r,' + "Modifiers"']):"modelModifiers";p.push(be(n,Ee(`{ ${t} }`,!1,e.loc,2)))}return Wn(p)};function Wn(e=[]){return{props:e}}const Kn=/[\w).+\-_$\]]/,Un=(e,t)=>{it("COMPILER_FILTER",t)&&(5===e.type&&Jn(e.content,t),1===e.type&&e.props.forEach((e=>{7===e.type&&"for"!==e.name&&e.exp&&Jn(e.exp,t)})))};function Jn(e,t){if(4===e.type)Gn(e,t);else for(let n=0;n<e.children.length;n++){const o=e.children[n];"object"==typeof o&&(4===o.type?Gn(o,t):8===o.type?Jn(e,t):5===o.type&&Jn(o.content,t))}}function Gn(e,t){const n=e.content;let o,r,s,i,c=!1,l=!1,a=!1,p=!1,u=0,f=0,d=0,h=0,m=[];for(s=0;s<n.length;s++)if(r=o,o=n.charCodeAt(s),c)39===o&&92!==r&&(c=!1);else if(l)34===o&&92!==r&&(l=!1);else if(a)96===o&&92!==r&&(a=!1);else if(p)47===o&&92!==r&&(p=!1);else if(124!==o||124===n.charCodeAt(s+1)||124===n.charCodeAt(s-1)||u||f||d){switch(o){case 34:l=!0;break;case 39:c=!0;break;case 96:a=!0;break;case 40:d++;break;case 41:d--;break;case 91:f++;break;case 93:f--;break;case 123:u++;break;case 125:u--}if(47===o){let e,t=s-1;for(;t>=0&&(e=n.charAt(t)," "===e);t--);e&&Kn.test(e)||(p=!0)}}else void 0===i?(h=s+1,i=n.slice(0,s).trim()):g();function g(){m.push(n.slice(h,s).trim()),h=s+1}if(void 0===i?i=n.slice(0,s).trim():0!==h&&g(),m.length){for(s=0;s<m.length;s++)i=zn(i,m[s],t);e.content=i}}function zn(e,t,n){n.helper(K);const o=t.indexOf("(");if(o<0)return n.filters.add(t),`${tt(t,"filter")}(${e})`;{const r=t.slice(0,o),s=t.slice(o+1);return n.filters.add(r),`${tt(r,"filter")}(${e}${")"!==s?","+s:s}`}}const Yn=new WeakSet,qn=(e,t)=>{if(1===e.type){const n=He(e,"memo");if(!n||Yn.has(e))return;return Yn.add(e),()=>{const o=e.codegenNode||t.currentNode.codegenNode;o&&13===o.type&&(1!==e.tagType&&ot(o,t),e.codegenNode=_e(t.helper(ue),[n.exp,xe(void 0,o),"_cache",String(t.cached++)]))}}};function Zn(e){return[[jn,sn,qn,un,Un,wn,kn,bn,Bn],{on:Vn,bind:An,model:Hn}]}function Xn(e,t={}){const n=t.onError||T,o="module"===t.mode;!0===t.prefixIdentifiers?n(k(46)):o&&n(k(47));t.cacheHandlers&&n(k(48)),t.scopeId&&!o&&n(k(49));const r=h(e)?ut(e,t):e,[s,i]=Zn();return Kt(r,f({},t,{prefixIdentifiers:false,nodeTransforms:[...s,...t.nodeTransforms||[]],directiveTransforms:f({},i,t.directiveTransforms||{})})),zt(r,f({},t,{prefixIdentifiers:false}))}const Qn=()=>({props:[]}),eo=Symbol(""),to=Symbol(""),no=Symbol(""),oo=Symbol(""),ro=Symbol(""),so=Symbol(""),io=Symbol(""),co=Symbol(""),lo=Symbol(""),ao=Symbol("");let po;he({[eo]:"vModelRadio",[to]:"vModelCheckbox",[no]:"vModelText",[oo]:"vModelSelect",[ro]:"vModelDynamic",[so]:"withModifiers",[io]:"withKeys",[co]:"vShow",[lo]:"Transition",[ao]:"TransitionGroup"});const uo=t("style,iframe,script,noscript",!0),fo={isVoidTag:i,isNativeTag:e=>r(e)||s(e),isPreTag:e=>"pre"===e,decodeEntities:function(e,t=!1){return po||(po=document.createElement("div")),t?(po.innerHTML=`<div foo="${e.replace(/"/g,"&quot;")}">`,po.children[0].getAttribute("foo")):(po.innerHTML=e,po.textContent)},isBuiltInComponent:e=>Ie(e,"Transition")?lo:Ie(e,"TransitionGroup")?ao:void 0,getNamespace(e,t){let n=t?t.ns:0;if(t&&2===n)if("annotation-xml"===t.tag){if("svg"===e)return 1;t.props.some((e=>6===e.type&&"encoding"===e.name&&null!=e.value&&("text/html"===e.value.content||"application/xhtml+xml"===e.value.content)))&&(n=0)}else/^m(?:[ions]|text)$/.test(t.tag)&&"mglyph"!==e&&"malignmark"!==e&&(n=0);else t&&1===n&&("foreignObject"!==t.tag&&"desc"!==t.tag&&"title"!==t.tag||(n=0));if(0===n){if("svg"===e)return 1;if("math"===e)return 2}return n},getTextMode({tag:e,ns:t}){if(0===t){if("textarea"===e||"title"===e)return 1;if(uo(e))return 2}return 0}},ho=e=>{1===e.type&&e.props.forEach(((t,n)=>{6===t.type&&"style"===t.name&&t.value&&(e.props[n]={type:7,name:"bind",arg:Ee("style",!0,t.loc),exp:mo(t.value.content,t.loc),modifiers:[],loc:t.loc})}))},mo=(e,t)=>{const r=function(e){const t={};return e.split(n).forEach((e=>{if(e){const n=e.split(o);n.length>1&&(t[n[0].trim()]=n[1].trim())}})),t}(e);return Ee(JSON.stringify(r),!1,t,3)};function go(e,t){return k(e,t)}const yo=t("passive,once,capture"),vo=t("stop,prevent,self,ctrl,shift,alt,meta,exact,middle"),So=t("left,right"),bo=t("onkeyup,onkeydown,onkeypress",!0),Eo=(e,t)=>Ce(e)&&"onclick"===e.content.toLowerCase()?Ee(t,!0):4!==e.type?Ne(["(",e,`) === "onClick" ? "${t}" : (`,e,")"]):e,No=(e,t)=>{1!==e.type||0!==e.tagType||"script"!==e.tag&&"style"!==e.tag||t.removeNode()},_o=[ho],xo={cloak:Qn,html:(e,t,n)=>{const{exp:o,loc:r}=e;return t.children.length&&(t.children.length=0),{props:[be(Ee("innerHTML",!0,r),o||Ee("",!0))]}},text:(e,t,n)=>{const{exp:o,loc:r}=e;return t.children.length&&(t.children.length=0),{props:[be(Ee("textContent",!0),o?_e(n.helperString(Y),[o],r):Ee("",!0))]}},model:(e,t,n)=>{const o=Hn(e,t,n);if(!o.props.length||1===t.tagType)return o;const{tag:r}=t,s=n.isCustomElement(r);if("input"===r||"textarea"===r||"select"===r||s){let e=no,i=!1;if("input"===r||s){const n=We(t,"type");if(n){if(7===n.type)e=ro;else if(n.value)switch(n.value.content){case"radio":e=eo;break;case"checkbox":e=to;break;case"file":i=!0}}else Ue(t)&&(e=ro)}else"select"===r&&(e=oo);i||(o.needRuntime=n.helper(e))}return o.props=o.props.filter((e=>!(4===e.key.type&&"modelValue"===e.key.content))),o},on:(e,t,n)=>Vn(e,0,n,(t=>{const{modifiers:o}=e;if(!o.length)return t;let{key:r,value:s}=t.props[0];const{keyModifiers:i,nonKeyModifiers:c,eventOptionModifiers:l}=((e,t,n,o)=>{const r=[],s=[],i=[];for(let c=0;c<t.length;c++){const o=t[c];"native"===o&&ct("COMPILER_V_ON_NATIVE",n)||yo(o)?i.push(o):So(o)?Ce(e)?bo(e.content)?r.push(o):s.push(o):(r.push(o),s.push(o)):vo(o)?s.push(o):r.push(o)}return{keyModifiers:r,nonKeyModifiers:s,eventOptionModifiers:i}})(r,o,n);if(c.includes("right")&&(r=Eo(r,"onContextmenu")),c.includes("middle")&&(r=Eo(r,"onMouseup")),c.length&&(s=_e(n.helper(so),[s,JSON.stringify(c)])),!i.length||Ce(r)&&!bo(r.content)||(s=_e(n.helper(io),[s,JSON.stringify(i)])),l.length){const e=l.map(_).join("");r=Ce(r)?Ee(`${r.content}${e}`,!0):Ne(["(",r,`) + "${e}"`])}return{props:[be(r,s)]}})),show:(e,t,n)=>({props:[],needRuntime:n.helper(co)})};return e.BASE_TRANSITION=P,e.CAMELIZE=ne,e.CAPITALIZE=oe,e.CREATE_BLOCK=$,e.CREATE_COMMENT=D,e.CREATE_ELEMENT_BLOCK=L,e.CREATE_ELEMENT_VNODE=A,e.CREATE_SLOTS=z,e.CREATE_STATIC=F,e.CREATE_TEXT=B,e.CREATE_VNODE=V,e.DOMDirectiveTransforms=xo,e.DOMNodeTransforms=_o,e.FRAGMENT=C,e.GUARD_REACTIVE_PROPS=ee,e.IS_MEMO_SAME=fe,e.IS_REF=pe,e.KEEP_ALIVE=R,e.MERGE_PROPS=q,e.NORMALIZE_CLASS=Z,e.NORMALIZE_PROPS=Q,e.NORMALIZE_STYLE=X,e.OPEN_BLOCK=w,e.POP_SCOPE_ID=ce,e.PUSH_SCOPE_ID=ie,e.RENDER_LIST=J,e.RENDER_SLOT=G,e.RESOLVE_COMPONENT=j,e.RESOLVE_DIRECTIVE=W,e.RESOLVE_DYNAMIC_COMPONENT=H,e.RESOLVE_FILTER=K,e.SET_BLOCK_TRACKING=se,e.SUSPENSE=M,e.TELEPORT=I,e.TO_DISPLAY_STRING=Y,e.TO_HANDLERS=te,e.TO_HANDLER_KEY=re,e.TRANSITION=lo,e.TRANSITION_GROUP=ao,e.UNREF=ae,e.V_MODEL_CHECKBOX=to,e.V_MODEL_DYNAMIC=ro,e.V_MODEL_RADIO=eo,e.V_MODEL_SELECT=oo,e.V_MODEL_TEXT=no,e.V_ON_WITH_KEYS=io,e.V_ON_WITH_MODIFIERS=so,e.V_SHOW=co,e.WITH_CTX=le,e.WITH_DIRECTIVES=U,e.WITH_MEMO=ue,e.advancePositionWithClone=Fe,e.advancePositionWithMutation=je,e.assert=function(e,t){if(!e)throw new Error(t||"unexpected compiler condition")},e.baseCompile=Xn,e.baseParse=ut,e.buildProps=In,e.buildSlots=Nn,e.checkCompatEnabled=ct,e.compile=function(e,t={}){return Xn(e,f({},fo,t,{nodeTransforms:[No,..._o,...t.nodeTransforms||[]],directiveTransforms:f({},xo,t.directiveTransforms||{}),transformHoist:null}))},e.createArrayExpression=ve,e.createAssignmentExpression=function(e,t){return{type:24,left:e,right:t,loc:me}},e.createBlockStatement=ke,e.createCacheExpression=Oe,e.createCallExpression=_e,e.createCompilerError=k,e.createCompoundExpression=Ne,e.createConditionalExpression=Te,e.createDOMCompilerError=go,e.createForLoopParams=vn,e.createFunctionExpression=xe,e.createIfStatement=function(e,t,n){return{type:23,test:e,consequent:t,alternate:n,loc:me}},e.createInterpolation=function(e,t){return{type:5,loc:t,content:h(e)?Ee(e,!1,t):e}},e.createObjectExpression=Se,e.createObjectProperty=be,e.createReturnStatement=function(e){return{type:26,returns:e,loc:me}},e.createRoot=ge,e.createSequenceExpression=function(e){return{type:25,expressions:e,loc:me}},e.createSimpleExpression=Ee,e.createStructuralDirectiveTransform=Jt,e.createTemplateLiteral=function(e){return{type:22,elements:e,loc:me}},e.createTransformContext=Wt,e.createVNodeCall=ye,e.extractIdentifiers=nn,e.findDir=He,e.findProp=We,e.generate=zt,e.generateCodeFrame=function(e,t=0,n=e.length){let o=e.split(/(\r?\n)/);const r=o.filter(((e,t)=>t%2==1));o=o.filter(((e,t)=>t%2==0));let s=0;const i=[];for(let c=0;c<o.length;c++)if(s+=o[c].length+(r[c]&&r[c].length||0),s>=t){for(let e=c-2;e<=c+2||n>s;e++){if(e<0||e>=o.length)continue;const l=e+1;i.push(`${l}${" ".repeat(Math.max(3-String(l).length,0))}|  ${o[e]}`);const a=o[e].length,p=r[e]&&r[e].length||0;if(e===c){const e=t-(s-(a+p)),o=Math.max(1,n>s?a-e:n-t);i.push("   |  "+" ".repeat(e)+"^".repeat(o))}else if(e>c){if(n>s){const e=Math.max(Math.min(n-s,a),1);i.push("   |  "+"^".repeat(e))}s+=a+p}}break}return i.join("\n")},e.getBaseTransformPreset=Zn,e.getInnerRange=Be,e.getMemoedVNodeCall=nt,e.getVNodeBlockHelper=Ze,e.getVNodeHelper=qe,e.hasDynamicKeyVBind=Ue,e.hasScopeRef=function e(t,n){if(!t||0===Object.keys(n).length)return!1;switch(t.type){case 1:for(let o=0;o<t.props.length;o++){const r=t.props[o];if(7===r.type&&(e(r.arg,n)||e(r.exp,n)))return!0}return t.children.some((t=>e(t,n)));case 11:return!!e(t.source,n)||t.children.some((t=>e(t,n)));case 9:return t.branches.some((t=>e(t,n)));case 10:return!!e(t.condition,n)||t.children.some((t=>e(t,n)));case 4:return!t.isStatic&&Pe(t.content)&&!!n[t.content];case 8:return t.children.some((t=>g(t)&&e(t,n)));case 5:case 12:return e(t.content,n);default:return!1}},e.helperNameMap=de,e.injectProp=et,e.isBuiltInType=Ie,e.isCoreComponent=Me,e.isFunctionType=e=>/Function(?:Expression|Declaration)$|Method$/.test(e.type),e.isInDestructureAssignment=function(e,t){if(e&&("ObjectProperty"===e.type||"ArrayPattern"===e.type)){let e=t.length;for(;e--;){const n=t[e];if("AssignmentExpression"===n.type)return!0;if("ObjectProperty"!==n.type&&!n.type.endsWith("Pattern"))break}}return!1},e.isMemberExpression=De,e.isMemberExpressionBrowser=Ve,e.isMemberExpressionNode=Ae,e.isReferencedIdentifier=function(e,t,n){return!1},e.isSimpleIdentifier=Pe,e.isSlotOutlet=Ye,e.isStaticArgOf=Ke,e.isStaticExp=Ce,e.isStaticProperty=on,e.isStaticPropertyKey=(e,t)=>on(t)&&t.key===e,e.isTemplateNode=ze,e.isText=Je,e.isVSlot=Ge,e.locStub=me,e.makeBlock=ot,e.noopDirectiveTransform=Qn,e.parse=function(e,t={}){return ut(e,f({},fo,t))},e.parserOptions=fo,e.processExpression=rn,e.processFor=fn,e.processIf=cn,e.processSlotOutlet=$n,e.registerRuntimeHelpers=he,e.resolveComponentType=Cn,e.toValidAssetId=tt,e.trackSlotScopes=bn,e.trackVForSlotScopes=(e,t)=>{let n;if(ze(e)&&e.props.some(Ge)&&(n=He(e,"for"))){const e=n.parseResult=gn(n.exp);if(e){const{value:n,key:o,index:r}=e,{addIdentifiers:s,removeIdentifiers:i}=t;return n&&s(n),o&&s(o),r&&s(r),()=>{n&&i(n),o&&i(o),r&&i(r)}}}},e.transform=Kt,e.transformBind=An,e.transformElement=kn,e.transformExpression=(e,t)=>{if(5===e.type)e.content=rn(e.content,t);else if(1===e.type)for(let n=0;n<e.props.length;n++){const o=e.props[n];if(7===o.type&&"for"!==o.name){const e=o.exp,n=o.arg;!e||4!==e.type||"on"===o.name&&n||(o.exp=rn(e,t,"slot"===o.name)),n&&4===n.type&&!n.isStatic&&(o.arg=rn(n,t))}}},e.transformModel=Hn,e.transformOn=Vn,e.transformStyle=ho,e.traverseNode=Ut,e.walkBlockDeclarations=function(e,t){for(const n of e.body)if("VariableDeclaration"===n.type){if(n.declare)continue;for(const e of n.declarations)for(const n of nn(e.id))t(n)}else if("FunctionDeclaration"===n.type||"ClassDeclaration"===n.type){if(n.declare||!n.id)continue;t(n.id)}},e.walkFunctionParams=function(e,t){for(const n of e.params)for(const e of nn(n))t(e)},e.walkIdentifiers=function(e,t,n=!1,o=[],r=Object.create(null)){},e.warnDeprecation=function(e,t,n,...o){if("suppress-warning"===st(e,t))return;const{message:r,link:s}=rt[e],i=`(deprecation ${e}) ${"function"==typeof r?r(...o):r}${s?`\n  Details: ${s}`:""}`,c=new SyntaxError(i);c.code=e,n&&(c.loc=n),t.onWarn(c)},Object.defineProperty(e,"__esModule",{value:!0}),e}({});
