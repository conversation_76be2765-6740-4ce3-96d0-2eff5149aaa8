{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _PasswordInput from \"./PasswordInput.mjs\";\nvar PasswordInput = withInstall(_PasswordInput);\nvar stdin_default = PasswordInput;\nexport { PasswordInput, stdin_default as default };", "map": {"version": 3, "names": ["withInstall", "_PasswordInput", "PasswordInput", "stdin_default", "default"], "sources": ["C:/Users/<USER>/Downloads/Telegram Desktop/vue3_3.0/vue3_3.0/node_modules/vant/es/password-input/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _PasswordInput from \"./PasswordInput.mjs\";\nconst PasswordInput = withInstall(_PasswordInput);\nvar stdin_default = PasswordInput;\nexport {\n  PasswordInput,\n  stdin_default as default\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,cAAc,MAAM,qBAAqB;AAChD,IAAMC,aAAa,GAAGF,WAAW,CAACC,cAAc,CAAC;AACjD,IAAIE,aAAa,GAAGD,aAAa;AACjC,SACEA,aAAa,EACbC,aAAa,IAAIC,OAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}