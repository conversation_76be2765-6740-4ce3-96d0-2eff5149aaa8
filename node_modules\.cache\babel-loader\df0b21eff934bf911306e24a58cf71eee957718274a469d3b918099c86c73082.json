{"ast": null, "code": "import { createApp, reactive } from \"vue\";\nimport { extend } from \"../utils/index.mjs\";\nimport { useExpose } from \"../composables/use-expose.mjs\";\nfunction usePopupState() {\n  var state = reactive({\n    show: false\n  });\n  var toggle = function toggle(show) {\n    state.show = show;\n  };\n  var open = function open(props) {\n    extend(state, props, {\n      transitionAppear: true\n    });\n    toggle(true);\n  };\n  var close = function close() {\n    return toggle(false);\n  };\n  useExpose({\n    open: open,\n    close: close,\n    toggle: toggle\n  });\n  return {\n    open: open,\n    close: close,\n    state: state,\n    toggle: toggle\n  };\n}\nfunction mountComponent(RootComponent) {\n  var app = createApp(RootComponent);\n  var root = document.createElement(\"div\");\n  document.body.appendChild(root);\n  return {\n    instance: app.mount(root),\n    unmount: function unmount() {\n      app.unmount();\n      document.body.removeChild(root);\n    }\n  };\n}\nexport { mountComponent, usePopupState };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}