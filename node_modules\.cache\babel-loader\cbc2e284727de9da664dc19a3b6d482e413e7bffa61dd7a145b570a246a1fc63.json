{"ast": null, "code": "var lockCount = 0;\nfunction lockClick(lock) {\n  if (lock) {\n    if (!lockCount) {\n      document.body.classList.add(\"van-toast--unclickable\");\n    }\n    lockCount++;\n  } else if (lockCount) {\n    lockCount--;\n    if (!lockCount) {\n      document.body.classList.remove(\"van-toast--unclickable\");\n    }\n  }\n}\nexport { lockClick };", "map": {"version": 3, "names": ["lockCount", "lockClick", "lock", "document", "body", "classList", "add", "remove"], "sources": ["C:/Users/<USER>/Downloads/Telegram Desktop/vue3_3.0/vue3_3.0/node_modules/vant/es/toast/lock-click.mjs"], "sourcesContent": ["let lockCount = 0;\nfunction lockClick(lock) {\n  if (lock) {\n    if (!lockCount) {\n      document.body.classList.add(\"van-toast--unclickable\");\n    }\n    lockCount++;\n  } else if (lockCount) {\n    lockCount--;\n    if (!lockCount) {\n      document.body.classList.remove(\"van-toast--unclickable\");\n    }\n  }\n}\nexport {\n  lockClick\n};\n"], "mappings": "AAAA,IAAIA,SAAS,GAAG,CAAC;AACjB,SAASC,SAASA,CAACC,IAAI,EAAE;EACvB,IAAIA,IAAI,EAAE;IACR,IAAI,CAACF,SAAS,EAAE;MACdG,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACC,GAAG,CAAC,wBAAwB,CAAC;IACvD;IACAN,SAAS,EAAE;EACb,CAAC,MAAM,IAAIA,SAAS,EAAE;IACpBA,SAAS,EAAE;IACX,IAAI,CAACA,SAAS,EAAE;MACdG,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACE,MAAM,CAAC,wBAAwB,CAAC;IAC1D;EACF;AACF;AACA,SACEN,SAAS"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}