{"ast": null, "code": "function _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return _typeof(key) === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (_typeof(input) !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (_typeof(res) !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\nimport zh from './lang/zh';\nimport en from './lang/en';\nimport spa from './lang/spa';\nimport pl from './lang/pl';\nimport cs from './lang/cs';\nimport dan from './lang/dan';\nimport el from './lang/el';\nimport rom from './lang/rom';\nimport sk from './lang/sk';\nimport de from './lang/de';\nimport it from './lang/it';\nimport jp from './lang/jp';\nimport tr from './lang/tr';\nimport ara from './lang/ara';\nimport nl from './lang/nl';\nimport kor from './lang/kor';\nimport pt from './lang/pt';\n\n// const langlist  = ['zh','en','spa','pl','cs','dan','el','rom','sk','de','it','jp','tr','ara','nl','kor'];\n// const langlist  = ['spa','de','it','jp','ara','nl','kor'];\n\nexport default {\n  'zh': _objectSpread(_objectSpread({}, zh), zh),\n  'en': _objectSpread(_objectSpread({}, en), en),\n  'spa': _objectSpread(_objectSpread({}, spa), spa),\n  'pl': _objectSpread(_objectSpread({}, pl), pl),\n  'cs': _objectSpread(_objectSpread({}, cs), cs),\n  'dan': _objectSpread(_objectSpread({}, dan), dan),\n  'el': _objectSpread(_objectSpread({}, el), el),\n  'rom': _objectSpread(_objectSpread({}, rom), rom),\n  'sk': _objectSpread(_objectSpread({}, sk), sk),\n  'de': _objectSpread(_objectSpread({}, de), de),\n  'it': _objectSpread(_objectSpread({}, it), it),\n  'jp': _objectSpread(_objectSpread({}, jp), jp),\n  'tr': _objectSpread(_objectSpread({}, tr), tr),\n  'ara': _objectSpread(_objectSpread({}, ara), ara),\n  'nl': _objectSpread(_objectSpread({}, nl), nl),\n  'kor': _objectSpread(_objectSpread({}, kor), kor),\n  'pt': _objectSpread(_objectSpread({}, pt), pt)\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}