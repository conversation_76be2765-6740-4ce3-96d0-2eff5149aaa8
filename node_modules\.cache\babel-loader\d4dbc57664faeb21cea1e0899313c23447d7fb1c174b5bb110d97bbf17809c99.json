{"ast": null, "code": "function _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return _typeof(key) === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (_typeof(input) !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (_typeof(res) !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\nfunction _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nfunction _iterableToArrayLimit(arr, i) { var _i = null == arr ? null : \"undefined\" != typeof Symbol && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (null != _i) { var _s, _e, _x, _r, _arr = [], _n = !0, _d = !1; try { if (_x = (_i = _i.call(arr)).next, 0 === i) { if (Object(_i) !== _i) return; _n = !1; } else for (; !(_n = (_s = _x.call(_i)).done) && (_arr.push(_s.value), _arr.length !== i); _n = !0); } catch (err) { _d = !0, _e = err; } finally { try { if (!_n && null != _i.return && (_r = _i.return(), Object(_r) !== _r)) return; } finally { if (_d) throw _e; } } return _arr; } }\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\nimport { createVNode as _createVNode, mergeProps as _mergeProps } from \"vue\";\nimport { computed, defineComponent } from \"vue\";\nimport { BORDER, extend, addUnit, numericProp, createNamespace } from \"../utils/index.mjs\";\nimport { GRID_KEY } from \"../grid/Grid.mjs\";\nimport { useParent } from \"@vant/use\";\nimport { useRoute, routeProps } from \"../composables/use-route.mjs\";\nimport { Icon } from \"../icon/index.mjs\";\nimport { Badge } from \"../badge/index.mjs\";\nvar _createNamespace = createNamespace(\"grid-item\"),\n  _createNamespace2 = _slicedToArray(_createNamespace, 2),\n  name = _createNamespace2[0],\n  bem = _createNamespace2[1];\nvar gridItemProps = extend({}, routeProps, {\n  dot: Boolean,\n  text: String,\n  icon: String,\n  badge: numericProp,\n  iconColor: String,\n  iconPrefix: String,\n  badgeProps: Object\n});\nvar stdin_default = defineComponent({\n  name: name,\n  props: gridItemProps,\n  setup: function setup(props, _ref) {\n    var slots = _ref.slots;\n    var _useParent = useParent(GRID_KEY),\n      parent = _useParent.parent,\n      index = _useParent.index;\n    var route = useRoute();\n    if (!parent) {\n      if (process.env.NODE_ENV !== \"production\") {\n        console.error(\"[Vant] <GridItem> must be a child component of <Grid>.\");\n      }\n      return;\n    }\n    var rootStyle = computed(function () {\n      var _parent$props = parent.props,\n        square = _parent$props.square,\n        gutter = _parent$props.gutter,\n        columnNum = _parent$props.columnNum;\n      var percent = \"\".concat(100 / +columnNum, \"%\");\n      var style = {\n        flexBasis: percent\n      };\n      if (square) {\n        style.paddingTop = percent;\n      } else if (gutter) {\n        var gutterValue = addUnit(gutter);\n        style.paddingRight = gutterValue;\n        if (index.value >= columnNum) {\n          style.marginTop = gutterValue;\n        }\n      }\n      return style;\n    });\n    var contentStyle = computed(function () {\n      var _parent$props2 = parent.props,\n        square = _parent$props2.square,\n        gutter = _parent$props2.gutter;\n      if (square && gutter) {\n        var gutterValue = addUnit(gutter);\n        return {\n          right: gutterValue,\n          bottom: gutterValue,\n          height: \"auto\"\n        };\n      }\n    });\n    var renderIcon = function renderIcon() {\n      if (slots.icon) {\n        return _createVNode(Badge, _mergeProps({\n          \"dot\": props.dot,\n          \"content\": props.badge\n        }, props.badgeProps), {\n          default: slots.icon\n        });\n      }\n      if (props.icon) {\n        return _createVNode(Icon, {\n          \"dot\": props.dot,\n          \"name\": props.icon,\n          \"size\": parent.props.iconSize,\n          \"badge\": props.badge,\n          \"class\": bem(\"icon\"),\n          \"color\": props.iconColor,\n          \"badgeProps\": props.badgeProps,\n          \"classPrefix\": props.iconPrefix\n        }, null);\n      }\n    };\n    var renderText = function renderText() {\n      if (slots.text) {\n        return slots.text();\n      }\n      if (props.text) {\n        return _createVNode(\"span\", {\n          \"class\": bem(\"text\")\n        }, [props.text]);\n      }\n    };\n    var renderContent = function renderContent() {\n      if (slots.default) {\n        return slots.default();\n      }\n      return [renderIcon(), renderText()];\n    };\n    return function () {\n      var _parent$props3 = parent.props,\n        center = _parent$props3.center,\n        border = _parent$props3.border,\n        square = _parent$props3.square,\n        gutter = _parent$props3.gutter,\n        reverse = _parent$props3.reverse,\n        direction = _parent$props3.direction,\n        clickable = _parent$props3.clickable;\n      var classes = [bem(\"content\", [direction, {\n        center: center,\n        square: square,\n        reverse: reverse,\n        clickable: clickable,\n        surround: border && gutter\n      }]), _defineProperty({}, BORDER, border)];\n      return _createVNode(\"div\", {\n        \"class\": [bem({\n          square: square\n        })],\n        \"style\": rootStyle.value\n      }, [_createVNode(\"div\", {\n        \"role\": clickable ? \"button\" : void 0,\n        \"class\": classes,\n        \"style\": contentStyle.value,\n        \"tabindex\": clickable ? 0 : void 0,\n        \"onClick\": route\n      }, [renderContent()])]);\n    };\n  }\n});\nexport { stdin_default as default };", "map": {"version": 3, "names": ["createVNode", "_createVNode", "mergeProps", "_mergeProps", "computed", "defineComponent", "BORDER", "extend", "addUnit", "numericProp", "createNamespace", "GRID_KEY", "useParent", "useRoute", "routeProps", "Icon", "Badge", "_createNamespace", "_createNamespace2", "_slicedToArray", "name", "bem", "gridItemProps", "dot", "Boolean", "text", "String", "icon", "badge", "iconColor", "iconPrefix", "badgeProps", "Object", "stdin_default", "props", "setup", "_ref", "slots", "_useParent", "parent", "index", "route", "process", "env", "NODE_ENV", "console", "error", "rootStyle", "_parent$props", "square", "gutter", "columnNum", "percent", "concat", "style", "flexBasis", "paddingTop", "gutterValue", "paddingRight", "value", "marginTop", "contentStyle", "_parent$props2", "right", "bottom", "height", "renderIcon", "default", "iconSize", "renderText", "renderContent", "_parent$props3", "center", "border", "reverse", "direction", "clickable", "classes", "surround", "_defineProperty"], "sources": ["C:/Users/<USER>/Desktop/vue3_3.0/node_modules/vant/es/grid-item/GridItem.mjs"], "sourcesContent": ["import { createVNode as _createVNode, mergeProps as _mergeProps } from \"vue\";\nimport { computed, defineComponent } from \"vue\";\nimport { BORDER, extend, addUnit, numericProp, createNamespace } from \"../utils/index.mjs\";\nimport { GRID_KEY } from \"../grid/Grid.mjs\";\nimport { useParent } from \"@vant/use\";\nimport { useRoute, routeProps } from \"../composables/use-route.mjs\";\nimport { Icon } from \"../icon/index.mjs\";\nimport { Badge } from \"../badge/index.mjs\";\nconst [name, bem] = createNamespace(\"grid-item\");\nconst gridItemProps = extend({}, routeProps, {\n  dot: Boolean,\n  text: String,\n  icon: String,\n  badge: numericProp,\n  iconColor: String,\n  iconPrefix: String,\n  badgeProps: Object\n});\nvar stdin_default = defineComponent({\n  name,\n  props: gridItemProps,\n  setup(props, {\n    slots\n  }) {\n    const {\n      parent,\n      index\n    } = useParent(GRID_KEY);\n    const route = useRoute();\n    if (!parent) {\n      if (process.env.NODE_ENV !== \"production\") {\n        console.error(\"[Vant] <GridItem> must be a child component of <Grid>.\");\n      }\n      return;\n    }\n    const rootStyle = computed(() => {\n      const {\n        square,\n        gutter,\n        columnNum\n      } = parent.props;\n      const percent = `${100 / +columnNum}%`;\n      const style = {\n        flexBasis: percent\n      };\n      if (square) {\n        style.paddingTop = percent;\n      } else if (gutter) {\n        const gutterValue = addUnit(gutter);\n        style.paddingRight = gutterValue;\n        if (index.value >= columnNum) {\n          style.marginTop = gutterValue;\n        }\n      }\n      return style;\n    });\n    const contentStyle = computed(() => {\n      const {\n        square,\n        gutter\n      } = parent.props;\n      if (square && gutter) {\n        const gutterValue = addUnit(gutter);\n        return {\n          right: gutterValue,\n          bottom: gutterValue,\n          height: \"auto\"\n        };\n      }\n    });\n    const renderIcon = () => {\n      if (slots.icon) {\n        return _createVNode(Badge, _mergeProps({\n          \"dot\": props.dot,\n          \"content\": props.badge\n        }, props.badgeProps), {\n          default: slots.icon\n        });\n      }\n      if (props.icon) {\n        return _createVNode(Icon, {\n          \"dot\": props.dot,\n          \"name\": props.icon,\n          \"size\": parent.props.iconSize,\n          \"badge\": props.badge,\n          \"class\": bem(\"icon\"),\n          \"color\": props.iconColor,\n          \"badgeProps\": props.badgeProps,\n          \"classPrefix\": props.iconPrefix\n        }, null);\n      }\n    };\n    const renderText = () => {\n      if (slots.text) {\n        return slots.text();\n      }\n      if (props.text) {\n        return _createVNode(\"span\", {\n          \"class\": bem(\"text\")\n        }, [props.text]);\n      }\n    };\n    const renderContent = () => {\n      if (slots.default) {\n        return slots.default();\n      }\n      return [renderIcon(), renderText()];\n    };\n    return () => {\n      const {\n        center,\n        border,\n        square,\n        gutter,\n        reverse,\n        direction,\n        clickable\n      } = parent.props;\n      const classes = [bem(\"content\", [direction, {\n        center,\n        square,\n        reverse,\n        clickable,\n        surround: border && gutter\n      }]), {\n        [BORDER]: border\n      }];\n      return _createVNode(\"div\", {\n        \"class\": [bem({\n          square\n        })],\n        \"style\": rootStyle.value\n      }, [_createVNode(\"div\", {\n        \"role\": clickable ? \"button\" : void 0,\n        \"class\": classes,\n        \"style\": contentStyle.value,\n        \"tabindex\": clickable ? 0 : void 0,\n        \"onClick\": route\n      }, [renderContent()])]);\n    };\n  }\n});\nexport {\n  stdin_default as default\n};\n"], "mappings": ";;;;;;;;;;AAAA,SAASA,WAAW,IAAIC,YAAY,EAAEC,UAAU,IAAIC,WAAW,QAAQ,KAAK;AAC5E,SAASC,QAAQ,EAAEC,eAAe,QAAQ,KAAK;AAC/C,SAASC,MAAM,EAAEC,MAAM,EAAEC,OAAO,EAAEC,WAAW,EAAEC,eAAe,QAAQ,oBAAoB;AAC1F,SAASC,QAAQ,QAAQ,kBAAkB;AAC3C,SAASC,SAAS,QAAQ,WAAW;AACrC,SAASC,QAAQ,EAAEC,UAAU,QAAQ,8BAA8B;AACnE,SAASC,IAAI,QAAQ,mBAAmB;AACxC,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,IAAAC,gBAAA,GAAoBP,eAAe,CAAC,WAAW,CAAC;EAAAQ,iBAAA,GAAAC,cAAA,CAAAF,gBAAA;EAAzCG,IAAI,GAAAF,iBAAA;EAAEG,GAAG,GAAAH,iBAAA;AAChB,IAAMI,aAAa,GAAGf,MAAM,CAAC,CAAC,CAAC,EAAEO,UAAU,EAAE;EAC3CS,GAAG,EAAEC,OAAO;EACZC,IAAI,EAAEC,MAAM;EACZC,IAAI,EAAED,MAAM;EACZE,KAAK,EAAEnB,WAAW;EAClBoB,SAAS,EAAEH,MAAM;EACjBI,UAAU,EAAEJ,MAAM;EAClBK,UAAU,EAAEC;AACd,CAAC,CAAC;AACF,IAAIC,aAAa,GAAG5B,eAAe,CAAC;EAClCe,IAAI,EAAJA,IAAI;EACJc,KAAK,EAAEZ,aAAa;EACpBa,KAAK,WAAAA,MAACD,KAAK,EAAAE,IAAA,EAER;IAAA,IADDC,KAAK,GAAAD,IAAA,CAALC,KAAK;IAEL,IAAAC,UAAA,GAGI1B,SAAS,CAACD,QAAQ,CAAC;MAFrB4B,MAAM,GAAAD,UAAA,CAANC,MAAM;MACNC,KAAK,GAAAF,UAAA,CAALE,KAAK;IAEP,IAAMC,KAAK,GAAG5B,QAAQ,CAAC,CAAC;IACxB,IAAI,CAAC0B,MAAM,EAAE;MACX,IAAIG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzCC,OAAO,CAACC,KAAK,CAAC,wDAAwD,CAAC;MACzE;MACA;IACF;IACA,IAAMC,SAAS,GAAG3C,QAAQ,CAAC,YAAM;MAC/B,IAAA4C,aAAA,GAIIT,MAAM,CAACL,KAAK;QAHde,MAAM,GAAAD,aAAA,CAANC,MAAM;QACNC,MAAM,GAAAF,aAAA,CAANE,MAAM;QACNC,SAAS,GAAAH,aAAA,CAATG,SAAS;MAEX,IAAMC,OAAO,MAAAC,MAAA,CAAM,GAAG,GAAG,CAACF,SAAS,MAAG;MACtC,IAAMG,KAAK,GAAG;QACZC,SAAS,EAAEH;MACb,CAAC;MACD,IAAIH,MAAM,EAAE;QACVK,KAAK,CAACE,UAAU,GAAGJ,OAAO;MAC5B,CAAC,MAAM,IAAIF,MAAM,EAAE;QACjB,IAAMO,WAAW,GAAGjD,OAAO,CAAC0C,MAAM,CAAC;QACnCI,KAAK,CAACI,YAAY,GAAGD,WAAW;QAChC,IAAIjB,KAAK,CAACmB,KAAK,IAAIR,SAAS,EAAE;UAC5BG,KAAK,CAACM,SAAS,GAAGH,WAAW;QAC/B;MACF;MACA,OAAOH,KAAK;IACd,CAAC,CAAC;IACF,IAAMO,YAAY,GAAGzD,QAAQ,CAAC,YAAM;MAClC,IAAA0D,cAAA,GAGIvB,MAAM,CAACL,KAAK;QAFde,MAAM,GAAAa,cAAA,CAANb,MAAM;QACNC,MAAM,GAAAY,cAAA,CAANZ,MAAM;MAER,IAAID,MAAM,IAAIC,MAAM,EAAE;QACpB,IAAMO,WAAW,GAAGjD,OAAO,CAAC0C,MAAM,CAAC;QACnC,OAAO;UACLa,KAAK,EAAEN,WAAW;UAClBO,MAAM,EAAEP,WAAW;UACnBQ,MAAM,EAAE;QACV,CAAC;MACH;IACF,CAAC,CAAC;IACF,IAAMC,UAAU,GAAG,SAAbA,UAAUA,CAAA,EAAS;MACvB,IAAI7B,KAAK,CAACV,IAAI,EAAE;QACd,OAAO1B,YAAY,CAACe,KAAK,EAAEb,WAAW,CAAC;UACrC,KAAK,EAAE+B,KAAK,CAACX,GAAG;UAChB,SAAS,EAAEW,KAAK,CAACN;QACnB,CAAC,EAAEM,KAAK,CAACH,UAAU,CAAC,EAAE;UACpBoC,OAAO,EAAE9B,KAAK,CAACV;QACjB,CAAC,CAAC;MACJ;MACA,IAAIO,KAAK,CAACP,IAAI,EAAE;QACd,OAAO1B,YAAY,CAACc,IAAI,EAAE;UACxB,KAAK,EAAEmB,KAAK,CAACX,GAAG;UAChB,MAAM,EAAEW,KAAK,CAACP,IAAI;UAClB,MAAM,EAAEY,MAAM,CAACL,KAAK,CAACkC,QAAQ;UAC7B,OAAO,EAAElC,KAAK,CAACN,KAAK;UACpB,OAAO,EAAEP,GAAG,CAAC,MAAM,CAAC;UACpB,OAAO,EAAEa,KAAK,CAACL,SAAS;UACxB,YAAY,EAAEK,KAAK,CAACH,UAAU;UAC9B,aAAa,EAAEG,KAAK,CAACJ;QACvB,CAAC,EAAE,IAAI,CAAC;MACV;IACF,CAAC;IACD,IAAMuC,UAAU,GAAG,SAAbA,UAAUA,CAAA,EAAS;MACvB,IAAIhC,KAAK,CAACZ,IAAI,EAAE;QACd,OAAOY,KAAK,CAACZ,IAAI,CAAC,CAAC;MACrB;MACA,IAAIS,KAAK,CAACT,IAAI,EAAE;QACd,OAAOxB,YAAY,CAAC,MAAM,EAAE;UAC1B,OAAO,EAAEoB,GAAG,CAAC,MAAM;QACrB,CAAC,EAAE,CAACa,KAAK,CAACT,IAAI,CAAC,CAAC;MAClB;IACF,CAAC;IACD,IAAM6C,aAAa,GAAG,SAAhBA,aAAaA,CAAA,EAAS;MAC1B,IAAIjC,KAAK,CAAC8B,OAAO,EAAE;QACjB,OAAO9B,KAAK,CAAC8B,OAAO,CAAC,CAAC;MACxB;MACA,OAAO,CAACD,UAAU,CAAC,CAAC,EAAEG,UAAU,CAAC,CAAC,CAAC;IACrC,CAAC;IACD,OAAO,YAAM;MACX,IAAAE,cAAA,GAQIhC,MAAM,CAACL,KAAK;QAPdsC,MAAM,GAAAD,cAAA,CAANC,MAAM;QACNC,MAAM,GAAAF,cAAA,CAANE,MAAM;QACNxB,MAAM,GAAAsB,cAAA,CAANtB,MAAM;QACNC,MAAM,GAAAqB,cAAA,CAANrB,MAAM;QACNwB,OAAO,GAAAH,cAAA,CAAPG,OAAO;QACPC,SAAS,GAAAJ,cAAA,CAATI,SAAS;QACTC,SAAS,GAAAL,cAAA,CAATK,SAAS;MAEX,IAAMC,OAAO,GAAG,CAACxD,GAAG,CAAC,SAAS,EAAE,CAACsD,SAAS,EAAE;QAC1CH,MAAM,EAANA,MAAM;QACNvB,MAAM,EAANA,MAAM;QACNyB,OAAO,EAAPA,OAAO;QACPE,SAAS,EAATA,SAAS;QACTE,QAAQ,EAAEL,MAAM,IAAIvB;MACtB,CAAC,CAAC,CAAC,EAAA6B,eAAA,KACAzE,MAAM,EAAGmE,MAAM,EAChB;MACF,OAAOxE,YAAY,CAAC,KAAK,EAAE;QACzB,OAAO,EAAE,CAACoB,GAAG,CAAC;UACZ4B,MAAM,EAANA;QACF,CAAC,CAAC,CAAC;QACH,OAAO,EAAEF,SAAS,CAACY;MACrB,CAAC,EAAE,CAAC1D,YAAY,CAAC,KAAK,EAAE;QACtB,MAAM,EAAE2E,SAAS,GAAG,QAAQ,GAAG,KAAK,CAAC;QACrC,OAAO,EAAEC,OAAO;QAChB,OAAO,EAAEhB,YAAY,CAACF,KAAK;QAC3B,UAAU,EAAEiB,SAAS,GAAG,CAAC,GAAG,KAAK,CAAC;QAClC,SAAS,EAAEnC;MACb,CAAC,EAAE,CAAC6B,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzB,CAAC;EACH;AACF,CAAC,CAAC;AACF,SACErC,aAAa,IAAIkC,OAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}