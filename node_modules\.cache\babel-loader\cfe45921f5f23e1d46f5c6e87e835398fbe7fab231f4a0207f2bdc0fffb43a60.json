{"ast": null, "code": "function _createForOfIteratorHelper(o, allowArrayLike) { var it = typeof Symbol !== \"undefined\" && o[Symbol.iterator] || o[\"@@iterator\"]; if (!it) { if (Array.isArray(o) || (it = _unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === \"number\") { if (it) o = it; var i = 0; var F = function F() {}; return { s: F, n: function n() { if (i >= o.length) return { done: true }; return { done: false, value: o[i++] }; }, e: function e(_e) { throw _e; }, f: F }; } throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); } var normalCompletion = true, didErr = false, err; return { s: function s() { it = it.call(o); }, n: function n() { var step = it.next(); normalCompletion = step.done; return step; }, e: function e(_e2) { didErr = true; err = _e2; }, f: function f() { try { if (!normalCompletion && it.return != null) it.return(); } finally { if (didErr) throw err; } } }; }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nfunction _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __export = function __export(target, all) {\n  for (var name in all) __defProp(target, name, {\n    get: all[name],\n    enumerable: true\n  });\n};\nvar __copyProps = function __copyProps(to, from, except, desc) {\n  if (from && _typeof(from) === \"object\" || typeof from === \"function\") {\n    var _iterator = _createForOfIteratorHelper(__getOwnPropNames(from)),\n      _step;\n    try {\n      var _loop = function _loop() {\n        var key = _step.value;\n        if (!__hasOwnProp.call(to, key) && key !== except) __defProp(to, key, {\n          get: function get() {\n            return from[key];\n          },\n          enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable\n        });\n      };\n      for (_iterator.s(); !(_step = _iterator.n()).done;) {\n        _loop();\n      }\n    } catch (err) {\n      _iterator.e(err);\n    } finally {\n      _iterator.f();\n    }\n  }\n  return to;\n};\nvar __toCommonJS = function __toCommonJS(mod) {\n  return __copyProps(__defProp({}, \"__esModule\", {\n    value: true\n  }), mod);\n};\nvar stdin_exports = {};\n__export(stdin_exports, {\n  default: function _default() {\n    return stdin_default;\n  }\n});\nmodule.exports = __toCommonJS(stdin_exports);\nvar stdin_default = {\n  name: \"Nom\",\n  tel: \"Telephone\",\n  save: \"Sauvegarder\",\n  confirm: \"Confirmer\",\n  cancel: \"Annuler\",\n  delete: \"Suprimer\",\n  loading: \"Chargement...\",\n  noCoupon: \"Pas de coupons\",\n  nameEmpty: \"Veuillez remplir le nom\",\n  addContact: \"Ajouter contact\",\n  telInvalid: \"Num\\xE9ro de t\\xE9l\\xE9phone incorrect\",\n  vanCalendar: {\n    end: \"Fin\",\n    start: \"D\\xE9but\",\n    title: \"Calendrier\",\n    weekdays: [\"Dim\", \"Lun\", \"Mar\", \"Mer\", \"Jeu\", \"Ven\", \"Sam\"],\n    monthTitle: function monthTitle(year, month) {\n      return \"\".concat(year, \"/\").concat(month);\n    },\n    rangePrompt: function rangePrompt(maxRange) {\n      return \"Choisir pas plus de \".concat(maxRange, \" jours\");\n    }\n  },\n  vanCascader: {\n    select: \"S\\xE9lectionner\"\n  },\n  vanPagination: {\n    prev: \"Pr\\xE9c\\xE9dent\",\n    next: \"Suivant\"\n  },\n  vanPullRefresh: {\n    pulling: \"Tirer pour actualiser ...\",\n    loosing: \"Rel\\xE2chez pour actualiser...\"\n  },\n  vanSubmitBar: {\n    label: \"Total:\"\n  },\n  vanCoupon: {\n    unlimited: \"Illimit\\xE9\",\n    discount: function discount(_discount) {\n      return \"\".concat(_discount * 10, \"% de r\\xE9duction\");\n    },\n    condition: function condition(_condition) {\n      return \"Au moins \".concat(_condition);\n    }\n  },\n  vanCouponCell: {\n    title: \"Coupon\",\n    count: function count(_count) {\n      return \"Vous avez \".concat(_count, \" coupons\");\n    }\n  },\n  vanCouponList: {\n    exchange: \"Exchange\",\n    close: \"Fermer\",\n    enable: \"Disponible\",\n    disabled: \"Indisponible\",\n    placeholder: \"Code coupon\"\n  },\n  vanAddressEdit: {\n    area: \"Zone\",\n    postal: \"Postal\",\n    areaEmpty: \"Veuillez s\\xE9lectionner une zone de r\\xE9ception\",\n    addressEmpty: \"L'adresse ne peut pas \\xEAtre vide\",\n    postalEmpty: \"Mauvais code postal\",\n    addressDetail: \"Adresse\",\n    defaultAddress: \"D\\xE9finir comme adresse par d\\xE9faut\"\n  },\n  vanAddressList: {\n    add: \"Ajouter une nouvelle adresse\"\n  }\n};", "map": {"version": 3, "names": ["__defProp", "Object", "defineProperty", "__getOwnPropDesc", "getOwnPropertyDescriptor", "__getOwnPropNames", "getOwnPropertyNames", "__hasOwnProp", "prototype", "hasOwnProperty", "__export", "target", "all", "name", "get", "enumerable", "__copyProps", "to", "from", "except", "desc", "_typeof", "_iterator", "_createForOfIteratorHelper", "_step", "_loop", "key", "value", "call", "s", "n", "done", "err", "e", "f", "__toCommonJS", "mod", "stdin_exports", "default", "_default", "stdin_default", "module", "exports", "tel", "save", "confirm", "cancel", "delete", "loading", "noCoupon", "nameEmpty", "addContact", "telInvalid", "vanCalendar", "end", "start", "title", "weekdays", "monthTitle", "year", "month", "concat", "rangePrompt", "max<PERSON><PERSON><PERSON>", "vanCascader", "select", "vanPagination", "prev", "next", "vanPullRefresh", "pulling", "loosing", "vanSubmitBar", "label", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "unlimited", "discount", "condition", "vanCouponCell", "count", "vanCouponList", "exchange", "close", "enable", "disabled", "placeholder", "vanAddressEdit", "area", "postal", "areaEmpty", "addressEmpty", "postalEmpty", "addressDetail", "defaultAddress", "vanAddressList", "add"], "sources": ["C:/Users/<USER>/Downloads/Telegram Desktop/vue3_3.0/vue3_3.0/node_modules/vant/lib/locale/lang/fr-FR.js"], "sourcesContent": ["var __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\nvar stdin_exports = {};\n__export(stdin_exports, {\n  default: () => stdin_default\n});\nmodule.exports = __toCommonJS(stdin_exports);\nvar stdin_default = {\n  name: \"Nom\",\n  tel: \"Telephone\",\n  save: \"Sauvegarder\",\n  confirm: \"Confirmer\",\n  cancel: \"Annuler\",\n  delete: \"Suprimer\",\n  loading: \"Chargement...\",\n  noCoupon: \"Pas de coupons\",\n  nameEmpty: \"Veuillez remplir le nom\",\n  addContact: \"Ajouter contact\",\n  telInvalid: \"Num\\xE9ro de t\\xE9l\\xE9phone incorrect\",\n  vanCalendar: {\n    end: \"Fin\",\n    start: \"D\\xE9but\",\n    title: \"Calendrier\",\n    weekdays: [\"Dim\", \"Lun\", \"Mar\", \"Mer\", \"Jeu\", \"Ven\", \"Sam\"],\n    monthTitle: (year, month) => `${year}/${month}`,\n    rangePrompt: (maxRange) => `Choisir pas plus de ${maxRange} jours`\n  },\n  vanCascader: {\n    select: \"S\\xE9lectionner\"\n  },\n  vanPagination: {\n    prev: \"Pr\\xE9c\\xE9dent\",\n    next: \"Suivant\"\n  },\n  vanPullRefresh: {\n    pulling: \"Tirer pour actualiser ...\",\n    loosing: \"Rel\\xE2chez pour actualiser...\"\n  },\n  vanSubmitBar: {\n    label: \"Total:\"\n  },\n  vanCoupon: {\n    unlimited: \"Illimit\\xE9\",\n    discount: (discount) => `${discount * 10}% de r\\xE9duction`,\n    condition: (condition) => `Au moins ${condition}`\n  },\n  vanCouponCell: {\n    title: \"Coupon\",\n    count: (count) => `Vous avez ${count} coupons`\n  },\n  vanCouponList: {\n    exchange: \"Exchange\",\n    close: \"Fermer\",\n    enable: \"Disponible\",\n    disabled: \"Indisponible\",\n    placeholder: \"Code coupon\"\n  },\n  vanAddressEdit: {\n    area: \"Zone\",\n    postal: \"Postal\",\n    areaEmpty: \"Veuillez s\\xE9lectionner une zone de r\\xE9ception\",\n    addressEmpty: \"L'adresse ne peut pas \\xEAtre vide\",\n    postalEmpty: \"Mauvais code postal\",\n    addressDetail: \"Adresse\",\n    defaultAddress: \"D\\xE9finir comme adresse par d\\xE9faut\"\n  },\n  vanAddressList: {\n    add: \"Ajouter une nouvelle adresse\"\n  }\n};\n"], "mappings": ";;;;AAAA,IAAIA,SAAS,GAAGC,MAAM,CAACC,cAAc;AACrC,IAAIC,gBAAgB,GAAGF,MAAM,CAACG,wBAAwB;AACtD,IAAIC,iBAAiB,GAAGJ,MAAM,CAACK,mBAAmB;AAClD,IAAIC,YAAY,GAAGN,MAAM,CAACO,SAAS,CAACC,cAAc;AAClD,IAAIC,QAAQ,GAAG,SAAXA,QAAQA,CAAIC,MAAM,EAAEC,GAAG,EAAK;EAC9B,KAAK,IAAIC,IAAI,IAAID,GAAG,EAClBZ,SAAS,CAACW,MAAM,EAAEE,IAAI,EAAE;IAAEC,GAAG,EAAEF,GAAG,CAACC,IAAI,CAAC;IAAEE,UAAU,EAAE;EAAK,CAAC,CAAC;AACjE,CAAC;AACD,IAAIC,WAAW,GAAG,SAAdA,WAAWA,CAAIC,EAAE,EAAEC,IAAI,EAAEC,MAAM,EAAEC,IAAI,EAAK;EAC5C,IAAIF,IAAI,IAAIG,OAAA,CAAOH,IAAI,MAAK,QAAQ,IAAI,OAAOA,IAAI,KAAK,UAAU,EAAE;IAAA,IAAAI,SAAA,GAAAC,0BAAA,CAClDlB,iBAAiB,CAACa,IAAI,CAAC;MAAAM,KAAA;IAAA;MAAA,IAAAC,KAAA,YAAAA,MAAA,EACrC;QAAA,IADOC,GAAG,GAAAF,KAAA,CAAAG,KAAA;QACV,IAAI,CAACpB,YAAY,CAACqB,IAAI,CAACX,EAAE,EAAES,GAAG,CAAC,IAAIA,GAAG,KAAKP,MAAM,EAC/CnB,SAAS,CAACiB,EAAE,EAAES,GAAG,EAAE;UAAEZ,GAAG,EAAE,SAAAA,IAAA;YAAA,OAAMI,IAAI,CAACQ,GAAG,CAAC;UAAA;UAAEX,UAAU,EAAE,EAAEK,IAAI,GAAGjB,gBAAgB,CAACe,IAAI,EAAEQ,GAAG,CAAC,CAAC,IAAIN,IAAI,CAACL;QAAW,CAAC,CAAC;MAAA,CAAC;MAFvH,KAAAO,SAAA,CAAAO,CAAA,MAAAL,KAAA,GAAAF,SAAA,CAAAQ,CAAA,IAAAC,IAAA;QAAAN,KAAA;MAAA;IAEuH,SAAAO,GAAA;MAAAV,SAAA,CAAAW,CAAA,CAAAD,GAAA;IAAA;MAAAV,SAAA,CAAAY,CAAA;IAAA;EACzH;EACA,OAAOjB,EAAE;AACX,CAAC;AACD,IAAIkB,YAAY,GAAG,SAAfA,YAAYA,CAAIC,GAAG;EAAA,OAAKpB,WAAW,CAAChB,SAAS,CAAC,CAAC,CAAC,EAAE,YAAY,EAAE;IAAE2B,KAAK,EAAE;EAAK,CAAC,CAAC,EAAES,GAAG,CAAC;AAAA;AAC1F,IAAIC,aAAa,GAAG,CAAC,CAAC;AACtB3B,QAAQ,CAAC2B,aAAa,EAAE;EACtBC,OAAO,EAAE,SAAAC,SAAA;IAAA,OAAMC,aAAa;EAAA;AAC9B,CAAC,CAAC;AACFC,MAAM,CAACC,OAAO,GAAGP,YAAY,CAACE,aAAa,CAAC;AAC5C,IAAIG,aAAa,GAAG;EAClB3B,IAAI,EAAE,KAAK;EACX8B,GAAG,EAAE,WAAW;EAChBC,IAAI,EAAE,aAAa;EACnBC,OAAO,EAAE,WAAW;EACpBC,MAAM,EAAE,SAAS;EACjBC,MAAM,EAAE,UAAU;EAClBC,OAAO,EAAE,eAAe;EACxBC,QAAQ,EAAE,gBAAgB;EAC1BC,SAAS,EAAE,yBAAyB;EACpCC,UAAU,EAAE,iBAAiB;EAC7BC,UAAU,EAAE,wCAAwC;EACpDC,WAAW,EAAE;IACXC,GAAG,EAAE,KAAK;IACVC,KAAK,EAAE,UAAU;IACjBC,KAAK,EAAE,YAAY;IACnBC,QAAQ,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IAC3DC,UAAU,EAAE,SAAAA,WAACC,IAAI,EAAEC,KAAK;MAAA,UAAAC,MAAA,CAAQF,IAAI,OAAAE,MAAA,CAAID,KAAK;IAAA,CAAE;IAC/CE,WAAW,EAAE,SAAAA,YAACC,QAAQ;MAAA,8BAAAF,MAAA,CAA4BE,QAAQ;IAAA;EAC5D,CAAC;EACDC,WAAW,EAAE;IACXC,MAAM,EAAE;EACV,CAAC;EACDC,aAAa,EAAE;IACbC,IAAI,EAAE,iBAAiB;IACvBC,IAAI,EAAE;EACR,CAAC;EACDC,cAAc,EAAE;IACdC,OAAO,EAAE,2BAA2B;IACpCC,OAAO,EAAE;EACX,CAAC;EACDC,YAAY,EAAE;IACZC,KAAK,EAAE;EACT,CAAC;EACDC,SAAS,EAAE;IACTC,SAAS,EAAE,aAAa;IACxBC,QAAQ,EAAE,SAAAA,SAACA,SAAQ;MAAA,UAAAf,MAAA,CAAQe,SAAQ,GAAG,EAAE;IAAA,CAAmB;IAC3DC,SAAS,EAAE,SAAAA,UAACA,UAAS;MAAA,mBAAAhB,MAAA,CAAiBgB,UAAS;IAAA;EACjD,CAAC;EACDC,aAAa,EAAE;IACbtB,KAAK,EAAE,QAAQ;IACfuB,KAAK,EAAE,SAAAA,MAACA,MAAK;MAAA,oBAAAlB,MAAA,CAAkBkB,MAAK;IAAA;EACtC,CAAC;EACDC,aAAa,EAAE;IACbC,QAAQ,EAAE,UAAU;IACpBC,KAAK,EAAE,QAAQ;IACfC,MAAM,EAAE,YAAY;IACpBC,QAAQ,EAAE,cAAc;IACxBC,WAAW,EAAE;EACf,CAAC;EACDC,cAAc,EAAE;IACdC,IAAI,EAAE,MAAM;IACZC,MAAM,EAAE,QAAQ;IAChBC,SAAS,EAAE,mDAAmD;IAC9DC,YAAY,EAAE,oCAAoC;IAClDC,WAAW,EAAE,qBAAqB;IAClCC,aAAa,EAAE,SAAS;IACxBC,cAAc,EAAE;EAClB,CAAC;EACDC,cAAc,EAAE;IACdC,GAAG,EAAE;EACP;AACF,CAAC"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}