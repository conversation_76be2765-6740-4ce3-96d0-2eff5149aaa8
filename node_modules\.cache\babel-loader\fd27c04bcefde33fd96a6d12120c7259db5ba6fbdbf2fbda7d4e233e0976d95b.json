{"ast": null, "code": "function _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return _typeof(key) === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (_typeof(input) !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (_typeof(res) !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\nimport { ref, getCurrentInstance } from 'vue';\nimport { get_invite } from '@/api/self/index';\nimport { useI18n } from 'vue-i18n';\nimport store from '@/store/index';\nimport { formatTime } from '@/api/format.js';\nimport { watch } from 'vue';\nimport { useRoute } from \"vue-router\";\nimport { useRouter } from 'vue-router';\nexport default {\n  setup: function setup() {\n    var _route$query;\n    var route = useRoute();\n    var _useRouter = useRouter(),\n      push = _useRouter.push;\n    var url = ((_route$query = route.query) === null || _route$query === void 0 ? void 0 : _route$query.data) || 1;\n    var list = [[1, 'US'], [86, 'CN'], [44, 'GB'], [33, 'FR'], [81, 'JP'], [34, 'ES'], [20, 'EG'], [84, 'VN'], [7, 'RU'], [51, 'PE'], [54, 'AR'], [93, 'AF'], [376, 'AD'], [61, 'AU'], [43, 'AT'], [880, 'BD'], [375, 'BY'], [32, 'BE'], [975, 'BT'], [55, 'BR'], [673, 'BN'], [359, 'BG'], [855, 'KH'], [237, 'CM'], ['001', 'CA'], [236, 'CF'], [56, 'CL'], [57, 'CO'], [242, 'CG'], [53, 'CU'], [420, 'CZ'], [45, 'DK'], [251, 'ET'], [697, 'FJ'], [358, 'FI'], [30, 'GR'], [299, 'GL'], [852, 'HK'], [36, 'HU'], [354, 'IS'], [91, 'IN'], [62, 'ID'], [972, 'IL'], [39, 'IT'], [49, 'DE'], [98, 'IR'], [1876, 'JM'], [353, 'IE'], [85, 'KP'], [82, 'KR'], [856, 'LA'], [52, 'MX'], [60, 'MY'], [377, 'MC'], [47, 'NO'], [212, 'MA'], [95, 'MM'], [31, 'NL'], [64, 'NZ'], [63, 'PH'], [48, 'PL'], [351, 'PT'], [92, 'PK'], [974, 'QA'], [40, 'RO'], [250, 'RW'], [966, 'SA'], [65, 'SG'], [421, 'SK'], [27, 'ZA'], [94, 'LK'], [46, 'SE'], [41, 'CH'], [886, 'TW'], [66, 'TH'], [670, 'TL'], [971, 'AE'], [90, 'TR'], [380, 'UA'], [598, 'UY'], [965, 'KW'], [968, 'OM']];\n    var tourl = function tourl(v) {\n      if (url == 1) {\n        push({\n          path: '/login',\n          query: {\n            data: v\n          }\n        });\n      } else {\n        push({\n          path: '/register',\n          query: {\n            data: v\n          }\n        });\n      }\n    };\n    var search = ref('');\n    var list2 = [];\n    watch(function () {\n      return search;\n    }, function (newVal) {\n      list2.length = 0;\n      list.forEach(function (v) {\n        var text = v[0];\n        text = text.toString();\n        var news = _objectSpread({}, newVal)._value;\n        var ret = text.search(news);\n        if (ret != -1) {\n          list2.push(v);\n        }\n      });\n    }, {\n      deep: true\n    });\n    return {\n      list: list,\n      list2: list2,\n      search: search,\n      tourl: tourl\n    };\n  },\n  mounted: function mounted() {}\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}