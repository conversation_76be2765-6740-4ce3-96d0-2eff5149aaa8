{"ast": null, "code": "import { common_parameters } from '@/api/login/index';\nimport { useI18n } from 'vue-i18n';\nimport store from '@/store/index';\nimport { vantLocales } from '@/i18n/i18n';\nimport myScroll from './components/scroll.vue';\nexport default {\n  components: {\n    myScroll: myScroll\n  },\n  setup: function setup() {\n    var _useI18n = useI18n(),\n      locale = _useI18n.locale;\n    // const {proxy} = getCurrentInstance()\n    var changeFavicon = function changeFavicon(link) {\n      var $favicon = document.querySelector('link[rel=\"icon\"]');\n      // If a <link rel=\"icon\"> element already exists,\n      // change its href to the given link.\n      if ($favicon !== null) {\n        $favicon.href = link;\n        // Otherwise, create a new element and append it to <head>.\n      } else {\n        $favicon = document.createElement(\"link\");\n        $favicon.rel = \"icon\";\n        $favicon.href = link;\n        document.head.appendChild($favicon);\n      }\n    };\n    Promise.resolve(common_parameters()).then(function (res) {\n      if (res.code === 0) {\n        var info = JSON.parse(JSON.stringify(res.data));\n        locale.value = info.language;\n        var languageList = info.languageList;\n        var json = languageList.find(function (rr) {\n          return rr.link == info.language;\n        });\n        var langImg = json && json.image_url;\n        store.dispatch('changelang', info.language);\n        store.dispatch('changelangImg', langImg);\n        store.dispatch('changebaseInfo', info);\n        changeFavicon(info.site_icon); // 动态修改网站图标\n        document.title = info.app_name; // 动态修改网站标题\n        vantLocales(info.language);\n      }\n    }).catch(function (err) {\n      console.log(err);\n    });\n    return {};\n  }\n};", "map": {"version": 3, "names": ["common_parameters", "useI18n", "store", "vantLocales", "myScroll", "components", "setup", "_useI18n", "locale", "changeFavicon", "link", "$favicon", "document", "querySelector", "href", "createElement", "rel", "head", "append<PERSON><PERSON><PERSON>", "Promise", "resolve", "then", "res", "code", "info", "JSON", "parse", "stringify", "data", "value", "language", "languageList", "json", "find", "rr", "langImg", "image_url", "dispatch", "site_icon", "title", "app_name", "catch", "err", "console", "log"], "sources": ["C:\\Users\\<USER>\\Desktop\\vue3_3.0\\src\\App.vue"], "sourcesContent": ["<template>\r\n  <my-scroll>\r\n    <router-view/>\r\n  </my-scroll>\r\n</template>\r\n<script>\r\nimport {common_parameters} from '@/api/login/index'\r\nimport { useI18n } from 'vue-i18n'\r\nimport store from '@/store/index'\r\nimport {vantLocales} from '@/i18n/i18n';\r\nimport myScroll from './components/scroll.vue'\r\n\r\nexport default {\r\n  components:{myScroll},\r\n  setup(){\r\n    const { locale } = useI18n()\r\n    // const {proxy} = getCurrentInstance()\r\n     const changeFavicon = link => {\r\n        let $favicon = document.querySelector('link[rel=\"icon\"]');\r\n        // If a <link rel=\"icon\"> element already exists,\r\n        // change its href to the given link.\r\n        if ($favicon !== null) {\r\n          $favicon.href = link;\r\n          // Otherwise, create a new element and append it to <head>.\r\n        } else {\r\n          $favicon = document.createElement(\"link\");\r\n          $favicon.rel = \"icon\";\r\n          $favicon.href = link;\r\n          document.head.appendChild($favicon);\r\n        }\r\n      };\r\n      Promise.resolve(common_parameters()).then(res=>{\r\n        if(res.code === 0) {\r\n          const info = JSON.parse(JSON.stringify(res.data))\r\n            locale.value = info.language\r\n            let languageList = info.languageList\r\n            let json = languageList.find(rr => rr.link == info.language)\r\n            let langImg = json && json.image_url\r\n            store.dispatch('changelang',info.language)\r\n            store.dispatch('changelangImg',langImg)\r\n            store.dispatch('changebaseInfo',info)\r\n            changeFavicon(info.site_icon); // 动态修改网站图标\r\n            document.title = info.app_name; // 动态修改网站标题\r\n            vantLocales(info.language)\r\n        }\r\n        }).catch(err=>{\r\n          console.log(err)\r\n      })\r\n\r\n    return {}\r\n  }\r\n}\r\n</script>\r\n<style lang='scss'>\r\n#app {\r\n  font-family:\"PingFang SC,Helvetica Neue,Helvetica,Arial,Hiragino Sans GB,Heiti SC,Microsoft YaHei,WenQuanYi Micro Hei,sans-serif\"!important;\r\n  -webkit-font-smoothing: antialiased;\r\n  -moz-osx-font-smoothing: grayscale;\r\n  text-align: center;\r\n  color: $textColor;\r\n  // background-image: linear-gradient(#d4dff5,#f3f7fd);\r\n  background-image: #fff;\r\n  height: 100vh;\r\n}\r\n\r\n</style>\r\n"], "mappings": "AAMA,SAAQA,iBAAiB,QAAO,mBAAkB;AAClD,SAASC,OAAM,QAAS,UAAS;AACjC,OAAOC,KAAI,MAAO,eAAc;AAChC,SAAQC,WAAW,QAAO,aAAa;AACvC,OAAOC,QAAO,MAAO,yBAAwB;AAE7C,eAAe;EACbC,UAAU,EAAC;IAACD,QAAQ,EAARA;EAAQ,CAAC;EACrBE,KAAK,WAAAA,MAAA,EAAE;IACL,IAAAC,QAAA,GAAmBN,OAAO,CAAC;MAAnBO,MAAK,GAAAD,QAAA,CAALC,MAAK;IACb;IACC,IAAMC,aAAY,GAAI,SAAhBA,aAAYA,CAAIC,IAAG,EAAK;MAC3B,IAAIC,QAAO,GAAIC,QAAQ,CAACC,aAAa,CAAC,kBAAkB,CAAC;MACzD;MACA;MACA,IAAIF,QAAO,KAAM,IAAI,EAAE;QACrBA,QAAQ,CAACG,IAAG,GAAIJ,IAAI;QACpB;MACF,OAAO;QACLC,QAAO,GAAIC,QAAQ,CAACG,aAAa,CAAC,MAAM,CAAC;QACzCJ,QAAQ,CAACK,GAAE,GAAI,MAAM;QACrBL,QAAQ,CAACG,IAAG,GAAIJ,IAAI;QACpBE,QAAQ,CAACK,IAAI,CAACC,WAAW,CAACP,QAAQ,CAAC;MACrC;IACF,CAAC;IACDQ,OAAO,CAACC,OAAO,CAACpB,iBAAiB,CAAC,CAAC,CAAC,CAACqB,IAAI,CAAC,UAAAC,GAAG,EAAE;MAC7C,IAAGA,GAAG,CAACC,IAAG,KAAM,CAAC,EAAE;QACjB,IAAMC,IAAG,GAAIC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAACL,GAAG,CAACM,IAAI,CAAC;QAC9CpB,MAAM,CAACqB,KAAI,GAAIL,IAAI,CAACM,QAAO;QAC3B,IAAIC,YAAW,GAAIP,IAAI,CAACO,YAAW;QACnC,IAAIC,IAAG,GAAID,YAAY,CAACE,IAAI,CAAC,UAAAC,EAAC;UAAA,OAAKA,EAAE,CAACxB,IAAG,IAAKc,IAAI,CAACM,QAAQ;QAAA;QAC3D,IAAIK,OAAM,GAAIH,IAAG,IAAKA,IAAI,CAACI,SAAQ;QACnClC,KAAK,CAACmC,QAAQ,CAAC,YAAY,EAACb,IAAI,CAACM,QAAQ;QACzC5B,KAAK,CAACmC,QAAQ,CAAC,eAAe,EAACF,OAAO;QACtCjC,KAAK,CAACmC,QAAQ,CAAC,gBAAgB,EAACb,IAAI;QACpCf,aAAa,CAACe,IAAI,CAACc,SAAS,CAAC,EAAE;QAC/B1B,QAAQ,CAAC2B,KAAI,GAAIf,IAAI,CAACgB,QAAQ,EAAE;QAChCrC,WAAW,CAACqB,IAAI,CAACM,QAAQ;MAC7B;IACA,CAAC,CAAC,CAACW,KAAK,CAAC,UAAAC,GAAG,EAAE;MACZC,OAAO,CAACC,GAAG,CAACF,GAAG;IACnB,CAAC;IAEH,OAAO,CAAC;EACV;AACF"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}