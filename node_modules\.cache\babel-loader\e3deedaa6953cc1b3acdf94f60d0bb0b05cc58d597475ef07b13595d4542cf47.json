{"ast": null, "code": "import { getCurrentInstance } from \"vue\";\nvar current = 0;\nfunction useId() {\n  var vm = getCurrentInstance();\n  var _ref = (vm == null ? void 0 : vm.type) || {},\n    _ref$name = _ref.name,\n    name = _ref$name === void 0 ? \"unknown\" : _ref$name;\n  if (process.env.NODE_ENV === \"test\") {\n    return name;\n  }\n  return \"\".concat(name, \"-\").concat(++current);\n}\nexport { useId };", "map": {"version": 3, "names": ["getCurrentInstance", "current", "useId", "vm", "_ref", "type", "_ref$name", "name", "process", "env", "NODE_ENV", "concat"], "sources": ["C:/Users/<USER>/Downloads/Telegram Desktop/vue3_3.0/vue3_3.0/node_modules/vant/es/composables/use-id.mjs"], "sourcesContent": ["import { getCurrentInstance } from \"vue\";\nlet current = 0;\nfunction useId() {\n  const vm = getCurrentInstance();\n  const { name = \"unknown\" } = (vm == null ? void 0 : vm.type) || {};\n  if (process.env.NODE_ENV === \"test\") {\n    return name;\n  }\n  return `${name}-${++current}`;\n}\nexport {\n  useId\n};\n"], "mappings": "AAAA,SAASA,kBAAkB,QAAQ,KAAK;AACxC,IAAIC,OAAO,GAAG,CAAC;AACf,SAASC,KAAKA,CAAA,EAAG;EACf,IAAMC,EAAE,GAAGH,kBAAkB,CAAC,CAAC;EAC/B,IAAAI,IAAA,GAA6B,CAACD,EAAE,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACE,IAAI,KAAK,CAAC,CAAC;IAAAC,SAAA,GAAAF,IAAA,CAA1DG,IAAI;IAAJA,IAAI,GAAAD,SAAA,cAAG,SAAS,GAAAA,SAAA;EACxB,IAAIE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,MAAM,EAAE;IACnC,OAAOH,IAAI;EACb;EACA,UAAAI,MAAA,CAAUJ,IAAI,OAAAI,MAAA,CAAI,EAAEV,OAAO;AAC7B;AACA,SACEC,KAAK"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}