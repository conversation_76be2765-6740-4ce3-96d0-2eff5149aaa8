{"ast": null, "code": "import { onMounted, ref } from 'vue';\nimport { Icon } from 'vant';\nexport default {\n  name: 'myMessage',\n  components: {\n    Icon: Icon\n  },\n  props: {\n    message: {\n      type: String,\n      default: ''\n    },\n    type: {\n      type: String,\n      // warn 警告  error 错误  success 成功\n      default: 'warn'\n    }\n  },\n  setup: function setup() {\n    // 定义一个对象，包含三种情况的样式，对象key就是类型字符串\n    var style = {\n      warn: {\n        icon: 'icon-warning',\n        color: '#E6A23C',\n        backgroundColor: 'rgb(253, 246, 236)',\n        borderColor: 'rgb(250, 236, 216)'\n      },\n      error: {\n        icon: 'clear',\n        color: '#F56C6C',\n        backgroundColor: 'rgb(254, 240, 240)',\n        borderColor: 'rgb(253, 226, 226)'\n      },\n      success: {\n        icon: 'checked',\n        color: '#67C23A',\n        backgroundColor: 'rgb(240, 249, 235)',\n        borderColor: 'rgb(225, 243, 216)'\n      }\n    };\n    // 控制动画\n    var isShow = ref(false);\n    // 组件模板渲染成功后触发\n    onMounted(function () {\n      isShow.value = true;\n    });\n    return {\n      style: style,\n      isShow: isShow\n    };\n  }\n};", "map": {"version": 3, "names": ["onMounted", "ref", "Icon", "name", "components", "props", "message", "type", "String", "default", "setup", "style", "warn", "icon", "color", "backgroundColor", "borderColor", "error", "success", "isShow", "value"], "sources": ["C:\\Users\\<USER>\\Downloads\\Telegram Desktop\\vue3_3.0\\vue3_3.0\\src\\components\\message.vue"], "sourcesContent": ["<template>\r\n  <Transition name=\"down\">\r\n    <div class=\"my-message\" :style=\"style[type]\" v-show='isShow'>\r\n      <!-- 上面绑定的是样式 -->\r\n      <!-- 不同提示图标会变 -->\r\n      <!-- <i class=\"iconfont\" :class=\"[style[type].icon]\"></i> -->\r\n      <Icon :name=\"style[type].icon\" size=\"18\" />\r\n      <span class=\"text\">{{message}}</span>\r\n    </div>\r\n  </Transition>\r\n</template>\r\n<script>\r\nimport { onMounted, ref } from 'vue'\r\nimport { Icon } from 'vant'\r\nexport default {\r\n  name: 'myMessage',\r\n  components:{Icon},\r\n  props: {\r\n    message: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    type: {\r\n      type: String,\r\n      // warn 警告  error 错误  success 成功\r\n      default: 'warn'\r\n    }\r\n  },\r\n  setup () {\r\n    // 定义一个对象，包含三种情况的样式，对象key就是类型字符串\r\n    const style = {\r\n      warn: {\r\n        icon: 'icon-warning',\r\n        color: '#E6A23C',\r\n        backgroundColor: 'rgb(253, 246, 236)',\r\n        borderColor: 'rgb(250, 236, 216)'\r\n      },\r\n      error: {\r\n        icon: 'clear',\r\n        color: '#F56C6C',\r\n        backgroundColor: 'rgb(254, 240, 240)',\r\n        borderColor: 'rgb(253, 226, 226)'\r\n      },\r\n      success: {\r\n        icon: 'checked',\r\n        color: '#67C23A',\r\n        backgroundColor: 'rgb(240, 249, 235)',\r\n        borderColor: 'rgb(225, 243, 216)'\r\n      }\r\n    }\r\n    // 控制动画\r\n    const isShow = ref(false)\r\n    // 组件模板渲染成功后触发\r\n    onMounted(() => {\r\n      isShow.value = true\r\n    })\r\n    return { style, isShow }\r\n  }\r\n}\r\n</script>\r\n<style scoped lang=\"scss\">\r\n.down {\r\n  &-enter {\r\n    &-from {\r\n      transform: translate3d(0, -75px, 0);\r\n      opacity: 0;\r\n    }\r\n    &-active {\r\n      transition: all 0.5s;\r\n    }\r\n    &-to {\r\n      transform: none;\r\n      opacity: 1;\r\n    }\r\n  }\r\n}\r\n.my-message {\r\n  width: 90%;\r\n  // height: 60px;\r\n  position: fixed;\r\n  z-index: 9999;\r\n  left: 50%;\r\n  top: 25px;\r\n  transform: translate(-50%);\r\n  line-height: 1.2;\r\n  padding: 12px 25px;\r\n  border: 1px solid #e4e4e4;\r\n  background: #f5f5f5;\r\n  color: #999;\r\n  border-radius: 4px;\r\n  i {\r\n    margin-right: 12px;\r\n    vertical-align: middle;\r\n  }\r\n  .text {\r\n    vertical-align: middle;\r\n  }\r\n}\r\n</style>"], "mappings": "AAYA,SAASA,SAAS,EAAEC,GAAE,QAAS,KAAI;AACnC,SAASC,IAAG,QAAS,MAAK;AAC1B,eAAe;EACbC,IAAI,EAAE,WAAW;EACjBC,UAAU,EAAC;IAACF,IAAI,EAAJA;EAAI,CAAC;EACjBG,KAAK,EAAE;IACLC,OAAO,EAAE;MACPC,IAAI,EAAEC,MAAM;MACZC,OAAO,EAAE;IACX,CAAC;IACDF,IAAI,EAAE;MACJA,IAAI,EAAEC,MAAM;MACZ;MACAC,OAAO,EAAE;IACX;EACF,CAAC;EACDC,KAAI,WAAAA,MAAA,EAAK;IACP;IACA,IAAMC,KAAI,GAAI;MACZC,IAAI,EAAE;QACJC,IAAI,EAAE,cAAc;QACpBC,KAAK,EAAE,SAAS;QAChBC,eAAe,EAAE,oBAAoB;QACrCC,WAAW,EAAE;MACf,CAAC;MACDC,KAAK,EAAE;QACLJ,IAAI,EAAE,OAAO;QACbC,KAAK,EAAE,SAAS;QAChBC,eAAe,EAAE,oBAAoB;QACrCC,WAAW,EAAE;MACf,CAAC;MACDE,OAAO,EAAE;QACPL,IAAI,EAAE,SAAS;QACfC,KAAK,EAAE,SAAS;QAChBC,eAAe,EAAE,oBAAoB;QACrCC,WAAW,EAAE;MACf;IACF;IACA;IACA,IAAMG,MAAK,GAAIlB,GAAG,CAAC,KAAK;IACxB;IACAD,SAAS,CAAC,YAAM;MACdmB,MAAM,CAACC,KAAI,GAAI,IAAG;IACpB,CAAC;IACD,OAAO;MAAET,KAAK,EAALA,KAAK;MAAEQ,MAAK,EAALA;IAAO;EACzB;AACF"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}