{"ast": null, "code": "'use strict';\n\nfunction _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\nvar formats = require('./formats');\nvar has = Object.prototype.hasOwnProperty;\nvar isArray = Array.isArray;\nvar hexTable = function () {\n  var array = [];\n  for (var i = 0; i < 256; ++i) {\n    array.push('%' + ((i < 16 ? '0' : '') + i.toString(16)).toUpperCase());\n  }\n  return array;\n}();\nvar compactQueue = function compactQueue(queue) {\n  while (queue.length > 1) {\n    var item = queue.pop();\n    var obj = item.obj[item.prop];\n    if (isArray(obj)) {\n      var compacted = [];\n      for (var j = 0; j < obj.length; ++j) {\n        if (typeof obj[j] !== 'undefined') {\n          compacted.push(obj[j]);\n        }\n      }\n      item.obj[item.prop] = compacted;\n    }\n  }\n};\nvar arrayToObject = function arrayToObject(source, options) {\n  var obj = options && options.plainObjects ? Object.create(null) : {};\n  for (var i = 0; i < source.length; ++i) {\n    if (typeof source[i] !== 'undefined') {\n      obj[i] = source[i];\n    }\n  }\n  return obj;\n};\nvar merge = function merge(target, source, options) {\n  /* eslint no-param-reassign: 0 */\n  if (!source) {\n    return target;\n  }\n  if (_typeof(source) !== 'object') {\n    if (isArray(target)) {\n      target.push(source);\n    } else if (target && _typeof(target) === 'object') {\n      if (options && (options.plainObjects || options.allowPrototypes) || !has.call(Object.prototype, source)) {\n        target[source] = true;\n      }\n    } else {\n      return [target, source];\n    }\n    return target;\n  }\n  if (!target || _typeof(target) !== 'object') {\n    return [target].concat(source);\n  }\n  var mergeTarget = target;\n  if (isArray(target) && !isArray(source)) {\n    mergeTarget = arrayToObject(target, options);\n  }\n  if (isArray(target) && isArray(source)) {\n    source.forEach(function (item, i) {\n      if (has.call(target, i)) {\n        var targetItem = target[i];\n        if (targetItem && _typeof(targetItem) === 'object' && item && _typeof(item) === 'object') {\n          target[i] = merge(targetItem, item, options);\n        } else {\n          target.push(item);\n        }\n      } else {\n        target[i] = item;\n      }\n    });\n    return target;\n  }\n  return Object.keys(source).reduce(function (acc, key) {\n    var value = source[key];\n    if (has.call(acc, key)) {\n      acc[key] = merge(acc[key], value, options);\n    } else {\n      acc[key] = value;\n    }\n    return acc;\n  }, mergeTarget);\n};\nvar assign = function assignSingleSource(target, source) {\n  return Object.keys(source).reduce(function (acc, key) {\n    acc[key] = source[key];\n    return acc;\n  }, target);\n};\nvar decode = function decode(str, decoder, charset) {\n  var strWithoutPlus = str.replace(/\\+/g, ' ');\n  if (charset === 'iso-8859-1') {\n    // unescape never throws, no try...catch needed:\n    return strWithoutPlus.replace(/%[0-9a-f]{2}/gi, unescape);\n  }\n  // utf-8\n  try {\n    return decodeURIComponent(strWithoutPlus);\n  } catch (e) {\n    return strWithoutPlus;\n  }\n};\nvar encode = function encode(str, defaultEncoder, charset, kind, format) {\n  // This code was originally written by Brian White (mscdex) for the io.js core querystring library.\n  // It has been adapted here for stricter adherence to RFC 3986\n  if (str.length === 0) {\n    return str;\n  }\n  var string = str;\n  if (_typeof(str) === 'symbol') {\n    string = Symbol.prototype.toString.call(str);\n  } else if (typeof str !== 'string') {\n    string = String(str);\n  }\n  if (charset === 'iso-8859-1') {\n    return escape(string).replace(/%u[0-9a-f]{4}/gi, function ($0) {\n      return '%26%23' + parseInt($0.slice(2), 16) + '%3B';\n    });\n  }\n  var out = '';\n  for (var i = 0; i < string.length; ++i) {\n    var c = string.charCodeAt(i);\n    if (c === 0x2D // -\n    || c === 0x2E // .\n    || c === 0x5F // _\n    || c === 0x7E // ~\n    || c >= 0x30 && c <= 0x39 // 0-9\n    || c >= 0x41 && c <= 0x5A // a-z\n    || c >= 0x61 && c <= 0x7A // A-Z\n    || format === formats.RFC1738 && (c === 0x28 || c === 0x29) // ( )\n    ) {\n      out += string.charAt(i);\n      continue;\n    }\n    if (c < 0x80) {\n      out = out + hexTable[c];\n      continue;\n    }\n    if (c < 0x800) {\n      out = out + (hexTable[0xC0 | c >> 6] + hexTable[0x80 | c & 0x3F]);\n      continue;\n    }\n    if (c < 0xD800 || c >= 0xE000) {\n      out = out + (hexTable[0xE0 | c >> 12] + hexTable[0x80 | c >> 6 & 0x3F] + hexTable[0x80 | c & 0x3F]);\n      continue;\n    }\n    i += 1;\n    c = 0x10000 + ((c & 0x3FF) << 10 | string.charCodeAt(i) & 0x3FF);\n    /* eslint operator-linebreak: [2, \"before\"] */\n    out += hexTable[0xF0 | c >> 18] + hexTable[0x80 | c >> 12 & 0x3F] + hexTable[0x80 | c >> 6 & 0x3F] + hexTable[0x80 | c & 0x3F];\n  }\n  return out;\n};\nvar compact = function compact(value) {\n  var queue = [{\n    obj: {\n      o: value\n    },\n    prop: 'o'\n  }];\n  var refs = [];\n  for (var i = 0; i < queue.length; ++i) {\n    var item = queue[i];\n    var obj = item.obj[item.prop];\n    var keys = Object.keys(obj);\n    for (var j = 0; j < keys.length; ++j) {\n      var key = keys[j];\n      var val = obj[key];\n      if (_typeof(val) === 'object' && val !== null && refs.indexOf(val) === -1) {\n        queue.push({\n          obj: obj,\n          prop: key\n        });\n        refs.push(val);\n      }\n    }\n  }\n  compactQueue(queue);\n  return value;\n};\nvar isRegExp = function isRegExp(obj) {\n  return Object.prototype.toString.call(obj) === '[object RegExp]';\n};\nvar isBuffer = function isBuffer(obj) {\n  if (!obj || _typeof(obj) !== 'object') {\n    return false;\n  }\n  return !!(obj.constructor && obj.constructor.isBuffer && obj.constructor.isBuffer(obj));\n};\nvar combine = function combine(a, b) {\n  return [].concat(a, b);\n};\nvar maybeMap = function maybeMap(val, fn) {\n  if (isArray(val)) {\n    var mapped = [];\n    for (var i = 0; i < val.length; i += 1) {\n      mapped.push(fn(val[i]));\n    }\n    return mapped;\n  }\n  return fn(val);\n};\nmodule.exports = {\n  arrayToObject: arrayToObject,\n  assign: assign,\n  combine: combine,\n  compact: compact,\n  decode: decode,\n  encode: encode,\n  isBuffer: isBuffer,\n  isRegExp: isRegExp,\n  maybeMap: maybeMap,\n  merge: merge\n};", "map": {"version": 3, "names": ["_typeof", "obj", "Symbol", "iterator", "constructor", "prototype", "formats", "require", "has", "Object", "hasOwnProperty", "isArray", "Array", "hexTable", "array", "i", "push", "toString", "toUpperCase", "compactQueue", "queue", "length", "item", "pop", "prop", "compacted", "j", "arrayToObject", "source", "options", "plainObjects", "create", "merge", "target", "allowPrototypes", "call", "concat", "mergeTarget", "for<PERSON>ach", "targetItem", "keys", "reduce", "acc", "key", "value", "assign", "assignSingleSource", "decode", "str", "decoder", "charset", "strWithoutPlus", "replace", "unescape", "decodeURIComponent", "e", "encode", "defaultEncoder", "kind", "format", "string", "String", "escape", "$0", "parseInt", "slice", "out", "c", "charCodeAt", "RFC1738", "char<PERSON>t", "compact", "o", "refs", "val", "indexOf", "isRegExp", "<PERSON><PERSON><PERSON><PERSON>", "combine", "a", "b", "maybeMap", "fn", "mapped", "module", "exports"], "sources": ["C:/Users/<USER>/Downloads/Telegram Desktop/vue3_3.0/vue3_3.0/node_modules/qs/lib/utils.js"], "sourcesContent": ["'use strict';\n\nvar formats = require('./formats');\n\nvar has = Object.prototype.hasOwnProperty;\nvar isArray = Array.isArray;\n\nvar hexTable = (function () {\n    var array = [];\n    for (var i = 0; i < 256; ++i) {\n        array.push('%' + ((i < 16 ? '0' : '') + i.toString(16)).toUpperCase());\n    }\n\n    return array;\n}());\n\nvar compactQueue = function compactQueue(queue) {\n    while (queue.length > 1) {\n        var item = queue.pop();\n        var obj = item.obj[item.prop];\n\n        if (isArray(obj)) {\n            var compacted = [];\n\n            for (var j = 0; j < obj.length; ++j) {\n                if (typeof obj[j] !== 'undefined') {\n                    compacted.push(obj[j]);\n                }\n            }\n\n            item.obj[item.prop] = compacted;\n        }\n    }\n};\n\nvar arrayToObject = function arrayToObject(source, options) {\n    var obj = options && options.plainObjects ? Object.create(null) : {};\n    for (var i = 0; i < source.length; ++i) {\n        if (typeof source[i] !== 'undefined') {\n            obj[i] = source[i];\n        }\n    }\n\n    return obj;\n};\n\nvar merge = function merge(target, source, options) {\n    /* eslint no-param-reassign: 0 */\n    if (!source) {\n        return target;\n    }\n\n    if (typeof source !== 'object') {\n        if (isArray(target)) {\n            target.push(source);\n        } else if (target && typeof target === 'object') {\n            if ((options && (options.plainObjects || options.allowPrototypes)) || !has.call(Object.prototype, source)) {\n                target[source] = true;\n            }\n        } else {\n            return [target, source];\n        }\n\n        return target;\n    }\n\n    if (!target || typeof target !== 'object') {\n        return [target].concat(source);\n    }\n\n    var mergeTarget = target;\n    if (isArray(target) && !isArray(source)) {\n        mergeTarget = arrayToObject(target, options);\n    }\n\n    if (isArray(target) && isArray(source)) {\n        source.forEach(function (item, i) {\n            if (has.call(target, i)) {\n                var targetItem = target[i];\n                if (targetItem && typeof targetItem === 'object' && item && typeof item === 'object') {\n                    target[i] = merge(targetItem, item, options);\n                } else {\n                    target.push(item);\n                }\n            } else {\n                target[i] = item;\n            }\n        });\n        return target;\n    }\n\n    return Object.keys(source).reduce(function (acc, key) {\n        var value = source[key];\n\n        if (has.call(acc, key)) {\n            acc[key] = merge(acc[key], value, options);\n        } else {\n            acc[key] = value;\n        }\n        return acc;\n    }, mergeTarget);\n};\n\nvar assign = function assignSingleSource(target, source) {\n    return Object.keys(source).reduce(function (acc, key) {\n        acc[key] = source[key];\n        return acc;\n    }, target);\n};\n\nvar decode = function (str, decoder, charset) {\n    var strWithoutPlus = str.replace(/\\+/g, ' ');\n    if (charset === 'iso-8859-1') {\n        // unescape never throws, no try...catch needed:\n        return strWithoutPlus.replace(/%[0-9a-f]{2}/gi, unescape);\n    }\n    // utf-8\n    try {\n        return decodeURIComponent(strWithoutPlus);\n    } catch (e) {\n        return strWithoutPlus;\n    }\n};\n\nvar encode = function encode(str, defaultEncoder, charset, kind, format) {\n    // This code was originally written by Brian White (mscdex) for the io.js core querystring library.\n    // It has been adapted here for stricter adherence to RFC 3986\n    if (str.length === 0) {\n        return str;\n    }\n\n    var string = str;\n    if (typeof str === 'symbol') {\n        string = Symbol.prototype.toString.call(str);\n    } else if (typeof str !== 'string') {\n        string = String(str);\n    }\n\n    if (charset === 'iso-8859-1') {\n        return escape(string).replace(/%u[0-9a-f]{4}/gi, function ($0) {\n            return '%26%23' + parseInt($0.slice(2), 16) + '%3B';\n        });\n    }\n\n    var out = '';\n    for (var i = 0; i < string.length; ++i) {\n        var c = string.charCodeAt(i);\n\n        if (\n            c === 0x2D // -\n            || c === 0x2E // .\n            || c === 0x5F // _\n            || c === 0x7E // ~\n            || (c >= 0x30 && c <= 0x39) // 0-9\n            || (c >= 0x41 && c <= 0x5A) // a-z\n            || (c >= 0x61 && c <= 0x7A) // A-Z\n            || (format === formats.RFC1738 && (c === 0x28 || c === 0x29)) // ( )\n        ) {\n            out += string.charAt(i);\n            continue;\n        }\n\n        if (c < 0x80) {\n            out = out + hexTable[c];\n            continue;\n        }\n\n        if (c < 0x800) {\n            out = out + (hexTable[0xC0 | (c >> 6)] + hexTable[0x80 | (c & 0x3F)]);\n            continue;\n        }\n\n        if (c < 0xD800 || c >= 0xE000) {\n            out = out + (hexTable[0xE0 | (c >> 12)] + hexTable[0x80 | ((c >> 6) & 0x3F)] + hexTable[0x80 | (c & 0x3F)]);\n            continue;\n        }\n\n        i += 1;\n        c = 0x10000 + (((c & 0x3FF) << 10) | (string.charCodeAt(i) & 0x3FF));\n        /* eslint operator-linebreak: [2, \"before\"] */\n        out += hexTable[0xF0 | (c >> 18)]\n            + hexTable[0x80 | ((c >> 12) & 0x3F)]\n            + hexTable[0x80 | ((c >> 6) & 0x3F)]\n            + hexTable[0x80 | (c & 0x3F)];\n    }\n\n    return out;\n};\n\nvar compact = function compact(value) {\n    var queue = [{ obj: { o: value }, prop: 'o' }];\n    var refs = [];\n\n    for (var i = 0; i < queue.length; ++i) {\n        var item = queue[i];\n        var obj = item.obj[item.prop];\n\n        var keys = Object.keys(obj);\n        for (var j = 0; j < keys.length; ++j) {\n            var key = keys[j];\n            var val = obj[key];\n            if (typeof val === 'object' && val !== null && refs.indexOf(val) === -1) {\n                queue.push({ obj: obj, prop: key });\n                refs.push(val);\n            }\n        }\n    }\n\n    compactQueue(queue);\n\n    return value;\n};\n\nvar isRegExp = function isRegExp(obj) {\n    return Object.prototype.toString.call(obj) === '[object RegExp]';\n};\n\nvar isBuffer = function isBuffer(obj) {\n    if (!obj || typeof obj !== 'object') {\n        return false;\n    }\n\n    return !!(obj.constructor && obj.constructor.isBuffer && obj.constructor.isBuffer(obj));\n};\n\nvar combine = function combine(a, b) {\n    return [].concat(a, b);\n};\n\nvar maybeMap = function maybeMap(val, fn) {\n    if (isArray(val)) {\n        var mapped = [];\n        for (var i = 0; i < val.length; i += 1) {\n            mapped.push(fn(val[i]));\n        }\n        return mapped;\n    }\n    return fn(val);\n};\n\nmodule.exports = {\n    arrayToObject: arrayToObject,\n    assign: assign,\n    combine: combine,\n    compact: compact,\n    decode: decode,\n    encode: encode,\n    isBuffer: isBuffer,\n    isRegExp: isRegExp,\n    maybeMap: maybeMap,\n    merge: merge\n};\n"], "mappings": "AAAA,YAAY;;AAAC,SAAAA,QAAAC,GAAA,sCAAAD,OAAA,wBAAAE,MAAA,uBAAAA,MAAA,CAAAC,QAAA,aAAAF,GAAA,kBAAAA,GAAA,gBAAAA,GAAA,WAAAA,GAAA,yBAAAC,MAAA,IAAAD,GAAA,CAAAG,WAAA,KAAAF,MAAA,IAAAD,GAAA,KAAAC,MAAA,CAAAG,SAAA,qBAAAJ,GAAA,KAAAD,OAAA,CAAAC,GAAA;AAEb,IAAIK,OAAO,GAAGC,OAAO,CAAC,WAAW,CAAC;AAElC,IAAIC,GAAG,GAAGC,MAAM,CAACJ,SAAS,CAACK,cAAc;AACzC,IAAIC,OAAO,GAAGC,KAAK,CAACD,OAAO;AAE3B,IAAIE,QAAQ,GAAI,YAAY;EACxB,IAAIC,KAAK,GAAG,EAAE;EACd,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,GAAG,EAAE,EAAEA,CAAC,EAAE;IAC1BD,KAAK,CAACE,IAAI,CAAC,GAAG,GAAG,CAAC,CAACD,CAAC,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,IAAIA,CAAC,CAACE,QAAQ,CAAC,EAAE,CAAC,EAAEC,WAAW,CAAC,CAAC,CAAC;EAC1E;EAEA,OAAOJ,KAAK;AAChB,CAAC,CAAC,CAAE;AAEJ,IAAIK,YAAY,GAAG,SAASA,YAAYA,CAACC,KAAK,EAAE;EAC5C,OAAOA,KAAK,CAACC,MAAM,GAAG,CAAC,EAAE;IACrB,IAAIC,IAAI,GAAGF,KAAK,CAACG,GAAG,CAAC,CAAC;IACtB,IAAItB,GAAG,GAAGqB,IAAI,CAACrB,GAAG,CAACqB,IAAI,CAACE,IAAI,CAAC;IAE7B,IAAIb,OAAO,CAACV,GAAG,CAAC,EAAE;MACd,IAAIwB,SAAS,GAAG,EAAE;MAElB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGzB,GAAG,CAACoB,MAAM,EAAE,EAAEK,CAAC,EAAE;QACjC,IAAI,OAAOzB,GAAG,CAACyB,CAAC,CAAC,KAAK,WAAW,EAAE;UAC/BD,SAAS,CAACT,IAAI,CAACf,GAAG,CAACyB,CAAC,CAAC,CAAC;QAC1B;MACJ;MAEAJ,IAAI,CAACrB,GAAG,CAACqB,IAAI,CAACE,IAAI,CAAC,GAAGC,SAAS;IACnC;EACJ;AACJ,CAAC;AAED,IAAIE,aAAa,GAAG,SAASA,aAAaA,CAACC,MAAM,EAAEC,OAAO,EAAE;EACxD,IAAI5B,GAAG,GAAG4B,OAAO,IAAIA,OAAO,CAACC,YAAY,GAAGrB,MAAM,CAACsB,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;EACpE,KAAK,IAAIhB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGa,MAAM,CAACP,MAAM,EAAE,EAAEN,CAAC,EAAE;IACpC,IAAI,OAAOa,MAAM,CAACb,CAAC,CAAC,KAAK,WAAW,EAAE;MAClCd,GAAG,CAACc,CAAC,CAAC,GAAGa,MAAM,CAACb,CAAC,CAAC;IACtB;EACJ;EAEA,OAAOd,GAAG;AACd,CAAC;AAED,IAAI+B,KAAK,GAAG,SAASA,KAAKA,CAACC,MAAM,EAAEL,MAAM,EAAEC,OAAO,EAAE;EAChD;EACA,IAAI,CAACD,MAAM,EAAE;IACT,OAAOK,MAAM;EACjB;EAEA,IAAIjC,OAAA,CAAO4B,MAAM,MAAK,QAAQ,EAAE;IAC5B,IAAIjB,OAAO,CAACsB,MAAM,CAAC,EAAE;MACjBA,MAAM,CAACjB,IAAI,CAACY,MAAM,CAAC;IACvB,CAAC,MAAM,IAAIK,MAAM,IAAIjC,OAAA,CAAOiC,MAAM,MAAK,QAAQ,EAAE;MAC7C,IAAKJ,OAAO,KAAKA,OAAO,CAACC,YAAY,IAAID,OAAO,CAACK,eAAe,CAAC,IAAK,CAAC1B,GAAG,CAAC2B,IAAI,CAAC1B,MAAM,CAACJ,SAAS,EAAEuB,MAAM,CAAC,EAAE;QACvGK,MAAM,CAACL,MAAM,CAAC,GAAG,IAAI;MACzB;IACJ,CAAC,MAAM;MACH,OAAO,CAACK,MAAM,EAAEL,MAAM,CAAC;IAC3B;IAEA,OAAOK,MAAM;EACjB;EAEA,IAAI,CAACA,MAAM,IAAIjC,OAAA,CAAOiC,MAAM,MAAK,QAAQ,EAAE;IACvC,OAAO,CAACA,MAAM,CAAC,CAACG,MAAM,CAACR,MAAM,CAAC;EAClC;EAEA,IAAIS,WAAW,GAAGJ,MAAM;EACxB,IAAItB,OAAO,CAACsB,MAAM,CAAC,IAAI,CAACtB,OAAO,CAACiB,MAAM,CAAC,EAAE;IACrCS,WAAW,GAAGV,aAAa,CAACM,MAAM,EAAEJ,OAAO,CAAC;EAChD;EAEA,IAAIlB,OAAO,CAACsB,MAAM,CAAC,IAAItB,OAAO,CAACiB,MAAM,CAAC,EAAE;IACpCA,MAAM,CAACU,OAAO,CAAC,UAAUhB,IAAI,EAAEP,CAAC,EAAE;MAC9B,IAAIP,GAAG,CAAC2B,IAAI,CAACF,MAAM,EAAElB,CAAC,CAAC,EAAE;QACrB,IAAIwB,UAAU,GAAGN,MAAM,CAAClB,CAAC,CAAC;QAC1B,IAAIwB,UAAU,IAAIvC,OAAA,CAAOuC,UAAU,MAAK,QAAQ,IAAIjB,IAAI,IAAItB,OAAA,CAAOsB,IAAI,MAAK,QAAQ,EAAE;UAClFW,MAAM,CAAClB,CAAC,CAAC,GAAGiB,KAAK,CAACO,UAAU,EAAEjB,IAAI,EAAEO,OAAO,CAAC;QAChD,CAAC,MAAM;UACHI,MAAM,CAACjB,IAAI,CAACM,IAAI,CAAC;QACrB;MACJ,CAAC,MAAM;QACHW,MAAM,CAAClB,CAAC,CAAC,GAAGO,IAAI;MACpB;IACJ,CAAC,CAAC;IACF,OAAOW,MAAM;EACjB;EAEA,OAAOxB,MAAM,CAAC+B,IAAI,CAACZ,MAAM,CAAC,CAACa,MAAM,CAAC,UAAUC,GAAG,EAAEC,GAAG,EAAE;IAClD,IAAIC,KAAK,GAAGhB,MAAM,CAACe,GAAG,CAAC;IAEvB,IAAInC,GAAG,CAAC2B,IAAI,CAACO,GAAG,EAAEC,GAAG,CAAC,EAAE;MACpBD,GAAG,CAACC,GAAG,CAAC,GAAGX,KAAK,CAACU,GAAG,CAACC,GAAG,CAAC,EAAEC,KAAK,EAAEf,OAAO,CAAC;IAC9C,CAAC,MAAM;MACHa,GAAG,CAACC,GAAG,CAAC,GAAGC,KAAK;IACpB;IACA,OAAOF,GAAG;EACd,CAAC,EAAEL,WAAW,CAAC;AACnB,CAAC;AAED,IAAIQ,MAAM,GAAG,SAASC,kBAAkBA,CAACb,MAAM,EAAEL,MAAM,EAAE;EACrD,OAAOnB,MAAM,CAAC+B,IAAI,CAACZ,MAAM,CAAC,CAACa,MAAM,CAAC,UAAUC,GAAG,EAAEC,GAAG,EAAE;IAClDD,GAAG,CAACC,GAAG,CAAC,GAAGf,MAAM,CAACe,GAAG,CAAC;IACtB,OAAOD,GAAG;EACd,CAAC,EAAET,MAAM,CAAC;AACd,CAAC;AAED,IAAIc,MAAM,GAAG,SAATA,MAAMA,CAAaC,GAAG,EAAEC,OAAO,EAAEC,OAAO,EAAE;EAC1C,IAAIC,cAAc,GAAGH,GAAG,CAACI,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;EAC5C,IAAIF,OAAO,KAAK,YAAY,EAAE;IAC1B;IACA,OAAOC,cAAc,CAACC,OAAO,CAAC,gBAAgB,EAAEC,QAAQ,CAAC;EAC7D;EACA;EACA,IAAI;IACA,OAAOC,kBAAkB,CAACH,cAAc,CAAC;EAC7C,CAAC,CAAC,OAAOI,CAAC,EAAE;IACR,OAAOJ,cAAc;EACzB;AACJ,CAAC;AAED,IAAIK,MAAM,GAAG,SAASA,MAAMA,CAACR,GAAG,EAAES,cAAc,EAAEP,OAAO,EAAEQ,IAAI,EAAEC,MAAM,EAAE;EACrE;EACA;EACA,IAAIX,GAAG,CAAC3B,MAAM,KAAK,CAAC,EAAE;IAClB,OAAO2B,GAAG;EACd;EAEA,IAAIY,MAAM,GAAGZ,GAAG;EAChB,IAAIhD,OAAA,CAAOgD,GAAG,MAAK,QAAQ,EAAE;IACzBY,MAAM,GAAG1D,MAAM,CAACG,SAAS,CAACY,QAAQ,CAACkB,IAAI,CAACa,GAAG,CAAC;EAChD,CAAC,MAAM,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;IAChCY,MAAM,GAAGC,MAAM,CAACb,GAAG,CAAC;EACxB;EAEA,IAAIE,OAAO,KAAK,YAAY,EAAE;IAC1B,OAAOY,MAAM,CAACF,MAAM,CAAC,CAACR,OAAO,CAAC,iBAAiB,EAAE,UAAUW,EAAE,EAAE;MAC3D,OAAO,QAAQ,GAAGC,QAAQ,CAACD,EAAE,CAACE,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK;IACvD,CAAC,CAAC;EACN;EAEA,IAAIC,GAAG,GAAG,EAAE;EACZ,KAAK,IAAInD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6C,MAAM,CAACvC,MAAM,EAAE,EAAEN,CAAC,EAAE;IACpC,IAAIoD,CAAC,GAAGP,MAAM,CAACQ,UAAU,CAACrD,CAAC,CAAC;IAE5B,IACIoD,CAAC,KAAK,IAAI,CAAC;IAAA,GACRA,CAAC,KAAK,IAAI,CAAC;IAAA,GACXA,CAAC,KAAK,IAAI,CAAC;IAAA,GACXA,CAAC,KAAK,IAAI,CAAC;IAAA,GACVA,CAAC,IAAI,IAAI,IAAIA,CAAC,IAAI,IAAK,CAAC;IAAA,GACxBA,CAAC,IAAI,IAAI,IAAIA,CAAC,IAAI,IAAK,CAAC;IAAA,GACxBA,CAAC,IAAI,IAAI,IAAIA,CAAC,IAAI,IAAK,CAAC;IAAA,GACxBR,MAAM,KAAKrD,OAAO,CAAC+D,OAAO,KAAKF,CAAC,KAAK,IAAI,IAAIA,CAAC,KAAK,IAAI,CAAE,CAAC;IAAA,EAChE;MACED,GAAG,IAAIN,MAAM,CAACU,MAAM,CAACvD,CAAC,CAAC;MACvB;IACJ;IAEA,IAAIoD,CAAC,GAAG,IAAI,EAAE;MACVD,GAAG,GAAGA,GAAG,GAAGrD,QAAQ,CAACsD,CAAC,CAAC;MACvB;IACJ;IAEA,IAAIA,CAAC,GAAG,KAAK,EAAE;MACXD,GAAG,GAAGA,GAAG,IAAIrD,QAAQ,CAAC,IAAI,GAAIsD,CAAC,IAAI,CAAE,CAAC,GAAGtD,QAAQ,CAAC,IAAI,GAAIsD,CAAC,GAAG,IAAK,CAAC,CAAC;MACrE;IACJ;IAEA,IAAIA,CAAC,GAAG,MAAM,IAAIA,CAAC,IAAI,MAAM,EAAE;MAC3BD,GAAG,GAAGA,GAAG,IAAIrD,QAAQ,CAAC,IAAI,GAAIsD,CAAC,IAAI,EAAG,CAAC,GAAGtD,QAAQ,CAAC,IAAI,GAAKsD,CAAC,IAAI,CAAC,GAAI,IAAK,CAAC,GAAGtD,QAAQ,CAAC,IAAI,GAAIsD,CAAC,GAAG,IAAK,CAAC,CAAC;MAC3G;IACJ;IAEApD,CAAC,IAAI,CAAC;IACNoD,CAAC,GAAG,OAAO,IAAK,CAACA,CAAC,GAAG,KAAK,KAAK,EAAE,GAAKP,MAAM,CAACQ,UAAU,CAACrD,CAAC,CAAC,GAAG,KAAM,CAAC;IACpE;IACAmD,GAAG,IAAIrD,QAAQ,CAAC,IAAI,GAAIsD,CAAC,IAAI,EAAG,CAAC,GAC3BtD,QAAQ,CAAC,IAAI,GAAKsD,CAAC,IAAI,EAAE,GAAI,IAAK,CAAC,GACnCtD,QAAQ,CAAC,IAAI,GAAKsD,CAAC,IAAI,CAAC,GAAI,IAAK,CAAC,GAClCtD,QAAQ,CAAC,IAAI,GAAIsD,CAAC,GAAG,IAAK,CAAC;EACrC;EAEA,OAAOD,GAAG;AACd,CAAC;AAED,IAAIK,OAAO,GAAG,SAASA,OAAOA,CAAC3B,KAAK,EAAE;EAClC,IAAIxB,KAAK,GAAG,CAAC;IAAEnB,GAAG,EAAE;MAAEuE,CAAC,EAAE5B;IAAM,CAAC;IAAEpB,IAAI,EAAE;EAAI,CAAC,CAAC;EAC9C,IAAIiD,IAAI,GAAG,EAAE;EAEb,KAAK,IAAI1D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGK,KAAK,CAACC,MAAM,EAAE,EAAEN,CAAC,EAAE;IACnC,IAAIO,IAAI,GAAGF,KAAK,CAACL,CAAC,CAAC;IACnB,IAAId,GAAG,GAAGqB,IAAI,CAACrB,GAAG,CAACqB,IAAI,CAACE,IAAI,CAAC;IAE7B,IAAIgB,IAAI,GAAG/B,MAAM,CAAC+B,IAAI,CAACvC,GAAG,CAAC;IAC3B,KAAK,IAAIyB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGc,IAAI,CAACnB,MAAM,EAAE,EAAEK,CAAC,EAAE;MAClC,IAAIiB,GAAG,GAAGH,IAAI,CAACd,CAAC,CAAC;MACjB,IAAIgD,GAAG,GAAGzE,GAAG,CAAC0C,GAAG,CAAC;MAClB,IAAI3C,OAAA,CAAO0E,GAAG,MAAK,QAAQ,IAAIA,GAAG,KAAK,IAAI,IAAID,IAAI,CAACE,OAAO,CAACD,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;QACrEtD,KAAK,CAACJ,IAAI,CAAC;UAAEf,GAAG,EAAEA,GAAG;UAAEuB,IAAI,EAAEmB;QAAI,CAAC,CAAC;QACnC8B,IAAI,CAACzD,IAAI,CAAC0D,GAAG,CAAC;MAClB;IACJ;EACJ;EAEAvD,YAAY,CAACC,KAAK,CAAC;EAEnB,OAAOwB,KAAK;AAChB,CAAC;AAED,IAAIgC,QAAQ,GAAG,SAASA,QAAQA,CAAC3E,GAAG,EAAE;EAClC,OAAOQ,MAAM,CAACJ,SAAS,CAACY,QAAQ,CAACkB,IAAI,CAAClC,GAAG,CAAC,KAAK,iBAAiB;AACpE,CAAC;AAED,IAAI4E,QAAQ,GAAG,SAASA,QAAQA,CAAC5E,GAAG,EAAE;EAClC,IAAI,CAACA,GAAG,IAAID,OAAA,CAAOC,GAAG,MAAK,QAAQ,EAAE;IACjC,OAAO,KAAK;EAChB;EAEA,OAAO,CAAC,EAAEA,GAAG,CAACG,WAAW,IAAIH,GAAG,CAACG,WAAW,CAACyE,QAAQ,IAAI5E,GAAG,CAACG,WAAW,CAACyE,QAAQ,CAAC5E,GAAG,CAAC,CAAC;AAC3F,CAAC;AAED,IAAI6E,OAAO,GAAG,SAASA,OAAOA,CAACC,CAAC,EAAEC,CAAC,EAAE;EACjC,OAAO,EAAE,CAAC5C,MAAM,CAAC2C,CAAC,EAAEC,CAAC,CAAC;AAC1B,CAAC;AAED,IAAIC,QAAQ,GAAG,SAASA,QAAQA,CAACP,GAAG,EAAEQ,EAAE,EAAE;EACtC,IAAIvE,OAAO,CAAC+D,GAAG,CAAC,EAAE;IACd,IAAIS,MAAM,GAAG,EAAE;IACf,KAAK,IAAIpE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2D,GAAG,CAACrD,MAAM,EAAEN,CAAC,IAAI,CAAC,EAAE;MACpCoE,MAAM,CAACnE,IAAI,CAACkE,EAAE,CAACR,GAAG,CAAC3D,CAAC,CAAC,CAAC,CAAC;IAC3B;IACA,OAAOoE,MAAM;EACjB;EACA,OAAOD,EAAE,CAACR,GAAG,CAAC;AAClB,CAAC;AAEDU,MAAM,CAACC,OAAO,GAAG;EACb1D,aAAa,EAAEA,aAAa;EAC5BkB,MAAM,EAAEA,MAAM;EACdiC,OAAO,EAAEA,OAAO;EAChBP,OAAO,EAAEA,OAAO;EAChBxB,MAAM,EAAEA,MAAM;EACdS,MAAM,EAAEA,MAAM;EACdqB,QAAQ,EAAEA,QAAQ;EAClBD,QAAQ,EAAEA,QAAQ;EAClBK,QAAQ,EAAEA,QAAQ;EAClBjD,KAAK,EAAEA;AACX,CAAC"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}