{"ast": null, "code": "import { createRouter, createWebHistory } from 'vue-router';\nimport login from '../views/login/login.vue';\nvar routes = [{\n  path: '/login',\n  name: 'login',\n  component: login\n}, {\n  path: '/area',\n  name: 'area',\n  component: function component() {\n    return import('@/views/login/area.vue');\n  }\n}, {\n  path: '/register',\n  name: 'register',\n  component: function component() {\n    return import('@/views/login/register.vue');\n  }\n}, {\n  path: '/',\n  name: 'index',\n  component: function component() {\n    return import('@/views/index/index.vue');\n  },\n  redirect: '/home',\n  children: [{\n    path: '/home',\n    name: 'home',\n    meta: {\n      keepAlive: true,\n      name: 'Home'\n    },\n    component: function component() {\n      return import( /* webpackChunkName: \"index\" */'@/views/index/home.vue');\n    }\n  }, {\n    path: '/order',\n    name: 'order',\n    meta: {\n      keepAlive: true,\n      name: 'order'\n    },\n    component: function component() {\n      return import( /* webpackChunkName: \"index\" */'@/views/order/index.vue');\n    }\n  }, {\n    path: '/tel',\n    name: 'tel',\n    meta: {\n      keepAlive: true,\n      name: 'tel'\n    },\n    component: function component() {\n      return import( /* webpackChunkName: \"index\" */'@/views/tel/index.vue');\n    }\n  }, {\n    path: '/obj',\n    name: 'obj',\n    meta: {\n      keepAlive: true,\n      name: 'obj'\n    },\n    component: function component() {\n      return import( /* webpackChunkName: \"index\" */'@/views/obj/index.vue');\n    }\n  }, {\n    path: '/self',\n    name: 'self',\n    meta: {\n      keepAlive: true,\n      name: 'self'\n    },\n    component: function component() {\n      return import( /* webpackChunkName: \"index\" */'@/views/self/index.vue');\n    }\n  },\n  // 绑定银行卡\n  {\n    path: '/bingbank',\n    name: 'bingbank',\n    meta: {\n      keepAlive: true,\n      name: 'bingbank'\n    },\n    component: function component() {\n      return import( /* webpackChunkName: \"index\" */'@/views/self/components/bingbank.vue');\n    }\n  },\n  // 收获地址\n  {\n    path: '/address',\n    name: 'address',\n    meta: {\n      keepAlive: true,\n      name: 'address'\n    },\n    component: function component() {\n      return import( /* webpackChunkName: \"index\" */'@/views/self/components/address.vue');\n    }\n  },\n  // 设置头像\n  {\n    path: '/avatar',\n    name: 'avatar',\n    meta: {\n      keepAlive: true,\n      name: 'avatar'\n    },\n    component: function component() {\n      return import( /* webpackChunkName: \"index\" */'@/views/self/components/avatar.vue');\n    }\n  },\n  // 团队记录\n  {\n    path: '/team',\n    name: 'team',\n    meta: {\n      keepAlive: true,\n      name: 'team'\n    },\n    component: function component() {\n      return import( /* webpackChunkName: \"index\" */'@/views/self/components/team.vue');\n    }\n  },\n  // 我的消息\n  {\n    path: '/message',\n    name: 'message',\n    meta: {\n      keepAlive: true,\n      name: 'message'\n    },\n    component: function component() {\n      return import( /* webpackChunkName: \"index\" */'@/views/self/components/message.vue');\n    }\n  },\n  // 修改密码\n  {\n    path: '/editPwd',\n    name: 'editPwd',\n    meta: {\n      keepAlive: true,\n      name: 'editPwd'\n    },\n    component: function component() {\n      return import( /* webpackChunkName: \"index\" */'@/views/self/components/editPwd.vue');\n    }\n  },\n  // 账户明细\n  {\n    path: '/account_details',\n    name: 'account_details',\n    meta: {\n      keepAlive: true,\n      name: 'account_details'\n    },\n    component: function component() {\n      return import( /* webpackChunkName: \"index\" */'@/views/self/components/account_details.vue');\n    }\n  },\n  // 分享\n  {\n    path: '/share',\n    name: 'share',\n    meta: {\n      keepAlive: true,\n      name: 'share'\n    },\n    component: function component() {\n      return import( /* webpackChunkName: \"index\" */'@/views/self/components/share.vue');\n    }\n  },\n  // 充值记录\n  {\n    path: '/recharge',\n    name: 'recharge',\n    meta: {\n      keepAlive: true,\n      name: 'recharge'\n    },\n    component: function component() {\n      return import( /* webpackChunkName: \"index\" */'@/views/self/components/recharge.vue');\n    }\n  },\n  // 提现记录\n  {\n    path: '/deposit',\n    name: 'deposit',\n    meta: {\n      keepAlive: true,\n      name: 'deposit'\n    },\n    component: function component() {\n      return import( /* webpackChunkName: \"index\" */'@/views/self/components/deposit.vue');\n    }\n  },\n  // 提现\n  {\n    path: '/drawing',\n    name: 'drawing',\n    meta: {\n      keepAlive: true,\n      name: 'drawing'\n    },\n    component: function component() {\n      return import( /* webpackChunkName: \"index\" */'@/views/self/components/drawing.vue');\n    }\n  },\n  // 余利宝\n  {\n    path: '/libao',\n    name: 'libao',\n    meta: {\n      keepAlive: true,\n      name: 'libao'\n    },\n    component: function component() {\n      return import( /* webpackChunkName: \"index\" */'@/views/index/components/libao.vue');\n    }\n  },\n  // 余利宝转入记录\n  {\n    path: '/libao_jl',\n    name: 'libao_jl',\n    meta: {\n      keepAlive: true,\n      name: 'libao_jl'\n    },\n    component: function component() {\n      return import( /* webpackChunkName: \"index\" */'@/views/index/components/libao_jl.vue');\n    }\n  },\n  // 提升等级\n  {\n    path: '/addlevel',\n    name: 'addlevel',\n    meta: {\n      keepAlive: true,\n      name: 'addlevel'\n    },\n    component: function component() {\n      return import( /* webpackChunkName: \"index\" */'@/views/index/components/addlevel.vue');\n    }\n  },\n  // 充值付款确认\n  {\n    path: '/next_cz',\n    name: 'next_cz',\n    meta: {\n      keepAlive: true,\n      name: 'next_cz'\n    },\n    component: function component() {\n      return import( /* webpackChunkName: \"index\" */'@/views/index/components/next_cz.vue');\n    }\n  },\n  // 充值付款确认\n  {\n    path: '/next_cz2',\n    name: 'next_cz2',\n    meta: {\n      keepAlive: true,\n      name: 'next_cz2'\n    },\n    component: function component() {\n      return import( /* webpackChunkName: \"index\" */'@/views/index/components/next_cz2.vue');\n    }\n  },\n  // 充值\n  {\n    path: '/chongzhi',\n    name: 'chongzhi',\n    meta: {\n      keepAlive: true,\n      name: 'chongzhi'\n    },\n    component: function component() {\n      return import( /* webpackChunkName: \"index\" */'@/views/index/components/chongzhi.vue');\n    }\n  },\n  // 充值\n  {\n    path: '/content',\n    name: 'content',\n    meta: {\n      keepAlive: true,\n      name: 'content'\n    },\n    component: function component() {\n      return import( /* webpackChunkName: \"index\" */'@/views/index/components/content.vue');\n    }\n  }]\n}];\nvar router = createRouter({\n  history: createWebHistory(process.env.BASE_URL),\n  routes: routes\n});\n\n// 4. 你还可以监听路由拦截，比如权限验证。\nrouter.beforeEach(function (to, from, next) {\n  // 1. 每个条件执行后都要跟上 next() 或 使用路由跳转 api 否则页面就会停留一动不动\n  // 2. 要合理的搭配条件语句，避免出现路由死循环。\n  var token = localStorage.getItem('token');\n  if (to.name == 'home' || to.name == 'login' || to.name == 'register' || to.name == 'area') {\n    next();\n  } else if (!token) {\n    next('/login');\n  } else {\n    next();\n  }\n});\nexport default router;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}