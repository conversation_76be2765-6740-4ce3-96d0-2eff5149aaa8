{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString, createElementVNode as _createElementVNode, pushScopeId as _pushScopeId, popScopeId as _popScopeId } from \"vue\";\nvar _withScopeId = function _withScopeId(n) {\n  return _pushScopeId(\"data-v-0bf1196c\"), n = n(), _popScopeId(), n;\n};\nvar _hoisted_1 = {\n  class: \"tel home\"\n};\nvar _hoisted_2 = {\n  class: \"l\"\n};\nvar _hoisted_3 = {\n  class: \"time\"\n};\nvar _hoisted_4 = {\n  class: \"time\"\n};\nvar _hoisted_5 = {\n  class: \"time\"\n};\nvar _hoisted_6 = {\n  class: \"time red\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_van_icon = _resolveComponent(\"van-icon\");\n  var _component_van_nav_bar = _resolveComponent(\"van-nav-bar\");\n  var _component_van_list = _resolveComponent(\"van-list\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_van_nav_bar, {\n    title: _ctx.$t('msg.txjl'),\n    \"left-arrow\": \"\",\n    onClickLeft: _cache[0] || (_cache[0] = function ($event) {\n      return _ctx.$router.go(-1);\n    }),\n    onClickRight: $setup.clickRight\n  }, {\n    right: _withCtx(function () {\n      return [_createVNode(_component_van_icon, {\n        name: \"comment-o\",\n        size: \"18\"\n      })];\n    }),\n    _: 1\n  }, 8, [\"title\", \"onClickRight\"]), _createVNode(_component_van_list, {\n    finished: $setup.finished,\n    offset: 100,\n    \"finished-text\": _ctx.$t('msg.not_move'),\n    onLoad: $setup.getCW\n  }, {\n    default: _withCtx(function () {\n      return [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.list, function (item, index) {\n        var _$setup$tabs$find;\n        return _openBlock(), _createElementBlock(\"div\", {\n          class: \"address\",\n          key: index\n        }, [_createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, _toDisplayString(_ctx.$t('msg.sqje')) + \"：\" + _toDisplayString($setup.currency) + \" \" + _toDisplayString(item.num || '0.00'), 1), _createElementVNode(\"div\", _hoisted_4, _toDisplayString(_ctx.$t('msg.sxf')) + \"：0，\" + _toDisplayString(_ctx.$t('msg.dz')) + \"：\" + _toDisplayString($setup.currency) + _toDisplayString(item.num || '0.00'), 1), _createElementVNode(\"div\", _hoisted_5, _toDisplayString(_ctx.$t('msg.sqsj')) + \"：\" + _toDisplayString($setup.formatTime('', item.addtime)), 1), _createElementVNode(\"div\", _hoisted_6, _toDisplayString(((_$setup$tabs$find = $setup.tabs.find(function (rr) {\n          return rr.value == item.status;\n        })) === null || _$setup$tabs$find === void 0 ? void 0 : _$setup$tabs$find.label) || item.status), 1)])]);\n      }), 128))];\n    }),\n    _: 1\n  }, 8, [\"finished\", \"finished-text\", \"onLoad\"])]);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}