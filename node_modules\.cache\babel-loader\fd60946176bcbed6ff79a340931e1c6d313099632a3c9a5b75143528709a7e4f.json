{"ast": null, "code": "import http from '@/request/index';\nimport qs from 'qs';\n// 首页数据\nexport var getHomeData = function getHomeData() {\n  return http.get('/index/home').then(function (result) {\n    return result.data;\n  });\n};\n\n// 首页数据\nexport var get_msg = function get_msg() {\n  return http.get('/index/get_msg').then(function (result) {\n    return result.data;\n  });\n};\n\n// 首页数据\nexport var get_level_list = function get_level_list() {\n  return http.get('/index/get_level_list').then(function (result) {\n    return result.data;\n  });\n};\n\n// 首页数据\nexport var get_lixibao = function get_lixibao() {\n  return http.get('/ctrl/lixibao').then(function (result) {\n    return result.data;\n  });\n};\n// 首页数据\nexport var lixibao_chu = function lixibao_chu(params) {\n  return http.get('/ctrl/lixibao_chu?' + qs.stringify(params)).then(function (result) {\n    return result.data;\n  });\n};\n// 首页数据\nexport var getdetailbyid = function getdetailbyid(id) {\n  return http.get('/my/detail?id=' + id).then(function (result) {\n    return result.data;\n  });\n};\n\n// 首页数据\nexport var lixibao_ru = function lixibao_ru(params) {\n  return http.post('/ctrl/lixibao_ru', qs.stringify(params)).then(function (result) {\n    return result.data;\n  });\n};\n\n// 首页数据\nexport var get_lixibao_chu = function get_lixibao_chu(params) {\n  return http.post('/ctrl/lixibao_chu', qs.stringify(params)).then(function (result) {\n    return result.data;\n  });\n};\n\n// 首页数据\nexport var get_recharge = function get_recharge(params) {\n  return http.post('/ctrl/recharge?' + qs.stringify(params)).then(function (result) {\n    return result.data;\n  });\n};\n\n// 首页数据\nexport var get_recharge2 = function get_recharge2(params) {\n  return http.post('/ctrl/recharge2?' + qs.stringify(params)).then(function (result) {\n    return result.data;\n  });\n};\n\n// 首页数据\nexport var bank_recharge = function bank_recharge(params) {\n  return http.post('/ctrl/bank_recharge', qs.stringify(params)).then(function (result) {\n    return result.data;\n  });\n};\n\n// 首页数据\nexport var headpicUpdatae = function headpicUpdatae(params) {\n  return http.post('/my/headpicUpdatae', qs.stringify(params)).then(function (result) {\n    return result.data;\n  });\n};\n\n// 首页数据\nexport var uploadImg = function uploadImg(params) {\n  return http.post('/admin/index.html?s=/admin/api.plugs/upload', params, {\n    // 因为我们上传了图片,因此需要单独执行请求头的Content-Type\n    headers: {\n      // 表示上传的是文件,而不是普通的表单数据\n      'Content-Type': 'multipart/form-data'\n    }\n  }).then(function (result) {\n    return result.data;\n  });\n};", "map": {"version": 3, "names": ["http", "qs", "getHomeData", "get", "then", "result", "data", "get_msg", "get_level_list", "get_lixibao", "lixibao_chu", "params", "stringify", "getdetailbyid", "id", "lixibao_ru", "post", "get_lixibao_chu", "get_recharge", "get_recharge2", "bank_recharge", "headpicUp<PERSON><PERSON>", "uploadImg", "headers"], "sources": ["C:/Users/<USER>/Desktop/vue3_3.0/src/api/home/<USER>"], "sourcesContent": ["import http from '@/request/index'\r\n\r\nimport qs from 'qs';\r\n// 首页数据\r\nexport const getHomeData = () => {\r\n    return http.get('/index/home')\r\n      .then((result) => {\r\n        return result.data\r\n      })\r\n  }\r\n\r\n// 首页数据\r\nexport const get_msg = () => {\r\n    return http.get('/index/get_msg')\r\n      .then((result) => {\r\n        return result.data\r\n      })\r\n  }\r\n\r\n// 首页数据\r\nexport const get_level_list = () => {\r\n    return http.get('/index/get_level_list')\r\n      .then((result) => {\r\n        return result.data\r\n      })\r\n  }\r\n\r\n// 首页数据\r\nexport const get_lixibao = () => {\r\n    return http.get('/ctrl/lixibao')\r\n      .then((result) => {\r\n        return result.data\r\n      })\r\n  }\r\n// 首页数据\r\nexport const lixibao_chu = (params) => {\r\n    return http.get('/ctrl/lixibao_chu?'+qs.stringify(params))\r\n      .then((result) => {\r\n        return result.data\r\n      })\r\n  }\r\n// 首页数据\r\nexport const getdetailbyid = (id) => {\r\n    return http.get('/my/detail?id='+id)\r\n      .then((result) => {\r\n        return result.data\r\n      })\r\n  }\r\n\r\n// 首页数据\r\nexport const lixibao_ru = (params) => {\r\n    return http.post('/ctrl/lixibao_ru',qs.stringify(params))\r\n      .then((result) => {\r\n        return result.data\r\n      })\r\n  }\r\n\r\n\r\n// 首页数据\r\nexport const get_lixibao_chu = (params) => {\r\n    return http.post('/ctrl/lixibao_chu',qs.stringify(params))\r\n      .then((result) => {\r\n        return result.data\r\n      })\r\n  }\r\n\r\n\r\n// 首页数据\r\nexport const get_recharge = (params) => {\r\n    return http.post('/ctrl/recharge?'+qs.stringify(params))\r\n      .then((result) => {\r\n        return result.data\r\n      })\r\n  }\r\n\r\n\r\n// 首页数据\r\nexport const get_recharge2 = (params) => {\r\n    return http.post('/ctrl/recharge2?'+qs.stringify(params))\r\n      .then((result) => {\r\n        return result.data\r\n      })\r\n  }\r\n\r\n// 首页数据\r\nexport const bank_recharge = (params) => {\r\n    return http.post('/ctrl/bank_recharge',qs.stringify(params))\r\n      .then((result) => {\r\n        return result.data\r\n      })\r\n  }\r\n\r\n// 首页数据\r\nexport const headpicUpdatae = (params) => {\r\n    return http.post('/my/headpicUpdatae',qs.stringify(params))\r\n      .then((result) => {\r\n        return result.data\r\n      })\r\n  }\r\n\r\n\r\n// 首页数据\r\nexport const uploadImg = (params) => {\r\n    return http.post('/admin/index.html?s=/admin/api.plugs/upload',params, {\r\n      // 因为我们上传了图片,因此需要单独执行请求头的Content-Type\r\n      headers: {\r\n        // 表示上传的是文件,而不是普通的表单数据\r\n        'Content-Type': 'multipart/form-data'\r\n      }\r\n    })\r\n      .then((result) => {\r\n        return result.data\r\n      })\r\n  }\r\n\r\n"], "mappings": "AAAA,OAAOA,IAAI,MAAM,iBAAiB;AAElC,OAAOC,EAAE,MAAM,IAAI;AACnB;AACA,OAAO,IAAMC,WAAW,GAAG,SAAdA,WAAWA,CAAA,EAAS;EAC7B,OAAOF,IAAI,CAACG,GAAG,CAAC,aAAa,CAAC,CAC3BC,IAAI,CAAC,UAACC,MAAM,EAAK;IAChB,OAAOA,MAAM,CAACC,IAAI;EACpB,CAAC,CAAC;AACN,CAAC;;AAEH;AACA,OAAO,IAAMC,OAAO,GAAG,SAAVA,OAAOA,CAAA,EAAS;EACzB,OAAOP,IAAI,CAACG,GAAG,CAAC,gBAAgB,CAAC,CAC9BC,IAAI,CAAC,UAACC,MAAM,EAAK;IAChB,OAAOA,MAAM,CAACC,IAAI;EACpB,CAAC,CAAC;AACN,CAAC;;AAEH;AACA,OAAO,IAAME,cAAc,GAAG,SAAjBA,cAAcA,CAAA,EAAS;EAChC,OAAOR,IAAI,CAACG,GAAG,CAAC,uBAAuB,CAAC,CACrCC,IAAI,CAAC,UAACC,MAAM,EAAK;IAChB,OAAOA,MAAM,CAACC,IAAI;EACpB,CAAC,CAAC;AACN,CAAC;;AAEH;AACA,OAAO,IAAMG,WAAW,GAAG,SAAdA,WAAWA,CAAA,EAAS;EAC7B,OAAOT,IAAI,CAACG,GAAG,CAAC,eAAe,CAAC,CAC7BC,IAAI,CAAC,UAACC,MAAM,EAAK;IAChB,OAAOA,MAAM,CAACC,IAAI;EACpB,CAAC,CAAC;AACN,CAAC;AACH;AACA,OAAO,IAAMI,WAAW,GAAG,SAAdA,WAAWA,CAAIC,MAAM,EAAK;EACnC,OAAOX,IAAI,CAACG,GAAG,CAAC,oBAAoB,GAACF,EAAE,CAACW,SAAS,CAACD,MAAM,CAAC,CAAC,CACvDP,IAAI,CAAC,UAACC,MAAM,EAAK;IAChB,OAAOA,MAAM,CAACC,IAAI;EACpB,CAAC,CAAC;AACN,CAAC;AACH;AACA,OAAO,IAAMO,aAAa,GAAG,SAAhBA,aAAaA,CAAIC,EAAE,EAAK;EACjC,OAAOd,IAAI,CAACG,GAAG,CAAC,gBAAgB,GAACW,EAAE,CAAC,CACjCV,IAAI,CAAC,UAACC,MAAM,EAAK;IAChB,OAAOA,MAAM,CAACC,IAAI;EACpB,CAAC,CAAC;AACN,CAAC;;AAEH;AACA,OAAO,IAAMS,UAAU,GAAG,SAAbA,UAAUA,CAAIJ,MAAM,EAAK;EAClC,OAAOX,IAAI,CAACgB,IAAI,CAAC,kBAAkB,EAACf,EAAE,CAACW,SAAS,CAACD,MAAM,CAAC,CAAC,CACtDP,IAAI,CAAC,UAACC,MAAM,EAAK;IAChB,OAAOA,MAAM,CAACC,IAAI;EACpB,CAAC,CAAC;AACN,CAAC;;AAGH;AACA,OAAO,IAAMW,eAAe,GAAG,SAAlBA,eAAeA,CAAIN,MAAM,EAAK;EACvC,OAAOX,IAAI,CAACgB,IAAI,CAAC,mBAAmB,EAACf,EAAE,CAACW,SAAS,CAACD,MAAM,CAAC,CAAC,CACvDP,IAAI,CAAC,UAACC,MAAM,EAAK;IAChB,OAAOA,MAAM,CAACC,IAAI;EACpB,CAAC,CAAC;AACN,CAAC;;AAGH;AACA,OAAO,IAAMY,YAAY,GAAG,SAAfA,YAAYA,CAAIP,MAAM,EAAK;EACpC,OAAOX,IAAI,CAACgB,IAAI,CAAC,iBAAiB,GAACf,EAAE,CAACW,SAAS,CAACD,MAAM,CAAC,CAAC,CACrDP,IAAI,CAAC,UAACC,MAAM,EAAK;IAChB,OAAOA,MAAM,CAACC,IAAI;EACpB,CAAC,CAAC;AACN,CAAC;;AAGH;AACA,OAAO,IAAMa,aAAa,GAAG,SAAhBA,aAAaA,CAAIR,MAAM,EAAK;EACrC,OAAOX,IAAI,CAACgB,IAAI,CAAC,kBAAkB,GAACf,EAAE,CAACW,SAAS,CAACD,MAAM,CAAC,CAAC,CACtDP,IAAI,CAAC,UAACC,MAAM,EAAK;IAChB,OAAOA,MAAM,CAACC,IAAI;EACpB,CAAC,CAAC;AACN,CAAC;;AAEH;AACA,OAAO,IAAMc,aAAa,GAAG,SAAhBA,aAAaA,CAAIT,MAAM,EAAK;EACrC,OAAOX,IAAI,CAACgB,IAAI,CAAC,qBAAqB,EAACf,EAAE,CAACW,SAAS,CAACD,MAAM,CAAC,CAAC,CACzDP,IAAI,CAAC,UAACC,MAAM,EAAK;IAChB,OAAOA,MAAM,CAACC,IAAI;EACpB,CAAC,CAAC;AACN,CAAC;;AAEH;AACA,OAAO,IAAMe,cAAc,GAAG,SAAjBA,cAAcA,CAAIV,MAAM,EAAK;EACtC,OAAOX,IAAI,CAACgB,IAAI,CAAC,oBAAoB,EAACf,EAAE,CAACW,SAAS,CAACD,MAAM,CAAC,CAAC,CACxDP,IAAI,CAAC,UAACC,MAAM,EAAK;IAChB,OAAOA,MAAM,CAACC,IAAI;EACpB,CAAC,CAAC;AACN,CAAC;;AAGH;AACA,OAAO,IAAMgB,SAAS,GAAG,SAAZA,SAASA,CAAIX,MAAM,EAAK;EACjC,OAAOX,IAAI,CAACgB,IAAI,CAAC,6CAA6C,EAACL,MAAM,EAAE;IACrE;IACAY,OAAO,EAAE;MACP;MACA,cAAc,EAAE;IAClB;EACF,CAAC,CAAC,CACCnB,IAAI,CAAC,UAACC,MAAM,EAAK;IAChB,OAAOA,MAAM,CAACC,IAAI;EACpB,CAAC,CAAC;AACN,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}