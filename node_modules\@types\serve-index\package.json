{"name": "@types/serve-index", "version": "1.9.1", "description": "TypeScript definitions for serve-index", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/serve-index", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/tkrotoff", "githubUsername": "t<PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/serve-index"}, "scripts": {}, "dependencies": {"@types/express": "*"}, "typesPublisherContentHash": "14036a52b61d5326a23e498434889cb337bebd08a9e1ae3f609c099255155afd", "typeScriptVersion": "3.6"}