{"version": 3, "file": "index.mjs", "sources": ["../src/built-in-definitions.ts", "../src/add-platform-specific-polyfills.ts", "../src/helpers.ts", "../src/index.ts"], "sourcesContent": ["import corejs2Polyfills from \"@babel/compat-data/corejs2-built-ins\";\n\ntype ObjectMap<V> = { [name: string]: V };\n\ntype PolyfillDescriptor<T> = {\n  name: string;\n  pure: string | null;\n  global: string[];\n  meta: T | null;\n};\n\ntype CoreJS2Meta = {\n  minRuntimeVersion: string | null;\n};\n\nconst define = <T>(\n  name: string,\n  pure?: string | null,\n  global: string[] = [],\n  meta?: T | null,\n): PolyfillDescriptor<T> => {\n  return { name, pure, global, meta };\n};\n\nconst pureAndGlobal = (\n  pure: string,\n  global: string[],\n  minRuntimeVersion: string | null = null,\n) => define<CoreJS2Meta>(global[0], pure, global, { minRuntimeVersion });\n\nconst globalOnly = (global: string[]) =>\n  define<CoreJS2Meta>(global[0], null, global);\n\nconst pureOnly = (pure: string, name: string) =>\n  define<CoreJS2Meta>(name, pure, []);\n\nconst ArrayNatureIterators = [\n  \"es6.object.to-string\",\n  \"es6.array.iterator\",\n  \"web.dom.iterable\",\n];\n\nexport const CommonIterators = [\"es6.string.iterator\", ...ArrayNatureIterators];\n\nconst PromiseDependencies = [\"es6.object.to-string\", \"es6.promise\"];\n\nexport const BuiltIns: ObjectMap<PolyfillDescriptor<CoreJS2Meta>> = {\n  DataView: globalOnly([\"es6.typed.data-view\"]),\n  Float32Array: globalOnly([\"es6.typed.float32-array\"]),\n  Float64Array: globalOnly([\"es6.typed.float64-array\"]),\n  Int8Array: globalOnly([\"es6.typed.int8-array\"]),\n  Int16Array: globalOnly([\"es6.typed.int16-array\"]),\n  Int32Array: globalOnly([\"es6.typed.int32-array\"]),\n  Map: pureAndGlobal(\"map\", [\"es6.map\", ...CommonIterators]),\n  Number: globalOnly([\"es6.number.constructor\"]),\n  Promise: pureAndGlobal(\"promise\", PromiseDependencies),\n  RegExp: globalOnly([\"es6.regexp.constructor\"]),\n  Set: pureAndGlobal(\"set\", [\"es6.set\", ...CommonIterators]),\n  Symbol: pureAndGlobal(\"symbol/index\", [\"es6.symbol\"]),\n  Uint8Array: globalOnly([\"es6.typed.uint8-array\"]),\n  Uint8ClampedArray: globalOnly([\"es6.typed.uint8-clamped-array\"]),\n  Uint16Array: globalOnly([\"es6.typed.uint16-array\"]),\n  Uint32Array: globalOnly([\"es6.typed.uint32-array\"]),\n  WeakMap: pureAndGlobal(\"weak-map\", [\"es6.weak-map\", ...CommonIterators]),\n  WeakSet: pureAndGlobal(\"weak-set\", [\"es6.weak-set\", ...CommonIterators]),\n\n  setImmediate: pureOnly(\"set-immediate\", \"web.immediate\"),\n  clearImmediate: pureOnly(\"clear-immediate\", \"web.immediate\"),\n  parseFloat: pureOnly(\"parse-float\", \"es6.parse-float\"),\n  parseInt: pureOnly(\"parse-int\", \"es6.parse-int\"),\n};\n\nexport const InstanceProperties: ObjectMap<PolyfillDescriptor<CoreJS2Meta>> = {\n  __defineGetter__: globalOnly([\"es7.object.define-getter\"]),\n  __defineSetter__: globalOnly([\"es7.object.define-setter\"]),\n  __lookupGetter__: globalOnly([\"es7.object.lookup-getter\"]),\n  __lookupSetter__: globalOnly([\"es7.object.lookup-setter\"]),\n  anchor: globalOnly([\"es6.string.anchor\"]),\n  big: globalOnly([\"es6.string.big\"]),\n  bind: globalOnly([\"es6.function.bind\"]),\n  blink: globalOnly([\"es6.string.blink\"]),\n  bold: globalOnly([\"es6.string.bold\"]),\n  codePointAt: globalOnly([\"es6.string.code-point-at\"]),\n  copyWithin: globalOnly([\"es6.array.copy-within\"]),\n  endsWith: globalOnly([\"es6.string.ends-with\"]),\n  entries: globalOnly(ArrayNatureIterators),\n  every: globalOnly([\"es6.array.every\"]),\n  fill: globalOnly([\"es6.array.fill\"]),\n  filter: globalOnly([\"es6.array.filter\"]),\n  finally: globalOnly([\"es7.promise.finally\", ...PromiseDependencies]),\n  find: globalOnly([\"es6.array.find\"]),\n  findIndex: globalOnly([\"es6.array.find-index\"]),\n  fixed: globalOnly([\"es6.string.fixed\"]),\n  flags: globalOnly([\"es6.regexp.flags\"]),\n  flatMap: globalOnly([\"es7.array.flat-map\"]),\n  fontcolor: globalOnly([\"es6.string.fontcolor\"]),\n  fontsize: globalOnly([\"es6.string.fontsize\"]),\n  forEach: globalOnly([\"es6.array.for-each\"]),\n  includes: globalOnly([\"es6.string.includes\", \"es7.array.includes\"]),\n  indexOf: globalOnly([\"es6.array.index-of\"]),\n  italics: globalOnly([\"es6.string.italics\"]),\n  keys: globalOnly(ArrayNatureIterators),\n  lastIndexOf: globalOnly([\"es6.array.last-index-of\"]),\n  link: globalOnly([\"es6.string.link\"]),\n  map: globalOnly([\"es6.array.map\"]),\n  match: globalOnly([\"es6.regexp.match\"]),\n  name: globalOnly([\"es6.function.name\"]),\n  padStart: globalOnly([\"es7.string.pad-start\"]),\n  padEnd: globalOnly([\"es7.string.pad-end\"]),\n  reduce: globalOnly([\"es6.array.reduce\"]),\n  reduceRight: globalOnly([\"es6.array.reduce-right\"]),\n  repeat: globalOnly([\"es6.string.repeat\"]),\n  replace: globalOnly([\"es6.regexp.replace\"]),\n  search: globalOnly([\"es6.regexp.search\"]),\n  small: globalOnly([\"es6.string.small\"]),\n  some: globalOnly([\"es6.array.some\"]),\n  sort: globalOnly([\"es6.array.sort\"]),\n  split: globalOnly([\"es6.regexp.split\"]),\n  startsWith: globalOnly([\"es6.string.starts-with\"]),\n  strike: globalOnly([\"es6.string.strike\"]),\n  sub: globalOnly([\"es6.string.sub\"]),\n  sup: globalOnly([\"es6.string.sup\"]),\n  toISOString: globalOnly([\"es6.date.to-iso-string\"]),\n  toJSON: globalOnly([\"es6.date.to-json\"]),\n  toString: globalOnly([\n    \"es6.object.to-string\",\n    \"es6.date.to-string\",\n    \"es6.regexp.to-string\",\n  ]),\n  trim: globalOnly([\"es6.string.trim\"]),\n  trimEnd: globalOnly([\"es7.string.trim-right\"]),\n  trimLeft: globalOnly([\"es7.string.trim-left\"]),\n  trimRight: globalOnly([\"es7.string.trim-right\"]),\n  trimStart: globalOnly([\"es7.string.trim-left\"]),\n  values: globalOnly(ArrayNatureIterators),\n};\n\n// This isn't present in older @babel/compat-data versions\nif (\"es6.array.slice\" in corejs2Polyfills) {\n  InstanceProperties.slice = globalOnly([\"es6.array.slice\"]);\n}\n\nexport const StaticProperties: ObjectMap<\n  ObjectMap<PolyfillDescriptor<CoreJS2Meta>>\n> = {\n  Array: {\n    from: pureAndGlobal(\"array/from\", [\n      \"es6.symbol\",\n      \"es6.array.from\",\n      ...CommonIterators,\n    ]),\n    isArray: pureAndGlobal(\"array/is-array\", [\"es6.array.is-array\"]),\n    of: pureAndGlobal(\"array/of\", [\"es6.array.of\"]),\n  },\n\n  Date: {\n    now: pureAndGlobal(\"date/now\", [\"es6.date.now\"]),\n  },\n\n  JSON: {\n    stringify: pureOnly(\"json/stringify\", \"es6.symbol\"),\n  },\n\n  Math: {\n    // 'Math' was not included in the 7.0.0\n    // release of '@babel/runtime'. See issue https://github.com/babel/babel/pull/8616.\n    acosh: pureAndGlobal(\"math/acosh\", [\"es6.math.acosh\"], \"7.0.1\"),\n    asinh: pureAndGlobal(\"math/asinh\", [\"es6.math.asinh\"], \"7.0.1\"),\n    atanh: pureAndGlobal(\"math/atanh\", [\"es6.math.atanh\"], \"7.0.1\"),\n    cbrt: pureAndGlobal(\"math/cbrt\", [\"es6.math.cbrt\"], \"7.0.1\"),\n    clz32: pureAndGlobal(\"math/clz32\", [\"es6.math.clz32\"], \"7.0.1\"),\n    cosh: pureAndGlobal(\"math/cosh\", [\"es6.math.cosh\"], \"7.0.1\"),\n    expm1: pureAndGlobal(\"math/expm1\", [\"es6.math.expm1\"], \"7.0.1\"),\n    fround: pureAndGlobal(\"math/fround\", [\"es6.math.fround\"], \"7.0.1\"),\n    hypot: pureAndGlobal(\"math/hypot\", [\"es6.math.hypot\"], \"7.0.1\"),\n    imul: pureAndGlobal(\"math/imul\", [\"es6.math.imul\"], \"7.0.1\"),\n    log1p: pureAndGlobal(\"math/log1p\", [\"es6.math.log1p\"], \"7.0.1\"),\n    log10: pureAndGlobal(\"math/log10\", [\"es6.math.log10\"], \"7.0.1\"),\n    log2: pureAndGlobal(\"math/log2\", [\"es6.math.log2\"], \"7.0.1\"),\n    sign: pureAndGlobal(\"math/sign\", [\"es6.math.sign\"], \"7.0.1\"),\n    sinh: pureAndGlobal(\"math/sinh\", [\"es6.math.sinh\"], \"7.0.1\"),\n    tanh: pureAndGlobal(\"math/tanh\", [\"es6.math.tanh\"], \"7.0.1\"),\n    trunc: pureAndGlobal(\"math/trunc\", [\"es6.math.trunc\"], \"7.0.1\"),\n  },\n\n  Number: {\n    EPSILON: pureAndGlobal(\"number/epsilon\", [\"es6.number.epsilon\"]),\n    MIN_SAFE_INTEGER: pureAndGlobal(\"number/min-safe-integer\", [\n      \"es6.number.min-safe-integer\",\n    ]),\n    MAX_SAFE_INTEGER: pureAndGlobal(\"number/max-safe-integer\", [\n      \"es6.number.max-safe-integer\",\n    ]),\n    isFinite: pureAndGlobal(\"number/is-finite\", [\"es6.number.is-finite\"]),\n    isInteger: pureAndGlobal(\"number/is-integer\", [\"es6.number.is-integer\"]),\n    isSafeInteger: pureAndGlobal(\"number/is-safe-integer\", [\n      \"es6.number.is-safe-integer\",\n    ]),\n    isNaN: pureAndGlobal(\"number/is-nan\", [\"es6.number.is-nan\"]),\n    parseFloat: pureAndGlobal(\"number/parse-float\", [\"es6.number.parse-float\"]),\n    parseInt: pureAndGlobal(\"number/parse-int\", [\"es6.number.parse-int\"]),\n  },\n\n  Object: {\n    assign: pureAndGlobal(\"object/assign\", [\"es6.object.assign\"]),\n    create: pureAndGlobal(\"object/create\", [\"es6.object.create\"]),\n    defineProperties: pureAndGlobal(\"object/define-properties\", [\n      \"es6.object.define-properties\",\n    ]),\n    defineProperty: pureAndGlobal(\"object/define-property\", [\n      \"es6.object.define-property\",\n    ]),\n    entries: pureAndGlobal(\"object/entries\", [\"es7.object.entries\"]),\n    freeze: pureAndGlobal(\"object/freeze\", [\"es6.object.freeze\"]),\n    getOwnPropertyDescriptor: pureAndGlobal(\n      \"object/get-own-property-descriptor\",\n      [\"es6.object.get-own-property-descriptor\"],\n    ),\n    getOwnPropertyDescriptors: pureAndGlobal(\n      \"object/get-own-property-descriptors\",\n      [\"es7.object.get-own-property-descriptors\"],\n    ),\n    getOwnPropertyNames: pureAndGlobal(\"object/get-own-property-names\", [\n      \"es6.object.get-own-property-names\",\n    ]),\n    getOwnPropertySymbols: pureAndGlobal(\"object/get-own-property-symbols\", [\n      \"es6.symbol\",\n    ]),\n    getPrototypeOf: pureAndGlobal(\"object/get-prototype-of\", [\n      \"es6.object.get-prototype-of\",\n    ]),\n    is: pureAndGlobal(\"object/is\", [\"es6.object.is\"]),\n    isExtensible: pureAndGlobal(\"object/is-extensible\", [\n      \"es6.object.is-extensible\",\n    ]),\n    isFrozen: pureAndGlobal(\"object/is-frozen\", [\"es6.object.is-frozen\"]),\n    isSealed: pureAndGlobal(\"object/is-sealed\", [\"es6.object.is-sealed\"]),\n    keys: pureAndGlobal(\"object/keys\", [\"es6.object.keys\"]),\n    preventExtensions: pureAndGlobal(\"object/prevent-extensions\", [\n      \"es6.object.prevent-extensions\",\n    ]),\n    seal: pureAndGlobal(\"object/seal\", [\"es6.object.seal\"]),\n    setPrototypeOf: pureAndGlobal(\"object/set-prototype-of\", [\n      \"es6.object.set-prototype-of\",\n    ]),\n    values: pureAndGlobal(\"object/values\", [\"es7.object.values\"]),\n  },\n\n  Promise: {\n    all: globalOnly(CommonIterators),\n    race: globalOnly(CommonIterators),\n  },\n\n  Reflect: {\n    apply: pureAndGlobal(\"reflect/apply\", [\"es6.reflect.apply\"]),\n    construct: pureAndGlobal(\"reflect/construct\", [\"es6.reflect.construct\"]),\n    defineProperty: pureAndGlobal(\"reflect/define-property\", [\n      \"es6.reflect.define-property\",\n    ]),\n    deleteProperty: pureAndGlobal(\"reflect/delete-property\", [\n      \"es6.reflect.delete-property\",\n    ]),\n    get: pureAndGlobal(\"reflect/get\", [\"es6.reflect.get\"]),\n    getOwnPropertyDescriptor: pureAndGlobal(\n      \"reflect/get-own-property-descriptor\",\n      [\"es6.reflect.get-own-property-descriptor\"],\n    ),\n    getPrototypeOf: pureAndGlobal(\"reflect/get-prototype-of\", [\n      \"es6.reflect.get-prototype-of\",\n    ]),\n    has: pureAndGlobal(\"reflect/has\", [\"es6.reflect.has\"]),\n    isExtensible: pureAndGlobal(\"reflect/is-extensible\", [\n      \"es6.reflect.is-extensible\",\n    ]),\n    ownKeys: pureAndGlobal(\"reflect/own-keys\", [\"es6.reflect.own-keys\"]),\n    preventExtensions: pureAndGlobal(\"reflect/prevent-extensions\", [\n      \"es6.reflect.prevent-extensions\",\n    ]),\n    set: pureAndGlobal(\"reflect/set\", [\"es6.reflect.set\"]),\n    setPrototypeOf: pureAndGlobal(\"reflect/set-prototype-of\", [\n      \"es6.reflect.set-prototype-of\",\n    ]),\n  },\n\n  String: {\n    at: pureOnly(\"string/at\", \"es7.string.at\"),\n    fromCodePoint: pureAndGlobal(\"string/from-code-point\", [\n      \"es6.string.from-code-point\",\n    ]),\n    raw: pureAndGlobal(\"string/raw\", [\"es6.string.raw\"]),\n  },\n\n  Symbol: {\n    // FIXME: Pure disabled to work around zloirock/core-js#262.\n    asyncIterator: globalOnly([\"es6.symbol\", \"es7.symbol.async-iterator\"]),\n    for: pureOnly(\"symbol/for\", \"es6.symbol\"),\n    hasInstance: pureOnly(\"symbol/has-instance\", \"es6.symbol\"),\n    isConcatSpreadable: pureOnly(\"symbol/is-concat-spreadable\", \"es6.symbol\"),\n    iterator: define(\"es6.symbol\", \"symbol/iterator\", CommonIterators),\n    keyFor: pureOnly(\"symbol/key-for\", \"es6.symbol\"),\n    match: pureAndGlobal(\"symbol/match\", [\"es6.regexp.match\"]),\n    replace: pureOnly(\"symbol/replace\", \"es6.symbol\"),\n    search: pureOnly(\"symbol/search\", \"es6.symbol\"),\n    species: pureOnly(\"symbol/species\", \"es6.symbol\"),\n    split: pureOnly(\"symbol/split\", \"es6.symbol\"),\n    toPrimitive: pureOnly(\"symbol/to-primitive\", \"es6.symbol\"),\n    toStringTag: pureOnly(\"symbol/to-string-tag\", \"es6.symbol\"),\n    unscopables: pureOnly(\"symbol/unscopables\", \"es6.symbol\"),\n  },\n};\n", "import type { Targets } from \"@babel/helper-define-polyfill-provider\";\n\nconst webPolyfills = {\n  \"web.timers\": {},\n  \"web.immediate\": {},\n  \"web.dom.iterable\": {},\n};\n\nconst purePolyfills = {\n  \"es6.parse-float\": {},\n  \"es6.parse-int\": {},\n  \"es7.string.at\": {},\n};\n\nexport default function (targets: Targets, method: string, polyfills: any) {\n  const targetNames = Object.keys(targets);\n  const isAnyTarget = !targetNames.length;\n  const isWebTarget = targetNames.some(name => name !== \"node\");\n\n  return {\n    ...polyfills,\n    ...(method === \"usage-pure\" ? purePolyfills : null),\n    ...(isAnyTarget || isWebTarget ? webPolyfills : null),\n  };\n}\n", "import semver from \"semver\";\n\nexport function hasMinVersion(\n  minVersion?: string | null,\n  runtimeVersion?: string | number | null,\n) {\n  // If the range is unavailable, we're running the script during Babel's\n  // build process, and we want to assume that all versions are satisfied so\n  // that the built output will include all definitions.\n  if (!runtimeVersion || !minVersion) return true;\n\n  // semver.intersects() has some surprising behavior with comparing ranges\n  // with preprelease versions. We add '^' to ensure that we are always\n  // comparing ranges with ranges, which sidesteps this logic.\n  // For example:\n  //\n  //   semver.intersects(`<7.0.1`, \"7.0.0-beta.0\") // false - surprising\n  //   semver.intersects(`<7.0.1`, \"^7.0.0-beta.0\") // true - expected\n  //\n  // This is because the first falls back to\n  //\n  //   semver.satisfies(\"7.0.0-beta.0\", `<7.0.1`) // false - surprising\n  //\n  // and this fails because a prerelease version can only satisfy a range\n  // if it is a prerelease within the same major/minor/patch range.\n  //\n  // Note: If this is found to have issues, please also revist the logic in\n  // babel-core's availableHelper() API.\n  if (semver.valid(runtimeVersion)) runtimeVersion = `^${runtimeVersion}`;\n\n  return (\n    !semver.intersects(`<${minVersion}`, runtimeVersion) &&\n    !semver.intersects(`>=8.0.0`, runtimeVersion)\n  );\n}\n", "import corejs2Polyfills from \"@babel/compat-data/corejs2-built-ins\";\nimport {\n  BuiltIns,\n  StaticProperties,\n  InstanceProperties,\n  CommonIterators,\n} from \"./built-in-definitions\";\nimport addPlatformSpecificPolyfills from \"./add-platform-specific-polyfills\";\nimport { hasMinVersion } from \"./helpers\";\n\nimport defineProvider from \"@babel/helper-define-polyfill-provider\";\nimport type { NodePath } from \"@babel/traverse\";\nimport { types as t } from \"@babel/core\";\n\nconst BABEL_RUNTIME = \"@babel/runtime-corejs2\";\n\nconst presetEnvCompat = \"#__secret_key__@babel/preset-env__compatibility\";\nconst runtimeCompat = \"#__secret_key__@babel/runtime__compatibility\";\n\nconst has = Function.call.bind(Object.hasOwnProperty);\n\ntype Options = {\n  [presetEnvCompat]?: {\n    entryInjectRegenerator: boolean;\n    noRuntimeName: boolean;\n  };\n  [runtimeCompat]?: {\n    useBabelRuntime: boolean;\n    runtimeVersion: string;\n    ext: string;\n  };\n};\n\nexport default defineProvider<Options>(function (\n  api,\n  {\n    [presetEnvCompat]: {\n      entryInjectRegenerator = false,\n      noRuntimeName = false,\n    } = {},\n    [runtimeCompat]: {\n      useBabelRuntime = false,\n      runtimeVersion = \"\",\n      ext = \".js\",\n    } = {},\n  },\n) {\n  const resolve = api.createMetaResolver({\n    global: BuiltIns,\n    static: StaticProperties,\n    instance: InstanceProperties,\n  });\n\n  const { debug, shouldInjectPolyfill, method } = api;\n\n  const polyfills = addPlatformSpecificPolyfills(\n    api.targets,\n    method,\n    corejs2Polyfills,\n  );\n\n  const coreJSBase = useBabelRuntime\n    ? `${BABEL_RUNTIME}/core-js`\n    : method === \"usage-pure\"\n    ? \"core-js/library/fn\"\n    : \"core-js/modules\";\n\n  function inject(name: string | string[], utils) {\n    if (typeof name === \"string\") {\n      // Some polyfills aren't always available, for example\n      // web.dom.iterable when targeting node\n      if (has(polyfills, name) && shouldInjectPolyfill(name)) {\n        debug(name);\n        utils.injectGlobalImport(`${coreJSBase}/${name}.js`);\n      }\n      return;\n    }\n\n    name.forEach(name => inject(name, utils));\n  }\n\n  function maybeInjectPure(desc, hint, utils) {\n    let { pure, meta, name } = desc;\n\n    if (!pure || !shouldInjectPolyfill(name)) return;\n\n    if (\n      runtimeVersion &&\n      meta &&\n      meta.minRuntimeVersion &&\n      !hasMinVersion(meta && meta.minRuntimeVersion, runtimeVersion)\n    ) {\n      return;\n    }\n\n    // Unfortunately core-js and @babel/runtime-corejs2 don't have the same\n    // directory structure, so we need to special case this.\n    if (useBabelRuntime && pure === \"symbol/index\") pure = \"symbol\";\n\n    return utils.injectDefaultImport(`${coreJSBase}/${pure}${ext}`, hint);\n  }\n\n  return {\n    name: \"corejs2\",\n\n    runtimeName: noRuntimeName ? null : BABEL_RUNTIME,\n\n    polyfills,\n\n    entryGlobal(meta, utils, path) {\n      if (meta.kind === \"import\" && meta.source === \"core-js\") {\n        debug(null);\n\n        inject(Object.keys(polyfills), utils);\n\n        if (entryInjectRegenerator) {\n          utils.injectGlobalImport(\"regenerator-runtime/runtime.js\");\n        }\n\n        path.remove();\n      }\n    },\n\n    usageGlobal(meta, utils) {\n      const resolved = resolve(meta);\n      if (!resolved) return;\n\n      let deps = resolved.desc.global;\n\n      if (\n        resolved.kind !== \"global\" &&\n        \"object\" in meta &&\n        meta.object &&\n        meta.placement === \"prototype\"\n      ) {\n        const low = meta.object.toLowerCase();\n        deps = deps.filter(m => m.includes(low));\n      }\n\n      inject(deps, utils);\n    },\n\n    usagePure(meta, utils, path) {\n      if (meta.kind === \"in\") {\n        if (meta.key === \"Symbol.iterator\") {\n          path.replaceWith(\n            t.callExpression(\n              utils.injectDefaultImport(\n                `${coreJSBase}/is-iterable${ext}`,\n                \"isIterable\",\n              ),\n              [(path.node as t.BinaryExpression).right], // meta.kind === \"in\" narrows this\n            ),\n          );\n        }\n\n        return;\n      }\n\n      if (path.parentPath.isUnaryExpression({ operator: \"delete\" })) return;\n\n      if (meta.kind === \"property\") {\n        // We can't compile destructuring.\n        if (!path.isMemberExpression()) return;\n        if (!path.isReferenced()) return;\n\n        if (\n          meta.key === \"Symbol.iterator\" &&\n          shouldInjectPolyfill(\"es6.symbol\") &&\n          path.parentPath.isCallExpression({ callee: path.node }) &&\n          path.parentPath.node.arguments.length === 0\n        ) {\n          path.parentPath.replaceWith(\n            t.callExpression(\n              utils.injectDefaultImport(\n                `${coreJSBase}/get-iterator${ext}`,\n                \"getIterator\",\n              ),\n              [path.node.object],\n            ),\n          );\n          path.skip();\n\n          return;\n        }\n      }\n\n      const resolved = resolve(meta);\n      if (!resolved) return;\n\n      const id = maybeInjectPure(resolved.desc, resolved.name, utils);\n      if (id) path.replaceWith(id);\n    },\n\n    visitor: method === \"usage-global\" && {\n      // yield*\n      YieldExpression(path: NodePath<t.YieldExpression>) {\n        if (path.node.delegate) {\n          inject(\"web.dom.iterable\", api.getUtils(path));\n        }\n      },\n\n      // for-of, [a, b] = c\n      \"ForOfStatement|ArrayPattern\"(\n        path: NodePath<t.ForOfStatement | t.ArrayPattern>,\n      ) {\n        CommonIterators.forEach(name => inject(name, api.getUtils(path)));\n      },\n    },\n  };\n});\n"], "names": ["define", "name", "pure", "global", "meta", "pureAndGlobal", "minRuntimeVersion", "globalOnly", "pureOnly", "ArrayNatureIterators", "CommonIterators", "PromiseDependencies", "BuiltIns", "DataView", "Float32Array", "Float64Array", "Int8Array", "Int16Array", "Int32Array", "Map", "Number", "Promise", "RegExp", "Set", "Symbol", "Uint8Array", "Uint8ClampedArray", "Uint16Array", "Uint32Array", "WeakMap", "WeakSet", "setImmediate", "clearImmediate", "parseFloat", "parseInt", "InstanceProperties", "__defineGetter__", "__defineSetter__", "__lookupGetter__", "__lookupSetter__", "anchor", "big", "bind", "blink", "bold", "codePointAt", "copyWithin", "endsWith", "entries", "every", "fill", "filter", "finally", "find", "findIndex", "fixed", "flags", "flatMap", "fontcolor", "fontsize", "for<PERSON>ach", "includes", "indexOf", "italics", "keys", "lastIndexOf", "link", "map", "match", "padStart", "padEnd", "reduce", "reduceRight", "repeat", "replace", "search", "small", "some", "sort", "split", "startsWith", "strike", "sub", "sup", "toISOString", "toJSON", "toString", "trim", "trimEnd", "trimLeft", "trimRight", "trimStart", "values", "corejs2Polyfills", "slice", "StaticProperties", "Array", "from", "isArray", "of", "Date", "now", "JSON", "stringify", "Math", "acosh", "asinh", "atanh", "cbrt", "clz32", "cosh", "expm1", "fround", "hypot", "imul", "log1p", "log10", "log2", "sign", "sinh", "tanh", "trunc", "EPSILON", "MIN_SAFE_INTEGER", "MAX_SAFE_INTEGER", "isFinite", "isInteger", "isSafeInteger", "isNaN", "Object", "assign", "create", "defineProperties", "defineProperty", "freeze", "getOwnPropertyDescriptor", "getOwnPropertyDescriptors", "getOwnPropertyNames", "getOwnPropertySymbols", "getPrototypeOf", "is", "isExtensible", "isFrozen", "isSealed", "preventExtensions", "seal", "setPrototypeOf", "all", "race", "Reflect", "apply", "construct", "deleteProperty", "get", "has", "ownKeys", "set", "String", "at", "fromCodePoint", "raw", "asyncIterator", "for", "hasInstance", "isConcatSpreadable", "iterator", "keyFor", "species", "toPrimitive", "toStringTag", "unscopables", "webPolyfills", "purePolyfills", "targets", "method", "polyfills", "targetNames", "isAnyTarget", "length", "isWebTarget", "hasMinVersion", "minVersion", "runtimeVersion", "semver", "valid", "intersects", "types", "t", "BABEL_RUNTIME", "presetEnvCompat", "runtimeCompat", "Function", "call", "hasOwnProperty", "define<PERSON>rovider", "api", "entryInjectRegenerator", "noRuntimeName", "useBabelRuntime", "ext", "resolve", "createMetaResolver", "static", "instance", "debug", "shouldInjectPolyfill", "addPlatformSpecificPolyfills", "coreJSBase", "inject", "utils", "injectGlobalImport", "maybeInjectPure", "desc", "hint", "injectDefaultImport", "runtimeName", "entryGlobal", "path", "kind", "source", "remove", "usageGlobal", "resolved", "deps", "object", "placement", "low", "toLowerCase", "m", "usagePure", "key", "replaceWith", "callExpression", "node", "right", "parentPath", "isUnaryExpression", "operator", "isMemberExpression", "isReferenced", "isCallExpression", "callee", "arguments", "skip", "id", "visitor", "YieldExpression", "delegate", "getUtils"], "mappings": ";;;;;AAeA,MAAMA,MAAM,GAAG,CACbC,IADa,EAEbC,IAFa,EAGbC,MAAgB,GAAG,EAHN,EAIbC,IAJa,KAKa;EAC1B,OAAO;IAAEH,IAAF;IAAQC,IAAR;IAAcC,MAAd;IAAsBC;GAA7B;AACD,CAPD;;AASA,MAAMC,aAAa,GAAG,CACpBH,IADoB,EAEpBC,MAFoB,EAGpBG,iBAAgC,GAAG,IAHf,KAIjBN,MAAM,CAAcG,MAAM,CAAC,CAAD,CAApB,EAAyBD,IAAzB,EAA+BC,MAA/B,EAAuC;EAAEG;AAAF,CAAvC,CAJX;;AAMA,MAAMC,UAAU,GAAIJ,MAAD,IACjBH,MAAM,CAAcG,MAAM,CAAC,CAAD,CAApB,EAAyB,IAAzB,EAA+BA,MAA/B,CADR;;AAGA,MAAMK,QAAQ,GAAG,CAACN,IAAD,EAAeD,IAAf,KACfD,MAAM,CAAcC,IAAd,EAAoBC,IAApB,EAA0B,EAA1B,CADR;;AAGA,MAAMO,oBAAoB,GAAG,CAC3B,sBAD2B,EAE3B,oBAF2B,EAG3B,kBAH2B,CAA7B;AAMO,MAAMC,eAAe,GAAG,CAAC,qBAAD,EAAwB,GAAGD,oBAA3B,CAAxB;AAEP,MAAME,mBAAmB,GAAG,CAAC,sBAAD,EAAyB,aAAzB,CAA5B;AAEO,MAAMC,QAAoD,GAAG;EAClEC,QAAQ,EAAEN,UAAU,CAAC,CAAC,qBAAD,CAAD,CAD8C;EAElEO,YAAY,EAAEP,UAAU,CAAC,CAAC,yBAAD,CAAD,CAF0C;EAGlEQ,YAAY,EAAER,UAAU,CAAC,CAAC,yBAAD,CAAD,CAH0C;EAIlES,SAAS,EAAET,UAAU,CAAC,CAAC,sBAAD,CAAD,CAJ6C;EAKlEU,UAAU,EAAEV,UAAU,CAAC,CAAC,uBAAD,CAAD,CAL4C;EAMlEW,UAAU,EAAEX,UAAU,CAAC,CAAC,uBAAD,CAAD,CAN4C;EAOlEY,GAAG,EAAEd,aAAa,CAAC,KAAD,EAAQ,CAAC,SAAD,EAAY,GAAGK,eAAf,CAAR,CAPgD;EAQlEU,MAAM,EAAEb,UAAU,CAAC,CAAC,wBAAD,CAAD,CARgD;EASlEc,OAAO,EAAEhB,aAAa,CAAC,SAAD,EAAYM,mBAAZ,CAT4C;EAUlEW,MAAM,EAAEf,UAAU,CAAC,CAAC,wBAAD,CAAD,CAVgD;EAWlEgB,GAAG,EAAElB,aAAa,CAAC,KAAD,EAAQ,CAAC,SAAD,EAAY,GAAGK,eAAf,CAAR,CAXgD;EAYlEc,MAAM,EAAEnB,aAAa,CAAC,cAAD,EAAiB,CAAC,YAAD,CAAjB,CAZ6C;EAalEoB,UAAU,EAAElB,UAAU,CAAC,CAAC,uBAAD,CAAD,CAb4C;EAclEmB,iBAAiB,EAAEnB,UAAU,CAAC,CAAC,+BAAD,CAAD,CAdqC;EAelEoB,WAAW,EAAEpB,UAAU,CAAC,CAAC,wBAAD,CAAD,CAf2C;EAgBlEqB,WAAW,EAAErB,UAAU,CAAC,CAAC,wBAAD,CAAD,CAhB2C;EAiBlEsB,OAAO,EAAExB,aAAa,CAAC,UAAD,EAAa,CAAC,cAAD,EAAiB,GAAGK,eAApB,CAAb,CAjB4C;EAkBlEoB,OAAO,EAAEzB,aAAa,CAAC,UAAD,EAAa,CAAC,cAAD,EAAiB,GAAGK,eAApB,CAAb,CAlB4C;EAoBlEqB,YAAY,EAAEvB,QAAQ,CAAC,eAAD,EAAkB,eAAlB,CApB4C;EAqBlEwB,cAAc,EAAExB,QAAQ,CAAC,iBAAD,EAAoB,eAApB,CArB0C;EAsBlEyB,UAAU,EAAEzB,QAAQ,CAAC,aAAD,EAAgB,iBAAhB,CAtB8C;EAuBlE0B,QAAQ,EAAE1B,QAAQ,CAAC,WAAD,EAAc,eAAd;AAvBgD,CAA7D;AA0BA,MAAM2B,kBAA8D,GAAG;EAC5EC,gBAAgB,EAAE7B,UAAU,CAAC,CAAC,0BAAD,CAAD,CADgD;EAE5E8B,gBAAgB,EAAE9B,UAAU,CAAC,CAAC,0BAAD,CAAD,CAFgD;EAG5E+B,gBAAgB,EAAE/B,UAAU,CAAC,CAAC,0BAAD,CAAD,CAHgD;EAI5EgC,gBAAgB,EAAEhC,UAAU,CAAC,CAAC,0BAAD,CAAD,CAJgD;EAK5EiC,MAAM,EAAEjC,UAAU,CAAC,CAAC,mBAAD,CAAD,CAL0D;EAM5EkC,GAAG,EAAElC,UAAU,CAAC,CAAC,gBAAD,CAAD,CAN6D;EAO5EmC,IAAI,EAAEnC,UAAU,CAAC,CAAC,mBAAD,CAAD,CAP4D;EAQ5EoC,KAAK,EAAEpC,UAAU,CAAC,CAAC,kBAAD,CAAD,CAR2D;EAS5EqC,IAAI,EAAErC,UAAU,CAAC,CAAC,iBAAD,CAAD,CAT4D;EAU5EsC,WAAW,EAAEtC,UAAU,CAAC,CAAC,0BAAD,CAAD,CAVqD;EAW5EuC,UAAU,EAAEvC,UAAU,CAAC,CAAC,uBAAD,CAAD,CAXsD;EAY5EwC,QAAQ,EAAExC,UAAU,CAAC,CAAC,sBAAD,CAAD,CAZwD;EAa5EyC,OAAO,EAAEzC,UAAU,CAACE,oBAAD,CAbyD;EAc5EwC,KAAK,EAAE1C,UAAU,CAAC,CAAC,iBAAD,CAAD,CAd2D;EAe5E2C,IAAI,EAAE3C,UAAU,CAAC,CAAC,gBAAD,CAAD,CAf4D;EAgB5E4C,MAAM,EAAE5C,UAAU,CAAC,CAAC,kBAAD,CAAD,CAhB0D;EAiB5E6C,OAAO,EAAE7C,UAAU,CAAC,CAAC,qBAAD,EAAwB,GAAGI,mBAA3B,CAAD,CAjByD;EAkB5E0C,IAAI,EAAE9C,UAAU,CAAC,CAAC,gBAAD,CAAD,CAlB4D;EAmB5E+C,SAAS,EAAE/C,UAAU,CAAC,CAAC,sBAAD,CAAD,CAnBuD;EAoB5EgD,KAAK,EAAEhD,UAAU,CAAC,CAAC,kBAAD,CAAD,CApB2D;EAqB5EiD,KAAK,EAAEjD,UAAU,CAAC,CAAC,kBAAD,CAAD,CArB2D;EAsB5EkD,OAAO,EAAElD,UAAU,CAAC,CAAC,oBAAD,CAAD,CAtByD;EAuB5EmD,SAAS,EAAEnD,UAAU,CAAC,CAAC,sBAAD,CAAD,CAvBuD;EAwB5EoD,QAAQ,EAAEpD,UAAU,CAAC,CAAC,qBAAD,CAAD,CAxBwD;EAyB5EqD,OAAO,EAAErD,UAAU,CAAC,CAAC,oBAAD,CAAD,CAzByD;EA0B5EsD,QAAQ,EAAEtD,UAAU,CAAC,CAAC,qBAAD,EAAwB,oBAAxB,CAAD,CA1BwD;EA2B5EuD,OAAO,EAAEvD,UAAU,CAAC,CAAC,oBAAD,CAAD,CA3ByD;EA4B5EwD,OAAO,EAAExD,UAAU,CAAC,CAAC,oBAAD,CAAD,CA5ByD;EA6B5EyD,IAAI,EAAEzD,UAAU,CAACE,oBAAD,CA7B4D;EA8B5EwD,WAAW,EAAE1D,UAAU,CAAC,CAAC,yBAAD,CAAD,CA9BqD;EA+B5E2D,IAAI,EAAE3D,UAAU,CAAC,CAAC,iBAAD,CAAD,CA/B4D;EAgC5E4D,GAAG,EAAE5D,UAAU,CAAC,CAAC,eAAD,CAAD,CAhC6D;EAiC5E6D,KAAK,EAAE7D,UAAU,CAAC,CAAC,kBAAD,CAAD,CAjC2D;EAkC5EN,IAAI,EAAEM,UAAU,CAAC,CAAC,mBAAD,CAAD,CAlC4D;EAmC5E8D,QAAQ,EAAE9D,UAAU,CAAC,CAAC,sBAAD,CAAD,CAnCwD;EAoC5E+D,MAAM,EAAE/D,UAAU,CAAC,CAAC,oBAAD,CAAD,CApC0D;EAqC5EgE,MAAM,EAAEhE,UAAU,CAAC,CAAC,kBAAD,CAAD,CArC0D;EAsC5EiE,WAAW,EAAEjE,UAAU,CAAC,CAAC,wBAAD,CAAD,CAtCqD;EAuC5EkE,MAAM,EAAElE,UAAU,CAAC,CAAC,mBAAD,CAAD,CAvC0D;EAwC5EmE,OAAO,EAAEnE,UAAU,CAAC,CAAC,oBAAD,CAAD,CAxCyD;EAyC5EoE,MAAM,EAAEpE,UAAU,CAAC,CAAC,mBAAD,CAAD,CAzC0D;EA0C5EqE,KAAK,EAAErE,UAAU,CAAC,CAAC,kBAAD,CAAD,CA1C2D;EA2C5EsE,IAAI,EAAEtE,UAAU,CAAC,CAAC,gBAAD,CAAD,CA3C4D;EA4C5EuE,IAAI,EAAEvE,UAAU,CAAC,CAAC,gBAAD,CAAD,CA5C4D;EA6C5EwE,KAAK,EAAExE,UAAU,CAAC,CAAC,kBAAD,CAAD,CA7C2D;EA8C5EyE,UAAU,EAAEzE,UAAU,CAAC,CAAC,wBAAD,CAAD,CA9CsD;EA+C5E0E,MAAM,EAAE1E,UAAU,CAAC,CAAC,mBAAD,CAAD,CA/C0D;EAgD5E2E,GAAG,EAAE3E,UAAU,CAAC,CAAC,gBAAD,CAAD,CAhD6D;EAiD5E4E,GAAG,EAAE5E,UAAU,CAAC,CAAC,gBAAD,CAAD,CAjD6D;EAkD5E6E,WAAW,EAAE7E,UAAU,CAAC,CAAC,wBAAD,CAAD,CAlDqD;EAmD5E8E,MAAM,EAAE9E,UAAU,CAAC,CAAC,kBAAD,CAAD,CAnD0D;EAoD5E+E,QAAQ,EAAE/E,UAAU,CAAC,CACnB,sBADmB,EAEnB,oBAFmB,EAGnB,sBAHmB,CAAD,CApDwD;EAyD5EgF,IAAI,EAAEhF,UAAU,CAAC,CAAC,iBAAD,CAAD,CAzD4D;EA0D5EiF,OAAO,EAAEjF,UAAU,CAAC,CAAC,uBAAD,CAAD,CA1DyD;EA2D5EkF,QAAQ,EAAElF,UAAU,CAAC,CAAC,sBAAD,CAAD,CA3DwD;EA4D5EmF,SAAS,EAAEnF,UAAU,CAAC,CAAC,uBAAD,CAAD,CA5DuD;EA6D5EoF,SAAS,EAAEpF,UAAU,CAAC,CAAC,sBAAD,CAAD,CA7DuD;EA8D5EqF,MAAM,EAAErF,UAAU,CAACE,oBAAD;AA9D0D,CAAvE;;AAkEP,IAAI,qBAAqBoF,gBAAzB,EAA2C;EACzC1D,kBAAkB,CAAC2D,KAAnB,GAA2BvF,UAAU,CAAC,CAAC,iBAAD,CAAD,CAArC;AACD;;AAEM,MAAMwF,gBAEZ,GAAG;EACFC,KAAK,EAAE;IACLC,IAAI,EAAE5F,aAAa,CAAC,YAAD,EAAe,CAChC,YADgC,EAEhC,gBAFgC,EAGhC,GAAGK,eAH6B,CAAf,CADd;IAMLwF,OAAO,EAAE7F,aAAa,CAAC,gBAAD,EAAmB,CAAC,oBAAD,CAAnB,CANjB;IAOL8F,EAAE,EAAE9F,aAAa,CAAC,UAAD,EAAa,CAAC,cAAD,CAAb;GARjB;EAWF+F,IAAI,EAAE;IACJC,GAAG,EAAEhG,aAAa,CAAC,UAAD,EAAa,CAAC,cAAD,CAAb;GAZlB;EAeFiG,IAAI,EAAE;IACJC,SAAS,EAAE/F,QAAQ,CAAC,gBAAD,EAAmB,YAAnB;GAhBnB;EAmBFgG,IAAI,EAAE;;;IAGJC,KAAK,EAAEpG,aAAa,CAAC,YAAD,EAAe,CAAC,gBAAD,CAAf,EAAmC,OAAnC,CAHhB;IAIJqG,KAAK,EAAErG,aAAa,CAAC,YAAD,EAAe,CAAC,gBAAD,CAAf,EAAmC,OAAnC,CAJhB;IAKJsG,KAAK,EAAEtG,aAAa,CAAC,YAAD,EAAe,CAAC,gBAAD,CAAf,EAAmC,OAAnC,CALhB;IAMJuG,IAAI,EAAEvG,aAAa,CAAC,WAAD,EAAc,CAAC,eAAD,CAAd,EAAiC,OAAjC,CANf;IAOJwG,KAAK,EAAExG,aAAa,CAAC,YAAD,EAAe,CAAC,gBAAD,CAAf,EAAmC,OAAnC,CAPhB;IAQJyG,IAAI,EAAEzG,aAAa,CAAC,WAAD,EAAc,CAAC,eAAD,CAAd,EAAiC,OAAjC,CARf;IASJ0G,KAAK,EAAE1G,aAAa,CAAC,YAAD,EAAe,CAAC,gBAAD,CAAf,EAAmC,OAAnC,CAThB;IAUJ2G,MAAM,EAAE3G,aAAa,CAAC,aAAD,EAAgB,CAAC,iBAAD,CAAhB,EAAqC,OAArC,CAVjB;IAWJ4G,KAAK,EAAE5G,aAAa,CAAC,YAAD,EAAe,CAAC,gBAAD,CAAf,EAAmC,OAAnC,CAXhB;IAYJ6G,IAAI,EAAE7G,aAAa,CAAC,WAAD,EAAc,CAAC,eAAD,CAAd,EAAiC,OAAjC,CAZf;IAaJ8G,KAAK,EAAE9G,aAAa,CAAC,YAAD,EAAe,CAAC,gBAAD,CAAf,EAAmC,OAAnC,CAbhB;IAcJ+G,KAAK,EAAE/G,aAAa,CAAC,YAAD,EAAe,CAAC,gBAAD,CAAf,EAAmC,OAAnC,CAdhB;IAeJgH,IAAI,EAAEhH,aAAa,CAAC,WAAD,EAAc,CAAC,eAAD,CAAd,EAAiC,OAAjC,CAff;IAgBJiH,IAAI,EAAEjH,aAAa,CAAC,WAAD,EAAc,CAAC,eAAD,CAAd,EAAiC,OAAjC,CAhBf;IAiBJkH,IAAI,EAAElH,aAAa,CAAC,WAAD,EAAc,CAAC,eAAD,CAAd,EAAiC,OAAjC,CAjBf;IAkBJmH,IAAI,EAAEnH,aAAa,CAAC,WAAD,EAAc,CAAC,eAAD,CAAd,EAAiC,OAAjC,CAlBf;IAmBJoH,KAAK,EAAEpH,aAAa,CAAC,YAAD,EAAe,CAAC,gBAAD,CAAf,EAAmC,OAAnC;GAtCpB;EAyCFe,MAAM,EAAE;IACNsG,OAAO,EAAErH,aAAa,CAAC,gBAAD,EAAmB,CAAC,oBAAD,CAAnB,CADhB;IAENsH,gBAAgB,EAAEtH,aAAa,CAAC,yBAAD,EAA4B,CACzD,6BADyD,CAA5B,CAFzB;IAKNuH,gBAAgB,EAAEvH,aAAa,CAAC,yBAAD,EAA4B,CACzD,6BADyD,CAA5B,CALzB;IAQNwH,QAAQ,EAAExH,aAAa,CAAC,kBAAD,EAAqB,CAAC,sBAAD,CAArB,CARjB;IASNyH,SAAS,EAAEzH,aAAa,CAAC,mBAAD,EAAsB,CAAC,uBAAD,CAAtB,CATlB;IAUN0H,aAAa,EAAE1H,aAAa,CAAC,wBAAD,EAA2B,CACrD,4BADqD,CAA3B,CAVtB;IAaN2H,KAAK,EAAE3H,aAAa,CAAC,eAAD,EAAkB,CAAC,mBAAD,CAAlB,CAbd;IAcN4B,UAAU,EAAE5B,aAAa,CAAC,oBAAD,EAAuB,CAAC,wBAAD,CAAvB,CAdnB;IAeN6B,QAAQ,EAAE7B,aAAa,CAAC,kBAAD,EAAqB,CAAC,sBAAD,CAArB;GAxDvB;EA2DF4H,MAAM,EAAE;IACNC,MAAM,EAAE7H,aAAa,CAAC,eAAD,EAAkB,CAAC,mBAAD,CAAlB,CADf;IAEN8H,MAAM,EAAE9H,aAAa,CAAC,eAAD,EAAkB,CAAC,mBAAD,CAAlB,CAFf;IAGN+H,gBAAgB,EAAE/H,aAAa,CAAC,0BAAD,EAA6B,CAC1D,8BAD0D,CAA7B,CAHzB;IAMNgI,cAAc,EAAEhI,aAAa,CAAC,wBAAD,EAA2B,CACtD,4BADsD,CAA3B,CANvB;IASN2C,OAAO,EAAE3C,aAAa,CAAC,gBAAD,EAAmB,CAAC,oBAAD,CAAnB,CAThB;IAUNiI,MAAM,EAAEjI,aAAa,CAAC,eAAD,EAAkB,CAAC,mBAAD,CAAlB,CAVf;IAWNkI,wBAAwB,EAAElI,aAAa,CACrC,oCADqC,EAErC,CAAC,wCAAD,CAFqC,CAXjC;IAeNmI,yBAAyB,EAAEnI,aAAa,CACtC,qCADsC,EAEtC,CAAC,yCAAD,CAFsC,CAflC;IAmBNoI,mBAAmB,EAAEpI,aAAa,CAAC,+BAAD,EAAkC,CAClE,mCADkE,CAAlC,CAnB5B;IAsBNqI,qBAAqB,EAAErI,aAAa,CAAC,iCAAD,EAAoC,CACtE,YADsE,CAApC,CAtB9B;IAyBNsI,cAAc,EAAEtI,aAAa,CAAC,yBAAD,EAA4B,CACvD,6BADuD,CAA5B,CAzBvB;IA4BNuI,EAAE,EAAEvI,aAAa,CAAC,WAAD,EAAc,CAAC,eAAD,CAAd,CA5BX;IA6BNwI,YAAY,EAAExI,aAAa,CAAC,sBAAD,EAAyB,CAClD,0BADkD,CAAzB,CA7BrB;IAgCNyI,QAAQ,EAAEzI,aAAa,CAAC,kBAAD,EAAqB,CAAC,sBAAD,CAArB,CAhCjB;IAiCN0I,QAAQ,EAAE1I,aAAa,CAAC,kBAAD,EAAqB,CAAC,sBAAD,CAArB,CAjCjB;IAkCN2D,IAAI,EAAE3D,aAAa,CAAC,aAAD,EAAgB,CAAC,iBAAD,CAAhB,CAlCb;IAmCN2I,iBAAiB,EAAE3I,aAAa,CAAC,2BAAD,EAA8B,CAC5D,+BAD4D,CAA9B,CAnC1B;IAsCN4I,IAAI,EAAE5I,aAAa,CAAC,aAAD,EAAgB,CAAC,iBAAD,CAAhB,CAtCb;IAuCN6I,cAAc,EAAE7I,aAAa,CAAC,yBAAD,EAA4B,CACvD,6BADuD,CAA5B,CAvCvB;IA0CNuF,MAAM,EAAEvF,aAAa,CAAC,eAAD,EAAkB,CAAC,mBAAD,CAAlB;GArGrB;EAwGFgB,OAAO,EAAE;IACP8H,GAAG,EAAE5I,UAAU,CAACG,eAAD,CADR;IAEP0I,IAAI,EAAE7I,UAAU,CAACG,eAAD;GA1GhB;EA6GF2I,OAAO,EAAE;IACPC,KAAK,EAAEjJ,aAAa,CAAC,eAAD,EAAkB,CAAC,mBAAD,CAAlB,CADb;IAEPkJ,SAAS,EAAElJ,aAAa,CAAC,mBAAD,EAAsB,CAAC,uBAAD,CAAtB,CAFjB;IAGPgI,cAAc,EAAEhI,aAAa,CAAC,yBAAD,EAA4B,CACvD,6BADuD,CAA5B,CAHtB;IAMPmJ,cAAc,EAAEnJ,aAAa,CAAC,yBAAD,EAA4B,CACvD,6BADuD,CAA5B,CANtB;IASPoJ,GAAG,EAAEpJ,aAAa,CAAC,aAAD,EAAgB,CAAC,iBAAD,CAAhB,CATX;IAUPkI,wBAAwB,EAAElI,aAAa,CACrC,qCADqC,EAErC,CAAC,yCAAD,CAFqC,CAVhC;IAcPsI,cAAc,EAAEtI,aAAa,CAAC,0BAAD,EAA6B,CACxD,8BADwD,CAA7B,CAdtB;IAiBPqJ,GAAG,EAAErJ,aAAa,CAAC,aAAD,EAAgB,CAAC,iBAAD,CAAhB,CAjBX;IAkBPwI,YAAY,EAAExI,aAAa,CAAC,uBAAD,EAA0B,CACnD,2BADmD,CAA1B,CAlBpB;IAqBPsJ,OAAO,EAAEtJ,aAAa,CAAC,kBAAD,EAAqB,CAAC,sBAAD,CAArB,CArBf;IAsBP2I,iBAAiB,EAAE3I,aAAa,CAAC,4BAAD,EAA+B,CAC7D,gCAD6D,CAA/B,CAtBzB;IAyBPuJ,GAAG,EAAEvJ,aAAa,CAAC,aAAD,EAAgB,CAAC,iBAAD,CAAhB,CAzBX;IA0BP6I,cAAc,EAAE7I,aAAa,CAAC,0BAAD,EAA6B,CACxD,8BADwD,CAA7B;GAvI7B;EA4IFwJ,MAAM,EAAE;IACNC,EAAE,EAAEtJ,QAAQ,CAAC,WAAD,EAAc,eAAd,CADN;IAENuJ,aAAa,EAAE1J,aAAa,CAAC,wBAAD,EAA2B,CACrD,4BADqD,CAA3B,CAFtB;IAKN2J,GAAG,EAAE3J,aAAa,CAAC,YAAD,EAAe,CAAC,gBAAD,CAAf;GAjJlB;EAoJFmB,MAAM,EAAE;;IAENyI,aAAa,EAAE1J,UAAU,CAAC,CAAC,YAAD,EAAe,2BAAf,CAAD,CAFnB;IAGN2J,GAAG,EAAE1J,QAAQ,CAAC,YAAD,EAAe,YAAf,CAHP;IAIN2J,WAAW,EAAE3J,QAAQ,CAAC,qBAAD,EAAwB,YAAxB,CAJf;IAKN4J,kBAAkB,EAAE5J,QAAQ,CAAC,6BAAD,EAAgC,YAAhC,CALtB;IAMN6J,QAAQ,EAAErK,MAAM,CAAC,YAAD,EAAe,iBAAf,EAAkCU,eAAlC,CANV;IAON4J,MAAM,EAAE9J,QAAQ,CAAC,gBAAD,EAAmB,YAAnB,CAPV;IAQN4D,KAAK,EAAE/D,aAAa,CAAC,cAAD,EAAiB,CAAC,kBAAD,CAAjB,CARd;IASNqE,OAAO,EAAElE,QAAQ,CAAC,gBAAD,EAAmB,YAAnB,CATX;IAUNmE,MAAM,EAAEnE,QAAQ,CAAC,eAAD,EAAkB,YAAlB,CAVV;IAWN+J,OAAO,EAAE/J,QAAQ,CAAC,gBAAD,EAAmB,YAAnB,CAXX;IAYNuE,KAAK,EAAEvE,QAAQ,CAAC,cAAD,EAAiB,YAAjB,CAZT;IAaNgK,WAAW,EAAEhK,QAAQ,CAAC,qBAAD,EAAwB,YAAxB,CAbf;IAcNiK,WAAW,EAAEjK,QAAQ,CAAC,sBAAD,EAAyB,YAAzB,CAdf;IAeNkK,WAAW,EAAElK,QAAQ,CAAC,oBAAD,EAAuB,YAAvB;;AAnKrB,CAFG;;AC5IP,MAAMmK,YAAY,GAAG;EACnB,cAAc,EADK;EAEnB,iBAAiB,EAFE;EAGnB,oBAAoB;AAHD,CAArB;AAMA,MAAMC,aAAa,GAAG;EACpB,mBAAmB,EADC;EAEpB,iBAAiB,EAFG;EAGpB,iBAAiB;AAHG,CAAtB;AAMe,uCAAUC,OAAV,EAA4BC,MAA5B,EAA4CC,SAA5C,EAA4D;EACzE,MAAMC,WAAW,GAAG/C,MAAM,CAACjE,IAAP,CAAY6G,OAAZ,CAApB;EACA,MAAMI,WAAW,GAAG,CAACD,WAAW,CAACE,MAAjC;EACA,MAAMC,WAAW,GAAGH,WAAW,CAACnG,IAAZ,CAAiB5E,IAAI,IAAIA,IAAI,KAAK,MAAlC,CAApB;EAEA,OAAO,EACL,GAAG8K,SADE;IAEL,IAAID,MAAM,KAAK,YAAX,GAA0BF,aAA1B,GAA0C,IAA9C,CAFK;IAGL,IAAIK,WAAW,IAAIE,WAAf,GAA6BR,YAA7B,GAA4C,IAAhD;GAHF;AAKD;;ACtBM,SAASS,aAAT,CACLC,UADK,EAELC,cAFK,EAGL;;;;EAIA,IAAI,CAACA,cAAD,IAAmB,CAACD,UAAxB,EAAoC,OAAO,IAAP,CAJpC;;;;;;;;;;;;;;;;;;EAuBA,IAAIE,MAAM,CAACC,KAAP,CAAaF,cAAb,CAAJ,EAAkCA,cAAc,GAAI,IAAGA,cAAe,EAApC;EAElC,OACE,CAACC,MAAM,CAACE,UAAP,CAAmB,IAAGJ,UAAW,EAAjC,EAAoCC,cAApC,CAAD,IACA,CAACC,MAAM,CAACE,UAAP,CAAmB,SAAnB,EAA6BH,cAA7B,CAFH;AAID;;;ECtBQI,OAASC;;AAElB,MAAMC,aAAa,GAAG,wBAAtB;AAEA,MAAMC,eAAe,GAAG,iDAAxB;AACA,MAAMC,aAAa,GAAG,8CAAtB;AAEA,MAAMpC,GAAG,GAAGqC,QAAQ,CAACC,IAAT,CAActJ,IAAd,CAAmBuF,MAAM,CAACgE,cAA1B,CAAZ;AAcA,YAAeC,cAAc,CAAU,UACrCC,GADqC,EAErC;EACE,CAACN,eAAD,GAAmB;IACjBO,sBAAsB,GAAG,KADR;IAEjBC,aAAa,GAAG;MACd,EAJN;EAKE,CAACP,aAAD,GAAiB;IACfQ,eAAe,GAAG,KADH;IAEfhB,cAAc,GAAG,EAFF;IAGfiB,GAAG,GAAG;MACJ;AATN,CAFqC,EAarC;EACA,MAAMC,OAAO,GAAGL,GAAG,CAACM,kBAAJ,CAAuB;IACrCtM,MAAM,EAAES,QAD6B;IAErC8L,MAAM,EAAE3G,gBAF6B;IAGrC4G,QAAQ,EAAExK;GAHI,CAAhB;EAMA,MAAM;IAAEyK,KAAF;IAASC,oBAAT;IAA+B/B;MAAWqB,GAAhD;EAEA,MAAMpB,SAAS,GAAG+B,4BAA4B,CAC5CX,GAAG,CAACtB,OADwC,EAE5CC,MAF4C,EAG5CjF,gBAH4C,CAA9C;EAMA,MAAMkH,UAAU,GAAGT,eAAe,GAC7B,GAAEV,aAAc,UADa,GAE9Bd,MAAM,KAAK,YAAX,GACA,oBADA,GAEA,iBAJJ;;EAMA,SAASkC,MAAT,CAAgB/M,IAAhB,EAAyCgN,KAAzC,EAAgD;IAC9C,IAAI,OAAOhN,IAAP,KAAgB,QAApB,EAA8B;;;MAG5B,IAAIyJ,GAAG,CAACqB,SAAD,EAAY9K,IAAZ,CAAH,IAAwB4M,oBAAoB,CAAC5M,IAAD,CAAhD,EAAwD;QACtD2M,KAAK,CAAC3M,IAAD,CAAL;QACAgN,KAAK,CAACC,kBAAN,CAA0B,GAAEH,UAAW,IAAG9M,IAAK,KAA/C;;;MAEF;;;IAGFA,IAAI,CAAC2D,OAAL,CAAa3D,IAAI,IAAI+M,MAAM,CAAC/M,IAAD,EAAOgN,KAAP,CAA3B;;;EAGF,SAASE,eAAT,CAAyBC,IAAzB,EAA+BC,IAA/B,EAAqCJ,KAArC,EAA4C;IAC1C,IAAI;MAAE/M,IAAF;MAAQE,IAAR;MAAcH;QAASmN,IAA3B;IAEA,IAAI,CAAClN,IAAD,IAAS,CAAC2M,oBAAoB,CAAC5M,IAAD,CAAlC,EAA0C;;IAE1C,IACEqL,cAAc,IACdlL,IADA,IAEAA,IAAI,CAACE,iBAFL,IAGA,CAAC8K,aAAa,CAAChL,IAAI,IAAIA,IAAI,CAACE,iBAAd,EAAiCgL,cAAjC,CAJhB,EAKE;MACA;KAXwC;;;;IAgB1C,IAAIgB,eAAe,IAAIpM,IAAI,KAAK,cAAhC,EAAgDA,IAAI,GAAG,QAAP;IAEhD,OAAO+M,KAAK,CAACK,mBAAN,CAA2B,GAAEP,UAAW,IAAG7M,IAAK,GAAEqM,GAAI,EAAtD,EAAyDc,IAAzD,CAAP;;;EAGF,OAAO;IACLpN,IAAI,EAAE,SADD;IAGLsN,WAAW,EAAElB,aAAa,GAAG,IAAH,GAAUT,aAH/B;IAKLb,SALK;;IAOLyC,WAAW,CAACpN,IAAD,EAAO6M,KAAP,EAAcQ,IAAd,EAAoB;MAC7B,IAAIrN,IAAI,CAACsN,IAAL,KAAc,QAAd,IAA0BtN,IAAI,CAACuN,MAAL,KAAgB,SAA9C,EAAyD;QACvDf,KAAK,CAAC,IAAD,CAAL;QAEAI,MAAM,CAAC/E,MAAM,CAACjE,IAAP,CAAY+G,SAAZ,CAAD,EAAyBkC,KAAzB,CAAN;;QAEA,IAAIb,sBAAJ,EAA4B;UAC1Ba,KAAK,CAACC,kBAAN,CAAyB,gCAAzB;;;QAGFO,IAAI,CAACG,MAAL;;KAjBC;;IAqBLC,WAAW,CAACzN,IAAD,EAAO6M,KAAP,EAAc;MACvB,MAAMa,QAAQ,GAAGtB,OAAO,CAACpM,IAAD,CAAxB;MACA,IAAI,CAAC0N,QAAL,EAAe;MAEf,IAAIC,IAAI,GAAGD,QAAQ,CAACV,IAAT,CAAcjN,MAAzB;;MAEA,IACE2N,QAAQ,CAACJ,IAAT,KAAkB,QAAlB,IACA,YAAYtN,IADZ,IAEAA,IAAI,CAAC4N,MAFL,IAGA5N,IAAI,CAAC6N,SAAL,KAAmB,WAJrB,EAKE;QACA,MAAMC,GAAG,GAAG9N,IAAI,CAAC4N,MAAL,CAAYG,WAAZ,EAAZ;QACAJ,IAAI,GAAGA,IAAI,CAAC5K,MAAL,CAAYiL,CAAC,IAAIA,CAAC,CAACvK,QAAF,CAAWqK,GAAX,CAAjB,CAAP;;;MAGFlB,MAAM,CAACe,IAAD,EAAOd,KAAP,CAAN;KArCG;;IAwCLoB,SAAS,CAACjO,IAAD,EAAO6M,KAAP,EAAcQ,IAAd,EAAoB;MAC3B,IAAIrN,IAAI,CAACsN,IAAL,KAAc,IAAlB,EAAwB;QACtB,IAAItN,IAAI,CAACkO,GAAL,KAAa,iBAAjB,EAAoC;UAClCb,IAAI,CAACc,WAAL,CACE5C,CAAC,CAAC6C,cAAF,CACEvB,KAAK,CAACK,mBAAN,CACG,GAAEP,UAAW,eAAcR,GAAI,EADlC,EAEE,YAFF,CADF,EAKE,CAAEkB,IAAI,CAACgB,IAAN,CAAkCC,KAAnC,CALF;WADF;;;QAWF;;;MAGF,IAAIjB,IAAI,CAACkB,UAAL,CAAgBC,iBAAhB,CAAkC;QAAEC,QAAQ,EAAE;OAA9C,CAAJ,EAA+D;;MAE/D,IAAIzO,IAAI,CAACsN,IAAL,KAAc,UAAlB,EAA8B;;QAE5B,IAAI,CAACD,IAAI,CAACqB,kBAAL,EAAL,EAAgC;QAChC,IAAI,CAACrB,IAAI,CAACsB,YAAL,EAAL,EAA0B;;QAE1B,IACE3O,IAAI,CAACkO,GAAL,KAAa,iBAAb,IACAzB,oBAAoB,CAAC,YAAD,CADpB,IAEAY,IAAI,CAACkB,UAAL,CAAgBK,gBAAhB,CAAiC;UAAEC,MAAM,EAAExB,IAAI,CAACgB;SAAhD,CAFA,IAGAhB,IAAI,CAACkB,UAAL,CAAgBF,IAAhB,CAAqBS,SAArB,CAA+BhE,MAA/B,KAA0C,CAJ5C,EAKE;UACAuC,IAAI,CAACkB,UAAL,CAAgBJ,WAAhB,CACE5C,CAAC,CAAC6C,cAAF,CACEvB,KAAK,CAACK,mBAAN,CACG,GAAEP,UAAW,gBAAeR,GAAI,EADnC,EAEE,aAFF,CADF,EAKE,CAACkB,IAAI,CAACgB,IAAL,CAAUT,MAAX,CALF,CADF;UASAP,IAAI,CAAC0B,IAAL;UAEA;;;;MAIJ,MAAMrB,QAAQ,GAAGtB,OAAO,CAACpM,IAAD,CAAxB;MACA,IAAI,CAAC0N,QAAL,EAAe;MAEf,MAAMsB,EAAE,GAAGjC,eAAe,CAACW,QAAQ,CAACV,IAAV,EAAgBU,QAAQ,CAAC7N,IAAzB,EAA+BgN,KAA/B,CAA1B;MACA,IAAImC,EAAJ,EAAQ3B,IAAI,CAACc,WAAL,CAAiBa,EAAjB;KAzFL;;IA4FLC,OAAO,EAAEvE,MAAM,KAAK,cAAX,IAA6B;;MAEpCwE,eAAe,CAAC7B,IAAD,EAAoC;QACjD,IAAIA,IAAI,CAACgB,IAAL,CAAUc,QAAd,EAAwB;UACtBvC,MAAM,CAAC,kBAAD,EAAqBb,GAAG,CAACqD,QAAJ,CAAa/B,IAAb,CAArB,CAAN;;OAJgC;;;MASpC,8BACEA,IADF,EAEE;QACA/M,eAAe,CAACkD,OAAhB,CAAwB3D,IAAI,IAAI+M,MAAM,CAAC/M,IAAD,EAAOkM,GAAG,CAACqD,QAAJ,CAAa/B,IAAb,CAAP,CAAtC;;;;GAxGN;AA4GD,CAjL4B,CAA7B;;;;"}