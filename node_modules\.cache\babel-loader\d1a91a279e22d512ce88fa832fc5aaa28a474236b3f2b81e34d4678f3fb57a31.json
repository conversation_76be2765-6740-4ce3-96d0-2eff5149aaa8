{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _DatetimePicker from \"./DatetimePicker.mjs\";\nvar DatetimePicker = withInstall(_DatetimePicker);\nvar stdin_default = DatetimePicker;\nexport { DatetimePicker, stdin_default as default };", "map": {"version": 3, "names": ["withInstall", "_DatetimePicker", "DatetimePicker", "stdin_default", "default"], "sources": ["C:/Users/<USER>/Desktop/vue3_3.0/node_modules/vant/es/datetime-picker/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _DatetimePicker from \"./DatetimePicker.mjs\";\nconst DatetimePicker = withInstall(_DatetimePicker);\nvar stdin_default = DatetimePicker;\nexport {\n  DatetimePicker,\n  stdin_default as default\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,eAAe,MAAM,sBAAsB;AAClD,IAAMC,cAAc,GAAGF,WAAW,CAACC,eAAe,CAAC;AACnD,IAAIE,aAAa,GAAGD,cAAc;AAClC,SACEA,cAAc,EACdC,aAAa,IAAIC,OAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}