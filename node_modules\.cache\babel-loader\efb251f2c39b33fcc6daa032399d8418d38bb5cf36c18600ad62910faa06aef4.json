{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _CollapseItem from \"./CollapseItem.mjs\";\nvar CollapseItem = withInstall(_CollapseItem);\nvar stdin_default = CollapseItem;\nexport { CollapseItem, stdin_default as default };", "map": {"version": 3, "names": ["withInstall", "_CollapseItem", "CollapseItem", "stdin_default", "default"], "sources": ["C:/Users/<USER>/Downloads/Telegram Desktop/vue3_3.0/vue3_3.0/node_modules/vant/es/collapse-item/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _CollapseItem from \"./CollapseItem.mjs\";\nconst CollapseItem = withInstall(_CollapseItem);\nvar stdin_default = CollapseItem;\nexport {\n  CollapseItem,\n  stdin_default as default\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,aAAa,MAAM,oBAAoB;AAC9C,IAAMC,YAAY,GAAGF,WAAW,CAACC,aAAa,CAAC;AAC/C,IAAIE,aAAa,GAAGD,YAAY;AAChC,SACEA,YAAY,EACZC,aAAa,IAAIC,OAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}