{"ast": null, "code": "function _createForOfIteratorHelper(o, allowArrayLike) { var it = typeof Symbol !== \"undefined\" && o[Symbol.iterator] || o[\"@@iterator\"]; if (!it) { if (Array.isArray(o) || (it = _unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === \"number\") { if (it) o = it; var i = 0; var F = function F() {}; return { s: F, n: function n() { if (i >= o.length) return { done: true }; return { done: false, value: o[i++] }; }, e: function e(_e2) { throw _e2; }, f: F }; } throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); } var normalCompletion = true, didErr = false, err; return { s: function s() { it = it.call(o); }, n: function n() { var step = it.next(); normalCompletion = step.done; return step; }, e: function e(_e3) { didErr = true; err = _e3; }, f: function f() { try { if (!normalCompletion && it.return != null) it.return(); } finally { if (didErr) throw err; } } }; }\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor); } }\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, \"prototype\", { writable: false }); return Constructor; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return _typeof(key) === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (_typeof(input) !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (_typeof(res) !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function\"); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, writable: true, configurable: true } }); Object.defineProperty(subClass, \"prototype\", { writable: false }); if (superClass) _setPrototypeOf(subClass, superClass); }\nfunction _setPrototypeOf(o, p) { _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) { o.__proto__ = p; return o; }; return _setPrototypeOf(o, p); }\nfunction _createSuper(Derived) { var hasNativeReflectConstruct = _isNativeReflectConstruct(); return function _createSuperInternal() { var Super = _getPrototypeOf(Derived), result; if (hasNativeReflectConstruct) { var NewTarget = _getPrototypeOf(this).constructor; result = Reflect.construct(Super, arguments, NewTarget); } else { result = Super.apply(this, arguments); } return _possibleConstructorReturn(this, result); }; }\nfunction _possibleConstructorReturn(self, call) { if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) { return call; } else if (call !== void 0) { throw new TypeError(\"Derived constructors may only return object or undefined\"); } return _assertThisInitialized(self); }\nfunction _assertThisInitialized(self) { if (self === void 0) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return self; }\nfunction _isNativeReflectConstruct() { if (typeof Reflect === \"undefined\" || !Reflect.construct) return false; if (Reflect.construct.sham) return false; if (typeof Proxy === \"function\") return true; try { Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); return true; } catch (e) { return false; } }\nfunction _getPrototypeOf(o) { _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) { return o.__proto__ || Object.getPrototypeOf(o); }; return _getPrototypeOf(o); }\nfunction _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _iterableToArrayLimit(arr, i) { var _i = null == arr ? null : \"undefined\" != typeof Symbol && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (null != _i) { var _s, _e, _x, _r, _arr = [], _n = !0, _d = !1; try { if (_x = (_i = _i.call(arr)).next, 0 === i) { if (Object(_i) !== _i) return; _n = !1; } else for (; !(_n = (_s = _x.call(_i)).done) && (_arr.push(_s.value), _arr.length !== i); _n = !0); } catch (err) { _d = !0, _e = err; } finally { try { if (!_n && null != _i.return && (_r = _i.return(), Object(_r) !== _r)) return; } finally { if (_d) throw _e; } } return _arr; } }\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\nfunction _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\nfunction _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread(); }\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _iterableToArray(iter) { if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter); }\nfunction _arrayWithoutHoles(arr) { if (Array.isArray(arr)) return _arrayLikeToArray(arr); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nimport { camelize, warn, callWithAsyncErrorHandling, defineComponent, nextTick, createVNode, getCurrentInstance, watchPostEffect, onMounted, onUnmounted, Fragment, Static, h, BaseTransition, useTransitionState, onUpdated, toRaw, getTransitionRawChildren, setTransitionHooks, resolveTransitionHooks, createRenderer, isRuntimeOnly, createHydrationRenderer } from '@vue/runtime-core';\nexport * from '@vue/runtime-core';\nimport { isString, isArray, hyphenate, capitalize, isSpecialBooleanAttr, includeBooleanAttr, isOn, isModelListener, isFunction, toNumber, camelize as camelize$1, extend, EMPTY_OBJ, isObject, invokeArrayFns, looseIndexOf, isSet, looseEqual, isHTMLTag, isSVGTag } from '@vue/shared';\nvar svgNS = 'http://www.w3.org/2000/svg';\nvar doc = typeof document !== 'undefined' ? document : null;\nvar staticTemplateCache = new Map();\nvar nodeOps = {\n  insert: function insert(child, parent, anchor) {\n    parent.insertBefore(child, anchor || null);\n  },\n  remove: function remove(child) {\n    var parent = child.parentNode;\n    if (parent) {\n      parent.removeChild(child);\n    }\n  },\n  createElement: function createElement(tag, isSVG, is, props) {\n    var el = isSVG ? doc.createElementNS(svgNS, tag) : doc.createElement(tag, is ? {\n      is: is\n    } : undefined);\n    if (tag === 'select' && props && props.multiple != null) {\n      el.setAttribute('multiple', props.multiple);\n    }\n    return el;\n  },\n  createText: function createText(text) {\n    return doc.createTextNode(text);\n  },\n  createComment: function createComment(text) {\n    return doc.createComment(text);\n  },\n  setText: function setText(node, text) {\n    node.nodeValue = text;\n  },\n  setElementText: function setElementText(el, text) {\n    el.textContent = text;\n  },\n  parentNode: function parentNode(node) {\n    return node.parentNode;\n  },\n  nextSibling: function nextSibling(node) {\n    return node.nextSibling;\n  },\n  querySelector: function querySelector(selector) {\n    return doc.querySelector(selector);\n  },\n  setScopeId: function setScopeId(el, id) {\n    el.setAttribute(id, '');\n  },\n  cloneNode: function cloneNode(el) {\n    var cloned = el.cloneNode(true);\n    // #3072\n    // - in `patchDOMProp`, we store the actual value in the `el._value` property.\n    // - normally, elements using `:value` bindings will not be hoisted, but if\n    //   the bound value is a constant, e.g. `:value=\"true\"` - they do get\n    //   hoisted.\n    // - in production, hoisted nodes are cloned when subsequent inserts, but\n    //   cloneNode() does not copy the custom property we attached.\n    // - This may need to account for other custom DOM properties we attach to\n    //   elements in addition to `_value` in the future.\n    if (\"_value\" in el) {\n      cloned._value = el._value;\n    }\n    return cloned;\n  },\n  // __UNSAFE__\n  // Reason: innerHTML.\n  // Static content here can only come from compiled templates.\n  // As long as the user only uses trusted templates, this is safe.\n  insertStaticContent: function insertStaticContent(content, parent, anchor, isSVG) {\n    // <parent> before | first ... last | anchor </parent>\n    var before = anchor ? anchor.previousSibling : parent.lastChild;\n    var template = staticTemplateCache.get(content);\n    if (!template) {\n      var t = doc.createElement('template');\n      t.innerHTML = isSVG ? \"<svg>\".concat(content, \"</svg>\") : content;\n      template = t.content;\n      if (isSVG) {\n        // remove outer svg wrapper\n        var wrapper = template.firstChild;\n        while (wrapper.firstChild) {\n          template.appendChild(wrapper.firstChild);\n        }\n        template.removeChild(wrapper);\n      }\n      staticTemplateCache.set(content, template);\n    }\n    parent.insertBefore(template.cloneNode(true), anchor);\n    return [\n    // first\n    before ? before.nextSibling : parent.firstChild,\n    // last\n    anchor ? anchor.previousSibling : parent.lastChild];\n  }\n};\n\n// compiler should normalize class + :class bindings on the same element\n// into a single binding ['staticClass', dynamic]\nfunction patchClass(el, value, isSVG) {\n  // directly setting className should be faster than setAttribute in theory\n  // if this is an element during a transition, take the temporary transition\n  // classes into account.\n  var transitionClasses = el._vtc;\n  if (transitionClasses) {\n    value = (value ? [value].concat(_toConsumableArray(transitionClasses)) : _toConsumableArray(transitionClasses)).join(' ');\n  }\n  if (value == null) {\n    el.removeAttribute('class');\n  } else if (isSVG) {\n    el.setAttribute('class', value);\n  } else {\n    el.className = value;\n  }\n}\nfunction patchStyle(el, prev, next) {\n  var style = el.style;\n  var isCssString = isString(next);\n  if (next && !isCssString) {\n    for (var key in next) {\n      setStyle(style, key, next[key]);\n    }\n    if (prev && !isString(prev)) {\n      for (var _key in prev) {\n        if (next[_key] == null) {\n          setStyle(style, _key, '');\n        }\n      }\n    }\n  } else {\n    var currentDisplay = style.display;\n    if (isCssString) {\n      if (prev !== next) {\n        style.cssText = next;\n      }\n    } else if (prev) {\n      el.removeAttribute('style');\n    }\n    // indicates that the `display` of the element is controlled by `v-show`,\n    // so we always keep the current `display` value regardless of the `style`\n    // value, thus handing over control to `v-show`.\n    if ('_vod' in el) {\n      style.display = currentDisplay;\n    }\n  }\n}\nvar importantRE = /\\s*!important$/;\nfunction setStyle(style, name, val) {\n  if (isArray(val)) {\n    val.forEach(function (v) {\n      return setStyle(style, name, v);\n    });\n  } else {\n    if (name.startsWith('--')) {\n      // custom property definition\n      style.setProperty(name, val);\n    } else {\n      var prefixed = autoPrefix(style, name);\n      if (importantRE.test(val)) {\n        // !important\n        style.setProperty(hyphenate(prefixed), val.replace(importantRE, ''), 'important');\n      } else {\n        style[prefixed] = val;\n      }\n    }\n  }\n}\nvar prefixes = ['Webkit', 'Moz', 'ms'];\nvar prefixCache = {};\nfunction autoPrefix(style, rawName) {\n  var cached = prefixCache[rawName];\n  if (cached) {\n    return cached;\n  }\n  var name = camelize(rawName);\n  if (name !== 'filter' && name in style) {\n    return prefixCache[rawName] = name;\n  }\n  name = capitalize(name);\n  for (var i = 0; i < prefixes.length; i++) {\n    var prefixed = prefixes[i] + name;\n    if (prefixed in style) {\n      return prefixCache[rawName] = prefixed;\n    }\n  }\n  return rawName;\n}\nvar xlinkNS = 'http://www.w3.org/1999/xlink';\nfunction patchAttr(el, key, value, isSVG, instance) {\n  if (isSVG && key.startsWith('xlink:')) {\n    if (value == null) {\n      el.removeAttributeNS(xlinkNS, key.slice(6, key.length));\n    } else {\n      el.setAttributeNS(xlinkNS, key, value);\n    }\n  } else {\n    // note we are only checking boolean attributes that don't have a\n    // corresponding dom prop of the same name here.\n    var isBoolean = isSpecialBooleanAttr(key);\n    if (value == null || isBoolean && !includeBooleanAttr(value)) {\n      el.removeAttribute(key);\n    } else {\n      el.setAttribute(key, isBoolean ? '' : value);\n    }\n  }\n}\n\n// __UNSAFE__\n// functions. The user is responsible for using them with only trusted content.\nfunction patchDOMProp(el, key, value,\n// the following args are passed only due to potential innerHTML/textContent\n// overriding existing VNodes, in which case the old tree must be properly\n// unmounted.\nprevChildren, parentComponent, parentSuspense, unmountChildren) {\n  if (key === 'innerHTML' || key === 'textContent') {\n    if (prevChildren) {\n      unmountChildren(prevChildren, parentComponent, parentSuspense);\n    }\n    el[key] = value == null ? '' : value;\n    return;\n  }\n  if (key === 'value' && el.tagName !== 'PROGRESS' &&\n  // custom elements may use _value internally\n  !el.tagName.includes('-')) {\n    // store value as _value as well since\n    // non-string values will be stringified.\n    el._value = value;\n    var newValue = value == null ? '' : value;\n    if (el.value !== newValue ||\n    // #4956: always set for OPTION elements because its value falls back to\n    // textContent if no value attribute is present. And setting .value for\n    // OPTION has no side effect\n    el.tagName === 'OPTION') {\n      el.value = newValue;\n    }\n    if (value == null) {\n      el.removeAttribute(key);\n    }\n    return;\n  }\n  if (value === '' || value == null) {\n    var type = _typeof(el[key]);\n    if (type === 'boolean') {\n      // e.g. <select multiple> compiles to { multiple: '' }\n      el[key] = includeBooleanAttr(value);\n      return;\n    } else if (value == null && type === 'string') {\n      // e.g. <div :id=\"null\">\n      el[key] = '';\n      el.removeAttribute(key);\n      return;\n    } else if (type === 'number') {\n      // e.g. <img :width=\"null\">\n      // the value of some IDL attr must be greater than 0, e.g. input.size = 0 -> error\n      try {\n        el[key] = 0;\n      } catch (_a) {}\n      el.removeAttribute(key);\n      return;\n    }\n  }\n  // some properties perform value validation and throw\n  try {\n    el[key] = value;\n  } catch (e) {\n    if (process.env.NODE_ENV !== 'production') {\n      warn(\"Failed setting prop \\\"\".concat(key, \"\\\" on <\").concat(el.tagName.toLowerCase(), \">: \") + \"value \".concat(value, \" is invalid.\"), e);\n    }\n  }\n}\n\n// Async edge case fix requires storing an event listener's attach timestamp.\nvar _getNow = Date.now;\nvar skipTimestampCheck = false;\nif (typeof window !== 'undefined') {\n  // Determine what event timestamp the browser is using. Annoyingly, the\n  // timestamp can either be hi-res (relative to page load) or low-res\n  // (relative to UNIX epoch), so in order to compare time we have to use the\n  // same timestamp type when saving the flush timestamp.\n  if (_getNow() > document.createEvent('Event').timeStamp) {\n    // if the low-res timestamp which is bigger than the event timestamp\n    // (which is evaluated AFTER) it means the event is using a hi-res timestamp,\n    // and we need to use the hi-res version for event listeners as well.\n    _getNow = function _getNow() {\n      return performance.now();\n    };\n  }\n  // #3485: Firefox <= 53 has incorrect Event.timeStamp implementation\n  // and does not fire microtasks in between event propagation, so safe to exclude.\n  var ffMatch = navigator.userAgent.match(/firefox\\/(\\d+)/i);\n  skipTimestampCheck = !!(ffMatch && Number(ffMatch[1]) <= 53);\n}\n// To avoid the overhead of repeatedly calling performance.now(), we cache\n// and use the same timestamp for all event listeners attached in the same tick.\nvar cachedNow = 0;\nvar p = Promise.resolve();\nvar reset = function reset() {\n  cachedNow = 0;\n};\nvar getNow = function getNow() {\n  return cachedNow || (p.then(reset), cachedNow = _getNow());\n};\nfunction addEventListener(el, event, handler, options) {\n  el.addEventListener(event, handler, options);\n}\nfunction removeEventListener(el, event, handler, options) {\n  el.removeEventListener(event, handler, options);\n}\nfunction patchEvent(el, rawName, prevValue, nextValue) {\n  var instance = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : null;\n  // vei = vue event invokers\n  var invokers = el._vei || (el._vei = {});\n  var existingInvoker = invokers[rawName];\n  if (nextValue && existingInvoker) {\n    // patch\n    existingInvoker.value = nextValue;\n  } else {\n    var _parseName = parseName(rawName),\n      _parseName2 = _slicedToArray(_parseName, 2),\n      name = _parseName2[0],\n      options = _parseName2[1];\n    if (nextValue) {\n      // add\n      var invoker = invokers[rawName] = createInvoker(nextValue, instance);\n      addEventListener(el, name, invoker, options);\n    } else if (existingInvoker) {\n      // remove\n      removeEventListener(el, name, existingInvoker, options);\n      invokers[rawName] = undefined;\n    }\n  }\n}\nvar optionsModifierRE = /(?:Once|Passive|Capture)$/;\nfunction parseName(name) {\n  var options;\n  if (optionsModifierRE.test(name)) {\n    options = {};\n    var m;\n    while (m = name.match(optionsModifierRE)) {\n      name = name.slice(0, name.length - m[0].length);\n      options[m[0].toLowerCase()] = true;\n    }\n  }\n  return [hyphenate(name.slice(2)), options];\n}\nfunction createInvoker(initialValue, instance) {\n  var invoker = function invoker(e) {\n    // async edge case #6566: inner click event triggers patch, event handler\n    // attached to outer element during patch, and triggered again. This\n    // happens because browsers fire microtask ticks between event propagation.\n    // the solution is simple: we save the timestamp when a handler is attached,\n    // and the handler would only fire if the event passed to it was fired\n    // AFTER it was attached.\n    var timeStamp = e.timeStamp || _getNow();\n    if (skipTimestampCheck || timeStamp >= invoker.attached - 1) {\n      callWithAsyncErrorHandling(patchStopImmediatePropagation(e, invoker.value), instance, 5 /* NATIVE_EVENT_HANDLER */, [e]);\n    }\n  };\n  invoker.value = initialValue;\n  invoker.attached = getNow();\n  return invoker;\n}\nfunction patchStopImmediatePropagation(e, value) {\n  if (isArray(value)) {\n    var originalStop = e.stopImmediatePropagation;\n    e.stopImmediatePropagation = function () {\n      originalStop.call(e);\n      e._stopped = true;\n    };\n    return value.map(function (fn) {\n      return function (e) {\n        return !e._stopped && fn(e);\n      };\n    });\n  } else {\n    return value;\n  }\n}\nvar nativeOnRE = /^on[a-z]/;\nvar patchProp = function patchProp(el, key, prevValue, nextValue) {\n  var isSVG = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : false;\n  var prevChildren = arguments.length > 5 ? arguments[5] : undefined;\n  var parentComponent = arguments.length > 6 ? arguments[6] : undefined;\n  var parentSuspense = arguments.length > 7 ? arguments[7] : undefined;\n  var unmountChildren = arguments.length > 8 ? arguments[8] : undefined;\n  if (key === 'class') {\n    patchClass(el, nextValue, isSVG);\n  } else if (key === 'style') {\n    patchStyle(el, prevValue, nextValue);\n  } else if (isOn(key)) {\n    // ignore v-model listeners\n    if (!isModelListener(key)) {\n      patchEvent(el, key, prevValue, nextValue, parentComponent);\n    }\n  } else if (key[0] === '.' ? (key = key.slice(1), true) : key[0] === '^' ? (key = key.slice(1), false) : shouldSetAsProp(el, key, nextValue, isSVG)) {\n    patchDOMProp(el, key, nextValue, prevChildren, parentComponent, parentSuspense, unmountChildren);\n  } else {\n    // special case for <input v-model type=\"checkbox\"> with\n    // :true-value & :false-value\n    // store value as dom properties since non-string values will be\n    // stringified.\n    if (key === 'true-value') {\n      el._trueValue = nextValue;\n    } else if (key === 'false-value') {\n      el._falseValue = nextValue;\n    }\n    patchAttr(el, key, nextValue, isSVG);\n  }\n};\nfunction shouldSetAsProp(el, key, value, isSVG) {\n  if (isSVG) {\n    // most keys must be set as attribute on svg elements to work\n    // ...except innerHTML & textContent\n    if (key === 'innerHTML' || key === 'textContent') {\n      return true;\n    }\n    // or native onclick with function values\n    if (key in el && nativeOnRE.test(key) && isFunction(value)) {\n      return true;\n    }\n    return false;\n  }\n  // spellcheck and draggable are numerated attrs, however their\n  // corresponding DOM properties are actually booleans - this leads to\n  // setting it with a string \"false\" value leading it to be coerced to\n  // `true`, so we need to always treat them as attributes.\n  // Note that `contentEditable` doesn't have this problem: its DOM\n  // property is also enumerated string values.\n  if (key === 'spellcheck' || key === 'draggable') {\n    return false;\n  }\n  // #1787, #2840 form property on form elements is readonly and must be set as\n  // attribute.\n  if (key === 'form') {\n    return false;\n  }\n  // #1526 <input list> must be set as attribute\n  if (key === 'list' && el.tagName === 'INPUT') {\n    return false;\n  }\n  // #2766 <textarea type> must be set as attribute\n  if (key === 'type' && el.tagName === 'TEXTAREA') {\n    return false;\n  }\n  // native onclick with string value, must be set as attribute\n  if (nativeOnRE.test(key) && isString(value)) {\n    return false;\n  }\n  return key in el;\n}\nfunction defineCustomElement(options, hydate) {\n  var Comp = defineComponent(options);\n  var VueCustomElement = /*#__PURE__*/function (_VueElement) {\n    _inherits(VueCustomElement, _VueElement);\n    var _super = _createSuper(VueCustomElement);\n    function VueCustomElement(initialProps) {\n      _classCallCheck(this, VueCustomElement);\n      return _super.call(this, Comp, initialProps, hydate);\n    }\n    return _createClass(VueCustomElement);\n  }(VueElement);\n  VueCustomElement.def = Comp;\n  return VueCustomElement;\n}\nvar defineSSRCustomElement = function defineSSRCustomElement(options) {\n  // @ts-ignore\n  return defineCustomElement(options, hydrate);\n};\nvar BaseClass = typeof HTMLElement !== 'undefined' ? HTMLElement : /*#__PURE__*/function () {\n  function _class() {\n    _classCallCheck(this, _class);\n  }\n  return _createClass(_class);\n}();\nvar VueElement = /*#__PURE__*/function (_BaseClass) {\n  _inherits(VueElement, _BaseClass);\n  var _super2 = _createSuper(VueElement);\n  function VueElement(_def) {\n    var _this;\n    var _props = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    var hydrate = arguments.length > 2 ? arguments[2] : undefined;\n    _classCallCheck(this, VueElement);\n    _this = _super2.call(this);\n    _this._def = _def;\n    _this._props = _props;\n    /**\r\n     * @internal\r\n     */\n    _this._instance = null;\n    _this._connected = false;\n    _this._resolved = false;\n    _this._numberProps = null;\n    if (_this.shadowRoot && hydrate) {\n      hydrate(_this._createVNode(), _this.shadowRoot);\n    } else {\n      if (process.env.NODE_ENV !== 'production' && _this.shadowRoot) {\n        warn(\"Custom element has pre-rendered declarative shadow root but is not \" + \"defined as hydratable. Use `defineSSRCustomElement`.\");\n      }\n      _this.attachShadow({\n        mode: 'open'\n      });\n    }\n    return _this;\n  }\n  _createClass(VueElement, [{\n    key: \"connectedCallback\",\n    value: function connectedCallback() {\n      this._connected = true;\n      if (!this._instance) {\n        this._resolveDef();\n      }\n    }\n  }, {\n    key: \"disconnectedCallback\",\n    value: function disconnectedCallback() {\n      var _this2 = this;\n      this._connected = false;\n      nextTick(function () {\n        if (!_this2._connected) {\n          render(null, _this2.shadowRoot);\n          _this2._instance = null;\n        }\n      });\n    }\n    /**\r\n     * resolve inner component definition (handle possible async component)\r\n     */\n  }, {\n    key: \"_resolveDef\",\n    value: function _resolveDef() {\n      var _this3 = this;\n      if (this._resolved) {\n        return;\n      }\n      this._resolved = true;\n      // set initial attrs\n      for (var i = 0; i < this.attributes.length; i++) {\n        this._setAttr(this.attributes[i].name);\n      }\n      // watch future attr changes\n      new MutationObserver(function (mutations) {\n        var _iterator = _createForOfIteratorHelper(mutations),\n          _step;\n        try {\n          for (_iterator.s(); !(_step = _iterator.n()).done;) {\n            var m = _step.value;\n            _this3._setAttr(m.attributeName);\n          }\n        } catch (err) {\n          _iterator.e(err);\n        } finally {\n          _iterator.f();\n        }\n      }).observe(this, {\n        attributes: true\n      });\n      var resolve = function resolve(def) {\n        var props = def.props,\n          styles = def.styles;\n        var hasOptions = !isArray(props);\n        var rawKeys = props ? hasOptions ? Object.keys(props) : props : [];\n        // cast Number-type props set before resolve\n        var numberProps;\n        if (hasOptions) {\n          for (var key in _this3._props) {\n            var opt = props[key];\n            if (opt === Number || opt && opt.type === Number) {\n              _this3._props[key] = toNumber(_this3._props[key]);\n              (numberProps || (numberProps = Object.create(null)))[key] = true;\n            }\n          }\n        }\n        _this3._numberProps = numberProps;\n        // check if there are props set pre-upgrade or connect\n        for (var _i2 = 0, _Object$keys = Object.keys(_this3); _i2 < _Object$keys.length; _i2++) {\n          var _key2 = _Object$keys[_i2];\n          if (_key2[0] !== '_') {\n            _this3._setProp(_key2, _this3[_key2], true, false);\n          }\n        }\n        // defining getter/setters on prototype\n        var _iterator2 = _createForOfIteratorHelper(rawKeys.map(camelize$1)),\n          _step2;\n        try {\n          var _loop = function _loop() {\n            var key = _step2.value;\n            Object.defineProperty(_this3, key, {\n              get: function get() {\n                return this._getProp(key);\n              },\n              set: function set(val) {\n                this._setProp(key, val);\n              }\n            });\n          };\n          for (_iterator2.s(); !(_step2 = _iterator2.n()).done;) {\n            _loop();\n          }\n          // apply CSS\n        } catch (err) {\n          _iterator2.e(err);\n        } finally {\n          _iterator2.f();\n        }\n        _this3._applyStyles(styles);\n        // initial render\n        _this3._update();\n      };\n      var asyncDef = this._def.__asyncLoader;\n      if (asyncDef) {\n        asyncDef().then(resolve);\n      } else {\n        resolve(this._def);\n      }\n    }\n  }, {\n    key: \"_setAttr\",\n    value: function _setAttr(key) {\n      var value = this.getAttribute(key);\n      if (this._numberProps && this._numberProps[key]) {\n        value = toNumber(value);\n      }\n      this._setProp(camelize$1(key), value, false);\n    }\n    /**\r\n     * @internal\r\n     */\n  }, {\n    key: \"_getProp\",\n    value: function _getProp(key) {\n      return this._props[key];\n    }\n    /**\r\n     * @internal\r\n     */\n  }, {\n    key: \"_setProp\",\n    value: function _setProp(key, val) {\n      var shouldReflect = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : true;\n      var shouldUpdate = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : true;\n      if (val !== this._props[key]) {\n        this._props[key] = val;\n        if (shouldUpdate && this._instance) {\n          this._update();\n        }\n        // reflect\n        if (shouldReflect) {\n          if (val === true) {\n            this.setAttribute(hyphenate(key), '');\n          } else if (typeof val === 'string' || typeof val === 'number') {\n            this.setAttribute(hyphenate(key), val + '');\n          } else if (!val) {\n            this.removeAttribute(hyphenate(key));\n          }\n        }\n      }\n    }\n  }, {\n    key: \"_update\",\n    value: function _update() {\n      render(this._createVNode(), this.shadowRoot);\n    }\n  }, {\n    key: \"_createVNode\",\n    value: function _createVNode() {\n      var _this4 = this;\n      var vnode = createVNode(this._def, extend({}, this._props));\n      if (!this._instance) {\n        vnode.ce = function (instance) {\n          _this4._instance = instance;\n          instance.isCE = true;\n          // HMR\n          if (process.env.NODE_ENV !== 'production') {\n            instance.ceReload = function (newStyles) {\n              // always reset styles\n              if (_this4._styles) {\n                _this4._styles.forEach(function (s) {\n                  return _this4.shadowRoot.removeChild(s);\n                });\n                _this4._styles.length = 0;\n              }\n              _this4._applyStyles(newStyles);\n              // if this is an async component, ceReload is called from the inner\n              // component so no need to reload the async wrapper\n              if (!_this4._def.__asyncLoader) {\n                // reload\n                _this4._instance = null;\n                _this4._update();\n              }\n            };\n          }\n          // intercept emit\n          instance.emit = function (event) {\n            for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key3 = 1; _key3 < _len; _key3++) {\n              args[_key3 - 1] = arguments[_key3];\n            }\n            _this4.dispatchEvent(new CustomEvent(event, {\n              detail: args\n            }));\n          };\n          // locate nearest Vue custom element parent for provide/inject\n          var parent = _this4;\n          while (parent = parent && (parent.parentNode || parent.host)) {\n            if (parent instanceof VueElement) {\n              instance.parent = parent._instance;\n              break;\n            }\n          }\n        };\n      }\n      return vnode;\n    }\n  }, {\n    key: \"_applyStyles\",\n    value: function _applyStyles(styles) {\n      var _this5 = this;\n      if (styles) {\n        styles.forEach(function (css) {\n          var s = document.createElement('style');\n          s.textContent = css;\n          _this5.shadowRoot.appendChild(s);\n          // record for HMR\n          if (process.env.NODE_ENV !== 'production') {\n            (_this5._styles || (_this5._styles = [])).push(s);\n          }\n        });\n      }\n    }\n  }]);\n  return VueElement;\n}(BaseClass);\nfunction useCssModule() {\n  var name = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : '$style';\n  /* istanbul ignore else */\n  {\n    var instance = getCurrentInstance();\n    if (!instance) {\n      process.env.NODE_ENV !== 'production' && warn(\"useCssModule must be called inside setup()\");\n      return EMPTY_OBJ;\n    }\n    var modules = instance.type.__cssModules;\n    if (!modules) {\n      process.env.NODE_ENV !== 'production' && warn(\"Current instance does not have CSS modules injected.\");\n      return EMPTY_OBJ;\n    }\n    var mod = modules[name];\n    if (!mod) {\n      process.env.NODE_ENV !== 'production' && warn(\"Current instance does not have CSS module named \\\"\".concat(name, \"\\\".\"));\n      return EMPTY_OBJ;\n    }\n    return mod;\n  }\n}\n\n/**\r\n * Runtime helper for SFC's CSS variable injection feature.\r\n * @private\r\n */\nfunction useCssVars(getter) {\n  var instance = getCurrentInstance();\n  /* istanbul ignore next */\n  if (!instance) {\n    process.env.NODE_ENV !== 'production' && warn(\"useCssVars is called without current active component instance.\");\n    return;\n  }\n  var setVars = function setVars() {\n    return setVarsOnVNode(instance.subTree, getter(instance.proxy));\n  };\n  watchPostEffect(setVars);\n  onMounted(function () {\n    var ob = new MutationObserver(setVars);\n    ob.observe(instance.subTree.el.parentNode, {\n      childList: true\n    });\n    onUnmounted(function () {\n      return ob.disconnect();\n    });\n  });\n}\nfunction setVarsOnVNode(vnode, vars) {\n  if (vnode.shapeFlag & 128 /* SUSPENSE */) {\n    var suspense = vnode.suspense;\n    vnode = suspense.activeBranch;\n    if (suspense.pendingBranch && !suspense.isHydrating) {\n      suspense.effects.push(function () {\n        setVarsOnVNode(suspense.activeBranch, vars);\n      });\n    }\n  }\n  // drill down HOCs until it's a non-component vnode\n  while (vnode.component) {\n    vnode = vnode.component.subTree;\n  }\n  if (vnode.shapeFlag & 1 /* ELEMENT */ && vnode.el) {\n    setVarsOnNode(vnode.el, vars);\n  } else if (vnode.type === Fragment) {\n    vnode.children.forEach(function (c) {\n      return setVarsOnVNode(c, vars);\n    });\n  } else if (vnode.type === Static) {\n    var _vnode = vnode,\n      el = _vnode.el,\n      anchor = _vnode.anchor;\n    while (el) {\n      setVarsOnNode(el, vars);\n      if (el === anchor) break;\n      el = el.nextSibling;\n    }\n  }\n}\nfunction setVarsOnNode(el, vars) {\n  if (el.nodeType === 1) {\n    var style = el.style;\n    for (var key in vars) {\n      style.setProperty(\"--\".concat(key), vars[key]);\n    }\n  }\n}\nvar TRANSITION = 'transition';\nvar ANIMATION = 'animation';\n// DOM Transition is a higher-order-component based on the platform-agnostic\n// base Transition component, with DOM-specific logic.\nvar Transition = function Transition(props, _ref) {\n  var slots = _ref.slots;\n  return h(BaseTransition, resolveTransitionProps(props), slots);\n};\nTransition.displayName = 'Transition';\nvar DOMTransitionPropsValidators = {\n  name: String,\n  type: String,\n  css: {\n    type: Boolean,\n    default: true\n  },\n  duration: [String, Number, Object],\n  enterFromClass: String,\n  enterActiveClass: String,\n  enterToClass: String,\n  appearFromClass: String,\n  appearActiveClass: String,\n  appearToClass: String,\n  leaveFromClass: String,\n  leaveActiveClass: String,\n  leaveToClass: String\n};\nvar TransitionPropsValidators = Transition.props = /*#__PURE__*/extend({}, BaseTransition.props, DOMTransitionPropsValidators);\n/**\r\n * #3227 Incoming hooks may be merged into arrays when wrapping Transition\r\n * with custom HOCs.\r\n */\nvar callHook = function callHook(hook) {\n  var args = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : [];\n  if (isArray(hook)) {\n    hook.forEach(function (h) {\n      return h.apply(void 0, _toConsumableArray(args));\n    });\n  } else if (hook) {\n    hook.apply(void 0, _toConsumableArray(args));\n  }\n};\n/**\r\n * Check if a hook expects a callback (2nd arg), which means the user\r\n * intends to explicitly control the end of the transition.\r\n */\nvar hasExplicitCallback = function hasExplicitCallback(hook) {\n  return hook ? isArray(hook) ? hook.some(function (h) {\n    return h.length > 1;\n  }) : hook.length > 1 : false;\n};\nfunction resolveTransitionProps(rawProps) {\n  var baseProps = {};\n  for (var key in rawProps) {\n    if (!(key in DOMTransitionPropsValidators)) {\n      baseProps[key] = rawProps[key];\n    }\n  }\n  if (rawProps.css === false) {\n    return baseProps;\n  }\n  var _rawProps$name = rawProps.name,\n    name = _rawProps$name === void 0 ? 'v' : _rawProps$name,\n    type = rawProps.type,\n    duration = rawProps.duration,\n    _rawProps$enterFromCl = rawProps.enterFromClass,\n    enterFromClass = _rawProps$enterFromCl === void 0 ? \"\".concat(name, \"-enter-from\") : _rawProps$enterFromCl,\n    _rawProps$enterActive = rawProps.enterActiveClass,\n    enterActiveClass = _rawProps$enterActive === void 0 ? \"\".concat(name, \"-enter-active\") : _rawProps$enterActive,\n    _rawProps$enterToClas = rawProps.enterToClass,\n    enterToClass = _rawProps$enterToClas === void 0 ? \"\".concat(name, \"-enter-to\") : _rawProps$enterToClas,\n    _rawProps$appearFromC = rawProps.appearFromClass,\n    appearFromClass = _rawProps$appearFromC === void 0 ? enterFromClass : _rawProps$appearFromC,\n    _rawProps$appearActiv = rawProps.appearActiveClass,\n    appearActiveClass = _rawProps$appearActiv === void 0 ? enterActiveClass : _rawProps$appearActiv,\n    _rawProps$appearToCla = rawProps.appearToClass,\n    appearToClass = _rawProps$appearToCla === void 0 ? enterToClass : _rawProps$appearToCla,\n    _rawProps$leaveFromCl = rawProps.leaveFromClass,\n    leaveFromClass = _rawProps$leaveFromCl === void 0 ? \"\".concat(name, \"-leave-from\") : _rawProps$leaveFromCl,\n    _rawProps$leaveActive = rawProps.leaveActiveClass,\n    leaveActiveClass = _rawProps$leaveActive === void 0 ? \"\".concat(name, \"-leave-active\") : _rawProps$leaveActive,\n    _rawProps$leaveToClas = rawProps.leaveToClass,\n    leaveToClass = _rawProps$leaveToClas === void 0 ? \"\".concat(name, \"-leave-to\") : _rawProps$leaveToClas;\n  var durations = normalizeDuration(duration);\n  var enterDuration = durations && durations[0];\n  var leaveDuration = durations && durations[1];\n  var _onBeforeEnter = baseProps.onBeforeEnter,\n    onEnter = baseProps.onEnter,\n    _onEnterCancelled = baseProps.onEnterCancelled,\n    _onLeave = baseProps.onLeave,\n    _onLeaveCancelled = baseProps.onLeaveCancelled,\n    _baseProps$onBeforeAp = baseProps.onBeforeAppear,\n    _onBeforeAppear = _baseProps$onBeforeAp === void 0 ? _onBeforeEnter : _baseProps$onBeforeAp,\n    _baseProps$onAppear = baseProps.onAppear,\n    onAppear = _baseProps$onAppear === void 0 ? onEnter : _baseProps$onAppear,\n    _baseProps$onAppearCa = baseProps.onAppearCancelled,\n    _onAppearCancelled = _baseProps$onAppearCa === void 0 ? _onEnterCancelled : _baseProps$onAppearCa;\n  var finishEnter = function finishEnter(el, isAppear, done) {\n    removeTransitionClass(el, isAppear ? appearToClass : enterToClass);\n    removeTransitionClass(el, isAppear ? appearActiveClass : enterActiveClass);\n    done && done();\n  };\n  var finishLeave = function finishLeave(el, done) {\n    removeTransitionClass(el, leaveToClass);\n    removeTransitionClass(el, leaveActiveClass);\n    done && done();\n  };\n  var makeEnterHook = function makeEnterHook(isAppear) {\n    return function (el, done) {\n      var hook = isAppear ? onAppear : onEnter;\n      var resolve = function resolve() {\n        return finishEnter(el, isAppear, done);\n      };\n      callHook(hook, [el, resolve]);\n      nextFrame(function () {\n        removeTransitionClass(el, isAppear ? appearFromClass : enterFromClass);\n        addTransitionClass(el, isAppear ? appearToClass : enterToClass);\n        if (!hasExplicitCallback(hook)) {\n          whenTransitionEnds(el, type, enterDuration, resolve);\n        }\n      });\n    };\n  };\n  return extend(baseProps, {\n    onBeforeEnter: function onBeforeEnter(el) {\n      callHook(_onBeforeEnter, [el]);\n      addTransitionClass(el, enterFromClass);\n      addTransitionClass(el, enterActiveClass);\n    },\n    onBeforeAppear: function onBeforeAppear(el) {\n      callHook(_onBeforeAppear, [el]);\n      addTransitionClass(el, appearFromClass);\n      addTransitionClass(el, appearActiveClass);\n    },\n    onEnter: makeEnterHook(false),\n    onAppear: makeEnterHook(true),\n    onLeave: function onLeave(el, done) {\n      var resolve = function resolve() {\n        return finishLeave(el, done);\n      };\n      addTransitionClass(el, leaveFromClass);\n      // force reflow so *-leave-from classes immediately take effect (#2593)\n      forceReflow();\n      addTransitionClass(el, leaveActiveClass);\n      nextFrame(function () {\n        removeTransitionClass(el, leaveFromClass);\n        addTransitionClass(el, leaveToClass);\n        if (!hasExplicitCallback(_onLeave)) {\n          whenTransitionEnds(el, type, leaveDuration, resolve);\n        }\n      });\n      callHook(_onLeave, [el, resolve]);\n    },\n    onEnterCancelled: function onEnterCancelled(el) {\n      finishEnter(el, false);\n      callHook(_onEnterCancelled, [el]);\n    },\n    onAppearCancelled: function onAppearCancelled(el) {\n      finishEnter(el, true);\n      callHook(_onAppearCancelled, [el]);\n    },\n    onLeaveCancelled: function onLeaveCancelled(el) {\n      finishLeave(el);\n      callHook(_onLeaveCancelled, [el]);\n    }\n  });\n}\nfunction normalizeDuration(duration) {\n  if (duration == null) {\n    return null;\n  } else if (isObject(duration)) {\n    return [NumberOf(duration.enter), NumberOf(duration.leave)];\n  } else {\n    var n = NumberOf(duration);\n    return [n, n];\n  }\n}\nfunction NumberOf(val) {\n  var res = toNumber(val);\n  if (process.env.NODE_ENV !== 'production') validateDuration(res);\n  return res;\n}\nfunction validateDuration(val) {\n  if (typeof val !== 'number') {\n    warn(\"<transition> explicit duration is not a valid number - \" + \"got \".concat(JSON.stringify(val), \".\"));\n  } else if (isNaN(val)) {\n    warn(\"<transition> explicit duration is NaN - \" + 'the duration expression might be incorrect.');\n  }\n}\nfunction addTransitionClass(el, cls) {\n  cls.split(/\\s+/).forEach(function (c) {\n    return c && el.classList.add(c);\n  });\n  (el._vtc || (el._vtc = new Set())).add(cls);\n}\nfunction removeTransitionClass(el, cls) {\n  cls.split(/\\s+/).forEach(function (c) {\n    return c && el.classList.remove(c);\n  });\n  var _vtc = el._vtc;\n  if (_vtc) {\n    _vtc.delete(cls);\n    if (!_vtc.size) {\n      el._vtc = undefined;\n    }\n  }\n}\nfunction nextFrame(cb) {\n  requestAnimationFrame(function () {\n    requestAnimationFrame(cb);\n  });\n}\nvar endId = 0;\nfunction whenTransitionEnds(el, expectedType, explicitTimeout, resolve) {\n  var id = el._endId = ++endId;\n  var resolveIfNotStale = function resolveIfNotStale() {\n    if (id === el._endId) {\n      resolve();\n    }\n  };\n  if (explicitTimeout) {\n    return setTimeout(resolveIfNotStale, explicitTimeout);\n  }\n  var _getTransitionInfo = getTransitionInfo(el, expectedType),\n    type = _getTransitionInfo.type,\n    timeout = _getTransitionInfo.timeout,\n    propCount = _getTransitionInfo.propCount;\n  if (!type) {\n    return resolve();\n  }\n  var endEvent = type + 'end';\n  var ended = 0;\n  var end = function end() {\n    el.removeEventListener(endEvent, onEnd);\n    resolveIfNotStale();\n  };\n  var onEnd = function onEnd(e) {\n    if (e.target === el && ++ended >= propCount) {\n      end();\n    }\n  };\n  setTimeout(function () {\n    if (ended < propCount) {\n      end();\n    }\n  }, timeout + 1);\n  el.addEventListener(endEvent, onEnd);\n}\nfunction getTransitionInfo(el, expectedType) {\n  var styles = window.getComputedStyle(el);\n  // JSDOM may return undefined for transition properties\n  var getStyleProperties = function getStyleProperties(key) {\n    return (styles[key] || '').split(', ');\n  };\n  var transitionDelays = getStyleProperties(TRANSITION + 'Delay');\n  var transitionDurations = getStyleProperties(TRANSITION + 'Duration');\n  var transitionTimeout = getTimeout(transitionDelays, transitionDurations);\n  var animationDelays = getStyleProperties(ANIMATION + 'Delay');\n  var animationDurations = getStyleProperties(ANIMATION + 'Duration');\n  var animationTimeout = getTimeout(animationDelays, animationDurations);\n  var type = null;\n  var timeout = 0;\n  var propCount = 0;\n  /* istanbul ignore if */\n  if (expectedType === TRANSITION) {\n    if (transitionTimeout > 0) {\n      type = TRANSITION;\n      timeout = transitionTimeout;\n      propCount = transitionDurations.length;\n    }\n  } else if (expectedType === ANIMATION) {\n    if (animationTimeout > 0) {\n      type = ANIMATION;\n      timeout = animationTimeout;\n      propCount = animationDurations.length;\n    }\n  } else {\n    timeout = Math.max(transitionTimeout, animationTimeout);\n    type = timeout > 0 ? transitionTimeout > animationTimeout ? TRANSITION : ANIMATION : null;\n    propCount = type ? type === TRANSITION ? transitionDurations.length : animationDurations.length : 0;\n  }\n  var hasTransform = type === TRANSITION && /\\b(transform|all)(,|$)/.test(styles[TRANSITION + 'Property']);\n  return {\n    type: type,\n    timeout: timeout,\n    propCount: propCount,\n    hasTransform: hasTransform\n  };\n}\nfunction getTimeout(delays, durations) {\n  while (delays.length < durations.length) {\n    delays = delays.concat(delays);\n  }\n  return Math.max.apply(Math, _toConsumableArray(durations.map(function (d, i) {\n    return toMs(d) + toMs(delays[i]);\n  })));\n}\n// Old versions of Chromium (below 61.0.3163.100) formats floating pointer\n// numbers in a locale-dependent way, using a comma instead of a dot.\n// If comma is not replaced with a dot, the input will be rounded down\n// (i.e. acting as a floor function) causing unexpected behaviors\nfunction toMs(s) {\n  return Number(s.slice(0, -1).replace(',', '.')) * 1000;\n}\n// synchronously force layout to put elements into a certain state\nfunction forceReflow() {\n  return document.body.offsetHeight;\n}\nvar positionMap = new WeakMap();\nvar newPositionMap = new WeakMap();\nvar TransitionGroupImpl = {\n  name: 'TransitionGroup',\n  props: /*#__PURE__*/extend({}, TransitionPropsValidators, {\n    tag: String,\n    moveClass: String\n  }),\n  setup: function setup(props, _ref2) {\n    var slots = _ref2.slots;\n    var instance = getCurrentInstance();\n    var state = useTransitionState();\n    var prevChildren;\n    var children;\n    onUpdated(function () {\n      // children is guaranteed to exist after initial render\n      if (!prevChildren.length) {\n        return;\n      }\n      var moveClass = props.moveClass || \"\".concat(props.name || 'v', \"-move\");\n      if (!hasCSSTransform(prevChildren[0].el, instance.vnode.el, moveClass)) {\n        return;\n      }\n      // we divide the work into three loops to avoid mixing DOM reads and writes\n      // in each iteration - which helps prevent layout thrashing.\n      prevChildren.forEach(callPendingCbs);\n      prevChildren.forEach(recordPosition);\n      var movedChildren = prevChildren.filter(applyTranslation);\n      // force reflow to put everything in position\n      forceReflow();\n      movedChildren.forEach(function (c) {\n        var el = c.el;\n        var style = el.style;\n        addTransitionClass(el, moveClass);\n        style.transform = style.webkitTransform = style.transitionDuration = '';\n        var cb = el._moveCb = function (e) {\n          if (e && e.target !== el) {\n            return;\n          }\n          if (!e || /transform$/.test(e.propertyName)) {\n            el.removeEventListener('transitionend', cb);\n            el._moveCb = null;\n            removeTransitionClass(el, moveClass);\n          }\n        };\n        el.addEventListener('transitionend', cb);\n      });\n    });\n    return function () {\n      var rawProps = toRaw(props);\n      var cssTransitionProps = resolveTransitionProps(rawProps);\n      var tag = rawProps.tag || Fragment;\n      prevChildren = children;\n      children = slots.default ? getTransitionRawChildren(slots.default()) : [];\n      for (var i = 0; i < children.length; i++) {\n        var child = children[i];\n        if (child.key != null) {\n          setTransitionHooks(child, resolveTransitionHooks(child, cssTransitionProps, state, instance));\n        } else if (process.env.NODE_ENV !== 'production') {\n          warn(\"<TransitionGroup> children must be keyed.\");\n        }\n      }\n      if (prevChildren) {\n        for (var _i3 = 0; _i3 < prevChildren.length; _i3++) {\n          var _child = prevChildren[_i3];\n          setTransitionHooks(_child, resolveTransitionHooks(_child, cssTransitionProps, state, instance));\n          positionMap.set(_child, _child.el.getBoundingClientRect());\n        }\n      }\n      return createVNode(tag, null, children);\n    };\n  }\n};\nvar TransitionGroup = TransitionGroupImpl;\nfunction callPendingCbs(c) {\n  var el = c.el;\n  if (el._moveCb) {\n    el._moveCb();\n  }\n  if (el._enterCb) {\n    el._enterCb();\n  }\n}\nfunction recordPosition(c) {\n  newPositionMap.set(c, c.el.getBoundingClientRect());\n}\nfunction applyTranslation(c) {\n  var oldPos = positionMap.get(c);\n  var newPos = newPositionMap.get(c);\n  var dx = oldPos.left - newPos.left;\n  var dy = oldPos.top - newPos.top;\n  if (dx || dy) {\n    var s = c.el.style;\n    s.transform = s.webkitTransform = \"translate(\".concat(dx, \"px,\").concat(dy, \"px)\");\n    s.transitionDuration = '0s';\n    return c;\n  }\n}\nfunction hasCSSTransform(el, root, moveClass) {\n  // Detect whether an element with the move class applied has\n  // CSS transitions. Since the element may be inside an entering\n  // transition at this very moment, we make a clone of it and remove\n  // all other transition classes applied to ensure only the move class\n  // is applied.\n  var clone = el.cloneNode();\n  if (el._vtc) {\n    el._vtc.forEach(function (cls) {\n      cls.split(/\\s+/).forEach(function (c) {\n        return c && clone.classList.remove(c);\n      });\n    });\n  }\n  moveClass.split(/\\s+/).forEach(function (c) {\n    return c && clone.classList.add(c);\n  });\n  clone.style.display = 'none';\n  var container = root.nodeType === 1 ? root : root.parentNode;\n  container.appendChild(clone);\n  var _getTransitionInfo2 = getTransitionInfo(clone),\n    hasTransform = _getTransitionInfo2.hasTransform;\n  container.removeChild(clone);\n  return hasTransform;\n}\nvar getModelAssigner = function getModelAssigner(vnode) {\n  var fn = vnode.props['onUpdate:modelValue'];\n  return isArray(fn) ? function (value) {\n    return invokeArrayFns(fn, value);\n  } : fn;\n};\nfunction onCompositionStart(e) {\n  e.target.composing = true;\n}\nfunction onCompositionEnd(e) {\n  var target = e.target;\n  if (target.composing) {\n    target.composing = false;\n    trigger(target, 'input');\n  }\n}\nfunction trigger(el, type) {\n  var e = document.createEvent('HTMLEvents');\n  e.initEvent(type, true, true);\n  el.dispatchEvent(e);\n}\n// We are exporting the v-model runtime directly as vnode hooks so that it can\n// be tree-shaken in case v-model is never used.\nvar vModelText = {\n  created: function created(el, _ref3, vnode) {\n    var _ref3$modifiers = _ref3.modifiers,\n      lazy = _ref3$modifiers.lazy,\n      trim = _ref3$modifiers.trim,\n      number = _ref3$modifiers.number;\n    el._assign = getModelAssigner(vnode);\n    var castToNumber = number || vnode.props && vnode.props.type === 'number';\n    addEventListener(el, lazy ? 'change' : 'input', function (e) {\n      if (e.target.composing) return;\n      var domValue = el.value;\n      if (trim) {\n        domValue = domValue.trim();\n      } else if (castToNumber) {\n        domValue = toNumber(domValue);\n      }\n      el._assign(domValue);\n    });\n    if (trim) {\n      addEventListener(el, 'change', function () {\n        el.value = el.value.trim();\n      });\n    }\n    if (!lazy) {\n      addEventListener(el, 'compositionstart', onCompositionStart);\n      addEventListener(el, 'compositionend', onCompositionEnd);\n      // Safari < 10.2 & UIWebView doesn't fire compositionend when\n      // switching focus before confirming composition choice\n      // this also fixes the issue where some browsers e.g. iOS Chrome\n      // fires \"change\" instead of \"input\" on autocomplete.\n      addEventListener(el, 'change', onCompositionEnd);\n    }\n  },\n  // set value on mounted so it's after min/max for type=\"range\"\n  mounted: function mounted(el, _ref4) {\n    var value = _ref4.value;\n    el.value = value == null ? '' : value;\n  },\n  beforeUpdate: function beforeUpdate(el, _ref5, vnode) {\n    var value = _ref5.value,\n      _ref5$modifiers = _ref5.modifiers,\n      lazy = _ref5$modifiers.lazy,\n      trim = _ref5$modifiers.trim,\n      number = _ref5$modifiers.number;\n    el._assign = getModelAssigner(vnode);\n    // avoid clearing unresolved text. #2302\n    if (el.composing) return;\n    if (document.activeElement === el) {\n      if (lazy) {\n        return;\n      }\n      if (trim && el.value.trim() === value) {\n        return;\n      }\n      if ((number || el.type === 'number') && toNumber(el.value) === value) {\n        return;\n      }\n    }\n    var newValue = value == null ? '' : value;\n    if (el.value !== newValue) {\n      el.value = newValue;\n    }\n  }\n};\nvar vModelCheckbox = {\n  // #4096 array checkboxes need to be deep traversed\n  deep: true,\n  created: function created(el, _, vnode) {\n    el._assign = getModelAssigner(vnode);\n    addEventListener(el, 'change', function () {\n      var modelValue = el._modelValue;\n      var elementValue = getValue(el);\n      var checked = el.checked;\n      var assign = el._assign;\n      if (isArray(modelValue)) {\n        var index = looseIndexOf(modelValue, elementValue);\n        var found = index !== -1;\n        if (checked && !found) {\n          assign(modelValue.concat(elementValue));\n        } else if (!checked && found) {\n          var filtered = _toConsumableArray(modelValue);\n          filtered.splice(index, 1);\n          assign(filtered);\n        }\n      } else if (isSet(modelValue)) {\n        var cloned = new Set(modelValue);\n        if (checked) {\n          cloned.add(elementValue);\n        } else {\n          cloned.delete(elementValue);\n        }\n        assign(cloned);\n      } else {\n        assign(getCheckboxValue(el, checked));\n      }\n    });\n  },\n  // set initial checked on mount to wait for true-value/false-value\n  mounted: setChecked,\n  beforeUpdate: function beforeUpdate(el, binding, vnode) {\n    el._assign = getModelAssigner(vnode);\n    setChecked(el, binding, vnode);\n  }\n};\nfunction setChecked(el, _ref6, vnode) {\n  var value = _ref6.value,\n    oldValue = _ref6.oldValue;\n  el._modelValue = value;\n  if (isArray(value)) {\n    el.checked = looseIndexOf(value, vnode.props.value) > -1;\n  } else if (isSet(value)) {\n    el.checked = value.has(vnode.props.value);\n  } else if (value !== oldValue) {\n    el.checked = looseEqual(value, getCheckboxValue(el, true));\n  }\n}\nvar vModelRadio = {\n  created: function created(el, _ref7, vnode) {\n    var value = _ref7.value;\n    el.checked = looseEqual(value, vnode.props.value);\n    el._assign = getModelAssigner(vnode);\n    addEventListener(el, 'change', function () {\n      el._assign(getValue(el));\n    });\n  },\n  beforeUpdate: function beforeUpdate(el, _ref8, vnode) {\n    var value = _ref8.value,\n      oldValue = _ref8.oldValue;\n    el._assign = getModelAssigner(vnode);\n    if (value !== oldValue) {\n      el.checked = looseEqual(value, vnode.props.value);\n    }\n  }\n};\nvar vModelSelect = {\n  // <select multiple> value need to be deep traversed\n  deep: true,\n  created: function created(el, _ref9, vnode) {\n    var value = _ref9.value,\n      number = _ref9.modifiers.number;\n    var isSetModel = isSet(value);\n    addEventListener(el, 'change', function () {\n      var selectedVal = Array.prototype.filter.call(el.options, function (o) {\n        return o.selected;\n      }).map(function (o) {\n        return number ? toNumber(getValue(o)) : getValue(o);\n      });\n      el._assign(el.multiple ? isSetModel ? new Set(selectedVal) : selectedVal : selectedVal[0]);\n    });\n    el._assign = getModelAssigner(vnode);\n  },\n  // set value in mounted & updated because <select> relies on its children\n  // <option>s.\n  mounted: function mounted(el, _ref10) {\n    var value = _ref10.value;\n    setSelected(el, value);\n  },\n  beforeUpdate: function beforeUpdate(el, _binding, vnode) {\n    el._assign = getModelAssigner(vnode);\n  },\n  updated: function updated(el, _ref11) {\n    var value = _ref11.value;\n    setSelected(el, value);\n  }\n};\nfunction setSelected(el, value) {\n  var isMultiple = el.multiple;\n  if (isMultiple && !isArray(value) && !isSet(value)) {\n    process.env.NODE_ENV !== 'production' && warn(\"<select multiple v-model> expects an Array or Set value for its binding, \" + \"but got \".concat(Object.prototype.toString.call(value).slice(8, -1), \".\"));\n    return;\n  }\n  for (var i = 0, l = el.options.length; i < l; i++) {\n    var option = el.options[i];\n    var optionValue = getValue(option);\n    if (isMultiple) {\n      if (isArray(value)) {\n        option.selected = looseIndexOf(value, optionValue) > -1;\n      } else {\n        option.selected = value.has(optionValue);\n      }\n    } else {\n      if (looseEqual(getValue(option), value)) {\n        if (el.selectedIndex !== i) el.selectedIndex = i;\n        return;\n      }\n    }\n  }\n  if (!isMultiple && el.selectedIndex !== -1) {\n    el.selectedIndex = -1;\n  }\n}\n// retrieve raw value set via :value bindings\nfunction getValue(el) {\n  return '_value' in el ? el._value : el.value;\n}\n// retrieve raw value for true-value and false-value set via :true-value or :false-value bindings\nfunction getCheckboxValue(el, checked) {\n  var key = checked ? '_trueValue' : '_falseValue';\n  return key in el ? el[key] : checked;\n}\nvar vModelDynamic = {\n  created: function created(el, binding, vnode) {\n    callModelHook(el, binding, vnode, null, 'created');\n  },\n  mounted: function mounted(el, binding, vnode) {\n    callModelHook(el, binding, vnode, null, 'mounted');\n  },\n  beforeUpdate: function beforeUpdate(el, binding, vnode, prevVNode) {\n    callModelHook(el, binding, vnode, prevVNode, 'beforeUpdate');\n  },\n  updated: function updated(el, binding, vnode, prevVNode) {\n    callModelHook(el, binding, vnode, prevVNode, 'updated');\n  }\n};\nfunction callModelHook(el, binding, vnode, prevVNode, hook) {\n  var modelToUse;\n  switch (el.tagName) {\n    case 'SELECT':\n      modelToUse = vModelSelect;\n      break;\n    case 'TEXTAREA':\n      modelToUse = vModelText;\n      break;\n    default:\n      switch (vnode.props && vnode.props.type) {\n        case 'checkbox':\n          modelToUse = vModelCheckbox;\n          break;\n        case 'radio':\n          modelToUse = vModelRadio;\n          break;\n        default:\n          modelToUse = vModelText;\n      }\n  }\n  var fn = modelToUse[hook];\n  fn && fn(el, binding, vnode, prevVNode);\n}\n// SSR vnode transforms, only used when user includes client-oriented render\n// function in SSR\nfunction initVModelForSSR() {\n  vModelText.getSSRProps = function (_ref12) {\n    var value = _ref12.value;\n    return {\n      value: value\n    };\n  };\n  vModelRadio.getSSRProps = function (_ref13, vnode) {\n    var value = _ref13.value;\n    if (vnode.props && looseEqual(vnode.props.value, value)) {\n      return {\n        checked: true\n      };\n    }\n  };\n  vModelCheckbox.getSSRProps = function (_ref14, vnode) {\n    var value = _ref14.value;\n    if (isArray(value)) {\n      if (vnode.props && looseIndexOf(value, vnode.props.value) > -1) {\n        return {\n          checked: true\n        };\n      }\n    } else if (isSet(value)) {\n      if (vnode.props && value.has(vnode.props.value)) {\n        return {\n          checked: true\n        };\n      }\n    } else if (value) {\n      return {\n        checked: true\n      };\n    }\n  };\n}\nvar systemModifiers = ['ctrl', 'shift', 'alt', 'meta'];\nvar modifierGuards = {\n  stop: function stop(e) {\n    return e.stopPropagation();\n  },\n  prevent: function prevent(e) {\n    return e.preventDefault();\n  },\n  self: function self(e) {\n    return e.target !== e.currentTarget;\n  },\n  ctrl: function ctrl(e) {\n    return !e.ctrlKey;\n  },\n  shift: function shift(e) {\n    return !e.shiftKey;\n  },\n  alt: function alt(e) {\n    return !e.altKey;\n  },\n  meta: function meta(e) {\n    return !e.metaKey;\n  },\n  left: function left(e) {\n    return 'button' in e && e.button !== 0;\n  },\n  middle: function middle(e) {\n    return 'button' in e && e.button !== 1;\n  },\n  right: function right(e) {\n    return 'button' in e && e.button !== 2;\n  },\n  exact: function exact(e, modifiers) {\n    return systemModifiers.some(function (m) {\n      return e[\"\".concat(m, \"Key\")] && !modifiers.includes(m);\n    });\n  }\n};\n/**\r\n * @private\r\n */\nvar withModifiers = function withModifiers(fn, modifiers) {\n  return function (event) {\n    for (var i = 0; i < modifiers.length; i++) {\n      var guard = modifierGuards[modifiers[i]];\n      if (guard && guard(event, modifiers)) return;\n    }\n    for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key4 = 1; _key4 < _len2; _key4++) {\n      args[_key4 - 1] = arguments[_key4];\n    }\n    return fn.apply(void 0, [event].concat(args));\n  };\n};\n// Kept for 2.x compat.\n// Note: IE11 compat for `spacebar` and `del` is removed for now.\nvar keyNames = {\n  esc: 'escape',\n  space: ' ',\n  up: 'arrow-up',\n  left: 'arrow-left',\n  right: 'arrow-right',\n  down: 'arrow-down',\n  delete: 'backspace'\n};\n/**\r\n * @private\r\n */\nvar withKeys = function withKeys(fn, modifiers) {\n  return function (event) {\n    if (!('key' in event)) {\n      return;\n    }\n    var eventKey = hyphenate(event.key);\n    if (modifiers.some(function (k) {\n      return k === eventKey || keyNames[k] === eventKey;\n    })) {\n      return fn(event);\n    }\n  };\n};\nvar vShow = {\n  beforeMount: function beforeMount(el, _ref15, _ref16) {\n    var value = _ref15.value;\n    var transition = _ref16.transition;\n    el._vod = el.style.display === 'none' ? '' : el.style.display;\n    if (transition && value) {\n      transition.beforeEnter(el);\n    } else {\n      setDisplay(el, value);\n    }\n  },\n  mounted: function mounted(el, _ref17, _ref18) {\n    var value = _ref17.value;\n    var transition = _ref18.transition;\n    if (transition && value) {\n      transition.enter(el);\n    }\n  },\n  updated: function updated(el, _ref19, _ref20) {\n    var value = _ref19.value,\n      oldValue = _ref19.oldValue;\n    var transition = _ref20.transition;\n    if (!value === !oldValue) return;\n    if (transition) {\n      if (value) {\n        transition.beforeEnter(el);\n        setDisplay(el, true);\n        transition.enter(el);\n      } else {\n        transition.leave(el, function () {\n          setDisplay(el, false);\n        });\n      }\n    } else {\n      setDisplay(el, value);\n    }\n  },\n  beforeUnmount: function beforeUnmount(el, _ref21) {\n    var value = _ref21.value;\n    setDisplay(el, value);\n  }\n};\nfunction setDisplay(el, value) {\n  el.style.display = value ? el._vod : 'none';\n}\n// SSR vnode transforms, only used when user includes client-oriented render\n// function in SSR\nfunction initVShowForSSR() {\n  vShow.getSSRProps = function (_ref22) {\n    var value = _ref22.value;\n    if (!value) {\n      return {\n        style: {\n          display: 'none'\n        }\n      };\n    }\n  };\n}\nvar rendererOptions = extend({\n  patchProp: patchProp\n}, nodeOps);\n// lazy create the renderer - this makes core renderer logic tree-shakable\n// in case the user only imports reactivity utilities from Vue.\nvar renderer;\nvar enabledHydration = false;\nfunction ensureRenderer() {\n  return renderer || (renderer = createRenderer(rendererOptions));\n}\nfunction ensureHydrationRenderer() {\n  renderer = enabledHydration ? renderer : createHydrationRenderer(rendererOptions);\n  enabledHydration = true;\n  return renderer;\n}\n// use explicit type casts here to avoid import() calls in rolled-up d.ts\nvar render = function render() {\n  var _ensureRenderer;\n  (_ensureRenderer = ensureRenderer()).render.apply(_ensureRenderer, arguments);\n};\nvar hydrate = function hydrate() {\n  var _ensureHydrationRende;\n  (_ensureHydrationRende = ensureHydrationRenderer()).hydrate.apply(_ensureHydrationRende, arguments);\n};\nvar createApp = function createApp() {\n  var _ensureRenderer2;\n  var app = (_ensureRenderer2 = ensureRenderer()).createApp.apply(_ensureRenderer2, arguments);\n  if (process.env.NODE_ENV !== 'production') {\n    injectNativeTagCheck(app);\n    injectCompilerOptionsCheck(app);\n  }\n  var mount = app.mount;\n  app.mount = function (containerOrSelector) {\n    var container = normalizeContainer(containerOrSelector);\n    if (!container) return;\n    var component = app._component;\n    if (!isFunction(component) && !component.render && !component.template) {\n      // __UNSAFE__\n      // Reason: potential execution of JS expressions in in-DOM template.\n      // The user must make sure the in-DOM template is trusted. If it's\n      // rendered by the server, the template should not contain any user data.\n      component.template = container.innerHTML;\n    }\n    // clear content before mounting\n    container.innerHTML = '';\n    var proxy = mount(container, false, container instanceof SVGElement);\n    if (container instanceof Element) {\n      container.removeAttribute('v-cloak');\n      container.setAttribute('data-v-app', '');\n    }\n    return proxy;\n  };\n  return app;\n};\nvar createSSRApp = function createSSRApp() {\n  var _ensureHydrationRende2;\n  var app = (_ensureHydrationRende2 = ensureHydrationRenderer()).createApp.apply(_ensureHydrationRende2, arguments);\n  if (process.env.NODE_ENV !== 'production') {\n    injectNativeTagCheck(app);\n    injectCompilerOptionsCheck(app);\n  }\n  var mount = app.mount;\n  app.mount = function (containerOrSelector) {\n    var container = normalizeContainer(containerOrSelector);\n    if (container) {\n      return mount(container, true, container instanceof SVGElement);\n    }\n  };\n  return app;\n};\nfunction injectNativeTagCheck(app) {\n  // Inject `isNativeTag`\n  // this is used for component name validation (dev only)\n  Object.defineProperty(app.config, 'isNativeTag', {\n    value: function value(tag) {\n      return isHTMLTag(tag) || isSVGTag(tag);\n    },\n    writable: false\n  });\n}\n// dev only\nfunction injectCompilerOptionsCheck(app) {\n  if (isRuntimeOnly()) {\n    var isCustomElement = app.config.isCustomElement;\n    Object.defineProperty(app.config, 'isCustomElement', {\n      get: function get() {\n        return isCustomElement;\n      },\n      set: function set() {\n        warn(\"The `isCustomElement` config option is deprecated. Use \" + \"`compilerOptions.isCustomElement` instead.\");\n      }\n    });\n    var compilerOptions = app.config.compilerOptions;\n    var msg = \"The `compilerOptions` config option is only respected when using \" + \"a build of Vue.js that includes the runtime compiler (aka \\\"full build\\\"). \" + \"Since you are using the runtime-only build, `compilerOptions` \" + \"must be passed to `@vue/compiler-dom` in the build setup instead.\\n\" + \"- For vue-loader: pass it via vue-loader's `compilerOptions` loader option.\\n\" + \"- For vue-cli: see https://cli.vuejs.org/guide/webpack.html#modifying-options-of-a-loader\\n\" + \"- For vite: pass it via @vitejs/plugin-vue options. See https://github.com/vitejs/vite/tree/main/packages/plugin-vue#example-for-passing-options-to-vuecompiler-dom\";\n    Object.defineProperty(app.config, 'compilerOptions', {\n      get: function get() {\n        warn(msg);\n        return compilerOptions;\n      },\n      set: function set() {\n        warn(msg);\n      }\n    });\n  }\n}\nfunction normalizeContainer(container) {\n  if (isString(container)) {\n    var res = document.querySelector(container);\n    if (process.env.NODE_ENV !== 'production' && !res) {\n      warn(\"Failed to mount app: mount target selector \\\"\".concat(container, \"\\\" returned null.\"));\n    }\n    return res;\n  }\n  if (process.env.NODE_ENV !== 'production' && window.ShadowRoot && container instanceof window.ShadowRoot && container.mode === 'closed') {\n    warn(\"mounting on a ShadowRoot with `{mode: \\\"closed\\\"}` may lead to unpredictable bugs\");\n  }\n  return container;\n}\nvar ssrDirectiveInitialized = false;\n/**\r\n * @internal\r\n */\nvar initDirectivesForSSR = function initDirectivesForSSR() {\n  if (!ssrDirectiveInitialized) {\n    ssrDirectiveInitialized = true;\n    initVModelForSSR();\n    initVShowForSSR();\n  }\n};\nexport { Transition, TransitionGroup, VueElement, createApp, createSSRApp, defineCustomElement, defineSSRCustomElement, hydrate, initDirectivesForSSR, render, useCssModule, useCssVars, vModelCheckbox, vModelDynamic, vModelRadio, vModelSelect, vModelText, vShow, withKeys, withModifiers };", "map": {"version": 3, "names": ["camelize", "warn", "callWithAsyncErrorHandling", "defineComponent", "nextTick", "createVNode", "getCurrentInstance", "watchPostEffect", "onMounted", "onUnmounted", "Fragment", "Static", "h", "BaseTransition", "useTransitionState", "onUpdated", "toRaw", "getTransitionRawChildren", "setTransitionHooks", "resolveTransitionHooks", "<PERSON><PERSON><PERSON><PERSON>", "isRuntimeOnly", "createHydrationRenderer", "isString", "isArray", "hyphenate", "capitalize", "isSpecialBooleanAttr", "includeBooleanAttr", "isOn", "isModelListener", "isFunction", "toNumber", "camelize$1", "extend", "EMPTY_OBJ", "isObject", "invokeArrayFns", "looseIndexOf", "isSet", "looseEqual", "isHTMLTag", "isSVGTag", "svgNS", "doc", "document", "staticTemplateCache", "Map", "nodeOps", "insert", "child", "parent", "anchor", "insertBefore", "remove", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "createElement", "tag", "isSVG", "is", "props", "el", "createElementNS", "undefined", "multiple", "setAttribute", "createText", "text", "createTextNode", "createComment", "setText", "node", "nodeValue", "setElementText", "textContent", "nextS<PERSON>ling", "querySelector", "selector", "setScopeId", "id", "cloneNode", "cloned", "_value", "insertStatic<PERSON>ontent", "content", "before", "previousSibling", "<PERSON><PERSON><PERSON><PERSON>", "template", "get", "t", "innerHTML", "concat", "wrapper", "<PERSON><PERSON><PERSON><PERSON>", "append<PERSON><PERSON><PERSON>", "set", "patchClass", "value", "transitionClasses", "_vtc", "_toConsumableArray", "join", "removeAttribute", "className", "patchStyle", "prev", "next", "style", "isCssString", "key", "setStyle", "currentDisplay", "display", "cssText", "importantRE", "name", "val", "for<PERSON>ach", "v", "startsWith", "setProperty", "prefixed", "autoPrefix", "test", "replace", "prefixes", "prefixCache", "rawName", "cached", "i", "length", "xlinkNS", "patchAttr", "instance", "removeAttributeNS", "slice", "setAttributeNS", "isBoolean", "patchDOMProp", "prev<PERSON><PERSON><PERSON><PERSON>", "parentComponent", "parentSuspense", "unmountC<PERSON><PERSON>n", "tagName", "includes", "newValue", "type", "_typeof", "_a", "e", "process", "env", "NODE_ENV", "toLowerCase", "_getNow", "Date", "now", "skipTimestampCheck", "window", "createEvent", "timeStamp", "performance", "ffMatch", "navigator", "userAgent", "match", "Number", "cachedNow", "p", "Promise", "resolve", "reset", "getNow", "then", "addEventListener", "event", "handler", "options", "removeEventListener", "patchEvent", "prevValue", "nextValue", "arguments", "invokers", "_vei", "existingInvoker", "_parseName", "parseName", "_parseName2", "_slicedToArray", "invoker", "createInvoker", "optionsModifierRE", "m", "initialValue", "attached", "patchStopImmediatePropagation", "originalStop", "stopImmediatePropagation", "call", "_stopped", "map", "fn", "nativeOnRE", "patchProp", "shouldSetAsProp", "_trueValue", "_falseValue", "defineCustomElement", "hydate", "Comp", "VueCustomElement", "_VueElement", "_inherits", "_super", "_createSuper", "initialProps", "_classCallCheck", "_createClass", "<PERSON>ue<PERSON>lement", "def", "defineSSRCustomElement", "hydrate", "BaseClass", "HTMLElement", "_class", "_BaseClass", "_super2", "_def", "_this", "_props", "_instance", "_connected", "_resolved", "_numberProps", "shadowRoot", "_createVNode", "attachShadow", "mode", "connectedCallback", "_resolveDef", "disconnectedCallback", "_this2", "render", "_this3", "attributes", "_setAttr", "MutationObserver", "mutations", "_iterator", "_createForOfIteratorHelper", "_step", "s", "n", "done", "attributeName", "err", "f", "observe", "styles", "hasOptions", "raw<PERSON><PERSON>s", "Object", "keys", "numberProps", "opt", "create", "_i2", "_Object$keys", "_setProp", "_iterator2", "_step2", "_loop", "defineProperty", "_getProp", "_applyStyles", "_update", "asyncDef", "__as<PERSON><PERSON><PERSON><PERSON>", "getAttribute", "shouldReflect", "shouldUpdate", "_this4", "vnode", "ce", "isCE", "ceReload", "newStyles", "_styles", "emit", "_len", "args", "Array", "_key3", "dispatchEvent", "CustomEvent", "detail", "host", "_this5", "css", "push", "useCssModule", "modules", "__cssModules", "mod", "useCssVars", "getter", "setVars", "setVarsOnVNode", "subTree", "proxy", "ob", "childList", "disconnect", "vars", "shapeFlag", "suspense", "activeBranch", "pendingBranch", "isHydrating", "effects", "component", "setVarsOnNode", "children", "c", "_vnode", "nodeType", "TRANSITION", "ANIMATION", "Transition", "_ref", "slots", "resolveTransitionProps", "displayName", "DOMTransitionPropsValidators", "String", "Boolean", "default", "duration", "enterFromClass", "enterActiveClass", "enterToClass", "appearFromClass", "appearActiveClass", "appearToClass", "leaveFromClass", "leaveActiveClass", "leaveToClass", "TransitionPropsValidators", "callHook", "hook", "apply", "hasExplicitCallback", "some", "rawProps", "baseProps", "_rawProps$name", "_rawProps$enterFromCl", "_rawProps$enterActive", "_rawProps$enterToClas", "_rawProps$appearFromC", "_rawProps$appearActiv", "_rawProps$appearToCla", "_rawProps$leaveFromCl", "_rawProps$leaveActive", "_rawProps$leaveToClas", "durations", "normalizeDuration", "enterDuration", "leaveDuration", "onBeforeEnter", "onEnter", "onEnterCancelled", "onLeave", "onLeaveCancelled", "_baseProps$onBeforeAp", "onBeforeAppear", "_baseProps$onAppear", "onAppear", "_baseProps$onAppearCa", "onAppearCancelled", "finishEnter", "isAppear", "removeTransitionClass", "finishLeave", "makeEnterHook", "next<PERSON><PERSON><PERSON>", "addTransitionClass", "whenTransitionEnds", "forceReflow", "NumberOf", "enter", "leave", "res", "validateDuration", "JSON", "stringify", "isNaN", "cls", "split", "classList", "add", "Set", "delete", "size", "cb", "requestAnimationFrame", "endId", "expectedType", "explicitTimeout", "_endId", "resolveIfNotStale", "setTimeout", "_getTransitionInfo", "getTransitionInfo", "timeout", "propCount", "endEvent", "ended", "end", "onEnd", "target", "getComputedStyle", "getStyleProperties", "transitionDelays", "transitionDurations", "transitionTimeout", "getTimeout", "animationDelays", "animationDurations", "animationTimeout", "Math", "max", "hasTransform", "delays", "d", "toMs", "body", "offsetHeight", "positionMap", "WeakMap", "newPositionMap", "TransitionGroupImpl", "moveClass", "setup", "_ref2", "state", "hasCSSTransform", "callPendingCbs", "recordPosition", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "filter", "applyTranslation", "transform", "webkitTransform", "transitionDuration", "_moveCb", "propertyName", "cssTransitionProps", "getBoundingClientRect", "TransitionGroup", "_enterCb", "oldPos", "newPos", "dx", "left", "dy", "top", "root", "clone", "container", "_getTransitionInfo2", "getModelAssigner", "onCompositionStart", "composing", "onCompositionEnd", "trigger", "initEvent", "vModelText", "created", "_ref3", "_ref3$modifiers", "modifiers", "lazy", "trim", "number", "_assign", "castToNumber", "domValue", "mounted", "_ref4", "beforeUpdate", "_ref5", "_ref5$modifiers", "activeElement", "vModelCheckbox", "deep", "_", "modelValue", "_modelValue", "elementValue", "getValue", "checked", "assign", "index", "found", "filtered", "splice", "getCheckboxValue", "setChecked", "binding", "_ref6", "oldValue", "has", "vModelRadio", "_ref7", "_ref8", "vModelSelect", "_ref9", "isSetModel", "selected<PERSON><PERSON>", "prototype", "o", "selected", "_ref10", "setSelected", "_binding", "updated", "_ref11", "isMultiple", "toString", "l", "option", "optionValue", "selectedIndex", "vModelDynamic", "callModelHook", "prevVNode", "modelToUse", "initVModelForSSR", "getSSRProps", "_ref12", "_ref13", "_ref14", "systemModifiers", "modifierGuards", "stop", "stopPropagation", "prevent", "preventDefault", "self", "currentTarget", "ctrl", "ctrl<PERSON>ey", "shift", "shift<PERSON>ey", "alt", "altKey", "meta", "metaKey", "button", "middle", "right", "exact", "withModifiers", "guard", "_len2", "_key4", "keyNames", "esc", "space", "up", "down", "<PERSON><PERSON><PERSON><PERSON>", "eventKey", "k", "vShow", "beforeMount", "_ref15", "_ref16", "transition", "_vod", "beforeEnter", "setDisplay", "_ref17", "_ref18", "_ref19", "_ref20", "beforeUnmount", "_ref21", "initVShowForSSR", "_ref22", "rendererOptions", "renderer", "enabledHydration", "<PERSON><PERSON><PERSON><PERSON>", "ensureHydration<PERSON><PERSON><PERSON>", "_ensure<PERSON><PERSON><PERSON>", "_ensureHydrationRende", "createApp", "_ensureRenderer2", "app", "injectNativeTag<PERSON><PERSON><PERSON>", "injectCompilerOptionsCheck", "mount", "containerOrSelector", "normalizeContainer", "_component", "SVGElement", "Element", "createSSRApp", "_ensureHydrationRende2", "config", "writable", "isCustomElement", "compilerOptions", "msg", "ShadowRoot", "ssrDirectiveInitialized", "initDirectivesForSSR"], "sources": ["C:/Users/<USER>/Downloads/Telegram Desktop/vue3_3.0/vue3_3.0/node_modules/@vue/runtime-dom/dist/runtime-dom.esm-bundler.js"], "sourcesContent": ["import { camelize, warn, callWithAsyncErrorHandling, defineComponent, nextTick, createVNode, getCurrentInstance, watchPostEffect, onMounted, onUnmounted, Fragment, Static, h, BaseTransition, useTransitionState, onUpdated, toRaw, getTransitionRawChildren, setTransitionHooks, resolveTransitionHooks, createR<PERSON>er, isRuntimeOnly, createHydrationRenderer } from '@vue/runtime-core';\nexport * from '@vue/runtime-core';\nimport { isString, isArray, hyphenate, capitalize, isSpecialBooleanAttr, includeBooleanAttr, isOn, isModelListener, isFunction, toNumber, camelize as camelize$1, extend, EMPTY_OBJ, isObject, invokeArrayFns, looseIndexOf, isSet, looseEqual, isHTMLTag, isSVGTag } from '@vue/shared';\n\nconst svgNS = 'http://www.w3.org/2000/svg';\r\nconst doc = (typeof document !== 'undefined' ? document : null);\r\nconst staticTemplateCache = new Map();\r\nconst nodeOps = {\r\n    insert: (child, parent, anchor) => {\r\n        parent.insertBefore(child, anchor || null);\r\n    },\r\n    remove: child => {\r\n        const parent = child.parentNode;\r\n        if (parent) {\r\n            parent.removeChild(child);\r\n        }\r\n    },\r\n    createElement: (tag, isSVG, is, props) => {\r\n        const el = isSVG\r\n            ? doc.createElementNS(svgNS, tag)\r\n            : doc.createElement(tag, is ? { is } : undefined);\r\n        if (tag === 'select' && props && props.multiple != null) {\r\n            el.setAttribute('multiple', props.multiple);\r\n        }\r\n        return el;\r\n    },\r\n    createText: text => doc.createTextNode(text),\r\n    createComment: text => doc.createComment(text),\r\n    setText: (node, text) => {\r\n        node.nodeValue = text;\r\n    },\r\n    setElementText: (el, text) => {\r\n        el.textContent = text;\r\n    },\r\n    parentNode: node => node.parentNode,\r\n    nextSibling: node => node.nextSibling,\r\n    querySelector: selector => doc.querySelector(selector),\r\n    setScopeId(el, id) {\r\n        el.setAttribute(id, '');\r\n    },\r\n    cloneNode(el) {\r\n        const cloned = el.cloneNode(true);\r\n        // #3072\r\n        // - in `patchDOMProp`, we store the actual value in the `el._value` property.\r\n        // - normally, elements using `:value` bindings will not be hoisted, but if\r\n        //   the bound value is a constant, e.g. `:value=\"true\"` - they do get\r\n        //   hoisted.\r\n        // - in production, hoisted nodes are cloned when subsequent inserts, but\r\n        //   cloneNode() does not copy the custom property we attached.\r\n        // - This may need to account for other custom DOM properties we attach to\r\n        //   elements in addition to `_value` in the future.\r\n        if (`_value` in el) {\r\n            cloned._value = el._value;\r\n        }\r\n        return cloned;\r\n    },\r\n    // __UNSAFE__\r\n    // Reason: innerHTML.\r\n    // Static content here can only come from compiled templates.\r\n    // As long as the user only uses trusted templates, this is safe.\r\n    insertStaticContent(content, parent, anchor, isSVG) {\r\n        // <parent> before | first ... last | anchor </parent>\r\n        const before = anchor ? anchor.previousSibling : parent.lastChild;\r\n        let template = staticTemplateCache.get(content);\r\n        if (!template) {\r\n            const t = doc.createElement('template');\r\n            t.innerHTML = isSVG ? `<svg>${content}</svg>` : content;\r\n            template = t.content;\r\n            if (isSVG) {\r\n                // remove outer svg wrapper\r\n                const wrapper = template.firstChild;\r\n                while (wrapper.firstChild) {\r\n                    template.appendChild(wrapper.firstChild);\r\n                }\r\n                template.removeChild(wrapper);\r\n            }\r\n            staticTemplateCache.set(content, template);\r\n        }\r\n        parent.insertBefore(template.cloneNode(true), anchor);\r\n        return [\r\n            // first\r\n            before ? before.nextSibling : parent.firstChild,\r\n            // last\r\n            anchor ? anchor.previousSibling : parent.lastChild\r\n        ];\r\n    }\r\n};\n\n// compiler should normalize class + :class bindings on the same element\r\n// into a single binding ['staticClass', dynamic]\r\nfunction patchClass(el, value, isSVG) {\r\n    // directly setting className should be faster than setAttribute in theory\r\n    // if this is an element during a transition, take the temporary transition\r\n    // classes into account.\r\n    const transitionClasses = el._vtc;\r\n    if (transitionClasses) {\r\n        value = (value ? [value, ...transitionClasses] : [...transitionClasses]).join(' ');\r\n    }\r\n    if (value == null) {\r\n        el.removeAttribute('class');\r\n    }\r\n    else if (isSVG) {\r\n        el.setAttribute('class', value);\r\n    }\r\n    else {\r\n        el.className = value;\r\n    }\r\n}\n\nfunction patchStyle(el, prev, next) {\r\n    const style = el.style;\r\n    const isCssString = isString(next);\r\n    if (next && !isCssString) {\r\n        for (const key in next) {\r\n            setStyle(style, key, next[key]);\r\n        }\r\n        if (prev && !isString(prev)) {\r\n            for (const key in prev) {\r\n                if (next[key] == null) {\r\n                    setStyle(style, key, '');\r\n                }\r\n            }\r\n        }\r\n    }\r\n    else {\r\n        const currentDisplay = style.display;\r\n        if (isCssString) {\r\n            if (prev !== next) {\r\n                style.cssText = next;\r\n            }\r\n        }\r\n        else if (prev) {\r\n            el.removeAttribute('style');\r\n        }\r\n        // indicates that the `display` of the element is controlled by `v-show`,\r\n        // so we always keep the current `display` value regardless of the `style`\r\n        // value, thus handing over control to `v-show`.\r\n        if ('_vod' in el) {\r\n            style.display = currentDisplay;\r\n        }\r\n    }\r\n}\r\nconst importantRE = /\\s*!important$/;\r\nfunction setStyle(style, name, val) {\r\n    if (isArray(val)) {\r\n        val.forEach(v => setStyle(style, name, v));\r\n    }\r\n    else {\r\n        if (name.startsWith('--')) {\r\n            // custom property definition\r\n            style.setProperty(name, val);\r\n        }\r\n        else {\r\n            const prefixed = autoPrefix(style, name);\r\n            if (importantRE.test(val)) {\r\n                // !important\r\n                style.setProperty(hyphenate(prefixed), val.replace(importantRE, ''), 'important');\r\n            }\r\n            else {\r\n                style[prefixed] = val;\r\n            }\r\n        }\r\n    }\r\n}\r\nconst prefixes = ['Webkit', 'Moz', 'ms'];\r\nconst prefixCache = {};\r\nfunction autoPrefix(style, rawName) {\r\n    const cached = prefixCache[rawName];\r\n    if (cached) {\r\n        return cached;\r\n    }\r\n    let name = camelize(rawName);\r\n    if (name !== 'filter' && name in style) {\r\n        return (prefixCache[rawName] = name);\r\n    }\r\n    name = capitalize(name);\r\n    for (let i = 0; i < prefixes.length; i++) {\r\n        const prefixed = prefixes[i] + name;\r\n        if (prefixed in style) {\r\n            return (prefixCache[rawName] = prefixed);\r\n        }\r\n    }\r\n    return rawName;\r\n}\n\nconst xlinkNS = 'http://www.w3.org/1999/xlink';\r\nfunction patchAttr(el, key, value, isSVG, instance) {\r\n    if (isSVG && key.startsWith('xlink:')) {\r\n        if (value == null) {\r\n            el.removeAttributeNS(xlinkNS, key.slice(6, key.length));\r\n        }\r\n        else {\r\n            el.setAttributeNS(xlinkNS, key, value);\r\n        }\r\n    }\r\n    else {\r\n        // note we are only checking boolean attributes that don't have a\r\n        // corresponding dom prop of the same name here.\r\n        const isBoolean = isSpecialBooleanAttr(key);\r\n        if (value == null || (isBoolean && !includeBooleanAttr(value))) {\r\n            el.removeAttribute(key);\r\n        }\r\n        else {\r\n            el.setAttribute(key, isBoolean ? '' : value);\r\n        }\r\n    }\r\n}\n\n// __UNSAFE__\r\n// functions. The user is responsible for using them with only trusted content.\r\nfunction patchDOMProp(el, key, value, \r\n// the following args are passed only due to potential innerHTML/textContent\r\n// overriding existing VNodes, in which case the old tree must be properly\r\n// unmounted.\r\nprevChildren, parentComponent, parentSuspense, unmountChildren) {\r\n    if (key === 'innerHTML' || key === 'textContent') {\r\n        if (prevChildren) {\r\n            unmountChildren(prevChildren, parentComponent, parentSuspense);\r\n        }\r\n        el[key] = value == null ? '' : value;\r\n        return;\r\n    }\r\n    if (key === 'value' &&\r\n        el.tagName !== 'PROGRESS' &&\r\n        // custom elements may use _value internally\r\n        !el.tagName.includes('-')) {\r\n        // store value as _value as well since\r\n        // non-string values will be stringified.\r\n        el._value = value;\r\n        const newValue = value == null ? '' : value;\r\n        if (el.value !== newValue ||\r\n            // #4956: always set for OPTION elements because its value falls back to\r\n            // textContent if no value attribute is present. And setting .value for\r\n            // OPTION has no side effect\r\n            el.tagName === 'OPTION') {\r\n            el.value = newValue;\r\n        }\r\n        if (value == null) {\r\n            el.removeAttribute(key);\r\n        }\r\n        return;\r\n    }\r\n    if (value === '' || value == null) {\r\n        const type = typeof el[key];\r\n        if (type === 'boolean') {\r\n            // e.g. <select multiple> compiles to { multiple: '' }\r\n            el[key] = includeBooleanAttr(value);\r\n            return;\r\n        }\r\n        else if (value == null && type === 'string') {\r\n            // e.g. <div :id=\"null\">\r\n            el[key] = '';\r\n            el.removeAttribute(key);\r\n            return;\r\n        }\r\n        else if (type === 'number') {\r\n            // e.g. <img :width=\"null\">\r\n            // the value of some IDL attr must be greater than 0, e.g. input.size = 0 -> error\r\n            try {\r\n                el[key] = 0;\r\n            }\r\n            catch (_a) { }\r\n            el.removeAttribute(key);\r\n            return;\r\n        }\r\n    }\r\n    // some properties perform value validation and throw\r\n    try {\r\n        el[key] = value;\r\n    }\r\n    catch (e) {\r\n        if ((process.env.NODE_ENV !== 'production')) {\r\n            warn(`Failed setting prop \"${key}\" on <${el.tagName.toLowerCase()}>: ` +\r\n                `value ${value} is invalid.`, e);\r\n        }\r\n    }\r\n}\n\n// Async edge case fix requires storing an event listener's attach timestamp.\r\nlet _getNow = Date.now;\r\nlet skipTimestampCheck = false;\r\nif (typeof window !== 'undefined') {\r\n    // Determine what event timestamp the browser is using. Annoyingly, the\r\n    // timestamp can either be hi-res (relative to page load) or low-res\r\n    // (relative to UNIX epoch), so in order to compare time we have to use the\r\n    // same timestamp type when saving the flush timestamp.\r\n    if (_getNow() > document.createEvent('Event').timeStamp) {\r\n        // if the low-res timestamp which is bigger than the event timestamp\r\n        // (which is evaluated AFTER) it means the event is using a hi-res timestamp,\r\n        // and we need to use the hi-res version for event listeners as well.\r\n        _getNow = () => performance.now();\r\n    }\r\n    // #3485: Firefox <= 53 has incorrect Event.timeStamp implementation\r\n    // and does not fire microtasks in between event propagation, so safe to exclude.\r\n    const ffMatch = navigator.userAgent.match(/firefox\\/(\\d+)/i);\r\n    skipTimestampCheck = !!(ffMatch && Number(ffMatch[1]) <= 53);\r\n}\r\n// To avoid the overhead of repeatedly calling performance.now(), we cache\r\n// and use the same timestamp for all event listeners attached in the same tick.\r\nlet cachedNow = 0;\r\nconst p = Promise.resolve();\r\nconst reset = () => {\r\n    cachedNow = 0;\r\n};\r\nconst getNow = () => cachedNow || (p.then(reset), (cachedNow = _getNow()));\r\nfunction addEventListener(el, event, handler, options) {\r\n    el.addEventListener(event, handler, options);\r\n}\r\nfunction removeEventListener(el, event, handler, options) {\r\n    el.removeEventListener(event, handler, options);\r\n}\r\nfunction patchEvent(el, rawName, prevValue, nextValue, instance = null) {\r\n    // vei = vue event invokers\r\n    const invokers = el._vei || (el._vei = {});\r\n    const existingInvoker = invokers[rawName];\r\n    if (nextValue && existingInvoker) {\r\n        // patch\r\n        existingInvoker.value = nextValue;\r\n    }\r\n    else {\r\n        const [name, options] = parseName(rawName);\r\n        if (nextValue) {\r\n            // add\r\n            const invoker = (invokers[rawName] = createInvoker(nextValue, instance));\r\n            addEventListener(el, name, invoker, options);\r\n        }\r\n        else if (existingInvoker) {\r\n            // remove\r\n            removeEventListener(el, name, existingInvoker, options);\r\n            invokers[rawName] = undefined;\r\n        }\r\n    }\r\n}\r\nconst optionsModifierRE = /(?:Once|Passive|Capture)$/;\r\nfunction parseName(name) {\r\n    let options;\r\n    if (optionsModifierRE.test(name)) {\r\n        options = {};\r\n        let m;\r\n        while ((m = name.match(optionsModifierRE))) {\r\n            name = name.slice(0, name.length - m[0].length);\r\n            options[m[0].toLowerCase()] = true;\r\n        }\r\n    }\r\n    return [hyphenate(name.slice(2)), options];\r\n}\r\nfunction createInvoker(initialValue, instance) {\r\n    const invoker = (e) => {\r\n        // async edge case #6566: inner click event triggers patch, event handler\r\n        // attached to outer element during patch, and triggered again. This\r\n        // happens because browsers fire microtask ticks between event propagation.\r\n        // the solution is simple: we save the timestamp when a handler is attached,\r\n        // and the handler would only fire if the event passed to it was fired\r\n        // AFTER it was attached.\r\n        const timeStamp = e.timeStamp || _getNow();\r\n        if (skipTimestampCheck || timeStamp >= invoker.attached - 1) {\r\n            callWithAsyncErrorHandling(patchStopImmediatePropagation(e, invoker.value), instance, 5 /* NATIVE_EVENT_HANDLER */, [e]);\r\n        }\r\n    };\r\n    invoker.value = initialValue;\r\n    invoker.attached = getNow();\r\n    return invoker;\r\n}\r\nfunction patchStopImmediatePropagation(e, value) {\r\n    if (isArray(value)) {\r\n        const originalStop = e.stopImmediatePropagation;\r\n        e.stopImmediatePropagation = () => {\r\n            originalStop.call(e);\r\n            e._stopped = true;\r\n        };\r\n        return value.map(fn => (e) => !e._stopped && fn(e));\r\n    }\r\n    else {\r\n        return value;\r\n    }\r\n}\n\nconst nativeOnRE = /^on[a-z]/;\r\nconst patchProp = (el, key, prevValue, nextValue, isSVG = false, prevChildren, parentComponent, parentSuspense, unmountChildren) => {\r\n    if (key === 'class') {\r\n        patchClass(el, nextValue, isSVG);\r\n    }\r\n    else if (key === 'style') {\r\n        patchStyle(el, prevValue, nextValue);\r\n    }\r\n    else if (isOn(key)) {\r\n        // ignore v-model listeners\r\n        if (!isModelListener(key)) {\r\n            patchEvent(el, key, prevValue, nextValue, parentComponent);\r\n        }\r\n    }\r\n    else if (key[0] === '.'\r\n        ? ((key = key.slice(1)), true)\r\n        : key[0] === '^'\r\n            ? ((key = key.slice(1)), false)\r\n            : shouldSetAsProp(el, key, nextValue, isSVG)) {\r\n        patchDOMProp(el, key, nextValue, prevChildren, parentComponent, parentSuspense, unmountChildren);\r\n    }\r\n    else {\r\n        // special case for <input v-model type=\"checkbox\"> with\r\n        // :true-value & :false-value\r\n        // store value as dom properties since non-string values will be\r\n        // stringified.\r\n        if (key === 'true-value') {\r\n            el._trueValue = nextValue;\r\n        }\r\n        else if (key === 'false-value') {\r\n            el._falseValue = nextValue;\r\n        }\r\n        patchAttr(el, key, nextValue, isSVG);\r\n    }\r\n};\r\nfunction shouldSetAsProp(el, key, value, isSVG) {\r\n    if (isSVG) {\r\n        // most keys must be set as attribute on svg elements to work\r\n        // ...except innerHTML & textContent\r\n        if (key === 'innerHTML' || key === 'textContent') {\r\n            return true;\r\n        }\r\n        // or native onclick with function values\r\n        if (key in el && nativeOnRE.test(key) && isFunction(value)) {\r\n            return true;\r\n        }\r\n        return false;\r\n    }\r\n    // spellcheck and draggable are numerated attrs, however their\r\n    // corresponding DOM properties are actually booleans - this leads to\r\n    // setting it with a string \"false\" value leading it to be coerced to\r\n    // `true`, so we need to always treat them as attributes.\r\n    // Note that `contentEditable` doesn't have this problem: its DOM\r\n    // property is also enumerated string values.\r\n    if (key === 'spellcheck' || key === 'draggable') {\r\n        return false;\r\n    }\r\n    // #1787, #2840 form property on form elements is readonly and must be set as\r\n    // attribute.\r\n    if (key === 'form') {\r\n        return false;\r\n    }\r\n    // #1526 <input list> must be set as attribute\r\n    if (key === 'list' && el.tagName === 'INPUT') {\r\n        return false;\r\n    }\r\n    // #2766 <textarea type> must be set as attribute\r\n    if (key === 'type' && el.tagName === 'TEXTAREA') {\r\n        return false;\r\n    }\r\n    // native onclick with string value, must be set as attribute\r\n    if (nativeOnRE.test(key) && isString(value)) {\r\n        return false;\r\n    }\r\n    return key in el;\r\n}\n\nfunction defineCustomElement(options, hydate) {\r\n    const Comp = defineComponent(options);\r\n    class VueCustomElement extends VueElement {\r\n        constructor(initialProps) {\r\n            super(Comp, initialProps, hydate);\r\n        }\r\n    }\r\n    VueCustomElement.def = Comp;\r\n    return VueCustomElement;\r\n}\r\nconst defineSSRCustomElement = ((options) => {\r\n    // @ts-ignore\r\n    return defineCustomElement(options, hydrate);\r\n});\r\nconst BaseClass = (typeof HTMLElement !== 'undefined' ? HTMLElement : class {\r\n});\r\nclass VueElement extends BaseClass {\r\n    constructor(_def, _props = {}, hydrate) {\r\n        super();\r\n        this._def = _def;\r\n        this._props = _props;\r\n        /**\r\n         * @internal\r\n         */\r\n        this._instance = null;\r\n        this._connected = false;\r\n        this._resolved = false;\r\n        this._numberProps = null;\r\n        if (this.shadowRoot && hydrate) {\r\n            hydrate(this._createVNode(), this.shadowRoot);\r\n        }\r\n        else {\r\n            if ((process.env.NODE_ENV !== 'production') && this.shadowRoot) {\r\n                warn(`Custom element has pre-rendered declarative shadow root but is not ` +\r\n                    `defined as hydratable. Use \\`defineSSRCustomElement\\`.`);\r\n            }\r\n            this.attachShadow({ mode: 'open' });\r\n        }\r\n    }\r\n    connectedCallback() {\r\n        this._connected = true;\r\n        if (!this._instance) {\r\n            this._resolveDef();\r\n        }\r\n    }\r\n    disconnectedCallback() {\r\n        this._connected = false;\r\n        nextTick(() => {\r\n            if (!this._connected) {\r\n                render(null, this.shadowRoot);\r\n                this._instance = null;\r\n            }\r\n        });\r\n    }\r\n    /**\r\n     * resolve inner component definition (handle possible async component)\r\n     */\r\n    _resolveDef() {\r\n        if (this._resolved) {\r\n            return;\r\n        }\r\n        this._resolved = true;\r\n        // set initial attrs\r\n        for (let i = 0; i < this.attributes.length; i++) {\r\n            this._setAttr(this.attributes[i].name);\r\n        }\r\n        // watch future attr changes\r\n        new MutationObserver(mutations => {\r\n            for (const m of mutations) {\r\n                this._setAttr(m.attributeName);\r\n            }\r\n        }).observe(this, { attributes: true });\r\n        const resolve = (def) => {\r\n            const { props, styles } = def;\r\n            const hasOptions = !isArray(props);\r\n            const rawKeys = props ? (hasOptions ? Object.keys(props) : props) : [];\r\n            // cast Number-type props set before resolve\r\n            let numberProps;\r\n            if (hasOptions) {\r\n                for (const key in this._props) {\r\n                    const opt = props[key];\r\n                    if (opt === Number || (opt && opt.type === Number)) {\r\n                        this._props[key] = toNumber(this._props[key]);\r\n                        (numberProps || (numberProps = Object.create(null)))[key] = true;\r\n                    }\r\n                }\r\n            }\r\n            this._numberProps = numberProps;\r\n            // check if there are props set pre-upgrade or connect\r\n            for (const key of Object.keys(this)) {\r\n                if (key[0] !== '_') {\r\n                    this._setProp(key, this[key], true, false);\r\n                }\r\n            }\r\n            // defining getter/setters on prototype\r\n            for (const key of rawKeys.map(camelize$1)) {\r\n                Object.defineProperty(this, key, {\r\n                    get() {\r\n                        return this._getProp(key);\r\n                    },\r\n                    set(val) {\r\n                        this._setProp(key, val);\r\n                    }\r\n                });\r\n            }\r\n            // apply CSS\r\n            this._applyStyles(styles);\r\n            // initial render\r\n            this._update();\r\n        };\r\n        const asyncDef = this._def.__asyncLoader;\r\n        if (asyncDef) {\r\n            asyncDef().then(resolve);\r\n        }\r\n        else {\r\n            resolve(this._def);\r\n        }\r\n    }\r\n    _setAttr(key) {\r\n        let value = this.getAttribute(key);\r\n        if (this._numberProps && this._numberProps[key]) {\r\n            value = toNumber(value);\r\n        }\r\n        this._setProp(camelize$1(key), value, false);\r\n    }\r\n    /**\r\n     * @internal\r\n     */\r\n    _getProp(key) {\r\n        return this._props[key];\r\n    }\r\n    /**\r\n     * @internal\r\n     */\r\n    _setProp(key, val, shouldReflect = true, shouldUpdate = true) {\r\n        if (val !== this._props[key]) {\r\n            this._props[key] = val;\r\n            if (shouldUpdate && this._instance) {\r\n                this._update();\r\n            }\r\n            // reflect\r\n            if (shouldReflect) {\r\n                if (val === true) {\r\n                    this.setAttribute(hyphenate(key), '');\r\n                }\r\n                else if (typeof val === 'string' || typeof val === 'number') {\r\n                    this.setAttribute(hyphenate(key), val + '');\r\n                }\r\n                else if (!val) {\r\n                    this.removeAttribute(hyphenate(key));\r\n                }\r\n            }\r\n        }\r\n    }\r\n    _update() {\r\n        render(this._createVNode(), this.shadowRoot);\r\n    }\r\n    _createVNode() {\r\n        const vnode = createVNode(this._def, extend({}, this._props));\r\n        if (!this._instance) {\r\n            vnode.ce = instance => {\r\n                this._instance = instance;\r\n                instance.isCE = true;\r\n                // HMR\r\n                if ((process.env.NODE_ENV !== 'production')) {\r\n                    instance.ceReload = newStyles => {\r\n                        // always reset styles\r\n                        if (this._styles) {\r\n                            this._styles.forEach(s => this.shadowRoot.removeChild(s));\r\n                            this._styles.length = 0;\r\n                        }\r\n                        this._applyStyles(newStyles);\r\n                        // if this is an async component, ceReload is called from the inner\r\n                        // component so no need to reload the async wrapper\r\n                        if (!this._def.__asyncLoader) {\r\n                            // reload\r\n                            this._instance = null;\r\n                            this._update();\r\n                        }\r\n                    };\r\n                }\r\n                // intercept emit\r\n                instance.emit = (event, ...args) => {\r\n                    this.dispatchEvent(new CustomEvent(event, {\r\n                        detail: args\r\n                    }));\r\n                };\r\n                // locate nearest Vue custom element parent for provide/inject\r\n                let parent = this;\r\n                while ((parent =\r\n                    parent && (parent.parentNode || parent.host))) {\r\n                    if (parent instanceof VueElement) {\r\n                        instance.parent = parent._instance;\r\n                        break;\r\n                    }\r\n                }\r\n            };\r\n        }\r\n        return vnode;\r\n    }\r\n    _applyStyles(styles) {\r\n        if (styles) {\r\n            styles.forEach(css => {\r\n                const s = document.createElement('style');\r\n                s.textContent = css;\r\n                this.shadowRoot.appendChild(s);\r\n                // record for HMR\r\n                if ((process.env.NODE_ENV !== 'production')) {\r\n                    (this._styles || (this._styles = [])).push(s);\r\n                }\r\n            });\r\n        }\r\n    }\r\n}\n\nfunction useCssModule(name = '$style') {\r\n    /* istanbul ignore else */\r\n    {\r\n        const instance = getCurrentInstance();\r\n        if (!instance) {\r\n            (process.env.NODE_ENV !== 'production') && warn(`useCssModule must be called inside setup()`);\r\n            return EMPTY_OBJ;\r\n        }\r\n        const modules = instance.type.__cssModules;\r\n        if (!modules) {\r\n            (process.env.NODE_ENV !== 'production') && warn(`Current instance does not have CSS modules injected.`);\r\n            return EMPTY_OBJ;\r\n        }\r\n        const mod = modules[name];\r\n        if (!mod) {\r\n            (process.env.NODE_ENV !== 'production') &&\r\n                warn(`Current instance does not have CSS module named \"${name}\".`);\r\n            return EMPTY_OBJ;\r\n        }\r\n        return mod;\r\n    }\r\n}\n\n/**\r\n * Runtime helper for SFC's CSS variable injection feature.\r\n * @private\r\n */\r\nfunction useCssVars(getter) {\r\n    const instance = getCurrentInstance();\r\n    /* istanbul ignore next */\r\n    if (!instance) {\r\n        (process.env.NODE_ENV !== 'production') &&\r\n            warn(`useCssVars is called without current active component instance.`);\r\n        return;\r\n    }\r\n    const setVars = () => setVarsOnVNode(instance.subTree, getter(instance.proxy));\r\n    watchPostEffect(setVars);\r\n    onMounted(() => {\r\n        const ob = new MutationObserver(setVars);\r\n        ob.observe(instance.subTree.el.parentNode, { childList: true });\r\n        onUnmounted(() => ob.disconnect());\r\n    });\r\n}\r\nfunction setVarsOnVNode(vnode, vars) {\r\n    if (vnode.shapeFlag & 128 /* SUSPENSE */) {\r\n        const suspense = vnode.suspense;\r\n        vnode = suspense.activeBranch;\r\n        if (suspense.pendingBranch && !suspense.isHydrating) {\r\n            suspense.effects.push(() => {\r\n                setVarsOnVNode(suspense.activeBranch, vars);\r\n            });\r\n        }\r\n    }\r\n    // drill down HOCs until it's a non-component vnode\r\n    while (vnode.component) {\r\n        vnode = vnode.component.subTree;\r\n    }\r\n    if (vnode.shapeFlag & 1 /* ELEMENT */ && vnode.el) {\r\n        setVarsOnNode(vnode.el, vars);\r\n    }\r\n    else if (vnode.type === Fragment) {\r\n        vnode.children.forEach(c => setVarsOnVNode(c, vars));\r\n    }\r\n    else if (vnode.type === Static) {\r\n        let { el, anchor } = vnode;\r\n        while (el) {\r\n            setVarsOnNode(el, vars);\r\n            if (el === anchor)\r\n                break;\r\n            el = el.nextSibling;\r\n        }\r\n    }\r\n}\r\nfunction setVarsOnNode(el, vars) {\r\n    if (el.nodeType === 1) {\r\n        const style = el.style;\r\n        for (const key in vars) {\r\n            style.setProperty(`--${key}`, vars[key]);\r\n        }\r\n    }\r\n}\n\nconst TRANSITION = 'transition';\r\nconst ANIMATION = 'animation';\r\n// DOM Transition is a higher-order-component based on the platform-agnostic\r\n// base Transition component, with DOM-specific logic.\r\nconst Transition = (props, { slots }) => h(BaseTransition, resolveTransitionProps(props), slots);\r\nTransition.displayName = 'Transition';\r\nconst DOMTransitionPropsValidators = {\r\n    name: String,\r\n    type: String,\r\n    css: {\r\n        type: Boolean,\r\n        default: true\r\n    },\r\n    duration: [String, Number, Object],\r\n    enterFromClass: String,\r\n    enterActiveClass: String,\r\n    enterToClass: String,\r\n    appearFromClass: String,\r\n    appearActiveClass: String,\r\n    appearToClass: String,\r\n    leaveFromClass: String,\r\n    leaveActiveClass: String,\r\n    leaveToClass: String\r\n};\r\nconst TransitionPropsValidators = (Transition.props =\r\n    /*#__PURE__*/ extend({}, BaseTransition.props, DOMTransitionPropsValidators));\r\n/**\r\n * #3227 Incoming hooks may be merged into arrays when wrapping Transition\r\n * with custom HOCs.\r\n */\r\nconst callHook = (hook, args = []) => {\r\n    if (isArray(hook)) {\r\n        hook.forEach(h => h(...args));\r\n    }\r\n    else if (hook) {\r\n        hook(...args);\r\n    }\r\n};\r\n/**\r\n * Check if a hook expects a callback (2nd arg), which means the user\r\n * intends to explicitly control the end of the transition.\r\n */\r\nconst hasExplicitCallback = (hook) => {\r\n    return hook\r\n        ? isArray(hook)\r\n            ? hook.some(h => h.length > 1)\r\n            : hook.length > 1\r\n        : false;\r\n};\r\nfunction resolveTransitionProps(rawProps) {\r\n    const baseProps = {};\r\n    for (const key in rawProps) {\r\n        if (!(key in DOMTransitionPropsValidators)) {\r\n            baseProps[key] = rawProps[key];\r\n        }\r\n    }\r\n    if (rawProps.css === false) {\r\n        return baseProps;\r\n    }\r\n    const { name = 'v', type, duration, enterFromClass = `${name}-enter-from`, enterActiveClass = `${name}-enter-active`, enterToClass = `${name}-enter-to`, appearFromClass = enterFromClass, appearActiveClass = enterActiveClass, appearToClass = enterToClass, leaveFromClass = `${name}-leave-from`, leaveActiveClass = `${name}-leave-active`, leaveToClass = `${name}-leave-to` } = rawProps;\r\n    const durations = normalizeDuration(duration);\r\n    const enterDuration = durations && durations[0];\r\n    const leaveDuration = durations && durations[1];\r\n    const { onBeforeEnter, onEnter, onEnterCancelled, onLeave, onLeaveCancelled, onBeforeAppear = onBeforeEnter, onAppear = onEnter, onAppearCancelled = onEnterCancelled } = baseProps;\r\n    const finishEnter = (el, isAppear, done) => {\r\n        removeTransitionClass(el, isAppear ? appearToClass : enterToClass);\r\n        removeTransitionClass(el, isAppear ? appearActiveClass : enterActiveClass);\r\n        done && done();\r\n    };\r\n    const finishLeave = (el, done) => {\r\n        removeTransitionClass(el, leaveToClass);\r\n        removeTransitionClass(el, leaveActiveClass);\r\n        done && done();\r\n    };\r\n    const makeEnterHook = (isAppear) => {\r\n        return (el, done) => {\r\n            const hook = isAppear ? onAppear : onEnter;\r\n            const resolve = () => finishEnter(el, isAppear, done);\r\n            callHook(hook, [el, resolve]);\r\n            nextFrame(() => {\r\n                removeTransitionClass(el, isAppear ? appearFromClass : enterFromClass);\r\n                addTransitionClass(el, isAppear ? appearToClass : enterToClass);\r\n                if (!hasExplicitCallback(hook)) {\r\n                    whenTransitionEnds(el, type, enterDuration, resolve);\r\n                }\r\n            });\r\n        };\r\n    };\r\n    return extend(baseProps, {\r\n        onBeforeEnter(el) {\r\n            callHook(onBeforeEnter, [el]);\r\n            addTransitionClass(el, enterFromClass);\r\n            addTransitionClass(el, enterActiveClass);\r\n        },\r\n        onBeforeAppear(el) {\r\n            callHook(onBeforeAppear, [el]);\r\n            addTransitionClass(el, appearFromClass);\r\n            addTransitionClass(el, appearActiveClass);\r\n        },\r\n        onEnter: makeEnterHook(false),\r\n        onAppear: makeEnterHook(true),\r\n        onLeave(el, done) {\r\n            const resolve = () => finishLeave(el, done);\r\n            addTransitionClass(el, leaveFromClass);\r\n            // force reflow so *-leave-from classes immediately take effect (#2593)\r\n            forceReflow();\r\n            addTransitionClass(el, leaveActiveClass);\r\n            nextFrame(() => {\r\n                removeTransitionClass(el, leaveFromClass);\r\n                addTransitionClass(el, leaveToClass);\r\n                if (!hasExplicitCallback(onLeave)) {\r\n                    whenTransitionEnds(el, type, leaveDuration, resolve);\r\n                }\r\n            });\r\n            callHook(onLeave, [el, resolve]);\r\n        },\r\n        onEnterCancelled(el) {\r\n            finishEnter(el, false);\r\n            callHook(onEnterCancelled, [el]);\r\n        },\r\n        onAppearCancelled(el) {\r\n            finishEnter(el, true);\r\n            callHook(onAppearCancelled, [el]);\r\n        },\r\n        onLeaveCancelled(el) {\r\n            finishLeave(el);\r\n            callHook(onLeaveCancelled, [el]);\r\n        }\r\n    });\r\n}\r\nfunction normalizeDuration(duration) {\r\n    if (duration == null) {\r\n        return null;\r\n    }\r\n    else if (isObject(duration)) {\r\n        return [NumberOf(duration.enter), NumberOf(duration.leave)];\r\n    }\r\n    else {\r\n        const n = NumberOf(duration);\r\n        return [n, n];\r\n    }\r\n}\r\nfunction NumberOf(val) {\r\n    const res = toNumber(val);\r\n    if ((process.env.NODE_ENV !== 'production'))\r\n        validateDuration(res);\r\n    return res;\r\n}\r\nfunction validateDuration(val) {\r\n    if (typeof val !== 'number') {\r\n        warn(`<transition> explicit duration is not a valid number - ` +\r\n            `got ${JSON.stringify(val)}.`);\r\n    }\r\n    else if (isNaN(val)) {\r\n        warn(`<transition> explicit duration is NaN - ` +\r\n            'the duration expression might be incorrect.');\r\n    }\r\n}\r\nfunction addTransitionClass(el, cls) {\r\n    cls.split(/\\s+/).forEach(c => c && el.classList.add(c));\r\n    (el._vtc ||\r\n        (el._vtc = new Set())).add(cls);\r\n}\r\nfunction removeTransitionClass(el, cls) {\r\n    cls.split(/\\s+/).forEach(c => c && el.classList.remove(c));\r\n    const { _vtc } = el;\r\n    if (_vtc) {\r\n        _vtc.delete(cls);\r\n        if (!_vtc.size) {\r\n            el._vtc = undefined;\r\n        }\r\n    }\r\n}\r\nfunction nextFrame(cb) {\r\n    requestAnimationFrame(() => {\r\n        requestAnimationFrame(cb);\r\n    });\r\n}\r\nlet endId = 0;\r\nfunction whenTransitionEnds(el, expectedType, explicitTimeout, resolve) {\r\n    const id = (el._endId = ++endId);\r\n    const resolveIfNotStale = () => {\r\n        if (id === el._endId) {\r\n            resolve();\r\n        }\r\n    };\r\n    if (explicitTimeout) {\r\n        return setTimeout(resolveIfNotStale, explicitTimeout);\r\n    }\r\n    const { type, timeout, propCount } = getTransitionInfo(el, expectedType);\r\n    if (!type) {\r\n        return resolve();\r\n    }\r\n    const endEvent = type + 'end';\r\n    let ended = 0;\r\n    const end = () => {\r\n        el.removeEventListener(endEvent, onEnd);\r\n        resolveIfNotStale();\r\n    };\r\n    const onEnd = (e) => {\r\n        if (e.target === el && ++ended >= propCount) {\r\n            end();\r\n        }\r\n    };\r\n    setTimeout(() => {\r\n        if (ended < propCount) {\r\n            end();\r\n        }\r\n    }, timeout + 1);\r\n    el.addEventListener(endEvent, onEnd);\r\n}\r\nfunction getTransitionInfo(el, expectedType) {\r\n    const styles = window.getComputedStyle(el);\r\n    // JSDOM may return undefined for transition properties\r\n    const getStyleProperties = (key) => (styles[key] || '').split(', ');\r\n    const transitionDelays = getStyleProperties(TRANSITION + 'Delay');\r\n    const transitionDurations = getStyleProperties(TRANSITION + 'Duration');\r\n    const transitionTimeout = getTimeout(transitionDelays, transitionDurations);\r\n    const animationDelays = getStyleProperties(ANIMATION + 'Delay');\r\n    const animationDurations = getStyleProperties(ANIMATION + 'Duration');\r\n    const animationTimeout = getTimeout(animationDelays, animationDurations);\r\n    let type = null;\r\n    let timeout = 0;\r\n    let propCount = 0;\r\n    /* istanbul ignore if */\r\n    if (expectedType === TRANSITION) {\r\n        if (transitionTimeout > 0) {\r\n            type = TRANSITION;\r\n            timeout = transitionTimeout;\r\n            propCount = transitionDurations.length;\r\n        }\r\n    }\r\n    else if (expectedType === ANIMATION) {\r\n        if (animationTimeout > 0) {\r\n            type = ANIMATION;\r\n            timeout = animationTimeout;\r\n            propCount = animationDurations.length;\r\n        }\r\n    }\r\n    else {\r\n        timeout = Math.max(transitionTimeout, animationTimeout);\r\n        type =\r\n            timeout > 0\r\n                ? transitionTimeout > animationTimeout\r\n                    ? TRANSITION\r\n                    : ANIMATION\r\n                : null;\r\n        propCount = type\r\n            ? type === TRANSITION\r\n                ? transitionDurations.length\r\n                : animationDurations.length\r\n            : 0;\r\n    }\r\n    const hasTransform = type === TRANSITION &&\r\n        /\\b(transform|all)(,|$)/.test(styles[TRANSITION + 'Property']);\r\n    return {\r\n        type,\r\n        timeout,\r\n        propCount,\r\n        hasTransform\r\n    };\r\n}\r\nfunction getTimeout(delays, durations) {\r\n    while (delays.length < durations.length) {\r\n        delays = delays.concat(delays);\r\n    }\r\n    return Math.max(...durations.map((d, i) => toMs(d) + toMs(delays[i])));\r\n}\r\n// Old versions of Chromium (below 61.0.3163.100) formats floating pointer\r\n// numbers in a locale-dependent way, using a comma instead of a dot.\r\n// If comma is not replaced with a dot, the input will be rounded down\r\n// (i.e. acting as a floor function) causing unexpected behaviors\r\nfunction toMs(s) {\r\n    return Number(s.slice(0, -1).replace(',', '.')) * 1000;\r\n}\r\n// synchronously force layout to put elements into a certain state\r\nfunction forceReflow() {\r\n    return document.body.offsetHeight;\r\n}\n\nconst positionMap = new WeakMap();\r\nconst newPositionMap = new WeakMap();\r\nconst TransitionGroupImpl = {\r\n    name: 'TransitionGroup',\r\n    props: /*#__PURE__*/ extend({}, TransitionPropsValidators, {\r\n        tag: String,\r\n        moveClass: String\r\n    }),\r\n    setup(props, { slots }) {\r\n        const instance = getCurrentInstance();\r\n        const state = useTransitionState();\r\n        let prevChildren;\r\n        let children;\r\n        onUpdated(() => {\r\n            // children is guaranteed to exist after initial render\r\n            if (!prevChildren.length) {\r\n                return;\r\n            }\r\n            const moveClass = props.moveClass || `${props.name || 'v'}-move`;\r\n            if (!hasCSSTransform(prevChildren[0].el, instance.vnode.el, moveClass)) {\r\n                return;\r\n            }\r\n            // we divide the work into three loops to avoid mixing DOM reads and writes\r\n            // in each iteration - which helps prevent layout thrashing.\r\n            prevChildren.forEach(callPendingCbs);\r\n            prevChildren.forEach(recordPosition);\r\n            const movedChildren = prevChildren.filter(applyTranslation);\r\n            // force reflow to put everything in position\r\n            forceReflow();\r\n            movedChildren.forEach(c => {\r\n                const el = c.el;\r\n                const style = el.style;\r\n                addTransitionClass(el, moveClass);\r\n                style.transform = style.webkitTransform = style.transitionDuration = '';\r\n                const cb = (el._moveCb = (e) => {\r\n                    if (e && e.target !== el) {\r\n                        return;\r\n                    }\r\n                    if (!e || /transform$/.test(e.propertyName)) {\r\n                        el.removeEventListener('transitionend', cb);\r\n                        el._moveCb = null;\r\n                        removeTransitionClass(el, moveClass);\r\n                    }\r\n                });\r\n                el.addEventListener('transitionend', cb);\r\n            });\r\n        });\r\n        return () => {\r\n            const rawProps = toRaw(props);\r\n            const cssTransitionProps = resolveTransitionProps(rawProps);\r\n            let tag = rawProps.tag || Fragment;\r\n            prevChildren = children;\r\n            children = slots.default ? getTransitionRawChildren(slots.default()) : [];\r\n            for (let i = 0; i < children.length; i++) {\r\n                const child = children[i];\r\n                if (child.key != null) {\r\n                    setTransitionHooks(child, resolveTransitionHooks(child, cssTransitionProps, state, instance));\r\n                }\r\n                else if ((process.env.NODE_ENV !== 'production')) {\r\n                    warn(`<TransitionGroup> children must be keyed.`);\r\n                }\r\n            }\r\n            if (prevChildren) {\r\n                for (let i = 0; i < prevChildren.length; i++) {\r\n                    const child = prevChildren[i];\r\n                    setTransitionHooks(child, resolveTransitionHooks(child, cssTransitionProps, state, instance));\r\n                    positionMap.set(child, child.el.getBoundingClientRect());\r\n                }\r\n            }\r\n            return createVNode(tag, null, children);\r\n        };\r\n    }\r\n};\r\nconst TransitionGroup = TransitionGroupImpl;\r\nfunction callPendingCbs(c) {\r\n    const el = c.el;\r\n    if (el._moveCb) {\r\n        el._moveCb();\r\n    }\r\n    if (el._enterCb) {\r\n        el._enterCb();\r\n    }\r\n}\r\nfunction recordPosition(c) {\r\n    newPositionMap.set(c, c.el.getBoundingClientRect());\r\n}\r\nfunction applyTranslation(c) {\r\n    const oldPos = positionMap.get(c);\r\n    const newPos = newPositionMap.get(c);\r\n    const dx = oldPos.left - newPos.left;\r\n    const dy = oldPos.top - newPos.top;\r\n    if (dx || dy) {\r\n        const s = c.el.style;\r\n        s.transform = s.webkitTransform = `translate(${dx}px,${dy}px)`;\r\n        s.transitionDuration = '0s';\r\n        return c;\r\n    }\r\n}\r\nfunction hasCSSTransform(el, root, moveClass) {\r\n    // Detect whether an element with the move class applied has\r\n    // CSS transitions. Since the element may be inside an entering\r\n    // transition at this very moment, we make a clone of it and remove\r\n    // all other transition classes applied to ensure only the move class\r\n    // is applied.\r\n    const clone = el.cloneNode();\r\n    if (el._vtc) {\r\n        el._vtc.forEach(cls => {\r\n            cls.split(/\\s+/).forEach(c => c && clone.classList.remove(c));\r\n        });\r\n    }\r\n    moveClass.split(/\\s+/).forEach(c => c && clone.classList.add(c));\r\n    clone.style.display = 'none';\r\n    const container = (root.nodeType === 1 ? root : root.parentNode);\r\n    container.appendChild(clone);\r\n    const { hasTransform } = getTransitionInfo(clone);\r\n    container.removeChild(clone);\r\n    return hasTransform;\r\n}\n\nconst getModelAssigner = (vnode) => {\r\n    const fn = vnode.props['onUpdate:modelValue'];\r\n    return isArray(fn) ? value => invokeArrayFns(fn, value) : fn;\r\n};\r\nfunction onCompositionStart(e) {\r\n    e.target.composing = true;\r\n}\r\nfunction onCompositionEnd(e) {\r\n    const target = e.target;\r\n    if (target.composing) {\r\n        target.composing = false;\r\n        trigger(target, 'input');\r\n    }\r\n}\r\nfunction trigger(el, type) {\r\n    const e = document.createEvent('HTMLEvents');\r\n    e.initEvent(type, true, true);\r\n    el.dispatchEvent(e);\r\n}\r\n// We are exporting the v-model runtime directly as vnode hooks so that it can\r\n// be tree-shaken in case v-model is never used.\r\nconst vModelText = {\r\n    created(el, { modifiers: { lazy, trim, number } }, vnode) {\r\n        el._assign = getModelAssigner(vnode);\r\n        const castToNumber = number || (vnode.props && vnode.props.type === 'number');\r\n        addEventListener(el, lazy ? 'change' : 'input', e => {\r\n            if (e.target.composing)\r\n                return;\r\n            let domValue = el.value;\r\n            if (trim) {\r\n                domValue = domValue.trim();\r\n            }\r\n            else if (castToNumber) {\r\n                domValue = toNumber(domValue);\r\n            }\r\n            el._assign(domValue);\r\n        });\r\n        if (trim) {\r\n            addEventListener(el, 'change', () => {\r\n                el.value = el.value.trim();\r\n            });\r\n        }\r\n        if (!lazy) {\r\n            addEventListener(el, 'compositionstart', onCompositionStart);\r\n            addEventListener(el, 'compositionend', onCompositionEnd);\r\n            // Safari < 10.2 & UIWebView doesn't fire compositionend when\r\n            // switching focus before confirming composition choice\r\n            // this also fixes the issue where some browsers e.g. iOS Chrome\r\n            // fires \"change\" instead of \"input\" on autocomplete.\r\n            addEventListener(el, 'change', onCompositionEnd);\r\n        }\r\n    },\r\n    // set value on mounted so it's after min/max for type=\"range\"\r\n    mounted(el, { value }) {\r\n        el.value = value == null ? '' : value;\r\n    },\r\n    beforeUpdate(el, { value, modifiers: { lazy, trim, number } }, vnode) {\r\n        el._assign = getModelAssigner(vnode);\r\n        // avoid clearing unresolved text. #2302\r\n        if (el.composing)\r\n            return;\r\n        if (document.activeElement === el) {\r\n            if (lazy) {\r\n                return;\r\n            }\r\n            if (trim && el.value.trim() === value) {\r\n                return;\r\n            }\r\n            if ((number || el.type === 'number') && toNumber(el.value) === value) {\r\n                return;\r\n            }\r\n        }\r\n        const newValue = value == null ? '' : value;\r\n        if (el.value !== newValue) {\r\n            el.value = newValue;\r\n        }\r\n    }\r\n};\r\nconst vModelCheckbox = {\r\n    // #4096 array checkboxes need to be deep traversed\r\n    deep: true,\r\n    created(el, _, vnode) {\r\n        el._assign = getModelAssigner(vnode);\r\n        addEventListener(el, 'change', () => {\r\n            const modelValue = el._modelValue;\r\n            const elementValue = getValue(el);\r\n            const checked = el.checked;\r\n            const assign = el._assign;\r\n            if (isArray(modelValue)) {\r\n                const index = looseIndexOf(modelValue, elementValue);\r\n                const found = index !== -1;\r\n                if (checked && !found) {\r\n                    assign(modelValue.concat(elementValue));\r\n                }\r\n                else if (!checked && found) {\r\n                    const filtered = [...modelValue];\r\n                    filtered.splice(index, 1);\r\n                    assign(filtered);\r\n                }\r\n            }\r\n            else if (isSet(modelValue)) {\r\n                const cloned = new Set(modelValue);\r\n                if (checked) {\r\n                    cloned.add(elementValue);\r\n                }\r\n                else {\r\n                    cloned.delete(elementValue);\r\n                }\r\n                assign(cloned);\r\n            }\r\n            else {\r\n                assign(getCheckboxValue(el, checked));\r\n            }\r\n        });\r\n    },\r\n    // set initial checked on mount to wait for true-value/false-value\r\n    mounted: setChecked,\r\n    beforeUpdate(el, binding, vnode) {\r\n        el._assign = getModelAssigner(vnode);\r\n        setChecked(el, binding, vnode);\r\n    }\r\n};\r\nfunction setChecked(el, { value, oldValue }, vnode) {\r\n    el._modelValue = value;\r\n    if (isArray(value)) {\r\n        el.checked = looseIndexOf(value, vnode.props.value) > -1;\r\n    }\r\n    else if (isSet(value)) {\r\n        el.checked = value.has(vnode.props.value);\r\n    }\r\n    else if (value !== oldValue) {\r\n        el.checked = looseEqual(value, getCheckboxValue(el, true));\r\n    }\r\n}\r\nconst vModelRadio = {\r\n    created(el, { value }, vnode) {\r\n        el.checked = looseEqual(value, vnode.props.value);\r\n        el._assign = getModelAssigner(vnode);\r\n        addEventListener(el, 'change', () => {\r\n            el._assign(getValue(el));\r\n        });\r\n    },\r\n    beforeUpdate(el, { value, oldValue }, vnode) {\r\n        el._assign = getModelAssigner(vnode);\r\n        if (value !== oldValue) {\r\n            el.checked = looseEqual(value, vnode.props.value);\r\n        }\r\n    }\r\n};\r\nconst vModelSelect = {\r\n    // <select multiple> value need to be deep traversed\r\n    deep: true,\r\n    created(el, { value, modifiers: { number } }, vnode) {\r\n        const isSetModel = isSet(value);\r\n        addEventListener(el, 'change', () => {\r\n            const selectedVal = Array.prototype.filter\r\n                .call(el.options, (o) => o.selected)\r\n                .map((o) => number ? toNumber(getValue(o)) : getValue(o));\r\n            el._assign(el.multiple\r\n                ? isSetModel\r\n                    ? new Set(selectedVal)\r\n                    : selectedVal\r\n                : selectedVal[0]);\r\n        });\r\n        el._assign = getModelAssigner(vnode);\r\n    },\r\n    // set value in mounted & updated because <select> relies on its children\r\n    // <option>s.\r\n    mounted(el, { value }) {\r\n        setSelected(el, value);\r\n    },\r\n    beforeUpdate(el, _binding, vnode) {\r\n        el._assign = getModelAssigner(vnode);\r\n    },\r\n    updated(el, { value }) {\r\n        setSelected(el, value);\r\n    }\r\n};\r\nfunction setSelected(el, value) {\r\n    const isMultiple = el.multiple;\r\n    if (isMultiple && !isArray(value) && !isSet(value)) {\r\n        (process.env.NODE_ENV !== 'production') &&\r\n            warn(`<select multiple v-model> expects an Array or Set value for its binding, ` +\r\n                `but got ${Object.prototype.toString.call(value).slice(8, -1)}.`);\r\n        return;\r\n    }\r\n    for (let i = 0, l = el.options.length; i < l; i++) {\r\n        const option = el.options[i];\r\n        const optionValue = getValue(option);\r\n        if (isMultiple) {\r\n            if (isArray(value)) {\r\n                option.selected = looseIndexOf(value, optionValue) > -1;\r\n            }\r\n            else {\r\n                option.selected = value.has(optionValue);\r\n            }\r\n        }\r\n        else {\r\n            if (looseEqual(getValue(option), value)) {\r\n                if (el.selectedIndex !== i)\r\n                    el.selectedIndex = i;\r\n                return;\r\n            }\r\n        }\r\n    }\r\n    if (!isMultiple && el.selectedIndex !== -1) {\r\n        el.selectedIndex = -1;\r\n    }\r\n}\r\n// retrieve raw value set via :value bindings\r\nfunction getValue(el) {\r\n    return '_value' in el ? el._value : el.value;\r\n}\r\n// retrieve raw value for true-value and false-value set via :true-value or :false-value bindings\r\nfunction getCheckboxValue(el, checked) {\r\n    const key = checked ? '_trueValue' : '_falseValue';\r\n    return key in el ? el[key] : checked;\r\n}\r\nconst vModelDynamic = {\r\n    created(el, binding, vnode) {\r\n        callModelHook(el, binding, vnode, null, 'created');\r\n    },\r\n    mounted(el, binding, vnode) {\r\n        callModelHook(el, binding, vnode, null, 'mounted');\r\n    },\r\n    beforeUpdate(el, binding, vnode, prevVNode) {\r\n        callModelHook(el, binding, vnode, prevVNode, 'beforeUpdate');\r\n    },\r\n    updated(el, binding, vnode, prevVNode) {\r\n        callModelHook(el, binding, vnode, prevVNode, 'updated');\r\n    }\r\n};\r\nfunction callModelHook(el, binding, vnode, prevVNode, hook) {\r\n    let modelToUse;\r\n    switch (el.tagName) {\r\n        case 'SELECT':\r\n            modelToUse = vModelSelect;\r\n            break;\r\n        case 'TEXTAREA':\r\n            modelToUse = vModelText;\r\n            break;\r\n        default:\r\n            switch (vnode.props && vnode.props.type) {\r\n                case 'checkbox':\r\n                    modelToUse = vModelCheckbox;\r\n                    break;\r\n                case 'radio':\r\n                    modelToUse = vModelRadio;\r\n                    break;\r\n                default:\r\n                    modelToUse = vModelText;\r\n            }\r\n    }\r\n    const fn = modelToUse[hook];\r\n    fn && fn(el, binding, vnode, prevVNode);\r\n}\r\n// SSR vnode transforms, only used when user includes client-oriented render\r\n// function in SSR\r\nfunction initVModelForSSR() {\r\n    vModelText.getSSRProps = ({ value }) => ({ value });\r\n    vModelRadio.getSSRProps = ({ value }, vnode) => {\r\n        if (vnode.props && looseEqual(vnode.props.value, value)) {\r\n            return { checked: true };\r\n        }\r\n    };\r\n    vModelCheckbox.getSSRProps = ({ value }, vnode) => {\r\n        if (isArray(value)) {\r\n            if (vnode.props && looseIndexOf(value, vnode.props.value) > -1) {\r\n                return { checked: true };\r\n            }\r\n        }\r\n        else if (isSet(value)) {\r\n            if (vnode.props && value.has(vnode.props.value)) {\r\n                return { checked: true };\r\n            }\r\n        }\r\n        else if (value) {\r\n            return { checked: true };\r\n        }\r\n    };\r\n}\n\nconst systemModifiers = ['ctrl', 'shift', 'alt', 'meta'];\r\nconst modifierGuards = {\r\n    stop: e => e.stopPropagation(),\r\n    prevent: e => e.preventDefault(),\r\n    self: e => e.target !== e.currentTarget,\r\n    ctrl: e => !e.ctrlKey,\r\n    shift: e => !e.shiftKey,\r\n    alt: e => !e.altKey,\r\n    meta: e => !e.metaKey,\r\n    left: e => 'button' in e && e.button !== 0,\r\n    middle: e => 'button' in e && e.button !== 1,\r\n    right: e => 'button' in e && e.button !== 2,\r\n    exact: (e, modifiers) => systemModifiers.some(m => e[`${m}Key`] && !modifiers.includes(m))\r\n};\r\n/**\r\n * @private\r\n */\r\nconst withModifiers = (fn, modifiers) => {\r\n    return (event, ...args) => {\r\n        for (let i = 0; i < modifiers.length; i++) {\r\n            const guard = modifierGuards[modifiers[i]];\r\n            if (guard && guard(event, modifiers))\r\n                return;\r\n        }\r\n        return fn(event, ...args);\r\n    };\r\n};\r\n// Kept for 2.x compat.\r\n// Note: IE11 compat for `spacebar` and `del` is removed for now.\r\nconst keyNames = {\r\n    esc: 'escape',\r\n    space: ' ',\r\n    up: 'arrow-up',\r\n    left: 'arrow-left',\r\n    right: 'arrow-right',\r\n    down: 'arrow-down',\r\n    delete: 'backspace'\r\n};\r\n/**\r\n * @private\r\n */\r\nconst withKeys = (fn, modifiers) => {\r\n    return (event) => {\r\n        if (!('key' in event)) {\r\n            return;\r\n        }\r\n        const eventKey = hyphenate(event.key);\r\n        if (modifiers.some(k => k === eventKey || keyNames[k] === eventKey)) {\r\n            return fn(event);\r\n        }\r\n    };\r\n};\n\nconst vShow = {\r\n    beforeMount(el, { value }, { transition }) {\r\n        el._vod = el.style.display === 'none' ? '' : el.style.display;\r\n        if (transition && value) {\r\n            transition.beforeEnter(el);\r\n        }\r\n        else {\r\n            setDisplay(el, value);\r\n        }\r\n    },\r\n    mounted(el, { value }, { transition }) {\r\n        if (transition && value) {\r\n            transition.enter(el);\r\n        }\r\n    },\r\n    updated(el, { value, oldValue }, { transition }) {\r\n        if (!value === !oldValue)\r\n            return;\r\n        if (transition) {\r\n            if (value) {\r\n                transition.beforeEnter(el);\r\n                setDisplay(el, true);\r\n                transition.enter(el);\r\n            }\r\n            else {\r\n                transition.leave(el, () => {\r\n                    setDisplay(el, false);\r\n                });\r\n            }\r\n        }\r\n        else {\r\n            setDisplay(el, value);\r\n        }\r\n    },\r\n    beforeUnmount(el, { value }) {\r\n        setDisplay(el, value);\r\n    }\r\n};\r\nfunction setDisplay(el, value) {\r\n    el.style.display = value ? el._vod : 'none';\r\n}\r\n// SSR vnode transforms, only used when user includes client-oriented render\r\n// function in SSR\r\nfunction initVShowForSSR() {\r\n    vShow.getSSRProps = ({ value }) => {\r\n        if (!value) {\r\n            return { style: { display: 'none' } };\r\n        }\r\n    };\r\n}\n\nconst rendererOptions = extend({ patchProp }, nodeOps);\r\n// lazy create the renderer - this makes core renderer logic tree-shakable\r\n// in case the user only imports reactivity utilities from Vue.\r\nlet renderer;\r\nlet enabledHydration = false;\r\nfunction ensureRenderer() {\r\n    return (renderer ||\r\n        (renderer = createRenderer(rendererOptions)));\r\n}\r\nfunction ensureHydrationRenderer() {\r\n    renderer = enabledHydration\r\n        ? renderer\r\n        : createHydrationRenderer(rendererOptions);\r\n    enabledHydration = true;\r\n    return renderer;\r\n}\r\n// use explicit type casts here to avoid import() calls in rolled-up d.ts\r\nconst render = ((...args) => {\r\n    ensureRenderer().render(...args);\r\n});\r\nconst hydrate = ((...args) => {\r\n    ensureHydrationRenderer().hydrate(...args);\r\n});\r\nconst createApp = ((...args) => {\r\n    const app = ensureRenderer().createApp(...args);\r\n    if ((process.env.NODE_ENV !== 'production')) {\r\n        injectNativeTagCheck(app);\r\n        injectCompilerOptionsCheck(app);\r\n    }\r\n    const { mount } = app;\r\n    app.mount = (containerOrSelector) => {\r\n        const container = normalizeContainer(containerOrSelector);\r\n        if (!container)\r\n            return;\r\n        const component = app._component;\r\n        if (!isFunction(component) && !component.render && !component.template) {\r\n            // __UNSAFE__\r\n            // Reason: potential execution of JS expressions in in-DOM template.\r\n            // The user must make sure the in-DOM template is trusted. If it's\r\n            // rendered by the server, the template should not contain any user data.\r\n            component.template = container.innerHTML;\r\n        }\r\n        // clear content before mounting\r\n        container.innerHTML = '';\r\n        const proxy = mount(container, false, container instanceof SVGElement);\r\n        if (container instanceof Element) {\r\n            container.removeAttribute('v-cloak');\r\n            container.setAttribute('data-v-app', '');\r\n        }\r\n        return proxy;\r\n    };\r\n    return app;\r\n});\r\nconst createSSRApp = ((...args) => {\r\n    const app = ensureHydrationRenderer().createApp(...args);\r\n    if ((process.env.NODE_ENV !== 'production')) {\r\n        injectNativeTagCheck(app);\r\n        injectCompilerOptionsCheck(app);\r\n    }\r\n    const { mount } = app;\r\n    app.mount = (containerOrSelector) => {\r\n        const container = normalizeContainer(containerOrSelector);\r\n        if (container) {\r\n            return mount(container, true, container instanceof SVGElement);\r\n        }\r\n    };\r\n    return app;\r\n});\r\nfunction injectNativeTagCheck(app) {\r\n    // Inject `isNativeTag`\r\n    // this is used for component name validation (dev only)\r\n    Object.defineProperty(app.config, 'isNativeTag', {\r\n        value: (tag) => isHTMLTag(tag) || isSVGTag(tag),\r\n        writable: false\r\n    });\r\n}\r\n// dev only\r\nfunction injectCompilerOptionsCheck(app) {\r\n    if (isRuntimeOnly()) {\r\n        const isCustomElement = app.config.isCustomElement;\r\n        Object.defineProperty(app.config, 'isCustomElement', {\r\n            get() {\r\n                return isCustomElement;\r\n            },\r\n            set() {\r\n                warn(`The \\`isCustomElement\\` config option is deprecated. Use ` +\r\n                    `\\`compilerOptions.isCustomElement\\` instead.`);\r\n            }\r\n        });\r\n        const compilerOptions = app.config.compilerOptions;\r\n        const msg = `The \\`compilerOptions\\` config option is only respected when using ` +\r\n            `a build of Vue.js that includes the runtime compiler (aka \"full build\"). ` +\r\n            `Since you are using the runtime-only build, \\`compilerOptions\\` ` +\r\n            `must be passed to \\`@vue/compiler-dom\\` in the build setup instead.\\n` +\r\n            `- For vue-loader: pass it via vue-loader's \\`compilerOptions\\` loader option.\\n` +\r\n            `- For vue-cli: see https://cli.vuejs.org/guide/webpack.html#modifying-options-of-a-loader\\n` +\r\n            `- For vite: pass it via @vitejs/plugin-vue options. See https://github.com/vitejs/vite/tree/main/packages/plugin-vue#example-for-passing-options-to-vuecompiler-dom`;\r\n        Object.defineProperty(app.config, 'compilerOptions', {\r\n            get() {\r\n                warn(msg);\r\n                return compilerOptions;\r\n            },\r\n            set() {\r\n                warn(msg);\r\n            }\r\n        });\r\n    }\r\n}\r\nfunction normalizeContainer(container) {\r\n    if (isString(container)) {\r\n        const res = document.querySelector(container);\r\n        if ((process.env.NODE_ENV !== 'production') && !res) {\r\n            warn(`Failed to mount app: mount target selector \"${container}\" returned null.`);\r\n        }\r\n        return res;\r\n    }\r\n    if ((process.env.NODE_ENV !== 'production') &&\r\n        window.ShadowRoot &&\r\n        container instanceof window.ShadowRoot &&\r\n        container.mode === 'closed') {\r\n        warn(`mounting on a ShadowRoot with \\`{mode: \"closed\"}\\` may lead to unpredictable bugs`);\r\n    }\r\n    return container;\r\n}\r\nlet ssrDirectiveInitialized = false;\r\n/**\r\n * @internal\r\n */\r\nconst initDirectivesForSSR = () => {\r\n        if (!ssrDirectiveInitialized) {\r\n            ssrDirectiveInitialized = true;\r\n            initVModelForSSR();\r\n            initVShowForSSR();\r\n        }\r\n    }\r\n    ;\n\nexport { Transition, TransitionGroup, VueElement, createApp, createSSRApp, defineCustomElement, defineSSRCustomElement, hydrate, initDirectivesForSSR, render, useCssModule, useCssVars, vModelCheckbox, vModelDynamic, vModelRadio, vModelSelect, vModelText, vShow, withKeys, withModifiers };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAAA,SAASA,QAAQ,EAAEC,IAAI,EAAEC,0BAA0B,EAAEC,eAAe,EAAEC,QAAQ,EAAEC,WAAW,EAAEC,kBAAkB,EAAEC,eAAe,EAAEC,SAAS,EAAEC,WAAW,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,CAAC,EAAEC,cAAc,EAAEC,kBAAkB,EAAEC,SAAS,EAAEC,KAAK,EAAEC,wBAAwB,EAAEC,kBAAkB,EAAEC,sBAAsB,EAAEC,cAAc,EAAEC,aAAa,EAAEC,uBAAuB,QAAQ,mBAAmB;AAC5X,cAAc,mBAAmB;AACjC,SAASC,QAAQ,EAAEC,OAAO,EAAEC,SAAS,EAAEC,UAAU,EAAEC,oBAAoB,EAAEC,kBAAkB,EAAEC,IAAI,EAAEC,eAAe,EAAEC,UAAU,EAAEC,QAAQ,EAAEhC,QAAQ,IAAIiC,UAAU,EAAEC,MAAM,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,cAAc,EAAEC,YAAY,EAAEC,KAAK,EAAEC,UAAU,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,aAAa;AAExR,IAAMC,KAAK,GAAG,4BAA4B;AAC1C,IAAMC,GAAG,GAAI,OAAOC,QAAQ,KAAK,WAAW,GAAGA,QAAQ,GAAG,IAAK;AAC/D,IAAMC,mBAAmB,GAAG,IAAIC,GAAG,CAAC,CAAC;AACrC,IAAMC,OAAO,GAAG;EACZC,MAAM,EAAE,SAAAA,OAACC,KAAK,EAAEC,MAAM,EAAEC,MAAM,EAAK;IAC/BD,MAAM,CAACE,YAAY,CAACH,KAAK,EAAEE,MAAM,IAAI,IAAI,CAAC;EAC9C,CAAC;EACDE,MAAM,EAAE,SAAAA,OAAAJ,KAAK,EAAI;IACb,IAAMC,MAAM,GAAGD,KAAK,CAACK,UAAU;IAC/B,IAAIJ,MAAM,EAAE;MACRA,MAAM,CAACK,WAAW,CAACN,KAAK,CAAC;IAC7B;EACJ,CAAC;EACDO,aAAa,EAAE,SAAAA,cAACC,GAAG,EAAEC,KAAK,EAAEC,EAAE,EAAEC,KAAK,EAAK;IACtC,IAAMC,EAAE,GAAGH,KAAK,GACVf,GAAG,CAACmB,eAAe,CAACpB,KAAK,EAAEe,GAAG,CAAC,GAC/Bd,GAAG,CAACa,aAAa,CAACC,GAAG,EAAEE,EAAE,GAAG;MAAEA,EAAE,EAAFA;IAAG,CAAC,GAAGI,SAAS,CAAC;IACrD,IAAIN,GAAG,KAAK,QAAQ,IAAIG,KAAK,IAAIA,KAAK,CAACI,QAAQ,IAAI,IAAI,EAAE;MACrDH,EAAE,CAACI,YAAY,CAAC,UAAU,EAAEL,KAAK,CAACI,QAAQ,CAAC;IAC/C;IACA,OAAOH,EAAE;EACb,CAAC;EACDK,UAAU,EAAE,SAAAA,WAAAC,IAAI;IAAA,OAAIxB,GAAG,CAACyB,cAAc,CAACD,IAAI,CAAC;EAAA;EAC5CE,aAAa,EAAE,SAAAA,cAAAF,IAAI;IAAA,OAAIxB,GAAG,CAAC0B,aAAa,CAACF,IAAI,CAAC;EAAA;EAC9CG,OAAO,EAAE,SAAAA,QAACC,IAAI,EAAEJ,IAAI,EAAK;IACrBI,IAAI,CAACC,SAAS,GAAGL,IAAI;EACzB,CAAC;EACDM,cAAc,EAAE,SAAAA,eAACZ,EAAE,EAAEM,IAAI,EAAK;IAC1BN,EAAE,CAACa,WAAW,GAAGP,IAAI;EACzB,CAAC;EACDb,UAAU,EAAE,SAAAA,WAAAiB,IAAI;IAAA,OAAIA,IAAI,CAACjB,UAAU;EAAA;EACnCqB,WAAW,EAAE,SAAAA,YAAAJ,IAAI;IAAA,OAAIA,IAAI,CAACI,WAAW;EAAA;EACrCC,aAAa,EAAE,SAAAA,cAAAC,QAAQ;IAAA,OAAIlC,GAAG,CAACiC,aAAa,CAACC,QAAQ,CAAC;EAAA;EACtDC,UAAU,WAAAA,WAACjB,EAAE,EAAEkB,EAAE,EAAE;IACflB,EAAE,CAACI,YAAY,CAACc,EAAE,EAAE,EAAE,CAAC;EAC3B,CAAC;EACDC,SAAS,WAAAA,UAACnB,EAAE,EAAE;IACV,IAAMoB,MAAM,GAAGpB,EAAE,CAACmB,SAAS,CAAC,IAAI,CAAC;IACjC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,YAAYnB,EAAE,EAAE;MAChBoB,MAAM,CAACC,MAAM,GAAGrB,EAAE,CAACqB,MAAM;IAC7B;IACA,OAAOD,MAAM;EACjB,CAAC;EACD;EACA;EACA;EACA;EACAE,mBAAmB,WAAAA,oBAACC,OAAO,EAAElC,MAAM,EAAEC,MAAM,EAAEO,KAAK,EAAE;IAChD;IACA,IAAM2B,MAAM,GAAGlC,MAAM,GAAGA,MAAM,CAACmC,eAAe,GAAGpC,MAAM,CAACqC,SAAS;IACjE,IAAIC,QAAQ,GAAG3C,mBAAmB,CAAC4C,GAAG,CAACL,OAAO,CAAC;IAC/C,IAAI,CAACI,QAAQ,EAAE;MACX,IAAME,CAAC,GAAG/C,GAAG,CAACa,aAAa,CAAC,UAAU,CAAC;MACvCkC,CAAC,CAACC,SAAS,GAAGjC,KAAK,WAAAkC,MAAA,CAAWR,OAAO,cAAWA,OAAO;MACvDI,QAAQ,GAAGE,CAAC,CAACN,OAAO;MACpB,IAAI1B,KAAK,EAAE;QACP;QACA,IAAMmC,OAAO,GAAGL,QAAQ,CAACM,UAAU;QACnC,OAAOD,OAAO,CAACC,UAAU,EAAE;UACvBN,QAAQ,CAACO,WAAW,CAACF,OAAO,CAACC,UAAU,CAAC;QAC5C;QACAN,QAAQ,CAACjC,WAAW,CAACsC,OAAO,CAAC;MACjC;MACAhD,mBAAmB,CAACmD,GAAG,CAACZ,OAAO,EAAEI,QAAQ,CAAC;IAC9C;IACAtC,MAAM,CAACE,YAAY,CAACoC,QAAQ,CAACR,SAAS,CAAC,IAAI,CAAC,EAAE7B,MAAM,CAAC;IACrD,OAAO;IACH;IACAkC,MAAM,GAAGA,MAAM,CAACV,WAAW,GAAGzB,MAAM,CAAC4C,UAAU;IAC/C;IACA3C,MAAM,GAAGA,MAAM,CAACmC,eAAe,GAAGpC,MAAM,CAACqC,SAAS,CACrD;EACL;AACJ,CAAC;;AAED;AACA;AACA,SAASU,UAAUA,CAACpC,EAAE,EAAEqC,KAAK,EAAExC,KAAK,EAAE;EAClC;EACA;EACA;EACA,IAAMyC,iBAAiB,GAAGtC,EAAE,CAACuC,IAAI;EACjC,IAAID,iBAAiB,EAAE;IACnBD,KAAK,GAAG,CAACA,KAAK,IAAIA,KAAK,EAAAN,MAAA,CAAAS,kBAAA,CAAKF,iBAAiB,KAAAE,kBAAA,CAAQF,iBAAiB,CAAC,EAAEG,IAAI,CAAC,GAAG,CAAC;EACtF;EACA,IAAIJ,KAAK,IAAI,IAAI,EAAE;IACfrC,EAAE,CAAC0C,eAAe,CAAC,OAAO,CAAC;EAC/B,CAAC,MACI,IAAI7C,KAAK,EAAE;IACZG,EAAE,CAACI,YAAY,CAAC,OAAO,EAAEiC,KAAK,CAAC;EACnC,CAAC,MACI;IACDrC,EAAE,CAAC2C,SAAS,GAAGN,KAAK;EACxB;AACJ;AAEA,SAASO,UAAUA,CAAC5C,EAAE,EAAE6C,IAAI,EAAEC,IAAI,EAAE;EAChC,IAAMC,KAAK,GAAG/C,EAAE,CAAC+C,KAAK;EACtB,IAAMC,WAAW,GAAGvF,QAAQ,CAACqF,IAAI,CAAC;EAClC,IAAIA,IAAI,IAAI,CAACE,WAAW,EAAE;IACtB,KAAK,IAAMC,GAAG,IAAIH,IAAI,EAAE;MACpBI,QAAQ,CAACH,KAAK,EAAEE,GAAG,EAAEH,IAAI,CAACG,GAAG,CAAC,CAAC;IACnC;IACA,IAAIJ,IAAI,IAAI,CAACpF,QAAQ,CAACoF,IAAI,CAAC,EAAE;MACzB,KAAK,IAAMI,IAAG,IAAIJ,IAAI,EAAE;QACpB,IAAIC,IAAI,CAACG,IAAG,CAAC,IAAI,IAAI,EAAE;UACnBC,QAAQ,CAACH,KAAK,EAAEE,IAAG,EAAE,EAAE,CAAC;QAC5B;MACJ;IACJ;EACJ,CAAC,MACI;IACD,IAAME,cAAc,GAAGJ,KAAK,CAACK,OAAO;IACpC,IAAIJ,WAAW,EAAE;MACb,IAAIH,IAAI,KAAKC,IAAI,EAAE;QACfC,KAAK,CAACM,OAAO,GAAGP,IAAI;MACxB;IACJ,CAAC,MACI,IAAID,IAAI,EAAE;MACX7C,EAAE,CAAC0C,eAAe,CAAC,OAAO,CAAC;IAC/B;IACA;IACA;IACA;IACA,IAAI,MAAM,IAAI1C,EAAE,EAAE;MACd+C,KAAK,CAACK,OAAO,GAAGD,cAAc;IAClC;EACJ;AACJ;AACA,IAAMG,WAAW,GAAG,gBAAgB;AACpC,SAASJ,QAAQA,CAACH,KAAK,EAAEQ,IAAI,EAAEC,GAAG,EAAE;EAChC,IAAI9F,OAAO,CAAC8F,GAAG,CAAC,EAAE;IACdA,GAAG,CAACC,OAAO,CAAC,UAAAC,CAAC;MAAA,OAAIR,QAAQ,CAACH,KAAK,EAAEQ,IAAI,EAAEG,CAAC,CAAC;IAAA,EAAC;EAC9C,CAAC,MACI;IACD,IAAIH,IAAI,CAACI,UAAU,CAAC,IAAI,CAAC,EAAE;MACvB;MACAZ,KAAK,CAACa,WAAW,CAACL,IAAI,EAAEC,GAAG,CAAC;IAChC,CAAC,MACI;MACD,IAAMK,QAAQ,GAAGC,UAAU,CAACf,KAAK,EAAEQ,IAAI,CAAC;MACxC,IAAID,WAAW,CAACS,IAAI,CAACP,GAAG,CAAC,EAAE;QACvB;QACAT,KAAK,CAACa,WAAW,CAACjG,SAAS,CAACkG,QAAQ,CAAC,EAAEL,GAAG,CAACQ,OAAO,CAACV,WAAW,EAAE,EAAE,CAAC,EAAE,WAAW,CAAC;MACrF,CAAC,MACI;QACDP,KAAK,CAACc,QAAQ,CAAC,GAAGL,GAAG;MACzB;IACJ;EACJ;AACJ;AACA,IAAMS,QAAQ,GAAG,CAAC,QAAQ,EAAE,KAAK,EAAE,IAAI,CAAC;AACxC,IAAMC,WAAW,GAAG,CAAC,CAAC;AACtB,SAASJ,UAAUA,CAACf,KAAK,EAAEoB,OAAO,EAAE;EAChC,IAAMC,MAAM,GAAGF,WAAW,CAACC,OAAO,CAAC;EACnC,IAAIC,MAAM,EAAE;IACR,OAAOA,MAAM;EACjB;EACA,IAAIb,IAAI,GAAGrH,QAAQ,CAACiI,OAAO,CAAC;EAC5B,IAAIZ,IAAI,KAAK,QAAQ,IAAIA,IAAI,IAAIR,KAAK,EAAE;IACpC,OAAQmB,WAAW,CAACC,OAAO,CAAC,GAAGZ,IAAI;EACvC;EACAA,IAAI,GAAG3F,UAAU,CAAC2F,IAAI,CAAC;EACvB,KAAK,IAAIc,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,QAAQ,CAACK,MAAM,EAAED,CAAC,EAAE,EAAE;IACtC,IAAMR,QAAQ,GAAGI,QAAQ,CAACI,CAAC,CAAC,GAAGd,IAAI;IACnC,IAAIM,QAAQ,IAAId,KAAK,EAAE;MACnB,OAAQmB,WAAW,CAACC,OAAO,CAAC,GAAGN,QAAQ;IAC3C;EACJ;EACA,OAAOM,OAAO;AAClB;AAEA,IAAMI,OAAO,GAAG,8BAA8B;AAC9C,SAASC,SAASA,CAACxE,EAAE,EAAEiD,GAAG,EAAEZ,KAAK,EAAExC,KAAK,EAAE4E,QAAQ,EAAE;EAChD,IAAI5E,KAAK,IAAIoD,GAAG,CAACU,UAAU,CAAC,QAAQ,CAAC,EAAE;IACnC,IAAItB,KAAK,IAAI,IAAI,EAAE;MACfrC,EAAE,CAAC0E,iBAAiB,CAACH,OAAO,EAAEtB,GAAG,CAAC0B,KAAK,CAAC,CAAC,EAAE1B,GAAG,CAACqB,MAAM,CAAC,CAAC;IAC3D,CAAC,MACI;MACDtE,EAAE,CAAC4E,cAAc,CAACL,OAAO,EAAEtB,GAAG,EAAEZ,KAAK,CAAC;IAC1C;EACJ,CAAC,MACI;IACD;IACA;IACA,IAAMwC,SAAS,GAAGhH,oBAAoB,CAACoF,GAAG,CAAC;IAC3C,IAAIZ,KAAK,IAAI,IAAI,IAAKwC,SAAS,IAAI,CAAC/G,kBAAkB,CAACuE,KAAK,CAAE,EAAE;MAC5DrC,EAAE,CAAC0C,eAAe,CAACO,GAAG,CAAC;IAC3B,CAAC,MACI;MACDjD,EAAE,CAACI,YAAY,CAAC6C,GAAG,EAAE4B,SAAS,GAAG,EAAE,GAAGxC,KAAK,CAAC;IAChD;EACJ;AACJ;;AAEA;AACA;AACA,SAASyC,YAAYA,CAAC9E,EAAE,EAAEiD,GAAG,EAAEZ,KAAK;AACpC;AACA;AACA;AACA0C,YAAY,EAAEC,eAAe,EAAEC,cAAc,EAAEC,eAAe,EAAE;EAC5D,IAAIjC,GAAG,KAAK,WAAW,IAAIA,GAAG,KAAK,aAAa,EAAE;IAC9C,IAAI8B,YAAY,EAAE;MACdG,eAAe,CAACH,YAAY,EAAEC,eAAe,EAAEC,cAAc,CAAC;IAClE;IACAjF,EAAE,CAACiD,GAAG,CAAC,GAAGZ,KAAK,IAAI,IAAI,GAAG,EAAE,GAAGA,KAAK;IACpC;EACJ;EACA,IAAIY,GAAG,KAAK,OAAO,IACfjD,EAAE,CAACmF,OAAO,KAAK,UAAU;EACzB;EACA,CAACnF,EAAE,CAACmF,OAAO,CAACC,QAAQ,CAAC,GAAG,CAAC,EAAE;IAC3B;IACA;IACApF,EAAE,CAACqB,MAAM,GAAGgB,KAAK;IACjB,IAAMgD,QAAQ,GAAGhD,KAAK,IAAI,IAAI,GAAG,EAAE,GAAGA,KAAK;IAC3C,IAAIrC,EAAE,CAACqC,KAAK,KAAKgD,QAAQ;IACrB;IACA;IACA;IACArF,EAAE,CAACmF,OAAO,KAAK,QAAQ,EAAE;MACzBnF,EAAE,CAACqC,KAAK,GAAGgD,QAAQ;IACvB;IACA,IAAIhD,KAAK,IAAI,IAAI,EAAE;MACfrC,EAAE,CAAC0C,eAAe,CAACO,GAAG,CAAC;IAC3B;IACA;EACJ;EACA,IAAIZ,KAAK,KAAK,EAAE,IAAIA,KAAK,IAAI,IAAI,EAAE;IAC/B,IAAMiD,IAAI,GAAAC,OAAA,CAAUvF,EAAE,CAACiD,GAAG,CAAC;IAC3B,IAAIqC,IAAI,KAAK,SAAS,EAAE;MACpB;MACAtF,EAAE,CAACiD,GAAG,CAAC,GAAGnF,kBAAkB,CAACuE,KAAK,CAAC;MACnC;IACJ,CAAC,MACI,IAAIA,KAAK,IAAI,IAAI,IAAIiD,IAAI,KAAK,QAAQ,EAAE;MACzC;MACAtF,EAAE,CAACiD,GAAG,CAAC,GAAG,EAAE;MACZjD,EAAE,CAAC0C,eAAe,CAACO,GAAG,CAAC;MACvB;IACJ,CAAC,MACI,IAAIqC,IAAI,KAAK,QAAQ,EAAE;MACxB;MACA;MACA,IAAI;QACAtF,EAAE,CAACiD,GAAG,CAAC,GAAG,CAAC;MACf,CAAC,CACD,OAAOuC,EAAE,EAAE,CAAE;MACbxF,EAAE,CAAC0C,eAAe,CAACO,GAAG,CAAC;MACvB;IACJ;EACJ;EACA;EACA,IAAI;IACAjD,EAAE,CAACiD,GAAG,CAAC,GAAGZ,KAAK;EACnB,CAAC,CACD,OAAOoD,CAAC,EAAE;IACN,IAAKC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAG;MACzCzJ,IAAI,CAAC,yBAAA4F,MAAA,CAAwBkB,GAAG,aAAAlB,MAAA,CAAS/B,EAAE,CAACmF,OAAO,CAACU,WAAW,CAAC,CAAC,oBAAA9D,MAAA,CACpDM,KAAK,iBAAc,EAAEoD,CAAC,CAAC;IACxC;EACJ;AACJ;;AAEA;AACA,IAAIK,OAAO,GAAGC,IAAI,CAACC,GAAG;AACtB,IAAIC,kBAAkB,GAAG,KAAK;AAC9B,IAAI,OAAOC,MAAM,KAAK,WAAW,EAAE;EAC/B;EACA;EACA;EACA;EACA,IAAIJ,OAAO,CAAC,CAAC,GAAG/G,QAAQ,CAACoH,WAAW,CAAC,OAAO,CAAC,CAACC,SAAS,EAAE;IACrD;IACA;IACA;IACAN,OAAO,GAAG,SAAAA,QAAA;MAAA,OAAMO,WAAW,CAACL,GAAG,CAAC,CAAC;IAAA;EACrC;EACA;EACA;EACA,IAAMM,OAAO,GAAGC,SAAS,CAACC,SAAS,CAACC,KAAK,CAAC,iBAAiB,CAAC;EAC5DR,kBAAkB,GAAG,CAAC,EAAEK,OAAO,IAAII,MAAM,CAACJ,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;AAChE;AACA;AACA;AACA,IAAIK,SAAS,GAAG,CAAC;AACjB,IAAMC,CAAC,GAAGC,OAAO,CAACC,OAAO,CAAC,CAAC;AAC3B,IAAMC,KAAK,GAAG,SAARA,KAAKA,CAAA,EAAS;EAChBJ,SAAS,GAAG,CAAC;AACjB,CAAC;AACD,IAAMK,MAAM,GAAG,SAATA,MAAMA,CAAA;EAAA,OAASL,SAAS,KAAKC,CAAC,CAACK,IAAI,CAACF,KAAK,CAAC,EAAGJ,SAAS,GAAGb,OAAO,CAAC,CAAE,CAAC;AAAA;AAC1E,SAASoB,gBAAgBA,CAAClH,EAAE,EAAEmH,KAAK,EAAEC,OAAO,EAAEC,OAAO,EAAE;EACnDrH,EAAE,CAACkH,gBAAgB,CAACC,KAAK,EAAEC,OAAO,EAAEC,OAAO,CAAC;AAChD;AACA,SAASC,mBAAmBA,CAACtH,EAAE,EAAEmH,KAAK,EAAEC,OAAO,EAAEC,OAAO,EAAE;EACtDrH,EAAE,CAACsH,mBAAmB,CAACH,KAAK,EAAEC,OAAO,EAAEC,OAAO,CAAC;AACnD;AACA,SAASE,UAAUA,CAACvH,EAAE,EAAEmE,OAAO,EAAEqD,SAAS,EAAEC,SAAS,EAAmB;EAAA,IAAjBhD,QAAQ,GAAAiD,SAAA,CAAApD,MAAA,QAAAoD,SAAA,QAAAxH,SAAA,GAAAwH,SAAA,MAAG,IAAI;EAClE;EACA,IAAMC,QAAQ,GAAG3H,EAAE,CAAC4H,IAAI,KAAK5H,EAAE,CAAC4H,IAAI,GAAG,CAAC,CAAC,CAAC;EAC1C,IAAMC,eAAe,GAAGF,QAAQ,CAACxD,OAAO,CAAC;EACzC,IAAIsD,SAAS,IAAII,eAAe,EAAE;IAC9B;IACAA,eAAe,CAACxF,KAAK,GAAGoF,SAAS;EACrC,CAAC,MACI;IACD,IAAAK,UAAA,GAAwBC,SAAS,CAAC5D,OAAO,CAAC;MAAA6D,WAAA,GAAAC,cAAA,CAAAH,UAAA;MAAnCvE,IAAI,GAAAyE,WAAA;MAAEX,OAAO,GAAAW,WAAA;IACpB,IAAIP,SAAS,EAAE;MACX;MACA,IAAMS,OAAO,GAAIP,QAAQ,CAACxD,OAAO,CAAC,GAAGgE,aAAa,CAACV,SAAS,EAAEhD,QAAQ,CAAE;MACxEyC,gBAAgB,CAAClH,EAAE,EAAEuD,IAAI,EAAE2E,OAAO,EAAEb,OAAO,CAAC;IAChD,CAAC,MACI,IAAIQ,eAAe,EAAE;MACtB;MACAP,mBAAmB,CAACtH,EAAE,EAAEuD,IAAI,EAAEsE,eAAe,EAAER,OAAO,CAAC;MACvDM,QAAQ,CAACxD,OAAO,CAAC,GAAGjE,SAAS;IACjC;EACJ;AACJ;AACA,IAAMkI,iBAAiB,GAAG,2BAA2B;AACrD,SAASL,SAASA,CAACxE,IAAI,EAAE;EACrB,IAAI8D,OAAO;EACX,IAAIe,iBAAiB,CAACrE,IAAI,CAACR,IAAI,CAAC,EAAE;IAC9B8D,OAAO,GAAG,CAAC,CAAC;IACZ,IAAIgB,CAAC;IACL,OAAQA,CAAC,GAAG9E,IAAI,CAACkD,KAAK,CAAC2B,iBAAiB,CAAC,EAAG;MACxC7E,IAAI,GAAGA,IAAI,CAACoB,KAAK,CAAC,CAAC,EAAEpB,IAAI,CAACe,MAAM,GAAG+D,CAAC,CAAC,CAAC,CAAC,CAAC/D,MAAM,CAAC;MAC/C+C,OAAO,CAACgB,CAAC,CAAC,CAAC,CAAC,CAACxC,WAAW,CAAC,CAAC,CAAC,GAAG,IAAI;IACtC;EACJ;EACA,OAAO,CAAClI,SAAS,CAAC4F,IAAI,CAACoB,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE0C,OAAO,CAAC;AAC9C;AACA,SAASc,aAAaA,CAACG,YAAY,EAAE7D,QAAQ,EAAE;EAC3C,IAAMyD,OAAO,GAAG,SAAVA,OAAOA,CAAIzC,CAAC,EAAK;IACnB;IACA;IACA;IACA;IACA;IACA;IACA,IAAMW,SAAS,GAAGX,CAAC,CAACW,SAAS,IAAIN,OAAO,CAAC,CAAC;IAC1C,IAAIG,kBAAkB,IAAIG,SAAS,IAAI8B,OAAO,CAACK,QAAQ,GAAG,CAAC,EAAE;MACzDnM,0BAA0B,CAACoM,6BAA6B,CAAC/C,CAAC,EAAEyC,OAAO,CAAC7F,KAAK,CAAC,EAAEoC,QAAQ,EAAE,CAAC,CAAC,4BAA4B,CAACgB,CAAC,CAAC,CAAC;IAC5H;EACJ,CAAC;EACDyC,OAAO,CAAC7F,KAAK,GAAGiG,YAAY;EAC5BJ,OAAO,CAACK,QAAQ,GAAGvB,MAAM,CAAC,CAAC;EAC3B,OAAOkB,OAAO;AAClB;AACA,SAASM,6BAA6BA,CAAC/C,CAAC,EAAEpD,KAAK,EAAE;EAC7C,IAAI3E,OAAO,CAAC2E,KAAK,CAAC,EAAE;IAChB,IAAMoG,YAAY,GAAGhD,CAAC,CAACiD,wBAAwB;IAC/CjD,CAAC,CAACiD,wBAAwB,GAAG,YAAM;MAC/BD,YAAY,CAACE,IAAI,CAAClD,CAAC,CAAC;MACpBA,CAAC,CAACmD,QAAQ,GAAG,IAAI;IACrB,CAAC;IACD,OAAOvG,KAAK,CAACwG,GAAG,CAAC,UAAAC,EAAE;MAAA,OAAI,UAACrD,CAAC;QAAA,OAAK,CAACA,CAAC,CAACmD,QAAQ,IAAIE,EAAE,CAACrD,CAAC,CAAC;MAAA;IAAA,EAAC;EACvD,CAAC,MACI;IACD,OAAOpD,KAAK;EAChB;AACJ;AAEA,IAAM0G,UAAU,GAAG,UAAU;AAC7B,IAAMC,SAAS,GAAG,SAAZA,SAASA,CAAIhJ,EAAE,EAAEiD,GAAG,EAAEuE,SAAS,EAAEC,SAAS,EAAoF;EAAA,IAAlF5H,KAAK,GAAA6H,SAAA,CAAApD,MAAA,QAAAoD,SAAA,QAAAxH,SAAA,GAAAwH,SAAA,MAAG,KAAK;EAAA,IAAE3C,YAAY,GAAA2C,SAAA,CAAApD,MAAA,OAAAoD,SAAA,MAAAxH,SAAA;EAAA,IAAE8E,eAAe,GAAA0C,SAAA,CAAApD,MAAA,OAAAoD,SAAA,MAAAxH,SAAA;EAAA,IAAE+E,cAAc,GAAAyC,SAAA,CAAApD,MAAA,OAAAoD,SAAA,MAAAxH,SAAA;EAAA,IAAEgF,eAAe,GAAAwC,SAAA,CAAApD,MAAA,OAAAoD,SAAA,MAAAxH,SAAA;EAC3H,IAAI+C,GAAG,KAAK,OAAO,EAAE;IACjBb,UAAU,CAACpC,EAAE,EAAEyH,SAAS,EAAE5H,KAAK,CAAC;EACpC,CAAC,MACI,IAAIoD,GAAG,KAAK,OAAO,EAAE;IACtBL,UAAU,CAAC5C,EAAE,EAAEwH,SAAS,EAAEC,SAAS,CAAC;EACxC,CAAC,MACI,IAAI1J,IAAI,CAACkF,GAAG,CAAC,EAAE;IAChB;IACA,IAAI,CAACjF,eAAe,CAACiF,GAAG,CAAC,EAAE;MACvBsE,UAAU,CAACvH,EAAE,EAAEiD,GAAG,EAAEuE,SAAS,EAAEC,SAAS,EAAEzC,eAAe,CAAC;IAC9D;EACJ,CAAC,MACI,IAAI/B,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,IACfA,GAAG,GAAGA,GAAG,CAAC0B,KAAK,CAAC,CAAC,CAAC,EAAG,IAAI,IAC3B1B,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,IACRA,GAAG,GAAGA,GAAG,CAAC0B,KAAK,CAAC,CAAC,CAAC,EAAG,KAAK,IAC5BsE,eAAe,CAACjJ,EAAE,EAAEiD,GAAG,EAAEwE,SAAS,EAAE5H,KAAK,CAAC,EAAE;IAClDiF,YAAY,CAAC9E,EAAE,EAAEiD,GAAG,EAAEwE,SAAS,EAAE1C,YAAY,EAAEC,eAAe,EAAEC,cAAc,EAAEC,eAAe,CAAC;EACpG,CAAC,MACI;IACD;IACA;IACA;IACA;IACA,IAAIjC,GAAG,KAAK,YAAY,EAAE;MACtBjD,EAAE,CAACkJ,UAAU,GAAGzB,SAAS;IAC7B,CAAC,MACI,IAAIxE,GAAG,KAAK,aAAa,EAAE;MAC5BjD,EAAE,CAACmJ,WAAW,GAAG1B,SAAS;IAC9B;IACAjD,SAAS,CAACxE,EAAE,EAAEiD,GAAG,EAAEwE,SAAS,EAAE5H,KAAK,CAAC;EACxC;AACJ,CAAC;AACD,SAASoJ,eAAeA,CAACjJ,EAAE,EAAEiD,GAAG,EAAEZ,KAAK,EAAExC,KAAK,EAAE;EAC5C,IAAIA,KAAK,EAAE;IACP;IACA;IACA,IAAIoD,GAAG,KAAK,WAAW,IAAIA,GAAG,KAAK,aAAa,EAAE;MAC9C,OAAO,IAAI;IACf;IACA;IACA,IAAIA,GAAG,IAAIjD,EAAE,IAAI+I,UAAU,CAAChF,IAAI,CAACd,GAAG,CAAC,IAAIhF,UAAU,CAACoE,KAAK,CAAC,EAAE;MACxD,OAAO,IAAI;IACf;IACA,OAAO,KAAK;EAChB;EACA;EACA;EACA;EACA;EACA;EACA;EACA,IAAIY,GAAG,KAAK,YAAY,IAAIA,GAAG,KAAK,WAAW,EAAE;IAC7C,OAAO,KAAK;EAChB;EACA;EACA;EACA,IAAIA,GAAG,KAAK,MAAM,EAAE;IAChB,OAAO,KAAK;EAChB;EACA;EACA,IAAIA,GAAG,KAAK,MAAM,IAAIjD,EAAE,CAACmF,OAAO,KAAK,OAAO,EAAE;IAC1C,OAAO,KAAK;EAChB;EACA;EACA,IAAIlC,GAAG,KAAK,MAAM,IAAIjD,EAAE,CAACmF,OAAO,KAAK,UAAU,EAAE;IAC7C,OAAO,KAAK;EAChB;EACA;EACA,IAAI4D,UAAU,CAAChF,IAAI,CAACd,GAAG,CAAC,IAAIxF,QAAQ,CAAC4E,KAAK,CAAC,EAAE;IACzC,OAAO,KAAK;EAChB;EACA,OAAOY,GAAG,IAAIjD,EAAE;AACpB;AAEA,SAASoJ,mBAAmBA,CAAC/B,OAAO,EAAEgC,MAAM,EAAE;EAC1C,IAAMC,IAAI,GAAGjN,eAAe,CAACgL,OAAO,CAAC;EAAC,IAChCkC,gBAAgB,0BAAAC,WAAA;IAAAC,SAAA,CAAAF,gBAAA,EAAAC,WAAA;IAAA,IAAAE,MAAA,GAAAC,YAAA,CAAAJ,gBAAA;IAClB,SAAAA,iBAAYK,YAAY,EAAE;MAAAC,eAAA,OAAAN,gBAAA;MAAA,OAAAG,MAAA,CAAAf,IAAA,OAChBW,IAAI,EAAEM,YAAY,EAAEP,MAAM;IACpC;IAAC,OAAAS,YAAA,CAAAP,gBAAA;EAAA,EAH0BQ,UAAU;EAKzCR,gBAAgB,CAACS,GAAG,GAAGV,IAAI;EAC3B,OAAOC,gBAAgB;AAC3B;AACA,IAAMU,sBAAsB,GAAI,SAA1BA,sBAAsBA,CAAK5C,OAAO,EAAK;EACzC;EACA,OAAO+B,mBAAmB,CAAC/B,OAAO,EAAE6C,OAAO,CAAC;AAChD,CAAE;AACF,IAAMC,SAAS,GAAI,OAAOC,WAAW,KAAK,WAAW,GAAGA,WAAW;EAAA,SAAAC,OAAA;IAAAR,eAAA,OAAAQ,MAAA;EAAA;EAAA,OAAAP,YAAA,CAAAO,MAAA;AAAA,GACjE;AAAC,IACGN,UAAU,0BAAAO,UAAA;EAAAb,SAAA,CAAAM,UAAA,EAAAO,UAAA;EAAA,IAAAC,OAAA,GAAAZ,YAAA,CAAAI,UAAA;EACZ,SAAAA,WAAYS,IAAI,EAAwB;IAAA,IAAAC,KAAA;IAAA,IAAtBC,MAAM,GAAAhD,SAAA,CAAApD,MAAA,QAAAoD,SAAA,QAAAxH,SAAA,GAAAwH,SAAA,MAAG,CAAC,CAAC;IAAA,IAAEwC,OAAO,GAAAxC,SAAA,CAAApD,MAAA,OAAAoD,SAAA,MAAAxH,SAAA;IAAA2J,eAAA,OAAAE,UAAA;IAClCU,KAAA,GAAAF,OAAA,CAAA5B,IAAA;IACA8B,KAAA,CAAKD,IAAI,GAAGA,IAAI;IAChBC,KAAA,CAAKC,MAAM,GAAGA,MAAM;IACpB;AACR;AACA;IACQD,KAAA,CAAKE,SAAS,GAAG,IAAI;IACrBF,KAAA,CAAKG,UAAU,GAAG,KAAK;IACvBH,KAAA,CAAKI,SAAS,GAAG,KAAK;IACtBJ,KAAA,CAAKK,YAAY,GAAG,IAAI;IACxB,IAAIL,KAAA,CAAKM,UAAU,IAAIb,OAAO,EAAE;MAC5BA,OAAO,CAACO,KAAA,CAAKO,YAAY,CAAC,CAAC,EAAEP,KAAA,CAAKM,UAAU,CAAC;IACjD,CAAC,MACI;MACD,IAAKrF,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAK6E,KAAA,CAAKM,UAAU,EAAE;QAC5D5O,IAAI,CAAC,8HACuD,CAAC;MACjE;MACAsO,KAAA,CAAKQ,YAAY,CAAC;QAAEC,IAAI,EAAE;MAAO,CAAC,CAAC;IACvC;IAAC,OAAAT,KAAA;EACL;EAACX,YAAA,CAAAC,UAAA;IAAA9G,GAAA;IAAAZ,KAAA,EACD,SAAA8I,kBAAA,EAAoB;MAChB,IAAI,CAACP,UAAU,GAAG,IAAI;MACtB,IAAI,CAAC,IAAI,CAACD,SAAS,EAAE;QACjB,IAAI,CAACS,WAAW,CAAC,CAAC;MACtB;IACJ;EAAC;IAAAnI,GAAA;IAAAZ,KAAA,EACD,SAAAgJ,qBAAA,EAAuB;MAAA,IAAAC,MAAA;MACnB,IAAI,CAACV,UAAU,GAAG,KAAK;MACvBtO,QAAQ,CAAC,YAAM;QACX,IAAI,CAACgP,MAAI,CAACV,UAAU,EAAE;UAClBW,MAAM,CAAC,IAAI,EAAED,MAAI,CAACP,UAAU,CAAC;UAC7BO,MAAI,CAACX,SAAS,GAAG,IAAI;QACzB;MACJ,CAAC,CAAC;IACN;IACA;AACJ;AACA;EAFI;IAAA1H,GAAA;IAAAZ,KAAA,EAGA,SAAA+I,YAAA,EAAc;MAAA,IAAAI,MAAA;MACV,IAAI,IAAI,CAACX,SAAS,EAAE;QAChB;MACJ;MACA,IAAI,CAACA,SAAS,GAAG,IAAI;MACrB;MACA,KAAK,IAAIxG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACoH,UAAU,CAACnH,MAAM,EAAED,CAAC,EAAE,EAAE;QAC7C,IAAI,CAACqH,QAAQ,CAAC,IAAI,CAACD,UAAU,CAACpH,CAAC,CAAC,CAACd,IAAI,CAAC;MAC1C;MACA;MACA,IAAIoI,gBAAgB,CAAC,UAAAC,SAAS,EAAI;QAAA,IAAAC,SAAA,GAAAC,0BAAA,CACdF,SAAS;UAAAG,KAAA;QAAA;UAAzB,KAAAF,SAAA,CAAAG,CAAA,MAAAD,KAAA,GAAAF,SAAA,CAAAI,CAAA,IAAAC,IAAA,GAA2B;YAAA,IAAhB7D,CAAC,GAAA0D,KAAA,CAAA1J,KAAA;YACRmJ,MAAI,CAACE,QAAQ,CAACrD,CAAC,CAAC8D,aAAa,CAAC;UAClC;QAAC,SAAAC,GAAA;UAAAP,SAAA,CAAApG,CAAA,CAAA2G,GAAA;QAAA;UAAAP,SAAA,CAAAQ,CAAA;QAAA;MACL,CAAC,CAAC,CAACC,OAAO,CAAC,IAAI,EAAE;QAAEb,UAAU,EAAE;MAAK,CAAC,CAAC;MACtC,IAAM3E,OAAO,GAAG,SAAVA,OAAOA,CAAIkD,GAAG,EAAK;QACrB,IAAQjK,KAAK,GAAaiK,GAAG,CAArBjK,KAAK;UAAEwM,MAAM,GAAKvC,GAAG,CAAduC,MAAM;QACrB,IAAMC,UAAU,GAAG,CAAC9O,OAAO,CAACqC,KAAK,CAAC;QAClC,IAAM0M,OAAO,GAAG1M,KAAK,GAAIyM,UAAU,GAAGE,MAAM,CAACC,IAAI,CAAC5M,KAAK,CAAC,GAAGA,KAAK,GAAI,EAAE;QACtE;QACA,IAAI6M,WAAW;QACf,IAAIJ,UAAU,EAAE;UACZ,KAAK,IAAMvJ,GAAG,IAAIuI,MAAI,CAACd,MAAM,EAAE;YAC3B,IAAMmC,GAAG,GAAG9M,KAAK,CAACkD,GAAG,CAAC;YACtB,IAAI4J,GAAG,KAAKnG,MAAM,IAAKmG,GAAG,IAAIA,GAAG,CAACvH,IAAI,KAAKoB,MAAO,EAAE;cAChD8E,MAAI,CAACd,MAAM,CAACzH,GAAG,CAAC,GAAG/E,QAAQ,CAACsN,MAAI,CAACd,MAAM,CAACzH,GAAG,CAAC,CAAC;cAC7C,CAAC2J,WAAW,KAAKA,WAAW,GAAGF,MAAM,CAACI,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE7J,GAAG,CAAC,GAAG,IAAI;YACpE;UACJ;QACJ;QACAuI,MAAI,CAACV,YAAY,GAAG8B,WAAW;QAC/B;QACA,SAAAG,GAAA,MAAAC,YAAA,GAAkBN,MAAM,CAACC,IAAI,CAACnB,MAAI,CAAC,EAAAuB,GAAA,GAAAC,YAAA,CAAA1I,MAAA,EAAAyI,GAAA,IAAE;UAAhC,IAAM9J,KAAG,GAAA+J,YAAA,CAAAD,GAAA;UACV,IAAI9J,KAAG,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;YAChBuI,MAAI,CAACyB,QAAQ,CAAChK,KAAG,EAAEuI,MAAI,CAACvI,KAAG,CAAC,EAAE,IAAI,EAAE,KAAK,CAAC;UAC9C;QACJ;QACA;QAAA,IAAAiK,UAAA,GAAApB,0BAAA,CACkBW,OAAO,CAAC5D,GAAG,CAAC1K,UAAU,CAAC;UAAAgP,MAAA;QAAA;UAAA,IAAAC,KAAA,YAAAA,MAAA,EAAE;YAAA,IAAhCnK,GAAG,GAAAkK,MAAA,CAAA9K,KAAA;YACVqK,MAAM,CAACW,cAAc,CAAC7B,MAAI,EAAEvI,GAAG,EAAE;cAC7BrB,GAAG,WAAAA,IAAA,EAAG;gBACF,OAAO,IAAI,CAAC0L,QAAQ,CAACrK,GAAG,CAAC;cAC7B,CAAC;cACDd,GAAG,WAAAA,IAACqB,GAAG,EAAE;gBACL,IAAI,CAACyJ,QAAQ,CAAChK,GAAG,EAAEO,GAAG,CAAC;cAC3B;YACJ,CAAC,CAAC;UACN,CAAC;UATD,KAAA0J,UAAA,CAAAlB,CAAA,MAAAmB,MAAA,GAAAD,UAAA,CAAAjB,CAAA,IAAAC,IAAA;YAAAkB,KAAA;UAAA;UAUA;QAAA,SAAAhB,GAAA;UAAAc,UAAA,CAAAzH,CAAA,CAAA2G,GAAA;QAAA;UAAAc,UAAA,CAAAb,CAAA;QAAA;QACAb,MAAI,CAAC+B,YAAY,CAAChB,MAAM,CAAC;QACzB;QACAf,MAAI,CAACgC,OAAO,CAAC,CAAC;MAClB,CAAC;MACD,IAAMC,QAAQ,GAAG,IAAI,CAACjD,IAAI,CAACkD,aAAa;MACxC,IAAID,QAAQ,EAAE;QACVA,QAAQ,CAAC,CAAC,CAACxG,IAAI,CAACH,OAAO,CAAC;MAC5B,CAAC,MACI;QACDA,OAAO,CAAC,IAAI,CAAC0D,IAAI,CAAC;MACtB;IACJ;EAAC;IAAAvH,GAAA;IAAAZ,KAAA,EACD,SAAAqJ,SAASzI,GAAG,EAAE;MACV,IAAIZ,KAAK,GAAG,IAAI,CAACsL,YAAY,CAAC1K,GAAG,CAAC;MAClC,IAAI,IAAI,CAAC6H,YAAY,IAAI,IAAI,CAACA,YAAY,CAAC7H,GAAG,CAAC,EAAE;QAC7CZ,KAAK,GAAGnE,QAAQ,CAACmE,KAAK,CAAC;MAC3B;MACA,IAAI,CAAC4K,QAAQ,CAAC9O,UAAU,CAAC8E,GAAG,CAAC,EAAEZ,KAAK,EAAE,KAAK,CAAC;IAChD;IACA;AACJ;AACA;EAFI;IAAAY,GAAA;IAAAZ,KAAA,EAGA,SAAAiL,SAASrK,GAAG,EAAE;MACV,OAAO,IAAI,CAACyH,MAAM,CAACzH,GAAG,CAAC;IAC3B;IACA;AACJ;AACA;EAFI;IAAAA,GAAA;IAAAZ,KAAA,EAGA,SAAA4K,SAAShK,GAAG,EAAEO,GAAG,EAA6C;MAAA,IAA3CoK,aAAa,GAAAlG,SAAA,CAAApD,MAAA,QAAAoD,SAAA,QAAAxH,SAAA,GAAAwH,SAAA,MAAG,IAAI;MAAA,IAAEmG,YAAY,GAAAnG,SAAA,CAAApD,MAAA,QAAAoD,SAAA,QAAAxH,SAAA,GAAAwH,SAAA,MAAG,IAAI;MACxD,IAAIlE,GAAG,KAAK,IAAI,CAACkH,MAAM,CAACzH,GAAG,CAAC,EAAE;QAC1B,IAAI,CAACyH,MAAM,CAACzH,GAAG,CAAC,GAAGO,GAAG;QACtB,IAAIqK,YAAY,IAAI,IAAI,CAAClD,SAAS,EAAE;UAChC,IAAI,CAAC6C,OAAO,CAAC,CAAC;QAClB;QACA;QACA,IAAII,aAAa,EAAE;UACf,IAAIpK,GAAG,KAAK,IAAI,EAAE;YACd,IAAI,CAACpD,YAAY,CAACzC,SAAS,CAACsF,GAAG,CAAC,EAAE,EAAE,CAAC;UACzC,CAAC,MACI,IAAI,OAAOO,GAAG,KAAK,QAAQ,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;YACzD,IAAI,CAACpD,YAAY,CAACzC,SAAS,CAACsF,GAAG,CAAC,EAAEO,GAAG,GAAG,EAAE,CAAC;UAC/C,CAAC,MACI,IAAI,CAACA,GAAG,EAAE;YACX,IAAI,CAACd,eAAe,CAAC/E,SAAS,CAACsF,GAAG,CAAC,CAAC;UACxC;QACJ;MACJ;IACJ;EAAC;IAAAA,GAAA;IAAAZ,KAAA,EACD,SAAAmL,QAAA,EAAU;MACNjC,MAAM,CAAC,IAAI,CAACP,YAAY,CAAC,CAAC,EAAE,IAAI,CAACD,UAAU,CAAC;IAChD;EAAC;IAAA9H,GAAA;IAAAZ,KAAA,EACD,SAAA2I,aAAA,EAAe;MAAA,IAAA8C,MAAA;MACX,IAAMC,KAAK,GAAGxR,WAAW,CAAC,IAAI,CAACiO,IAAI,EAAEpM,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAACsM,MAAM,CAAC,CAAC;MAC7D,IAAI,CAAC,IAAI,CAACC,SAAS,EAAE;QACjBoD,KAAK,CAACC,EAAE,GAAG,UAAAvJ,QAAQ,EAAI;UACnBqJ,MAAI,CAACnD,SAAS,GAAGlG,QAAQ;UACzBA,QAAQ,CAACwJ,IAAI,GAAG,IAAI;UACpB;UACA,IAAKvI,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAG;YACzCnB,QAAQ,CAACyJ,QAAQ,GAAG,UAAAC,SAAS,EAAI;cAC7B;cACA,IAAIL,MAAI,CAACM,OAAO,EAAE;gBACdN,MAAI,CAACM,OAAO,CAAC3K,OAAO,CAAC,UAAAuI,CAAC;kBAAA,OAAI8B,MAAI,CAAC/C,UAAU,CAACrL,WAAW,CAACsM,CAAC,CAAC;gBAAA,EAAC;gBACzD8B,MAAI,CAACM,OAAO,CAAC9J,MAAM,GAAG,CAAC;cAC3B;cACAwJ,MAAI,CAACP,YAAY,CAACY,SAAS,CAAC;cAC5B;cACA;cACA,IAAI,CAACL,MAAI,CAACtD,IAAI,CAACkD,aAAa,EAAE;gBAC1B;gBACAI,MAAI,CAACnD,SAAS,GAAG,IAAI;gBACrBmD,MAAI,CAACN,OAAO,CAAC,CAAC;cAClB;YACJ,CAAC;UACL;UACA;UACA/I,QAAQ,CAAC4J,IAAI,GAAG,UAAClH,KAAK,EAAc;YAAA,SAAAmH,IAAA,GAAA5G,SAAA,CAAApD,MAAA,EAATiK,IAAI,OAAAC,KAAA,CAAAF,IAAA,OAAAA,IAAA,WAAAG,KAAA,MAAAA,KAAA,GAAAH,IAAA,EAAAG,KAAA;cAAJF,IAAI,CAAAE,KAAA,QAAA/G,SAAA,CAAA+G,KAAA;YAAA;YAC3BX,MAAI,CAACY,aAAa,CAAC,IAAIC,WAAW,CAACxH,KAAK,EAAE;cACtCyH,MAAM,EAAEL;YACZ,CAAC,CAAC,CAAC;UACP,CAAC;UACD;UACA,IAAIlP,MAAM,GAAGyO,MAAI;UACjB,OAAQzO,MAAM,GACVA,MAAM,KAAKA,MAAM,CAACI,UAAU,IAAIJ,MAAM,CAACwP,IAAI,CAAC,EAAG;YAC/C,IAAIxP,MAAM,YAAY0K,UAAU,EAAE;cAC9BtF,QAAQ,CAACpF,MAAM,GAAGA,MAAM,CAACsL,SAAS;cAClC;YACJ;UACJ;QACJ,CAAC;MACL;MACA,OAAOoD,KAAK;IAChB;EAAC;IAAA9K,GAAA;IAAAZ,KAAA,EACD,SAAAkL,aAAahB,MAAM,EAAE;MAAA,IAAAuC,MAAA;MACjB,IAAIvC,MAAM,EAAE;QACRA,MAAM,CAAC9I,OAAO,CAAC,UAAAsL,GAAG,EAAI;UAClB,IAAM/C,CAAC,GAAGjN,QAAQ,CAACY,aAAa,CAAC,OAAO,CAAC;UACzCqM,CAAC,CAACnL,WAAW,GAAGkO,GAAG;UACnBD,MAAI,CAAC/D,UAAU,CAAC7I,WAAW,CAAC8J,CAAC,CAAC;UAC9B;UACA,IAAKtG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAG;YACzC,CAACkJ,MAAI,CAACV,OAAO,KAAKU,MAAI,CAACV,OAAO,GAAG,EAAE,CAAC,EAAEY,IAAI,CAAChD,CAAC,CAAC;UACjD;QACJ,CAAC,CAAC;MACN;IACJ;EAAC;EAAA,OAAAjC,UAAA;AAAA,EApMoBI,SAAS;AAuMlC,SAAS8E,YAAYA,CAAA,EAAkB;EAAA,IAAjB1L,IAAI,GAAAmE,SAAA,CAAApD,MAAA,QAAAoD,SAAA,QAAAxH,SAAA,GAAAwH,SAAA,MAAG,QAAQ;EACjC;EACA;IACI,IAAMjD,QAAQ,GAAGjI,kBAAkB,CAAC,CAAC;IACrC,IAAI,CAACiI,QAAQ,EAAE;MACViB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAKzJ,IAAI,6CAA6C,CAAC;MAC7F,OAAOkC,SAAS;IACpB;IACA,IAAM6Q,OAAO,GAAGzK,QAAQ,CAACa,IAAI,CAAC6J,YAAY;IAC1C,IAAI,CAACD,OAAO,EAAE;MACTxJ,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAKzJ,IAAI,uDAAuD,CAAC;MACvG,OAAOkC,SAAS;IACpB;IACA,IAAM+Q,GAAG,GAAGF,OAAO,CAAC3L,IAAI,CAAC;IACzB,IAAI,CAAC6L,GAAG,EAAE;MACL1J,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAClCzJ,IAAI,sDAAA4F,MAAA,CAAqDwB,IAAI,QAAI,CAAC;MACtE,OAAOlF,SAAS;IACpB;IACA,OAAO+Q,GAAG;EACd;AACJ;;AAEA;AACA;AACA;AACA;AACA,SAASC,UAAUA,CAACC,MAAM,EAAE;EACxB,IAAM7K,QAAQ,GAAGjI,kBAAkB,CAAC,CAAC;EACrC;EACA,IAAI,CAACiI,QAAQ,EAAE;IACViB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAClCzJ,IAAI,kEAAkE,CAAC;IAC3E;EACJ;EACA,IAAMoT,OAAO,GAAG,SAAVA,OAAOA,CAAA;IAAA,OAASC,cAAc,CAAC/K,QAAQ,CAACgL,OAAO,EAAEH,MAAM,CAAC7K,QAAQ,CAACiL,KAAK,CAAC,CAAC;EAAA;EAC9EjT,eAAe,CAAC8S,OAAO,CAAC;EACxB7S,SAAS,CAAC,YAAM;IACZ,IAAMiT,EAAE,GAAG,IAAIhE,gBAAgB,CAAC4D,OAAO,CAAC;IACxCI,EAAE,CAACrD,OAAO,CAAC7H,QAAQ,CAACgL,OAAO,CAACzP,EAAE,CAACP,UAAU,EAAE;MAAEmQ,SAAS,EAAE;IAAK,CAAC,CAAC;IAC/DjT,WAAW,CAAC;MAAA,OAAMgT,EAAE,CAACE,UAAU,CAAC,CAAC;IAAA,EAAC;EACtC,CAAC,CAAC;AACN;AACA,SAASL,cAAcA,CAACzB,KAAK,EAAE+B,IAAI,EAAE;EACjC,IAAI/B,KAAK,CAACgC,SAAS,GAAG,GAAG,CAAC,gBAAgB;IACtC,IAAMC,QAAQ,GAAGjC,KAAK,CAACiC,QAAQ;IAC/BjC,KAAK,GAAGiC,QAAQ,CAACC,YAAY;IAC7B,IAAID,QAAQ,CAACE,aAAa,IAAI,CAACF,QAAQ,CAACG,WAAW,EAAE;MACjDH,QAAQ,CAACI,OAAO,CAACpB,IAAI,CAAC,YAAM;QACxBQ,cAAc,CAACQ,QAAQ,CAACC,YAAY,EAAEH,IAAI,CAAC;MAC/C,CAAC,CAAC;IACN;EACJ;EACA;EACA,OAAO/B,KAAK,CAACsC,SAAS,EAAE;IACpBtC,KAAK,GAAGA,KAAK,CAACsC,SAAS,CAACZ,OAAO;EACnC;EACA,IAAI1B,KAAK,CAACgC,SAAS,GAAG,CAAC,CAAC,iBAAiBhC,KAAK,CAAC/N,EAAE,EAAE;IAC/CsQ,aAAa,CAACvC,KAAK,CAAC/N,EAAE,EAAE8P,IAAI,CAAC;EACjC,CAAC,MACI,IAAI/B,KAAK,CAACzI,IAAI,KAAK1I,QAAQ,EAAE;IAC9BmR,KAAK,CAACwC,QAAQ,CAAC9M,OAAO,CAAC,UAAA+M,CAAC;MAAA,OAAIhB,cAAc,CAACgB,CAAC,EAAEV,IAAI,CAAC;IAAA,EAAC;EACxD,CAAC,MACI,IAAI/B,KAAK,CAACzI,IAAI,KAAKzI,MAAM,EAAE;IAC5B,IAAA4T,MAAA,GAAqB1C,KAAK;MAApB/N,EAAE,GAAAyQ,MAAA,CAAFzQ,EAAE;MAAEV,MAAM,GAAAmR,MAAA,CAANnR,MAAM;IAChB,OAAOU,EAAE,EAAE;MACPsQ,aAAa,CAACtQ,EAAE,EAAE8P,IAAI,CAAC;MACvB,IAAI9P,EAAE,KAAKV,MAAM,EACb;MACJU,EAAE,GAAGA,EAAE,CAACc,WAAW;IACvB;EACJ;AACJ;AACA,SAASwP,aAAaA,CAACtQ,EAAE,EAAE8P,IAAI,EAAE;EAC7B,IAAI9P,EAAE,CAAC0Q,QAAQ,KAAK,CAAC,EAAE;IACnB,IAAM3N,KAAK,GAAG/C,EAAE,CAAC+C,KAAK;IACtB,KAAK,IAAME,GAAG,IAAI6M,IAAI,EAAE;MACpB/M,KAAK,CAACa,WAAW,MAAA7B,MAAA,CAAMkB,GAAG,GAAI6M,IAAI,CAAC7M,GAAG,CAAC,CAAC;IAC5C;EACJ;AACJ;AAEA,IAAM0N,UAAU,GAAG,YAAY;AAC/B,IAAMC,SAAS,GAAG,WAAW;AAC7B;AACA;AACA,IAAMC,UAAU,GAAG,SAAbA,UAAUA,CAAI9Q,KAAK,EAAA+Q,IAAA;EAAA,IAAIC,KAAK,GAAAD,IAAA,CAALC,KAAK;EAAA,OAAOjU,CAAC,CAACC,cAAc,EAAEiU,sBAAsB,CAACjR,KAAK,CAAC,EAAEgR,KAAK,CAAC;AAAA;AAChGF,UAAU,CAACI,WAAW,GAAG,YAAY;AACrC,IAAMC,4BAA4B,GAAG;EACjC3N,IAAI,EAAE4N,MAAM;EACZ7L,IAAI,EAAE6L,MAAM;EACZpC,GAAG,EAAE;IACDzJ,IAAI,EAAE8L,OAAO;IACbC,OAAO,EAAE;EACb,CAAC;EACDC,QAAQ,EAAE,CAACH,MAAM,EAAEzK,MAAM,EAAEgG,MAAM,CAAC;EAClC6E,cAAc,EAAEJ,MAAM;EACtBK,gBAAgB,EAAEL,MAAM;EACxBM,YAAY,EAAEN,MAAM;EACpBO,eAAe,EAAEP,MAAM;EACvBQ,iBAAiB,EAAER,MAAM;EACzBS,aAAa,EAAET,MAAM;EACrBU,cAAc,EAAEV,MAAM;EACtBW,gBAAgB,EAAEX,MAAM;EACxBY,YAAY,EAAEZ;AAClB,CAAC;AACD,IAAMa,yBAAyB,GAAInB,UAAU,CAAC9Q,KAAK,GAC/C,aAAc3B,MAAM,CAAC,CAAC,CAAC,EAAErB,cAAc,CAACgD,KAAK,EAAEmR,4BAA4B,CAAE;AACjF;AACA;AACA;AACA;AACA,IAAMe,QAAQ,GAAG,SAAXA,QAAQA,CAAIC,IAAI,EAAgB;EAAA,IAAd3D,IAAI,GAAA7G,SAAA,CAAApD,MAAA,QAAAoD,SAAA,QAAAxH,SAAA,GAAAwH,SAAA,MAAG,EAAE;EAC7B,IAAIhK,OAAO,CAACwU,IAAI,CAAC,EAAE;IACfA,IAAI,CAACzO,OAAO,CAAC,UAAA3G,CAAC;MAAA,OAAIA,CAAC,CAAAqV,KAAA,SAAA3P,kBAAA,CAAI+L,IAAI,EAAC;IAAA,EAAC;EACjC,CAAC,MACI,IAAI2D,IAAI,EAAE;IACXA,IAAI,CAAAC,KAAA,SAAA3P,kBAAA,CAAI+L,IAAI,EAAC;EACjB;AACJ,CAAC;AACD;AACA;AACA;AACA;AACA,IAAM6D,mBAAmB,GAAG,SAAtBA,mBAAmBA,CAAIF,IAAI,EAAK;EAClC,OAAOA,IAAI,GACLxU,OAAO,CAACwU,IAAI,CAAC,GACTA,IAAI,CAACG,IAAI,CAAC,UAAAvV,CAAC;IAAA,OAAIA,CAAC,CAACwH,MAAM,GAAG,CAAC;EAAA,EAAC,GAC5B4N,IAAI,CAAC5N,MAAM,GAAG,CAAC,GACnB,KAAK;AACf,CAAC;AACD,SAAS0M,sBAAsBA,CAACsB,QAAQ,EAAE;EACtC,IAAMC,SAAS,GAAG,CAAC,CAAC;EACpB,KAAK,IAAMtP,GAAG,IAAIqP,QAAQ,EAAE;IACxB,IAAI,EAAErP,GAAG,IAAIiO,4BAA4B,CAAC,EAAE;MACxCqB,SAAS,CAACtP,GAAG,CAAC,GAAGqP,QAAQ,CAACrP,GAAG,CAAC;IAClC;EACJ;EACA,IAAIqP,QAAQ,CAACvD,GAAG,KAAK,KAAK,EAAE;IACxB,OAAOwD,SAAS;EACpB;EACA,IAAAC,cAAA,GAAuXF,QAAQ,CAAvX/O,IAAI;IAAJA,IAAI,GAAAiP,cAAA,cAAG,GAAG,GAAAA,cAAA;IAAElN,IAAI,GAA+VgN,QAAQ,CAA3WhN,IAAI;IAAEgM,QAAQ,GAAqVgB,QAAQ,CAArWhB,QAAQ;IAAAmB,qBAAA,GAAqVH,QAAQ,CAA3Vf,cAAc;IAAdA,cAAc,GAAAkB,qBAAA,iBAAA1Q,MAAA,CAAMwB,IAAI,mBAAAkP,qBAAA;IAAAC,qBAAA,GAA2TJ,QAAQ,CAApTd,gBAAgB;IAAhBA,gBAAgB,GAAAkB,qBAAA,iBAAA3Q,MAAA,CAAMwB,IAAI,qBAAAmP,qBAAA;IAAAC,qBAAA,GAAkRL,QAAQ,CAAzQb,YAAY;IAAZA,YAAY,GAAAkB,qBAAA,iBAAA5Q,MAAA,CAAMwB,IAAI,iBAAAoP,qBAAA;IAAAC,qBAAA,GAA2ON,QAAQ,CAAtOZ,eAAe;IAAfA,eAAe,GAAAkB,qBAAA,cAAGrB,cAAc,GAAAqB,qBAAA;IAAAC,qBAAA,GAA8LP,QAAQ,CAApMX,iBAAiB;IAAjBA,iBAAiB,GAAAkB,qBAAA,cAAGrB,gBAAgB,GAAAqB,qBAAA;IAAAC,qBAAA,GAAwJR,QAAQ,CAA9JV,aAAa;IAAbA,aAAa,GAAAkB,qBAAA,cAAGrB,YAAY,GAAAqB,qBAAA;IAAAC,qBAAA,GAA0HT,QAAQ,CAAhIT,cAAc;IAAdA,cAAc,GAAAkB,qBAAA,iBAAAhR,MAAA,CAAMwB,IAAI,mBAAAwP,qBAAA;IAAAC,qBAAA,GAAgGV,QAAQ,CAAzFR,gBAAgB;IAAhBA,gBAAgB,GAAAkB,qBAAA,iBAAAjR,MAAA,CAAMwB,IAAI,qBAAAyP,qBAAA;IAAAC,qBAAA,GAAuDX,QAAQ,CAA9CP,YAAY;IAAZA,YAAY,GAAAkB,qBAAA,iBAAAlR,MAAA,CAAMwB,IAAI,iBAAA0P,qBAAA;EACvW,IAAMC,SAAS,GAAGC,iBAAiB,CAAC7B,QAAQ,CAAC;EAC7C,IAAM8B,aAAa,GAAGF,SAAS,IAAIA,SAAS,CAAC,CAAC,CAAC;EAC/C,IAAMG,aAAa,GAAGH,SAAS,IAAIA,SAAS,CAAC,CAAC,CAAC;EAC/C,IAAQI,cAAa,GAAqJf,SAAS,CAA3Ke,aAAa;IAAEC,OAAO,GAA4IhB,SAAS,CAA5JgB,OAAO;IAAEC,iBAAgB,GAA0HjB,SAAS,CAAnJiB,gBAAgB;IAAEC,QAAO,GAAiHlB,SAAS,CAAjIkB,OAAO;IAAEC,iBAAgB,GAA+FnB,SAAS,CAAxHmB,gBAAgB;IAAAC,qBAAA,GAA+FpB,SAAS,CAAtGqB,cAAc;IAAdA,eAAc,GAAAD,qBAAA,cAAGL,cAAa,GAAAK,qBAAA;IAAAE,mBAAA,GAA+DtB,SAAS,CAAtEuB,QAAQ;IAARA,QAAQ,GAAAD,mBAAA,cAAGN,OAAO,GAAAM,mBAAA;IAAAE,qBAAA,GAA2CxB,SAAS,CAAlDyB,iBAAiB;IAAjBA,kBAAiB,GAAAD,qBAAA,cAAGP,iBAAgB,GAAAO,qBAAA;EACrK,IAAME,WAAW,GAAG,SAAdA,WAAWA,CAAIjU,EAAE,EAAEkU,QAAQ,EAAEhI,IAAI,EAAK;IACxCiI,qBAAqB,CAACnU,EAAE,EAAEkU,QAAQ,GAAGtC,aAAa,GAAGH,YAAY,CAAC;IAClE0C,qBAAqB,CAACnU,EAAE,EAAEkU,QAAQ,GAAGvC,iBAAiB,GAAGH,gBAAgB,CAAC;IAC1EtF,IAAI,IAAIA,IAAI,CAAC,CAAC;EAClB,CAAC;EACD,IAAMkI,WAAW,GAAG,SAAdA,WAAWA,CAAIpU,EAAE,EAAEkM,IAAI,EAAK;IAC9BiI,qBAAqB,CAACnU,EAAE,EAAE+R,YAAY,CAAC;IACvCoC,qBAAqB,CAACnU,EAAE,EAAE8R,gBAAgB,CAAC;IAC3C5F,IAAI,IAAIA,IAAI,CAAC,CAAC;EAClB,CAAC;EACD,IAAMmI,aAAa,GAAG,SAAhBA,aAAaA,CAAIH,QAAQ,EAAK;IAChC,OAAO,UAAClU,EAAE,EAAEkM,IAAI,EAAK;MACjB,IAAMgG,IAAI,GAAGgC,QAAQ,GAAGJ,QAAQ,GAAGP,OAAO;MAC1C,IAAMzM,OAAO,GAAG,SAAVA,OAAOA,CAAA;QAAA,OAASmN,WAAW,CAACjU,EAAE,EAAEkU,QAAQ,EAAEhI,IAAI,CAAC;MAAA;MACrD+F,QAAQ,CAACC,IAAI,EAAE,CAAClS,EAAE,EAAE8G,OAAO,CAAC,CAAC;MAC7BwN,SAAS,CAAC,YAAM;QACZH,qBAAqB,CAACnU,EAAE,EAAEkU,QAAQ,GAAGxC,eAAe,GAAGH,cAAc,CAAC;QACtEgD,kBAAkB,CAACvU,EAAE,EAAEkU,QAAQ,GAAGtC,aAAa,GAAGH,YAAY,CAAC;QAC/D,IAAI,CAACW,mBAAmB,CAACF,IAAI,CAAC,EAAE;UAC5BsC,kBAAkB,CAACxU,EAAE,EAAEsF,IAAI,EAAE8N,aAAa,EAAEtM,OAAO,CAAC;QACxD;MACJ,CAAC,CAAC;IACN,CAAC;EACL,CAAC;EACD,OAAO1I,MAAM,CAACmU,SAAS,EAAE;IACrBe,aAAa,WAAAA,cAACtT,EAAE,EAAE;MACdiS,QAAQ,CAACqB,cAAa,EAAE,CAACtT,EAAE,CAAC,CAAC;MAC7BuU,kBAAkB,CAACvU,EAAE,EAAEuR,cAAc,CAAC;MACtCgD,kBAAkB,CAACvU,EAAE,EAAEwR,gBAAgB,CAAC;IAC5C,CAAC;IACDoC,cAAc,WAAAA,eAAC5T,EAAE,EAAE;MACfiS,QAAQ,CAAC2B,eAAc,EAAE,CAAC5T,EAAE,CAAC,CAAC;MAC9BuU,kBAAkB,CAACvU,EAAE,EAAE0R,eAAe,CAAC;MACvC6C,kBAAkB,CAACvU,EAAE,EAAE2R,iBAAiB,CAAC;IAC7C,CAAC;IACD4B,OAAO,EAAEc,aAAa,CAAC,KAAK,CAAC;IAC7BP,QAAQ,EAAEO,aAAa,CAAC,IAAI,CAAC;IAC7BZ,OAAO,WAAAA,QAACzT,EAAE,EAAEkM,IAAI,EAAE;MACd,IAAMpF,OAAO,GAAG,SAAVA,OAAOA,CAAA;QAAA,OAASsN,WAAW,CAACpU,EAAE,EAAEkM,IAAI,CAAC;MAAA;MAC3CqI,kBAAkB,CAACvU,EAAE,EAAE6R,cAAc,CAAC;MACtC;MACA4C,WAAW,CAAC,CAAC;MACbF,kBAAkB,CAACvU,EAAE,EAAE8R,gBAAgB,CAAC;MACxCwC,SAAS,CAAC,YAAM;QACZH,qBAAqB,CAACnU,EAAE,EAAE6R,cAAc,CAAC;QACzC0C,kBAAkB,CAACvU,EAAE,EAAE+R,YAAY,CAAC;QACpC,IAAI,CAACK,mBAAmB,CAACqB,QAAO,CAAC,EAAE;UAC/Be,kBAAkB,CAACxU,EAAE,EAAEsF,IAAI,EAAE+N,aAAa,EAAEvM,OAAO,CAAC;QACxD;MACJ,CAAC,CAAC;MACFmL,QAAQ,CAACwB,QAAO,EAAE,CAACzT,EAAE,EAAE8G,OAAO,CAAC,CAAC;IACpC,CAAC;IACD0M,gBAAgB,WAAAA,iBAACxT,EAAE,EAAE;MACjBiU,WAAW,CAACjU,EAAE,EAAE,KAAK,CAAC;MACtBiS,QAAQ,CAACuB,iBAAgB,EAAE,CAACxT,EAAE,CAAC,CAAC;IACpC,CAAC;IACDgU,iBAAiB,WAAAA,kBAAChU,EAAE,EAAE;MAClBiU,WAAW,CAACjU,EAAE,EAAE,IAAI,CAAC;MACrBiS,QAAQ,CAAC+B,kBAAiB,EAAE,CAAChU,EAAE,CAAC,CAAC;IACrC,CAAC;IACD0T,gBAAgB,WAAAA,iBAAC1T,EAAE,EAAE;MACjBoU,WAAW,CAACpU,EAAE,CAAC;MACfiS,QAAQ,CAACyB,iBAAgB,EAAE,CAAC1T,EAAE,CAAC,CAAC;IACpC;EACJ,CAAC,CAAC;AACN;AACA,SAASmT,iBAAiBA,CAAC7B,QAAQ,EAAE;EACjC,IAAIA,QAAQ,IAAI,IAAI,EAAE;IAClB,OAAO,IAAI;EACf,CAAC,MACI,IAAIhT,QAAQ,CAACgT,QAAQ,CAAC,EAAE;IACzB,OAAO,CAACoD,QAAQ,CAACpD,QAAQ,CAACqD,KAAK,CAAC,EAAED,QAAQ,CAACpD,QAAQ,CAACsD,KAAK,CAAC,CAAC;EAC/D,CAAC,MACI;IACD,IAAM3I,CAAC,GAAGyI,QAAQ,CAACpD,QAAQ,CAAC;IAC5B,OAAO,CAACrF,CAAC,EAAEA,CAAC,CAAC;EACjB;AACJ;AACA,SAASyI,QAAQA,CAAClR,GAAG,EAAE;EACnB,IAAMqR,GAAG,GAAG3W,QAAQ,CAACsF,GAAG,CAAC;EACzB,IAAKkC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EACtCkP,gBAAgB,CAACD,GAAG,CAAC;EACzB,OAAOA,GAAG;AACd;AACA,SAASC,gBAAgBA,CAACtR,GAAG,EAAE;EAC3B,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;IACzBrH,IAAI,CAAC,mEAAA4F,MAAA,CACMgT,IAAI,CAACC,SAAS,CAACxR,GAAG,CAAC,MAAG,CAAC;EACtC,CAAC,MACI,IAAIyR,KAAK,CAACzR,GAAG,CAAC,EAAE;IACjBrH,IAAI,CAAC,6CACD,6CAA6C,CAAC;EACtD;AACJ;AACA,SAASoY,kBAAkBA,CAACvU,EAAE,EAAEkV,GAAG,EAAE;EACjCA,GAAG,CAACC,KAAK,CAAC,KAAK,CAAC,CAAC1R,OAAO,CAAC,UAAA+M,CAAC;IAAA,OAAIA,CAAC,IAAIxQ,EAAE,CAACoV,SAAS,CAACC,GAAG,CAAC7E,CAAC,CAAC;EAAA,EAAC;EACvD,CAACxQ,EAAE,CAACuC,IAAI,KACHvC,EAAE,CAACuC,IAAI,GAAG,IAAI+S,GAAG,CAAC,CAAC,CAAC,EAAED,GAAG,CAACH,GAAG,CAAC;AACvC;AACA,SAASf,qBAAqBA,CAACnU,EAAE,EAAEkV,GAAG,EAAE;EACpCA,GAAG,CAACC,KAAK,CAAC,KAAK,CAAC,CAAC1R,OAAO,CAAC,UAAA+M,CAAC;IAAA,OAAIA,CAAC,IAAIxQ,EAAE,CAACoV,SAAS,CAAC5V,MAAM,CAACgR,CAAC,CAAC;EAAA,EAAC;EAC1D,IAAQjO,IAAI,GAAKvC,EAAE,CAAXuC,IAAI;EACZ,IAAIA,IAAI,EAAE;IACNA,IAAI,CAACgT,MAAM,CAACL,GAAG,CAAC;IAChB,IAAI,CAAC3S,IAAI,CAACiT,IAAI,EAAE;MACZxV,EAAE,CAACuC,IAAI,GAAGrC,SAAS;IACvB;EACJ;AACJ;AACA,SAASoU,SAASA,CAACmB,EAAE,EAAE;EACnBC,qBAAqB,CAAC,YAAM;IACxBA,qBAAqB,CAACD,EAAE,CAAC;EAC7B,CAAC,CAAC;AACN;AACA,IAAIE,KAAK,GAAG,CAAC;AACb,SAASnB,kBAAkBA,CAACxU,EAAE,EAAE4V,YAAY,EAAEC,eAAe,EAAE/O,OAAO,EAAE;EACpE,IAAM5F,EAAE,GAAIlB,EAAE,CAAC8V,MAAM,GAAG,EAAEH,KAAM;EAChC,IAAMI,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAA,EAAS;IAC5B,IAAI7U,EAAE,KAAKlB,EAAE,CAAC8V,MAAM,EAAE;MAClBhP,OAAO,CAAC,CAAC;IACb;EACJ,CAAC;EACD,IAAI+O,eAAe,EAAE;IACjB,OAAOG,UAAU,CAACD,iBAAiB,EAAEF,eAAe,CAAC;EACzD;EACA,IAAAI,kBAAA,GAAqCC,iBAAiB,CAAClW,EAAE,EAAE4V,YAAY,CAAC;IAAhEtQ,IAAI,GAAA2Q,kBAAA,CAAJ3Q,IAAI;IAAE6Q,OAAO,GAAAF,kBAAA,CAAPE,OAAO;IAAEC,SAAS,GAAAH,kBAAA,CAATG,SAAS;EAChC,IAAI,CAAC9Q,IAAI,EAAE;IACP,OAAOwB,OAAO,CAAC,CAAC;EACpB;EACA,IAAMuP,QAAQ,GAAG/Q,IAAI,GAAG,KAAK;EAC7B,IAAIgR,KAAK,GAAG,CAAC;EACb,IAAMC,GAAG,GAAG,SAANA,GAAGA,CAAA,EAAS;IACdvW,EAAE,CAACsH,mBAAmB,CAAC+O,QAAQ,EAAEG,KAAK,CAAC;IACvCT,iBAAiB,CAAC,CAAC;EACvB,CAAC;EACD,IAAMS,KAAK,GAAG,SAARA,KAAKA,CAAI/Q,CAAC,EAAK;IACjB,IAAIA,CAAC,CAACgR,MAAM,KAAKzW,EAAE,IAAI,EAAEsW,KAAK,IAAIF,SAAS,EAAE;MACzCG,GAAG,CAAC,CAAC;IACT;EACJ,CAAC;EACDP,UAAU,CAAC,YAAM;IACb,IAAIM,KAAK,GAAGF,SAAS,EAAE;MACnBG,GAAG,CAAC,CAAC;IACT;EACJ,CAAC,EAAEJ,OAAO,GAAG,CAAC,CAAC;EACfnW,EAAE,CAACkH,gBAAgB,CAACmP,QAAQ,EAAEG,KAAK,CAAC;AACxC;AACA,SAASN,iBAAiBA,CAAClW,EAAE,EAAE4V,YAAY,EAAE;EACzC,IAAMrJ,MAAM,GAAGrG,MAAM,CAACwQ,gBAAgB,CAAC1W,EAAE,CAAC;EAC1C;EACA,IAAM2W,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAI1T,GAAG;IAAA,OAAK,CAACsJ,MAAM,CAACtJ,GAAG,CAAC,IAAI,EAAE,EAAEkS,KAAK,CAAC,IAAI,CAAC;EAAA;EACnE,IAAMyB,gBAAgB,GAAGD,kBAAkB,CAAChG,UAAU,GAAG,OAAO,CAAC;EACjE,IAAMkG,mBAAmB,GAAGF,kBAAkB,CAAChG,UAAU,GAAG,UAAU,CAAC;EACvE,IAAMmG,iBAAiB,GAAGC,UAAU,CAACH,gBAAgB,EAAEC,mBAAmB,CAAC;EAC3E,IAAMG,eAAe,GAAGL,kBAAkB,CAAC/F,SAAS,GAAG,OAAO,CAAC;EAC/D,IAAMqG,kBAAkB,GAAGN,kBAAkB,CAAC/F,SAAS,GAAG,UAAU,CAAC;EACrE,IAAMsG,gBAAgB,GAAGH,UAAU,CAACC,eAAe,EAAEC,kBAAkB,CAAC;EACxE,IAAI3R,IAAI,GAAG,IAAI;EACf,IAAI6Q,OAAO,GAAG,CAAC;EACf,IAAIC,SAAS,GAAG,CAAC;EACjB;EACA,IAAIR,YAAY,KAAKjF,UAAU,EAAE;IAC7B,IAAImG,iBAAiB,GAAG,CAAC,EAAE;MACvBxR,IAAI,GAAGqL,UAAU;MACjBwF,OAAO,GAAGW,iBAAiB;MAC3BV,SAAS,GAAGS,mBAAmB,CAACvS,MAAM;IAC1C;EACJ,CAAC,MACI,IAAIsR,YAAY,KAAKhF,SAAS,EAAE;IACjC,IAAIsG,gBAAgB,GAAG,CAAC,EAAE;MACtB5R,IAAI,GAAGsL,SAAS;MAChBuF,OAAO,GAAGe,gBAAgB;MAC1Bd,SAAS,GAAGa,kBAAkB,CAAC3S,MAAM;IACzC;EACJ,CAAC,MACI;IACD6R,OAAO,GAAGgB,IAAI,CAACC,GAAG,CAACN,iBAAiB,EAAEI,gBAAgB,CAAC;IACvD5R,IAAI,GACA6Q,OAAO,GAAG,CAAC,GACLW,iBAAiB,GAAGI,gBAAgB,GAChCvG,UAAU,GACVC,SAAS,GACb,IAAI;IACdwF,SAAS,GAAG9Q,IAAI,GACVA,IAAI,KAAKqL,UAAU,GACfkG,mBAAmB,CAACvS,MAAM,GAC1B2S,kBAAkB,CAAC3S,MAAM,GAC7B,CAAC;EACX;EACA,IAAM+S,YAAY,GAAG/R,IAAI,KAAKqL,UAAU,IACpC,wBAAwB,CAAC5M,IAAI,CAACwI,MAAM,CAACoE,UAAU,GAAG,UAAU,CAAC,CAAC;EAClE,OAAO;IACHrL,IAAI,EAAJA,IAAI;IACJ6Q,OAAO,EAAPA,OAAO;IACPC,SAAS,EAATA,SAAS;IACTiB,YAAY,EAAZA;EACJ,CAAC;AACL;AACA,SAASN,UAAUA,CAACO,MAAM,EAAEpE,SAAS,EAAE;EACnC,OAAOoE,MAAM,CAAChT,MAAM,GAAG4O,SAAS,CAAC5O,MAAM,EAAE;IACrCgT,MAAM,GAAGA,MAAM,CAACvV,MAAM,CAACuV,MAAM,CAAC;EAClC;EACA,OAAOH,IAAI,CAACC,GAAG,CAAAjF,KAAA,CAARgF,IAAI,EAAA3U,kBAAA,CAAQ0Q,SAAS,CAACrK,GAAG,CAAC,UAAC0O,CAAC,EAAElT,CAAC;IAAA,OAAKmT,IAAI,CAACD,CAAC,CAAC,GAAGC,IAAI,CAACF,MAAM,CAACjT,CAAC,CAAC,CAAC;EAAA,EAAC,EAAC;AAC1E;AACA;AACA;AACA;AACA;AACA,SAASmT,IAAIA,CAACxL,CAAC,EAAE;EACb,OAAOtF,MAAM,CAACsF,CAAC,CAACrH,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACX,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,GAAG,IAAI;AAC1D;AACA;AACA,SAASyQ,WAAWA,CAAA,EAAG;EACnB,OAAO1V,QAAQ,CAAC0Y,IAAI,CAACC,YAAY;AACrC;AAEA,IAAMC,WAAW,GAAG,IAAIC,OAAO,CAAC,CAAC;AACjC,IAAMC,cAAc,GAAG,IAAID,OAAO,CAAC,CAAC;AACpC,IAAME,mBAAmB,GAAG;EACxBvU,IAAI,EAAE,iBAAiB;EACvBxD,KAAK,EAAE,aAAc3B,MAAM,CAAC,CAAC,CAAC,EAAE4T,yBAAyB,EAAE;IACvDpS,GAAG,EAAEuR,MAAM;IACX4G,SAAS,EAAE5G;EACf,CAAC,CAAC;EACF6G,KAAK,WAAAA,MAACjY,KAAK,EAAAkY,KAAA,EAAa;IAAA,IAATlH,KAAK,GAAAkH,KAAA,CAALlH,KAAK;IAChB,IAAMtM,QAAQ,GAAGjI,kBAAkB,CAAC,CAAC;IACrC,IAAM0b,KAAK,GAAGlb,kBAAkB,CAAC,CAAC;IAClC,IAAI+H,YAAY;IAChB,IAAIwL,QAAQ;IACZtT,SAAS,CAAC,YAAM;MACZ;MACA,IAAI,CAAC8H,YAAY,CAACT,MAAM,EAAE;QACtB;MACJ;MACA,IAAMyT,SAAS,GAAGhY,KAAK,CAACgY,SAAS,OAAAhW,MAAA,CAAOhC,KAAK,CAACwD,IAAI,IAAI,GAAG,UAAO;MAChE,IAAI,CAAC4U,eAAe,CAACpT,YAAY,CAAC,CAAC,CAAC,CAAC/E,EAAE,EAAEyE,QAAQ,CAACsJ,KAAK,CAAC/N,EAAE,EAAE+X,SAAS,CAAC,EAAE;QACpE;MACJ;MACA;MACA;MACAhT,YAAY,CAACtB,OAAO,CAAC2U,cAAc,CAAC;MACpCrT,YAAY,CAACtB,OAAO,CAAC4U,cAAc,CAAC;MACpC,IAAMC,aAAa,GAAGvT,YAAY,CAACwT,MAAM,CAACC,gBAAgB,CAAC;MAC3D;MACA/D,WAAW,CAAC,CAAC;MACb6D,aAAa,CAAC7U,OAAO,CAAC,UAAA+M,CAAC,EAAI;QACvB,IAAMxQ,EAAE,GAAGwQ,CAAC,CAACxQ,EAAE;QACf,IAAM+C,KAAK,GAAG/C,EAAE,CAAC+C,KAAK;QACtBwR,kBAAkB,CAACvU,EAAE,EAAE+X,SAAS,CAAC;QACjChV,KAAK,CAAC0V,SAAS,GAAG1V,KAAK,CAAC2V,eAAe,GAAG3V,KAAK,CAAC4V,kBAAkB,GAAG,EAAE;QACvE,IAAMlD,EAAE,GAAIzV,EAAE,CAAC4Y,OAAO,GAAG,UAACnT,CAAC,EAAK;UAC5B,IAAIA,CAAC,IAAIA,CAAC,CAACgR,MAAM,KAAKzW,EAAE,EAAE;YACtB;UACJ;UACA,IAAI,CAACyF,CAAC,IAAI,YAAY,CAAC1B,IAAI,CAAC0B,CAAC,CAACoT,YAAY,CAAC,EAAE;YACzC7Y,EAAE,CAACsH,mBAAmB,CAAC,eAAe,EAAEmO,EAAE,CAAC;YAC3CzV,EAAE,CAAC4Y,OAAO,GAAG,IAAI;YACjBzE,qBAAqB,CAACnU,EAAE,EAAE+X,SAAS,CAAC;UACxC;QACJ,CAAE;QACF/X,EAAE,CAACkH,gBAAgB,CAAC,eAAe,EAAEuO,EAAE,CAAC;MAC5C,CAAC,CAAC;IACN,CAAC,CAAC;IACF,OAAO,YAAM;MACT,IAAMnD,QAAQ,GAAGpV,KAAK,CAAC6C,KAAK,CAAC;MAC7B,IAAM+Y,kBAAkB,GAAG9H,sBAAsB,CAACsB,QAAQ,CAAC;MAC3D,IAAI1S,GAAG,GAAG0S,QAAQ,CAAC1S,GAAG,IAAIhD,QAAQ;MAClCmI,YAAY,GAAGwL,QAAQ;MACvBA,QAAQ,GAAGQ,KAAK,CAACM,OAAO,GAAGlU,wBAAwB,CAAC4T,KAAK,CAACM,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE;MACzE,KAAK,IAAIhN,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkM,QAAQ,CAACjM,MAAM,EAAED,CAAC,EAAE,EAAE;QACtC,IAAMjF,KAAK,GAAGmR,QAAQ,CAAClM,CAAC,CAAC;QACzB,IAAIjF,KAAK,CAAC6D,GAAG,IAAI,IAAI,EAAE;UACnB7F,kBAAkB,CAACgC,KAAK,EAAE/B,sBAAsB,CAAC+B,KAAK,EAAE0Z,kBAAkB,EAAEZ,KAAK,EAAEzT,QAAQ,CAAC,CAAC;QACjG,CAAC,MACI,IAAKiB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAG;UAC9CzJ,IAAI,4CAA4C,CAAC;QACrD;MACJ;MACA,IAAI4I,YAAY,EAAE;QACd,KAAK,IAAIV,GAAC,GAAG,CAAC,EAAEA,GAAC,GAAGU,YAAY,CAACT,MAAM,EAAED,GAAC,EAAE,EAAE;UAC1C,IAAMjF,MAAK,GAAG2F,YAAY,CAACV,GAAC,CAAC;UAC7BjH,kBAAkB,CAACgC,MAAK,EAAE/B,sBAAsB,CAAC+B,MAAK,EAAE0Z,kBAAkB,EAAEZ,KAAK,EAAEzT,QAAQ,CAAC,CAAC;UAC7FkT,WAAW,CAACxV,GAAG,CAAC/C,MAAK,EAAEA,MAAK,CAACY,EAAE,CAAC+Y,qBAAqB,CAAC,CAAC,CAAC;QAC5D;MACJ;MACA,OAAOxc,WAAW,CAACqD,GAAG,EAAE,IAAI,EAAE2Q,QAAQ,CAAC;IAC3C,CAAC;EACL;AACJ,CAAC;AACD,IAAMyI,eAAe,GAAGlB,mBAAmB;AAC3C,SAASM,cAAcA,CAAC5H,CAAC,EAAE;EACvB,IAAMxQ,EAAE,GAAGwQ,CAAC,CAACxQ,EAAE;EACf,IAAIA,EAAE,CAAC4Y,OAAO,EAAE;IACZ5Y,EAAE,CAAC4Y,OAAO,CAAC,CAAC;EAChB;EACA,IAAI5Y,EAAE,CAACiZ,QAAQ,EAAE;IACbjZ,EAAE,CAACiZ,QAAQ,CAAC,CAAC;EACjB;AACJ;AACA,SAASZ,cAAcA,CAAC7H,CAAC,EAAE;EACvBqH,cAAc,CAAC1V,GAAG,CAACqO,CAAC,EAAEA,CAAC,CAACxQ,EAAE,CAAC+Y,qBAAqB,CAAC,CAAC,CAAC;AACvD;AACA,SAASP,gBAAgBA,CAAChI,CAAC,EAAE;EACzB,IAAM0I,MAAM,GAAGvB,WAAW,CAAC/V,GAAG,CAAC4O,CAAC,CAAC;EACjC,IAAM2I,MAAM,GAAGtB,cAAc,CAACjW,GAAG,CAAC4O,CAAC,CAAC;EACpC,IAAM4I,EAAE,GAAGF,MAAM,CAACG,IAAI,GAAGF,MAAM,CAACE,IAAI;EACpC,IAAMC,EAAE,GAAGJ,MAAM,CAACK,GAAG,GAAGJ,MAAM,CAACI,GAAG;EAClC,IAAIH,EAAE,IAAIE,EAAE,EAAE;IACV,IAAMtN,CAAC,GAAGwE,CAAC,CAACxQ,EAAE,CAAC+C,KAAK;IACpBiJ,CAAC,CAACyM,SAAS,GAAGzM,CAAC,CAAC0M,eAAe,gBAAA3W,MAAA,CAAgBqX,EAAE,SAAArX,MAAA,CAAMuX,EAAE,QAAK;IAC9DtN,CAAC,CAAC2M,kBAAkB,GAAG,IAAI;IAC3B,OAAOnI,CAAC;EACZ;AACJ;AACA,SAAS2H,eAAeA,CAACnY,EAAE,EAAEwZ,IAAI,EAAEzB,SAAS,EAAE;EAC1C;EACA;EACA;EACA;EACA;EACA,IAAM0B,KAAK,GAAGzZ,EAAE,CAACmB,SAAS,CAAC,CAAC;EAC5B,IAAInB,EAAE,CAACuC,IAAI,EAAE;IACTvC,EAAE,CAACuC,IAAI,CAACkB,OAAO,CAAC,UAAAyR,GAAG,EAAI;MACnBA,GAAG,CAACC,KAAK,CAAC,KAAK,CAAC,CAAC1R,OAAO,CAAC,UAAA+M,CAAC;QAAA,OAAIA,CAAC,IAAIiJ,KAAK,CAACrE,SAAS,CAAC5V,MAAM,CAACgR,CAAC,CAAC;MAAA,EAAC;IACjE,CAAC,CAAC;EACN;EACAuH,SAAS,CAAC5C,KAAK,CAAC,KAAK,CAAC,CAAC1R,OAAO,CAAC,UAAA+M,CAAC;IAAA,OAAIA,CAAC,IAAIiJ,KAAK,CAACrE,SAAS,CAACC,GAAG,CAAC7E,CAAC,CAAC;EAAA,EAAC;EAChEiJ,KAAK,CAAC1W,KAAK,CAACK,OAAO,GAAG,MAAM;EAC5B,IAAMsW,SAAS,GAAIF,IAAI,CAAC9I,QAAQ,KAAK,CAAC,GAAG8I,IAAI,GAAGA,IAAI,CAAC/Z,UAAW;EAChEia,SAAS,CAACxX,WAAW,CAACuX,KAAK,CAAC;EAC5B,IAAAE,mBAAA,GAAyBzD,iBAAiB,CAACuD,KAAK,CAAC;IAAzCpC,YAAY,GAAAsC,mBAAA,CAAZtC,YAAY;EACpBqC,SAAS,CAACha,WAAW,CAAC+Z,KAAK,CAAC;EAC5B,OAAOpC,YAAY;AACvB;AAEA,IAAMuC,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAI7L,KAAK,EAAK;EAChC,IAAMjF,EAAE,GAAGiF,KAAK,CAAChO,KAAK,CAAC,qBAAqB,CAAC;EAC7C,OAAOrC,OAAO,CAACoL,EAAE,CAAC,GAAG,UAAAzG,KAAK;IAAA,OAAI9D,cAAc,CAACuK,EAAE,EAAEzG,KAAK,CAAC;EAAA,IAAGyG,EAAE;AAChE,CAAC;AACD,SAAS+Q,kBAAkBA,CAACpU,CAAC,EAAE;EAC3BA,CAAC,CAACgR,MAAM,CAACqD,SAAS,GAAG,IAAI;AAC7B;AACA,SAASC,gBAAgBA,CAACtU,CAAC,EAAE;EACzB,IAAMgR,MAAM,GAAGhR,CAAC,CAACgR,MAAM;EACvB,IAAIA,MAAM,CAACqD,SAAS,EAAE;IAClBrD,MAAM,CAACqD,SAAS,GAAG,KAAK;IACxBE,OAAO,CAACvD,MAAM,EAAE,OAAO,CAAC;EAC5B;AACJ;AACA,SAASuD,OAAOA,CAACha,EAAE,EAAEsF,IAAI,EAAE;EACvB,IAAMG,CAAC,GAAG1G,QAAQ,CAACoH,WAAW,CAAC,YAAY,CAAC;EAC5CV,CAAC,CAACwU,SAAS,CAAC3U,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAC7BtF,EAAE,CAAC0O,aAAa,CAACjJ,CAAC,CAAC;AACvB;AACA;AACA;AACA,IAAMyU,UAAU,GAAG;EACfC,OAAO,WAAAA,QAACna,EAAE,EAAAoa,KAAA,EAAyCrM,KAAK,EAAE;IAAA,IAAAsM,eAAA,GAAAD,KAAA,CAA5CE,SAAS;MAAIC,IAAI,GAAAF,eAAA,CAAJE,IAAI;MAAEC,IAAI,GAAAH,eAAA,CAAJG,IAAI;MAAEC,MAAM,GAAAJ,eAAA,CAANI,MAAM;IACzCza,EAAE,CAAC0a,OAAO,GAAGd,gBAAgB,CAAC7L,KAAK,CAAC;IACpC,IAAM4M,YAAY,GAAGF,MAAM,IAAK1M,KAAK,CAAChO,KAAK,IAAIgO,KAAK,CAAChO,KAAK,CAACuF,IAAI,KAAK,QAAS;IAC7E4B,gBAAgB,CAAClH,EAAE,EAAEua,IAAI,GAAG,QAAQ,GAAG,OAAO,EAAE,UAAA9U,CAAC,EAAI;MACjD,IAAIA,CAAC,CAACgR,MAAM,CAACqD,SAAS,EAClB;MACJ,IAAIc,QAAQ,GAAG5a,EAAE,CAACqC,KAAK;MACvB,IAAImY,IAAI,EAAE;QACNI,QAAQ,GAAGA,QAAQ,CAACJ,IAAI,CAAC,CAAC;MAC9B,CAAC,MACI,IAAIG,YAAY,EAAE;QACnBC,QAAQ,GAAG1c,QAAQ,CAAC0c,QAAQ,CAAC;MACjC;MACA5a,EAAE,CAAC0a,OAAO,CAACE,QAAQ,CAAC;IACxB,CAAC,CAAC;IACF,IAAIJ,IAAI,EAAE;MACNtT,gBAAgB,CAAClH,EAAE,EAAE,QAAQ,EAAE,YAAM;QACjCA,EAAE,CAACqC,KAAK,GAAGrC,EAAE,CAACqC,KAAK,CAACmY,IAAI,CAAC,CAAC;MAC9B,CAAC,CAAC;IACN;IACA,IAAI,CAACD,IAAI,EAAE;MACPrT,gBAAgB,CAAClH,EAAE,EAAE,kBAAkB,EAAE6Z,kBAAkB,CAAC;MAC5D3S,gBAAgB,CAAClH,EAAE,EAAE,gBAAgB,EAAE+Z,gBAAgB,CAAC;MACxD;MACA;MACA;MACA;MACA7S,gBAAgB,CAAClH,EAAE,EAAE,QAAQ,EAAE+Z,gBAAgB,CAAC;IACpD;EACJ,CAAC;EACD;EACAc,OAAO,WAAAA,QAAC7a,EAAE,EAAA8a,KAAA,EAAa;IAAA,IAATzY,KAAK,GAAAyY,KAAA,CAALzY,KAAK;IACfrC,EAAE,CAACqC,KAAK,GAAGA,KAAK,IAAI,IAAI,GAAG,EAAE,GAAGA,KAAK;EACzC,CAAC;EACD0Y,YAAY,WAAAA,aAAC/a,EAAE,EAAAgb,KAAA,EAAgDjN,KAAK,EAAE;IAAA,IAAnD1L,KAAK,GAAA2Y,KAAA,CAAL3Y,KAAK;MAAA4Y,eAAA,GAAAD,KAAA,CAAEV,SAAS;MAAIC,IAAI,GAAAU,eAAA,CAAJV,IAAI;MAAEC,IAAI,GAAAS,eAAA,CAAJT,IAAI;MAAEC,MAAM,GAAAQ,eAAA,CAANR,MAAM;IACrDza,EAAE,CAAC0a,OAAO,GAAGd,gBAAgB,CAAC7L,KAAK,CAAC;IACpC;IACA,IAAI/N,EAAE,CAAC8Z,SAAS,EACZ;IACJ,IAAI/a,QAAQ,CAACmc,aAAa,KAAKlb,EAAE,EAAE;MAC/B,IAAIua,IAAI,EAAE;QACN;MACJ;MACA,IAAIC,IAAI,IAAIxa,EAAE,CAACqC,KAAK,CAACmY,IAAI,CAAC,CAAC,KAAKnY,KAAK,EAAE;QACnC;MACJ;MACA,IAAI,CAACoY,MAAM,IAAIza,EAAE,CAACsF,IAAI,KAAK,QAAQ,KAAKpH,QAAQ,CAAC8B,EAAE,CAACqC,KAAK,CAAC,KAAKA,KAAK,EAAE;QAClE;MACJ;IACJ;IACA,IAAMgD,QAAQ,GAAGhD,KAAK,IAAI,IAAI,GAAG,EAAE,GAAGA,KAAK;IAC3C,IAAIrC,EAAE,CAACqC,KAAK,KAAKgD,QAAQ,EAAE;MACvBrF,EAAE,CAACqC,KAAK,GAAGgD,QAAQ;IACvB;EACJ;AACJ,CAAC;AACD,IAAM8V,cAAc,GAAG;EACnB;EACAC,IAAI,EAAE,IAAI;EACVjB,OAAO,WAAAA,QAACna,EAAE,EAAEqb,CAAC,EAAEtN,KAAK,EAAE;IAClB/N,EAAE,CAAC0a,OAAO,GAAGd,gBAAgB,CAAC7L,KAAK,CAAC;IACpC7G,gBAAgB,CAAClH,EAAE,EAAE,QAAQ,EAAE,YAAM;MACjC,IAAMsb,UAAU,GAAGtb,EAAE,CAACub,WAAW;MACjC,IAAMC,YAAY,GAAGC,QAAQ,CAACzb,EAAE,CAAC;MACjC,IAAM0b,OAAO,GAAG1b,EAAE,CAAC0b,OAAO;MAC1B,IAAMC,MAAM,GAAG3b,EAAE,CAAC0a,OAAO;MACzB,IAAIhd,OAAO,CAAC4d,UAAU,CAAC,EAAE;QACrB,IAAMM,KAAK,GAAGpd,YAAY,CAAC8c,UAAU,EAAEE,YAAY,CAAC;QACpD,IAAMK,KAAK,GAAGD,KAAK,KAAK,CAAC,CAAC;QAC1B,IAAIF,OAAO,IAAI,CAACG,KAAK,EAAE;UACnBF,MAAM,CAACL,UAAU,CAACvZ,MAAM,CAACyZ,YAAY,CAAC,CAAC;QAC3C,CAAC,MACI,IAAI,CAACE,OAAO,IAAIG,KAAK,EAAE;UACxB,IAAMC,QAAQ,GAAAtZ,kBAAA,CAAO8Y,UAAU,CAAC;UAChCQ,QAAQ,CAACC,MAAM,CAACH,KAAK,EAAE,CAAC,CAAC;UACzBD,MAAM,CAACG,QAAQ,CAAC;QACpB;MACJ,CAAC,MACI,IAAIrd,KAAK,CAAC6c,UAAU,CAAC,EAAE;QACxB,IAAMla,MAAM,GAAG,IAAIkU,GAAG,CAACgG,UAAU,CAAC;QAClC,IAAII,OAAO,EAAE;UACTta,MAAM,CAACiU,GAAG,CAACmG,YAAY,CAAC;QAC5B,CAAC,MACI;UACDpa,MAAM,CAACmU,MAAM,CAACiG,YAAY,CAAC;QAC/B;QACAG,MAAM,CAACva,MAAM,CAAC;MAClB,CAAC,MACI;QACDua,MAAM,CAACK,gBAAgB,CAAChc,EAAE,EAAE0b,OAAO,CAAC,CAAC;MACzC;IACJ,CAAC,CAAC;EACN,CAAC;EACD;EACAb,OAAO,EAAEoB,UAAU;EACnBlB,YAAY,WAAAA,aAAC/a,EAAE,EAAEkc,OAAO,EAAEnO,KAAK,EAAE;IAC7B/N,EAAE,CAAC0a,OAAO,GAAGd,gBAAgB,CAAC7L,KAAK,CAAC;IACpCkO,UAAU,CAACjc,EAAE,EAAEkc,OAAO,EAAEnO,KAAK,CAAC;EAClC;AACJ,CAAC;AACD,SAASkO,UAAUA,CAACjc,EAAE,EAAAmc,KAAA,EAAuBpO,KAAK,EAAE;EAAA,IAA1B1L,KAAK,GAAA8Z,KAAA,CAAL9Z,KAAK;IAAE+Z,QAAQ,GAAAD,KAAA,CAARC,QAAQ;EACrCpc,EAAE,CAACub,WAAW,GAAGlZ,KAAK;EACtB,IAAI3E,OAAO,CAAC2E,KAAK,CAAC,EAAE;IAChBrC,EAAE,CAAC0b,OAAO,GAAGld,YAAY,CAAC6D,KAAK,EAAE0L,KAAK,CAAChO,KAAK,CAACsC,KAAK,CAAC,GAAG,CAAC,CAAC;EAC5D,CAAC,MACI,IAAI5D,KAAK,CAAC4D,KAAK,CAAC,EAAE;IACnBrC,EAAE,CAAC0b,OAAO,GAAGrZ,KAAK,CAACga,GAAG,CAACtO,KAAK,CAAChO,KAAK,CAACsC,KAAK,CAAC;EAC7C,CAAC,MACI,IAAIA,KAAK,KAAK+Z,QAAQ,EAAE;IACzBpc,EAAE,CAAC0b,OAAO,GAAGhd,UAAU,CAAC2D,KAAK,EAAE2Z,gBAAgB,CAAChc,EAAE,EAAE,IAAI,CAAC,CAAC;EAC9D;AACJ;AACA,IAAMsc,WAAW,GAAG;EAChBnC,OAAO,WAAAA,QAACna,EAAE,EAAAuc,KAAA,EAAaxO,KAAK,EAAE;IAAA,IAAhB1L,KAAK,GAAAka,KAAA,CAALla,KAAK;IACfrC,EAAE,CAAC0b,OAAO,GAAGhd,UAAU,CAAC2D,KAAK,EAAE0L,KAAK,CAAChO,KAAK,CAACsC,KAAK,CAAC;IACjDrC,EAAE,CAAC0a,OAAO,GAAGd,gBAAgB,CAAC7L,KAAK,CAAC;IACpC7G,gBAAgB,CAAClH,EAAE,EAAE,QAAQ,EAAE,YAAM;MACjCA,EAAE,CAAC0a,OAAO,CAACe,QAAQ,CAACzb,EAAE,CAAC,CAAC;IAC5B,CAAC,CAAC;EACN,CAAC;EACD+a,YAAY,WAAAA,aAAC/a,EAAE,EAAAwc,KAAA,EAAuBzO,KAAK,EAAE;IAAA,IAA1B1L,KAAK,GAAAma,KAAA,CAALna,KAAK;MAAE+Z,QAAQ,GAAAI,KAAA,CAARJ,QAAQ;IAC9Bpc,EAAE,CAAC0a,OAAO,GAAGd,gBAAgB,CAAC7L,KAAK,CAAC;IACpC,IAAI1L,KAAK,KAAK+Z,QAAQ,EAAE;MACpBpc,EAAE,CAAC0b,OAAO,GAAGhd,UAAU,CAAC2D,KAAK,EAAE0L,KAAK,CAAChO,KAAK,CAACsC,KAAK,CAAC;IACrD;EACJ;AACJ,CAAC;AACD,IAAMoa,YAAY,GAAG;EACjB;EACArB,IAAI,EAAE,IAAI;EACVjB,OAAO,WAAAA,QAACna,EAAE,EAAA0c,KAAA,EAAoC3O,KAAK,EAAE;IAAA,IAAvC1L,KAAK,GAAAqa,KAAA,CAALra,KAAK;MAAeoY,MAAM,GAAAiC,KAAA,CAAnBpC,SAAS,CAAIG,MAAM;IACpC,IAAMkC,UAAU,GAAGle,KAAK,CAAC4D,KAAK,CAAC;IAC/B6E,gBAAgB,CAAClH,EAAE,EAAE,QAAQ,EAAE,YAAM;MACjC,IAAM4c,WAAW,GAAGpO,KAAK,CAACqO,SAAS,CAACtE,MAAM,CACrC5P,IAAI,CAAC3I,EAAE,CAACqH,OAAO,EAAE,UAACyV,CAAC;QAAA,OAAKA,CAAC,CAACC,QAAQ;MAAA,EAAC,CACnClU,GAAG,CAAC,UAACiU,CAAC;QAAA,OAAKrC,MAAM,GAAGvc,QAAQ,CAACud,QAAQ,CAACqB,CAAC,CAAC,CAAC,GAAGrB,QAAQ,CAACqB,CAAC,CAAC;MAAA,EAAC;MAC7D9c,EAAE,CAAC0a,OAAO,CAAC1a,EAAE,CAACG,QAAQ,GAChBwc,UAAU,GACN,IAAIrH,GAAG,CAACsH,WAAW,CAAC,GACpBA,WAAW,GACfA,WAAW,CAAC,CAAC,CAAC,CAAC;IACzB,CAAC,CAAC;IACF5c,EAAE,CAAC0a,OAAO,GAAGd,gBAAgB,CAAC7L,KAAK,CAAC;EACxC,CAAC;EACD;EACA;EACA8M,OAAO,WAAAA,QAAC7a,EAAE,EAAAgd,MAAA,EAAa;IAAA,IAAT3a,KAAK,GAAA2a,MAAA,CAAL3a,KAAK;IACf4a,WAAW,CAACjd,EAAE,EAAEqC,KAAK,CAAC;EAC1B,CAAC;EACD0Y,YAAY,WAAAA,aAAC/a,EAAE,EAAEkd,QAAQ,EAAEnP,KAAK,EAAE;IAC9B/N,EAAE,CAAC0a,OAAO,GAAGd,gBAAgB,CAAC7L,KAAK,CAAC;EACxC,CAAC;EACDoP,OAAO,WAAAA,QAACnd,EAAE,EAAAod,MAAA,EAAa;IAAA,IAAT/a,KAAK,GAAA+a,MAAA,CAAL/a,KAAK;IACf4a,WAAW,CAACjd,EAAE,EAAEqC,KAAK,CAAC;EAC1B;AACJ,CAAC;AACD,SAAS4a,WAAWA,CAACjd,EAAE,EAAEqC,KAAK,EAAE;EAC5B,IAAMgb,UAAU,GAAGrd,EAAE,CAACG,QAAQ;EAC9B,IAAIkd,UAAU,IAAI,CAAC3f,OAAO,CAAC2E,KAAK,CAAC,IAAI,CAAC5D,KAAK,CAAC4D,KAAK,CAAC,EAAE;IAC/CqD,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAClCzJ,IAAI,CAAC,yFAAA4F,MAAA,CACU2K,MAAM,CAACmQ,SAAS,CAACS,QAAQ,CAAC3U,IAAI,CAACtG,KAAK,CAAC,CAACsC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAG,CAAC;IACzE;EACJ;EACA,KAAK,IAAIN,CAAC,GAAG,CAAC,EAAEkZ,CAAC,GAAGvd,EAAE,CAACqH,OAAO,CAAC/C,MAAM,EAAED,CAAC,GAAGkZ,CAAC,EAAElZ,CAAC,EAAE,EAAE;IAC/C,IAAMmZ,MAAM,GAAGxd,EAAE,CAACqH,OAAO,CAAChD,CAAC,CAAC;IAC5B,IAAMoZ,WAAW,GAAGhC,QAAQ,CAAC+B,MAAM,CAAC;IACpC,IAAIH,UAAU,EAAE;MACZ,IAAI3f,OAAO,CAAC2E,KAAK,CAAC,EAAE;QAChBmb,MAAM,CAACT,QAAQ,GAAGve,YAAY,CAAC6D,KAAK,EAAEob,WAAW,CAAC,GAAG,CAAC,CAAC;MAC3D,CAAC,MACI;QACDD,MAAM,CAACT,QAAQ,GAAG1a,KAAK,CAACga,GAAG,CAACoB,WAAW,CAAC;MAC5C;IACJ,CAAC,MACI;MACD,IAAI/e,UAAU,CAAC+c,QAAQ,CAAC+B,MAAM,CAAC,EAAEnb,KAAK,CAAC,EAAE;QACrC,IAAIrC,EAAE,CAAC0d,aAAa,KAAKrZ,CAAC,EACtBrE,EAAE,CAAC0d,aAAa,GAAGrZ,CAAC;QACxB;MACJ;IACJ;EACJ;EACA,IAAI,CAACgZ,UAAU,IAAIrd,EAAE,CAAC0d,aAAa,KAAK,CAAC,CAAC,EAAE;IACxC1d,EAAE,CAAC0d,aAAa,GAAG,CAAC,CAAC;EACzB;AACJ;AACA;AACA,SAASjC,QAAQA,CAACzb,EAAE,EAAE;EAClB,OAAO,QAAQ,IAAIA,EAAE,GAAGA,EAAE,CAACqB,MAAM,GAAGrB,EAAE,CAACqC,KAAK;AAChD;AACA;AACA,SAAS2Z,gBAAgBA,CAAChc,EAAE,EAAE0b,OAAO,EAAE;EACnC,IAAMzY,GAAG,GAAGyY,OAAO,GAAG,YAAY,GAAG,aAAa;EAClD,OAAOzY,GAAG,IAAIjD,EAAE,GAAGA,EAAE,CAACiD,GAAG,CAAC,GAAGyY,OAAO;AACxC;AACA,IAAMiC,aAAa,GAAG;EAClBxD,OAAO,WAAAA,QAACna,EAAE,EAAEkc,OAAO,EAAEnO,KAAK,EAAE;IACxB6P,aAAa,CAAC5d,EAAE,EAAEkc,OAAO,EAAEnO,KAAK,EAAE,IAAI,EAAE,SAAS,CAAC;EACtD,CAAC;EACD8M,OAAO,WAAAA,QAAC7a,EAAE,EAAEkc,OAAO,EAAEnO,KAAK,EAAE;IACxB6P,aAAa,CAAC5d,EAAE,EAAEkc,OAAO,EAAEnO,KAAK,EAAE,IAAI,EAAE,SAAS,CAAC;EACtD,CAAC;EACDgN,YAAY,WAAAA,aAAC/a,EAAE,EAAEkc,OAAO,EAAEnO,KAAK,EAAE8P,SAAS,EAAE;IACxCD,aAAa,CAAC5d,EAAE,EAAEkc,OAAO,EAAEnO,KAAK,EAAE8P,SAAS,EAAE,cAAc,CAAC;EAChE,CAAC;EACDV,OAAO,WAAAA,QAACnd,EAAE,EAAEkc,OAAO,EAAEnO,KAAK,EAAE8P,SAAS,EAAE;IACnCD,aAAa,CAAC5d,EAAE,EAAEkc,OAAO,EAAEnO,KAAK,EAAE8P,SAAS,EAAE,SAAS,CAAC;EAC3D;AACJ,CAAC;AACD,SAASD,aAAaA,CAAC5d,EAAE,EAAEkc,OAAO,EAAEnO,KAAK,EAAE8P,SAAS,EAAE3L,IAAI,EAAE;EACxD,IAAI4L,UAAU;EACd,QAAQ9d,EAAE,CAACmF,OAAO;IACd,KAAK,QAAQ;MACT2Y,UAAU,GAAGrB,YAAY;MACzB;IACJ,KAAK,UAAU;MACXqB,UAAU,GAAG5D,UAAU;MACvB;IACJ;MACI,QAAQnM,KAAK,CAAChO,KAAK,IAAIgO,KAAK,CAAChO,KAAK,CAACuF,IAAI;QACnC,KAAK,UAAU;UACXwY,UAAU,GAAG3C,cAAc;UAC3B;QACJ,KAAK,OAAO;UACR2C,UAAU,GAAGxB,WAAW;UACxB;QACJ;UACIwB,UAAU,GAAG5D,UAAU;MAC/B;EACR;EACA,IAAMpR,EAAE,GAAGgV,UAAU,CAAC5L,IAAI,CAAC;EAC3BpJ,EAAE,IAAIA,EAAE,CAAC9I,EAAE,EAAEkc,OAAO,EAAEnO,KAAK,EAAE8P,SAAS,CAAC;AAC3C;AACA;AACA;AACA,SAASE,gBAAgBA,CAAA,EAAG;EACxB7D,UAAU,CAAC8D,WAAW,GAAG,UAAAC,MAAA;IAAA,IAAG5b,KAAK,GAAA4b,MAAA,CAAL5b,KAAK;IAAA,OAAQ;MAAEA,KAAK,EAALA;IAAM,CAAC;EAAA,CAAC;EACnDia,WAAW,CAAC0B,WAAW,GAAG,UAAAE,MAAA,EAAYnQ,KAAK,EAAK;IAAA,IAAnB1L,KAAK,GAAA6b,MAAA,CAAL7b,KAAK;IAC9B,IAAI0L,KAAK,CAAChO,KAAK,IAAIrB,UAAU,CAACqP,KAAK,CAAChO,KAAK,CAACsC,KAAK,EAAEA,KAAK,CAAC,EAAE;MACrD,OAAO;QAAEqZ,OAAO,EAAE;MAAK,CAAC;IAC5B;EACJ,CAAC;EACDP,cAAc,CAAC6C,WAAW,GAAG,UAAAG,MAAA,EAAYpQ,KAAK,EAAK;IAAA,IAAnB1L,KAAK,GAAA8b,MAAA,CAAL9b,KAAK;IACjC,IAAI3E,OAAO,CAAC2E,KAAK,CAAC,EAAE;MAChB,IAAI0L,KAAK,CAAChO,KAAK,IAAIvB,YAAY,CAAC6D,KAAK,EAAE0L,KAAK,CAAChO,KAAK,CAACsC,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE;QAC5D,OAAO;UAAEqZ,OAAO,EAAE;QAAK,CAAC;MAC5B;IACJ,CAAC,MACI,IAAIjd,KAAK,CAAC4D,KAAK,CAAC,EAAE;MACnB,IAAI0L,KAAK,CAAChO,KAAK,IAAIsC,KAAK,CAACga,GAAG,CAACtO,KAAK,CAAChO,KAAK,CAACsC,KAAK,CAAC,EAAE;QAC7C,OAAO;UAAEqZ,OAAO,EAAE;QAAK,CAAC;MAC5B;IACJ,CAAC,MACI,IAAIrZ,KAAK,EAAE;MACZ,OAAO;QAAEqZ,OAAO,EAAE;MAAK,CAAC;IAC5B;EACJ,CAAC;AACL;AAEA,IAAM0C,eAAe,GAAG,CAAC,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,CAAC;AACxD,IAAMC,cAAc,GAAG;EACnBC,IAAI,EAAE,SAAAA,KAAA7Y,CAAC;IAAA,OAAIA,CAAC,CAAC8Y,eAAe,CAAC,CAAC;EAAA;EAC9BC,OAAO,EAAE,SAAAA,QAAA/Y,CAAC;IAAA,OAAIA,CAAC,CAACgZ,cAAc,CAAC,CAAC;EAAA;EAChCC,IAAI,EAAE,SAAAA,KAAAjZ,CAAC;IAAA,OAAIA,CAAC,CAACgR,MAAM,KAAKhR,CAAC,CAACkZ,aAAa;EAAA;EACvCC,IAAI,EAAE,SAAAA,KAAAnZ,CAAC;IAAA,OAAI,CAACA,CAAC,CAACoZ,OAAO;EAAA;EACrBC,KAAK,EAAE,SAAAA,MAAArZ,CAAC;IAAA,OAAI,CAACA,CAAC,CAACsZ,QAAQ;EAAA;EACvBC,GAAG,EAAE,SAAAA,IAAAvZ,CAAC;IAAA,OAAI,CAACA,CAAC,CAACwZ,MAAM;EAAA;EACnBC,IAAI,EAAE,SAAAA,KAAAzZ,CAAC;IAAA,OAAI,CAACA,CAAC,CAAC0Z,OAAO;EAAA;EACrB9F,IAAI,EAAE,SAAAA,KAAA5T,CAAC;IAAA,OAAI,QAAQ,IAAIA,CAAC,IAAIA,CAAC,CAAC2Z,MAAM,KAAK,CAAC;EAAA;EAC1CC,MAAM,EAAE,SAAAA,OAAA5Z,CAAC;IAAA,OAAI,QAAQ,IAAIA,CAAC,IAAIA,CAAC,CAAC2Z,MAAM,KAAK,CAAC;EAAA;EAC5CE,KAAK,EAAE,SAAAA,MAAA7Z,CAAC;IAAA,OAAI,QAAQ,IAAIA,CAAC,IAAIA,CAAC,CAAC2Z,MAAM,KAAK,CAAC;EAAA;EAC3CG,KAAK,EAAE,SAAAA,MAAC9Z,CAAC,EAAE6U,SAAS;IAAA,OAAK8D,eAAe,CAAC/L,IAAI,CAAC,UAAAhK,CAAC;MAAA,OAAI5C,CAAC,IAAA1D,MAAA,CAAIsG,CAAC,SAAM,IAAI,CAACiS,SAAS,CAAClV,QAAQ,CAACiD,CAAC,CAAC;IAAA,EAAC;EAAA;AAC9F,CAAC;AACD;AACA;AACA;AACA,IAAMmX,aAAa,GAAG,SAAhBA,aAAaA,CAAI1W,EAAE,EAAEwR,SAAS,EAAK;EACrC,OAAO,UAACnT,KAAK,EAAc;IACvB,KAAK,IAAI9C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiW,SAAS,CAAChW,MAAM,EAAED,CAAC,EAAE,EAAE;MACvC,IAAMob,KAAK,GAAGpB,cAAc,CAAC/D,SAAS,CAACjW,CAAC,CAAC,CAAC;MAC1C,IAAIob,KAAK,IAAIA,KAAK,CAACtY,KAAK,EAAEmT,SAAS,CAAC,EAChC;IACR;IAAC,SAAAoF,KAAA,GAAAhY,SAAA,CAAApD,MAAA,EALaiK,IAAI,OAAAC,KAAA,CAAAkR,KAAA,OAAAA,KAAA,WAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;MAAJpR,IAAI,CAAAoR,KAAA,QAAAjY,SAAA,CAAAiY,KAAA;IAAA;IAMlB,OAAO7W,EAAE,CAAAqJ,KAAA,UAAChL,KAAK,EAAApF,MAAA,CAAKwM,IAAI,EAAC;EAC7B,CAAC;AACL,CAAC;AACD;AACA;AACA,IAAMqR,QAAQ,GAAG;EACbC,GAAG,EAAE,QAAQ;EACbC,KAAK,EAAE,GAAG;EACVC,EAAE,EAAE,UAAU;EACd1G,IAAI,EAAE,YAAY;EAClBiG,KAAK,EAAE,aAAa;EACpBU,IAAI,EAAE,YAAY;EAClBzK,MAAM,EAAE;AACZ,CAAC;AACD;AACA;AACA;AACA,IAAM0K,QAAQ,GAAG,SAAXA,QAAQA,CAAInX,EAAE,EAAEwR,SAAS,EAAK;EAChC,OAAO,UAACnT,KAAK,EAAK;IACd,IAAI,EAAE,KAAK,IAAIA,KAAK,CAAC,EAAE;MACnB;IACJ;IACA,IAAM+Y,QAAQ,GAAGviB,SAAS,CAACwJ,KAAK,CAAClE,GAAG,CAAC;IACrC,IAAIqX,SAAS,CAACjI,IAAI,CAAC,UAAA8N,CAAC;MAAA,OAAIA,CAAC,KAAKD,QAAQ,IAAIN,QAAQ,CAACO,CAAC,CAAC,KAAKD,QAAQ;IAAA,EAAC,EAAE;MACjE,OAAOpX,EAAE,CAAC3B,KAAK,CAAC;IACpB;EACJ,CAAC;AACL,CAAC;AAED,IAAMiZ,KAAK,GAAG;EACVC,WAAW,WAAAA,YAACrgB,EAAE,EAAAsgB,MAAA,EAAAC,MAAA,EAA6B;IAAA,IAAzBle,KAAK,GAAAie,MAAA,CAALje,KAAK;IAAA,IAAMme,UAAU,GAAAD,MAAA,CAAVC,UAAU;IACnCxgB,EAAE,CAACygB,IAAI,GAAGzgB,EAAE,CAAC+C,KAAK,CAACK,OAAO,KAAK,MAAM,GAAG,EAAE,GAAGpD,EAAE,CAAC+C,KAAK,CAACK,OAAO;IAC7D,IAAIod,UAAU,IAAIne,KAAK,EAAE;MACrBme,UAAU,CAACE,WAAW,CAAC1gB,EAAE,CAAC;IAC9B,CAAC,MACI;MACD2gB,UAAU,CAAC3gB,EAAE,EAAEqC,KAAK,CAAC;IACzB;EACJ,CAAC;EACDwY,OAAO,WAAAA,QAAC7a,EAAE,EAAA4gB,MAAA,EAAAC,MAAA,EAA6B;IAAA,IAAzBxe,KAAK,GAAAue,MAAA,CAALve,KAAK;IAAA,IAAMme,UAAU,GAAAK,MAAA,CAAVL,UAAU;IAC/B,IAAIA,UAAU,IAAIne,KAAK,EAAE;MACrBme,UAAU,CAAC7L,KAAK,CAAC3U,EAAE,CAAC;IACxB;EACJ,CAAC;EACDmd,OAAO,WAAAA,QAACnd,EAAE,EAAA8gB,MAAA,EAAAC,MAAA,EAAuC;IAAA,IAAnC1e,KAAK,GAAAye,MAAA,CAALze,KAAK;MAAE+Z,QAAQ,GAAA0E,MAAA,CAAR1E,QAAQ;IAAA,IAAMoE,UAAU,GAAAO,MAAA,CAAVP,UAAU;IACzC,IAAI,CAACne,KAAK,KAAK,CAAC+Z,QAAQ,EACpB;IACJ,IAAIoE,UAAU,EAAE;MACZ,IAAIne,KAAK,EAAE;QACPme,UAAU,CAACE,WAAW,CAAC1gB,EAAE,CAAC;QAC1B2gB,UAAU,CAAC3gB,EAAE,EAAE,IAAI,CAAC;QACpBwgB,UAAU,CAAC7L,KAAK,CAAC3U,EAAE,CAAC;MACxB,CAAC,MACI;QACDwgB,UAAU,CAAC5L,KAAK,CAAC5U,EAAE,EAAE,YAAM;UACvB2gB,UAAU,CAAC3gB,EAAE,EAAE,KAAK,CAAC;QACzB,CAAC,CAAC;MACN;IACJ,CAAC,MACI;MACD2gB,UAAU,CAAC3gB,EAAE,EAAEqC,KAAK,CAAC;IACzB;EACJ,CAAC;EACD2e,aAAa,WAAAA,cAAChhB,EAAE,EAAAihB,MAAA,EAAa;IAAA,IAAT5e,KAAK,GAAA4e,MAAA,CAAL5e,KAAK;IACrBse,UAAU,CAAC3gB,EAAE,EAAEqC,KAAK,CAAC;EACzB;AACJ,CAAC;AACD,SAASse,UAAUA,CAAC3gB,EAAE,EAAEqC,KAAK,EAAE;EAC3BrC,EAAE,CAAC+C,KAAK,CAACK,OAAO,GAAGf,KAAK,GAAGrC,EAAE,CAACygB,IAAI,GAAG,MAAM;AAC/C;AACA;AACA;AACA,SAASS,eAAeA,CAAA,EAAG;EACvBd,KAAK,CAACpC,WAAW,GAAG,UAAAmD,MAAA,EAAe;IAAA,IAAZ9e,KAAK,GAAA8e,MAAA,CAAL9e,KAAK;IACxB,IAAI,CAACA,KAAK,EAAE;MACR,OAAO;QAAEU,KAAK,EAAE;UAAEK,OAAO,EAAE;QAAO;MAAE,CAAC;IACzC;EACJ,CAAC;AACL;AAEA,IAAMge,eAAe,GAAGhjB,MAAM,CAAC;EAAE4K,SAAS,EAATA;AAAU,CAAC,EAAE9J,OAAO,CAAC;AACtD;AACA;AACA,IAAImiB,QAAQ;AACZ,IAAIC,gBAAgB,GAAG,KAAK;AAC5B,SAASC,cAAcA,CAAA,EAAG;EACtB,OAAQF,QAAQ,KACXA,QAAQ,GAAG/jB,cAAc,CAAC8jB,eAAe,CAAC,CAAC;AACpD;AACA,SAASI,uBAAuBA,CAAA,EAAG;EAC/BH,QAAQ,GAAGC,gBAAgB,GACrBD,QAAQ,GACR7jB,uBAAuB,CAAC4jB,eAAe,CAAC;EAC9CE,gBAAgB,GAAG,IAAI;EACvB,OAAOD,QAAQ;AACnB;AACA;AACA,IAAM9V,MAAM,GAAI,SAAVA,MAAMA,CAAA,EAAiB;EAAA,IAAAkW,eAAA;EACzB,CAAAA,eAAA,GAAAF,cAAc,CAAC,CAAC,EAAChW,MAAM,CAAA4G,KAAA,CAAAsP,eAAA,EAAA/Z,SAAQ,CAAC;AACpC,CAAE;AACF,IAAMwC,OAAO,GAAI,SAAXA,OAAOA,CAAA,EAAiB;EAAA,IAAAwX,qBAAA;EAC1B,CAAAA,qBAAA,GAAAF,uBAAuB,CAAC,CAAC,EAACtX,OAAO,CAAAiI,KAAA,CAAAuP,qBAAA,EAAAha,SAAQ,CAAC;AAC9C,CAAE;AACF,IAAMia,SAAS,GAAI,SAAbA,SAASA,CAAA,EAAiB;EAAA,IAAAC,gBAAA;EAC5B,IAAMC,GAAG,GAAG,CAAAD,gBAAA,GAAAL,cAAc,CAAC,CAAC,EAACI,SAAS,CAAAxP,KAAA,CAAAyP,gBAAA,EAAAla,SAAQ,CAAC;EAC/C,IAAKhC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAG;IACzCkc,oBAAoB,CAACD,GAAG,CAAC;IACzBE,0BAA0B,CAACF,GAAG,CAAC;EACnC;EACA,IAAQG,KAAK,GAAKH,GAAG,CAAbG,KAAK;EACbH,GAAG,CAACG,KAAK,GAAG,UAACC,mBAAmB,EAAK;IACjC,IAAMvI,SAAS,GAAGwI,kBAAkB,CAACD,mBAAmB,CAAC;IACzD,IAAI,CAACvI,SAAS,EACV;IACJ,IAAMrJ,SAAS,GAAGwR,GAAG,CAACM,UAAU;IAChC,IAAI,CAAClkB,UAAU,CAACoS,SAAS,CAAC,IAAI,CAACA,SAAS,CAAC9E,MAAM,IAAI,CAAC8E,SAAS,CAAC1O,QAAQ,EAAE;MACpE;MACA;MACA;MACA;MACA0O,SAAS,CAAC1O,QAAQ,GAAG+X,SAAS,CAAC5X,SAAS;IAC5C;IACA;IACA4X,SAAS,CAAC5X,SAAS,GAAG,EAAE;IACxB,IAAM4N,KAAK,GAAGsS,KAAK,CAACtI,SAAS,EAAE,KAAK,EAAEA,SAAS,YAAY0I,UAAU,CAAC;IACtE,IAAI1I,SAAS,YAAY2I,OAAO,EAAE;MAC9B3I,SAAS,CAAChX,eAAe,CAAC,SAAS,CAAC;MACpCgX,SAAS,CAACtZ,YAAY,CAAC,YAAY,EAAE,EAAE,CAAC;IAC5C;IACA,OAAOsP,KAAK;EAChB,CAAC;EACD,OAAOmS,GAAG;AACd,CAAE;AACF,IAAMS,YAAY,GAAI,SAAhBA,YAAYA,CAAA,EAAiB;EAAA,IAAAC,sBAAA;EAC/B,IAAMV,GAAG,GAAG,CAAAU,sBAAA,GAAAf,uBAAuB,CAAC,CAAC,EAACG,SAAS,CAAAxP,KAAA,CAAAoQ,sBAAA,EAAA7a,SAAQ,CAAC;EACxD,IAAKhC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAG;IACzCkc,oBAAoB,CAACD,GAAG,CAAC;IACzBE,0BAA0B,CAACF,GAAG,CAAC;EACnC;EACA,IAAQG,KAAK,GAAKH,GAAG,CAAbG,KAAK;EACbH,GAAG,CAACG,KAAK,GAAG,UAACC,mBAAmB,EAAK;IACjC,IAAMvI,SAAS,GAAGwI,kBAAkB,CAACD,mBAAmB,CAAC;IACzD,IAAIvI,SAAS,EAAE;MACX,OAAOsI,KAAK,CAACtI,SAAS,EAAE,IAAI,EAAEA,SAAS,YAAY0I,UAAU,CAAC;IAClE;EACJ,CAAC;EACD,OAAOP,GAAG;AACd,CAAE;AACF,SAASC,oBAAoBA,CAACD,GAAG,EAAE;EAC/B;EACA;EACAnV,MAAM,CAACW,cAAc,CAACwU,GAAG,CAACW,MAAM,EAAE,aAAa,EAAE;IAC7CngB,KAAK,EAAE,SAAAA,MAACzC,GAAG;MAAA,OAAKjB,SAAS,CAACiB,GAAG,CAAC,IAAIhB,QAAQ,CAACgB,GAAG,CAAC;IAAA;IAC/C6iB,QAAQ,EAAE;EACd,CAAC,CAAC;AACN;AACA;AACA,SAASV,0BAA0BA,CAACF,GAAG,EAAE;EACrC,IAAItkB,aAAa,CAAC,CAAC,EAAE;IACjB,IAAMmlB,eAAe,GAAGb,GAAG,CAACW,MAAM,CAACE,eAAe;IAClDhW,MAAM,CAACW,cAAc,CAACwU,GAAG,CAACW,MAAM,EAAE,iBAAiB,EAAE;MACjD5gB,GAAG,WAAAA,IAAA,EAAG;QACF,OAAO8gB,eAAe;MAC1B,CAAC;MACDvgB,GAAG,WAAAA,IAAA,EAAG;QACFhG,IAAI,CAAC,wGAC6C,CAAC;MACvD;IACJ,CAAC,CAAC;IACF,IAAMwmB,eAAe,GAAGd,GAAG,CAACW,MAAM,CAACG,eAAe;IAClD,IAAMC,GAAG,GAAG,mJACmE,mEACT,wEACK,kFACU,gGACY,wKACwE;IACzKlW,MAAM,CAACW,cAAc,CAACwU,GAAG,CAACW,MAAM,EAAE,iBAAiB,EAAE;MACjD5gB,GAAG,WAAAA,IAAA,EAAG;QACFzF,IAAI,CAACymB,GAAG,CAAC;QACT,OAAOD,eAAe;MAC1B,CAAC;MACDxgB,GAAG,WAAAA,IAAA,EAAG;QACFhG,IAAI,CAACymB,GAAG,CAAC;MACb;IACJ,CAAC,CAAC;EACN;AACJ;AACA,SAASV,kBAAkBA,CAACxI,SAAS,EAAE;EACnC,IAAIjc,QAAQ,CAACic,SAAS,CAAC,EAAE;IACrB,IAAM7E,GAAG,GAAG9V,QAAQ,CAACgC,aAAa,CAAC2Y,SAAS,CAAC;IAC7C,IAAKhU,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAK,CAACiP,GAAG,EAAE;MACjD1Y,IAAI,iDAAA4F,MAAA,CAAgD2X,SAAS,sBAAkB,CAAC;IACpF;IACA,OAAO7E,GAAG;EACd;EACA,IAAKnP,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IACtCM,MAAM,CAAC2c,UAAU,IACjBnJ,SAAS,YAAYxT,MAAM,CAAC2c,UAAU,IACtCnJ,SAAS,CAACxO,IAAI,KAAK,QAAQ,EAAE;IAC7B/O,IAAI,oFAAoF,CAAC;EAC7F;EACA,OAAOud,SAAS;AACpB;AACA,IAAIoJ,uBAAuB,GAAG,KAAK;AACnC;AACA;AACA;AACA,IAAMC,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAA,EAAS;EAC3B,IAAI,CAACD,uBAAuB,EAAE;IAC1BA,uBAAuB,GAAG,IAAI;IAC9B/E,gBAAgB,CAAC,CAAC;IAClBmD,eAAe,CAAC,CAAC;EACrB;AACJ,CAAC;AAGL,SAASrQ,UAAU,EAAEmI,eAAe,EAAEjP,UAAU,EAAE4X,SAAS,EAAEW,YAAY,EAAElZ,mBAAmB,EAAEa,sBAAsB,EAAEC,OAAO,EAAE6Y,oBAAoB,EAAExX,MAAM,EAAE0D,YAAY,EAAEI,UAAU,EAAE8L,cAAc,EAAEwC,aAAa,EAAErB,WAAW,EAAEG,YAAY,EAAEvC,UAAU,EAAEkG,KAAK,EAAEH,QAAQ,EAAET,aAAa"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}