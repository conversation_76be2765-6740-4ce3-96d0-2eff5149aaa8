{"ast": null, "code": "import { createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, pushScopeId as _pushScopeId, popScopeId as _popScopeId } from \"vue\";\nvar _withScopeId = function _withScopeId(n) {\n  return _pushScopeId(\"data-v-8724d12c\"), n = n(), _popScopeId(), n;\n};\nvar _hoisted_1 = {\n  class: \"home\"\n};\nvar _hoisted_2 = [\"src\"];\nvar _hoisted_3 = {\n  class: \"ktx\"\n};\nvar _hoisted_4 = {\n  class: \"b\"\n};\nvar _hoisted_5 = {\n  class: \"t\"\n};\nvar _hoisted_6 = {\n  class: \"check_money\"\n};\nvar _hoisted_7 = {\n  class: \"text\"\n};\nvar _hoisted_8 = {\n  class: \"tixian_money\"\n};\nvar _hoisted_9 = {\n  class: \"buttons\"\n};\nvar _hoisted_10 = [\"innerHTML\"];\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_van_nav_bar = _resolveComponent(\"van-nav-bar\");\n  var _component_van_radio = _resolveComponent(\"van-radio\");\n  var _component_van_radio_group = _resolveComponent(\"van-radio-group\");\n  var _component_van_field = _resolveComponent(\"van-field\");\n  var _component_van_cell_group = _resolveComponent(\"van-cell-group\");\n  var _component_van_button = _resolveComponent(\"van-button\");\n  var _component_van_form = _resolveComponent(\"van-form\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_van_nav_bar, {\n    title: _ctx.$t('msg.tikuan'),\n    \"left-arrow\": \"\",\n    onClickLeft: _cache[0] || (_cache[0] = function ($event) {\n      return _ctx.$router.go(-1);\n    }),\n    onClickRight: $setup.clickRight\n  }, {\n    right: _withCtx(function () {\n      return [_createElementVNode(\"img\", {\n        src: require('@/assets/images/self/hank/tel.png'),\n        class: \"img\",\n        alt: \"\"\n      }, null, 8, _hoisted_2)];\n    }),\n    _: 1\n  }, 8, [\"title\", \"onClickRight\"]), _createVNode(_component_van_form, {\n    onSubmit: $setup.onSubmit\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_van_cell_group, {\n        inset: \"\"\n      }, {\n        default: _withCtx(function () {\n          return [_createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"div\", _hoisted_4, _toDisplayString($setup.currency) + \" \" + _toDisplayString($setup.money), 1), _createElementVNode(\"div\", _hoisted_5, _toDisplayString(_ctx.$t('msg.my_yu_e')), 1)]), _createElementVNode(\"div\", _hoisted_6, [_createElementVNode(\"div\", _hoisted_7, [_createVNode(_component_van_radio_group, {\n            modelValue: $setup.types,\n            \"onUpdate:modelValue\": _cache[1] || (_cache[1] = function ($event) {\n              return $setup.types = $event;\n            }),\n            direction: \"horizontal\"\n          }, {\n            default: _withCtx(function () {\n              return [_createVNode(_component_van_radio, {\n                name: \"1\"\n              }, {\n                default: _withCtx(function () {\n                  return [_createTextVNode(_toDisplayString(_ctx.$t('msg.usdt_tx')), 1)];\n                }),\n                _: 1\n              })];\n            }),\n            _: 1\n          }, 8, [\"modelValue\"])])]), _createElementVNode(\"div\", _hoisted_8, _toDisplayString(_ctx.$t('msg.tixian_money')), 1), _createVNode(_component_van_field, {\n            class: \"zdy\",\n            modelValue: $setup.money_check,\n            \"onUpdate:modelValue\": _cache[2] || (_cache[2] = function ($event) {\n              return $setup.money_check = $event;\n            }),\n            placeholder: _ctx.$t('msg.tixian_money')\n          }, null, 8, [\"modelValue\", \"placeholder\"]), _createVNode(_component_van_field, {\n            class: \"zdy\",\n            modelValue: $setup.paypassword,\n            \"onUpdate:modelValue\": _cache[3] || (_cache[3] = function ($event) {\n              return $setup.paypassword = $event;\n            }),\n            type: \"password\",\n            name: \"paypassword\",\n            placeholder: _ctx.$t('msg.tx_pwd'),\n            rules: [{\n              required: true,\n              message: _ctx.$t('msg.input_tx_pwd')\n            }]\n          }, null, 8, [\"modelValue\", \"placeholder\", \"rules\"])];\n        }),\n        _: 1\n      }), _createElementVNode(\"div\", _hoisted_9, [_createVNode(_component_van_button, {\n        round: \"\",\n        block: \"\",\n        type: \"primary\",\n        \"native-type\": \"submit\"\n      }, {\n        default: _withCtx(function () {\n          return [_createTextVNode(_toDisplayString(_ctx.$t('msg.true_tx')), 1)];\n        }),\n        _: 1\n      })]), _createElementVNode(\"div\", {\n        class: \"text_b\",\n        innerHTML: $setup.content\n      }, null, 8, _hoisted_10)];\n    }),\n    _: 1\n  }, 8, [\"onSubmit\"])]);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}