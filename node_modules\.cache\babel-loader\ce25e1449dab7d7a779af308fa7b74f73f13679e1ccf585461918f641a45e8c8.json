{"ast": null, "code": "function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nfunction _iterableToArrayLimit(arr, i) { var _i = null == arr ? null : \"undefined\" != typeof Symbol && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (null != _i) { var _s, _e, _x, _r, _arr = [], _n = !0, _d = !1; try { if (_x = (_i = _i.call(arr)).next, 0 === i) { if (Object(_i) !== _i) return; _n = !1; } else for (; !(_n = (_s = _x.call(_i)).done) && (_arr.push(_s.value), _arr.length !== i); _n = !0); } catch (err) { _d = !0, _e = err; } finally { try { if (!_n && null != _i.return && (_r = _i.return(), Object(_r) !== _r)) return; } finally { if (_d) throw _e; } } return _arr; } }\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\nimport { createTextVNode as _createTextVNode, createVNode as _createVNode } from \"vue\";\nimport { defineComponent } from \"vue\";\nimport { isDef, numericProp, makeStringProp, createNamespace } from \"../utils/index.mjs\";\nimport { Tag } from \"../tag/index.mjs\";\nimport { Image } from \"../image/index.mjs\";\nvar _createNamespace = createNamespace(\"card\"),\n  _createNamespace2 = _slicedToArray(_createNamespace, 2),\n  name = _createNamespace2[0],\n  bem = _createNamespace2[1];\nvar cardProps = {\n  tag: String,\n  num: numericProp,\n  desc: String,\n  thumb: String,\n  title: String,\n  price: numericProp,\n  centered: Boolean,\n  lazyLoad: Boolean,\n  currency: makeStringProp(\"\\xA5\"),\n  thumbLink: String,\n  originPrice: numericProp\n};\nvar stdin_default = defineComponent({\n  name: name,\n  props: cardProps,\n  emits: [\"click-thumb\"],\n  setup: function setup(props, _ref) {\n    var slots = _ref.slots,\n      emit = _ref.emit;\n    var renderTitle = function renderTitle() {\n      if (slots.title) {\n        return slots.title();\n      }\n      if (props.title) {\n        return _createVNode(\"div\", {\n          \"class\": [bem(\"title\"), \"van-multi-ellipsis--l2\"]\n        }, [props.title]);\n      }\n    };\n    var renderThumbTag = function renderThumbTag() {\n      if (slots.tag || props.tag) {\n        return _createVNode(\"div\", {\n          \"class\": bem(\"tag\")\n        }, [slots.tag ? slots.tag() : _createVNode(Tag, {\n          \"mark\": true,\n          \"type\": \"danger\"\n        }, {\n          default: function _default() {\n            return [props.tag];\n          }\n        })]);\n      }\n    };\n    var renderThumbImage = function renderThumbImage() {\n      if (slots.thumb) {\n        return slots.thumb();\n      }\n      return _createVNode(Image, {\n        \"src\": props.thumb,\n        \"fit\": \"cover\",\n        \"width\": \"100%\",\n        \"height\": \"100%\",\n        \"lazyLoad\": props.lazyLoad\n      }, null);\n    };\n    var renderThumb = function renderThumb() {\n      if (slots.thumb || props.thumb) {\n        return _createVNode(\"a\", {\n          \"href\": props.thumbLink,\n          \"class\": bem(\"thumb\"),\n          \"onClick\": function onClick(event) {\n            return emit(\"click-thumb\", event);\n          }\n        }, [renderThumbImage(), renderThumbTag()]);\n      }\n    };\n    var renderDesc = function renderDesc() {\n      if (slots.desc) {\n        return slots.desc();\n      }\n      if (props.desc) {\n        return _createVNode(\"div\", {\n          \"class\": [bem(\"desc\"), \"van-ellipsis\"]\n        }, [props.desc]);\n      }\n    };\n    var renderPriceText = function renderPriceText() {\n      var priceArr = props.price.toString().split(\".\");\n      return _createVNode(\"div\", null, [_createVNode(\"span\", {\n        \"class\": bem(\"price-currency\")\n      }, [props.currency]), _createVNode(\"span\", {\n        \"class\": bem(\"price-integer\")\n      }, [priceArr[0]]), _createTextVNode(\".\"), _createVNode(\"span\", {\n        \"class\": bem(\"price-decimal\")\n      }, [priceArr[1]])]);\n    };\n    return function () {\n      var _a, _b, _c;\n      var showNum = slots.num || isDef(props.num);\n      var showPrice = slots.price || isDef(props.price);\n      var showOriginPrice = slots[\"origin-price\"] || isDef(props.originPrice);\n      var showBottom = showNum || showPrice || showOriginPrice || slots.bottom;\n      var Price = showPrice && _createVNode(\"div\", {\n        \"class\": bem(\"price\")\n      }, [slots.price ? slots.price() : renderPriceText()]);\n      var OriginPrice = showOriginPrice && _createVNode(\"div\", {\n        \"class\": bem(\"origin-price\")\n      }, [slots[\"origin-price\"] ? slots[\"origin-price\"]() : \"\".concat(props.currency, \" \").concat(props.originPrice)]);\n      var Num = showNum && _createVNode(\"div\", {\n        \"class\": bem(\"num\")\n      }, [slots.num ? slots.num() : \"x\".concat(props.num)]);\n      var Footer = slots.footer && _createVNode(\"div\", {\n        \"class\": bem(\"footer\")\n      }, [slots.footer()]);\n      var Bottom = showBottom && _createVNode(\"div\", {\n        \"class\": bem(\"bottom\")\n      }, [(_a = slots[\"price-top\"]) == null ? void 0 : _a.call(slots), Price, OriginPrice, Num, (_b = slots.bottom) == null ? void 0 : _b.call(slots)]);\n      return _createVNode(\"div\", {\n        \"class\": bem()\n      }, [_createVNode(\"div\", {\n        \"class\": bem(\"header\")\n      }, [renderThumb(), _createVNode(\"div\", {\n        \"class\": bem(\"content\", {\n          centered: props.centered\n        })\n      }, [_createVNode(\"div\", null, [renderTitle(), renderDesc(), (_c = slots.tags) == null ? void 0 : _c.call(slots)]), Bottom])]), Footer]);\n    };\n  }\n});\nexport { stdin_default as default };", "map": {"version": 3, "names": ["createTextVNode", "_createTextVNode", "createVNode", "_createVNode", "defineComponent", "isDef", "numericProp", "makeStringProp", "createNamespace", "Tag", "Image", "_createNamespace", "_createNamespace2", "_slicedToArray", "name", "bem", "cardProps", "tag", "String", "num", "desc", "thumb", "title", "price", "centered", "Boolean", "lazyLoad", "currency", "thumbLink", "originPrice", "stdin_default", "props", "emits", "setup", "_ref", "slots", "emit", "renderTitle", "renderThumbTag", "default", "_default", "renderThumbImage", "renderThumb", "onClick", "event", "renderDesc", "renderPriceText", "priceArr", "toString", "split", "_a", "_b", "_c", "showNum", "showPrice", "showOriginPrice", "showBottom", "bottom", "Price", "OriginPrice", "concat", "<PERSON><PERSON>", "Footer", "footer", "Bottom", "call", "tags"], "sources": ["C:/Users/<USER>/Downloads/Telegram Desktop/vue3_3.0/vue3_3.0/node_modules/vant/es/card/Card.mjs"], "sourcesContent": ["import { createTextVNode as _createTextVNode, createVNode as _createVNode } from \"vue\";\nimport { defineComponent } from \"vue\";\nimport { isDef, numericProp, makeStringProp, createNamespace } from \"../utils/index.mjs\";\nimport { Tag } from \"../tag/index.mjs\";\nimport { Image } from \"../image/index.mjs\";\nconst [name, bem] = createNamespace(\"card\");\nconst cardProps = {\n  tag: String,\n  num: numericProp,\n  desc: String,\n  thumb: String,\n  title: String,\n  price: numericProp,\n  centered: Boolean,\n  lazyLoad: Boolean,\n  currency: makeStringProp(\"\\xA5\"),\n  thumbLink: String,\n  originPrice: numericProp\n};\nvar stdin_default = defineComponent({\n  name,\n  props: cardProps,\n  emits: [\"click-thumb\"],\n  setup(props, {\n    slots,\n    emit\n  }) {\n    const renderTitle = () => {\n      if (slots.title) {\n        return slots.title();\n      }\n      if (props.title) {\n        return _createVNode(\"div\", {\n          \"class\": [bem(\"title\"), \"van-multi-ellipsis--l2\"]\n        }, [props.title]);\n      }\n    };\n    const renderThumbTag = () => {\n      if (slots.tag || props.tag) {\n        return _createVNode(\"div\", {\n          \"class\": bem(\"tag\")\n        }, [slots.tag ? slots.tag() : _createVNode(Tag, {\n          \"mark\": true,\n          \"type\": \"danger\"\n        }, {\n          default: () => [props.tag]\n        })]);\n      }\n    };\n    const renderThumbImage = () => {\n      if (slots.thumb) {\n        return slots.thumb();\n      }\n      return _createVNode(Image, {\n        \"src\": props.thumb,\n        \"fit\": \"cover\",\n        \"width\": \"100%\",\n        \"height\": \"100%\",\n        \"lazyLoad\": props.lazyLoad\n      }, null);\n    };\n    const renderThumb = () => {\n      if (slots.thumb || props.thumb) {\n        return _createVNode(\"a\", {\n          \"href\": props.thumbLink,\n          \"class\": bem(\"thumb\"),\n          \"onClick\": (event) => emit(\"click-thumb\", event)\n        }, [renderThumbImage(), renderThumbTag()]);\n      }\n    };\n    const renderDesc = () => {\n      if (slots.desc) {\n        return slots.desc();\n      }\n      if (props.desc) {\n        return _createVNode(\"div\", {\n          \"class\": [bem(\"desc\"), \"van-ellipsis\"]\n        }, [props.desc]);\n      }\n    };\n    const renderPriceText = () => {\n      const priceArr = props.price.toString().split(\".\");\n      return _createVNode(\"div\", null, [_createVNode(\"span\", {\n        \"class\": bem(\"price-currency\")\n      }, [props.currency]), _createVNode(\"span\", {\n        \"class\": bem(\"price-integer\")\n      }, [priceArr[0]]), _createTextVNode(\".\"), _createVNode(\"span\", {\n        \"class\": bem(\"price-decimal\")\n      }, [priceArr[1]])]);\n    };\n    return () => {\n      var _a, _b, _c;\n      const showNum = slots.num || isDef(props.num);\n      const showPrice = slots.price || isDef(props.price);\n      const showOriginPrice = slots[\"origin-price\"] || isDef(props.originPrice);\n      const showBottom = showNum || showPrice || showOriginPrice || slots.bottom;\n      const Price = showPrice && _createVNode(\"div\", {\n        \"class\": bem(\"price\")\n      }, [slots.price ? slots.price() : renderPriceText()]);\n      const OriginPrice = showOriginPrice && _createVNode(\"div\", {\n        \"class\": bem(\"origin-price\")\n      }, [slots[\"origin-price\"] ? slots[\"origin-price\"]() : `${props.currency} ${props.originPrice}`]);\n      const Num = showNum && _createVNode(\"div\", {\n        \"class\": bem(\"num\")\n      }, [slots.num ? slots.num() : `x${props.num}`]);\n      const Footer = slots.footer && _createVNode(\"div\", {\n        \"class\": bem(\"footer\")\n      }, [slots.footer()]);\n      const Bottom = showBottom && _createVNode(\"div\", {\n        \"class\": bem(\"bottom\")\n      }, [(_a = slots[\"price-top\"]) == null ? void 0 : _a.call(slots), Price, OriginPrice, Num, (_b = slots.bottom) == null ? void 0 : _b.call(slots)]);\n      return _createVNode(\"div\", {\n        \"class\": bem()\n      }, [_createVNode(\"div\", {\n        \"class\": bem(\"header\")\n      }, [renderThumb(), _createVNode(\"div\", {\n        \"class\": bem(\"content\", {\n          centered: props.centered\n        })\n      }, [_createVNode(\"div\", null, [renderTitle(), renderDesc(), (_c = slots.tags) == null ? void 0 : _c.call(slots)]), Bottom])]), Footer]);\n    };\n  }\n});\nexport {\n  stdin_default as default\n};\n"], "mappings": ";;;;;;AAAA,SAASA,eAAe,IAAIC,gBAAgB,EAAEC,WAAW,IAAIC,YAAY,QAAQ,KAAK;AACtF,SAASC,eAAe,QAAQ,KAAK;AACrC,SAASC,KAAK,EAAEC,WAAW,EAAEC,cAAc,EAAEC,eAAe,QAAQ,oBAAoB;AACxF,SAASC,GAAG,QAAQ,kBAAkB;AACtC,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,IAAAC,gBAAA,GAAoBH,eAAe,CAAC,MAAM,CAAC;EAAAI,iBAAA,GAAAC,cAAA,CAAAF,gBAAA;EAApCG,IAAI,GAAAF,iBAAA;EAAEG,GAAG,GAAAH,iBAAA;AAChB,IAAMI,SAAS,GAAG;EAChBC,GAAG,EAAEC,MAAM;EACXC,GAAG,EAAEb,WAAW;EAChBc,IAAI,EAAEF,MAAM;EACZG,KAAK,EAAEH,MAAM;EACbI,KAAK,EAAEJ,MAAM;EACbK,KAAK,EAAEjB,WAAW;EAClBkB,QAAQ,EAAEC,OAAO;EACjBC,QAAQ,EAAED,OAAO;EACjBE,QAAQ,EAAEpB,cAAc,CAAC,MAAM,CAAC;EAChCqB,SAAS,EAAEV,MAAM;EACjBW,WAAW,EAAEvB;AACf,CAAC;AACD,IAAIwB,aAAa,GAAG1B,eAAe,CAAC;EAClCU,IAAI,EAAJA,IAAI;EACJiB,KAAK,EAAEf,SAAS;EAChBgB,KAAK,EAAE,CAAC,aAAa,CAAC;EACtBC,KAAK,WAAAA,MAACF,KAAK,EAAAG,IAAA,EAGR;IAAA,IAFDC,KAAK,GAAAD,IAAA,CAALC,KAAK;MACLC,IAAI,GAAAF,IAAA,CAAJE,IAAI;IAEJ,IAAMC,WAAW,GAAG,SAAdA,WAAWA,CAAA,EAAS;MACxB,IAAIF,KAAK,CAACb,KAAK,EAAE;QACf,OAAOa,KAAK,CAACb,KAAK,CAAC,CAAC;MACtB;MACA,IAAIS,KAAK,CAACT,KAAK,EAAE;QACf,OAAOnB,YAAY,CAAC,KAAK,EAAE;UACzB,OAAO,EAAE,CAACY,GAAG,CAAC,OAAO,CAAC,EAAE,wBAAwB;QAClD,CAAC,EAAE,CAACgB,KAAK,CAACT,KAAK,CAAC,CAAC;MACnB;IACF,CAAC;IACD,IAAMgB,cAAc,GAAG,SAAjBA,cAAcA,CAAA,EAAS;MAC3B,IAAIH,KAAK,CAAClB,GAAG,IAAIc,KAAK,CAACd,GAAG,EAAE;QAC1B,OAAOd,YAAY,CAAC,KAAK,EAAE;UACzB,OAAO,EAAEY,GAAG,CAAC,KAAK;QACpB,CAAC,EAAE,CAACoB,KAAK,CAAClB,GAAG,GAAGkB,KAAK,CAAClB,GAAG,CAAC,CAAC,GAAGd,YAAY,CAACM,GAAG,EAAE;UAC9C,MAAM,EAAE,IAAI;UACZ,MAAM,EAAE;QACV,CAAC,EAAE;UACD8B,OAAO,EAAE,SAAAC,SAAA;YAAA,OAAM,CAACT,KAAK,CAACd,GAAG,CAAC;UAAA;QAC5B,CAAC,CAAC,CAAC,CAAC;MACN;IACF,CAAC;IACD,IAAMwB,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAA,EAAS;MAC7B,IAAIN,KAAK,CAACd,KAAK,EAAE;QACf,OAAOc,KAAK,CAACd,KAAK,CAAC,CAAC;MACtB;MACA,OAAOlB,YAAY,CAACO,KAAK,EAAE;QACzB,KAAK,EAAEqB,KAAK,CAACV,KAAK;QAClB,KAAK,EAAE,OAAO;QACd,OAAO,EAAE,MAAM;QACf,QAAQ,EAAE,MAAM;QAChB,UAAU,EAAEU,KAAK,CAACL;MACpB,CAAC,EAAE,IAAI,CAAC;IACV,CAAC;IACD,IAAMgB,WAAW,GAAG,SAAdA,WAAWA,CAAA,EAAS;MACxB,IAAIP,KAAK,CAACd,KAAK,IAAIU,KAAK,CAACV,KAAK,EAAE;QAC9B,OAAOlB,YAAY,CAAC,GAAG,EAAE;UACvB,MAAM,EAAE4B,KAAK,CAACH,SAAS;UACvB,OAAO,EAAEb,GAAG,CAAC,OAAO,CAAC;UACrB,SAAS,EAAE,SAAA4B,QAACC,KAAK;YAAA,OAAKR,IAAI,CAAC,aAAa,EAAEQ,KAAK,CAAC;UAAA;QAClD,CAAC,EAAE,CAACH,gBAAgB,CAAC,CAAC,EAAEH,cAAc,CAAC,CAAC,CAAC,CAAC;MAC5C;IACF,CAAC;IACD,IAAMO,UAAU,GAAG,SAAbA,UAAUA,CAAA,EAAS;MACvB,IAAIV,KAAK,CAACf,IAAI,EAAE;QACd,OAAOe,KAAK,CAACf,IAAI,CAAC,CAAC;MACrB;MACA,IAAIW,KAAK,CAACX,IAAI,EAAE;QACd,OAAOjB,YAAY,CAAC,KAAK,EAAE;UACzB,OAAO,EAAE,CAACY,GAAG,CAAC,MAAM,CAAC,EAAE,cAAc;QACvC,CAAC,EAAE,CAACgB,KAAK,CAACX,IAAI,CAAC,CAAC;MAClB;IACF,CAAC;IACD,IAAM0B,eAAe,GAAG,SAAlBA,eAAeA,CAAA,EAAS;MAC5B,IAAMC,QAAQ,GAAGhB,KAAK,CAACR,KAAK,CAACyB,QAAQ,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC;MAClD,OAAO9C,YAAY,CAAC,KAAK,EAAE,IAAI,EAAE,CAACA,YAAY,CAAC,MAAM,EAAE;QACrD,OAAO,EAAEY,GAAG,CAAC,gBAAgB;MAC/B,CAAC,EAAE,CAACgB,KAAK,CAACJ,QAAQ,CAAC,CAAC,EAAExB,YAAY,CAAC,MAAM,EAAE;QACzC,OAAO,EAAEY,GAAG,CAAC,eAAe;MAC9B,CAAC,EAAE,CAACgC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE9C,gBAAgB,CAAC,GAAG,CAAC,EAAEE,YAAY,CAAC,MAAM,EAAE;QAC7D,OAAO,EAAEY,GAAG,CAAC,eAAe;MAC9B,CAAC,EAAE,CAACgC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC;IACD,OAAO,YAAM;MACX,IAAIG,EAAE,EAAEC,EAAE,EAAEC,EAAE;MACd,IAAMC,OAAO,GAAGlB,KAAK,CAAChB,GAAG,IAAId,KAAK,CAAC0B,KAAK,CAACZ,GAAG,CAAC;MAC7C,IAAMmC,SAAS,GAAGnB,KAAK,CAACZ,KAAK,IAAIlB,KAAK,CAAC0B,KAAK,CAACR,KAAK,CAAC;MACnD,IAAMgC,eAAe,GAAGpB,KAAK,CAAC,cAAc,CAAC,IAAI9B,KAAK,CAAC0B,KAAK,CAACF,WAAW,CAAC;MACzE,IAAM2B,UAAU,GAAGH,OAAO,IAAIC,SAAS,IAAIC,eAAe,IAAIpB,KAAK,CAACsB,MAAM;MAC1E,IAAMC,KAAK,GAAGJ,SAAS,IAAInD,YAAY,CAAC,KAAK,EAAE;QAC7C,OAAO,EAAEY,GAAG,CAAC,OAAO;MACtB,CAAC,EAAE,CAACoB,KAAK,CAACZ,KAAK,GAAGY,KAAK,CAACZ,KAAK,CAAC,CAAC,GAAGuB,eAAe,CAAC,CAAC,CAAC,CAAC;MACrD,IAAMa,WAAW,GAAGJ,eAAe,IAAIpD,YAAY,CAAC,KAAK,EAAE;QACzD,OAAO,EAAEY,GAAG,CAAC,cAAc;MAC7B,CAAC,EAAE,CAACoB,KAAK,CAAC,cAAc,CAAC,GAAGA,KAAK,CAAC,cAAc,CAAC,CAAC,CAAC,MAAAyB,MAAA,CAAM7B,KAAK,CAACJ,QAAQ,OAAAiC,MAAA,CAAI7B,KAAK,CAACF,WAAW,CAAE,CAAC,CAAC;MAChG,IAAMgC,GAAG,GAAGR,OAAO,IAAIlD,YAAY,CAAC,KAAK,EAAE;QACzC,OAAO,EAAEY,GAAG,CAAC,KAAK;MACpB,CAAC,EAAE,CAACoB,KAAK,CAAChB,GAAG,GAAGgB,KAAK,CAAChB,GAAG,CAAC,CAAC,OAAAyC,MAAA,CAAO7B,KAAK,CAACZ,GAAG,CAAE,CAAC,CAAC;MAC/C,IAAM2C,MAAM,GAAG3B,KAAK,CAAC4B,MAAM,IAAI5D,YAAY,CAAC,KAAK,EAAE;QACjD,OAAO,EAAEY,GAAG,CAAC,QAAQ;MACvB,CAAC,EAAE,CAACoB,KAAK,CAAC4B,MAAM,CAAC,CAAC,CAAC,CAAC;MACpB,IAAMC,MAAM,GAAGR,UAAU,IAAIrD,YAAY,CAAC,KAAK,EAAE;QAC/C,OAAO,EAAEY,GAAG,CAAC,QAAQ;MACvB,CAAC,EAAE,CAAC,CAACmC,EAAE,GAAGf,KAAK,CAAC,WAAW,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGe,EAAE,CAACe,IAAI,CAAC9B,KAAK,CAAC,EAAEuB,KAAK,EAAEC,WAAW,EAAEE,GAAG,EAAE,CAACV,EAAE,GAAGhB,KAAK,CAACsB,MAAM,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGN,EAAE,CAACc,IAAI,CAAC9B,KAAK,CAAC,CAAC,CAAC;MACjJ,OAAOhC,YAAY,CAAC,KAAK,EAAE;QACzB,OAAO,EAAEY,GAAG,CAAC;MACf,CAAC,EAAE,CAACZ,YAAY,CAAC,KAAK,EAAE;QACtB,OAAO,EAAEY,GAAG,CAAC,QAAQ;MACvB,CAAC,EAAE,CAAC2B,WAAW,CAAC,CAAC,EAAEvC,YAAY,CAAC,KAAK,EAAE;QACrC,OAAO,EAAEY,GAAG,CAAC,SAAS,EAAE;UACtBS,QAAQ,EAAEO,KAAK,CAACP;QAClB,CAAC;MACH,CAAC,EAAE,CAACrB,YAAY,CAAC,KAAK,EAAE,IAAI,EAAE,CAACkC,WAAW,CAAC,CAAC,EAAEQ,UAAU,CAAC,CAAC,EAAE,CAACO,EAAE,GAAGjB,KAAK,CAAC+B,IAAI,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGd,EAAE,CAACa,IAAI,CAAC9B,KAAK,CAAC,CAAC,CAAC,EAAE6B,MAAM,CAAC,CAAC,CAAC,CAAC,EAAEF,MAAM,CAAC,CAAC;IACzI,CAAC;EACH;AACF,CAAC,CAAC;AACF,SACEhC,aAAa,IAAIS,OAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}