{"ast": null, "code": "export var HOOK_SETUP = 'devtools-plugin:setup';\nexport var HOOK_PLUGIN_SETTINGS_SET = 'plugin:settings:set';", "map": {"version": 3, "names": ["HOOK_SETUP", "HOOK_PLUGIN_SETTINGS_SET"], "sources": ["C:/Users/<USER>/Downloads/Telegram Desktop/vue3_3.0/vue3_3.0/node_modules/@vue/devtools-api/lib/esm/const.js"], "sourcesContent": ["export const HOOK_SETUP = 'devtools-plugin:setup';\nexport const HOOK_PLUGIN_SETTINGS_SET = 'plugin:settings:set';\n"], "mappings": "AAAA,OAAO,IAAMA,UAAU,GAAG,uBAAuB;AACjD,OAAO,IAAMC,wBAAwB,GAAG,qBAAqB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}