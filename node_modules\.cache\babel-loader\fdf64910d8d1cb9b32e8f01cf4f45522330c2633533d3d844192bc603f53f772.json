{"ast": null, "code": "import { ref, getCurrentInstance } from 'vue';\nimport store from '@/store/index';\nimport { vantLocales } from '@/i18n/i18n';\nimport { useI18n } from 'vue-i18n';\nexport default {\n  name: 'LanguageSwitcher',\n  props: {\n    color: String\n  },\n  setup: function setup() {\n    var _store$state$baseInfo;\n    // 语言切换\n    var _useI18n = useI18n(),\n      locale = _useI18n.locale;\n    var langcheck = ref(store.state.lang);\n    var langImg = ref('');\n    langImg.value = store.state.langImg;\n    var langs = ref((_store$state$baseInfo = store.state.baseInfo) === null || _store$state$baseInfo === void 0 ? void 0 : _store$state$baseInfo.languageList);\n    var show = ref(false);\n    var showLang = function showLang() {\n      langcheck.value = store.state.lang;\n      show.value = true;\n    };\n    var handSeletlanguages = function handSeletlanguages(row) {\n      langcheck.value = row.link;\n      langImg.value = row.image_url;\n      submitLang();\n    };\n    var submitLang = function submitLang() {\n      locale.value = langcheck.value;\n      store.dispatch('changelang', langcheck.value);\n      store.dispatch('changelangImg', langImg.value);\n      vantLocales(locale.value);\n      show.value = false;\n    };\n    submitLang();\n    return {\n      show: show,\n      submitLang: submitLang,\n      handSeletlanguages: handSeletlanguages,\n      langs: langs,\n      showLang: showLang,\n      langcheck: langcheck\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "getCurrentInstance", "store", "vantLocales", "useI18n", "name", "props", "color", "String", "setup", "_store$state$baseInfo", "_useI18n", "locale", "langcheck", "state", "lang", "langImg", "value", "langs", "baseInfo", "languageList", "show", "showLang", "handSeletlanguages", "row", "link", "image_url", "submitLang", "dispatch"], "sources": ["C:\\Users\\<USER>\\Downloads\\Telegram Desktop\\vue3_3.0\\vue3_3.0\\src\\components\\lang.vue"], "sourcesContent": ["<template>\r\n    <div class=\"footer\">\r\n        <img :src=\"require('@/assets/images/lang'+(color == 'white' ? 1 : '')+'.png')\" class=\"lang\" height=\"27\" width=\"27\" alt=\"\" @click=\"showLang()\">\r\n        <div v-if=\"show\">\r\n            <van-dialog v-model:show=\"show\" :showConfirmButton=\"false\" closeOnClickOverlay class=\"lang-dialog\">\r\n                <div class=\"lang_box\">\r\n                    <!-- <img :src=\"require('@/assets/images/register/lang_bg.png')\" class=\"lang_bg\" /> -->\r\n                    <div class=\"title\">{{$t('msg.check_lang')}}</div>\r\n                    <div class=\"content\">\r\n                        <!-- <img :src=\"require('@/assets/images/register/qiu.png')\" class=\"qiu\" /> -->\r\n                        <div class=\"langs\">\r\n                            <span class=\"li\" :class=\"langcheck==item.link && 'check'\" v-for=\"(item,index) in langs\" :key=\"index\"  @click=\"handSeletlanguages(item)\">\r\n                                <img :src=\"item.image_url\" class=\"img\" height=\"18\" width=\"27\" alt=\"\">\r\n                                <span class=\"text\">{{item.name}}</span>\r\n                            </span>\r\n                        </div>\r\n                        <div class=\"btn\">\r\n                        <!-- <van-button round block type=\"primary\" @click=\"submitLang\">\r\n                            {{$t('msg.nowQh')}}\r\n                        </van-button> -->\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </van-dialog>\r\n        </div>\r\n    </div>\r\n</template>\r\n<script>\r\nimport { ref, getCurrentInstance } from 'vue';\r\nimport store from '@/store/index'\r\nimport {vantLocales} from '@/i18n/i18n';\r\nimport { useI18n } from 'vue-i18n'\r\nexport default {\r\n    name: 'LanguageSwitcher',\r\n    props: {\r\n        color: String\r\n    },\r\n    setup(){\r\n        // 语言切换\r\n        const { locale } = useI18n()\r\n        const langcheck = ref(store.state.lang)\r\n        const langImg = ref('')\r\n        \r\n        langImg.value = store.state.langImg\r\n        const langs = ref(store.state.baseInfo?.languageList)\r\n\r\n        const show = ref(false);\r\n\r\n        const showLang = () => {\r\n            langcheck.value = store.state.lang\r\n            show.value = true\r\n        }\r\n        \r\n        const handSeletlanguages = (row) => {\r\n            langcheck.value = row.link\r\n            langImg.value = row.image_url\r\n            submitLang()\r\n        }\r\n        \r\n        const submitLang = () => {\r\n            locale.value = langcheck.value\r\n            store.dispatch('changelang', langcheck.value)\r\n            store.dispatch('changelangImg', langImg.value)\r\n            vantLocales(locale.value)\r\n            show.value = false\r\n        }\r\n      \r\n        submitLang()\r\n        return {show, submitLang, handSeletlanguages, langs, showLang, langcheck}\r\n    }\r\n}\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n    :deep(.lang-dialog) {\r\n        .van-dialog__content {\r\n            max-height: 80vh;\r\n            overflow: hidden;\r\n        }\r\n    }\r\n    .lang_box{\r\n        width: 100%;\r\n        position: relative;\r\n        padding-top: 60px;\r\n        .lang_title {\r\n            margin-bottom: 40px;\r\n        }\r\n        .lang_bg{\r\n        width: 100%;\r\n        position: absolute;\r\n        top: 0;\r\n        left: 0;\r\n        }\r\n        .content{\r\n        position: relative;\r\n        z-index: 1;\r\n        text-align: center;\r\n        .qiu{\r\n            width: 175px;\r\n            border-radius: 50%;\r\n            box-shadow: $shadow;\r\n            margin-bottom: 6px;\r\n        }\r\n        .langs{\r\n            margin-bottom: 15px;\r\n            max-height: 70vh;\r\n            overflow-y: auto;\r\n            -webkit-overflow-scrolling: touch;\r\n            border: 1px solid #ccc;\r\n            margin: 24px;\r\n            border-radius: 24px;\r\n            .li{\r\n                padding: 24px;\r\n                display: block;\r\n                text-align: left;\r\n                border-bottom: 1px solid #ccc;\r\n                &:last-child{\r\n                    border-bottom: none;\r\n                }\r\n                &.ctn{\r\n                    padding: 24px;\r\n                }\r\n                &.check{\r\n                    background-color: #ccc;\r\n                }\r\n                .img{\r\n                    margin-right: 34px;\r\n                    vertical-align: middle;\r\n                }\r\n                .text{\r\n                    font-size: 26px;\r\n                    color:$textColor;\r\n                }\r\n            }\r\n        }\r\n        .btn{\r\n            padding: 50px 54px 50px;\r\n        }\r\n        }\r\n    }\r\n</style>"], "mappings": "AA4BA,SAASA,GAAG,EAAEC,kBAAiB,QAAS,KAAK;AAC7C,OAAOC,KAAI,MAAO,eAAc;AAChC,SAAQC,WAAW,QAAO,aAAa;AACvC,SAASC,OAAM,QAAS,UAAS;AACjC,eAAe;EACXC,IAAI,EAAE,kBAAkB;EACxBC,KAAK,EAAE;IACHC,KAAK,EAAEC;EACX,CAAC;EACDC,KAAK,WAAAA,MAAA,EAAE;IAAA,IAAAC,qBAAA;IACH;IACA,IAAAC,QAAA,GAAmBP,OAAO,CAAC;MAAnBQ,MAAK,GAAAD,QAAA,CAALC,MAAK;IACb,IAAMC,SAAQ,GAAIb,GAAG,CAACE,KAAK,CAACY,KAAK,CAACC,IAAI;IACtC,IAAMC,OAAM,GAAIhB,GAAG,CAAC,EAAE;IAEtBgB,OAAO,CAACC,KAAI,GAAIf,KAAK,CAACY,KAAK,CAACE,OAAM;IAClC,IAAME,KAAI,GAAIlB,GAAG,EAAAU,qBAAA,GAACR,KAAK,CAACY,KAAK,CAACK,QAAQ,cAAAT,qBAAA,uBAApBA,qBAAA,CAAsBU,YAAY;IAEpD,IAAMC,IAAG,GAAIrB,GAAG,CAAC,KAAK,CAAC;IAEvB,IAAMsB,QAAO,GAAI,SAAXA,QAAOA,CAAA,EAAU;MACnBT,SAAS,CAACI,KAAI,GAAIf,KAAK,CAACY,KAAK,CAACC,IAAG;MACjCM,IAAI,CAACJ,KAAI,GAAI,IAAG;IACpB;IAEA,IAAMM,kBAAiB,GAAI,SAArBA,kBAAiBA,CAAKC,GAAG,EAAK;MAChCX,SAAS,CAACI,KAAI,GAAIO,GAAG,CAACC,IAAG;MACzBT,OAAO,CAACC,KAAI,GAAIO,GAAG,CAACE,SAAQ;MAC5BC,UAAU,CAAC;IACf;IAEA,IAAMA,UAAS,GAAI,SAAbA,UAASA,CAAA,EAAU;MACrBf,MAAM,CAACK,KAAI,GAAIJ,SAAS,CAACI,KAAI;MAC7Bf,KAAK,CAAC0B,QAAQ,CAAC,YAAY,EAAEf,SAAS,CAACI,KAAK;MAC5Cf,KAAK,CAAC0B,QAAQ,CAAC,eAAe,EAAEZ,OAAO,CAACC,KAAK;MAC7Cd,WAAW,CAACS,MAAM,CAACK,KAAK;MACxBI,IAAI,CAACJ,KAAI,GAAI,KAAI;IACrB;IAEAU,UAAU,CAAC;IACX,OAAO;MAACN,IAAI,EAAJA,IAAI;MAAEM,UAAU,EAAVA,UAAU;MAAEJ,kBAAkB,EAAlBA,kBAAkB;MAAEL,KAAK,EAALA,KAAK;MAAEI,QAAQ,EAARA,QAAQ;MAAET,SAAS,EAATA;IAAS;EAC5E;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}