{"ast": null, "code": "import { padZero } from \"../utils/index.mjs\";\nfunction getDate(timeStamp) {\n  var date = new Date(timeStamp * 1e3);\n  return \"\".concat(date.getFullYear(), \".\").concat(padZero(date.getMonth() + 1), \".\").concat(padZero(date.getDate()));\n}\nvar formatDiscount = function formatDiscount(discount) {\n  return (discount / 10).toFixed(discount % 10 === 0 ? 0 : 1);\n};\nvar formatAmount = function formatAmount(amount) {\n  return (amount / 100).toFixed(amount % 100 === 0 ? 0 : amount % 10 === 0 ? 1 : 2);\n};\nexport { formatAmount, formatDiscount, getDate };", "map": {"version": 3, "names": ["padZero", "getDate", "timeStamp", "date", "Date", "concat", "getFullYear", "getMonth", "formatDiscount", "discount", "toFixed", "formatAmount", "amount"], "sources": ["C:/Users/<USER>/Desktop/vue3_3.0/node_modules/vant/es/coupon/utils.mjs"], "sourcesContent": ["import { padZero } from \"../utils/index.mjs\";\nfunction getDate(timeStamp) {\n  const date = new Date(timeStamp * 1e3);\n  return `${date.getFullYear()}.${padZero(date.getMonth() + 1)}.${padZero(\n    date.getDate()\n  )}`;\n}\nconst formatDiscount = (discount) => (discount / 10).toFixed(discount % 10 === 0 ? 0 : 1);\nconst formatAmount = (amount) => (amount / 100).toFixed(amount % 100 === 0 ? 0 : amount % 10 === 0 ? 1 : 2);\nexport {\n  formatAmount,\n  formatDiscount,\n  getDate\n};\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,oBAAoB;AAC5C,SAASC,OAAOA,CAACC,SAAS,EAAE;EAC1B,IAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,SAAS,GAAG,GAAG,CAAC;EACtC,UAAAG,MAAA,CAAUF,IAAI,CAACG,WAAW,CAAC,CAAC,OAAAD,MAAA,CAAIL,OAAO,CAACG,IAAI,CAACI,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,OAAAF,MAAA,CAAIL,OAAO,CACrEG,IAAI,CAACF,OAAO,CAAC,CACf,CAAC;AACH;AACA,IAAMO,cAAc,GAAG,SAAjBA,cAAcA,CAAIC,QAAQ;EAAA,OAAK,CAACA,QAAQ,GAAG,EAAE,EAAEC,OAAO,CAACD,QAAQ,GAAG,EAAE,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAAA;AACzF,IAAME,YAAY,GAAG,SAAfA,YAAYA,CAAIC,MAAM;EAAA,OAAK,CAACA,MAAM,GAAG,GAAG,EAAEF,OAAO,CAACE,MAAM,GAAG,GAAG,KAAK,CAAC,GAAG,CAAC,GAAGA,MAAM,GAAG,EAAE,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAAA;AAC3G,SACED,YAAY,EACZH,cAAc,EACdP,OAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}