{"ast": null, "code": "function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nfunction _iterableToArrayLimit(arr, i) { var _i = null == arr ? null : \"undefined\" != typeof Symbol && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (null != _i) { var _s, _e, _x, _r, _arr = [], _n = !0, _d = !1; try { if (_x = (_i = _i.call(arr)).next, 0 === i) { if (Object(_i) !== _i) return; _n = !1; } else for (; !(_n = (_s = _x.call(_i)).done) && (_arr.push(_s.value), _arr.length !== i); _n = !0); } catch (err) { _d = !0, _e = err; } finally { try { if (!_n && null != _i.return && (_r = _i.return(), Object(_r) !== _r)) return; } finally { if (_d) throw _e; } } return _arr; } }\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\nimport { createVNode as _createVNode, mergeProps as _mergeProps } from \"vue\";\nimport { ref, watch, computed, reactive, nextTick, onMounted, defineComponent } from \"vue\";\nimport { deepClone } from \"../utils/deep-clone.mjs\";\nimport { pick, extend, makeArrayProp, makeNumericProp, createNamespace } from \"../utils/index.mjs\";\nimport { pickerSharedProps } from \"../picker/Picker.mjs\";\nimport { useExpose } from \"../composables/use-expose.mjs\";\nimport { Picker } from \"../picker/index.mjs\";\nvar _createNamespace = createNamespace(\"area\"),\n  _createNamespace2 = _slicedToArray(_createNamespace, 2),\n  name = _createNamespace2[0],\n  bem = _createNamespace2[1];\nvar EMPTY_CODE = \"000000\";\nvar INHERIT_SLOTS = [\"title\", \"cancel\", \"confirm\", \"toolbar\", \"columns-top\", \"columns-bottom\"];\nvar INHERIT_PROPS = [\"title\", \"loading\", \"readonly\", \"itemHeight\", \"swipeDuration\", \"visibleItemCount\", \"cancelButtonText\", \"confirmButtonText\"];\nvar isOverseaCode = function isOverseaCode(code) {\n  return code[0] === \"9\";\n};\nvar areaProps = extend({}, pickerSharedProps, {\n  value: String,\n  columnsNum: makeNumericProp(3),\n  columnsPlaceholder: makeArrayProp(),\n  areaList: {\n    type: Object,\n    default: function _default() {\n      return {};\n    }\n  },\n  isOverseaCode: {\n    type: Function,\n    default: isOverseaCode\n  }\n});\nvar stdin_default = defineComponent({\n  name: name,\n  props: areaProps,\n  emits: [\"change\", \"confirm\", \"cancel\"],\n  setup: function setup(props, _ref) {\n    var emit = _ref.emit,\n      slots = _ref.slots;\n    var pickerRef = ref();\n    var state = reactive({\n      code: props.value,\n      columns: [{\n        values: []\n      }, {\n        values: []\n      }, {\n        values: []\n      }]\n    });\n    var areaList = computed(function () {\n      var areaList2 = props.areaList;\n      return {\n        province: areaList2.province_list || {},\n        city: areaList2.city_list || {},\n        county: areaList2.county_list || {}\n      };\n    });\n    var placeholderMap = computed(function () {\n      var columnsPlaceholder = props.columnsPlaceholder;\n      return {\n        province: columnsPlaceholder[0] || \"\",\n        city: columnsPlaceholder[1] || \"\",\n        county: columnsPlaceholder[2] || \"\"\n      };\n    });\n    var getDefaultCode = function getDefaultCode() {\n      if (props.columnsPlaceholder.length) {\n        return EMPTY_CODE;\n      }\n      var _areaList$value = areaList.value,\n        county = _areaList$value.county,\n        city = _areaList$value.city;\n      var countyCodes = Object.keys(county);\n      if (countyCodes[0]) {\n        return countyCodes[0];\n      }\n      var cityCodes = Object.keys(city);\n      if (cityCodes[0]) {\n        return cityCodes[0];\n      }\n      return \"\";\n    };\n    var getColumnValues = function getColumnValues(type, code) {\n      var column = [];\n      if (type !== \"province\" && !code) {\n        return column;\n      }\n      var list = areaList.value[type];\n      column = Object.keys(list).map(function (listCode) {\n        return {\n          code: listCode,\n          name: list[listCode]\n        };\n      });\n      if (code) {\n        if (type === \"city\" && props.isOverseaCode(code)) {\n          code = \"9\";\n        }\n        column = column.filter(function (item) {\n          return item.code.indexOf(code) === 0;\n        });\n      }\n      if (placeholderMap.value[type] && column.length) {\n        var codeFill = \"\";\n        if (type === \"city\") {\n          codeFill = EMPTY_CODE.slice(2, 4);\n        } else if (type === \"county\") {\n          codeFill = EMPTY_CODE.slice(4, 6);\n        }\n        column.unshift({\n          code: code + codeFill,\n          name: placeholderMap.value[type]\n        });\n      }\n      return column;\n    };\n    var getIndex = function getIndex(type, code) {\n      var compareNum = code.length;\n      if (type === \"province\") {\n        compareNum = props.isOverseaCode(code) ? 1 : 2;\n      }\n      if (type === \"city\") {\n        compareNum = 4;\n      }\n      code = code.slice(0, compareNum);\n      var list = getColumnValues(type, compareNum > 2 ? code.slice(0, compareNum - 2) : \"\");\n      for (var i = 0; i < list.length; i++) {\n        if (list[i].code.slice(0, compareNum) === code) {\n          return i;\n        }\n      }\n      return 0;\n    };\n    var setValues = function setValues() {\n      var picker = pickerRef.value;\n      if (!picker) {\n        return;\n      }\n      var code = state.code || getDefaultCode();\n      var province = getColumnValues(\"province\");\n      var city = getColumnValues(\"city\", code.slice(0, 2));\n      picker.setColumnValues(0, province);\n      picker.setColumnValues(1, city);\n      if (city.length && code.slice(2, 4) === \"00\" && !props.isOverseaCode(code)) {\n        var _city = _slicedToArray(city, 1);\n        code = _city[0].code;\n      }\n      picker.setColumnValues(2, getColumnValues(\"county\", code.slice(0, 4)));\n      picker.setIndexes([getIndex(\"province\", code), getIndex(\"city\", code), getIndex(\"county\", code)]);\n    };\n    var parseValues = function parseValues(values) {\n      return values.map(function (value, index) {\n        if (value) {\n          value = deepClone(value);\n          if (!value.code || value.name === props.columnsPlaceholder[index]) {\n            value.code = \"\";\n            value.name = \"\";\n          }\n        }\n        return value;\n      });\n    };\n    var getValues = function getValues() {\n      if (pickerRef.value) {\n        var values = pickerRef.value.getValues().filter(Boolean);\n        return parseValues(values);\n      }\n      return [];\n    };\n    var getArea = function getArea() {\n      var values = getValues();\n      var area = {\n        code: \"\",\n        country: \"\",\n        province: \"\",\n        city: \"\",\n        county: \"\"\n      };\n      if (!values.length) {\n        return area;\n      }\n      var names = values.map(function (item) {\n        return item.name;\n      });\n      var validValues = values.filter(function (value) {\n        return value.code;\n      });\n      area.code = validValues.length ? validValues[validValues.length - 1].code : \"\";\n      if (props.isOverseaCode(area.code)) {\n        area.country = names[1] || \"\";\n        area.province = names[2] || \"\";\n      } else {\n        area.province = names[0] || \"\";\n        area.city = names[1] || \"\";\n        area.county = names[2] || \"\";\n      }\n      return area;\n    };\n    var reset = function reset() {\n      var newCode = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : \"\";\n      state.code = newCode;\n      setValues();\n    };\n    var onChange = function onChange(values, index) {\n      state.code = values[index].code;\n      setValues();\n      if (pickerRef.value) {\n        var parsedValues = parseValues(pickerRef.value.getValues());\n        emit(\"change\", parsedValues, index);\n      }\n    };\n    var onConfirm = function onConfirm(values, index) {\n      setValues();\n      emit(\"confirm\", parseValues(values), index);\n    };\n    var onCancel = function onCancel() {\n      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n      return emit.apply(void 0, [\"cancel\"].concat(args));\n    };\n    onMounted(setValues);\n    watch(function () {\n      return props.value;\n    }, function (value) {\n      state.code = value;\n      setValues();\n    });\n    watch(function () {\n      return props.areaList;\n    }, setValues, {\n      deep: true\n    });\n    watch(function () {\n      return props.columnsNum;\n    }, function () {\n      nextTick(setValues);\n    });\n    useExpose({\n      reset: reset,\n      getArea: getArea,\n      getValues: getValues\n    });\n    return function () {\n      var columns = state.columns.slice(0, +props.columnsNum);\n      return _createVNode(Picker, _mergeProps({\n        \"ref\": pickerRef,\n        \"class\": bem(),\n        \"columns\": columns,\n        \"columnsFieldNames\": {\n          text: \"name\"\n        },\n        \"onChange\": onChange,\n        \"onCancel\": onCancel,\n        \"onConfirm\": onConfirm\n      }, pick(props, INHERIT_PROPS)), pick(slots, INHERIT_SLOTS));\n    };\n  }\n});\nexport { stdin_default as default };", "map": {"version": 3, "names": ["createVNode", "_createVNode", "mergeProps", "_mergeProps", "ref", "watch", "computed", "reactive", "nextTick", "onMounted", "defineComponent", "deepClone", "pick", "extend", "makeArrayProp", "makeNumericProp", "createNamespace", "pickerSharedProps", "useExpose", "Picker", "_createNamespace", "_createNamespace2", "_slicedToArray", "name", "bem", "EMPTY_CODE", "INHERIT_SLOTS", "INHERIT_PROPS", "isOverseaCode", "code", "areaProps", "value", "String", "columnsNum", "columnsPlaceholder", "areaList", "type", "Object", "default", "_default", "Function", "stdin_default", "props", "emits", "setup", "_ref", "emit", "slots", "pickerRef", "state", "columns", "values", "areaList2", "province", "province_list", "city", "city_list", "county", "county_list", "placeholderM<PERSON>", "getDefaultCode", "length", "_areaList$value", "countyCodes", "keys", "cityCodes", "getColumnValues", "column", "list", "map", "listCode", "filter", "item", "indexOf", "codeFill", "slice", "unshift", "getIndex", "compareNum", "i", "set<PERSON><PERSON><PERSON>", "picker", "setColumnValues", "_city", "setIndexes", "parseV<PERSON>ues", "index", "getV<PERSON>ues", "Boolean", "getArea", "area", "country", "names", "validValues", "reset", "newCode", "arguments", "undefined", "onChange", "parsed<PERSON><PERSON>ues", "onConfirm", "onCancel", "_len", "args", "Array", "_key", "apply", "concat", "deep", "text"], "sources": ["C:/Users/<USER>/Downloads/Telegram Desktop/vue3_3.0/vue3_3.0/node_modules/vant/es/area/Area.mjs"], "sourcesContent": ["import { createVNode as _createVNode, mergeProps as _mergeProps } from \"vue\";\nimport { ref, watch, computed, reactive, nextTick, onMounted, defineComponent } from \"vue\";\nimport { deepClone } from \"../utils/deep-clone.mjs\";\nimport { pick, extend, makeArrayProp, makeNumericProp, createNamespace } from \"../utils/index.mjs\";\nimport { pickerSharedProps } from \"../picker/Picker.mjs\";\nimport { useExpose } from \"../composables/use-expose.mjs\";\nimport { Picker } from \"../picker/index.mjs\";\nconst [name, bem] = createNamespace(\"area\");\nconst EMPTY_CODE = \"000000\";\nconst INHERIT_SLOTS = [\"title\", \"cancel\", \"confirm\", \"toolbar\", \"columns-top\", \"columns-bottom\"];\nconst INHERIT_PROPS = [\"title\", \"loading\", \"readonly\", \"itemHeight\", \"swipeDuration\", \"visibleItemCount\", \"cancelButtonText\", \"confirmButtonText\"];\nconst isOverseaCode = (code) => code[0] === \"9\";\nconst areaProps = extend({}, pickerSharedProps, {\n  value: String,\n  columnsNum: makeNumericProp(3),\n  columnsPlaceholder: makeArrayProp(),\n  areaList: {\n    type: Object,\n    default: () => ({})\n  },\n  isOverseaCode: {\n    type: Function,\n    default: isOverseaCode\n  }\n});\nvar stdin_default = defineComponent({\n  name,\n  props: areaProps,\n  emits: [\"change\", \"confirm\", \"cancel\"],\n  setup(props, {\n    emit,\n    slots\n  }) {\n    const pickerRef = ref();\n    const state = reactive({\n      code: props.value,\n      columns: [{\n        values: []\n      }, {\n        values: []\n      }, {\n        values: []\n      }]\n    });\n    const areaList = computed(() => {\n      const {\n        areaList: areaList2\n      } = props;\n      return {\n        province: areaList2.province_list || {},\n        city: areaList2.city_list || {},\n        county: areaList2.county_list || {}\n      };\n    });\n    const placeholderMap = computed(() => {\n      const {\n        columnsPlaceholder\n      } = props;\n      return {\n        province: columnsPlaceholder[0] || \"\",\n        city: columnsPlaceholder[1] || \"\",\n        county: columnsPlaceholder[2] || \"\"\n      };\n    });\n    const getDefaultCode = () => {\n      if (props.columnsPlaceholder.length) {\n        return EMPTY_CODE;\n      }\n      const {\n        county,\n        city\n      } = areaList.value;\n      const countyCodes = Object.keys(county);\n      if (countyCodes[0]) {\n        return countyCodes[0];\n      }\n      const cityCodes = Object.keys(city);\n      if (cityCodes[0]) {\n        return cityCodes[0];\n      }\n      return \"\";\n    };\n    const getColumnValues = (type, code) => {\n      let column = [];\n      if (type !== \"province\" && !code) {\n        return column;\n      }\n      const list = areaList.value[type];\n      column = Object.keys(list).map((listCode) => ({\n        code: listCode,\n        name: list[listCode]\n      }));\n      if (code) {\n        if (type === \"city\" && props.isOverseaCode(code)) {\n          code = \"9\";\n        }\n        column = column.filter((item) => item.code.indexOf(code) === 0);\n      }\n      if (placeholderMap.value[type] && column.length) {\n        let codeFill = \"\";\n        if (type === \"city\") {\n          codeFill = EMPTY_CODE.slice(2, 4);\n        } else if (type === \"county\") {\n          codeFill = EMPTY_CODE.slice(4, 6);\n        }\n        column.unshift({\n          code: code + codeFill,\n          name: placeholderMap.value[type]\n        });\n      }\n      return column;\n    };\n    const getIndex = (type, code) => {\n      let compareNum = code.length;\n      if (type === \"province\") {\n        compareNum = props.isOverseaCode(code) ? 1 : 2;\n      }\n      if (type === \"city\") {\n        compareNum = 4;\n      }\n      code = code.slice(0, compareNum);\n      const list = getColumnValues(type, compareNum > 2 ? code.slice(0, compareNum - 2) : \"\");\n      for (let i = 0; i < list.length; i++) {\n        if (list[i].code.slice(0, compareNum) === code) {\n          return i;\n        }\n      }\n      return 0;\n    };\n    const setValues = () => {\n      const picker = pickerRef.value;\n      if (!picker) {\n        return;\n      }\n      let code = state.code || getDefaultCode();\n      const province = getColumnValues(\"province\");\n      const city = getColumnValues(\"city\", code.slice(0, 2));\n      picker.setColumnValues(0, province);\n      picker.setColumnValues(1, city);\n      if (city.length && code.slice(2, 4) === \"00\" && !props.isOverseaCode(code)) {\n        [{\n          code\n        }] = city;\n      }\n      picker.setColumnValues(2, getColumnValues(\"county\", code.slice(0, 4)));\n      picker.setIndexes([getIndex(\"province\", code), getIndex(\"city\", code), getIndex(\"county\", code)]);\n    };\n    const parseValues = (values) => values.map((value, index) => {\n      if (value) {\n        value = deepClone(value);\n        if (!value.code || value.name === props.columnsPlaceholder[index]) {\n          value.code = \"\";\n          value.name = \"\";\n        }\n      }\n      return value;\n    });\n    const getValues = () => {\n      if (pickerRef.value) {\n        const values = pickerRef.value.getValues().filter(Boolean);\n        return parseValues(values);\n      }\n      return [];\n    };\n    const getArea = () => {\n      const values = getValues();\n      const area = {\n        code: \"\",\n        country: \"\",\n        province: \"\",\n        city: \"\",\n        county: \"\"\n      };\n      if (!values.length) {\n        return area;\n      }\n      const names = values.map((item) => item.name);\n      const validValues = values.filter((value) => value.code);\n      area.code = validValues.length ? validValues[validValues.length - 1].code : \"\";\n      if (props.isOverseaCode(area.code)) {\n        area.country = names[1] || \"\";\n        area.province = names[2] || \"\";\n      } else {\n        area.province = names[0] || \"\";\n        area.city = names[1] || \"\";\n        area.county = names[2] || \"\";\n      }\n      return area;\n    };\n    const reset = (newCode = \"\") => {\n      state.code = newCode;\n      setValues();\n    };\n    const onChange = (values, index) => {\n      state.code = values[index].code;\n      setValues();\n      if (pickerRef.value) {\n        const parsedValues = parseValues(pickerRef.value.getValues());\n        emit(\"change\", parsedValues, index);\n      }\n    };\n    const onConfirm = (values, index) => {\n      setValues();\n      emit(\"confirm\", parseValues(values), index);\n    };\n    const onCancel = (...args) => emit(\"cancel\", ...args);\n    onMounted(setValues);\n    watch(() => props.value, (value) => {\n      state.code = value;\n      setValues();\n    });\n    watch(() => props.areaList, setValues, {\n      deep: true\n    });\n    watch(() => props.columnsNum, () => {\n      nextTick(setValues);\n    });\n    useExpose({\n      reset,\n      getArea,\n      getValues\n    });\n    return () => {\n      const columns = state.columns.slice(0, +props.columnsNum);\n      return _createVNode(Picker, _mergeProps({\n        \"ref\": pickerRef,\n        \"class\": bem(),\n        \"columns\": columns,\n        \"columnsFieldNames\": {\n          text: \"name\"\n        },\n        \"onChange\": onChange,\n        \"onCancel\": onCancel,\n        \"onConfirm\": onConfirm\n      }, pick(props, INHERIT_PROPS)), pick(slots, INHERIT_SLOTS));\n    };\n  }\n});\nexport {\n  stdin_default as default\n};\n"], "mappings": ";;;;;;AAAA,SAASA,WAAW,IAAIC,YAAY,EAAEC,UAAU,IAAIC,WAAW,QAAQ,KAAK;AAC5E,SAASC,GAAG,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,eAAe,QAAQ,KAAK;AAC1F,SAASC,SAAS,QAAQ,yBAAyB;AACnD,SAASC,IAAI,EAAEC,MAAM,EAAEC,aAAa,EAAEC,eAAe,EAAEC,eAAe,QAAQ,oBAAoB;AAClG,SAASC,iBAAiB,QAAQ,sBAAsB;AACxD,SAASC,SAAS,QAAQ,+BAA+B;AACzD,SAASC,MAAM,QAAQ,qBAAqB;AAC5C,IAAAC,gBAAA,GAAoBJ,eAAe,CAAC,MAAM,CAAC;EAAAK,iBAAA,GAAAC,cAAA,CAAAF,gBAAA;EAApCG,IAAI,GAAAF,iBAAA;EAAEG,GAAG,GAAAH,iBAAA;AAChB,IAAMI,UAAU,GAAG,QAAQ;AAC3B,IAAMC,aAAa,GAAG,CAAC,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,aAAa,EAAE,gBAAgB,CAAC;AAChG,IAAMC,aAAa,GAAG,CAAC,OAAO,EAAE,SAAS,EAAE,UAAU,EAAE,YAAY,EAAE,eAAe,EAAE,kBAAkB,EAAE,kBAAkB,EAAE,mBAAmB,CAAC;AAClJ,IAAMC,aAAa,GAAG,SAAhBA,aAAaA,CAAIC,IAAI;EAAA,OAAKA,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG;AAAA;AAC/C,IAAMC,SAAS,GAAGjB,MAAM,CAAC,CAAC,CAAC,EAAEI,iBAAiB,EAAE;EAC9Cc,KAAK,EAAEC,MAAM;EACbC,UAAU,EAAElB,eAAe,CAAC,CAAC,CAAC;EAC9BmB,kBAAkB,EAAEpB,aAAa,CAAC,CAAC;EACnCqB,QAAQ,EAAE;IACRC,IAAI,EAAEC,MAAM;IACZC,OAAO,EAAE,SAAAC,SAAA;MAAA,OAAO,CAAC,CAAC;IAAA;EACpB,CAAC;EACDX,aAAa,EAAE;IACbQ,IAAI,EAAEI,QAAQ;IACdF,OAAO,EAAEV;EACX;AACF,CAAC,CAAC;AACF,IAAIa,aAAa,GAAG/B,eAAe,CAAC;EAClCa,IAAI,EAAJA,IAAI;EACJmB,KAAK,EAAEZ,SAAS;EAChBa,KAAK,EAAE,CAAC,QAAQ,EAAE,SAAS,EAAE,QAAQ,CAAC;EACtCC,KAAK,WAAAA,MAACF,KAAK,EAAAG,IAAA,EAGR;IAAA,IAFDC,IAAI,GAAAD,IAAA,CAAJC,IAAI;MACJC,KAAK,GAAAF,IAAA,CAALE,KAAK;IAEL,IAAMC,SAAS,GAAG5C,GAAG,CAAC,CAAC;IACvB,IAAM6C,KAAK,GAAG1C,QAAQ,CAAC;MACrBsB,IAAI,EAAEa,KAAK,CAACX,KAAK;MACjBmB,OAAO,EAAE,CAAC;QACRC,MAAM,EAAE;MACV,CAAC,EAAE;QACDA,MAAM,EAAE;MACV,CAAC,EAAE;QACDA,MAAM,EAAE;MACV,CAAC;IACH,CAAC,CAAC;IACF,IAAMhB,QAAQ,GAAG7B,QAAQ,CAAC,YAAM;MAC9B,IACY8C,SAAS,GACjBV,KAAK,CADPP,QAAQ;MAEV,OAAO;QACLkB,QAAQ,EAAED,SAAS,CAACE,aAAa,IAAI,CAAC,CAAC;QACvCC,IAAI,EAAEH,SAAS,CAACI,SAAS,IAAI,CAAC,CAAC;QAC/BC,MAAM,EAAEL,SAAS,CAACM,WAAW,IAAI,CAAC;MACpC,CAAC;IACH,CAAC,CAAC;IACF,IAAMC,cAAc,GAAGrD,QAAQ,CAAC,YAAM;MACpC,IACE4B,kBAAkB,GAChBQ,KAAK,CADPR,kBAAkB;MAEpB,OAAO;QACLmB,QAAQ,EAAEnB,kBAAkB,CAAC,CAAC,CAAC,IAAI,EAAE;QACrCqB,IAAI,EAAErB,kBAAkB,CAAC,CAAC,CAAC,IAAI,EAAE;QACjCuB,MAAM,EAAEvB,kBAAkB,CAAC,CAAC,CAAC,IAAI;MACnC,CAAC;IACH,CAAC,CAAC;IACF,IAAM0B,cAAc,GAAG,SAAjBA,cAAcA,CAAA,EAAS;MAC3B,IAAIlB,KAAK,CAACR,kBAAkB,CAAC2B,MAAM,EAAE;QACnC,OAAOpC,UAAU;MACnB;MACA,IAAAqC,eAAA,GAGI3B,QAAQ,CAACJ,KAAK;QAFhB0B,MAAM,GAAAK,eAAA,CAANL,MAAM;QACNF,IAAI,GAAAO,eAAA,CAAJP,IAAI;MAEN,IAAMQ,WAAW,GAAG1B,MAAM,CAAC2B,IAAI,CAACP,MAAM,CAAC;MACvC,IAAIM,WAAW,CAAC,CAAC,CAAC,EAAE;QAClB,OAAOA,WAAW,CAAC,CAAC,CAAC;MACvB;MACA,IAAME,SAAS,GAAG5B,MAAM,CAAC2B,IAAI,CAACT,IAAI,CAAC;MACnC,IAAIU,SAAS,CAAC,CAAC,CAAC,EAAE;QAChB,OAAOA,SAAS,CAAC,CAAC,CAAC;MACrB;MACA,OAAO,EAAE;IACX,CAAC;IACD,IAAMC,eAAe,GAAG,SAAlBA,eAAeA,CAAI9B,IAAI,EAAEP,IAAI,EAAK;MACtC,IAAIsC,MAAM,GAAG,EAAE;MACf,IAAI/B,IAAI,KAAK,UAAU,IAAI,CAACP,IAAI,EAAE;QAChC,OAAOsC,MAAM;MACf;MACA,IAAMC,IAAI,GAAGjC,QAAQ,CAACJ,KAAK,CAACK,IAAI,CAAC;MACjC+B,MAAM,GAAG9B,MAAM,CAAC2B,IAAI,CAACI,IAAI,CAAC,CAACC,GAAG,CAAC,UAACC,QAAQ;QAAA,OAAM;UAC5CzC,IAAI,EAAEyC,QAAQ;UACd/C,IAAI,EAAE6C,IAAI,CAACE,QAAQ;QACrB,CAAC;MAAA,CAAC,CAAC;MACH,IAAIzC,IAAI,EAAE;QACR,IAAIO,IAAI,KAAK,MAAM,IAAIM,KAAK,CAACd,aAAa,CAACC,IAAI,CAAC,EAAE;UAChDA,IAAI,GAAG,GAAG;QACZ;QACAsC,MAAM,GAAGA,MAAM,CAACI,MAAM,CAAC,UAACC,IAAI;UAAA,OAAKA,IAAI,CAAC3C,IAAI,CAAC4C,OAAO,CAAC5C,IAAI,CAAC,KAAK,CAAC;QAAA,EAAC;MACjE;MACA,IAAI8B,cAAc,CAAC5B,KAAK,CAACK,IAAI,CAAC,IAAI+B,MAAM,CAACN,MAAM,EAAE;QAC/C,IAAIa,QAAQ,GAAG,EAAE;QACjB,IAAItC,IAAI,KAAK,MAAM,EAAE;UACnBsC,QAAQ,GAAGjD,UAAU,CAACkD,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;QACnC,CAAC,MAAM,IAAIvC,IAAI,KAAK,QAAQ,EAAE;UAC5BsC,QAAQ,GAAGjD,UAAU,CAACkD,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;QACnC;QACAR,MAAM,CAACS,OAAO,CAAC;UACb/C,IAAI,EAAEA,IAAI,GAAG6C,QAAQ;UACrBnD,IAAI,EAAEoC,cAAc,CAAC5B,KAAK,CAACK,IAAI;QACjC,CAAC,CAAC;MACJ;MACA,OAAO+B,MAAM;IACf,CAAC;IACD,IAAMU,QAAQ,GAAG,SAAXA,QAAQA,CAAIzC,IAAI,EAAEP,IAAI,EAAK;MAC/B,IAAIiD,UAAU,GAAGjD,IAAI,CAACgC,MAAM;MAC5B,IAAIzB,IAAI,KAAK,UAAU,EAAE;QACvB0C,UAAU,GAAGpC,KAAK,CAACd,aAAa,CAACC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;MAChD;MACA,IAAIO,IAAI,KAAK,MAAM,EAAE;QACnB0C,UAAU,GAAG,CAAC;MAChB;MACAjD,IAAI,GAAGA,IAAI,CAAC8C,KAAK,CAAC,CAAC,EAAEG,UAAU,CAAC;MAChC,IAAMV,IAAI,GAAGF,eAAe,CAAC9B,IAAI,EAAE0C,UAAU,GAAG,CAAC,GAAGjD,IAAI,CAAC8C,KAAK,CAAC,CAAC,EAAEG,UAAU,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;MACvF,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGX,IAAI,CAACP,MAAM,EAAEkB,CAAC,EAAE,EAAE;QACpC,IAAIX,IAAI,CAACW,CAAC,CAAC,CAAClD,IAAI,CAAC8C,KAAK,CAAC,CAAC,EAAEG,UAAU,CAAC,KAAKjD,IAAI,EAAE;UAC9C,OAAOkD,CAAC;QACV;MACF;MACA,OAAO,CAAC;IACV,CAAC;IACD,IAAMC,SAAS,GAAG,SAAZA,SAASA,CAAA,EAAS;MACtB,IAAMC,MAAM,GAAGjC,SAAS,CAACjB,KAAK;MAC9B,IAAI,CAACkD,MAAM,EAAE;QACX;MACF;MACA,IAAIpD,IAAI,GAAGoB,KAAK,CAACpB,IAAI,IAAI+B,cAAc,CAAC,CAAC;MACzC,IAAMP,QAAQ,GAAGa,eAAe,CAAC,UAAU,CAAC;MAC5C,IAAMX,IAAI,GAAGW,eAAe,CAAC,MAAM,EAAErC,IAAI,CAAC8C,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACtDM,MAAM,CAACC,eAAe,CAAC,CAAC,EAAE7B,QAAQ,CAAC;MACnC4B,MAAM,CAACC,eAAe,CAAC,CAAC,EAAE3B,IAAI,CAAC;MAC/B,IAAIA,IAAI,CAACM,MAAM,IAAIhC,IAAI,CAAC8C,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,IAAI,IAAI,CAACjC,KAAK,CAACd,aAAa,CAACC,IAAI,CAAC,EAAE;QAAA,IAAAsD,KAAA,GAAA7D,cAAA,CAGrEiC,IAAI;QADP1B,IAAI,GAAAsD,KAAA,IAAJtD,IAAI;MAER;MACAoD,MAAM,CAACC,eAAe,CAAC,CAAC,EAAEhB,eAAe,CAAC,QAAQ,EAAErC,IAAI,CAAC8C,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACtEM,MAAM,CAACG,UAAU,CAAC,CAACP,QAAQ,CAAC,UAAU,EAAEhD,IAAI,CAAC,EAAEgD,QAAQ,CAAC,MAAM,EAAEhD,IAAI,CAAC,EAAEgD,QAAQ,CAAC,QAAQ,EAAEhD,IAAI,CAAC,CAAC,CAAC;IACnG,CAAC;IACD,IAAMwD,WAAW,GAAG,SAAdA,WAAWA,CAAIlC,MAAM;MAAA,OAAKA,MAAM,CAACkB,GAAG,CAAC,UAACtC,KAAK,EAAEuD,KAAK,EAAK;QAC3D,IAAIvD,KAAK,EAAE;UACTA,KAAK,GAAGpB,SAAS,CAACoB,KAAK,CAAC;UACxB,IAAI,CAACA,KAAK,CAACF,IAAI,IAAIE,KAAK,CAACR,IAAI,KAAKmB,KAAK,CAACR,kBAAkB,CAACoD,KAAK,CAAC,EAAE;YACjEvD,KAAK,CAACF,IAAI,GAAG,EAAE;YACfE,KAAK,CAACR,IAAI,GAAG,EAAE;UACjB;QACF;QACA,OAAOQ,KAAK;MACd,CAAC,CAAC;IAAA;IACF,IAAMwD,SAAS,GAAG,SAAZA,SAASA,CAAA,EAAS;MACtB,IAAIvC,SAAS,CAACjB,KAAK,EAAE;QACnB,IAAMoB,MAAM,GAAGH,SAAS,CAACjB,KAAK,CAACwD,SAAS,CAAC,CAAC,CAAChB,MAAM,CAACiB,OAAO,CAAC;QAC1D,OAAOH,WAAW,CAAClC,MAAM,CAAC;MAC5B;MACA,OAAO,EAAE;IACX,CAAC;IACD,IAAMsC,OAAO,GAAG,SAAVA,OAAOA,CAAA,EAAS;MACpB,IAAMtC,MAAM,GAAGoC,SAAS,CAAC,CAAC;MAC1B,IAAMG,IAAI,GAAG;QACX7D,IAAI,EAAE,EAAE;QACR8D,OAAO,EAAE,EAAE;QACXtC,QAAQ,EAAE,EAAE;QACZE,IAAI,EAAE,EAAE;QACRE,MAAM,EAAE;MACV,CAAC;MACD,IAAI,CAACN,MAAM,CAACU,MAAM,EAAE;QAClB,OAAO6B,IAAI;MACb;MACA,IAAME,KAAK,GAAGzC,MAAM,CAACkB,GAAG,CAAC,UAACG,IAAI;QAAA,OAAKA,IAAI,CAACjD,IAAI;MAAA,EAAC;MAC7C,IAAMsE,WAAW,GAAG1C,MAAM,CAACoB,MAAM,CAAC,UAACxC,KAAK;QAAA,OAAKA,KAAK,CAACF,IAAI;MAAA,EAAC;MACxD6D,IAAI,CAAC7D,IAAI,GAAGgE,WAAW,CAAChC,MAAM,GAAGgC,WAAW,CAACA,WAAW,CAAChC,MAAM,GAAG,CAAC,CAAC,CAAChC,IAAI,GAAG,EAAE;MAC9E,IAAIa,KAAK,CAACd,aAAa,CAAC8D,IAAI,CAAC7D,IAAI,CAAC,EAAE;QAClC6D,IAAI,CAACC,OAAO,GAAGC,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE;QAC7BF,IAAI,CAACrC,QAAQ,GAAGuC,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE;MAChC,CAAC,MAAM;QACLF,IAAI,CAACrC,QAAQ,GAAGuC,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE;QAC9BF,IAAI,CAACnC,IAAI,GAAGqC,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE;QAC1BF,IAAI,CAACjC,MAAM,GAAGmC,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE;MAC9B;MACA,OAAOF,IAAI;IACb,CAAC;IACD,IAAMI,KAAK,GAAG,SAARA,KAAKA,CAAA,EAAqB;MAAA,IAAjBC,OAAO,GAAAC,SAAA,CAAAnC,MAAA,QAAAmC,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,EAAE;MACzB/C,KAAK,CAACpB,IAAI,GAAGkE,OAAO;MACpBf,SAAS,CAAC,CAAC;IACb,CAAC;IACD,IAAMkB,QAAQ,GAAG,SAAXA,QAAQA,CAAI/C,MAAM,EAAEmC,KAAK,EAAK;MAClCrC,KAAK,CAACpB,IAAI,GAAGsB,MAAM,CAACmC,KAAK,CAAC,CAACzD,IAAI;MAC/BmD,SAAS,CAAC,CAAC;MACX,IAAIhC,SAAS,CAACjB,KAAK,EAAE;QACnB,IAAMoE,YAAY,GAAGd,WAAW,CAACrC,SAAS,CAACjB,KAAK,CAACwD,SAAS,CAAC,CAAC,CAAC;QAC7DzC,IAAI,CAAC,QAAQ,EAAEqD,YAAY,EAAEb,KAAK,CAAC;MACrC;IACF,CAAC;IACD,IAAMc,SAAS,GAAG,SAAZA,SAASA,CAAIjD,MAAM,EAAEmC,KAAK,EAAK;MACnCN,SAAS,CAAC,CAAC;MACXlC,IAAI,CAAC,SAAS,EAAEuC,WAAW,CAAClC,MAAM,CAAC,EAAEmC,KAAK,CAAC;IAC7C,CAAC;IACD,IAAMe,QAAQ,GAAG,SAAXA,QAAQA,CAAA;MAAA,SAAAC,IAAA,GAAAN,SAAA,CAAAnC,MAAA,EAAO0C,IAAI,OAAAC,KAAA,CAAAF,IAAA,GAAAG,IAAA,MAAAA,IAAA,GAAAH,IAAA,EAAAG,IAAA;QAAJF,IAAI,CAAAE,IAAA,IAAAT,SAAA,CAAAS,IAAA;MAAA;MAAA,OAAK3D,IAAI,CAAA4D,KAAA,UAAC,QAAQ,EAAAC,MAAA,CAAKJ,IAAI,EAAC;IAAA;IACrD9F,SAAS,CAACuE,SAAS,CAAC;IACpB3E,KAAK,CAAC;MAAA,OAAMqC,KAAK,CAACX,KAAK;IAAA,GAAE,UAACA,KAAK,EAAK;MAClCkB,KAAK,CAACpB,IAAI,GAAGE,KAAK;MAClBiD,SAAS,CAAC,CAAC;IACb,CAAC,CAAC;IACF3E,KAAK,CAAC;MAAA,OAAMqC,KAAK,CAACP,QAAQ;IAAA,GAAE6C,SAAS,EAAE;MACrC4B,IAAI,EAAE;IACR,CAAC,CAAC;IACFvG,KAAK,CAAC;MAAA,OAAMqC,KAAK,CAACT,UAAU;IAAA,GAAE,YAAM;MAClCzB,QAAQ,CAACwE,SAAS,CAAC;IACrB,CAAC,CAAC;IACF9D,SAAS,CAAC;MACR4E,KAAK,EAALA,KAAK;MACLL,OAAO,EAAPA,OAAO;MACPF,SAAS,EAATA;IACF,CAAC,CAAC;IACF,OAAO,YAAM;MACX,IAAMrC,OAAO,GAAGD,KAAK,CAACC,OAAO,CAACyB,KAAK,CAAC,CAAC,EAAE,CAACjC,KAAK,CAACT,UAAU,CAAC;MACzD,OAAOhC,YAAY,CAACkB,MAAM,EAAEhB,WAAW,CAAC;QACtC,KAAK,EAAE6C,SAAS;QAChB,OAAO,EAAExB,GAAG,CAAC,CAAC;QACd,SAAS,EAAE0B,OAAO;QAClB,mBAAmB,EAAE;UACnB2D,IAAI,EAAE;QACR,CAAC;QACD,UAAU,EAAEX,QAAQ;QACpB,UAAU,EAAEG,QAAQ;QACpB,WAAW,EAAED;MACf,CAAC,EAAExF,IAAI,CAAC8B,KAAK,EAAEf,aAAa,CAAC,CAAC,EAAEf,IAAI,CAACmC,KAAK,EAAErB,aAAa,CAAC,CAAC;IAC7D,CAAC;EACH;AACF,CAAC,CAAC;AACF,SACEe,aAAa,IAAIH,OAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}