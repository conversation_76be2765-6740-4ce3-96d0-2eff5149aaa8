{"ast": null, "code": "function _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return _typeof(key) === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (_typeof(input) !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (_typeof(res) !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\nfunction _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nfunction _iterableToArrayLimit(arr, i) { var _i = null == arr ? null : \"undefined\" != typeof Symbol && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (null != _i) { var _s, _e, _x, _r, _arr = [], _n = !0, _d = !1; try { if (_x = (_i = _i.call(arr)).next, 0 === i) { if (Object(_i) !== _i) return; _n = !1; } else for (; !(_n = (_s = _x.call(_i)).done) && (_arr.push(_s.value), _arr.length !== i); _n = !0); } catch (err) { _d = !0, _e = err; } finally { try { if (!_n && null != _i.return && (_r = _i.return(), Object(_r) !== _r)) return; } finally { if (_d) throw _e; } } return _arr; } }\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\nimport { createVNode as _createVNode } from \"vue\";\nimport { computed, defineComponent } from \"vue\";\nimport { truthProp, makeStringProp, makeNumericProp, createNamespace } from \"../utils/index.mjs\";\nimport { useChildren } from \"@vant/use\";\nvar _createNamespace = createNamespace(\"row\"),\n  _createNamespace2 = _slicedToArray(_createNamespace, 2),\n  name = _createNamespace2[0],\n  bem = _createNamespace2[1];\nvar ROW_KEY = Symbol(name);\nvar rowProps = {\n  tag: makeStringProp(\"div\"),\n  wrap: truthProp,\n  align: String,\n  gutter: makeNumericProp(0),\n  justify: String\n};\nvar stdin_default = defineComponent({\n  name: name,\n  props: rowProps,\n  setup: function setup(props, _ref) {\n    var slots = _ref.slots;\n    var _useChildren = useChildren(ROW_KEY),\n      children = _useChildren.children,\n      linkChildren = _useChildren.linkChildren;\n    var groups = computed(function () {\n      var groups2 = [[]];\n      var totalSpan = 0;\n      children.forEach(function (child, index) {\n        totalSpan += Number(child.span);\n        if (totalSpan > 24) {\n          groups2.push([index]);\n          totalSpan -= 24;\n        } else {\n          groups2[groups2.length - 1].push(index);\n        }\n      });\n      return groups2;\n    });\n    var spaces = computed(function () {\n      var gutter = Number(props.gutter);\n      var spaces2 = [];\n      if (!gutter) {\n        return spaces2;\n      }\n      groups.value.forEach(function (group) {\n        var averagePadding = gutter * (group.length - 1) / group.length;\n        group.forEach(function (item, index) {\n          if (index === 0) {\n            spaces2.push({\n              right: averagePadding\n            });\n          } else {\n            var left = gutter - spaces2[item - 1].right;\n            var right = averagePadding - left;\n            spaces2.push({\n              left: left,\n              right: right\n            });\n          }\n        });\n      });\n      return spaces2;\n    });\n    linkChildren({\n      spaces: spaces\n    });\n    return function () {\n      var _bem;\n      var tag = props.tag,\n        wrap = props.wrap,\n        align = props.align,\n        justify = props.justify;\n      return _createVNode(tag, {\n        \"class\": bem((_bem = {}, _defineProperty(_bem, \"align-\".concat(align), align), _defineProperty(_bem, \"justify-\".concat(justify), justify), _defineProperty(_bem, \"nowrap\", !wrap), _bem))\n      }, {\n        default: function _default() {\n          var _a;\n          return [(_a = slots.default) == null ? void 0 : _a.call(slots)];\n        }\n      });\n    };\n  }\n});\nexport { ROW_KEY, stdin_default as default };", "map": {"version": 3, "names": ["createVNode", "_createVNode", "computed", "defineComponent", "truthProp", "makeStringProp", "makeNumericProp", "createNamespace", "useChildren", "_createNamespace", "_createNamespace2", "_slicedToArray", "name", "bem", "ROW_KEY", "Symbol", "rowProps", "tag", "wrap", "align", "String", "gutter", "justify", "stdin_default", "props", "setup", "_ref", "slots", "_useChildren", "children", "linkChildren", "groups", "groups2", "totalSpan", "for<PERSON>ach", "child", "index", "Number", "span", "push", "length", "spaces", "spaces2", "value", "group", "averagePadding", "item", "right", "left", "_bem", "_defineProperty", "concat", "default", "_default", "_a", "call"], "sources": ["C:/Users/<USER>/Desktop/vue3_3.0/node_modules/vant/es/row/Row.mjs"], "sourcesContent": ["import { createVNode as _createVNode } from \"vue\";\nimport { computed, defineComponent } from \"vue\";\nimport { truthProp, makeStringProp, makeNumericProp, createNamespace } from \"../utils/index.mjs\";\nimport { useChildren } from \"@vant/use\";\nconst [name, bem] = createNamespace(\"row\");\nconst ROW_KEY = Symbol(name);\nconst rowProps = {\n  tag: makeStringProp(\"div\"),\n  wrap: truthProp,\n  align: String,\n  gutter: makeNumericProp(0),\n  justify: String\n};\nvar stdin_default = defineComponent({\n  name,\n  props: rowProps,\n  setup(props, {\n    slots\n  }) {\n    const {\n      children,\n      linkChildren\n    } = useChildren(ROW_KEY);\n    const groups = computed(() => {\n      const groups2 = [[]];\n      let totalSpan = 0;\n      children.forEach((child, index) => {\n        totalSpan += Number(child.span);\n        if (totalSpan > 24) {\n          groups2.push([index]);\n          totalSpan -= 24;\n        } else {\n          groups2[groups2.length - 1].push(index);\n        }\n      });\n      return groups2;\n    });\n    const spaces = computed(() => {\n      const gutter = Number(props.gutter);\n      const spaces2 = [];\n      if (!gutter) {\n        return spaces2;\n      }\n      groups.value.forEach((group) => {\n        const averagePadding = gutter * (group.length - 1) / group.length;\n        group.forEach((item, index) => {\n          if (index === 0) {\n            spaces2.push({\n              right: averagePadding\n            });\n          } else {\n            const left = gutter - spaces2[item - 1].right;\n            const right = averagePadding - left;\n            spaces2.push({\n              left,\n              right\n            });\n          }\n        });\n      });\n      return spaces2;\n    });\n    linkChildren({\n      spaces\n    });\n    return () => {\n      const {\n        tag,\n        wrap,\n        align,\n        justify\n      } = props;\n      return _createVNode(tag, {\n        \"class\": bem({\n          [`align-${align}`]: align,\n          [`justify-${justify}`]: justify,\n          nowrap: !wrap\n        })\n      }, {\n        default: () => {\n          var _a;\n          return [(_a = slots.default) == null ? void 0 : _a.call(slots)];\n        }\n      });\n    };\n  }\n});\nexport {\n  ROW_KEY,\n  stdin_default as default\n};\n"], "mappings": ";;;;;;;;;;AAAA,SAASA,WAAW,IAAIC,YAAY,QAAQ,KAAK;AACjD,SAASC,QAAQ,EAAEC,eAAe,QAAQ,KAAK;AAC/C,SAASC,SAAS,EAAEC,cAAc,EAAEC,eAAe,EAAEC,eAAe,QAAQ,oBAAoB;AAChG,SAASC,WAAW,QAAQ,WAAW;AACvC,IAAAC,gBAAA,GAAoBF,eAAe,CAAC,KAAK,CAAC;EAAAG,iBAAA,GAAAC,cAAA,CAAAF,gBAAA;EAAnCG,IAAI,GAAAF,iBAAA;EAAEG,GAAG,GAAAH,iBAAA;AAChB,IAAMI,OAAO,GAAGC,MAAM,CAACH,IAAI,CAAC;AAC5B,IAAMI,QAAQ,GAAG;EACfC,GAAG,EAAEZ,cAAc,CAAC,KAAK,CAAC;EAC1Ba,IAAI,EAAEd,SAAS;EACfe,KAAK,EAAEC,MAAM;EACbC,MAAM,EAAEf,eAAe,CAAC,CAAC,CAAC;EAC1BgB,OAAO,EAAEF;AACX,CAAC;AACD,IAAIG,aAAa,GAAGpB,eAAe,CAAC;EAClCS,IAAI,EAAJA,IAAI;EACJY,KAAK,EAAER,QAAQ;EACfS,KAAK,WAAAA,MAACD,KAAK,EAAAE,IAAA,EAER;IAAA,IADDC,KAAK,GAAAD,IAAA,CAALC,KAAK;IAEL,IAAAC,YAAA,GAGIpB,WAAW,CAACM,OAAO,CAAC;MAFtBe,QAAQ,GAAAD,YAAA,CAARC,QAAQ;MACRC,YAAY,GAAAF,YAAA,CAAZE,YAAY;IAEd,IAAMC,MAAM,GAAG7B,QAAQ,CAAC,YAAM;MAC5B,IAAM8B,OAAO,GAAG,CAAC,EAAE,CAAC;MACpB,IAAIC,SAAS,GAAG,CAAC;MACjBJ,QAAQ,CAACK,OAAO,CAAC,UAACC,KAAK,EAAEC,KAAK,EAAK;QACjCH,SAAS,IAAII,MAAM,CAACF,KAAK,CAACG,IAAI,CAAC;QAC/B,IAAIL,SAAS,GAAG,EAAE,EAAE;UAClBD,OAAO,CAACO,IAAI,CAAC,CAACH,KAAK,CAAC,CAAC;UACrBH,SAAS,IAAI,EAAE;QACjB,CAAC,MAAM;UACLD,OAAO,CAACA,OAAO,CAACQ,MAAM,GAAG,CAAC,CAAC,CAACD,IAAI,CAACH,KAAK,CAAC;QACzC;MACF,CAAC,CAAC;MACF,OAAOJ,OAAO;IAChB,CAAC,CAAC;IACF,IAAMS,MAAM,GAAGvC,QAAQ,CAAC,YAAM;MAC5B,IAAMmB,MAAM,GAAGgB,MAAM,CAACb,KAAK,CAACH,MAAM,CAAC;MACnC,IAAMqB,OAAO,GAAG,EAAE;MAClB,IAAI,CAACrB,MAAM,EAAE;QACX,OAAOqB,OAAO;MAChB;MACAX,MAAM,CAACY,KAAK,CAACT,OAAO,CAAC,UAACU,KAAK,EAAK;QAC9B,IAAMC,cAAc,GAAGxB,MAAM,IAAIuB,KAAK,CAACJ,MAAM,GAAG,CAAC,CAAC,GAAGI,KAAK,CAACJ,MAAM;QACjEI,KAAK,CAACV,OAAO,CAAC,UAACY,IAAI,EAAEV,KAAK,EAAK;UAC7B,IAAIA,KAAK,KAAK,CAAC,EAAE;YACfM,OAAO,CAACH,IAAI,CAAC;cACXQ,KAAK,EAAEF;YACT,CAAC,CAAC;UACJ,CAAC,MAAM;YACL,IAAMG,IAAI,GAAG3B,MAAM,GAAGqB,OAAO,CAACI,IAAI,GAAG,CAAC,CAAC,CAACC,KAAK;YAC7C,IAAMA,KAAK,GAAGF,cAAc,GAAGG,IAAI;YACnCN,OAAO,CAACH,IAAI,CAAC;cACXS,IAAI,EAAJA,IAAI;cACJD,KAAK,EAALA;YACF,CAAC,CAAC;UACJ;QACF,CAAC,CAAC;MACJ,CAAC,CAAC;MACF,OAAOL,OAAO;IAChB,CAAC,CAAC;IACFZ,YAAY,CAAC;MACXW,MAAM,EAANA;IACF,CAAC,CAAC;IACF,OAAO,YAAM;MAAA,IAAAQ,IAAA;MACX,IACEhC,GAAG,GAIDO,KAAK,CAJPP,GAAG;QACHC,IAAI,GAGFM,KAAK,CAHPN,IAAI;QACJC,KAAK,GAEHK,KAAK,CAFPL,KAAK;QACLG,OAAO,GACLE,KAAK,CADPF,OAAO;MAET,OAAOrB,YAAY,CAACgB,GAAG,EAAE;QACvB,OAAO,EAAEJ,GAAG,EAAAoC,IAAA,OAAAC,eAAA,CAAAD,IAAA,WAAAE,MAAA,CACAhC,KAAK,GAAKA,KAAK,GAAA+B,eAAA,CAAAD,IAAA,aAAAE,MAAA,CACb7B,OAAO,GAAKA,OAAO,GAAA4B,eAAA,CAAAD,IAAA,YACvB,CAAC/B,IAAI,GAAA+B,IAAA,CACd;MACH,CAAC,EAAE;QACDG,OAAO,EAAE,SAAAC,SAAA,EAAM;UACb,IAAIC,EAAE;UACN,OAAO,CAAC,CAACA,EAAE,GAAG3B,KAAK,CAACyB,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGE,EAAE,CAACC,IAAI,CAAC5B,KAAK,CAAC,CAAC;QACjE;MACF,CAAC,CAAC;IACJ,CAAC;EACH;AACF,CAAC,CAAC;AACF,SACEb,OAAO,EACPS,aAAa,IAAI6B,OAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}