{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, createTextVNode as _createTextVNode, vShow as _vShow, withCtx as _withCtx, withDirectives as _withDirectives, renderList as _renderList, Fragment as _Fragment, createBlock as _createBlock, normalizeClass as _normalizeClass, pushScopeId as _pushScopeId, popScopeId as _popScopeId } from \"vue\";\nvar _withScopeId = function _withScopeId(n) {\n  return _pushScopeId(\"data-v-8d1252b4\"), n = n(), _popScopeId(), n;\n};\nvar _hoisted_1 = {\n  class: \"obj home\"\n};\nvar _hoisted_2 = {\n  key: 0,\n  class: \"img_loading\"\n};\nvar _hoisted_3 = [\"src\"];\nvar _hoisted_4 = {\n  class: \"content\",\n  style: {\n    \"margin-top\": \"0.5rem\"\n  }\n};\nvar _hoisted_5 = {\n  class: \"money moneybg\"\n};\nvar _hoisted_6 = {\n  class: \"left\"\n};\nvar _hoisted_7 = {\n  class: \"text\"\n};\nvar _hoisted_8 = {\n  class: \"top\",\n  style: {\n    \"margin-top\": \"-0.9rem\"\n  }\n};\nvar _hoisted_9 = {\n  class: \"span\"\n};\nvar _hoisted_10 = {\n  class: \"vip\"\n};\nvar _hoisted_11 = {\n  class: \"title\",\n  style: {\n    \"text-align\": \"left\",\n    \"color\": \"#000\",\n    \"padding\": \"0.5rem\"\n  }\n};\nvar _hoisted_12 = {\n  class: \"text_n\"\n};\nvar _hoisted_13 = [\"innerHTML\"];\nvar _hoisted_14 = /*#__PURE__*/_createTextVNode(\"60 \");\nvar _hoisted_15 = {\n  class: \"btnn\"\n};\nvar _hoisted_16 = [\"src\"];\nvar _hoisted_17 = {\n  \"data-v-875e2954\": \"\"\n};\nvar _hoisted_18 = /*#__PURE__*/_withScopeId(function () {\n  return /*#__PURE__*/_createElementVNode(\"h4\", {\n    \"data-v-875e2954\": \"\"\n  }, \" 0.6%\", -1);\n});\nvar _hoisted_19 = [\"src\"];\nvar _hoisted_20 = {\n  \"data-v-875e2954\": \"\"\n};\nvar _hoisted_21 = {\n  \"data-v-875e2954\": \"\",\n  style: {\n    \"color\": \"red\"\n  }\n};\nvar _hoisted_22 = {\n  style: {\n    \"text-align\": \"left\",\n    \"color\": \"#000\",\n    \"display\": \"flex\",\n    \"align-items\": \"center\",\n    \"font-size\": \"0.8rem\",\n    \"margin-bottom\": \"0.5rem\",\n    \"font-weight\": \"bold\"\n  }\n};\nvar _hoisted_23 = {\n  style: {\n    \"text-align\": \"left\",\n    \"color\": \"#000\",\n    \"display\": \"flex\",\n    \"align-items\": \"center\",\n    \"font-size\": \"0.8rem\",\n    \"margin-bottom\": \"0.5rem\",\n    \"font-weight\": \"bold\"\n  }\n};\nvar _hoisted_24 = /*#__PURE__*/_withScopeId(function () {\n  return /*#__PURE__*/_createElementVNode(\"div\", {\n    style: {\n      \"height\": \"0.5rem\"\n    }\n  }, null, -1);\n});\nvar _hoisted_25 = {\n  class: \"hy_box\"\n};\nvar _hoisted_26 = {\n  class: \"t\"\n};\nvar _hoisted_27 = [\"src\"];\nvar _hoisted_28 = {\n  class: \"text\"\n};\nvar _hoisted_29 = {\n  class: \"b\"\n};\nvar _hoisted_30 = {\n  class: \"sub\"\n};\nvar _hoisted_31 = {\n  class: \"sub\"\n};\nvar _hoisted_32 = /*#__PURE__*/_withScopeId(function () {\n  return /*#__PURE__*/_createElementVNode(\"span\", {\n    class: \"line\"\n  }, \"|\", -1);\n});\nvar _hoisted_33 = {\n  style: {\n    \"text-align\": \"center\"\n  }\n};\nvar _hoisted_34 = {\n  key: 0,\n  class: \"list\"\n};\nvar _hoisted_35 = {\n  class: \"cet\"\n};\nvar _hoisted_36 = [\"src\"];\nvar _hoisted_37 = {\n  class: \"monney\"\n};\nvar _hoisted_38 = {\n  class: \"tent\",\n  style: {\n    \"justify-content\": \"flex-start\",\n    \"align-items\": \"center\"\n  }\n};\nvar _hoisted_39 = {\n  class: \"span\"\n};\nvar _hoisted_40 = /*#__PURE__*/_withScopeId(function () {\n  return /*#__PURE__*/_createElementVNode(\"div\", {\n    style: {\n      \"width\": \"2rem\"\n    }\n  }, null, -1);\n});\nvar _hoisted_41 = /*#__PURE__*/_createTextVNode();\nvar _hoisted_42 = {\n  class: \"tent\"\n};\nvar _hoisted_43 = {\n  class: \"span\"\n};\nvar _hoisted_44 = {\n  class: \"value\"\n};\nvar _hoisted_45 = {\n  class: \"tent\"\n};\nvar _hoisted_46 = {\n  class: \"span\"\n};\nvar _hoisted_47 = {\n  class: \"value\"\n};\nvar _hoisted_48 = {\n  class: \"tent\"\n};\nvar _hoisted_49 = {\n  class: \"span\"\n};\nvar _hoisted_50 = {\n  class: \"value\"\n};\nvar _hoisted_51 = {\n  class: \"tent\"\n};\nvar _hoisted_52 = {\n  class: \"span\"\n};\nvar _hoisted_53 = {\n  class: \"value\"\n};\nvar _hoisted_54 = {\n  class: \"tent\"\n};\nvar _hoisted_55 = {\n  class: \"span\"\n};\nvar _hoisted_56 = {\n  class: \"value\"\n};\nvar _hoisted_57 = {\n  class: \"tent\"\n};\nvar _hoisted_58 = {\n  class: \"span\"\n};\nvar _hoisted_59 = {\n  class: \"value\"\n};\nvar _hoisted_60 = {\n  class: \"tent\"\n};\nvar _hoisted_61 = {\n  class: \"span\"\n};\nvar _hoisted_62 = {\n  class: \"value\"\n};\nvar _hoisted_63 = {\n  key: 1,\n  class: \"list\"\n};\nvar _hoisted_64 = {\n  class: \"tops\"\n};\nvar _hoisted_65 = {\n  class: \"span\"\n};\nvar _hoisted_66 = {\n  class: \"span\",\n  style: {\n    \"color\": \"red\"\n  }\n};\nvar _hoisted_67 = {\n  class: \"tops\"\n};\nvar _hoisted_68 = {\n  class: \"span\"\n};\nvar _hoisted_69 = {\n  class: \"span\",\n  style: {\n    \"color\": \"#00a300\"\n  }\n};\nvar _hoisted_70 = {\n  class: \"cet\"\n};\nvar _hoisted_71 = [\"src\"];\nvar _hoisted_72 = {\n  class: \"monney\"\n};\nvar _hoisted_73 = {\n  class: \"tent\"\n};\nvar _hoisted_74 = {\n  class: \"span\"\n};\nvar _hoisted_75 = {\n  class: \"value\"\n};\nvar _hoisted_76 = {\n  class: \"tent\"\n};\nvar _hoisted_77 = {\n  class: \"span\"\n};\nvar _hoisted_78 = {\n  class: \"value\"\n};\nvar _hoisted_79 = {\n  class: \"tent\"\n};\nvar _hoisted_80 = {\n  class: \"span\"\n};\nvar _hoisted_81 = {\n  class: \"value\"\n};\nvar _hoisted_82 = {\n  class: \"tent\"\n};\nvar _hoisted_83 = {\n  class: \"span\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _$setup$info$uinfo;\n  var _component_van_nav_bar = _resolveComponent(\"van-nav-bar\");\n  var _component_van_loading = _resolveComponent(\"van-loading\");\n  var _component_uni_view = _resolveComponent(\"uni-view\");\n  var _component_van_button = _resolveComponent(\"van-button\");\n  var _component_van_icon = _resolveComponent(\"van-icon\");\n  var _component_van_swipe_item = _resolveComponent(\"van-swipe-item\");\n  var _component_van_swipe = _resolveComponent(\"van-swipe\");\n  var _component_van_dialog = _resolveComponent(\"van-dialog\");\n  var _component_review = _resolveComponent(\"review\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_van_nav_bar, {\n    title: _ctx.$t('msg.jiaoyi'),\n    background: \"#ffffff\",\n    \"title-style\": \"color: black; font-size: 16px;\"\n  }, null, 8, [\"title\"]), $setup.loading ? (_openBlock(), _createElementBlock(\"div\", _hoisted_2, [_createElementVNode(\"img\", {\n    src: $setup.loadImg,\n    class: \"img\",\n    alt: \"\"\n  }, null, 8, _hoisted_3), _createElementVNode(\"div\", null, _toDisplayString($setup.loadText), 1)])) : _createCommentVNode(\"\", true), _withDirectives(_createVNode(_component_van_loading, {\n    size: \"24px\",\n    vertical: \"\"\n  }, {\n    default: _withCtx(function () {\n      return [_createTextVNode(_toDisplayString(_ctx.$t('msg.loading')) + \"...\", 1)];\n    }),\n    _: 1\n  }, 512), [[_vShow, $setup.loading]]), _createElementVNode(\"div\", _hoisted_4, [_createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"div\", _hoisted_6, [_createElementVNode(\"div\", _hoisted_7, _toDisplayString(_ctx.$t('msg.my_yu_e')), 1), _createElementVNode(\"div\", _hoisted_8, [_createTextVNode(_toDisplayString($setup.currency) + \": \", 1), _createElementVNode(\"span\", _hoisted_9, _toDisplayString((_$setup$info$uinfo = $setup.info.uinfo) === null || _$setup$info$uinfo === void 0 ? void 0 : _$setup$info$uinfo.balance), 1)])])]), _createElementVNode(\"div\", _hoisted_10, [_createElementVNode(\"div\", _hoisted_11, _toDisplayString(_ctx.$t('msg.qdsm')), 1), _createElementVNode(\"div\", _hoisted_12, [_createElementVNode(\"div\", {\n    class: \"sub\",\n    style: {\n      \"font-size\": \"0.7rem\",\n      \"color\": \"#000\",\n      \"text-align\": \"left\",\n      \"padding\": \"0.5rem\"\n    },\n    innerHTML: $setup.content\n  }, null, 8, _hoisted_13)]), _createVNode(_component_uni_view, {\n    \"data-v-875e2954\": \"\",\n    class: \"orders\"\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_uni_view, {\n        \"data-v-875e2954\": \"\",\n        class: \"other\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_uni_view, {\n            \"data-v-875e2954\": \"\",\n            class: \"item itmsnt\"\n          }, {\n            default: _withCtx(function () {\n              return [_createVNode(_component_uni_view, {\n                \"data-v-875e2954\": \"\",\n                class: \"title\",\n                style: {\n                  \"font-size\": \"0.9rem\",\n                  \"font-weight\": \"bold\"\n                }\n              }, {\n                default: _withCtx(function () {\n                  return [_createTextVNode(_toDisplayString(_ctx.$t('msg.wanchengshu')), 1)];\n                }),\n                _: 1\n              }), _createVNode(_component_uni_view, {\n                \"data-v-875e2954\": \"\",\n                class: \"money moneyheight\",\n                style: {\n                  \"color\": \"black\"\n                }\n              }, {\n                default: _withCtx(function () {\n                  return [_createTextVNode(_toDisplayString($setup.info.day_d_count || 0), 1)];\n                }),\n                _: 1\n              })];\n            }),\n            _: 1\n          }), _createVNode(_component_uni_view, {\n            \"data-v-875e2954\": \"\",\n            class: \"item\"\n          }, {\n            default: _withCtx(function () {\n              return [_createVNode(_component_uni_view, {\n                \"data-v-875e2954\": \"\",\n                class: \"title\",\n                style: {\n                  \"font-size\": \"0.9rem\",\n                  \"font-weight\": \"bold\"\n                }\n              }, {\n                default: _withCtx(function () {\n                  return [_createTextVNode(_toDisplayString(_ctx.$t('msg.renwushu')), 1)];\n                }),\n                _: 1\n              }), _createVNode(_component_uni_view, {\n                \"data-v-875e2954\": \"\",\n                class: \"money\"\n              }, {\n                default: _withCtx(function () {\n                  return [_hoisted_14];\n                }),\n                _: 1\n              })];\n            }),\n            _: 1\n          })];\n        }),\n        _: 1\n      }), _createElementVNode(\"div\", _hoisted_15, [_createVNode(_component_van_button, {\n        round: \"\",\n        block: \"\",\n        type: \"primary\",\n        class: \"ksqg\",\n        onClick: _cache[0] || (_cache[0] = function ($event) {\n          return $setup.getDd();\n        }),\n        style: {\n          \"font-size\": \"5vw\"\n        }\n      }, {\n        default: _withCtx(function () {\n          return [_createTextVNode(_toDisplayString($setup.loading ? _ctx.$t('msg.hddd') : _ctx.$t('msg.ksqg')), 1)];\n        }),\n        _: 1\n      })]), _createVNode(_component_uni_view, {\n        \"data-v-875e2954\": \"\",\n        class: \"funcs\",\n        style: {\n          \"display\": \"none\"\n        }\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_uni_view, {\n            \"data-v-875e2954\": \"\",\n            class: \"item\"\n          }, {\n            default: _withCtx(function () {\n              return [_createElementVNode(\"img\", {\n                src: require('@/assets/images/home/<USER>'),\n                style: {\n                  \"width\": \"6.066667vw\",\n                  \"height\": \"6.066667vw\",\n                  \"margin-right\": \"3.333333vw\"\n                },\n                alt: \"\"\n              }, null, 8, _hoisted_16), _createVNode(_component_uni_view, {\n                \"data-v-875e2954\": \"\",\n                class: \"txt\"\n              }, {\n                default: _withCtx(function () {\n                  return [_createElementVNode(\"span\", _hoisted_17, _toDisplayString(_ctx.$t('msg.yonj2')) + \"：\", 1), _hoisted_18];\n                }),\n                _: 1\n              })];\n            }),\n            _: 1\n          }), _createVNode(_component_uni_view, {\n            \"data-v-875e2954\": \"\",\n            class: \"item\"\n          }, {\n            default: _withCtx(function () {\n              return [_createElementVNode(\"img\", {\n                src: require('@/assets/images/home/<USER>'),\n                style: {\n                  \"width\": \"6.066667vw\",\n                  \"height\": \"6.066667vw\",\n                  \"margin-right\": \"3.333333vw\"\n                },\n                alt: \"\"\n              }, null, 8, _hoisted_19), _createVNode(_component_uni_view, {\n                \"data-v-875e2954\": \"\",\n                class: \"txt\"\n              }, {\n                default: _withCtx(function () {\n                  return [_createElementVNode(\"span\", _hoisted_20, _toDisplayString(_ctx.$t('msg.today_monney')) + \"：\", 1), _createElementVNode(\"h4\", _hoisted_21, _toDisplayString($setup.info.yon1 || 0.00) + \" \" + _toDisplayString($setup.currency), 1)];\n                }),\n                _: 1\n              })];\n            }),\n            _: 1\n          })];\n        }),\n        _: 1\n      })];\n    }),\n    _: 1\n  }), _createElementVNode(\"div\", _hoisted_22, [_createVNode(_component_van_icon, {\n    style: {\n      \"font-size\": \"1.6rem\",\n      \"margin-right\": \"0.5rem\"\n    },\n    name: \"balance-list\"\n  }), _createTextVNode(\" \" + _toDisplayString(_ctx.$t('msg.yonj')) + \": \" + \" 0.6 % \", 1)]), _createElementVNode(\"div\", _hoisted_23, [_createVNode(_component_van_icon, {\n    style: {\n      \"font-size\": \"1.46rem\",\n      \"margin-right\": \"0.5rem\"\n    },\n    name: \"gold-coin\"\n  }), _createTextVNode(\" \" + _toDisplayString(_ctx.$t('msg.today_monney')) + \": \" + _toDisplayString($setup.info.today_commission || 0.00) + \"USDT \", 1)]), _hoisted_24])]), _createVNode(_component_van_dialog, {\n    show: $setup.level_show,\n    \"onUpdate:show\": _cache[1] || (_cache[1] = function ($event) {\n      return $setup.level_show = $event;\n    }),\n    title: _ctx.$t('msg.djsm'),\n    cancelButtonText: _ctx.$t('msg.quxiao'),\n    \"show-cancel-button\": \"\",\n    showConfirmButton: false\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_van_swipe, {\n        class: \"my-swipe\",\n        \"indicator-color\": \"white\"\n      }, {\n        default: _withCtx(function () {\n          return [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.info.level_list || [], function (item) {\n            return _openBlock(), _createBlock(_component_van_swipe_item, {\n              key: item.id\n            }, {\n              default: _withCtx(function () {\n                return [_createElementVNode(\"div\", _hoisted_25, [_createElementVNode(\"div\", _hoisted_26, [_createElementVNode(\"img\", {\n                  src: require('@/assets/images/home/<USER>'),\n                  class: \"img\",\n                  alt: \"\"\n                }, null, 8, _hoisted_27), _createElementVNode(\"span\", _hoisted_28, _toDisplayString(_ctx.$t('msg.hy_level')) + \" \" + _toDisplayString(item.name), 1)]), _createElementVNode(\"div\", _hoisted_29, [_createElementVNode(\"div\", _hoisted_30, _toDisplayString(_ctx.$t('msg.sxtz')) + \"： \" + _toDisplayString($setup.currency) + _toDisplayString(item.num), 1), _createElementVNode(\"div\", _hoisted_31, [_createTextVNode(_toDisplayString(_ctx.$t('msg.yonj')) + \"： \" + _toDisplayString(item.bili * 100) + \"% \", 1), _hoisted_32, _createTextVNode(_toDisplayString(item.order_num) + _toDisplayString(_ctx.$t('msg.order')), 1)])]), _createVNode(_component_van_button, {\n                  type: \"primary\",\n                  class: \"txlevel\",\n                  round: \"\",\n                  block: \"\"\n                }, {\n                  default: _withCtx(function () {\n                    var _$setup$info$uinfo2;\n                    return [_createTextVNode(_toDisplayString(item.level <= ((_$setup$info$uinfo2 = $setup.info.uinfo) === null || _$setup$info$uinfo2 === void 0 ? void 0 : _$setup$info$uinfo2.level) ? _ctx.$t('msg.now_level') : _ctx.$t('msg.add_level')), 1)];\n                  }),\n                  _: 2\n                }, 1024)])];\n              }),\n              _: 2\n            }, 1024);\n          }), 128))];\n        }),\n        _: 1\n      })];\n    }),\n    _: 1\n  }, 8, [\"show\", \"title\", \"cancelButtonText\"]), _createVNode(_component_van_dialog, {\n    show: $setup.showTj,\n    \"onUpdate:show\": _cache[3] || (_cache[3] = function ($event) {\n      return $setup.showTj = $event;\n    }),\n    confirmButtonText: _ctx.$t('msg.queren'),\n    cancelButtonText: _ctx.$t('msg.close'),\n    \"show-confirm-button\": true,\n    title: _ctx.$t('msg.ddxq'),\n    \"show-cancel-button\": \"\",\n    onConfirm: _cache[4] || (_cache[4] = function ($event) {\n      return $setup.confirmPwd($data.num);\n    })\n  }, {\n    title: _withCtx(function () {\n      return [_createElementVNode(\"div\", _hoisted_33, _toDisplayString(_ctx.$t('msg.ddxq')), 1)];\n    }),\n    default: _withCtx(function () {\n      var _$setup$onceinfo$data, _$setup$onceinfo$data2, _$setup$onceinfo$data3, _$setup$onceinfo$data4, _$setup$onceinfo$data5, _$setup$onceinfo$data6, _$setup$onceinfo$data7, _$setup$onceinfo$data8, _$setup$onceinfo$data9, _$setup$onceinfo$data10, _$setup$onceinfo$data11, _$setup$onceinfo$data12;\n      return [((_$setup$onceinfo$data = $setup.onceinfo.data) === null || _$setup$onceinfo$data === void 0 ? void 0 : _$setup$onceinfo$data.group_rule_num) == 0 || ((_$setup$onceinfo$data2 = $setup.onceinfo.data) === null || _$setup$onceinfo$data2 === void 0 ? void 0 : _$setup$onceinfo$data2.duorw) == 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_34, [_createElementVNode(\"div\", _hoisted_35, [_createElementVNode(\"img\", {\n        src: (_$setup$onceinfo$data3 = $setup.onceinfo.data) === null || _$setup$onceinfo$data3 === void 0 ? void 0 : _$setup$onceinfo$data3.goods_pic,\n        class: \"img\",\n        alt: \"\"\n      }, null, 8, _hoisted_36)]), _createElementVNode(\"div\", _hoisted_37, [_createElementVNode(\"div\", _hoisted_38, [_createElementVNode(\"span\", _hoisted_39, _toDisplayString(_ctx.$t('msg.evaluate')), 1), _hoisted_40, _hoisted_41, _createVNode(_component_review, {\n        class: \"span\",\n        onQhf: $options.qhfun,\n        num: $data.num,\n        \"onUpdate:num\": _cache[2] || (_cache[2] = function ($event) {\n          return $data.num = $event;\n        })\n      }, null, 8, [\"onQhf\", \"num\"])]), _createElementVNode(\"div\", _hoisted_42, [_createElementVNode(\"span\", _hoisted_43, _toDisplayString(_ctx.$t('msg.ddh')), 1), _createElementVNode(\"span\", _hoisted_44, _toDisplayString((_$setup$onceinfo$data4 = $setup.onceinfo.data) === null || _$setup$onceinfo$data4 === void 0 ? void 0 : _$setup$onceinfo$data4.oid), 1)]), _createElementVNode(\"div\", _hoisted_45, [_createElementVNode(\"span\", _hoisted_46, _toDisplayString(_ctx.$t('msg.xdsj')), 1), _createElementVNode(\"span\", _hoisted_47, _toDisplayString($setup.formatTime('', (_$setup$onceinfo$data5 = $setup.onceinfo.data) === null || _$setup$onceinfo$data5 === void 0 ? void 0 : _$setup$onceinfo$data5.addtime)), 1)]), _createElementVNode(\"div\", _hoisted_48, [_createElementVNode(\"span\", _hoisted_49, _toDisplayString(_ctx.$t('msg.spdj')), 1), _createElementVNode(\"span\", _hoisted_50, _toDisplayString($setup.currency + ((_$setup$onceinfo$data6 = $setup.onceinfo.data) === null || _$setup$onceinfo$data6 === void 0 ? void 0 : _$setup$onceinfo$data6.goods_price)), 1)]), _createElementVNode(\"div\", _hoisted_51, [_createElementVNode(\"span\", _hoisted_52, _toDisplayString(_ctx.$t('msg.spsl')), 1), _createElementVNode(\"span\", _hoisted_53, _toDisplayString('x ' + ((_$setup$onceinfo$data7 = $setup.onceinfo.data) === null || _$setup$onceinfo$data7 === void 0 ? void 0 : _$setup$onceinfo$data7.goods_count)), 1)]), _createElementVNode(\"div\", _hoisted_54, [_createElementVNode(\"span\", _hoisted_55, _toDisplayString(_ctx.$t('msg.need')), 1), _createElementVNode(\"span\", _hoisted_56, _toDisplayString($setup.currency + ((_$setup$onceinfo$data8 = $setup.onceinfo.data) === null || _$setup$onceinfo$data8 === void 0 ? void 0 : _$setup$onceinfo$data8.need)), 1)]), _createElementVNode(\"div\", _hoisted_57, [_createElementVNode(\"span\", _hoisted_58, _toDisplayString(_ctx.$t('msg.order_Num')), 1), _createElementVNode(\"span\", _hoisted_59, _toDisplayString($setup.currency + ((_$setup$onceinfo$data9 = $setup.onceinfo.data) === null || _$setup$onceinfo$data9 === void 0 ? void 0 : _$setup$onceinfo$data9.num)), 1)]), _createElementVNode(\"div\", _hoisted_60, [_createElementVNode(\"span\", _hoisted_61, _toDisplayString(_ctx.$t('msg.yonj')), 1), _createElementVNode(\"span\", _hoisted_62, _toDisplayString($setup.currency + ((_$setup$onceinfo$data10 = $setup.onceinfo.data) === null || _$setup$onceinfo$data10 === void 0 ? void 0 : _$setup$onceinfo$data10.commission)), 1)])])])) : (_openBlock(), _createElementBlock(\"div\", _hoisted_63, [_createElementVNode(\"div\", _hoisted_64, [_createElementVNode(\"span\", _hoisted_65, _toDisplayString(_ctx.$t('msg.ddrws')) + \"：\", 1), _createElementVNode(\"span\", _hoisted_66, _toDisplayString((_$setup$onceinfo$data11 = $setup.onceinfo.data) === null || _$setup$onceinfo$data11 === void 0 ? void 0 : _$setup$onceinfo$data11.duorw), 1)]), _createElementVNode(\"div\", _hoisted_67, [_createElementVNode(\"span\", _hoisted_68, _toDisplayString(_ctx.$t('msg.ywc')) + \"：\", 1), _createElementVNode(\"span\", _hoisted_69, _toDisplayString((_$setup$onceinfo$data12 = $setup.onceinfo.data) === null || _$setup$onceinfo$data12 === void 0 ? void 0 : _$setup$onceinfo$data12.completedquantity), 1)]), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.onceinfo.group_data, function (item) {\n        return _openBlock(), _createElementBlock(\"div\", {\n          class: \"box\",\n          key: item.id\n        }, [_createElementVNode(\"div\", _hoisted_70, [_createElementVNode(\"img\", {\n          src: item === null || item === void 0 ? void 0 : item.goods_pic,\n          class: \"img\",\n          alt: \"\"\n        }, null, 8, _hoisted_71)]), _createElementVNode(\"div\", _hoisted_72, [_createElementVNode(\"div\", _hoisted_73, [_createElementVNode(\"span\", _hoisted_74, _toDisplayString(_ctx.$t('msg.spdj')), 1), _createElementVNode(\"span\", _hoisted_75, _toDisplayString($setup.currency + (item === null || item === void 0 ? void 0 : item.goods_price)), 1)]), _createElementVNode(\"div\", _hoisted_76, [_createElementVNode(\"span\", _hoisted_77, _toDisplayString(_ctx.$t('msg.spsl')), 1), _createElementVNode(\"span\", _hoisted_78, _toDisplayString('x ' + (item === null || item === void 0 ? void 0 : item.goods_count)), 1)]), _createElementVNode(\"div\", _hoisted_79, [_createElementVNode(\"span\", _hoisted_80, _toDisplayString(_ctx.$t('msg.order_Num')), 1), _createElementVNode(\"span\", _hoisted_81, _toDisplayString($setup.currency + (item === null || item === void 0 ? void 0 : item.num)), 1)]), _createElementVNode(\"div\", _hoisted_82, [_createElementVNode(\"span\", _hoisted_83, _toDisplayString(_ctx.$t('msg.fkzt')), 1), _createElementVNode(\"span\", {\n          class: _normalizeClass([\"value\", 'value' + item.is_pay])\n        }, _toDisplayString(item.is_pay === 0 ? _ctx.$t('msg.dfk') : _ctx.$t('msg.yfk')), 3)])])]);\n      }), 128))]))];\n    }),\n    _: 1\n  }, 8, [\"show\", \"confirmButtonText\", \"cancelButtonText\", \"title\"])]);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}