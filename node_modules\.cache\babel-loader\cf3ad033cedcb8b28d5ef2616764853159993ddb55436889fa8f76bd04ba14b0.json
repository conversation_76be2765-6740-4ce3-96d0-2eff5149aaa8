{"ast": null, "code": "import axios from './lib/axios.js';\n\n// This module is intended to unwrap Axios default export as named.\n// Keep top-level export same with static properties\n// so that it can keep same with es module or cjs\nvar Axios = axios.Axios,\n  AxiosError = axios.AxiosError,\n  CanceledError = axios.CanceledError,\n  isCancel = axios.isCancel,\n  CancelToken = axios.CancelToken,\n  VERSION = axios.VERSION,\n  all = axios.all,\n  Cancel = axios.Cancel,\n  isAxiosError = axios.isAxiosError,\n  spread = axios.spread,\n  toFormData = axios.toFormData,\n  AxiosHeaders = axios.AxiosHeaders,\n  HttpStatusCode = axios.HttpStatusCode,\n  formToJSON = axios.formToJSON,\n  mergeConfig = axios.mergeConfig;\nexport { axios as default, Axios, AxiosError, CanceledError, isCancel, CancelToken, VERSION, all, Cancel, isAxiosError, spread, toFormData, AxiosHeaders, HttpStatusCode, formToJSON, mergeConfig };", "map": {"version": 3, "names": ["axios", "A<PERSON>os", "AxiosError", "CanceledError", "isCancel", "CancelToken", "VERSION", "all", "Cancel", "isAxiosError", "spread", "toFormData", "AxiosHeaders", "HttpStatusCode", "formToJSON", "mergeConfig", "default"], "sources": ["C:/Users/<USER>/Downloads/Telegram Desktop/vue3_3.0/vue3_3.0/node_modules/axios/index.js"], "sourcesContent": ["import axios from './lib/axios.js';\n\n// This module is intended to unwrap Axios default export as named.\n// Keep top-level export same with static properties\n// so that it can keep same with es module or cjs\nconst {\n  Axios,\n  AxiosError,\n  CanceledError,\n  isCancel,\n  CancelToken,\n  VERSION,\n  all,\n  Cancel,\n  isAxiosError,\n  spread,\n  toFormData,\n  AxiosHeaders,\n  HttpStatusCode,\n  formToJSON,\n  mergeConfig\n} = axios;\n\nexport {\n  axios as default,\n  Axios,\n  AxiosError,\n  CanceledError,\n  isCancel,\n  CancelToken,\n  VERSION,\n  all,\n  Cancel,\n  isAxiosError,\n  spread,\n  toFormData,\n  AxiosHeaders,\n  HttpStatusCode,\n  formToJSON,\n  mergeConfig\n}\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,gBAAgB;;AAElC;AACA;AACA;AACA,IACEC,KAAK,GAeHD,KAAK,CAfPC,KAAK;EACLC,UAAU,GAcRF,KAAK,CAdPE,UAAU;EACVC,aAAa,GAaXH,KAAK,CAbPG,aAAa;EACbC,QAAQ,GAYNJ,KAAK,CAZPI,QAAQ;EACRC,WAAW,GAWTL,KAAK,CAXPK,WAAW;EACXC,OAAO,GAULN,KAAK,CAVPM,OAAO;EACPC,GAAG,GASDP,KAAK,CATPO,GAAG;EACHC,MAAM,GAQJR,KAAK,CARPQ,MAAM;EACNC,YAAY,GAOVT,KAAK,CAPPS,YAAY;EACZC,MAAM,GAMJV,KAAK,CANPU,MAAM;EACNC,UAAU,GAKRX,KAAK,CALPW,UAAU;EACVC,YAAY,GAIVZ,KAAK,CAJPY,YAAY;EACZC,cAAc,GAGZb,KAAK,CAHPa,cAAc;EACdC,UAAU,GAERd,KAAK,CAFPc,UAAU;EACVC,WAAW,GACTf,KAAK,CADPe,WAAW;AAGb,SACEf,KAAK,IAAIgB,OAAO,EAChBf,KAAK,EACLC,UAAU,EACVC,aAAa,EACbC,QAAQ,EACRC,WAAW,EACXC,OAAO,EACPC,GAAG,EACHC,MAAM,EACNC,YAAY,EACZC,MAAM,EACNC,UAAU,EACVC,YAAY,EACZC,cAAc,EACdC,UAAU,EACVC,WAAW"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}