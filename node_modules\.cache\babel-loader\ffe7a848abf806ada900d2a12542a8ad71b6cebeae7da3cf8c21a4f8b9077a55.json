{"ast": null, "code": "function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nfunction _iterableToArrayLimit(arr, i) { var _i = null == arr ? null : \"undefined\" != typeof Symbol && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (null != _i) { var _s, _e, _x, _r, _arr = [], _n = !0, _d = !1; try { if (_x = (_i = _i.call(arr)).next, 0 === i) { if (Object(_i) !== _i) return; _n = !1; } else for (; !(_n = (_s = _x.call(_i)).done) && (_arr.push(_s.value), _arr.length !== i); _n = !0); } catch (err) { _d = !0, _e = err; } finally { try { if (!_n && null != _i.return && (_r = _i.return(), Object(_r) !== _r)) return; } finally { if (_d) throw _e; } } return _arr; } }\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\nimport { createNamespace } from \"../utils/index.mjs\";\nvar _createNamespace = createNamespace(\"calendar\"),\n  _createNamespace2 = _slicedToArray(_createNamespace, 3),\n  name = _createNamespace2[0],\n  bem = _createNamespace2[1],\n  t = _createNamespace2[2];\nvar formatMonthTitle = function formatMonthTitle(date) {\n  return t(\"monthTitle\", date.getFullYear(), date.getMonth() + 1);\n};\nfunction compareMonth(date1, date2) {\n  var year1 = date1.getFullYear();\n  var year2 = date2.getFullYear();\n  if (year1 === year2) {\n    var month1 = date1.getMonth();\n    var month2 = date2.getMonth();\n    return month1 === month2 ? 0 : month1 > month2 ? 1 : -1;\n  }\n  return year1 > year2 ? 1 : -1;\n}\nfunction compareDay(day1, day2) {\n  var compareMonthResult = compareMonth(day1, day2);\n  if (compareMonthResult === 0) {\n    var date1 = day1.getDate();\n    var date2 = day2.getDate();\n    return date1 === date2 ? 0 : date1 > date2 ? 1 : -1;\n  }\n  return compareMonthResult;\n}\nvar cloneDate = function cloneDate(date) {\n  return new Date(date);\n};\nvar cloneDates = function cloneDates(dates) {\n  return Array.isArray(dates) ? dates.map(cloneDate) : cloneDate(dates);\n};\nfunction getDayByOffset(date, offset) {\n  var cloned = cloneDate(date);\n  cloned.setDate(cloned.getDate() + offset);\n  return cloned;\n}\nvar getPrevDay = function getPrevDay(date) {\n  return getDayByOffset(date, -1);\n};\nvar getNextDay = function getNextDay(date) {\n  return getDayByOffset(date, 1);\n};\nvar getToday = function getToday() {\n  var today = new Date();\n  today.setHours(0, 0, 0, 0);\n  return today;\n};\nfunction calcDateNum(date) {\n  var day1 = date[0].getTime();\n  var day2 = date[1].getTime();\n  return (day2 - day1) / (1e3 * 60 * 60 * 24) + 1;\n}\nexport { bem, calcDateNum, cloneDate, cloneDates, compareDay, compareMonth, formatMonthTitle, getDayByOffset, getNextDay, getPrevDay, getToday, name, t };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}