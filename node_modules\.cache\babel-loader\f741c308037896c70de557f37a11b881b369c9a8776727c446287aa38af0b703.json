{"ast": null, "code": "import { renderSlot as _renderSlot, openBlock as _openBlock, createElementBlock as _createElementBlock, pushScopeId as _pushScopeId, popScopeId as _popScopeId } from \"vue\";\nvar _withScopeId = function _withScopeId(n) {\n  return _pushScopeId(\"data-v-309a58d9\"), n = n(), _popScopeId(), n;\n};\nvar _hoisted_1 = {\n  id: \"wrapper\",\n  ref: \"wrapper\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_renderSlot(_ctx.$slots, \"default\", {}, undefined, true)], 512);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}