{"ast": null, "code": "'use strict';\n\nfunction _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\nvar getSideChannel = require('side-channel');\nvar utils = require('./utils');\nvar formats = require('./formats');\nvar has = Object.prototype.hasOwnProperty;\nvar arrayPrefixGenerators = {\n  brackets: function brackets(prefix) {\n    return prefix + '[]';\n  },\n  comma: 'comma',\n  indices: function indices(prefix, key) {\n    return prefix + '[' + key + ']';\n  },\n  repeat: function repeat(prefix) {\n    return prefix;\n  }\n};\nvar isArray = Array.isArray;\nvar push = Array.prototype.push;\nvar pushToArray = function pushToArray(arr, valueOrArray) {\n  push.apply(arr, isArray(valueOrArray) ? valueOrArray : [valueOrArray]);\n};\nvar toISO = Date.prototype.toISOString;\nvar defaultFormat = formats['default'];\nvar defaults = {\n  addQueryPrefix: false,\n  allowDots: false,\n  charset: 'utf-8',\n  charsetSentinel: false,\n  delimiter: '&',\n  encode: true,\n  encoder: utils.encode,\n  encodeValuesOnly: false,\n  format: defaultFormat,\n  formatter: formats.formatters[defaultFormat],\n  // deprecated\n  indices: false,\n  serializeDate: function serializeDate(date) {\n    return toISO.call(date);\n  },\n  skipNulls: false,\n  strictNullHandling: false\n};\nvar isNonNullishPrimitive = function isNonNullishPrimitive(v) {\n  return typeof v === 'string' || typeof v === 'number' || typeof v === 'boolean' || _typeof(v) === 'symbol' || typeof v === 'bigint';\n};\nvar sentinel = {};\nvar stringify = function stringify(object, prefix, generateArrayPrefix, commaRoundTrip, strictNullHandling, skipNulls, encoder, filter, sort, allowDots, serializeDate, format, formatter, encodeValuesOnly, charset, sideChannel) {\n  var obj = object;\n  var tmpSc = sideChannel;\n  var step = 0;\n  var findFlag = false;\n  while ((tmpSc = tmpSc.get(sentinel)) !== void undefined && !findFlag) {\n    // Where object last appeared in the ref tree\n    var pos = tmpSc.get(object);\n    step += 1;\n    if (typeof pos !== 'undefined') {\n      if (pos === step) {\n        throw new RangeError('Cyclic object value');\n      } else {\n        findFlag = true; // Break while\n      }\n    }\n\n    if (typeof tmpSc.get(sentinel) === 'undefined') {\n      step = 0;\n    }\n  }\n  if (typeof filter === 'function') {\n    obj = filter(prefix, obj);\n  } else if (obj instanceof Date) {\n    obj = serializeDate(obj);\n  } else if (generateArrayPrefix === 'comma' && isArray(obj)) {\n    obj = utils.maybeMap(obj, function (value) {\n      if (value instanceof Date) {\n        return serializeDate(value);\n      }\n      return value;\n    });\n  }\n  if (obj === null) {\n    if (strictNullHandling) {\n      return encoder && !encodeValuesOnly ? encoder(prefix, defaults.encoder, charset, 'key', format) : prefix;\n    }\n    obj = '';\n  }\n  if (isNonNullishPrimitive(obj) || utils.isBuffer(obj)) {\n    if (encoder) {\n      var keyValue = encodeValuesOnly ? prefix : encoder(prefix, defaults.encoder, charset, 'key', format);\n      return [formatter(keyValue) + '=' + formatter(encoder(obj, defaults.encoder, charset, 'value', format))];\n    }\n    return [formatter(prefix) + '=' + formatter(String(obj))];\n  }\n  var values = [];\n  if (typeof obj === 'undefined') {\n    return values;\n  }\n  var objKeys;\n  if (generateArrayPrefix === 'comma' && isArray(obj)) {\n    // we need to join elements in\n    if (encodeValuesOnly && encoder) {\n      obj = utils.maybeMap(obj, encoder);\n    }\n    objKeys = [{\n      value: obj.length > 0 ? obj.join(',') || null : void undefined\n    }];\n  } else if (isArray(filter)) {\n    objKeys = filter;\n  } else {\n    var keys = Object.keys(obj);\n    objKeys = sort ? keys.sort(sort) : keys;\n  }\n  var adjustedPrefix = commaRoundTrip && isArray(obj) && obj.length === 1 ? prefix + '[]' : prefix;\n  for (var j = 0; j < objKeys.length; ++j) {\n    var key = objKeys[j];\n    var value = _typeof(key) === 'object' && typeof key.value !== 'undefined' ? key.value : obj[key];\n    if (skipNulls && value === null) {\n      continue;\n    }\n    var keyPrefix = isArray(obj) ? typeof generateArrayPrefix === 'function' ? generateArrayPrefix(adjustedPrefix, key) : adjustedPrefix : adjustedPrefix + (allowDots ? '.' + key : '[' + key + ']');\n    sideChannel.set(object, step);\n    var valueSideChannel = getSideChannel();\n    valueSideChannel.set(sentinel, sideChannel);\n    pushToArray(values, stringify(value, keyPrefix, generateArrayPrefix, commaRoundTrip, strictNullHandling, skipNulls, generateArrayPrefix === 'comma' && encodeValuesOnly && isArray(obj) ? null : encoder, filter, sort, allowDots, serializeDate, format, formatter, encodeValuesOnly, charset, valueSideChannel));\n  }\n  return values;\n};\nvar normalizeStringifyOptions = function normalizeStringifyOptions(opts) {\n  if (!opts) {\n    return defaults;\n  }\n  if (opts.encoder !== null && typeof opts.encoder !== 'undefined' && typeof opts.encoder !== 'function') {\n    throw new TypeError('Encoder has to be a function.');\n  }\n  var charset = opts.charset || defaults.charset;\n  if (typeof opts.charset !== 'undefined' && opts.charset !== 'utf-8' && opts.charset !== 'iso-8859-1') {\n    throw new TypeError('The charset option must be either utf-8, iso-8859-1, or undefined');\n  }\n  var format = formats['default'];\n  if (typeof opts.format !== 'undefined') {\n    if (!has.call(formats.formatters, opts.format)) {\n      throw new TypeError('Unknown format option provided.');\n    }\n    format = opts.format;\n  }\n  var formatter = formats.formatters[format];\n  var filter = defaults.filter;\n  if (typeof opts.filter === 'function' || isArray(opts.filter)) {\n    filter = opts.filter;\n  }\n  return {\n    addQueryPrefix: typeof opts.addQueryPrefix === 'boolean' ? opts.addQueryPrefix : defaults.addQueryPrefix,\n    allowDots: typeof opts.allowDots === 'undefined' ? defaults.allowDots : !!opts.allowDots,\n    charset: charset,\n    charsetSentinel: typeof opts.charsetSentinel === 'boolean' ? opts.charsetSentinel : defaults.charsetSentinel,\n    delimiter: typeof opts.delimiter === 'undefined' ? defaults.delimiter : opts.delimiter,\n    encode: typeof opts.encode === 'boolean' ? opts.encode : defaults.encode,\n    encoder: typeof opts.encoder === 'function' ? opts.encoder : defaults.encoder,\n    encodeValuesOnly: typeof opts.encodeValuesOnly === 'boolean' ? opts.encodeValuesOnly : defaults.encodeValuesOnly,\n    filter: filter,\n    format: format,\n    formatter: formatter,\n    serializeDate: typeof opts.serializeDate === 'function' ? opts.serializeDate : defaults.serializeDate,\n    skipNulls: typeof opts.skipNulls === 'boolean' ? opts.skipNulls : defaults.skipNulls,\n    sort: typeof opts.sort === 'function' ? opts.sort : null,\n    strictNullHandling: typeof opts.strictNullHandling === 'boolean' ? opts.strictNullHandling : defaults.strictNullHandling\n  };\n};\nmodule.exports = function (object, opts) {\n  var obj = object;\n  var options = normalizeStringifyOptions(opts);\n  var objKeys;\n  var filter;\n  if (typeof options.filter === 'function') {\n    filter = options.filter;\n    obj = filter('', obj);\n  } else if (isArray(options.filter)) {\n    filter = options.filter;\n    objKeys = filter;\n  }\n  var keys = [];\n  if (_typeof(obj) !== 'object' || obj === null) {\n    return '';\n  }\n  var arrayFormat;\n  if (opts && opts.arrayFormat in arrayPrefixGenerators) {\n    arrayFormat = opts.arrayFormat;\n  } else if (opts && 'indices' in opts) {\n    arrayFormat = opts.indices ? 'indices' : 'repeat';\n  } else {\n    arrayFormat = 'indices';\n  }\n  var generateArrayPrefix = arrayPrefixGenerators[arrayFormat];\n  if (opts && 'commaRoundTrip' in opts && typeof opts.commaRoundTrip !== 'boolean') {\n    throw new TypeError('`commaRoundTrip` must be a boolean, or absent');\n  }\n  var commaRoundTrip = generateArrayPrefix === 'comma' && opts && opts.commaRoundTrip;\n  if (!objKeys) {\n    objKeys = Object.keys(obj);\n  }\n  if (options.sort) {\n    objKeys.sort(options.sort);\n  }\n  var sideChannel = getSideChannel();\n  for (var i = 0; i < objKeys.length; ++i) {\n    var key = objKeys[i];\n    if (options.skipNulls && obj[key] === null) {\n      continue;\n    }\n    pushToArray(keys, stringify(obj[key], key, generateArrayPrefix, commaRoundTrip, options.strictNullHandling, options.skipNulls, options.encode ? options.encoder : null, options.filter, options.sort, options.allowDots, options.serializeDate, options.format, options.formatter, options.encodeValuesOnly, options.charset, sideChannel));\n  }\n  var joined = keys.join(options.delimiter);\n  var prefix = options.addQueryPrefix === true ? '?' : '';\n  if (options.charsetSentinel) {\n    if (options.charset === 'iso-8859-1') {\n      // encodeURIComponent('&#10003;'), the \"numeric entity\" representation of a checkmark\n      prefix += 'utf8=%26%2310003%3B&';\n    } else {\n      // encodeURIComponent('✓')\n      prefix += 'utf8=%E2%9C%93&';\n    }\n  }\n  return joined.length > 0 ? prefix + joined : '';\n};", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}