{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, withCtx as _withCtx, createCommentVNode as _createCommentVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, pushScopeId as _pushScopeId, popScopeId as _popScopeId } from \"vue\";\nvar _withScopeId = function _withScopeId(n) {\n  return _pushScopeId(\"data-v-00a31741\"), n = n(), _popScopeId(), n;\n};\nvar _hoisted_1 = {\n  class: \"home\"\n};\nvar _hoisted_2 = {\n  class: \"van-form\"\n};\nvar _hoisted_3 = {\n  class: \"item\"\n};\nvar _hoisted_4 = [\"src\"];\nvar _hoisted_5 = {\n  class: \"item code\"\n};\nvar _hoisted_6 = {\n  class: \"span\"\n};\nvar _hoisted_7 = {\n  class: \"buttons\"\n};\nvar _hoisted_8 = /*#__PURE__*/_withScopeId(function () {\n  return /*#__PURE__*/_createElementVNode(\"br\", null, null, -1 /* HOISTED */);\n});\nvar _hoisted_9 = {\n  class: \"buttons\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _$setup$info, _$setup$info2;\n  var _component_van_nav_bar = _resolveComponent(\"van-nav-bar\");\n  var _component_van_button = _resolveComponent(\"van-button\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_van_nav_bar, {\n    title: _ctx.$t('msg.chongzhi'),\n    \"left-arrow\": \"\",\n    onClickLeft: _cache[0] || (_cache[0] = function ($event) {\n      return _ctx.$router.go(-1);\n    })\n  }, null, 8 /* PROPS */, [\"title\"]), _createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"img\", {\n    src: (_$setup$info = $setup.info) === null || _$setup$info === void 0 ? void 0 : _$setup$info.ewm,\n    alt: \"\",\n    class: \"img\"\n  }, null, 8 /* PROPS */, _hoisted_4)]), _createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"span\", _hoisted_6, _toDisplayString((_$setup$info2 = $setup.info) === null || _$setup$info2 === void 0 ? void 0 : _$setup$info2.usercode), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_7, [_createVNode(_component_van_button, {\n    round: \"\",\n    block: \"\",\n    type: \"primary\",\n    onClick: _cache[1] || (_cache[1] = function ($event) {\n      var _$setup$info3;\n      return $setup.copy((_$setup$info3 = $setup.info) === null || _$setup$info3 === void 0 ? void 0 : _$setup$info3.usercode);\n    })\n  }, {\n    default: _withCtx(function () {\n      return [_createTextVNode(_toDisplayString(_ctx.$t('msg.copy')), 1 /* TEXT */)];\n    }),\n\n    _: 1 /* STABLE */\n  })]), _createCommentVNode(\" <div class=\\\"r\\\" @click=\\\"copy(info?.usercode)\\\">{{$t('msg.copy')}}</div> \"), _createCommentVNode(\" 上传图片 \"), _createCommentVNode(\" <div class=\\\"upload_\\\">\\r\\n            <van-uploader v-model=\\\"fileList\\\" multiple :max-count=\\\"1\\\" :after-read=\\\"afterRead\\\"/>\\r\\n            {{$t('msg.scfkjt')}}\\r\\n        </div> \"), _hoisted_8, _createElementVNode(\"div\", _hoisted_9, [_createVNode(_component_van_button, {\n    round: \"\",\n    block: \"\",\n    type: \"primary\",\n    onClick: $setup.onSubmit\n  }, {\n    default: _withCtx(function () {\n      return [_createTextVNode(_toDisplayString(_ctx.$t('msg.yjfk')), 1 /* TEXT */)];\n    }),\n\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"onClick\"])])])]);\n}", "map": {"version": 3, "names": ["class", "_createElementVNode", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_van_nav_bar", "title", "_ctx", "$t", "onClickLeft", "_cache", "$event", "$router", "go", "_hoisted_2", "_hoisted_3", "src", "_$setup$info", "$setup", "info", "ewm", "alt", "_hoisted_5", "_hoisted_6", "_toDisplayString", "_$setup$info2", "usercode", "_hoisted_7", "_component_van_button", "round", "block", "type", "onClick", "_$setup$info3", "copy", "_createCommentVNode", "_hoisted_8", "_hoisted_9", "onSubmit"], "sources": ["C:\\Users\\<USER>\\Downloads\\Telegram Desktop\\vue3_3.0\\vue3_3.0\\src\\views\\index\\components\\next_cz2.vue"], "sourcesContent": ["<template>\r\n  <div class=\"home\">\r\n    <van-nav-bar :title=\"$t('msg.chongzhi')\" left-arrow @click-left=\"$router.go(-1)\"></van-nav-bar>\r\n    <div class=\"van-form\">\r\n        <div class=\"item\">\r\n            <img :src=\"info?.ewm\" alt=\"\" class=\"img\">\r\n        </div>\r\n        <div class=\"item code\">\r\n            <span class=\"span\">{{info?.usercode}}</span>\r\n           \r\n        </div>\r\n\t\t<div class=\"buttons\">\r\n\t\t    <van-button round block type=\"primary\" @click=\"copy(info?.usercode)\">{{$t('msg.copy')}}\r\n\t\t    </van-button>\r\n\t\t</div>\r\n\t\t<!-- <div class=\"r\" @click=\"copy(info?.usercode)\">{{$t('msg.copy')}}</div> -->\r\n        <!-- 上传图片 -->\r\n        <!-- <div class=\"upload_\">\r\n            <van-uploader v-model=\"fileList\" multiple :max-count=\"1\" :after-read=\"afterRead\"/>\r\n            {{$t('msg.scfkjt')}}\r\n        </div> -->\r\n        <br>\r\n        <div class=\"buttons\">\r\n            <van-button round block type=\"primary\" @click=\"onSubmit\">\r\n            {{$t('msg.yjfk')}}\r\n            </van-button>\r\n        </div>\r\n      </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { reactive, ref,getCurrentInstance } from 'vue';\r\nimport store from '@/store/index'\r\nimport {uploadImg,bank_recharge} from '@/api/home/<USER>'\r\nimport { useRouter,useRoute } from 'vue-router';\r\nimport { useI18n } from 'vue-i18n'\r\n// 复制函数\r\nimport useClipboard from 'vue-clipboard3';\r\nexport default {\r\n  name: 'HomeView',\r\n  setup() {\r\n    const { toClipboard } = useClipboard();\r\n    const { t } = useI18n()\r\n    const { push,back } = useRouter();\r\n    const route = useRoute();\r\n    const {proxy} = getCurrentInstance()\r\n    const info = ref(route.query)\r\n    const currency = ref(store.state.baseInfo?.currency)\r\n    const pay = ref([])\r\n    const checked = ref('')\r\n    const url = ref('')\r\n    const fileList = ref([]);\r\n    \r\n    const clickLeft = () => {\r\n        push('/self')\r\n    }\r\n    const clickRight = () => {\r\n        push('/tel')\r\n    }\r\n    const copy = (value) => {\r\n        try {\r\n            toClipboard(value);\r\n            proxy.$Message({ type: 'success', message:t('msg.copy_s')});\r\n        } catch (e) {\r\n            proxy.$Message({ type: 'error', message:t('msg.copy_b')});\r\n        }\r\n    }\r\n    const afterRead = (file) => {\r\n        file.status = 'uploading'\r\n        file.message = t('msg.scz')\r\n        const formData = new FormData();\r\n        formData.append('file', file.file);\r\n        uploadImg(formData).then(res => {\r\n            url.value = res.url || ''\r\n            file.status = 'success'\r\n        })\r\n    }\r\n    // next_cz checked\r\n\r\n    const onSubmit = () => {\r\n        let json = {\r\n                num: info.value?.num,\r\n                url: url.value,\r\n                vip_id: route.query?.vip_id,\r\n                payId: route.query?.id\r\n            }\r\n            bank_recharge(json).then(res => {\r\n                if(res.code === 0) {\r\n                    proxy.$Message({ type: 'success', message:res.info});\r\n                    back()\r\n                } else {\r\n                    proxy.$Message({ type: 'error', message:res.info});\r\n                }\r\n            })\r\n        // if(!url.value) {\r\n        //     proxy.$Message({ type: 'error', message:t('msg.qscfkjt')});\r\n        // } else {\r\n        //     let json = {\r\n        //         num: info.value?.num,\r\n        //         url: url.value,\r\n        //         vip_id: route.query?.vip_id,\r\n        //         payId: route.query?.id\r\n        //     }\r\n        //     bank_recharge(json).then(res => {\r\n        //         if(res.code === 0) {\r\n        //             proxy.$Message({ type: 'success', message:res.info});\r\n        //             back()\r\n        //         } else {\r\n        //             proxy.$Message({ type: 'error', message:res.info});\r\n        //         }\r\n        //     })\r\n        // }\r\n\r\n    };\r\n\r\n\r\n\r\n    return {\r\n        afterRead,\r\n        copy,\r\n        checked,\r\n        pay,\r\n        onSubmit,\r\n        clickLeft,\r\n        clickRight,\r\n        info,\r\n        currency,\r\n        fileList\r\n    };\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n@import '@/styles/theme.scss';\r\n.home{\r\n    background: #f5f5f5;\r\n    :deep(.van-nav-bar){\r\n        background-color: #fff;\r\n        color: #000;\r\n        .van-nav-bar__left{\r\n            .van-icon{\r\n                color: #000;\r\n            }\r\n        }\r\n        .van-nav-bar__title{\r\n            color: #000;\r\n        }\r\n        .van-nav-bar__right{\r\n            img{\r\n                height: 42px;\r\n            }\r\n        }\r\n    }\r\n    :deep(.van-form){\r\n        padding: 40px 30px 0;\r\n        \r\n        .text_b{\r\n            margin: 150px 60px 40px;\r\n            font-size: 18px;\r\n            color: #999;\r\n            text-align: left;\r\n            .tex{\r\n                margin-top: 20px;\r\n            }\r\n        }\r\n        .buttons{\r\n            padding: 0 76px;\r\n            .van-button{\r\n                font-size: 36px;\r\n                padding: 20px 0;\r\n                height: auto;\r\n            }\r\n            .van-button--plain{\r\n                margin-top: 40px;\r\n            }\r\n        }\r\n            .hy_box{\r\n                height: 230px;\r\n                width: 100%;\r\n                padding: 25px;\r\n                color: #fff;\r\n                background-image: url('~@/assets/images/home/<USER>');\r\n                background-size: 100% 100%;\r\n                border-radius: 10px;\r\n                overflow: hidden;\r\n                position: relative;\r\n                .t{\r\n                    margin-bottom: 18px;\r\n                    text-align: left;\r\n                    .img{\r\n                        width: 65px;\r\n                        height: auto;\r\n                        margin-right: 20px;\r\n                        vertical-align: middle;\r\n                    }\r\n                    .text{\r\n                        font-size: 27px;\r\n                    }\r\n                }\r\n                .b{\r\n                    padding-left: 85px;\r\n                    font-size: 18px;\r\n                    text-align: right;\r\n                    .sub{\r\n                        .line{\r\n                            margin: 0 22px;\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        .pay{\r\n            margin-top: 80px;\r\n            text-align: left;\r\n            .title{\r\n                padding-left: 30px;\r\n                border-left: 10px solid $theme;\r\n                font-size: 24px;\r\n                color: #333;\r\n                margin-bottom: 5px;\r\n            }\r\n            .van-radio-group{\r\n                .van-cell{\r\n                    padding: 32px 0;\r\n                }\r\n                .van-cell__title{\r\n                    .img{\r\n                        width: 52px;\r\n                        margin-right: 30px;\r\n                        vertical-align: middle;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n        .item{\r\n            width: 100%;\r\n            margin-top: 30px;\r\n            display: flex;\r\n            padding: 30px;\r\n            box-shadow: $shadow;\r\n            justify-content: space-around;\r\n            .r{\r\n                color: $theme;\r\n                display: flex;\r\n                flex-direction: column;\r\n                justify-content: center;\r\n            }\r\n            .span{\r\n                display: inline-block;\r\n                word-break: break-all;\r\n                flex: 1;\r\n            }\r\n            .img{\r\n                width: 60%;\r\n            }\r\n        }\r\n        .upload_{\r\n            padding: 60px 0;\r\n            margin: 40px 0;\r\n            width: 100%;\r\n            text-align: center;\r\n            background-color: #efefef;\r\n            display: flex;\r\n            flex-direction: column;\r\n        }\r\n        .van-uploader{\r\n            .van-uploader__upload{\r\n                width: 200px;\r\n                height: 200px;\r\n                \r\n            }\r\n            .van-uploader__wrapper{\r\n                justify-content: center;\r\n            }\r\n        }\r\n    }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;EACOA,KAAK,EAAC;AAAM;;EAEVA,KAAK,EAAC;AAAU;;EACZA,KAAK,EAAC;AAAM;;;EAGZA,KAAK,EAAC;AAAW;;EACZA,KAAK,EAAC;AAAM;;EAGvBA,KAAK,EAAC;AAAS;;sBAUdC,mBAAA,CAAI;AAAA;;EACCD,KAAK,EAAC;AAAS;;;;;uBArB1BE,mBAAA,CA2BM,OA3BNC,UA2BM,GA1BJC,YAAA,CAA+FC,sBAAA;IAAjFC,KAAK,EAAEC,IAAA,CAAAC,EAAE;IAAkB,YAAU,EAAV,EAAU;IAAEC,WAAU,EAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAAEJ,IAAA,CAAAK,OAAO,CAACC,EAAE;IAAA;sCAC3EZ,mBAAA,CAwBQ,OAxBRa,UAwBQ,GAvBJb,mBAAA,CAEM,OAFNc,UAEM,GADFd,mBAAA,CAAyC;IAAnCe,GAAG,GAAAC,YAAA,GAAEC,MAAA,CAAAC,IAAI,cAAAF,YAAA,uBAAJA,YAAA,CAAMG,GAAG;IAAEC,GAAG,EAAC,EAAE;IAACrB,KAAK,EAAC;yCAEvCC,mBAAA,CAGM,OAHNqB,UAGM,GAFFrB,mBAAA,CAA4C,QAA5CsB,UAA4C,EAAAC,gBAAA,EAAAC,aAAA,GAAvBP,MAAA,CAAAC,IAAI,cAAAM,aAAA,uBAAJA,aAAA,CAAMC,QAAQ,iB,GAG7CzB,mBAAA,CAGM,OAHN0B,UAGM,GAFFvB,YAAA,CACawB,qBAAA;IADDC,KAAK,EAAL,EAAK;IAACC,KAAK,EAAL,EAAK;IAACC,IAAI,EAAC,SAAS;IAAEC,OAAK,EAAAtB,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,IAAAsB,aAAA;MAAA,OAAEf,MAAA,CAAAgB,IAAI,EAAAD,aAAA,GAACf,MAAA,CAAAC,IAAI,cAAAc,aAAA,uBAAJA,aAAA,CAAMP,QAAQ;IAAA;;sBAAG;MAAA,OAAkB,C,kCAAhBnB,IAAA,CAAAC,EAAE,6B;;;;QAG7E2B,mBAAA,+EAA8E,EACxEA,mBAAA,UAAa,EACbA,mBAAA,2LAGU,EACVC,UAAI,EACJnC,mBAAA,CAIM,OAJNoC,UAIM,GAHFjC,YAAA,CAEawB,qBAAA;IAFDC,KAAK,EAAL,EAAK;IAACC,KAAK,EAAL,EAAK;IAACC,IAAI,EAAC,SAAS;IAAEC,OAAK,EAAEd,MAAA,CAAAoB;;sBAC/C;MAAA,OAAkB,C,kCAAhB/B,IAAA,CAAAC,EAAE,6B"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}