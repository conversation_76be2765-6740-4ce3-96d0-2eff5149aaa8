{"ast": null, "code": "function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nfunction _iterableToArrayLimit(arr, i) { var _i = null == arr ? null : \"undefined\" != typeof Symbol && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (null != _i) { var _s, _e, _x, _r, _arr = [], _n = !0, _d = !1; try { if (_x = (_i = _i.call(arr)).next, 0 === i) { if (Object(_i) !== _i) return; _n = !1; } else for (; !(_n = (_s = _x.call(_i)).done) && (_arr.push(_s.value), _arr.length !== i); _n = !0); } catch (err) { _d = !0, _e = err; } finally { try { if (!_n && null != _i.return && (_r = _i.return(), Object(_r) !== _r)) return; } finally { if (_d) throw _e; } } return _arr; } }\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\nimport { createVNode as _createVNode, mergeProps as _mergeProps } from \"vue\";\nimport { watch, computed, defineComponent } from \"vue\";\nimport { createNamespace, extend, pick, truthProp } from \"../utils/index.mjs\";\nimport { CHECKBOX_GROUP_KEY } from \"../checkbox-group/CheckboxGroup.mjs\";\nimport { useParent, useCustomFieldValue } from \"@vant/use\";\nimport { useExpose } from \"../composables/use-expose.mjs\";\nimport Checker, { checkerProps } from \"./Checker.mjs\";\nvar _createNamespace = createNamespace(\"checkbox\"),\n  _createNamespace2 = _slicedToArray(_createNamespace, 2),\n  name = _createNamespace2[0],\n  bem = _createNamespace2[1];\nvar checkboxProps = extend({}, checkerProps, {\n  bindGroup: truthProp\n});\nvar stdin_default = defineComponent({\n  name: name,\n  props: checkboxProps,\n  emits: [\"change\", \"update:modelValue\"],\n  setup: function setup(props, _ref) {\n    var emit = _ref.emit,\n      slots = _ref.slots;\n    var _useParent = useParent(CHECKBOX_GROUP_KEY),\n      parent = _useParent.parent;\n    var setParentValue = function setParentValue(checked2) {\n      var name2 = props.name;\n      var _parent$props = parent.props,\n        max = _parent$props.max,\n        modelValue = _parent$props.modelValue;\n      var value = modelValue.slice();\n      if (checked2) {\n        var overlimit = max && value.length >= max;\n        if (!overlimit && !value.includes(name2)) {\n          value.push(name2);\n          if (props.bindGroup) {\n            parent.updateValue(value);\n          }\n        }\n      } else {\n        var index = value.indexOf(name2);\n        if (index !== -1) {\n          value.splice(index, 1);\n          if (props.bindGroup) {\n            parent.updateValue(value);\n          }\n        }\n      }\n    };\n    var checked = computed(function () {\n      if (parent && props.bindGroup) {\n        return parent.props.modelValue.indexOf(props.name) !== -1;\n      }\n      return !!props.modelValue;\n    });\n    var toggle = function toggle() {\n      var newValue = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : !checked.value;\n      if (parent && props.bindGroup) {\n        setParentValue(newValue);\n      } else {\n        emit(\"update:modelValue\", newValue);\n      }\n    };\n    watch(function () {\n      return props.modelValue;\n    }, function (value) {\n      return emit(\"change\", value);\n    });\n    useExpose({\n      toggle: toggle,\n      props: props,\n      checked: checked\n    });\n    useCustomFieldValue(function () {\n      return props.modelValue;\n    });\n    return function () {\n      return _createVNode(Checker, _mergeProps({\n        \"bem\": bem,\n        \"role\": \"checkbox\",\n        \"parent\": parent,\n        \"checked\": checked.value,\n        \"onToggle\": toggle\n      }, props), pick(slots, [\"default\", \"icon\"]));\n    };\n  }\n});\nexport { stdin_default as default };", "map": {"version": 3, "names": ["createVNode", "_createVNode", "mergeProps", "_mergeProps", "watch", "computed", "defineComponent", "createNamespace", "extend", "pick", "truthProp", "CHECKBOX_GROUP_KEY", "useParent", "useCustomFieldValue", "useExpose", "Checker", "checkerProps", "_createNamespace", "_createNamespace2", "_slicedToArray", "name", "bem", "checkboxProps", "bindGroup", "stdin_default", "props", "emits", "setup", "_ref", "emit", "slots", "_useParent", "parent", "setParentValue", "checked2", "name2", "_parent$props", "max", "modelValue", "value", "slice", "overlimit", "length", "includes", "push", "updateValue", "index", "indexOf", "splice", "checked", "toggle", "newValue", "arguments", "undefined", "default"], "sources": ["C:/Users/<USER>/Desktop/vue3_3.0/node_modules/vant/es/checkbox/Checkbox.mjs"], "sourcesContent": ["import { createVNode as _createVNode, mergeProps as _mergeProps } from \"vue\";\nimport { watch, computed, defineComponent } from \"vue\";\nimport { createNamespace, extend, pick, truthProp } from \"../utils/index.mjs\";\nimport { CHECKBOX_GROUP_KEY } from \"../checkbox-group/CheckboxGroup.mjs\";\nimport { useParent, useCustomFieldValue } from \"@vant/use\";\nimport { useExpose } from \"../composables/use-expose.mjs\";\nimport Checker, { checkerProps } from \"./Checker.mjs\";\nconst [name, bem] = createNamespace(\"checkbox\");\nconst checkboxProps = extend({}, checkerProps, {\n  bindGroup: truthProp\n});\nvar stdin_default = defineComponent({\n  name,\n  props: checkboxProps,\n  emits: [\"change\", \"update:modelValue\"],\n  setup(props, {\n    emit,\n    slots\n  }) {\n    const {\n      parent\n    } = useParent(CHECKBOX_GROUP_KEY);\n    const setParentValue = (checked2) => {\n      const {\n        name: name2\n      } = props;\n      const {\n        max,\n        modelValue\n      } = parent.props;\n      const value = modelValue.slice();\n      if (checked2) {\n        const overlimit = max && value.length >= max;\n        if (!overlimit && !value.includes(name2)) {\n          value.push(name2);\n          if (props.bindGroup) {\n            parent.updateValue(value);\n          }\n        }\n      } else {\n        const index = value.indexOf(name2);\n        if (index !== -1) {\n          value.splice(index, 1);\n          if (props.bindGroup) {\n            parent.updateValue(value);\n          }\n        }\n      }\n    };\n    const checked = computed(() => {\n      if (parent && props.bindGroup) {\n        return parent.props.modelValue.indexOf(props.name) !== -1;\n      }\n      return !!props.modelValue;\n    });\n    const toggle = (newValue = !checked.value) => {\n      if (parent && props.bindGroup) {\n        setParentValue(newValue);\n      } else {\n        emit(\"update:modelValue\", newValue);\n      }\n    };\n    watch(() => props.modelValue, (value) => emit(\"change\", value));\n    useExpose({\n      toggle,\n      props,\n      checked\n    });\n    useCustomFieldValue(() => props.modelValue);\n    return () => _createVNode(Checker, _mergeProps({\n      \"bem\": bem,\n      \"role\": \"checkbox\",\n      \"parent\": parent,\n      \"checked\": checked.value,\n      \"onToggle\": toggle\n    }, props), pick(slots, [\"default\", \"icon\"]));\n  }\n});\nexport {\n  stdin_default as default\n};\n"], "mappings": ";;;;;;AAAA,SAASA,WAAW,IAAIC,YAAY,EAAEC,UAAU,IAAIC,WAAW,QAAQ,KAAK;AAC5E,SAASC,KAAK,EAAEC,QAAQ,EAAEC,eAAe,QAAQ,KAAK;AACtD,SAASC,eAAe,EAAEC,MAAM,EAAEC,IAAI,EAAEC,SAAS,QAAQ,oBAAoB;AAC7E,SAASC,kBAAkB,QAAQ,qCAAqC;AACxE,SAASC,SAAS,EAAEC,mBAAmB,QAAQ,WAAW;AAC1D,SAASC,SAAS,QAAQ,+BAA+B;AACzD,OAAOC,OAAO,IAAIC,YAAY,QAAQ,eAAe;AACrD,IAAAC,gBAAA,GAAoBV,eAAe,CAAC,UAAU,CAAC;EAAAW,iBAAA,GAAAC,cAAA,CAAAF,gBAAA;EAAxCG,IAAI,GAAAF,iBAAA;EAAEG,GAAG,GAAAH,iBAAA;AAChB,IAAMI,aAAa,GAAGd,MAAM,CAAC,CAAC,CAAC,EAAEQ,YAAY,EAAE;EAC7CO,SAAS,EAAEb;AACb,CAAC,CAAC;AACF,IAAIc,aAAa,GAAGlB,eAAe,CAAC;EAClCc,IAAI,EAAJA,IAAI;EACJK,KAAK,EAAEH,aAAa;EACpBI,KAAK,EAAE,CAAC,QAAQ,EAAE,mBAAmB,CAAC;EACtCC,KAAK,WAAAA,MAACF,KAAK,EAAAG,IAAA,EAGR;IAAA,IAFDC,IAAI,GAAAD,IAAA,CAAJC,IAAI;MACJC,KAAK,GAAAF,IAAA,CAALE,KAAK;IAEL,IAAAC,UAAA,GAEInB,SAAS,CAACD,kBAAkB,CAAC;MAD/BqB,MAAM,GAAAD,UAAA,CAANC,MAAM;IAER,IAAMC,cAAc,GAAG,SAAjBA,cAAcA,CAAIC,QAAQ,EAAK;MACnC,IACQC,KAAK,GACTV,KAAK,CADPL,IAAI;MAEN,IAAAgB,aAAA,GAGIJ,MAAM,CAACP,KAAK;QAFdY,GAAG,GAAAD,aAAA,CAAHC,GAAG;QACHC,UAAU,GAAAF,aAAA,CAAVE,UAAU;MAEZ,IAAMC,KAAK,GAAGD,UAAU,CAACE,KAAK,CAAC,CAAC;MAChC,IAAIN,QAAQ,EAAE;QACZ,IAAMO,SAAS,GAAGJ,GAAG,IAAIE,KAAK,CAACG,MAAM,IAAIL,GAAG;QAC5C,IAAI,CAACI,SAAS,IAAI,CAACF,KAAK,CAACI,QAAQ,CAACR,KAAK,CAAC,EAAE;UACxCI,KAAK,CAACK,IAAI,CAACT,KAAK,CAAC;UACjB,IAAIV,KAAK,CAACF,SAAS,EAAE;YACnBS,MAAM,CAACa,WAAW,CAACN,KAAK,CAAC;UAC3B;QACF;MACF,CAAC,MAAM;QACL,IAAMO,KAAK,GAAGP,KAAK,CAACQ,OAAO,CAACZ,KAAK,CAAC;QAClC,IAAIW,KAAK,KAAK,CAAC,CAAC,EAAE;UAChBP,KAAK,CAACS,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;UACtB,IAAIrB,KAAK,CAACF,SAAS,EAAE;YACnBS,MAAM,CAACa,WAAW,CAACN,KAAK,CAAC;UAC3B;QACF;MACF;IACF,CAAC;IACD,IAAMU,OAAO,GAAG5C,QAAQ,CAAC,YAAM;MAC7B,IAAI2B,MAAM,IAAIP,KAAK,CAACF,SAAS,EAAE;QAC7B,OAAOS,MAAM,CAACP,KAAK,CAACa,UAAU,CAACS,OAAO,CAACtB,KAAK,CAACL,IAAI,CAAC,KAAK,CAAC,CAAC;MAC3D;MACA,OAAO,CAAC,CAACK,KAAK,CAACa,UAAU;IAC3B,CAAC,CAAC;IACF,IAAMY,MAAM,GAAG,SAATA,MAAMA,CAAA,EAAkC;MAAA,IAA9BC,QAAQ,GAAAC,SAAA,CAAAV,MAAA,QAAAU,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,CAACH,OAAO,CAACV,KAAK;MACvC,IAAIP,MAAM,IAAIP,KAAK,CAACF,SAAS,EAAE;QAC7BU,cAAc,CAACkB,QAAQ,CAAC;MAC1B,CAAC,MAAM;QACLtB,IAAI,CAAC,mBAAmB,EAAEsB,QAAQ,CAAC;MACrC;IACF,CAAC;IACD/C,KAAK,CAAC;MAAA,OAAMqB,KAAK,CAACa,UAAU;IAAA,GAAE,UAACC,KAAK;MAAA,OAAKV,IAAI,CAAC,QAAQ,EAAEU,KAAK,CAAC;IAAA,EAAC;IAC/DzB,SAAS,CAAC;MACRoC,MAAM,EAANA,MAAM;MACNzB,KAAK,EAALA,KAAK;MACLwB,OAAO,EAAPA;IACF,CAAC,CAAC;IACFpC,mBAAmB,CAAC;MAAA,OAAMY,KAAK,CAACa,UAAU;IAAA,EAAC;IAC3C,OAAO;MAAA,OAAMrC,YAAY,CAACc,OAAO,EAAEZ,WAAW,CAAC;QAC7C,KAAK,EAAEkB,GAAG;QACV,MAAM,EAAE,UAAU;QAClB,QAAQ,EAAEW,MAAM;QAChB,SAAS,EAAEiB,OAAO,CAACV,KAAK;QACxB,UAAU,EAAEW;MACd,CAAC,EAAEzB,KAAK,CAAC,EAAEhB,IAAI,CAACqB,KAAK,EAAE,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC,CAAC;IAAA;EAC9C;AACF,CAAC,CAAC;AACF,SACEN,aAAa,IAAI8B,OAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}