{"ast": null, "code": "import http from '@/request/index';\nimport qs from 'qs';\n// 首页数据\nexport var getHomeData = function getHomeData() {\n  return http.get('/index/home').then(function (result) {\n    return result.data;\n  });\n};\n\n// 首页数据\nexport var get_msg = function get_msg() {\n  return http.get('/index/get_msg').then(function (result) {\n    return result.data;\n  });\n};\n\n// 首页数据\nexport var get_level_list = function get_level_list() {\n  return http.get('/index/get_level_list').then(function (result) {\n    return result.data;\n  });\n};\n\n// 首页数据\nexport var get_lixibao = function get_lixibao() {\n  return http.get('/ctrl/lixibao').then(function (result) {\n    return result.data;\n  });\n};\n// 首页数据\nexport var lixibao_chu = function lixibao_chu(params) {\n  return http.get('/ctrl/lixibao_chu?' + qs.stringify(params)).then(function (result) {\n    return result.data;\n  });\n};\n// 首页数据\nexport var getdetailbyid = function getdetailbyid(id) {\n  return http.get('/my/detail?id=' + id).then(function (result) {\n    return result.data;\n  });\n};\n\n// 首页数据\nexport var lixibao_ru = function lixibao_ru(params) {\n  return http.post('/ctrl/lixibao_ru', qs.stringify(params)).then(function (result) {\n    return result.data;\n  });\n};\n\n// 首页数据\nexport var get_lixibao_chu = function get_lixibao_chu(params) {\n  return http.post('/ctrl/lixibao_chu', qs.stringify(params)).then(function (result) {\n    return result.data;\n  });\n};\n\n// 首页数据\nexport var get_recharge = function get_recharge(params) {\n  return http.post('/ctrl/recharge?' + qs.stringify(params)).then(function (result) {\n    return result.data;\n  });\n};\n\n// 首页数据\nexport var get_recharge2 = function get_recharge2(params) {\n  return http.post('/ctrl/recharge2?' + qs.stringify(params)).then(function (result) {\n    return result.data;\n  });\n};\n\n// 首页数据\nexport var bank_recharge = function bank_recharge(params) {\n  return http.post('/ctrl/bank_recharge', qs.stringify(params)).then(function (result) {\n    return result.data;\n  });\n};\n\n// 首页数据\nexport var headpicUpdatae = function headpicUpdatae(params) {\n  return http.post('/my/headpicUpdatae', qs.stringify(params)).then(function (result) {\n    return result.data;\n  });\n};\n\n// 首页数据\nexport var uploadImg = function uploadImg(params) {\n  return http.post('/admin/index.html?s=/admin/api.plugs/upload', params, {\n    // 因为我们上传了图片,因此需要单独执行请求头的Content-Type\n    headers: {\n      // 表示上传的是文件,而不是普通的表单数据\n      'Content-Type': 'multipart/form-data'\n    }\n  }).then(function (result) {\n    return result.data;\n  });\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}