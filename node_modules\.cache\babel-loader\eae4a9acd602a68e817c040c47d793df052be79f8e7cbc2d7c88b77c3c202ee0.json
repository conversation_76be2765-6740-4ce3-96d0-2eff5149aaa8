{"ast": null, "code": "import http from '@/request/index';\nimport qs from 'qs';\n// 公共参数 -- 字典值\nexport var common_parameters = function common_parameters() {\n  return http.get('/user/common_parameters').then(function (result) {\n    return result.data;\n  });\n};\n\n// 定义接口的传参\n// interface UserInfoParam {\n//     tel: string,\n//     pwd: string\n//     qv: string\n// }\n//登录\nexport var login = function login(params) {\n  return http.post('/user/do_login', qs.stringify(params)).then(function (result) {\n    return result.data;\n  });\n};\n//退出登录\nexport var logout = function logout(params) {\n  return http.post('/user/logout', qs.stringify(params)).then(function (result) {\n    return result.data;\n  });\n};\n//注册\nexport var do_register = function do_register(params) {\n  return http.post('/user/do_register', qs.stringify(params)).then(function (result) {\n    return result.data;\n  });\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}