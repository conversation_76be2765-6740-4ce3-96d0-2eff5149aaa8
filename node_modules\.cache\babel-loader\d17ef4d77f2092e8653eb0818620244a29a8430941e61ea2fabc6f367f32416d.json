{"ast": null, "code": "function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nfunction _iterableToArrayLimit(arr, i) { var _i = null == arr ? null : \"undefined\" != typeof Symbol && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (null != _i) { var _s, _e, _x, _r, _arr = [], _n = !0, _d = !1; try { if (_x = (_i = _i.call(arr)).next, 0 === i) { if (Object(_i) !== _i) return; _n = !1; } else for (; !(_n = (_s = _x.call(_i)).done) && (_arr.push(_s.value), _arr.length !== i); _n = !0); } catch (err) { _d = !0, _e = err; } finally { try { if (!_n && null != _i.return && (_r = _i.return(), Object(_r) !== _r)) return; } finally { if (_d) throw _e; } } return _arr; } }\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\nimport { createVNode as _createVNode } from \"vue\";\nimport { defineComponent } from \"vue\";\nimport { makeStringProp, makeNumericProp, createNamespace } from \"../utils/index.mjs\";\nimport { useChildren } from \"@vant/use\";\nvar _createNamespace = createNamespace(\"steps\"),\n  _createNamespace2 = _slicedToArray(_createNamespace, 2),\n  name = _createNamespace2[0],\n  bem = _createNamespace2[1];\nvar stepsProps = {\n  active: makeNumericProp(0),\n  direction: makeStringProp(\"horizontal\"),\n  activeIcon: makeStringProp(\"checked\"),\n  iconPrefix: String,\n  finishIcon: String,\n  activeColor: String,\n  inactiveIcon: String,\n  inactiveColor: String\n};\nvar STEPS_KEY = Symbol(name);\nvar stdin_default = defineComponent({\n  name: name,\n  props: stepsProps,\n  emits: [\"click-step\"],\n  setup: function setup(props, _ref) {\n    var emit = _ref.emit,\n      slots = _ref.slots;\n    var _useChildren = useChildren(STEPS_KEY),\n      linkChildren = _useChildren.linkChildren;\n    var onClickStep = function onClickStep(index) {\n      return emit(\"click-step\", index);\n    };\n    linkChildren({\n      props: props,\n      onClickStep: onClickStep\n    });\n    return function () {\n      var _a;\n      return _createVNode(\"div\", {\n        \"class\": bem([props.direction])\n      }, [_createVNode(\"div\", {\n        \"class\": bem(\"items\")\n      }, [(_a = slots.default) == null ? void 0 : _a.call(slots)])]);\n    };\n  }\n});\nexport { STEPS_KEY, stdin_default as default };", "map": {"version": 3, "names": ["createVNode", "_createVNode", "defineComponent", "makeStringProp", "makeNumericProp", "createNamespace", "useChildren", "_createNamespace", "_createNamespace2", "_slicedToArray", "name", "bem", "stepsProps", "active", "direction", "activeIcon", "iconPrefix", "String", "finishIcon", "activeColor", "inactiveIcon", "inactiveColor", "STEPS_KEY", "Symbol", "stdin_default", "props", "emits", "setup", "_ref", "emit", "slots", "_useChildren", "linkChildren", "onClickStep", "index", "_a", "default", "call"], "sources": ["C:/Users/<USER>/Downloads/Telegram Desktop/vue3_3.0/vue3_3.0/node_modules/vant/es/steps/Steps.mjs"], "sourcesContent": ["import { createVNode as _createVNode } from \"vue\";\nimport { defineComponent } from \"vue\";\nimport { makeStringProp, makeNumericProp, createNamespace } from \"../utils/index.mjs\";\nimport { useChildren } from \"@vant/use\";\nconst [name, bem] = createNamespace(\"steps\");\nconst stepsProps = {\n  active: makeNumericProp(0),\n  direction: makeStringProp(\"horizontal\"),\n  activeIcon: makeStringProp(\"checked\"),\n  iconPrefix: String,\n  finishIcon: String,\n  activeColor: String,\n  inactiveIcon: String,\n  inactiveColor: String\n};\nconst STEPS_KEY = Symbol(name);\nvar stdin_default = defineComponent({\n  name,\n  props: stepsProps,\n  emits: [\"click-step\"],\n  setup(props, {\n    emit,\n    slots\n  }) {\n    const {\n      linkChildren\n    } = useChildren(STEPS_KEY);\n    const onClickStep = (index) => emit(\"click-step\", index);\n    linkChildren({\n      props,\n      onClickStep\n    });\n    return () => {\n      var _a;\n      return _createVNode(\"div\", {\n        \"class\": bem([props.direction])\n      }, [_createVNode(\"div\", {\n        \"class\": bem(\"items\")\n      }, [(_a = slots.default) == null ? void 0 : _a.call(slots)])]);\n    };\n  }\n});\nexport {\n  STEPS_KEY,\n  stdin_default as default\n};\n"], "mappings": ";;;;;;AAAA,SAASA,WAAW,IAAIC,YAAY,QAAQ,KAAK;AACjD,SAASC,eAAe,QAAQ,KAAK;AACrC,SAASC,cAAc,EAAEC,eAAe,EAAEC,eAAe,QAAQ,oBAAoB;AACrF,SAASC,WAAW,QAAQ,WAAW;AACvC,IAAAC,gBAAA,GAAoBF,eAAe,CAAC,OAAO,CAAC;EAAAG,iBAAA,GAAAC,cAAA,CAAAF,gBAAA;EAArCG,IAAI,GAAAF,iBAAA;EAAEG,GAAG,GAAAH,iBAAA;AAChB,IAAMI,UAAU,GAAG;EACjBC,MAAM,EAAET,eAAe,CAAC,CAAC,CAAC;EAC1BU,SAAS,EAAEX,cAAc,CAAC,YAAY,CAAC;EACvCY,UAAU,EAAEZ,cAAc,CAAC,SAAS,CAAC;EACrCa,UAAU,EAAEC,MAAM;EAClBC,UAAU,EAAED,MAAM;EAClBE,WAAW,EAAEF,MAAM;EACnBG,YAAY,EAAEH,MAAM;EACpBI,aAAa,EAAEJ;AACjB,CAAC;AACD,IAAMK,SAAS,GAAGC,MAAM,CAACb,IAAI,CAAC;AAC9B,IAAIc,aAAa,GAAGtB,eAAe,CAAC;EAClCQ,IAAI,EAAJA,IAAI;EACJe,KAAK,EAAEb,UAAU;EACjBc,KAAK,EAAE,CAAC,YAAY,CAAC;EACrBC,KAAK,WAAAA,MAACF,KAAK,EAAAG,IAAA,EAGR;IAAA,IAFDC,IAAI,GAAAD,IAAA,CAAJC,IAAI;MACJC,KAAK,GAAAF,IAAA,CAALE,KAAK;IAEL,IAAAC,YAAA,GAEIzB,WAAW,CAACgB,SAAS,CAAC;MADxBU,YAAY,GAAAD,YAAA,CAAZC,YAAY;IAEd,IAAMC,WAAW,GAAG,SAAdA,WAAWA,CAAIC,KAAK;MAAA,OAAKL,IAAI,CAAC,YAAY,EAAEK,KAAK,CAAC;IAAA;IACxDF,YAAY,CAAC;MACXP,KAAK,EAALA,KAAK;MACLQ,WAAW,EAAXA;IACF,CAAC,CAAC;IACF,OAAO,YAAM;MACX,IAAIE,EAAE;MACN,OAAOlC,YAAY,CAAC,KAAK,EAAE;QACzB,OAAO,EAAEU,GAAG,CAAC,CAACc,KAAK,CAACX,SAAS,CAAC;MAChC,CAAC,EAAE,CAACb,YAAY,CAAC,KAAK,EAAE;QACtB,OAAO,EAAEU,GAAG,CAAC,OAAO;MACtB,CAAC,EAAE,CAAC,CAACwB,EAAE,GAAGL,KAAK,CAACM,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGD,EAAE,CAACE,IAAI,CAACP,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IAChE,CAAC;EACH;AACF,CAAC,CAAC;AACF,SACER,SAAS,EACTE,aAAa,IAAIY,OAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}