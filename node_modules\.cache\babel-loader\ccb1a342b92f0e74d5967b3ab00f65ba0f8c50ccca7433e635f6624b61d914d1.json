{"ast": null, "code": "import { getTarget, getDevtoolsGlobalHook, isProxyAvailable } from './env.js';\nimport { HOOK_SETUP } from './const.js';\nimport { ApiProxy } from './proxy.js';\nexport * from './api/index.js';\nexport * from './plugin.js';\nexport * from './time.js';\nexport function setupDevtoolsPlugin(pluginDescriptor, setupFn) {\n  var descriptor = pluginDescriptor;\n  var target = getTarget();\n  var hook = getDevtoolsGlobalHook();\n  var enableProxy = isProxyAvailable && descriptor.enableEarlyProxy;\n  if (hook && (target.__VUE_DEVTOOLS_PLUGIN_API_AVAILABLE__ || !enableProxy)) {\n    hook.emit(HOOK_SETUP, pluginDescriptor, setupFn);\n  } else {\n    var proxy = enableProxy ? new ApiProxy(descriptor, hook) : null;\n    var list = target.__VUE_DEVTOOLS_PLUGINS__ = target.__VUE_DEVTOOLS_PLUGINS__ || [];\n    list.push({\n      pluginDescriptor: descriptor,\n      setupFn: setupFn,\n      proxy: proxy\n    });\n    if (proxy) setupFn(proxy.proxiedTarget);\n  }\n}", "map": {"version": 3, "names": ["get<PERSON><PERSON><PERSON>", "getDevtoolsGlobalHook", "isProxyAvailable", "HOOK_SETUP", "ApiProxy", "setupDevtoolsPlugin", "pluginDescriptor", "setupFn", "descriptor", "target", "hook", "enableProxy", "enableEarlyProxy", "__VUE_DEVTOOLS_PLUGIN_API_AVAILABLE__", "emit", "proxy", "list", "__VUE_DEVTOOLS_PLUGINS__", "push", "proxied<PERSON><PERSON><PERSON>"], "sources": ["C:/Users/<USER>/Desktop/vue3_3.0/node_modules/@vue/devtools-api/lib/esm/index.js"], "sourcesContent": ["import { getTarget, getDevtoolsGlobalHook, isProxyAvailable } from './env.js';\nimport { HOOK_SETUP } from './const.js';\nimport { ApiProxy } from './proxy.js';\nexport * from './api/index.js';\nexport * from './plugin.js';\nexport * from './time.js';\nexport function setupDevtoolsPlugin(pluginDescriptor, setupFn) {\n    const descriptor = pluginDescriptor;\n    const target = getTarget();\n    const hook = getDevtoolsGlobalHook();\n    const enableProxy = isProxyAvailable && descriptor.enableEarlyProxy;\n    if (hook && (target.__VUE_DEVTOOLS_PLUGIN_API_AVAILABLE__ || !enableProxy)) {\n        hook.emit(HOOK_SETUP, pluginDescriptor, setupFn);\n    }\n    else {\n        const proxy = enableProxy ? new ApiProxy(descriptor, hook) : null;\n        const list = target.__VUE_DEVTOOLS_PLUGINS__ = target.__VUE_DEVTOOLS_PLUGINS__ || [];\n        list.push({\n            pluginDescriptor: descriptor,\n            setupFn,\n            proxy,\n        });\n        if (proxy)\n            setupFn(proxy.proxiedTarget);\n    }\n}\n"], "mappings": "AAAA,SAASA,SAAS,EAAEC,qBAAqB,EAAEC,gBAAgB,QAAQ,UAAU;AAC7E,SAASC,UAAU,QAAQ,YAAY;AACvC,SAASC,QAAQ,QAAQ,YAAY;AACrC,cAAc,gBAAgB;AAC9B,cAAc,aAAa;AAC3B,cAAc,WAAW;AACzB,OAAO,SAASC,mBAAmBA,CAACC,gBAAgB,EAAEC,OAAO,EAAE;EAC3D,IAAMC,UAAU,GAAGF,gBAAgB;EACnC,IAAMG,MAAM,GAAGT,SAAS,CAAC,CAAC;EAC1B,IAAMU,IAAI,GAAGT,qBAAqB,CAAC,CAAC;EACpC,IAAMU,WAAW,GAAGT,gBAAgB,IAAIM,UAAU,CAACI,gBAAgB;EACnE,IAAIF,IAAI,KAAKD,MAAM,CAACI,qCAAqC,IAAI,CAACF,WAAW,CAAC,EAAE;IACxED,IAAI,CAACI,IAAI,CAACX,UAAU,EAAEG,gBAAgB,EAAEC,OAAO,CAAC;EACpD,CAAC,MACI;IACD,IAAMQ,KAAK,GAAGJ,WAAW,GAAG,IAAIP,QAAQ,CAACI,UAAU,EAAEE,IAAI,CAAC,GAAG,IAAI;IACjE,IAAMM,IAAI,GAAGP,MAAM,CAACQ,wBAAwB,GAAGR,MAAM,CAACQ,wBAAwB,IAAI,EAAE;IACpFD,IAAI,CAACE,IAAI,CAAC;MACNZ,gBAAgB,EAAEE,UAAU;MAC5BD,OAAO,EAAPA,OAAO;MACPQ,KAAK,EAALA;IACJ,CAAC,CAAC;IACF,IAAIA,KAAK,EACLR,OAAO,CAACQ,KAAK,CAACI,aAAa,CAAC;EACpC;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}