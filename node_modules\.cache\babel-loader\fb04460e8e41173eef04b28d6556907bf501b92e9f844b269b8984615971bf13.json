{"ast": null, "code": "function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nfunction _iterableToArrayLimit(arr, i) { var _i = null == arr ? null : \"undefined\" != typeof Symbol && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (null != _i) { var _s, _e, _x, _r, _arr = [], _n = !0, _d = !1; try { if (_x = (_i = _i.call(arr)).next, 0 === i) { if (Object(_i) !== _i) return; _n = !1; } else for (; !(_n = (_s = _x.call(_i)).done) && (_arr.push(_s.value), _arr.length !== i); _n = !0); } catch (err) { _d = !0, _e = err; } finally { try { if (!_n && null != _i.return && (_r = _i.return(), Object(_r) !== _r)) return; } finally { if (_d) throw _e; } } return _arr; } }\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\nimport { createVNode as _createVNode } from \"vue\";\nimport { defineComponent } from \"vue\";\nimport { addUnit, numericProp, unknownProp, createNamespace } from \"../utils/index.mjs\";\nimport { useCustomFieldValue } from \"@vant/use\";\nimport { Loading } from \"../loading/index.mjs\";\nvar _createNamespace = createNamespace(\"switch\"),\n  _createNamespace2 = _slicedToArray(_createNamespace, 2),\n  name = _createNamespace2[0],\n  bem = _createNamespace2[1];\nvar switchProps = {\n  size: numericProp,\n  loading: Boolean,\n  disabled: Boolean,\n  modelValue: unknownProp,\n  activeColor: String,\n  inactiveColor: String,\n  activeValue: {\n    type: unknownProp,\n    default: true\n  },\n  inactiveValue: {\n    type: unknownProp,\n    default: false\n  }\n};\nvar stdin_default = defineComponent({\n  name: name,\n  props: switchProps,\n  emits: [\"change\", \"update:modelValue\"],\n  setup: function setup(props, _ref) {\n    var emit = _ref.emit,\n      slots = _ref.slots;\n    var isChecked = function isChecked() {\n      return props.modelValue === props.activeValue;\n    };\n    var onClick = function onClick() {\n      if (!props.disabled && !props.loading) {\n        var newValue = isChecked() ? props.inactiveValue : props.activeValue;\n        emit(\"update:modelValue\", newValue);\n        emit(\"change\", newValue);\n      }\n    };\n    var renderLoading = function renderLoading() {\n      if (props.loading) {\n        var color = isChecked() ? props.activeColor : props.inactiveColor;\n        return _createVNode(Loading, {\n          \"class\": bem(\"loading\"),\n          \"color\": color\n        }, null);\n      }\n      if (slots.node) {\n        return slots.node();\n      }\n    };\n    useCustomFieldValue(function () {\n      return props.modelValue;\n    });\n    return function () {\n      var _a;\n      var size = props.size,\n        loading = props.loading,\n        disabled = props.disabled,\n        activeColor = props.activeColor,\n        inactiveColor = props.inactiveColor;\n      var checked = isChecked();\n      var style = {\n        fontSize: addUnit(size),\n        backgroundColor: checked ? activeColor : inactiveColor\n      };\n      return _createVNode(\"div\", {\n        \"role\": \"switch\",\n        \"class\": bem({\n          on: checked,\n          loading: loading,\n          disabled: disabled\n        }),\n        \"style\": style,\n        \"tabindex\": disabled ? void 0 : 0,\n        \"aria-checked\": checked,\n        \"onClick\": onClick\n      }, [_createVNode(\"div\", {\n        \"class\": bem(\"node\")\n      }, [renderLoading()]), (_a = slots.background) == null ? void 0 : _a.call(slots)]);\n    };\n  }\n});\nexport { stdin_default as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}