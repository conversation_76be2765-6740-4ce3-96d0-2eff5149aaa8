{"ast": null, "code": "import { createElementVNode as _createElementVNode, createCommentVNode as _createCommentVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, toDisplayString as _toDisplayString, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, createBlock as _createBlock, createTextVNode as _createTextVNode, pushScopeId as _pushScopeId, popScopeId as _popScopeId } from \"vue\";\nvar _withScopeId = function _withScopeId(n) {\n  return _pushScopeId(\"data-v-07ce50d4\"), n = n(), _popScopeId(), n;\n};\nvar _hoisted_1 = {\n  class: \"home\"\n};\nvar _hoisted_2 = [\"src\"];\nvar _hoisted_3 = {\n  class: \"content\"\n};\nvar _hoisted_4 = {\n  class: \"zq\"\n};\nvar _hoisted_5 = {\n  class: \"text2\"\n};\nvar _hoisted_6 = {\n  class: \"text\",\n  style: {\n    \"font-size\": \"15px\"\n  }\n};\nvar _hoisted_7 = {\n  class: \"text3\"\n};\nvar _hoisted_8 = [\"src\"];\nvar _hoisted_9 = {\n  class: \"monney\"\n};\nvar _hoisted_10 = [\"src\"];\nvar _hoisted_11 = {\n  class: \"topBox\"\n};\nvar _hoisted_12 = [\"src\"];\nvar _hoisted_13 = {\n  class: \"right\"\n};\nvar _hoisted_14 = {\n  class: \"t\"\n};\nvar _hoisted_15 = {\n  class: \"content\"\n};\nvar _hoisted_16 = {\n  class: \"ftitle\",\n  style: {\n    \"font-weight\": \"bold\"\n  }\n};\nvar _hoisted_17 = {\n  class: \"n_nav\"\n};\nvar _hoisted_18 = {\n  class: \"imge\"\n};\nvar _hoisted_19 = [\"src\"];\nvar _hoisted_20 = {\n  class: \"text\",\n  style: {\n    \"color\": \"#fff\"\n  }\n};\nvar _hoisted_21 = {\n  class: \"imge1\"\n};\nvar _hoisted_22 = /*#__PURE__*/_withScopeId(function () {\n  return /*#__PURE__*/_createElementVNode(\"div\", {\n    style: {\n      \"height\": \"0.5rem\"\n    }\n  }, null, -1 /* HOISTED */);\n});\nvar _hoisted_23 = {\n  class: \"imge\"\n};\nvar _hoisted_24 = [\"src\"];\nvar _hoisted_25 = {\n  class: \"text\",\n  style: {\n    \"color\": \"#fff\"\n  }\n};\nvar _hoisted_26 = {\n  class: \"imge1\"\n};\nvar _hoisted_27 = /*#__PURE__*/_withScopeId(function () {\n  return /*#__PURE__*/_createElementVNode(\"div\", {\n    style: {\n      \"height\": \"0.5rem\"\n    }\n  }, null, -1 /* HOISTED */);\n});\nvar _hoisted_28 = {\n  class: \"imge\"\n};\nvar _hoisted_29 = [\"src\"];\nvar _hoisted_30 = {\n  class: \"text\",\n  style: {\n    \"color\": \"#fff\"\n  }\n};\nvar _hoisted_31 = {\n  class: \"imge1\"\n};\nvar _hoisted_32 = {\n  style: {\n    \"height\": \"30%\",\n    \"display\": \"flex\",\n    \"justify-content\": \"center\",\n    \"align-items\": \"center\",\n    \"color\": \"#fff\",\n    \"font-weight\": \"bold\",\n    \"word-break\": \"keep-all\",\n    \"white-space\": \"nowrap\"\n  }\n};\nvar _hoisted_33 = [\"src\"];\nvar _hoisted_34 = {\n  class: \"ftitle\",\n  style: {\n    \"font-weight\": \"bold\"\n  }\n};\nvar _hoisted_35 = {\n  class: \"hzhb\"\n};\nvar _hoisted_36 = [\"src\", \"onClick\"];\nvar _hoisted_37 = {\n  class: \"hzhb\"\n};\nvar _hoisted_38 = [\"src\", \"onClick\"];\nvar _hoisted_39 = {\n  class: \"lang_box\"\n};\nvar _hoisted_40 = {\n  class: \"lang_title\"\n};\nvar _hoisted_41 = {\n  class: \"content\"\n};\nvar _hoisted_42 = {\n  class: \"langs\"\n};\nvar _hoisted_43 = [\"innerHTML\"];\nvar _hoisted_44 = {\n  class: \"btn\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_van_nav_bar = _resolveComponent(\"van-nav-bar\");\n  var _component_van_swipe_item = _resolveComponent(\"van-swipe-item\");\n  var _component_van_swipe = _resolveComponent(\"van-swipe\");\n  var _component_vue3_seamless_scroll = _resolveComponent(\"vue3-seamless-scroll\");\n  var _component_van_icon = _resolveComponent(\"van-icon\");\n  var _component_van_button = _resolveComponent(\"van-button\");\n  var _component_van_dialog = _resolveComponent(\"van-dialog\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_van_nav_bar, {\n    style: {\n      \"width\": \"100%\"\n    }\n  }, {\n    left: _withCtx(function () {\n      return [_createElementVNode(\"img\", {\n        src: $setup.logo,\n        class: \"logo\",\n        alt: \"\",\n        style: {\n          \"width\": \"100%\",\n          \"height\": \"100%\"\n        }\n      }, null, 8 /* PROPS */, _hoisted_2)];\n    }),\n    right: _withCtx(function () {\n      return [_createCommentVNode(\" <img :src=\\\"$store.state.langImg\\\" class=\\\"lang\\\" height=\\\"18\\\" width=\\\"27\\\" alt=\\\"\\\"> \"), _createCommentVNode(\" <lang-vue></lang-vue> \")];\n    }),\n    _: 1 /* STABLE */\n  }), _createCommentVNode(\" <div class=\\\"top\\\">\\n        </div> \"), _createElementVNode(\"div\", _hoisted_3, [_createCommentVNode(\"  <div class=\\\"ftitle2\\\">\\n\\t\\t\\t  \\t<img :src=\\\"require('@/assets/images/home/<USER>')\\\" width=\\\"18\\\" height=\\\"18\\\" alt=\\\"\\\" >\\n\\t\\t\\t   \\n\\t\\t\\t  </div>\\n\\t\\t\\t  \"), _createElementVNode(\"div\", _hoisted_4, [_createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"div\", _hoisted_6, _toDisplayString(_ctx.$t('msg.get_monney')), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_7, [_createElementVNode(\"img\", {\n    src: require('@/assets/images/home/<USER>'),\n    width: \"25\",\n    height: \"25\",\n    alt: \"\"\n  }, null, 8 /* PROPS */, _hoisted_8), _createElementVNode(\"span\", _hoisted_9, _toDisplayString($setup.mInfo.yon3), 1 /* TEXT */)])])]), _createCommentVNode(\" banner图 \"), $setup.banner.length > 0 ? (_openBlock(), _createBlock(_component_van_swipe, {\n    key: 0,\n    class: \"my-swipe banner\",\n    autoplay: 3000,\n    \"indicator-color\": \"white\",\n    round: \"25\"\n  }, {\n    default: _withCtx(function () {\n      return [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.banner, function (item) {\n        return _openBlock(), _createBlock(_component_van_swipe_item, {\n          key: item.id,\n          style: {\n            \"border-radius\": \"1rem\"\n          }\n        }, {\n          default: _withCtx(function () {\n            return [_createElementVNode(\"img\", {\n              src: item.image,\n              alt: \"\",\n              class: \"img\",\n              style: {\n                \"border-radius\": \"1rem\"\n              }\n            }, null, 8 /* PROPS */, _hoisted_10)];\n          }),\n          _: 2 /* DYNAMIC */\n        }, 1024 /* DYNAMIC_SLOTS */);\n      }), 128 /* KEYED_FRAGMENT */))];\n    }),\n\n    _: 1 /* STABLE */\n  })) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 交易轮播 \"), _createElementVNode(\"div\", _hoisted_11, [_createElementVNode(\"img\", {\n    class: \"imgto\",\n    src: require('@/assets/images/home/<USER>'),\n    alt: \"\"\n  }, null, 8 /* PROPS */, _hoisted_12), _createVNode(_component_vue3_seamless_scroll, {\n    list: $setup.mInfo.list || [],\n    class: \"scroll\",\n    waitTime: 600,\n    step: 7,\n    delay: 2,\n    hover: \"\",\n    limitScrollNum: 3,\n    singleHeight: 90\n  }, {\n    default: _withCtx(function () {\n      return [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.mInfo.list || [], function (item, index) {\n        return _openBlock(), _createElementBlock(\"div\", {\n          class: \"item\",\n          key: index\n        }, [_createCommentVNode(\"   <span class=\\\"left\\\">{{ item.addtime }}</span> \"), _createElementVNode(\"span\", _hoisted_13, [_createElementVNode(\"span\", _hoisted_14, _toDisplayString(item.name.replace(/(.{0}).*(.{3})/, \"$1***$2\")) + \" \" + _toDisplayString(_ctx.$t('msg.sryj')) + \"：\" + _toDisplayString($setup.currency) + \" \" + _toDisplayString(item.today_income), 1 /* TEXT */), _createCommentVNode(\"   <span class=\\\"b\\\">{{item.name.replace(/(.{3}).*(.{3})/,\\\"$1******$2\\\")}}</span> \")])]);\n      }), 128 /* KEYED_FRAGMENT */))];\n    }),\n\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"list\"])]), _createCommentVNode(\" 内容 \"), _createElementVNode(\"div\", _hoisted_15, [_createCommentVNode(\" 开始赚钱 \"), _createCommentVNode(\"  <van-button type=\\\"primary\\\" class=\\\"kszq\\\" round block  @click=\\\"toRoute('/obj')\\\">{{$t('msg.kszq')}}</van-button> \"), _createCommentVNode(\" 次导航 \"), _createElementVNode(\"div\", _hoisted_16, _toDisplayString(_ctx.$t('msg.wdfw')), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_17, [_createElementVNode(\"div\", null, [_createElementVNode(\"div\", {\n    class: \"li1 nav\",\n    onClick: _cache[0] || (_cache[0] = function ($event) {\n      return $setup.toDetails(6, _ctx.$t('msg.bangz'));\n    }),\n    style: {\n      \"width\": \"100%\",\n      \"margin-bottom\": \"0\",\n      \"border-radius\": \"7px\",\n      \"background\": \"#000\"\n    }\n  }, [_createElementVNode(\"div\", _hoisted_18, [_createElementVNode(\"img\", {\n    src: require('@/assets/images/home/<USER>'),\n    width: \"12\",\n    height: \"12\",\n    alt: \"\",\n    class: \"li_img\"\n  }, null, 8 /* PROPS */, _hoisted_19)]), _createElementVNode(\"div\", _hoisted_20, _toDisplayString(_ctx.$t('msg.bangz')), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_21, [_createCommentVNode(\" <img :src=\\\"require('@/assets/images/home/<USER>')\\\" width=\\\"15\\\" height=\\\"15\\\" alt=\\\"\\\"\\n                                class=\\\"li_img\\\"> \"), _createVNode(_component_van_icon, {\n    color: \"#fff\",\n    width: \"15\",\n    height: \"15\",\n    name: \"arrow\"\n  })])]), _hoisted_22, _createElementVNode(\"div\", {\n    class: \"li2 nav\",\n    onClick: _cache[1] || (_cache[1] = function ($event) {\n      return $setup.toDown();\n    }),\n    style: {\n      \"width\": \"100%\",\n      \"margin-bottom\": \"0\",\n      \"border-radius\": \"7px\",\n      \"background\": \"#fe2c55\"\n    }\n  }, [_createElementVNode(\"div\", _hoisted_23, [_createElementVNode(\"img\", {\n    src: require('@/assets/images/home/<USER>'),\n    width: \"12\",\n    height: \"12\",\n    alt: \"\",\n    class: \"li_img\"\n  }, null, 8 /* PROPS */, _hoisted_24)]), _createElementVNode(\"div\", _hoisted_25, _toDisplayString(_ctx.$t('msg.appDown')), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_26, [_createVNode(_component_van_icon, {\n    color: \"#fff\",\n    width: \"15\",\n    height: \"15\",\n    name: \"arrow\"\n  })])]), _hoisted_27, _createElementVNode(\"div\", {\n    class: \"li1 nav\",\n    onClick: _cache[2] || (_cache[2] = function ($event) {\n      return $setup.toDetails(7, _ctx.$t('msg.about'));\n    }),\n    style: {\n      \"width\": \"100%\",\n      \"margin-bottom\": \"0\",\n      \"border-radius\": \"7px\",\n      \"background\": \"#000\"\n    }\n  }, [_createElementVNode(\"div\", _hoisted_28, [_createElementVNode(\"img\", {\n    src: require('@/assets/images/home/<USER>'),\n    width: \"12\",\n    height: \"12\",\n    alt: \"\",\n    class: \"li_img\"\n  }, null, 8 /* PROPS */, _hoisted_29)]), _createElementVNode(\"div\", _hoisted_30, _toDisplayString(_ctx.$t('msg.about')), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_31, [_createVNode(_component_van_icon, {\n    color: \"#fff\",\n    width: \"15\",\n    height: \"15\",\n    name: \"arrow\"\n  })])])]), _createElementVNode(\"div\", {\n    class: \"li nav servsen\",\n    onClick: _cache[3] || (_cache[3] = function ($event) {\n      return $setup.toRoute('/tel');\n    }),\n    style: {\n      \"display\": \"flex\",\n      \"flex-direction\": \"column\",\n      \"margin-bottom\": \"0rem\",\n      \"padding\": \"0\",\n      \"border-radius\": \"7px\"\n    }\n  }, [_createElementVNode(\"div\", _hoisted_32, _toDisplayString(_ctx.$t('msg.tel')), 1 /* TEXT */), _createElementVNode(\"img\", {\n    class: \"serverimg\",\n    src: require('@/assets/images/home/<USER>'),\n    alt: \"\"\n  }, null, 8 /* PROPS */, _hoisted_33)])]), _createCommentVNode(\" 次导航 \"), _createCommentVNode(\" <div class=\\\"earnings mt-2\\\" style=\\\"\\\">\\n                <div class=\\\"earnings_Info\\\">\\n                    <div class=\\\"vip_level ft-16\\\" v-if=\\\"userinfo?.tel\\\">\\n                        <div style=\\\"flex: 2 1 0px;\\\">{{userinfo?.tel}}</div>\\n                        <div style=\\\"flex: 1 1 0px; justify-content: center;\\\">{{userinfo?.invite_code}}</div>\\n                    </div>\\n                    <div class=\\\"balance mt-2 d-flex justify-between\\\">\\n                        <span >{{$t('msg.zhye')}}</span><span >{{$t('msg.djje')}}</span>\\n                    </div>\\n                    <div class=\\\"balance-val d-flex justify-between\\\">\\n                        <span >{{currency}}\\n                            <span class=\\\"mm\\\">{{monney}}</span>\\n                        </span>\\n                        <span >{{currency}}\\n                            <span class=\\\"mm\\\">{{mInfo.freeze_balance}}</span></span>\\n                    </div>\\n                    <div class=\\\"count-data\\\">\\n                        <div class=\\\"flex-full\\\">\\n                            <div >{{$t('msg.today_monney')}}</div>\\n                            <div >{{currency}}{{mInfo.yon1}}</div>\\n                        </div>\\n                        <div class=\\\"flex-full\\\">\\n                            <div >{{$t('msg.zrsy')}}</div>\\n                            <div >{{currency}} {{mInfo.Yesterdaysearnings}}</div>\\n                        </div>\\n                    </div>\\n                    <div class=\\\"count-data\\\">\\n                        <div class=\\\"flex-full\\\">\\n                            <div >{{$t('msg.get_monney')}}</div>\\n                            <div class=\\\"two\\\">{{currency}}{{mInfo.yon3}}</div>\\n                        </div>\\n                        <div class=\\\"flex-full\\\">\\n                            <div >{{$t('msg.tdsy')}}</div>\\n                            <div class=\\\"two\\\">{{currency}} {{mInfo.Teambenefits}}</div>\\n                        </div>\\n                    </div>\\n                </div>\\n            </div> \"), _createCommentVNode(\" banner图 \"), _createCommentVNode(\"  <van-row gutter=\\\"20\\\" class=\\\"hy_box\\\" v-if=\\\"hyList.length > 0\\\">\\n                <van-col span=\\\"12\\\" v-for=\\\"item in hyList\\\" :key=\\\"item.id\\\" >\\n                    <div class=\\\"box\\\" @click=\\\"addLevel(item)\\\">\\n                        <div class=\\\"t\\\" >\\n                            <img :src=\\\"item.pic\\\" class=\\\"img goods_img\\\" :id=\\\"'img'+item.id\\\" alt=\\\"\\\" :style=\\\"'max-height:'+ setHeight('img'+item.id)\\\">\\n                            <div class=\\\"ts\\\">\\n                                <span class=\\\"text\\\">{{item.name}}</span>\\n                                <van-button type=\\\"primary\\\" class=\\\"txlevel\\\">\\n                                    {{mInfo.level == item.level ? $t('msg.now_level') : mInfo.level < item.level ?  $t('msg.join') : ''}}\\n                                </van-button>\\n                            </div>\\n                        </div>\\n                        <div class=\\\"b\\\">\\n                            <div class=\\\"sub\\\">{{$t('msg.sjje')}}\\n                                <span class=\\\"span\\\">{{currency}}{{item.num}}</span>\\n                            </div>\\n                            <div class=\\\"sub\\\">{{$t('msg.yonj')}}\\n                                <span class=\\\"span\\\">{{((item.bili || 0)*100).toFixed(1)}}%</span>\\n                            </div>\\n                        </div>\\n                    </div>\\n                </van-col>\\n            </van-row> \"), _createCommentVNode(\" 会员收益 \"), _createCommentVNode(\" <div class=\\\"ftitle\\\">\\n                {{$t('msg.get_monney')}}\\n            </div> \"), _createCommentVNode(\" <div class=\\\"m_nav\\\">\\n                <div class=\\\"li\\\">\\n                    <div class=\\\"monney\\\">{{currency}}{{mInfo.yon1}}</div>\\n                    <div class=\\\"text\\\">{{$t('msg.today_monney')}}</div>\\n                </div>\\n                <div class=\\\"li\\\">\\n                    <div class=\\\"monney\\\">{{currency}}{{mInfo.yon2}}</div>\\n                    <div class=\\\"text\\\">{{$t('msg.today_yonj')}}</div>\\n                </div>\\n                <div class=\\\"li\\\">\\n                    <div class=\\\"monney\\\">{{currency}}{{mInfo.yon3}}</div>\\n                    <div class=\\\"text\\\">{{$t('msg.get_monney')}}</div>\\n                </div>\\n                <div class=\\\"li\\\">\\n                    <div class=\\\"monney\\\">{{currency}}{{mInfo.yon4}}</div>\\n                    <div class=\\\"text\\\">{{$t('msg.ylb')}}</div>\\n                </div>\\n            </div> \"), _createCommentVNode(\" 会员收益 \"), _createCommentVNode(\" 公司简介 \"), _createCommentVNode(\"    <div class=\\\"n_nav\\\">\\n                <div class=\\\"li nav\\\" @click=\\\"toDetails(2,$t('msg.gsjj'))\\\">\\n                    <img :src=\\\"require('@/assets/images/news/poster_1.png')\\\" class=\\\"img nav_img\\\" alt=\\\"\\\" >\\n                    <div class=\\\"text\\\">{{$t('msg.gsjj')}}</div>\\n                </div>\\n                <div class=\\\"li nav\\\" @click=\\\"toDetails(3,$t('msg.gzms'))\\\">\\n                    <img :src=\\\"require('@/assets/images/news/poster_2.png')\\\" class=\\\"img nav_img\\\" alt=\\\"\\\">\\n                    <div class=\\\"text\\\">{{$t('msg.gzms')}}</div>\\n                </div>\\n                <div class=\\\"li nav\\\" @click=\\\"toDetails(4,$t('msg.dlhz'))\\\">\\n                    <img :src=\\\"require('@/assets/images/news/poster_3.png')\\\" class=\\\"img nav_img\\\" alt=\\\"\\\">\\n                    <div class=\\\"text\\\">{{$t('msg.dlhz')}}</div>\\n                </div>\\n                <div class=\\\"li nav\\\" @click=\\\"toDetails(12,$t('msg.qyzz'))\\\">\\n                    <img :src=\\\"require('@/assets/images/news/poster_4.png')\\\" class=\\\"img nav_img\\\" alt=\\\"\\\">\\n                    <div class=\\\"text\\\">{{$t('msg.qyzz')}}</div>\\n                </div>\\n            </div> \"), _createCommentVNode(\" 会员收益 \"), _createElementVNode(\"div\", _hoisted_34, _toDisplayString(_ctx.$t('msg.hzhb')), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_35, [(_openBlock(), _createElementBlock(_Fragment, null, _renderList(6, function (num) {\n    return _createElementVNode(\"img\", {\n      src: require('@/assets/images/news/' + num + '.png'),\n      key: num,\n      class: \"img hzhb_img\",\n      alt: \"\",\n      onClick: function onClick($event) {\n        return $setup.goToPartner(num);\n      }\n    }, null, 8 /* PROPS */, _hoisted_36);\n  }), 64 /* STABLE_FRAGMENT */))]), _createElementVNode(\"div\", _hoisted_37, [(_openBlock(), _createElementBlock(_Fragment, null, _renderList(6, function (num) {\n    return _createElementVNode(\"img\", {\n      src: require('@/assets/images/news/img/img/' + num + '.png'),\n      key: num + 6,\n      class: \"img hzhb_img\",\n      alt: \"\",\n      onClick: function onClick($event) {\n        return $setup.goToPartner(num + 6);\n      }\n    }, null, 8 /* PROPS */, _hoisted_38);\n  }), 64 /* STABLE_FRAGMENT */))])]), _createVNode(_component_van_dialog, {\n    show: $setup.showA,\n    \"onUpdate:show\": _cache[5] || (_cache[5] = function ($event) {\n      return $setup.showA = $event;\n    }),\n    width: \"90%\",\n    showConfirmButton: false\n  }, {\n    default: _withCtx(function () {\n      return [_createElementVNode(\"div\", _hoisted_39, [_createElementVNode(\"div\", _hoisted_40, _toDisplayString(_ctx.$t('msg.System_notification')), 1 /* TEXT */), _createCommentVNode(\" <img :src=\\\"require('@/assets/images/register/lang_bg.png')\\\" class=\\\"lang_bg\\\" /> \"), _createElementVNode(\"div\", _hoisted_41, [_createCommentVNode(\" <img :src=\\\"require('@/assets/images/register/qiu.png')\\\" class=\\\"qiu\\\" /> \"), _createElementVNode(\"div\", _hoisted_42, [_createElementVNode(\"span\", {\n        class: \"li ctn\",\n        innerHTML: $setup.a_content\n      }, null, 8 /* PROPS */, _hoisted_43)]), _createElementVNode(\"div\", _hoisted_44, [_createVNode(_component_van_button, {\n        round: \"\",\n        block: \"\",\n        type: \"primary\",\n        onClick: _cache[4] || (_cache[4] = function ($event) {\n          return $setup.showA = false;\n        })\n      }, {\n        default: _withCtx(function () {\n          return [_createTextVNode(_toDisplayString(_ctx.$t('msg.yes')), 1 /* TEXT */)];\n        }),\n\n        _: 1 /* STABLE */\n      })])])])];\n    }),\n\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"show\"])]);\n}", "map": {"version": 3, "names": ["class", "style", "_createElementVNode", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_van_nav_bar", "left", "_withCtx", "src", "$setup", "logo", "alt", "right", "_createCommentVNode", "_hoisted_3", "_hoisted_4", "_hoisted_5", "_hoisted_6", "_toDisplayString", "_ctx", "$t", "_hoisted_7", "require", "width", "height", "_hoisted_9", "mInfo", "yon3", "banner", "length", "_createBlock", "_component_van_swipe", "autoplay", "round", "_Fragment", "_renderList", "item", "_component_van_swipe_item", "key", "id", "image", "_hoisted_11", "_component_vue3_seamless_scroll", "list", "waitTime", "step", "delay", "hover", "limitScrollNum", "singleHeight", "index", "_hoisted_13", "_hoisted_14", "name", "replace", "currency", "today_income", "_hoisted_15", "_hoisted_16", "_hoisted_17", "onClick", "_cache", "$event", "toDetails", "_hoisted_18", "_hoisted_20", "_hoisted_21", "_component_van_icon", "color", "_hoisted_22", "toDown", "_hoisted_23", "_hoisted_25", "_hoisted_26", "_hoisted_27", "_hoisted_28", "_hoisted_30", "_hoisted_31", "toRoute", "_hoisted_32", "_hoisted_34", "_hoisted_35", "num", "goTo<PERSON><PERSON>ner", "_hoisted_37", "_component_van_dialog", "show", "showA", "showConfirmButton", "_hoisted_39", "_hoisted_40", "_hoisted_41", "_hoisted_42", "innerHTML", "a_content", "_hoisted_44", "_component_van_button", "block", "type"], "sources": ["C:\\Users\\<USER>\\Downloads\\Telegram Desktop\\vue3_3.0\\vue3_3.0\\src\\views\\index\\home.vue"], "sourcesContent": ["<template>\n    <div class=\"home\">\n        <van-nav-bar style=\"width: 100%;\">\n            <template  #left>\n                <img :src=\"logo\" class=\"logo\"  alt=\"\" style=\"width: 100%;height: 100%;\">\n            </template>\n            <template #right>\n                <!-- <img :src=\"$store.state.langImg\" class=\"lang\" height=\"18\" width=\"27\" alt=\"\"> -->\n                <!-- <lang-vue></lang-vue> -->\n            </template>\n        </van-nav-bar>\n        <!-- <div class=\"top\">\n        </div> -->\n        <div class=\"content\">\n            <!--  <div class=\"ftitle2\">\n\t\t\t  \t<img :src=\"require('@/assets/images/home/<USER>')\" width=\"18\" height=\"18\" alt=\"\" >\n\t\t\t   \n\t\t\t  </div>\n\t\t\t  -->\n\n\n            <div class=\"zq\">\n                <div class=\"text2\">\n                    <div class=\"text\" style=\"font-size: 15px;\">{{ $t('msg.get_monney') }}</div>\n                </div>\n                <div class=\"text3\">\n                    <img :src=\"require('@/assets/images/home/<USER>')\" width=\"25\" height=\"25\" alt=\"\">\n                    <span class=\"monney\">{{ mInfo.yon3 }}</span>\n                </div>\n\n            </div>\n        </div>\n\n\n        <!-- banner图 -->\n        <van-swipe class=\"my-swipe banner\" :autoplay=\"3000\" indicator-color=\"white\" round=\"25\"  v-if=\"banner.length > 0\" >\n            <van-swipe-item v-for=\"item in banner\" :key=\"item.id\" style=\"border-radius: 1rem\">\n                <img :src=\"item.image\" alt=\"\" class=\"img\" style=\"border-radius: 1rem\">\n            </van-swipe-item>\n        </van-swipe>\n\n        <!-- 交易轮播 -->\n        <div class=\"topBox\">\n            <img class=\"imgto\" :src=\"require('@/assets/images/home/<USER>')\" alt=\"\">\n\n            <vue3-seamless-scroll :list=\"mInfo.list || []\" class=\"scroll\" :waitTime=\"600\" :step=\"7\" :delay=\"2\" hover\n                :limitScrollNum=\"3\" :singleHeight=\"90\">\n                <div class=\"item\" v-for=\"(item, index) in mInfo.list || []\" :key=\"index\">\n                    <!--   <span class=\"left\">{{ item.addtime }}</span> -->\n                    <span class=\"right\">\n                        <span class=\"t\">{{ item.name.replace(/(.{0}).*(.{3})/, \"$1***$2\") }} {{ $t('msg.sryj') }}：{{ currency }} {{\n                            item.today_income }}</span>\n                        <!--   <span class=\"b\">{{item.name.replace(/(.{3}).*(.{3})/,\"$1******$2\")}}</span> -->\n                    </span>\n                </div>\n            </vue3-seamless-scroll>\n        </div>\n\n        <!-- 内容 -->\n        <div class=\"content\">\n            <!-- 开始赚钱 -->\n\n            <!--  <van-button type=\"primary\" class=\"kszq\" round block  @click=\"toRoute('/obj')\">{{$t('msg.kszq')}}</van-button> -->\n            <!-- 次导航 -->\n            <div class=\"ftitle\" style=\"font-weight: bold;\">\n\n                {{ $t('msg.wdfw') }}\n            </div>\n            <div class=\"n_nav\">\n                <div>\n                    <div class=\"li1 nav\"  @click=\"toDetails(6, $t('msg.bangz'))\" style=\"width: 100%;margin-bottom: 0;border-radius: 7px;background: #000;\">\n                        <div class=\"imge\">\n                            <img :src=\"require('@/assets/images/home/<USER>')\" width=\"12\" height=\"12\" alt=\"\"\n                                class=\"li_img\">\n                        </div>\n                        <div class=\"text\" style=\"color: #fff;\">{{ $t('msg.bangz') }}</div>\n                        <div class=\"imge1\">\n                            <!-- <img :src=\"require('@/assets/images/home/<USER>')\" width=\"15\" height=\"15\" alt=\"\"\n                                class=\"li_img\"> -->\n                                <van-icon color=\"#fff\" width=\"15\" height=\"15\" name=\"arrow\" />\n                        </div>\n                    </div>\n                    <div style=\"height: 0.5rem;\"></div>\n\n\n                    <div class=\"li2 nav\" @click=\"toDown()\" style=\"width: 100%;margin-bottom: 0;border-radius: 7px;background: #fe2c55;\">\n                        <div class=\"imge\">\n                            <img :src=\"require('@/assets/images/home/<USER>')\" width=\"12\" height=\"12\" alt=\"\" class=\"li_img\">\n                        </div>\n                        <div class=\"text\" style=\"color: #fff;\">{{ $t('msg.appDown') }}</div>\n                        <div class=\"imge1\">\n                            <van-icon color=\"#fff\" width=\"15\" height=\"15\" name=\"arrow\" />\n                        </div>\n                    </div>\n\n                \n\n                    <div style=\"height: 0.5rem;\"></div>\n                    <div class=\"li1 nav\"  @click=\"toDetails(7, $t('msg.about'))\" style=\"width: 100%;margin-bottom:0;border-radius: 7px;background: #000;\">\n                        <div class=\"imge\">\n                            <img :src=\"require('@/assets/images/home/<USER>')\" width=\"12\" height=\"12\" alt=\"\"\n                                class=\"li_img\">\n                        </div>\n                        <div class=\"text\" style=\"color: #fff;\">{{ $t('msg.about') }}</div>\n                        <div class=\"imge1\">\n                            <van-icon color=\"#fff\" width=\"15\" height=\"15\" name=\"arrow\" />\n                        </div>\n                    </div>\n\n                </div>\n\n                <div class=\"li nav servsen\" @click=\"toRoute('/tel')\" style=\"display: flex;flex-direction: column;margin-bottom: 0rem;padding:0;border-radius: 7px;\">\n                    <div style=\"height: 30%;display: flex;justify-content: center;align-items: center;color: #fff;font-weight: bold;word-break:keep-all;white-space:nowrap;\">{{ $t('msg.tel') }}  </div>\n                    <img class=\"serverimg\" :src=\"require('@/assets/images/home/<USER>')\" alt=\"\">\n\n                </div>\n\n\n            </div>\n\n           \n            <!-- 次导航 -->\n            <!-- <div class=\"earnings mt-2\" style=\"\">\n                <div class=\"earnings_Info\">\n                    <div class=\"vip_level ft-16\" v-if=\"userinfo?.tel\">\n                        <div style=\"flex: 2 1 0px;\">{{userinfo?.tel}}</div>\n                        <div style=\"flex: 1 1 0px; justify-content: center;\">{{userinfo?.invite_code}}</div>\n                    </div>\n                    <div class=\"balance mt-2 d-flex justify-between\">\n                        <span >{{$t('msg.zhye')}}</span><span >{{$t('msg.djje')}}</span>\n                    </div>\n                    <div class=\"balance-val d-flex justify-between\">\n                        <span >{{currency}}\n                            <span class=\"mm\">{{monney}}</span>\n                        </span>\n                        <span >{{currency}}\n                            <span class=\"mm\">{{mInfo.freeze_balance}}</span></span>\n                    </div>\n                    <div class=\"count-data\">\n                        <div class=\"flex-full\">\n                            <div >{{$t('msg.today_monney')}}</div>\n                            <div >{{currency}}{{mInfo.yon1}}</div>\n                        </div>\n                        <div class=\"flex-full\">\n                            <div >{{$t('msg.zrsy')}}</div>\n                            <div >{{currency}} {{mInfo.Yesterdaysearnings}}</div>\n                        </div>\n                    </div>\n                    <div class=\"count-data\">\n                        <div class=\"flex-full\">\n                            <div >{{$t('msg.get_monney')}}</div>\n                            <div class=\"two\">{{currency}}{{mInfo.yon3}}</div>\n                        </div>\n                        <div class=\"flex-full\">\n                            <div >{{$t('msg.tdsy')}}</div>\n                            <div class=\"two\">{{currency}} {{mInfo.Teambenefits}}</div>\n                        </div>\n                    </div>\n                </div>\n            </div> -->\n            <!-- banner图 -->\n            <!--  <van-row gutter=\"20\" class=\"hy_box\" v-if=\"hyList.length > 0\">\n                <van-col span=\"12\" v-for=\"item in hyList\" :key=\"item.id\" >\n                    <div class=\"box\" @click=\"addLevel(item)\">\n                        <div class=\"t\" >\n                            <img :src=\"item.pic\" class=\"img goods_img\" :id=\"'img'+item.id\" alt=\"\" :style=\"'max-height:'+ setHeight('img'+item.id)\">\n                            <div class=\"ts\">\n                                <span class=\"text\">{{item.name}}</span>\n                                <van-button type=\"primary\" class=\"txlevel\">\n                                    {{mInfo.level == item.level ? $t('msg.now_level') : mInfo.level < item.level ?  $t('msg.join') : ''}}\n                                </van-button>\n                            </div>\n                        </div>\n                        <div class=\"b\">\n                            <div class=\"sub\">{{$t('msg.sjje')}}\n                                <span class=\"span\">{{currency}}{{item.num}}</span>\n                            </div>\n                            <div class=\"sub\">{{$t('msg.yonj')}}\n                                <span class=\"span\">{{((item.bili || 0)*100).toFixed(1)}}%</span>\n                            </div>\n                        </div>\n                    </div>\n                </van-col>\n            </van-row> -->\n            <!-- 会员收益 -->\n            <!-- <div class=\"ftitle\">\n                {{$t('msg.get_monney')}}\n            </div> -->\n            <!-- <div class=\"m_nav\">\n                <div class=\"li\">\n                    <div class=\"monney\">{{currency}}{{mInfo.yon1}}</div>\n                    <div class=\"text\">{{$t('msg.today_monney')}}</div>\n                </div>\n                <div class=\"li\">\n                    <div class=\"monney\">{{currency}}{{mInfo.yon2}}</div>\n                    <div class=\"text\">{{$t('msg.today_yonj')}}</div>\n                </div>\n                <div class=\"li\">\n                    <div class=\"monney\">{{currency}}{{mInfo.yon3}}</div>\n                    <div class=\"text\">{{$t('msg.get_monney')}}</div>\n                </div>\n                <div class=\"li\">\n                    <div class=\"monney\">{{currency}}{{mInfo.yon4}}</div>\n                    <div class=\"text\">{{$t('msg.ylb')}}</div>\n                </div>\n            </div> -->\n            <!-- 会员收益 -->\n\n\n\n            <!-- 公司简介 -->\n            <!--    <div class=\"n_nav\">\n                <div class=\"li nav\" @click=\"toDetails(2,$t('msg.gsjj'))\">\n                    <img :src=\"require('@/assets/images/news/poster_1.png')\" class=\"img nav_img\" alt=\"\" >\n                    <div class=\"text\">{{$t('msg.gsjj')}}</div>\n                </div>\n                <div class=\"li nav\" @click=\"toDetails(3,$t('msg.gzms'))\">\n                    <img :src=\"require('@/assets/images/news/poster_2.png')\" class=\"img nav_img\" alt=\"\">\n                    <div class=\"text\">{{$t('msg.gzms')}}</div>\n                </div>\n                <div class=\"li nav\" @click=\"toDetails(4,$t('msg.dlhz'))\">\n                    <img :src=\"require('@/assets/images/news/poster_3.png')\" class=\"img nav_img\" alt=\"\">\n                    <div class=\"text\">{{$t('msg.dlhz')}}</div>\n                </div>\n                <div class=\"li nav\" @click=\"toDetails(12,$t('msg.qyzz'))\">\n                    <img :src=\"require('@/assets/images/news/poster_4.png')\" class=\"img nav_img\" alt=\"\">\n                    <div class=\"text\">{{$t('msg.qyzz')}}</div>\n                </div>\n            </div> -->\n            <!-- 会员收益 -->\n            <div class=\"ftitle\" style=\"font-weight: bold;\">\n                {{ $t('msg.hzhb') }}\n            </div>\n            <div class=\"hzhb\">\n                <img :src=\"require('@/assets/images/news/' + num + '.png')\" v-for=\"num in 6\" :key=\"num\" class=\"img hzhb_img\"\n                    alt=\"\" @click=\"goToPartner(num)\">\n\n            </div>\n            <div class=\"hzhb\">\n                <img :src=\"require('@/assets/images/news/img/img/' + num + '.png')\" v-for=\"num in 6\" :key=\"num+6\"\n                    class=\"img hzhb_img\" alt=\"\" @click=\"goToPartner(num+6)\">\n\n            </div>\n        </div>\n        <van-dialog v-model:show=\"showA\" width=\"90%\" :showConfirmButton=\"false\">\n            <div class=\"lang_box\">\n                <div class=\"lang_title\">{{ $t('msg.System_notification') }}</div>\n                <!-- <img :src=\"require('@/assets/images/register/lang_bg.png')\" class=\"lang_bg\" /> -->\n                <div class=\"content\">\n                    <!-- <img :src=\"require('@/assets/images/register/qiu.png')\" class=\"qiu\" /> -->\n                    <div class=\"langs\">\n                        <span class=\"li ctn\" v-html=\"a_content\"></span>\n                    </div>\n                    <div class=\"btn\">\n                        <van-button round block type=\"primary\" @click=\"showA = false\">\n                            {{ $t('msg.yes') }}\n                        </van-button>\n                    </div>\n                </div>\n            </div>\n        </van-dialog>\n    </div>\n</template>\n<script>\nimport { ref, getCurrentInstance, reactive } from 'vue';\nimport store from '@/store/index'\nimport { useI18n } from 'vue-i18n'\nimport { useRouter } from 'vue-router';\nimport { getHomeData, get_level_list, getdetailbyid } from '@/api/home/<USER>'\nimport langVue from '@/components/lang.vue'\nexport default {\n    components: { langVue },\n    setup() {\n        const { push } = useRouter();\n        // 语言切换\n        const { locale, t } = useI18n()\n        const a_content = ref('')\n        const showA = ref(false)\n        // 设置logo，需要网络logo就注释这个\n        // const logo = ref(store.state.baseInfo?.site_icon)\n        const logo = require('@/assets/images/home/<USER>')\n        const userinfo = ref(store.state.userinfo)\n        const currency = ref(store.state.baseInfo?.currency)\n        const xtTime = ref(store.state.xtTime)\n        const app_name = ref(store.state.baseInfo?.app_name)\n        const noicetop = require('@/assets/images/home/<USER>')\n        if (!xtTime.value && userinfo.value?.tel) {\n            getdetailbyid(1).then(res => {\n                a_content.value = res.data?.content\n                showA.value = true\n                store.dispatch('changextTime', 'true')\n            })\n\n        }\n\n\n        store.dispatch('changefooCheck', 'home')\n\n        const setHeight = (ref) => {\n            const el = document.getElementById(ref)\n            return el?.offsetWidth ? el?.offsetWidth + 'px' : ''\n        }\n        // banner图轮播\n        const banner = ref([])\n        // banner图轮播\n        const hyList = ref([])\n        // 主info\n        const monney = ref('')\n        const mInfo = ref({})\n        const baseInfo = ref(store.state.baseInfo)\n        getHomeData().then(res => {\n            if (res.code === 0) {\n                banner.value = res.data.banner\n                monney.value = res.data.balance\n                mInfo.value = { ...res.data }\n            }\n        })\n        if (userinfo.value?.tel) {\n            get_level_list().then(res => {\n                console.log(res)\n                if (res.code === 0) {\n                    hyList.value = res.data\n                }\n            })\n        }\n        const toDetails = (id, title) => {\n            push('/content?id=' + id + '&title=' + title)\n        }\n        const toRoute = (path, param) => {\n            if (path) {\n                push(path + (param ? '?param=' + param : ''))\n            }\n        }\n        const addLevel = (row) => {\n            if (row.level <= mInfo.value?.level) {\n                push('/obj')\n            } else {\n                push('/addlevel?vip=' + row.id)\n            }\n        }\n        const toDown = () => {\n            console.log(baseInfo.value.app_url)\n            if (baseInfo.value.app_url) {\n                window.location.href = baseInfo.value.app_url\n            }\n        }\n        \n        // 合作伙伴链接\n        const partnerLinks = {\n            1: 'https://www.binance.com', // 币安\n            2: 'https://www.okx.com', // 欧易\n            3: 'https://www.obey.com', // obey\n            4: 'https://www.amazon.com', // Amazon\n            5: 'https://www.alibaba.com', // 国际阿里巴巴\n            6: 'https://www.bestbuy.com', // bestbuy\n            7: 'https://www.shopify.com', // shopify\n            8: 'https://www.walmart.com', // walmart\n            9: 'https://www.groupon.com', // GROUPON\n            10: 'https://www.homedepot.com', // thehomedepot\n            11: 'https://www.zalando.com', // zalando\n            12: 'https://www.lotte.com' // lotte\n        }\n        \n        // 跳转到合作伙伴网站\n        const goToPartner = (num) => {\n            const url = partnerLinks[num];\n            if (url) {\n                window.open(url, '_blank');\n            }\n        }\n         \n        return { \n            banner, monney, hyList, mInfo, currency, toRoute, addLevel, toDetails, \n            a_content, showA, logo, userinfo, setHeight, app_name, toDown, goToPartner \n        }\n    }\n}\n</script>\n<style lang=\"scss\" scoped>\n.serverimg {\n    width: 96%;\n    height: 57%;\n\n}\n\n.servsen {}\n\n.logo {\n    width: 745px\n}\n\n.scrolltop {\n    width: 500px\n}\n\n.topBox {\n    position: relative\n}\n\n.imgto {\n    width: 35px !important;\n    height: 35px !important;\n    position: absolute !important;\n    top: 20px;\n    left: 62px;\n}\n\n@import '@/styles/theme.scss';\n\n.home {\n    position: relative;\n    width: 100vw;\n    overflow-x: hidden;\n    overflow-y: auto;\n    display: block !important;\n    // background-image: url('~@/assets/images/home/<USER>');\n\n    background-repeat: no-repeat;\n    background-size: 100% 100%;\n    height: 100%;\n    min-height: 100vh;\n    width: 100%;\n    padding-bottom: 95px;\n    background-color: #fff;\n\n    :deep(.van-nav-bar) {\n        // position: sticky;\n        // top: 0;\n        // background-color: #d4dff5;\n        \n        color: #333;\n        padding: 0px 0;\n\n        \n        .van-nav-bar__left {\n            width: 100%;\n            padding: 0;\n\n            .van-icon {\n                color: #fff;\n            }\n        }\n\n        .van-nav-bar__title {\n            color: #333;\n            font-weight: 600;\n            font-size: 28px;\n        }\n    }\n\n    .top {\n        height: 62px;\n        position: absolute;\n        top: 25px;\n        left: 0;\n        width: 100%;\n        display: flex;\n        justify-content: space-between;\n        padding: 0 25px;\n        z-index: 888;\n\n        .lang {\n            height: 30px;\n        }\n    }\n\n    .lang_box {\n        width: 100%;\n        position: relative;\n        padding-top: 60px;\n\n        .lang_title {\n            margin-bottom: 40px;\n        }\n\n        .lang_bg {\n            width: 100%;\n            position: absolute;\n            top: 0;\n            left: 0;\n        }\n\n        .content {\n            position: relative;\n            z-index: 1;\n            text-align: center;\n\n            .qiu {\n                width: 175px;\n                border-radius: 50%;\n                box-shadow: $shadow;\n                margin-bottom: 6px;\n            }\n\n            .langs {\n                margin-bottom: 15px;\n\n                .li {\n                    padding: 24px;\n                    display: block;\n                    text-align: left;\n                    margin-bottom: 10px;\n                    max-height: 500px;\n                    overflow: auto;\n\n                    &.ctn {\n                        padding: 24px;\n                    }\n\n                    &.check {\n                        box-shadow: $shadow;\n                    }\n\n                    .img {\n                        width: 80px;\n                        margin-right: 34px;\n                        vertical-align: middle;\n                    }\n\n                    .text {\n                        font-size: 26px;\n                        color: $textColor;\n                    }\n                }\n            }\n\n            .btn {\n                padding: 50px 54px 50px;\n            }\n        }\n    }\n\n    .hy_box {\n        width: 100%;\n        color: #333;\n        overflow: hidden;\n        position: relative;\n\n        .box {\n            // background-image: url('~@/assets/images/home/<USER>');\n            // background-size: 100% 100%;\n            // border-radius: 10px;\n            background-color: #fff;\n            border-radius: 12px;\n            margin-bottom: 20PX;\n            padding: 24px;\n        }\n\n        .t {\n            margin-bottom: 18px;\n            // height: 200px;\n            display: flex;\n            width: 100%;\n            position: relative;\n\n            .img {\n                width: 100%;\n            }\n\n            .ts {\n                width: 100%;\n                position: absolute;\n                top: 0;\n                left: 0;\n                display: flex;\n                justify-content: space-between;\n            }\n\n            .text {\n                display: inline-block;\n                border-radius: 30PX;\n                text-align: center;\n                vertical-align: middle;\n                padding: 0 15px;\n                border-radius: 15px;\n                font-weight: 700;\n                background: red;\n                color: #fff;\n                font-size: 27px;\n                height: 25PX;\n                line-height: 25PX;\n            }\n\n            .txlevel {\n                border: none;\n                background-color: initial;\n                color: red;\n                line-height: 1;\n                height: auto;\n                text-align: right;\n                width: auto;\n                font-size: 0.8rem;\n                font-weight: 600;\n\n                .van-button__content {\n                    justify-content: right;\n                }\n            }\n        }\n\n        .b {\n            font-size: 18px;\n            display: flex;\n\n            .sub {\n                color: orange;\n                font-size: 0.5rem;\n                font-weight: 600;\n\n                &:first-child {\n                    flex: 1;\n                }\n\n                &:last-child {\n                    width: 60PX;\n                    text-align: right;\n\n                    .span {\n                        width: 100%;\n                        height: 40px;\n                        line-height: 40px;\n                        font-size: 36px;\n                        background-color: green;\n                        color: #fff;\n                        text-align: center;\n                    }\n                }\n\n                .span {\n                    display: block;\n                    font-size: 24px;\n                    margin-top: 15px;\n                }\n            }\n        }\n    }\n\n    .my-swipe {\n        .van-swipe-item {\n            padding: 0 30px;\n            overflow: hidden;\n        }\n\n        .img {\n            width: 100%;\n            height: 250px;\n            border-radius: 0px;\n        }\n    }\n\n    .content {\n        padding: 0 30px;\n        text-align: left;\n\n        .zq {\n            height: 70px;\n            margin-bottom: 30px;\n            border-radius: 20px;\n            display: flex;\n            align-items: center;\n            justify-content: space-between;\n            padding: 0 30px;\n            background-color: #fe2c55;\n            font-size: 30px;\n            // margin-top: 3.333333vw;\n            box-shadow: $shadow;\n            position: relative;\n            overflow: hidden;\n            // padding: 3.333333vw 1.333333vw;\n            display: flex;\n\n\n            .text2 {\n                justify-content: center;\n                display: flex;\n                flex-direction: column;\n                align-items: center;\n                flex-direction: row;\n                padding: 0 10px;\n                height: 45px;\n                // background: rgba(18,163,192,.32);\n                border-radius: 6px;\n                margin-right: 2.933333vw;\n            }\n\n            .text3 {\n                display: flex;\n                justify-content: center;\n                align-items: center;\n\n                .monney {\n                    font-size: 4.666667vw;\n                    background-image: -webkit-linear-gradient(left, #a570fb, #2620ce);\n                    -webkit-background-clip: text;\n                    -webkit-text-fill-color: #fff;\n                    font-weight: 600;\n                    padding: 0px 3.333333vw;\n                }\n\n                img {\n                    height: 30px;\n                    width: 30px;\n                }\n\n            }\n\n            .text {\n                text-align: center;\n                white-space: nowrap;\n                font-size: 12px;\n                font-family: PingFangSC-Regular, PingFang SC;\n\n                color: #fff;\n\n                font-weight: 600;\n\n            }\n\n            &::after {\n                position: absolute;\n                content: '';\n                height: 8px;\n                width: 100%;\n                left: 0;\n                bottom: 0;\n                //background-image:linear-gradient(90deg,#a570fb,#2620ce)\n            }\n        }\n\n        .kszq {\n            margin-top: 40px;\n            font-size: 44px;\n            height: 88px;\n            line-height: 88px;\n        }\n    }\n\n    .n_nav {\n        display: flex;\n        justify-content: space-between;\n        margin-top: 40px;\n        flex-wrap: nowrap;\n\n        &.jj {\n            margin: 20px 0 60px;\n            padding: 0 38px;\n\n            .li {\n                .img {\n                    width: 40px;\n                    margin-bottom: 8px;\n                }\n\n                .text {\n                    font-size: 18px;\n                    color: $sub_theme;\n                }\n            }\n        }\n\n        .li {\n            text-align: center;\n\n            &.nav {\n                // background-image: url('~@/assets/images/home/<USER>');\n                background-color: #fe2c55 !important;\n                // background-size: 100% 100%;\n                width: 48%;\n                text-align: left;\n                margin-bottom: -9.8vw;\n                padding: 24px 0 24px 24px;\n                border-radius: 36px;\n                background-color: #fe2c55;\n                display: flex;\n\n                .text {\n                    margin-left: 15px;\n                    display: flex;\n                    flex-direction: column;\n                    justify-content: space-around;\n                    font-size: 26px;\n                }\n\n                .imge {\n                    width: 40PX;\n                    display: flex;\n                    flex-direction: column;\n                    justify-content: center;\n                }\n\n                .img {\n                    width: 40PX;\n                }\n            }\n\n            .img {\n                width: 106px;\n                vertical-align: middle;\n            }\n\n            .text {\n                // white-space: nowrap;\n                font-size: 24px;\n                color: #333;\n            }\n        }\n\n        .li1 {\n            text-align: center;\n\n            &.nav {\n                width: 45%;\n                text-align: left;\n                margin-bottom: 15.2vw;\n                padding: 2.2vw 0px 2.2vw 3.2vw;\n                border-radius: 24px;\n                background-color: #fce6e7;\n                display: flex;\n\n                .text {\n                    margin-left: 15px;\n                    display: flex;\n                    flex-direction: column;\n                    justify-content: space-around;\n                    font-size: 26px;\n                }\n\n                .imge {\n                    width: 30PX;\n                    display: flex;\n                    flex-direction: column;\n                    justify-content: center;\n                }\n\n                .imge1 {\n                    width: 30PX;\n                    margin-left: 11vw;\n                    display: flex;\n                    flex-direction: column;\n                    justify-content: center;\n\n                    img {\n                        width: 25px;\n                        height: 25px;\n                    }\n                }\n\n                .img {\n                    width: 40PX;\n                }\n            }\n\n            .img {\n                width: 106px;\n                vertical-align: middle;\n            }\n\n            .text {\n                // white-space: nowrap;\n                font-size: 24px;\n                color: #333;\n            }\n        }\n\n        .li2 {\n            text-align: center;\n\n            &.nav {\n                width: 45%;\n                text-align: left;\n                margin-bottom: 1.2vw;\n                padding: 2.2vw 0px 2.2vw 3.2vw;\n                border-radius: 24px;\n                background-color: #f6ecca;\n                display: flex;\n\n                .text {\n                    margin-left: 15px;\n                    display: flex;\n                    flex-direction: column;\n                    justify-content: space-around;\n                    font-size: 26px;\n\n                }\n\n                .imge {\n                    width: 30PX;\n                    display: flex;\n                    flex-direction: column;\n                    justify-content: center;\n                }\n\n                .imge1 {\n                    width: 30PX;\n                    margin-left: 11vw;\n                    display: flex;\n                    flex-direction: column;\n                    justify-content: center;\n\n                    img {\n                        width: 25px;\n                        height: 25px;\n                    }\n                }\n\n                .img {\n                    width: 40PX;\n                }\n            }\n\n            .img {\n                width: 106px;\n                vertical-align: middle;\n            }\n\n            .text {\n                // white-space: nowrap;\n                font-size: 24px;\n                color: #333;\n            }\n        }\n    }\n\n    .ftitle {\n        height: 4.533333vw;\n        line-height: 4.533333vw;\n        color: #000000;\n        margin: 3.333333vw 0;\n        white-space: nowrap;\n        font-size: 4.026667vw;\n        font-weight: 500;\n        // &::before{\n        //     content: '';\n        //     display: inline-block;\n        //     height: 100%;\n        //     width: 10px;\n        //     margin-right: 12px;\n        //     background-color: $theme;\n        //     vertical-align: middle;\n        // }\n    }\n\n    .ftitle2 {\n        height: 34px;\n        line-height: 21.533333vw;\n        font-size: 30px;\n        color: #333;\n        margin: 0px 0;\n        white-space: nowrap;\n        padding: 0px 1.333333vw;\n\n    }\n\n    .earnings {\n        background: url('~@/assets/images/news/balanceBG.png') no-repeat;\n        background-size: 100% 100%;\n        padding: 24px;\n        margin-bottom: 24px;\n\n        .vip_level {\n            height: 30px;\n            display: flex;\n            box-sizing: border-box;\n            font-size: 20px;\n            color: #333;\n\n            &>div {\n                flex: 3;\n                display: flex;\n                justify-content: space-between;\n                line-height: 30px;\n                padding-left: 5px;\n                font-size: 30px;\n                // font-weight: 500;\n                color: #000;\n\n                &:first-child {\n                    border-right: 1px solid #adadad;\n                    padding-right: 5px;\n                    padding-left: 0;\n                }\n            }\n        }\n\n        .balance {\n            margin: 20px 0;\n            font-size: 30px;\n            // font-weight: 500;\n            color: #000;\n            display: flex;\n            justify-content: space-between;\n        }\n\n        .balance-val {\n            margin: 10px 0 15px;\n            font-size: 50px;\n            font-family: PingFangSC-Semibold, PingFang SC;\n            font-weight: 600;\n            color: #000;\n            display: flex;\n            justify-content: space-between;\n\n            span {\n\n                // width: 60PX;\n                .mm {\n                    display: block;\n                }\n            }\n        }\n\n        .count-data {\n            display: flex;\n            margin-top: 15px;\n\n            .flex-full {\n                flex: 1;\n                color: #000;\n                font-size: 24px;\n\n                .two {\n                    display: block;\n                    font-weight: 600;\n                }\n            }\n        }\n    }\n\n    .m_nav {\n        display: flex;\n        flex-wrap: wrap;\n        justify-content: space-between;\n\n        .li {\n            width: 327px;\n            margin-bottom: 36px;\n            box-shadow: $shadow;\n            display: flex;\n            flex-direction: column;\n            justify-content: center;\n            font-size: 28px;\n            color: $textColor;\n            padding: 44px 0;\n            text-align: center;\n            border-radius: 10px;\n\n            .monney {\n                font-size: 30px;\n                background-image: -webkit-linear-gradient(left, #856cf0, #a35df1);\n                -webkit-background-clip: text;\n                -webkit-text-fill-color: transparent;\n                font-weight: 600;\n                margin-bottom: 15px;\n            }\n        }\n    }\n\n    .scroll {\n        overflow: hidden;\n        height: 33PX;\n        width: 92%;\n\n        height: 70px;\n        background-color: #e6e6e6;\n        border-radius: 10px;\n\n\n        padding: 0 10px;\n\n        box-shadow: 0 2px 10px 0 rgba(126, 148, 194, .1);\n        line-height: 28px;\n        border-radius: 9px;\n        margin: 30px auto;\n        overflow: hidden;\n\n        // display: flex;\n        .item {\n            width: 100%;\n            height: 26PX;\n            margin-top: 19PX;\n            // background-image: url('@/assets/images/home/<USER>');\n            background-size: 100% 100%;\n            display: flex;\n            // justify-content: space-between;\n            padding: 14PX 20px 12PX 0;\n            // box-shadow: $shadow;\n\n            .left {\n                line-height: 50PX;\n                padding: 0 50px;\n                font-size: 26px;\n                color: #333;\n            }\n\n            .right {\n                display: flex;\n                flex-direction: column;\n                justify-content: center;\n                padding-left: 20px;\n\n                //border-left: 15px solid #eee;\n                .t {\n                    color: #333;\n                    font-size: 26px;\n                    margin-bottom: 35px;\n                    padding: 0px 7.333333vw;\n                }\n\n                .b {\n                    color: #000000;\n                    font-size: 24px;\n                }\n            }\n        }\n    }\n\n    .hzhb {\n        display: flex;\n        justify-content: space-between;\n        flex-wrap: wrap;\n\n        .img {\n            width: 2.6rem;\n            height: 2.6rem;\n            box-shadow: $shadow;\n            border-radius: 10px;\n            margin-bottom: 30px;\n            cursor: pointer;\n            transition: all 0.3s ease;\n            \n            &:hover {\n                transform: scale(1.05);\n                box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);\n            }\n        }\n    }\n}\n\n.goods_img {\n    width: 100%;\n    height: 7rem;\n}\n\n.li_img {\n    width: 1.5rem;\n    height: 1.5rem;\n}\n\n.nav_img {\n    width: 2.2rem;\n    height: 2.2rem;\n}\n\n.hzhb_img {\n\n    height: 3.2rem;\n}</style>"], "mappings": ";;;;;EACSA,KAAK,EAAC;AAAM;;;EAYRA,KAAK,EAAC;AAAS;;EAQXA,KAAK,EAAC;AAAI;;EACNA,KAAK,EAAC;AAAO;;EACTA,KAAK,EAAC,MAAM;EAACC,KAAwB,EAAxB;IAAA;EAAA;;;EAEjBD,KAAK,EAAC;AAAO;;;EAERA,KAAK,EAAC;AAAQ;;;EAe3BA,KAAK,EAAC;AAAQ;;;EAODA,KAAK,EAAC;AAAO;;EACTA,KAAK,EAAC;AAAG;;EAS1BA,KAAK,EAAC;AAAS;;EAKXA,KAAK,EAAC,QAAQ;EAACC,KAA0B,EAA1B;IAAA;EAAA;;;EAIfD,KAAK,EAAC;AAAO;;EAGDA,KAAK,EAAC;AAAM;;;EAIZA,KAAK,EAAC,MAAM;EAACC,KAAoB,EAApB;IAAA;EAAA;;;EACbD,KAAK,EAAC;AAAO;;sBAMtBE,mBAAA,CAAmC;IAA9BD,KAAuB,EAAvB;MAAA;IAAA;EAAuB;AAAA;;EAInBD,KAAK,EAAC;AAAM;;;EAGZA,KAAK,EAAC,MAAM;EAACC,KAAoB,EAApB;IAAA;EAAA;;;EACbD,KAAK,EAAC;AAAO;;sBAOtBE,mBAAA,CAAmC;IAA9BD,KAAuB,EAAvB;MAAA;IAAA;EAAuB;AAAA;;EAEnBD,KAAK,EAAC;AAAM;;;EAIZA,KAAK,EAAC,MAAM;EAACC,KAAoB,EAApB;IAAA;EAAA;;;EACbD,KAAK,EAAC;AAAO;;EAQjBC,KAAmJ,EAAnJ;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;EAAA;AAAmJ;;;EAsH3JD,KAAK,EAAC,QAAQ;EAACC,KAA0B,EAA1B;IAAA;EAAA;;;EAGfD,KAAK,EAAC;AAAM;;;EAKZA,KAAK,EAAC;AAAM;;;EAOZA,KAAK,EAAC;AAAU;;EACZA,KAAK,EAAC;AAAY;;EAElBA,KAAK,EAAC;AAAS;;EAEXA,KAAK,EAAC;AAAO;;;EAGbA,KAAK,EAAC;AAAK;;;;;;;;;uBA5PhCG,mBAAA,CAoQM,OApQNC,UAoQM,GAnQFC,YAAA,CAQcC,sBAAA;IARDL,KAAoB,EAApB;MAAA;IAAA;EAAoB;IACjBM,IAAI,EAAAC,QAAA,CACZ;MAAA,OAAwE,CAAxEN,mBAAA,CAAwE;QAAlEO,GAAG,EAAEC,MAAA,CAAAC,IAAI;QAAEX,KAAK,EAAC,MAAM;QAAEY,GAAG,EAAC,EAAE;QAACX,KAAiC,EAAjC;UAAA;UAAA;QAAA;;;IAE/BY,KAAK,EAAAL,QAAA,CACZ;MAAA,OAAqF,CAArFM,mBAAA,4FAAqF,EACrFA,mBAAA,2BAA8B,C;;;MAGtCA,mBAAA,yCACU,EACVZ,mBAAA,CAkBM,OAlBNa,UAkBM,GAjBFD,mBAAA,wKAIJ,EAGIZ,mBAAA,CASM,OATNc,UASM,GARFd,mBAAA,CAEM,OAFNe,UAEM,GADFf,mBAAA,CAA2E,OAA3EgB,UAA2E,EAAAC,gBAAA,CAA7BC,IAAA,CAAAC,EAAE,mC,GAEpDnB,mBAAA,CAGM,OAHNoB,UAGM,GAFFpB,mBAAA,CAAmF;IAA7EO,GAAG,EAAEc,OAAO;IAAmCC,KAAK,EAAC,IAAI;IAACC,MAAM,EAAC,IAAI;IAACb,GAAG,EAAC;uCAChFV,mBAAA,CAA4C,QAA5CwB,UAA4C,EAAAP,gBAAA,CAApBT,MAAA,CAAAiB,KAAK,CAACC,IAAI,iB,OAO9Cd,mBAAA,aAAgB,EAC8EJ,MAAA,CAAAmB,MAAM,CAACC,MAAM,Q,cAA3GC,YAAA,CAIYC,oBAAA;;IAJDhC,KAAK,EAAC,iBAAiB;IAAEiC,QAAQ,EAAE,IAAI;IAAE,iBAAe,EAAC,OAAO;IAACC,KAAK,EAAC;;sBAC9D;MAAA,OAAsB,E,kBAAtC/B,mBAAA,CAEiBgC,SAAA,QAAAC,WAAA,CAFc1B,MAAA,CAAAmB,MAAM,YAAdQ,IAAI;6BAA3BN,YAAA,CAEiBO,yBAAA;UAFuBC,GAAG,EAAEF,IAAI,CAACG,EAAE;UAAEvC,KAA2B,EAA3B;YAAA;UAAA;;4BAClD;YAAA,OAAsE,CAAtEC,mBAAA,CAAsE;cAAhEO,GAAG,EAAE4B,IAAI,CAACI,KAAK;cAAE7B,GAAG,EAAC,EAAE;cAACZ,KAAK,EAAC,KAAK;cAACC,KAA2B,EAA3B;gBAAA;cAAA;;;;;;;;;2CAIlDa,mBAAA,UAAa,EACbZ,mBAAA,CAcM,OAdNwC,WAcM,GAbFxC,mBAAA,CAA2E;IAAtEF,KAAK,EAAC,OAAO;IAAES,GAAG,EAAEc,OAAO;IAAoCX,GAAG,EAAC;wCAExEP,YAAA,CAUuBsC,+BAAA;IAVAC,IAAI,EAAElC,MAAA,CAAAiB,KAAK,CAACiB,IAAI;IAAQ5C,KAAK,EAAC,QAAQ;IAAE6C,QAAQ,EAAE,GAAG;IAAGC,IAAI,EAAE,CAAC;IAAGC,KAAK,EAAE,CAAC;IAAEC,KAAK,EAAL,EAAK;IACnGC,cAAc,EAAE,CAAC;IAAGC,YAAY,EAAE;;sBACjB;MAAA,OAAyC,E,kBAA3D/C,mBAAA,CAOMgC,SAAA,QAAAC,WAAA,CAPoC1B,MAAA,CAAAiB,KAAK,CAACiB,IAAI,kBAA1BP,IAAI,EAAEc,KAAK;6BAArChD,mBAAA,CAOM;UAPDH,KAAK,EAAC,MAAM;UAA4CuC,GAAG,EAAEY;YAC9DrC,mBAAA,sDAAuD,EACvDZ,mBAAA,CAIO,QAJPkD,WAIO,GAHHlD,mBAAA,CAC+B,QAD/BmD,WAC+B,EAAAlC,gBAAA,CADZkB,IAAI,CAACiB,IAAI,CAACC,OAAO,iCAAgC,GAAC,GAAApC,gBAAA,CAAGC,IAAA,CAAAC,EAAE,gBAAe,GAAC,GAAAF,gBAAA,CAAGT,MAAA,CAAA8C,QAAQ,IAAG,GAAC,GAAArC,gBAAA,CACrGkB,IAAI,CAACoB,YAAY,kBACrB3C,mBAAA,uFAAsF,C;;;;;iCAMtGA,mBAAA,QAAW,EACXZ,mBAAA,CAwLM,OAxLNwD,WAwLM,GAvLF5C,mBAAA,UAAa,EAEbA,mBAAA,0HAAuH,EACvHA,mBAAA,SAAY,EACZZ,mBAAA,CAGM,OAHNyD,WAGM,EAAAxC,gBAAA,CADCC,IAAA,CAAAC,EAAE,8BAETnB,mBAAA,CAkDM,OAlDN0D,WAkDM,GAjDF1D,mBAAA,CAwCM,cAvCFA,mBAAA,CAWM;IAXDF,KAAK,EAAC,SAAS;IAAG6D,OAAK,EAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAAErD,MAAA,CAAAsD,SAAS,IAAI5C,IAAA,CAAAC,EAAE;IAAA;IAAgBpB,KAAyE,EAAzE;MAAA;MAAA;MAAA;MAAA;IAAA;MACzDC,mBAAA,CAGM,OAHN+D,WAGM,GAFF/D,mBAAA,CACmB;IADbO,GAAG,EAAEc,OAAO;IAAqCC,KAAK,EAAC,IAAI;IAACC,MAAM,EAAC,IAAI;IAACb,GAAG,EAAC,EAAE;IAChFZ,KAAK,EAAC;0CAEdE,mBAAA,CAAkE,OAAlEgE,WAAkE,EAAA/C,gBAAA,CAAxBC,IAAA,CAAAC,EAAE,+BAC5CnB,mBAAA,CAIM,OAJNiE,WAIM,GAHFrD,mBAAA,qJACuB,EACnBT,YAAA,CAA6D+D,mBAAA;IAAnDC,KAAK,EAAC,MAAM;IAAC7C,KAAK,EAAC,IAAI;IAACC,MAAM,EAAC,IAAI;IAAC6B,IAAI,EAAC;UAG/DgB,WAAmC,EAGnCpE,mBAAA,CAQM;IARDF,KAAK,EAAC,SAAS;IAAE6D,OAAK,EAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAAErD,MAAA,CAAA6D,MAAM;IAAA;IAAItE,KAA4E,EAA5E;MAAA;MAAA;MAAA;MAAA;IAAA;MACnCC,mBAAA,CAEM,OAFNsE,WAEM,GADFtE,mBAAA,CAAoG;IAA9FO,GAAG,EAAEc,OAAO;IAAqCC,KAAK,EAAC,IAAI;IAACC,MAAM,EAAC,IAAI;IAACb,GAAG,EAAC,EAAE;IAACZ,KAAK,EAAC;0CAE/FE,mBAAA,CAAoE,OAApEuE,WAAoE,EAAAtD,gBAAA,CAA1BC,IAAA,CAAAC,EAAE,iCAC5CnB,mBAAA,CAEM,OAFNwE,WAEM,GADFrE,YAAA,CAA6D+D,mBAAA;IAAnDC,KAAK,EAAC,MAAM;IAAC7C,KAAK,EAAC,IAAI;IAACC,MAAM,EAAC,IAAI;IAAC6B,IAAI,EAAC;UAM3DqB,WAAmC,EACnCzE,mBAAA,CASM;IATDF,KAAK,EAAC,SAAS;IAAG6D,OAAK,EAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAAErD,MAAA,CAAAsD,SAAS,IAAI5C,IAAA,CAAAC,EAAE;IAAA;IAAgBpB,KAAwE,EAAxE;MAAA;MAAA;MAAA;MAAA;IAAA;MACzDC,mBAAA,CAGM,OAHN0E,WAGM,GAFF1E,mBAAA,CACmB;IADbO,GAAG,EAAEc,OAAO;IAAqCC,KAAK,EAAC,IAAI;IAACC,MAAM,EAAC,IAAI;IAACb,GAAG,EAAC,EAAE;IAChFZ,KAAK,EAAC;0CAEdE,mBAAA,CAAkE,OAAlE2E,WAAkE,EAAA1D,gBAAA,CAAxBC,IAAA,CAAAC,EAAE,+BAC5CnB,mBAAA,CAEM,OAFN4E,WAEM,GADFzE,YAAA,CAA6D+D,mBAAA;IAAnDC,KAAK,EAAC,MAAM;IAAC7C,KAAK,EAAC,IAAI;IAACC,MAAM,EAAC,IAAI;IAAC6B,IAAI,EAAC;YAM/DpD,mBAAA,CAIM;IAJDF,KAAK,EAAC,gBAAgB;IAAE6D,OAAK,EAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAAErD,MAAA,CAAAqE,OAAO;IAAA;IAAU9E,KAA8F,EAA9F;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;MACjDC,mBAAA,CAAoL,OAApL8E,WAAoL,EAAA7D,gBAAA,CAAxBC,IAAA,CAAAC,EAAE,6BAC9JnB,mBAAA,CAAgF;IAA3EF,KAAK,EAAC,WAAW;IAAES,GAAG,EAAEc,OAAO;IAAqCX,GAAG,EAAC;4CAQrFE,mBAAA,SAAY,EACZA,mBAAA,2gEAqCU,EACVA,mBAAA,aAAgB,EAChBA,mBAAA,u5CAsBc,EACdA,mBAAA,UAAa,EACbA,mBAAA,0FAEU,EACVA,mBAAA,02BAiBU,EACVA,mBAAA,UAAa,EAIbA,mBAAA,UAAa,EACbA,mBAAA,wpCAiBU,EACVA,mBAAA,UAAa,EACbZ,mBAAA,CAEM,OAFN+E,WAEM,EAAA9D,gBAAA,CADCC,IAAA,CAAAC,EAAE,8BAETnB,mBAAA,CAIM,OAJNgF,WAIM,I,cAHF/E,mBAAA,CACqCgC,SAAA,QAAAC,WAAA,CADqC,CAAC,YAAR+C,GAAG;WAAtEjF,mBAAA,CACqC;MAD/BO,GAAG,EAAEc,OAAO,2BAA2B4D,GAAG;MAA8B5C,GAAG,EAAE4C,GAAG;MAAEnF,KAAK,EAAC,cAAc;MACxGY,GAAG,EAAC,EAAE;MAAEiD,OAAK,WAAAA,QAAAE,MAAA;QAAA,OAAErD,MAAA,CAAA0E,WAAW,CAACD,GAAG;MAAA;;oCAGtCjF,mBAAA,CAIM,OAJNmF,WAIM,I,cAHFlF,mBAAA,CAC4DgC,SAAA,QAAAC,WAAA,CADsB,CAAC,YAAR+C,GAAG;WAA9EjF,mBAAA,CAC4D;MADtDO,GAAG,EAAEc,OAAO,mCAAmC4D,GAAG;MAA8B5C,GAAG,EAAE4C,GAAG;MAC1FnF,KAAK,EAAC,cAAc;MAACY,GAAG,EAAC,EAAE;MAAEiD,OAAK,WAAAA,QAAAE,MAAA;QAAA,OAAErD,MAAA,CAAA0E,WAAW,CAACD,GAAG;MAAA;;sCAI/D9E,YAAA,CAgBaiF,qBAAA;IAhBOC,IAAI,EAAE7E,MAAA,CAAA8E,KAAK;;aAAL9E,MAAA,CAAA8E,KAAK,GAAAzB,MAAA;IAAA;IAAEvC,KAAK,EAAC,KAAK;IAAEiE,iBAAiB,EAAE;;sBAC7D;MAAA,OAcM,CAdNvF,mBAAA,CAcM,OAdNwF,WAcM,GAbFxF,mBAAA,CAAiE,OAAjEyF,WAAiE,EAAAxE,gBAAA,CAAtCC,IAAA,CAAAC,EAAE,6CAC7BP,mBAAA,wFAAuF,EACvFZ,mBAAA,CAUM,OAVN0F,WAUM,GATF9E,mBAAA,gFAA+E,EAC/EZ,mBAAA,CAEM,OAFN2F,WAEM,GADF3F,mBAAA,CAA+C;QAAzCF,KAAK,EAAC,QAAQ;QAAC8F,SAAkB,EAAVpF,MAAA,CAAAqF;8CAEjC7F,mBAAA,CAIM,OAJN8F,WAIM,GAHF3F,YAAA,CAEa4F,qBAAA;QAFD/D,KAAK,EAAL,EAAK;QAACgE,KAAK,EAAL,EAAK;QAACC,IAAI,EAAC,SAAS;QAAEtC,OAAK,EAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;UAAA,OAAErD,MAAA,CAAA8E,KAAK;QAAA;;0BAChD;UAAA,OAAmB,C,kCAAhBpE,IAAA,CAAAC,EAAE,4B"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}