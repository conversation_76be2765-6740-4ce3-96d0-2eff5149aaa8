{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _GridItem from \"./GridItem.mjs\";\nvar GridItem = withInstall(_GridItem);\nvar stdin_default = GridItem;\nexport { GridItem, stdin_default as default };", "map": {"version": 3, "names": ["withInstall", "_GridItem", "GridItem", "stdin_default", "default"], "sources": ["C:/Users/<USER>/Desktop/vue3_3.0/node_modules/vant/es/grid-item/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _GridItem from \"./GridItem.mjs\";\nconst GridItem = withInstall(_GridItem);\nvar stdin_default = GridItem;\nexport {\n  GridItem,\n  stdin_default as default\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,SAAS,MAAM,gBAAgB;AACtC,IAAMC,QAAQ,GAAGF,WAAW,CAACC,SAAS,CAAC;AACvC,IAAIE,aAAa,GAAGD,QAAQ;AAC5B,SACEA,QAAQ,EACRC,aAAa,IAAIC,OAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}