{"ast": null, "code": "function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nfunction _iterableToArrayLimit(arr, i) { var _i = null == arr ? null : \"undefined\" != typeof Symbol && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (null != _i) { var _s, _e, _x, _r, _arr = [], _n = !0, _d = !1; try { if (_x = (_i = _i.call(arr)).next, 0 === i) { if (Object(_i) !== _i) return; _n = !1; } else for (; !(_n = (_s = _x.call(_i)).done) && (_arr.push(_s.value), _arr.length !== i); _n = !0); } catch (err) { _d = !0, _e = err; } finally { try { if (!_n && null != _i.return && (_r = _i.return(), Object(_r) !== _r)) return; } finally { if (_d) throw _e; } } return _arr; } }\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\nimport { mergeProps as _mergeProps, createVNode as _createVNode } from \"vue\";\nimport { inject, computed, defineComponent } from \"vue\";\nimport { addUnit, numericProp, makeStringProp, createNamespace } from \"../utils/index.mjs\";\nimport { Badge } from \"../badge/index.mjs\";\nimport { CONFIG_PROVIDER_KEY } from \"../config-provider/ConfigProvider.mjs\";\nvar _createNamespace = createNamespace(\"icon\"),\n  _createNamespace2 = _slicedToArray(_createNamespace, 2),\n  name = _createNamespace2[0],\n  bem = _createNamespace2[1];\nvar isImage = function isImage(name2) {\n  return name2 == null ? void 0 : name2.includes(\"/\");\n};\nvar iconProps = {\n  dot: Boolean,\n  tag: makeStringProp(\"i\"),\n  name: String,\n  size: numericProp,\n  badge: numericProp,\n  color: String,\n  badgeProps: Object,\n  classPrefix: String\n};\nvar stdin_default = defineComponent({\n  name: name,\n  props: iconProps,\n  setup: function setup(props, _ref) {\n    var slots = _ref.slots;\n    var config = inject(CONFIG_PROVIDER_KEY, null);\n    var classPrefix = computed(function () {\n      return props.classPrefix || (config == null ? void 0 : config.iconPrefix) || bem();\n    });\n    return function () {\n      var tag = props.tag,\n        dot = props.dot,\n        name2 = props.name,\n        size = props.size,\n        badge = props.badge,\n        color = props.color;\n      var isImageIcon = isImage(name2);\n      return _createVNode(Badge, _mergeProps({\n        \"dot\": dot,\n        \"tag\": tag,\n        \"class\": [classPrefix.value, isImageIcon ? \"\" : \"\".concat(classPrefix.value, \"-\").concat(name2)],\n        \"style\": {\n          color: color,\n          fontSize: addUnit(size)\n        },\n        \"content\": badge\n      }, props.badgeProps), {\n        default: function _default() {\n          var _a;\n          return [(_a = slots.default) == null ? void 0 : _a.call(slots), isImageIcon && _createVNode(\"img\", {\n            \"class\": bem(\"image\"),\n            \"src\": name2\n          }, null)];\n        }\n      });\n    };\n  }\n});\nexport { stdin_default as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}