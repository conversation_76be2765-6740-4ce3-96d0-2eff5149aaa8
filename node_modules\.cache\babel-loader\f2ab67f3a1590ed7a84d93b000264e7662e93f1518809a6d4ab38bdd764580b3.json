{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _CouponCell from \"./CouponCell.mjs\";\nvar CouponCell = withInstall(_CouponCell);\nvar stdin_default = CouponCell;\nexport { CouponCell, stdin_default as default };", "map": {"version": 3, "names": ["withInstall", "_CouponCell", "CouponCell", "stdin_default", "default"], "sources": ["C:/Users/<USER>/Desktop/vue3_3.0/node_modules/vant/es/coupon-cell/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _CouponCell from \"./CouponCell.mjs\";\nconst CouponCell = withInstall(_CouponCell);\nvar stdin_default = CouponCell;\nexport {\n  CouponCell,\n  stdin_default as default\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,WAAW,MAAM,kBAAkB;AAC1C,IAAMC,UAAU,GAAGF,WAAW,CAACC,WAAW,CAAC;AAC3C,IAAIE,aAAa,GAAGD,UAAU;AAC9B,SACEA,UAAU,EACVC,aAAa,IAAIC,OAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}