{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, toDisplayString as _toDisplayString, createElementVNode as _createElementVNode, createCommentVNode as _createCommentVNode, createTextVNode as _createTextVNode, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, pushScopeId as _pushScopeId, popScopeId as _popScopeId } from \"vue\";\nvar _withScopeId = function _withScopeId(n) {\n  return _pushScopeId(\"data-v-3deb4ae6\"), n = n(), _popScopeId(), n;\n};\nvar _hoisted_1 = {\n  class: \"homes\"\n};\nvar _hoisted_2 = {\n  class: \"box\",\n  style: {\n    \"height\": \"19rem\"\n  }\n};\nvar _hoisted_3 = {\n  class: \"boxitem\",\n  style: {\n    \"margin-bottom\": \"0.4rem\"\n  }\n};\nvar _hoisted_4 = {\n  class: \"boxticon border\"\n};\nvar _hoisted_5 = {\n  class: \"boxticon\"\n};\nvar _hoisted_6 = {\n  class: \"recount\"\n};\nvar _hoisted_7 = {\n  class: \"self home\",\n  style: {\n    \"margin-top\": \"0.5rem\"\n  }\n};\nvar _hoisted_8 = {\n  class: \"list\",\n  style: {\n    \"width\": \"90%\",\n    \"margin-left\": \"5%\"\n  }\n};\nvar _hoisted_9 = {\n  class: \"box\"\n};\nvar _hoisted_10 = {\n  class: \"title\"\n};\nvar _hoisted_11 = {\n  class: \"l\"\n};\nvar _hoisted_12 = {\n  class: \"r\"\n};\nvar _hoisted_13 = {\n  class: \"address\"\n};\nvar _hoisted_14 = {\n  class: \"text\"\n};\nvar _hoisted_15 = {\n  class: \"span\"\n};\nvar _hoisted_16 = {\n  class: \"text\"\n};\nvar _hoisted_17 = {\n  class: \"span\"\n};\nvar _hoisted_18 = {\n  class: \"box\"\n};\nvar _hoisted_19 = {\n  class: \"title\"\n};\nvar _hoisted_20 = {\n  class: \"l\"\n};\nvar _hoisted_21 = {\n  class: \"r\"\n};\nvar _hoisted_22 = {\n  class: \"address\"\n};\nvar _hoisted_23 = {\n  class: \"text\"\n};\nvar _hoisted_24 = {\n  class: \"span\"\n};\nvar _hoisted_25 = {\n  class: \"text\"\n};\nvar _hoisted_26 = {\n  class: \"span\"\n};\nvar _hoisted_27 = {\n  class: \"box\"\n};\nvar _hoisted_28 = {\n  class: \"title\"\n};\nvar _hoisted_29 = {\n  class: \"l\"\n};\nvar _hoisted_30 = {\n  class: \"r\"\n};\nvar _hoisted_31 = {\n  class: \"address\"\n};\nvar _hoisted_32 = {\n  class: \"text\"\n};\nvar _hoisted_33 = {\n  class: \"span\"\n};\nvar _hoisted_34 = {\n  class: \"text\"\n};\nvar _hoisted_35 = {\n  class: \"span\"\n};\nvar _hoisted_36 = {\n  class: \"box\"\n};\nvar _hoisted_37 = {\n  class: \"title\"\n};\nvar _hoisted_38 = {\n  class: \"l\"\n};\nvar _hoisted_39 = {\n  class: \"r\"\n};\nvar _hoisted_40 = {\n  class: \"address\"\n};\nvar _hoisted_41 = {\n  class: \"text\"\n};\nvar _hoisted_42 = {\n  class: \"span\"\n};\nvar _hoisted_43 = {\n  class: \"text\"\n};\nvar _hoisted_44 = {\n  class: \"span\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_van_nav_bar = _resolveComponent(\"van-nav-bar\");\n  return _openBlock(), _createElementBlock(_Fragment, null, [_createElementVNode(\"div\", _hoisted_1, [_createVNode(_component_van_nav_bar, {\n    title: _ctx.$t('msg.tdbg')\n  }, null, 8 /* PROPS */, [\"title\"]), _createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"div\", _hoisted_4, [_createElementVNode(\"div\", null, _toDisplayString(_ctx.$t('msg.yonj')), 1 /* TEXT */), _createElementVNode(\"div\", null, _toDisplayString($setup.currency + $setup.info.team_yj), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"div\", null, _toDisplayString(_ctx.$t('msg.get_m')), 1 /* TEXT */), _createElementVNode(\"div\", null, _toDisplayString($setup.currency + $setup.info.team_rebate), 1 /* TEXT */)])]), _createElementVNode(\"div\", null, [_createElementVNode(\"div\", _hoisted_6, _toDisplayString(_ctx.$t('msg.lq')), 1 /* TEXT */)])]), _createCommentVNode(\" <div class=\\\"boxl\\\">\\r\\n\\t\\t\\t\\t<div class=\\\"box1_perpor\\\">人数 0</div>\\r\\n\\t\\t\\t\\t<div class=\\\"box1_row\\\">\\r\\n\\t\\t\\t\\t\\t<span>用户</span>\\r\\n\\t\\t\\t\\t\\t<span>贡献数量</span>\\r\\n\\t\\t\\t\\t</div>\\r\\n\\t\\t\\t</div> \"), _createElementVNode(\"div\", _hoisted_7, [_createElementVNode(\"div\", _hoisted_8, [_createElementVNode(\"div\", _hoisted_9, [_createElementVNode(\"div\", _hoisted_10, [_createElementVNode(\"span\", _hoisted_11, _toDisplayString(_ctx.$t('msg.sysj')), 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_12, _toDisplayString($setup.currency + $setup.info.team_rebate), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_13, [_createElementVNode(\"div\", _hoisted_14, [_createTextVNode(_toDisplayString(_ctx.$t('msg.tdsl')) + \" \", 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_15, _toDisplayString($setup.info.team_count), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_16, [_createTextVNode(_toDisplayString(_ctx.$t('msg.tdddyj')) + \" \", 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_17, _toDisplayString($setup.info.team_yj), 1 /* TEXT */)])])]), _createElementVNode(\"div\", _hoisted_18, [_createElementVNode(\"div\", _hoisted_19, [_createElementVNode(\"span\", _hoisted_20, _toDisplayString(_ctx.$t('msg.oneLevel')), 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_21, _toDisplayString($setup.currency + $setup.info.team1_rebate), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_22, [_createElementVNode(\"div\", _hoisted_23, [_createTextVNode(_toDisplayString(_ctx.$t('msg.tdsl')) + \" \", 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_24, _toDisplayString($setup.info.team1_count), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_25, [_createTextVNode(_toDisplayString(_ctx.$t('msg.tdddyj')) + \" \", 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_26, _toDisplayString($setup.info.team1_yj), 1 /* TEXT */)])])]), _createElementVNode(\"div\", _hoisted_27, [_createElementVNode(\"div\", _hoisted_28, [_createElementVNode(\"span\", _hoisted_29, _toDisplayString(_ctx.$t('msg.twoLevel')), 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_30, _toDisplayString($setup.currency + $setup.info.team2_rebate), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_31, [_createElementVNode(\"div\", _hoisted_32, [_createTextVNode(_toDisplayString(_ctx.$t('msg.tdsl')) + \" \", 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_33, _toDisplayString($setup.info.team2_count), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_34, [_createTextVNode(_toDisplayString(_ctx.$t('msg.tdddyj')) + \" \", 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_35, _toDisplayString($setup.info.team2_yj), 1 /* TEXT */)])])]), _createElementVNode(\"div\", _hoisted_36, [_createElementVNode(\"div\", _hoisted_37, [_createElementVNode(\"span\", _hoisted_38, _toDisplayString(_ctx.$t('msg.threeLevel')), 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_39, _toDisplayString($setup.currency + $setup.info.team3_rebate), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_40, [_createElementVNode(\"div\", _hoisted_41, [_createTextVNode(_toDisplayString(_ctx.$t('msg.tdsl')) + \" \", 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_42, _toDisplayString($setup.info.team3_count), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_43, [_createTextVNode(_toDisplayString(_ctx.$t('msg.tdddyj')) + \" \", 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_44, _toDisplayString($setup.info.team3_yj), 1 /* TEXT */)])])])])])]), _createCommentVNode(\" <div class=\\\"self home\\\">\\r\\n        <van-nav-bar :title=\\\"$t('msg.tdbg')\\\"  @click-left=\\\"$router.go(-1)\\\"></van-nav-bar>\\r\\n        <div class=\\\"top\\\">\\r\\n            <div class=\\\"info\\\">\\r\\n                <div class=\\\"avaitar\\\">\\r\\n                    <div class=\\\"top_s\\\">\\r\\n                        <span class=\\\"span\\\" :class=\\\"topcheck == 0 && 'check'\\\" @click=\\\"topClick(0)\\\">{{$t('msg.all')}}</span>\\r\\n                        <span class=\\\"span\\\" :class=\\\"topcheck == 1 && 'check'\\\" @click=\\\"topClick(1)\\\">{{$t('msg.today')}}</span>\\r\\n                        <span class=\\\"span\\\" :class=\\\"topcheck == 2 && 'check'\\\" @click=\\\"topClick(2)\\\">{{$t('msg.prev_day')}}</span>\\r\\n                        <span class=\\\"span\\\" :class=\\\"topcheck == 3 && 'check'\\\" @click=\\\"topClick(3)\\\">{{$t('msg.week')}}</span>\\r\\n                    </div>\\r\\n                    <div class=\\\"check_rili\\\" @click=\\\"function(){if(topcheck == 0){showCalendar = true}}\\\">\\r\\n                        <img :src=\\\"require('@/assets/images/self/team.png')\\\" class=\\\"img\\\" alt=\\\"\\\">\\r\\n                        <div class=\\\"r\\\" v-if=\\\"time1\\\">\\r\\n                            {{time1}}\\r\\n                        </div>\\r\\n                        <div class=\\\"r\\\" v-else>{{$t('msg.xzsj')}}</div>\\r\\n                    </div>\\r\\n                    <van-calendar v-model:show=\\\"showCalendar\\\" type=\\\"range\\\" @confirm=\\\"onConfirm\\\" color=\\\"#6833ff\\\" :readonly=\\\"topcheck!=0\\\" :min-date=\\\"new Date(2010, 0, 1)\\\"/>\\r\\n                </div>\\r\\n            </div>\\r\\n        </div>\\r\\n        <div class=\\\"list\\\">\\r\\n            <div class=\\\"box\\\">\\r\\n                <div class=\\\"title\\\">\\r\\n                    <span class=\\\"l\\\">{{$t('msg.sysj')}}</span>\\r\\n                    <span class=\\\"r\\\">{{currency + info.team_rebate}}</span>\\r\\n                </div>\\r\\n                <div class=\\\"address\\\">\\r\\n                    <div class=\\\"text\\\">{{$t('msg.tdsl')}} <span class=\\\"span\\\">{{info.team_count}}</span></div>\\r\\n                    <div class=\\\"text\\\">{{$t('msg.tdddyj')}} <span class=\\\"span\\\">{{info.team_yj}}</span></div>\\r\\n                </div>\\r\\n            </div>\\r\\n            <div class=\\\"box\\\">\\r\\n                <div class=\\\"title\\\">\\r\\n                    <span class=\\\"l\\\">{{$t('msg.oneLevel')}}</span>\\r\\n                    <span class=\\\"r\\\">{{currency + info.team1_rebate}}</span>\\r\\n                </div>\\r\\n                <div class=\\\"address\\\">\\r\\n                    <div class=\\\"text\\\">{{$t('msg.tdsl')}} <span class=\\\"span\\\">{{info.team1_count}}</span></div>\\r\\n                    <div class=\\\"text\\\">{{$t('msg.tdddyj')}} <span class=\\\"span\\\">{{info.team1_yj}}</span></div>\\r\\n                </div>\\r\\n            </div>\\r\\n            <div class=\\\"box\\\">\\r\\n                <div class=\\\"title\\\">\\r\\n                    <span class=\\\"l\\\">{{$t('msg.twoLevel')}}</span>\\r\\n                    <span class=\\\"r\\\">{{currency + info.team2_rebate}}</span>\\r\\n                </div>\\r\\n                <div class=\\\"address\\\">\\r\\n                    <div class=\\\"text\\\">{{$t('msg.tdsl')}} <span class=\\\"span\\\">{{info.team2_count}}</span></div>\\r\\n                    <div class=\\\"text\\\">{{$t('msg.tdddyj')}} <span class=\\\"span\\\">{{info.team2_yj}}</span></div>\\r\\n                </div>\\r\\n            </div>\\r\\n            <div class=\\\"box\\\">\\r\\n                <div class=\\\"title\\\">\\r\\n                    <span class=\\\"l\\\">{{$t('msg.threeLevel')}}</span>\\r\\n                    <span class=\\\"r\\\">{{currency + info.team3_rebate}}</span>\\r\\n                </div>\\r\\n                <div class=\\\"address\\\">\\r\\n                    <div class=\\\"text\\\">{{$t('msg.tdsl')}} <span class=\\\"span\\\">{{info.team3_count}}</span></div>\\r\\n                    <div class=\\\"text\\\">{{$t('msg.tdddyj')}} <span class=\\\"span\\\">{{info.team3_yj}}</span></div>\\r\\n                </div>\\r\\n            </div>\\r\\n        </div> \"), _createCommentVNode(\" </div> \")], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */);\n}", "map": {"version": 3, "names": ["class", "style", "_createElementVNode", "_hoisted_1", "_createVNode", "_component_van_nav_bar", "title", "_ctx", "$t", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_toDisplayString", "$setup", "currency", "info", "team_yj", "_hoisted_5", "team_rebate", "_hoisted_6", "_createCommentVNode", "_hoisted_7", "_hoisted_8", "_hoisted_9", "_hoisted_10", "_hoisted_11", "_hoisted_12", "_hoisted_13", "_hoisted_14", "_hoisted_15", "team_count", "_hoisted_16", "_hoisted_17", "_hoisted_18", "_hoisted_19", "_hoisted_20", "_hoisted_21", "team1_rebate", "_hoisted_22", "_hoisted_23", "_hoisted_24", "team1_count", "_hoisted_25", "_hoisted_26", "team1_yj", "_hoisted_27", "_hoisted_28", "_hoisted_29", "_hoisted_30", "team2_rebate", "_hoisted_31", "_hoisted_32", "_hoisted_33", "team2_count", "_hoisted_34", "_hoisted_35", "team2_yj", "_hoisted_36", "_hoisted_37", "_hoisted_38", "_hoisted_39", "team3_rebate", "_hoisted_40", "_hoisted_41", "_hoisted_42", "team3_count", "_hoisted_43", "_hoisted_44", "team3_yj"], "sources": ["C:\\Users\\<USER>\\Desktop\\vue3_3.0\\src\\views\\self\\components\\team.vue"], "sourcesContent": ["<template >\r\n    <div class=\" homes\">\r\n\r\n    \r\n\t<van-nav-bar   :title=\"$t('msg.tdbg')\"></van-nav-bar>\r\n\t\t\t<div class=\"box\" style=\"height: 19rem;\">\r\n\t\t\t\t<div class=\"boxitem\" style=\"margin-bottom: 0.4rem;\">\r\n\t\t\t\t\t<div class=\"boxticon border\">\r\n\t\t\t\t\t\t<div > {{$t('msg.yonj')}}</div>\r\n\t\t\t\t\t\t<div>{{currency + info.team_yj}}</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t<div  class=\"boxticon\">\r\n\t\t\t\t\t\t<div>{{$t('msg.get_m')}}</div>\r\n\t\t\t\t\t\t<div>{{currency + info.team_rebate}}</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div >\r\n\t\t\t\t\t<div class=\"recount\">{{$t('msg.lq')}}</div>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t\t<!-- <div class=\"boxl\">\r\n\t\t\t\t<div class=\"box1_perpor\">人数 0</div>\r\n\t\t\t\t<div class=\"box1_row\">\r\n\t\t\t\t\t<span>用户</span>\r\n\t\t\t\t\t<span>贡献数量</span>\r\n\t\t\t\t</div>\r\n\t\t\t</div> -->\r\n            <div class=\"self home\" style=\"margin-top: 0.5rem;\" >\r\n            <div class=\"list\" style=\"width: 90%;margin-left: 5%;\">\r\n            <div class=\"box\">\r\n                <div class=\"title\">\r\n                    <span class=\"l\">{{$t('msg.sysj')}}</span>\r\n                    <span class=\"r\">{{currency + info.team_rebate}}</span>\r\n                </div>\r\n                <div class=\"address\">\r\n                    <div class=\"text\">{{$t('msg.tdsl')}} <span class=\"span\">{{info.team_count}}</span></div>\r\n                    <div class=\"text\">{{$t('msg.tdddyj')}} <span class=\"span\">{{info.team_yj}}</span></div>\r\n                </div>\r\n            </div>\r\n            <div class=\"box\">\r\n                <div class=\"title\">\r\n                    <span class=\"l\">{{$t('msg.oneLevel')}}</span>\r\n                    <span class=\"r\">{{currency + info.team1_rebate}}</span>\r\n                </div>\r\n                <div class=\"address\">\r\n                    <div class=\"text\">{{$t('msg.tdsl')}} <span class=\"span\">{{info.team1_count}}</span></div>\r\n                    <div class=\"text\">{{$t('msg.tdddyj')}} <span class=\"span\">{{info.team1_yj}}</span></div>\r\n                </div>\r\n            </div>\r\n            <div class=\"box\">\r\n                <div class=\"title\">\r\n                    <span class=\"l\">{{$t('msg.twoLevel')}}</span>\r\n                    <span class=\"r\">{{currency + info.team2_rebate}}</span>\r\n                </div>\r\n                <div class=\"address\">\r\n                    <div class=\"text\">{{$t('msg.tdsl')}} <span class=\"span\">{{info.team2_count}}</span></div>\r\n                    <div class=\"text\">{{$t('msg.tdddyj')}} <span class=\"span\">{{info.team2_yj}}</span></div>\r\n                </div>\r\n            </div>\r\n            <div class=\"box\">\r\n                <div class=\"title\">\r\n                    <span class=\"l\">{{$t('msg.threeLevel')}}</span>\r\n                    <span class=\"r\">{{currency + info.team3_rebate}}</span>\r\n                </div>\r\n                <div class=\"address\">\r\n                    <div class=\"text\">{{$t('msg.tdsl')}} <span class=\"span\">{{info.team3_count}}</span></div>\r\n                    <div class=\"text\">{{$t('msg.tdddyj')}} <span class=\"span\">{{info.team3_yj}}</span></div>\r\n                </div>\r\n            </div>\r\n        </div> \r\n    </div> \r\n        </div>\r\n   <!-- <div class=\"self home\">\r\n        <van-nav-bar :title=\"$t('msg.tdbg')\"  @click-left=\"$router.go(-1)\"></van-nav-bar>\r\n        <div class=\"top\">\r\n            <div class=\"info\">\r\n                <div class=\"avaitar\">\r\n                    <div class=\"top_s\">\r\n                        <span class=\"span\" :class=\"topcheck == 0 && 'check'\" @click=\"topClick(0)\">{{$t('msg.all')}}</span>\r\n                        <span class=\"span\" :class=\"topcheck == 1 && 'check'\" @click=\"topClick(1)\">{{$t('msg.today')}}</span>\r\n                        <span class=\"span\" :class=\"topcheck == 2 && 'check'\" @click=\"topClick(2)\">{{$t('msg.prev_day')}}</span>\r\n                        <span class=\"span\" :class=\"topcheck == 3 && 'check'\" @click=\"topClick(3)\">{{$t('msg.week')}}</span>\r\n                    </div>\r\n                    <div class=\"check_rili\" @click=\"function(){if(topcheck == 0){showCalendar = true}}\">\r\n                        <img :src=\"require('@/assets/images/self/team.png')\" class=\"img\" alt=\"\">\r\n                        <div class=\"r\" v-if=\"time1\">\r\n                            {{time1}}\r\n                        </div>\r\n                        <div class=\"r\" v-else>{{$t('msg.xzsj')}}</div>\r\n                    </div>\r\n                    <van-calendar v-model:show=\"showCalendar\" type=\"range\" @confirm=\"onConfirm\" color=\"#6833ff\" :readonly=\"topcheck!=0\" :min-date=\"new Date(2010, 0, 1)\"/>\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"list\">\r\n            <div class=\"box\">\r\n                <div class=\"title\">\r\n                    <span class=\"l\">{{$t('msg.sysj')}}</span>\r\n                    <span class=\"r\">{{currency + info.team_rebate}}</span>\r\n                </div>\r\n                <div class=\"address\">\r\n                    <div class=\"text\">{{$t('msg.tdsl')}} <span class=\"span\">{{info.team_count}}</span></div>\r\n                    <div class=\"text\">{{$t('msg.tdddyj')}} <span class=\"span\">{{info.team_yj}}</span></div>\r\n                </div>\r\n            </div>\r\n            <div class=\"box\">\r\n                <div class=\"title\">\r\n                    <span class=\"l\">{{$t('msg.oneLevel')}}</span>\r\n                    <span class=\"r\">{{currency + info.team1_rebate}}</span>\r\n                </div>\r\n                <div class=\"address\">\r\n                    <div class=\"text\">{{$t('msg.tdsl')}} <span class=\"span\">{{info.team1_count}}</span></div>\r\n                    <div class=\"text\">{{$t('msg.tdddyj')}} <span class=\"span\">{{info.team1_yj}}</span></div>\r\n                </div>\r\n            </div>\r\n            <div class=\"box\">\r\n                <div class=\"title\">\r\n                    <span class=\"l\">{{$t('msg.twoLevel')}}</span>\r\n                    <span class=\"r\">{{currency + info.team2_rebate}}</span>\r\n                </div>\r\n                <div class=\"address\">\r\n                    <div class=\"text\">{{$t('msg.tdsl')}} <span class=\"span\">{{info.team2_count}}</span></div>\r\n                    <div class=\"text\">{{$t('msg.tdddyj')}} <span class=\"span\">{{info.team2_yj}}</span></div>\r\n                </div>\r\n            </div>\r\n            <div class=\"box\">\r\n                <div class=\"title\">\r\n                    <span class=\"l\">{{$t('msg.threeLevel')}}</span>\r\n                    <span class=\"r\">{{currency + info.team3_rebate}}</span>\r\n                </div>\r\n                <div class=\"address\">\r\n                    <div class=\"text\">{{$t('msg.tdsl')}} <span class=\"span\">{{info.team3_count}}</span></div>\r\n                    <div class=\"text\">{{$t('msg.tdddyj')}} <span class=\"span\">{{info.team3_yj}}</span></div>\r\n                </div>\r\n            </div>\r\n        </div> -->\r\n    <!-- </div> -->\r\n</template>\r\n<script>\r\nimport { ref} from 'vue';\r\nimport {junior} from '@/api/self/index'\r\nimport { useRouter } from 'vue-router';\r\nimport store from '@/store/index'\r\nexport default {\r\n    setup(){\r\n        const { push } = useRouter();\r\n        const currency = ref(store.state.baseInfo?.currency)\r\n        const topcheck = ref(0)\r\n        const imgCheck = ref(1)\r\n        const showCalendar = ref(false)\r\n        const time1 = ref('')\r\n        const info = ref({})\r\n        const start = ref('')\r\n        const end = ref('')\r\n\r\n        const getjunior = () => {\r\n            let json = {\r\n                ajax: 1,\r\n                start: start.value,\r\n                end: end.value\r\n            }\r\n            junior(json).then(res => {\r\n                info.value = {...(res || {})}\r\n            })\r\n        }\r\n        getjunior()\r\n        const clickLeft = () => {\r\n            push('/self')\r\n        }\r\n        \r\n        const formatDate = (date,num) => `${date.getMonth() + 1}-${(date.getDate() - (num || 0))}`;\r\n        const onConfirm = (values) => {\r\n            const [s, e] = values;\r\n            start.value = formatDate(s)\r\n            end.value = formatDate(e)\r\n            console.log(start.value)\r\n            showCalendar.value = false;\r\n            time1.value = `${formatDate(s)} — ${formatDate(e)}`;\r\n            getjunior()\r\n        }\r\n        const topClick = (val) => {\r\n            topcheck.value = val\r\n            switch (val) {\r\n                case 0:\r\n                    time1.value =  ''\r\n                    start.value = ''\r\n                    end.value = ''\r\n                    break;\r\n                case 1:\r\n                    let t = new Date()\r\n                    time1.value = `${formatDate(t)}`\r\n                    start.value = time1.value \r\n                    end.value = time1.value \r\n                    break;\r\n                case 2:\r\n                    let b = new Date()\r\n                    time1.value = `${formatDate(b,1)}`\r\n                    start.value = time1.value \r\n                    end.value = time1.value \r\n                    break;\r\n                case 3:\r\n                    let c = new Date()\r\n                    time1.value = `${formatDate(c,7)} — ${formatDate(c)}`;\r\n                    start.value = formatDate(c,7)\r\n                    end.value = formatDate(c)\r\n                    break;\r\n            \r\n                default:\r\n                    break;\r\n            }\r\n            getjunior()\r\n        }\r\n        return {info,imgCheck,clickLeft,topcheck,showCalendar,time1,onConfirm,topClick,currency}\r\n    }\r\n}\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.homes{\r\n    // background-image: url('~@/assets/images/home/<USER>');\r\n    background: #fff;\r\n    border-radius: 0;\r\n}\r\n:deep .van-nav-bar__content{\r\n\t\t\tbackground-color: #ffffff !important;\r\n\t\t\tbox-sizing: border-box;margin-top: -10px;\r\n\t\t}\r\n\r\n:deep .van-nav-bar{\r\n\tpadding: 0 !important;\r\n}\r\n.self{\r\n    overflow: auto;\r\n    :deep(.van-nav-bar){\r\n        background-color: #ffffff !important;\r\n        position: sticky;\r\n        top: 0;\r\n        left: 0;\r\n        color: black !important;\r\n        width: 100%;\r\n\t\r\n        .van-nav-bar__left{\r\n            .van-icon{\r\n                color: #fff;\r\n            }\r\n        }\r\n        .van-nav-bar__title{\r\n            color: #000000;\r\n        }\r\n        .van-nav-bar__right{\r\n            img{\r\n                height: 42px;\r\n            }\r\n        }\r\n        &::after{\r\n            display: none;\r\n        }\r\n    }\r\n    .top{\r\n        padding: 0 50px 100px;\r\n        background-color:#f90;\r\n        \r\n\r\n        color: #fff;\r\n        position: relative;\r\n        .info{\r\n            .avaitar{\r\n                // display: flex;\r\n                // height: 155px;\r\n                margin-bottom: 25px;\r\n                text-align: center;\r\n                .top_s{\r\n                    display: inline-block;\r\n                    border-radius: 20px;\r\n                    border: 2px solid #fff;\r\n                    color: #fff;\r\n                    .span{\r\n                        display: inline-block;\r\n                        padding: 14px 50px;\r\n                        font-size: 30px;\r\n                        font-weight: 600;\r\n                        border-radius: 20px;\r\n                        &.check{\r\n                            background-color: #fff;\r\n                            color: $theme;\r\n                        }\r\n                    }\r\n                }\r\n                .check_rili{\r\n                    display: flex;\r\n                    margin-top: 25px;\r\n                    font-size: 28px;\r\n                    font-weight: 600;\r\n                    letter-spacing: 4px;\r\n                    justify-content: center;\r\n                    .img{\r\n                        height: 35px;\r\n                        margin-right: 30px;\r\n                    }\r\n                }\r\n            }\r\n            :deep(.van-calendar){\r\n                color: #333;\r\n            }\r\n        }\r\n    }\r\n    .list{\r\n        position: relative;\r\n        // background-image: url('~@/assets/images/home/<USER>');\r\n        text-align: left;\r\n        // overflow: hidden;\r\n        margin-top: -85px;\r\n        display: flex;\r\n        flex-wrap: wrap;\r\n        .box{\r\n            width: 100%;\r\n            margin-top: 1rem;\r\n            .title{\r\n                margin: 30px;\r\n                display: flex;\r\n                justify-content: space-between;\r\n                padding: 0 30px;\r\n                font-size: 26px;\r\n                border-left: 10px solid #000;\r\n                color: #fff;\r\n                .r{\r\n                    color: #fff;\r\n                    font-weight: 600;\r\n                }\r\n            }\r\n        }\r\n        .address{\r\n            box-shadow: $shadow;\r\n            border-radius: 12px;\r\n            padding: 30px;\r\n            margin: 0 30px 40px;\r\n            background-image: url('~@/assets/images/self/address/bg.png');\r\n            background-size: 100% 100%;\r\n            text-align: left;\r\n            .text{\r\n                display: flex;\r\n                justify-content: space-between;\r\n                font-size: 30px;\r\n                font-weight: 600;\r\n                    &:first-child{\r\n                        margin-bottom: 40px;\r\n                    }\r\n                .span{\r\n                    color: #000;\r\n                    font-weight: 600;\r\n                    font-size: 26px;\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n.box{\t\r\n\t\twidth: 94%;\r\n\t\tmargin: 120px auto 0 auto;\r\n\t    padding: 15px;\r\n\t\t// background-color: #f90;\r\n        background-image: url('~@/assets/images/salesBack.png');\r\n        background-size: 100% 100%;\r\n\t\tborder-radius: 25px;\r\n\t\theight: 300px;\r\n\t\t.boxitem :fchild(1){\r\n\t\t\tborder-right: 1px solid #000000;\r\n\t\t}\r\n\t\t.boxitem{\r\n\t\t\twidth: 100%;\r\n\t\t\theight: 60%;\r\n\t\t\tdisplay: flex;\r\n\t\t\tjustify-content: space-between;\r\n\t\t\talign-items: center;\r\n\t\t\tmargin-bottom: 30px;\r\n\t\t\t.border{\r\n\t\t\t\tborder-right: 1px solid black;\r\n\t\t\t}\r\n\t\t\t.boxticon{\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\twidth: 50%;\r\n\t\t\t\tfont-size:35px;\r\n\t\t\t\tfont-weight: 800;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t}\r\n\t\t}\r\n}\r\n.recount{\r\n\twidth: 90%;\r\n\theight: 80px;\r\n\tbackground-color: #000;\r\n\tcolor: #fff;\r\n\tborder-radius: 20px;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tmargin:  0px auto;\r\n}\r\n.boxl{\r\n\twidth: 94%;\r\n\theight: 200px;\r\n\tbackground: linear-gradient(to bottom, #f39200, #fcead2);\r\n\tborder-radius: 25px;\r\n\tmargin: 20px auto;\r\n\t.box1_perpor{\r\n\t\theight: 50px;\r\n\t\tpadding: 30px 30px;\r\n\t\ttext-align: left;\r\n\t\tfont-size: 35px;\r\n\t\tcolor: #000000;\r\n\t}\r\n\t.box1_row{\r\n\t\twidth: 94%;\r\n\t\theight: 50px;\r\n\t\tmargin: 60px auto;\r\n\t\tdisplay: flex;\r\n\t\talign-content: center;\r\n\t\tjustify-content: space-evenly;\r\n\t\tborder-radius: 50px;\r\n\t\tbackground-color: #ffffff;\r\n\t}\r\n}\r\n</style>"], "mappings": ";;;;;EACSA,KAAK,EAAC;AAAQ;;EAIfA,KAAK,EAAC,KAAK;EAACC,KAAsB,EAAtB;IAAA;EAAA;;;EACXD,KAAK,EAAC,SAAS;EAACC,KAA8B,EAA9B;IAAA;EAAA;;;EACfD,KAAK,EAAC;AAAiB;;EAItBA,KAAK,EAAC;AAAU;;EAMjBA,KAAK,EAAC;AAAS;;EAURA,KAAK,EAAC,WAAW;EAACC,KAA2B,EAA3B;IAAA;EAAA;;;EAClBD,KAAK,EAAC,MAAM;EAACC,KAAmC,EAAnC;IAAA;IAAA;EAAA;;;EACbD,KAAK,EAAC;AAAK;;EACPA,KAAK,EAAC;AAAO;;EACRA,KAAK,EAAC;AAAG;;EACTA,KAAK,EAAC;AAAG;;EAEdA,KAAK,EAAC;AAAS;;EACXA,KAAK,EAAC;AAAM;;EAA0BA,KAAK,EAAC;AAAM;;EAClDA,KAAK,EAAC;AAAM;;EAA4BA,KAAK,EAAC;AAAM;;EAG5DA,KAAK,EAAC;AAAK;;EACPA,KAAK,EAAC;AAAO;;EACRA,KAAK,EAAC;AAAG;;EACTA,KAAK,EAAC;AAAG;;EAEdA,KAAK,EAAC;AAAS;;EACXA,KAAK,EAAC;AAAM;;EAA0BA,KAAK,EAAC;AAAM;;EAClDA,KAAK,EAAC;AAAM;;EAA4BA,KAAK,EAAC;AAAM;;EAG5DA,KAAK,EAAC;AAAK;;EACPA,KAAK,EAAC;AAAO;;EACRA,KAAK,EAAC;AAAG;;EACTA,KAAK,EAAC;AAAG;;EAEdA,KAAK,EAAC;AAAS;;EACXA,KAAK,EAAC;AAAM;;EAA0BA,KAAK,EAAC;AAAM;;EAClDA,KAAK,EAAC;AAAM;;EAA4BA,KAAK,EAAC;AAAM;;EAG5DA,KAAK,EAAC;AAAK;;EACPA,KAAK,EAAC;AAAO;;EACRA,KAAK,EAAC;AAAG;;EACTA,KAAK,EAAC;AAAG;;EAEdA,KAAK,EAAC;AAAS;;EACXA,KAAK,EAAC;AAAM;;EAA0BA,KAAK,EAAC;AAAM;;EAClDA,KAAK,EAAC;AAAM;;EAA4BA,KAAK,EAAC;AAAM;;;6DAjEzEE,mBAAA,CAsEU,OAtEVC,UAsEU,GAnEbC,YAAA,CAAqDC,sBAAA;IAArCC,KAAK,EAAEC,IAAA,CAAAC,EAAE;sCACvBN,mBAAA,CAcM,OAdNO,UAcM,GAbLP,mBAAA,CASM,OATNQ,UASM,GARLR,mBAAA,CAGM,OAHNS,UAGM,GAFLT,mBAAA,CAA+B,aAAAU,gBAAA,CAAtBL,IAAA,CAAAC,EAAE,8BACXN,mBAAA,CAAsC,aAAAU,gBAAA,CAA/BC,MAAA,CAAAC,QAAQ,GAAGD,MAAA,CAAAE,IAAI,CAACC,OAAO,iB,GAE/Bd,mBAAA,CAGM,OAHNe,UAGM,GAFLf,mBAAA,CAA8B,aAAAU,gBAAA,CAAvBL,IAAA,CAAAC,EAAE,+BACTN,mBAAA,CAA0C,aAAAU,gBAAA,CAAnCC,MAAA,CAAAC,QAAQ,GAAGD,MAAA,CAAAE,IAAI,CAACG,WAAW,iB,KAGpChB,mBAAA,CAEM,cADLA,mBAAA,CAA2C,OAA3CiB,UAA2C,EAAAP,gBAAA,CAApBL,IAAA,CAAAC,EAAE,2B,KAG3BY,mBAAA,6MAMU,EACDlB,mBAAA,CA2CF,OA3CEmB,UA2CF,GA1CEnB,mBAAA,CAyCE,OAzCFoB,UAyCE,GAxCFpB,mBAAA,CASM,OATNqB,UASM,GARFrB,mBAAA,CAGM,OAHNsB,WAGM,GAFFtB,mBAAA,CAAyC,QAAzCuB,WAAyC,EAAAb,gBAAA,CAAvBL,IAAA,CAAAC,EAAE,8BACpBN,mBAAA,CAAsD,QAAtDwB,WAAsD,EAAAd,gBAAA,CAApCC,MAAA,CAAAC,QAAQ,GAAGD,MAAA,CAAAE,IAAI,CAACG,WAAW,iB,GAEjDhB,mBAAA,CAGM,OAHNyB,WAGM,GAFFzB,mBAAA,CAAwF,OAAxF0B,WAAwF,G,kCAApErB,IAAA,CAAAC,EAAE,gBAAc,GAAC,iBAAAN,mBAAA,CAA6C,QAA7C2B,WAA6C,EAAAjB,gBAAA,CAAxBC,MAAA,CAAAE,IAAI,CAACe,UAAU,iB,GACzE5B,mBAAA,CAAuF,OAAvF6B,WAAuF,G,kCAAnExB,IAAA,CAAAC,EAAE,kBAAgB,GAAC,iBAAAN,mBAAA,CAA0C,QAA1C8B,WAA0C,EAAApB,gBAAA,CAArBC,MAAA,CAAAE,IAAI,CAACC,OAAO,iB,OAGhFd,mBAAA,CASM,OATN+B,WASM,GARF/B,mBAAA,CAGM,OAHNgC,WAGM,GAFFhC,mBAAA,CAA6C,QAA7CiC,WAA6C,EAAAvB,gBAAA,CAA3BL,IAAA,CAAAC,EAAE,kCACpBN,mBAAA,CAAuD,QAAvDkC,WAAuD,EAAAxB,gBAAA,CAArCC,MAAA,CAAAC,QAAQ,GAAGD,MAAA,CAAAE,IAAI,CAACsB,YAAY,iB,GAElDnC,mBAAA,CAGM,OAHNoC,WAGM,GAFFpC,mBAAA,CAAyF,OAAzFqC,WAAyF,G,kCAArEhC,IAAA,CAAAC,EAAE,gBAAc,GAAC,iBAAAN,mBAAA,CAA8C,QAA9CsC,WAA8C,EAAA5B,gBAAA,CAAzBC,MAAA,CAAAE,IAAI,CAAC0B,WAAW,iB,GAC1EvC,mBAAA,CAAwF,OAAxFwC,WAAwF,G,kCAApEnC,IAAA,CAAAC,EAAE,kBAAgB,GAAC,iBAAAN,mBAAA,CAA2C,QAA3CyC,WAA2C,EAAA/B,gBAAA,CAAtBC,MAAA,CAAAE,IAAI,CAAC6B,QAAQ,iB,OAGjF1C,mBAAA,CASM,OATN2C,WASM,GARF3C,mBAAA,CAGM,OAHN4C,WAGM,GAFF5C,mBAAA,CAA6C,QAA7C6C,WAA6C,EAAAnC,gBAAA,CAA3BL,IAAA,CAAAC,EAAE,kCACpBN,mBAAA,CAAuD,QAAvD8C,WAAuD,EAAApC,gBAAA,CAArCC,MAAA,CAAAC,QAAQ,GAAGD,MAAA,CAAAE,IAAI,CAACkC,YAAY,iB,GAElD/C,mBAAA,CAGM,OAHNgD,WAGM,GAFFhD,mBAAA,CAAyF,OAAzFiD,WAAyF,G,kCAArE5C,IAAA,CAAAC,EAAE,gBAAc,GAAC,iBAAAN,mBAAA,CAA8C,QAA9CkD,WAA8C,EAAAxC,gBAAA,CAAzBC,MAAA,CAAAE,IAAI,CAACsC,WAAW,iB,GAC1EnD,mBAAA,CAAwF,OAAxFoD,WAAwF,G,kCAApE/C,IAAA,CAAAC,EAAE,kBAAgB,GAAC,iBAAAN,mBAAA,CAA2C,QAA3CqD,WAA2C,EAAA3C,gBAAA,CAAtBC,MAAA,CAAAE,IAAI,CAACyC,QAAQ,iB,OAGjFtD,mBAAA,CASM,OATNuD,WASM,GARFvD,mBAAA,CAGM,OAHNwD,WAGM,GAFFxD,mBAAA,CAA+C,QAA/CyD,WAA+C,EAAA/C,gBAAA,CAA7BL,IAAA,CAAAC,EAAE,oCACpBN,mBAAA,CAAuD,QAAvD0D,WAAuD,EAAAhD,gBAAA,CAArCC,MAAA,CAAAC,QAAQ,GAAGD,MAAA,CAAAE,IAAI,CAAC8C,YAAY,iB,GAElD3D,mBAAA,CAGM,OAHN4D,WAGM,GAFF5D,mBAAA,CAAyF,OAAzF6D,WAAyF,G,kCAArExD,IAAA,CAAAC,EAAE,gBAAc,GAAC,iBAAAN,mBAAA,CAA8C,QAA9C8D,WAA8C,EAAApD,gBAAA,CAAzBC,MAAA,CAAAE,IAAI,CAACkD,WAAW,iB,GAC1E/D,mBAAA,CAAwF,OAAxFgE,WAAwF,G,kCAApE3D,IAAA,CAAAC,EAAE,kBAAgB,GAAC,iBAAAN,mBAAA,CAA2C,QAA3CiE,WAA2C,EAAAvD,gBAAA,CAAtBC,MAAA,CAAAE,IAAI,CAACqD,QAAQ,iB,aAM1FhD,mBAAA,60HA+De,EACdA,mBAAA,YAAe,C"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}