{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _Grid from \"./Grid.mjs\";\nvar Grid = withInstall(_Grid);\nvar stdin_default = Grid;\nexport { Grid, stdin_default as default };", "map": {"version": 3, "names": ["withInstall", "_Grid", "Grid", "stdin_default", "default"], "sources": ["C:/Users/<USER>/Downloads/Telegram Desktop/vue3_3.0/vue3_3.0/node_modules/vant/es/grid/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _Grid from \"./Grid.mjs\";\nconst Grid = withInstall(_Grid);\nvar stdin_default = Grid;\nexport {\n  Grid,\n  stdin_default as default\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,KAAK,MAAM,YAAY;AAC9B,IAAMC,IAAI,GAAGF,WAAW,CAACC,KAAK,CAAC;AAC/B,IAAIE,aAAa,GAAGD,IAAI;AACxB,SACEA,IAAI,EACJC,aAAa,IAAIC,OAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}