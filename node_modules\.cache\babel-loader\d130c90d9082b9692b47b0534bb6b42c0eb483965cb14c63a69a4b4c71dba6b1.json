{"ast": null, "code": "function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nfunction _iterableToArrayLimit(arr, i) { var _i = null == arr ? null : \"undefined\" != typeof Symbol && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (null != _i) { var _s, _e, _x, _r, _arr = [], _n = !0, _d = !1; try { if (_x = (_i = _i.call(arr)).next, 0 === i) { if (Object(_i) !== _i) return; _n = !1; } else for (; !(_n = (_s = _x.call(_i)).done) && (_arr.push(_s.value), _arr.length !== i); _n = !0); } catch (err) { _d = !0, _e = err; } finally { try { if (!_n && null != _i.return && (_r = _i.return(), Object(_r) !== _r)) return; } finally { if (_d) throw _e; } } return _arr; } }\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\nimport { createVNode as _createVNode } from \"vue\";\nimport { computed, defineComponent } from \"vue\";\nimport { isDef, addUnit, isNumeric, truthProp, numericProp, makeStringProp, createNamespace } from \"../utils/index.mjs\";\nvar _createNamespace = createNamespace(\"badge\"),\n  _createNamespace2 = _slicedToArray(_createNamespace, 2),\n  name = _createNamespace2[0],\n  bem = _createNamespace2[1];\nvar badgeProps = {\n  dot: Boolean,\n  max: numericProp,\n  tag: makeStringProp(\"div\"),\n  color: String,\n  offset: Array,\n  content: numericProp,\n  showZero: truthProp,\n  position: makeStringProp(\"top-right\")\n};\nvar stdin_default = defineComponent({\n  name: name,\n  props: badgeProps,\n  setup: function setup(props, _ref) {\n    var slots = _ref.slots;\n    var hasContent = function hasContent() {\n      if (slots.content) {\n        return true;\n      }\n      var content = props.content,\n        showZero = props.showZero;\n      return isDef(content) && content !== \"\" && (showZero || content !== 0 && content !== \"0\");\n    };\n    var renderContent = function renderContent() {\n      var dot = props.dot,\n        max = props.max,\n        content = props.content;\n      if (!dot && hasContent()) {\n        if (slots.content) {\n          return slots.content();\n        }\n        if (isDef(max) && isNumeric(content) && +content > max) {\n          return \"\".concat(max, \"+\");\n        }\n        return content;\n      }\n    };\n    var style = computed(function () {\n      var style2 = {\n        background: props.color\n      };\n      if (props.offset) {\n        var _props$offset = _slicedToArray(props.offset, 2),\n          x = _props$offset[0],\n          y = _props$offset[1];\n        if (slots.default) {\n          style2.top = addUnit(y);\n          if (typeof x === \"number\") {\n            style2.right = addUnit(-x);\n          } else {\n            style2.right = x.startsWith(\"-\") ? x.replace(\"-\", \"\") : \"-\".concat(x);\n          }\n        } else {\n          style2.marginTop = addUnit(y);\n          style2.marginLeft = addUnit(x);\n        }\n      }\n      return style2;\n    });\n    var renderBadge = function renderBadge() {\n      if (hasContent() || props.dot) {\n        return _createVNode(\"div\", {\n          \"class\": bem([props.position, {\n            dot: props.dot,\n            fixed: !!slots.default\n          }]),\n          \"style\": style.value\n        }, [renderContent()]);\n      }\n    };\n    return function () {\n      if (slots.default) {\n        var tag = props.tag;\n        return _createVNode(tag, {\n          \"class\": bem(\"wrapper\")\n        }, {\n          default: function _default() {\n            return [slots.default(), renderBadge()];\n          }\n        });\n      }\n      return renderBadge();\n    };\n  }\n});\nexport { stdin_default as default };", "map": {"version": 3, "names": ["createVNode", "_createVNode", "computed", "defineComponent", "isDef", "addUnit", "isNumeric", "truthProp", "numericProp", "makeStringProp", "createNamespace", "_createNamespace", "_createNamespace2", "_slicedToArray", "name", "bem", "badgeProps", "dot", "Boolean", "max", "tag", "color", "String", "offset", "Array", "content", "showZero", "position", "stdin_default", "props", "setup", "_ref", "slots", "<PERSON><PERSON><PERSON><PERSON>", "renderContent", "concat", "style", "style2", "background", "_props$offset", "x", "y", "default", "top", "right", "startsWith", "replace", "marginTop", "marginLeft", "renderBadge", "fixed", "value", "_default"], "sources": ["C:/Users/<USER>/Downloads/Telegram Desktop/vue3_3.0/vue3_3.0/node_modules/vant/es/badge/Badge.mjs"], "sourcesContent": ["import { createVNode as _createVNode } from \"vue\";\nimport { computed, defineComponent } from \"vue\";\nimport { isDef, addUnit, isNumeric, truthProp, numericProp, makeStringProp, createNamespace } from \"../utils/index.mjs\";\nconst [name, bem] = createNamespace(\"badge\");\nconst badgeProps = {\n  dot: Boolean,\n  max: numericProp,\n  tag: makeStringProp(\"div\"),\n  color: String,\n  offset: Array,\n  content: numericProp,\n  showZero: truthProp,\n  position: makeStringProp(\"top-right\")\n};\nvar stdin_default = defineComponent({\n  name,\n  props: badgeProps,\n  setup(props, {\n    slots\n  }) {\n    const hasContent = () => {\n      if (slots.content) {\n        return true;\n      }\n      const {\n        content,\n        showZero\n      } = props;\n      return isDef(content) && content !== \"\" && (showZero || content !== 0 && content !== \"0\");\n    };\n    const renderContent = () => {\n      const {\n        dot,\n        max,\n        content\n      } = props;\n      if (!dot && hasContent()) {\n        if (slots.content) {\n          return slots.content();\n        }\n        if (isDef(max) && isNumeric(content) && +content > max) {\n          return `${max}+`;\n        }\n        return content;\n      }\n    };\n    const style = computed(() => {\n      const style2 = {\n        background: props.color\n      };\n      if (props.offset) {\n        const [x, y] = props.offset;\n        if (slots.default) {\n          style2.top = addUnit(y);\n          if (typeof x === \"number\") {\n            style2.right = addUnit(-x);\n          } else {\n            style2.right = x.startsWith(\"-\") ? x.replace(\"-\", \"\") : `-${x}`;\n          }\n        } else {\n          style2.marginTop = addUnit(y);\n          style2.marginLeft = addUnit(x);\n        }\n      }\n      return style2;\n    });\n    const renderBadge = () => {\n      if (hasContent() || props.dot) {\n        return _createVNode(\"div\", {\n          \"class\": bem([props.position, {\n            dot: props.dot,\n            fixed: !!slots.default\n          }]),\n          \"style\": style.value\n        }, [renderContent()]);\n      }\n    };\n    return () => {\n      if (slots.default) {\n        const {\n          tag\n        } = props;\n        return _createVNode(tag, {\n          \"class\": bem(\"wrapper\")\n        }, {\n          default: () => [slots.default(), renderBadge()]\n        });\n      }\n      return renderBadge();\n    };\n  }\n});\nexport {\n  stdin_default as default\n};\n"], "mappings": ";;;;;;AAAA,SAASA,WAAW,IAAIC,YAAY,QAAQ,KAAK;AACjD,SAASC,QAAQ,EAAEC,eAAe,QAAQ,KAAK;AAC/C,SAASC,KAAK,EAAEC,OAAO,EAAEC,SAAS,EAAEC,SAAS,EAAEC,WAAW,EAAEC,cAAc,EAAEC,eAAe,QAAQ,oBAAoB;AACvH,IAAAC,gBAAA,GAAoBD,eAAe,CAAC,OAAO,CAAC;EAAAE,iBAAA,GAAAC,cAAA,CAAAF,gBAAA;EAArCG,IAAI,GAAAF,iBAAA;EAAEG,GAAG,GAAAH,iBAAA;AAChB,IAAMI,UAAU,GAAG;EACjBC,GAAG,EAAEC,OAAO;EACZC,GAAG,EAAEX,WAAW;EAChBY,GAAG,EAAEX,cAAc,CAAC,KAAK,CAAC;EAC1BY,KAAK,EAAEC,MAAM;EACbC,MAAM,EAAEC,KAAK;EACbC,OAAO,EAAEjB,WAAW;EACpBkB,QAAQ,EAAEnB,SAAS;EACnBoB,QAAQ,EAAElB,cAAc,CAAC,WAAW;AACtC,CAAC;AACD,IAAImB,aAAa,GAAGzB,eAAe,CAAC;EAClCW,IAAI,EAAJA,IAAI;EACJe,KAAK,EAAEb,UAAU;EACjBc,KAAK,WAAAA,MAACD,KAAK,EAAAE,IAAA,EAER;IAAA,IADDC,KAAK,GAAAD,IAAA,CAALC,KAAK;IAEL,IAAMC,UAAU,GAAG,SAAbA,UAAUA,CAAA,EAAS;MACvB,IAAID,KAAK,CAACP,OAAO,EAAE;QACjB,OAAO,IAAI;MACb;MACA,IACEA,OAAO,GAELI,KAAK,CAFPJ,OAAO;QACPC,QAAQ,GACNG,KAAK,CADPH,QAAQ;MAEV,OAAOtB,KAAK,CAACqB,OAAO,CAAC,IAAIA,OAAO,KAAK,EAAE,KAAKC,QAAQ,IAAID,OAAO,KAAK,CAAC,IAAIA,OAAO,KAAK,GAAG,CAAC;IAC3F,CAAC;IACD,IAAMS,aAAa,GAAG,SAAhBA,aAAaA,CAAA,EAAS;MAC1B,IACEjB,GAAG,GAGDY,KAAK,CAHPZ,GAAG;QACHE,GAAG,GAEDU,KAAK,CAFPV,GAAG;QACHM,OAAO,GACLI,KAAK,CADPJ,OAAO;MAET,IAAI,CAACR,GAAG,IAAIgB,UAAU,CAAC,CAAC,EAAE;QACxB,IAAID,KAAK,CAACP,OAAO,EAAE;UACjB,OAAOO,KAAK,CAACP,OAAO,CAAC,CAAC;QACxB;QACA,IAAIrB,KAAK,CAACe,GAAG,CAAC,IAAIb,SAAS,CAACmB,OAAO,CAAC,IAAI,CAACA,OAAO,GAAGN,GAAG,EAAE;UACtD,UAAAgB,MAAA,CAAUhB,GAAG;QACf;QACA,OAAOM,OAAO;MAChB;IACF,CAAC;IACD,IAAMW,KAAK,GAAGlC,QAAQ,CAAC,YAAM;MAC3B,IAAMmC,MAAM,GAAG;QACbC,UAAU,EAAET,KAAK,CAACR;MACpB,CAAC;MACD,IAAIQ,KAAK,CAACN,MAAM,EAAE;QAChB,IAAAgB,aAAA,GAAA1B,cAAA,CAAegB,KAAK,CAACN,MAAM;UAApBiB,CAAC,GAAAD,aAAA;UAAEE,CAAC,GAAAF,aAAA;QACX,IAAIP,KAAK,CAACU,OAAO,EAAE;UACjBL,MAAM,CAACM,GAAG,GAAGtC,OAAO,CAACoC,CAAC,CAAC;UACvB,IAAI,OAAOD,CAAC,KAAK,QAAQ,EAAE;YACzBH,MAAM,CAACO,KAAK,GAAGvC,OAAO,CAAC,CAACmC,CAAC,CAAC;UAC5B,CAAC,MAAM;YACLH,MAAM,CAACO,KAAK,GAAGJ,CAAC,CAACK,UAAU,CAAC,GAAG,CAAC,GAAGL,CAAC,CAACM,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,OAAAX,MAAA,CAAOK,CAAC,CAAE;UACjE;QACF,CAAC,MAAM;UACLH,MAAM,CAACU,SAAS,GAAG1C,OAAO,CAACoC,CAAC,CAAC;UAC7BJ,MAAM,CAACW,UAAU,GAAG3C,OAAO,CAACmC,CAAC,CAAC;QAChC;MACF;MACA,OAAOH,MAAM;IACf,CAAC,CAAC;IACF,IAAMY,WAAW,GAAG,SAAdA,WAAWA,CAAA,EAAS;MACxB,IAAIhB,UAAU,CAAC,CAAC,IAAIJ,KAAK,CAACZ,GAAG,EAAE;QAC7B,OAAOhB,YAAY,CAAC,KAAK,EAAE;UACzB,OAAO,EAAEc,GAAG,CAAC,CAACc,KAAK,CAACF,QAAQ,EAAE;YAC5BV,GAAG,EAAEY,KAAK,CAACZ,GAAG;YACdiC,KAAK,EAAE,CAAC,CAAClB,KAAK,CAACU;UACjB,CAAC,CAAC,CAAC;UACH,OAAO,EAAEN,KAAK,CAACe;QACjB,CAAC,EAAE,CAACjB,aAAa,CAAC,CAAC,CAAC,CAAC;MACvB;IACF,CAAC;IACD,OAAO,YAAM;MACX,IAAIF,KAAK,CAACU,OAAO,EAAE;QACjB,IACEtB,GAAG,GACDS,KAAK,CADPT,GAAG;QAEL,OAAOnB,YAAY,CAACmB,GAAG,EAAE;UACvB,OAAO,EAAEL,GAAG,CAAC,SAAS;QACxB,CAAC,EAAE;UACD2B,OAAO,EAAE,SAAAU,SAAA;YAAA,OAAM,CAACpB,KAAK,CAACU,OAAO,CAAC,CAAC,EAAEO,WAAW,CAAC,CAAC,CAAC;UAAA;QACjD,CAAC,CAAC;MACJ;MACA,OAAOA,WAAW,CAAC,CAAC;IACtB,CAAC;EACH;AACF,CAAC,CAAC;AACF,SACErB,aAAa,IAAIc,OAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}