{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, pushScopeId as _pushScopeId, popScopeId as _popScopeId } from \"vue\";\nvar _withScopeId = function _withScopeId(n) {\n  return _pushScopeId(\"data-v-498142aa\"), n = n(), _popScopeId(), n;\n};\nvar _hoisted_1 = {\n  class: \"home_box\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_router_view = _resolveComponent(\"router-view\");\n  var _component_footer_demo = _resolveComponent(\"footer-demo\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_router_view, {\n    class: \"pore\",\n    onHideFooter: $setup.hideFooter\n  }, null, 8 /* PROPS */, [\"onHideFooter\"]), _createVNode(_component_footer_demo)]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_router_view", "on<PERSON><PERSON><PERSON><PERSON>er", "$setup", "hideFooter", "_component_footer_demo"], "sources": ["C:\\Users\\<USER>\\Desktop\\vue3_3.0\\src\\views\\index\\index.vue"], "sourcesContent": ["<template>\r\n    <div class=\"home_box\">\r\n        <router-view class=\"pore\" @hideFooter=\"hideFooter\"/>\r\n        <footer-demo></footer-demo>\r\n    </div>\r\n    \r\n</template>\r\n<script>\r\nimport {ref} from 'vue'\r\nimport footerDemo from '@/components/footer.vue'\r\nexport default {\r\n    components:{footerDemo},\r\n    setup(){\r\n        const showFooter = ref(true)\r\n        console.log(showFooter.value)\r\n        const hideFooter = (val) => {\r\n            showFooter.value = !val\r\n        }\r\n        return{hideFooter,showFooter}\r\n    }\r\n}\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.home_box{\r\n    height: 100vh;\r\n    display: flex;\r\n    flex-direction: column;\r\n    .pore{\r\n        flex: 1;\r\n        overflow: auto;\r\n        padding-bottom:140px;\r\n        display: flex;\r\n        flex-direction: column;\r\n    }\r\n\r\n}\r\n</style>"], "mappings": ";;;;;EACSA,KAAK,EAAC;AAAU;;;;uBAArBC,mBAAA,CAGM,OAHNC,UAGM,GAFFC,YAAA,CAAoDC,sBAAA;IAAvCJ,KAAK,EAAC,MAAM;IAAEK,YAAU,EAAEC,MAAA,CAAAC;6CACvCJ,YAAA,CAA2BK,sBAAA,E"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}