{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _ContactCard from \"./ContactCard.mjs\";\nvar ContactCard = withInstall(_ContactCard);\nvar stdin_default = ContactCard;\nexport { ContactCard, stdin_default as default };", "map": {"version": 3, "names": ["withInstall", "_ContactCard", "ContactCard", "stdin_default", "default"], "sources": ["C:/Users/<USER>/Downloads/Telegram Desktop/vue3_3.0/vue3_3.0/node_modules/vant/es/contact-card/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _ContactCard from \"./ContactCard.mjs\";\nconst ContactCard = withInstall(_ContactCard);\nvar stdin_default = ContactCard;\nexport {\n  ContactCard,\n  stdin_default as default\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,YAAY,MAAM,mBAAmB;AAC5C,IAAMC,WAAW,GAAGF,WAAW,CAACC,YAAY,CAAC;AAC7C,IAAIE,aAAa,GAAGD,WAAW;AAC/B,SACEA,WAAW,EACXC,aAAa,IAAIC,OAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}