{"ast": null, "code": "import { ref, onBeforeUpdate } from \"vue\";\nfunction useRefs() {\n  var refs = ref([]);\n  var cache = [];\n  onBeforeUpdate(function () {\n    refs.value = [];\n  });\n  var setRefs = function setRefs(index) {\n    if (!cache[index]) {\n      cache[index] = function (el) {\n        refs.value[index] = el;\n      };\n    }\n    return cache[index];\n  };\n  return [refs, setRefs];\n}\nexport { useRefs };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}