{"ast": null, "code": "function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nfunction _iterableToArrayLimit(arr, i) { var _i = null == arr ? null : \"undefined\" != typeof Symbol && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (null != _i) { var _s, _e, _x, _r, _arr = [], _n = !0, _d = !1; try { if (_x = (_i = _i.call(arr)).next, 0 === i) { if (Object(_i) !== _i) return; _n = !1; } else for (; !(_n = (_s = _x.call(_i)).done) && (_arr.push(_s.value), _arr.length !== i); _n = !0); } catch (err) { _d = !0, _e = err; } finally { try { if (!_n && null != _i.return && (_r = _i.return(), Object(_r) !== _r)) return; } finally { if (_d) throw _e; } } return _arr; } }\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\nimport { createVNode as _createVNode, mergeProps as _mergeProps } from \"vue\";\nimport { defineComponent } from \"vue\";\nimport { pick, createNamespace } from \"../utils/index.mjs\";\nimport { RADIO_KEY } from \"../radio-group/RadioGroup.mjs\";\nimport { useParent } from \"@vant/use\";\nimport Checker, { checkerProps } from \"../checkbox/Checker.mjs\";\nvar _createNamespace = createNamespace(\"radio\"),\n  _createNamespace2 = _slicedToArray(_createNamespace, 2),\n  name = _createNamespace2[0],\n  bem = _createNamespace2[1];\nvar stdin_default = defineComponent({\n  name: name,\n  props: checkerProps,\n  emits: [\"update:modelValue\"],\n  setup: function setup(props, _ref) {\n    var emit = _ref.emit,\n      slots = _ref.slots;\n    var _useParent = useParent(RADIO_KEY),\n      parent = _useParent.parent;\n    var checked = function checked() {\n      var value = parent ? parent.props.modelValue : props.modelValue;\n      return value === props.name;\n    };\n    var toggle = function toggle() {\n      if (parent) {\n        parent.updateValue(props.name);\n      } else {\n        emit(\"update:modelValue\", props.name);\n      }\n    };\n    return function () {\n      return _createVNode(Checker, _mergeProps({\n        \"bem\": bem,\n        \"role\": \"radio\",\n        \"parent\": parent,\n        \"checked\": checked(),\n        \"onToggle\": toggle\n      }, props), pick(slots, [\"default\", \"icon\"]));\n    };\n  }\n});\nexport { stdin_default as default };", "map": {"version": 3, "names": ["createVNode", "_createVNode", "mergeProps", "_mergeProps", "defineComponent", "pick", "createNamespace", "RADIO_KEY", "useParent", "Checker", "checkerProps", "_createNamespace", "_createNamespace2", "_slicedToArray", "name", "bem", "stdin_default", "props", "emits", "setup", "_ref", "emit", "slots", "_useParent", "parent", "checked", "value", "modelValue", "toggle", "updateValue", "default"], "sources": ["C:/Users/<USER>/Downloads/Telegram Desktop/vue3_3.0/vue3_3.0/node_modules/vant/es/radio/Radio.mjs"], "sourcesContent": ["import { createVNode as _createVNode, mergeProps as _mergeProps } from \"vue\";\nimport { defineComponent } from \"vue\";\nimport { pick, createNamespace } from \"../utils/index.mjs\";\nimport { RADIO_KEY } from \"../radio-group/RadioGroup.mjs\";\nimport { useParent } from \"@vant/use\";\nimport Checker, { checkerProps } from \"../checkbox/Checker.mjs\";\nconst [name, bem] = createNamespace(\"radio\");\nvar stdin_default = defineComponent({\n  name,\n  props: checkerProps,\n  emits: [\"update:modelValue\"],\n  setup(props, {\n    emit,\n    slots\n  }) {\n    const {\n      parent\n    } = useParent(RADIO_KEY);\n    const checked = () => {\n      const value = parent ? parent.props.modelValue : props.modelValue;\n      return value === props.name;\n    };\n    const toggle = () => {\n      if (parent) {\n        parent.updateValue(props.name);\n      } else {\n        emit(\"update:modelValue\", props.name);\n      }\n    };\n    return () => _createVNode(Checker, _mergeProps({\n      \"bem\": bem,\n      \"role\": \"radio\",\n      \"parent\": parent,\n      \"checked\": checked(),\n      \"onToggle\": toggle\n    }, props), pick(slots, [\"default\", \"icon\"]));\n  }\n});\nexport {\n  stdin_default as default\n};\n"], "mappings": ";;;;;;AAAA,SAASA,WAAW,IAAIC,YAAY,EAAEC,UAAU,IAAIC,WAAW,QAAQ,KAAK;AAC5E,SAASC,eAAe,QAAQ,KAAK;AACrC,SAASC,IAAI,EAAEC,eAAe,QAAQ,oBAAoB;AAC1D,SAASC,SAAS,QAAQ,+BAA+B;AACzD,SAASC,SAAS,QAAQ,WAAW;AACrC,OAAOC,OAAO,IAAIC,YAAY,QAAQ,yBAAyB;AAC/D,IAAAC,gBAAA,GAAoBL,eAAe,CAAC,OAAO,CAAC;EAAAM,iBAAA,GAAAC,cAAA,CAAAF,gBAAA;EAArCG,IAAI,GAAAF,iBAAA;EAAEG,GAAG,GAAAH,iBAAA;AAChB,IAAII,aAAa,GAAGZ,eAAe,CAAC;EAClCU,IAAI,EAAJA,IAAI;EACJG,KAAK,EAAEP,YAAY;EACnBQ,KAAK,EAAE,CAAC,mBAAmB,CAAC;EAC5BC,KAAK,WAAAA,MAACF,KAAK,EAAAG,IAAA,EAGR;IAAA,IAFDC,IAAI,GAAAD,IAAA,CAAJC,IAAI;MACJC,KAAK,GAAAF,IAAA,CAALE,KAAK;IAEL,IAAAC,UAAA,GAEIf,SAAS,CAACD,SAAS,CAAC;MADtBiB,MAAM,GAAAD,UAAA,CAANC,MAAM;IAER,IAAMC,OAAO,GAAG,SAAVA,OAAOA,CAAA,EAAS;MACpB,IAAMC,KAAK,GAAGF,MAAM,GAAGA,MAAM,CAACP,KAAK,CAACU,UAAU,GAAGV,KAAK,CAACU,UAAU;MACjE,OAAOD,KAAK,KAAKT,KAAK,CAACH,IAAI;IAC7B,CAAC;IACD,IAAMc,MAAM,GAAG,SAATA,MAAMA,CAAA,EAAS;MACnB,IAAIJ,MAAM,EAAE;QACVA,MAAM,CAACK,WAAW,CAACZ,KAAK,CAACH,IAAI,CAAC;MAChC,CAAC,MAAM;QACLO,IAAI,CAAC,mBAAmB,EAAEJ,KAAK,CAACH,IAAI,CAAC;MACvC;IACF,CAAC;IACD,OAAO;MAAA,OAAMb,YAAY,CAACQ,OAAO,EAAEN,WAAW,CAAC;QAC7C,KAAK,EAAEY,GAAG;QACV,MAAM,EAAE,OAAO;QACf,QAAQ,EAAES,MAAM;QAChB,SAAS,EAAEC,OAAO,CAAC,CAAC;QACpB,UAAU,EAAEG;MACd,CAAC,EAAEX,KAAK,CAAC,EAAEZ,IAAI,CAACiB,KAAK,EAAE,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC,CAAC;IAAA;EAC9C;AACF,CAAC,CAAC;AACF,SACEN,aAAa,IAAIc,OAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}