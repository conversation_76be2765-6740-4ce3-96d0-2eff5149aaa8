{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _Divider from \"./Divider.mjs\";\nvar Divider = withInstall(_Divider);\nvar stdin_default = Divider;\nexport { Divider, stdin_default as default };", "map": {"version": 3, "names": ["withInstall", "_Divider", "Divider", "stdin_default", "default"], "sources": ["C:/Users/<USER>/Downloads/Telegram Desktop/vue3_3.0/vue3_3.0/node_modules/vant/es/divider/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _Divider from \"./Divider.mjs\";\nconst Divider = withInstall(_Divider);\nvar stdin_default = Divider;\nexport {\n  Divider,\n  stdin_default as default\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,QAAQ,MAAM,eAAe;AACpC,IAAMC,OAAO,GAAGF,WAAW,CAACC,QAAQ,CAAC;AACrC,IAAIE,aAAa,GAAGD,OAAO;AAC3B,SACEA,OAAO,EACPC,aAAa,IAAIC,OAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}