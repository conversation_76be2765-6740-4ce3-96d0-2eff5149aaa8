{"ast": null, "code": "function _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread(); }\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _iterableToArray(iter) { if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter); }\nfunction _arrayWithoutHoles(arr) { if (Array.isArray(arr)) return _arrayLikeToArray(arr); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nimport { isObject, isPromise, isFunction, getRootScrollTop, setRootScrollTop } from \"../utils/index.mjs\";\nfunction isEmptyValue(value) {\n  if (Array.isArray(value)) {\n    return !value.length;\n  }\n  if (value === 0) {\n    return false;\n  }\n  return !value;\n}\nfunction runSyncRule(value, rule) {\n  if (isEmptyValue(value)) {\n    if (rule.required) {\n      return false;\n    }\n    if (rule.validateEmpty === false) {\n      return true;\n    }\n  }\n  if (rule.pattern && !rule.pattern.test(String(value))) {\n    return false;\n  }\n  return true;\n}\nfunction runRuleValidator(value, rule) {\n  return new Promise(function (resolve) {\n    var returnVal = rule.validator(value, rule);\n    if (isPromise(returnVal)) {\n      returnVal.then(resolve);\n      return;\n    }\n    resolve(returnVal);\n  });\n}\nfunction getRuleMessage(value, rule) {\n  var message = rule.message;\n  if (isFunction(message)) {\n    return message(value, rule);\n  }\n  return message || \"\";\n}\nfunction startComposing(_ref) {\n  var target = _ref.target;\n  target.composing = true;\n}\nfunction endComposing(_ref2) {\n  var target = _ref2.target;\n  if (target.composing) {\n    target.composing = false;\n    target.dispatchEvent(new Event(\"input\"));\n  }\n}\nfunction resizeTextarea(input, autosize) {\n  var scrollTop = getRootScrollTop();\n  input.style.height = \"auto\";\n  var height = input.scrollHeight;\n  if (isObject(autosize)) {\n    var maxHeight = autosize.maxHeight,\n      minHeight = autosize.minHeight;\n    if (maxHeight !== void 0) {\n      height = Math.min(height, maxHeight);\n    }\n    if (minHeight !== void 0) {\n      height = Math.max(height, minHeight);\n    }\n  }\n  if (height) {\n    input.style.height = \"\".concat(height, \"px\");\n    setRootScrollTop(scrollTop);\n  }\n}\nfunction mapInputType(type) {\n  if (type === \"number\") {\n    return {\n      type: \"text\",\n      inputmode: \"decimal\"\n    };\n  }\n  if (type === \"digit\") {\n    return {\n      type: \"tel\",\n      inputmode: \"numeric\"\n    };\n  }\n  return {\n    type: type\n  };\n}\nfunction getStringLength(str) {\n  return _toConsumableArray(str).length;\n}\nfunction cutString(str, maxlength) {\n  return _toConsumableArray(str).slice(0, maxlength).join(\"\");\n}\nexport { cutString, endComposing, getRuleMessage, getStringLength, isEmptyValue, mapInputType, resizeTextarea, runRuleValidator, runSyncRule, startComposing };", "map": {"version": 3, "names": ["isObject", "isPromise", "isFunction", "getRootScrollTop", "setRootScrollTop", "isEmptyValue", "value", "Array", "isArray", "length", "runSyncRule", "rule", "required", "validateEmpty", "pattern", "test", "String", "runRuleValidator", "Promise", "resolve", "returnVal", "validator", "then", "getRuleMessage", "message", "startComposing", "_ref", "target", "composing", "endComposing", "_ref2", "dispatchEvent", "Event", "resizeTextarea", "input", "autosize", "scrollTop", "style", "height", "scrollHeight", "maxHeight", "minHeight", "Math", "min", "max", "concat", "mapInputType", "type", "inputmode", "getStringLength", "str", "_toConsumableArray", "cutString", "maxlength", "slice", "join"], "sources": ["C:/Users/<USER>/Downloads/Telegram Desktop/vue3_3.0/vue3_3.0/node_modules/vant/es/field/utils.mjs"], "sourcesContent": ["import {\n  isObject,\n  isPromise,\n  isFunction,\n  getRootScrollTop,\n  setRootScrollTop\n} from \"../utils/index.mjs\";\nfunction isEmptyValue(value) {\n  if (Array.isArray(value)) {\n    return !value.length;\n  }\n  if (value === 0) {\n    return false;\n  }\n  return !value;\n}\nfunction runSyncRule(value, rule) {\n  if (isEmptyValue(value)) {\n    if (rule.required) {\n      return false;\n    }\n    if (rule.validateEmpty === false) {\n      return true;\n    }\n  }\n  if (rule.pattern && !rule.pattern.test(String(value))) {\n    return false;\n  }\n  return true;\n}\nfunction runRuleValidator(value, rule) {\n  return new Promise((resolve) => {\n    const returnVal = rule.validator(value, rule);\n    if (isPromise(returnVal)) {\n      returnVal.then(resolve);\n      return;\n    }\n    resolve(returnVal);\n  });\n}\nfunction getRuleMessage(value, rule) {\n  const { message } = rule;\n  if (isFunction(message)) {\n    return message(value, rule);\n  }\n  return message || \"\";\n}\nfunction startComposing({ target }) {\n  target.composing = true;\n}\nfunction endComposing({ target }) {\n  if (target.composing) {\n    target.composing = false;\n    target.dispatchEvent(new Event(\"input\"));\n  }\n}\nfunction resizeTextarea(input, autosize) {\n  const scrollTop = getRootScrollTop();\n  input.style.height = \"auto\";\n  let height = input.scrollHeight;\n  if (isObject(autosize)) {\n    const { maxHeight, minHeight } = autosize;\n    if (maxHeight !== void 0) {\n      height = Math.min(height, maxHeight);\n    }\n    if (minHeight !== void 0) {\n      height = Math.max(height, minHeight);\n    }\n  }\n  if (height) {\n    input.style.height = `${height}px`;\n    setRootScrollTop(scrollTop);\n  }\n}\nfunction mapInputType(type) {\n  if (type === \"number\") {\n    return {\n      type: \"text\",\n      inputmode: \"decimal\"\n    };\n  }\n  if (type === \"digit\") {\n    return {\n      type: \"tel\",\n      inputmode: \"numeric\"\n    };\n  }\n  return { type };\n}\nfunction getStringLength(str) {\n  return [...str].length;\n}\nfunction cutString(str, maxlength) {\n  return [...str].slice(0, maxlength).join(\"\");\n}\nexport {\n  cutString,\n  endComposing,\n  getRuleMessage,\n  getStringLength,\n  isEmptyValue,\n  mapInputType,\n  resizeTextarea,\n  runRuleValidator,\n  runSyncRule,\n  startComposing\n};\n"], "mappings": ";;;;;;AAAA,SACEA,QAAQ,EACRC,SAAS,EACTC,UAAU,EACVC,gBAAgB,EAChBC,gBAAgB,QACX,oBAAoB;AAC3B,SAASC,YAAYA,CAACC,KAAK,EAAE;EAC3B,IAAIC,KAAK,CAACC,OAAO,CAACF,KAAK,CAAC,EAAE;IACxB,OAAO,CAACA,KAAK,CAACG,MAAM;EACtB;EACA,IAAIH,KAAK,KAAK,CAAC,EAAE;IACf,OAAO,KAAK;EACd;EACA,OAAO,CAACA,KAAK;AACf;AACA,SAASI,WAAWA,CAACJ,KAAK,EAAEK,IAAI,EAAE;EAChC,IAAIN,YAAY,CAACC,KAAK,CAAC,EAAE;IACvB,IAAIK,IAAI,CAACC,QAAQ,EAAE;MACjB,OAAO,KAAK;IACd;IACA,IAAID,IAAI,CAACE,aAAa,KAAK,KAAK,EAAE;MAChC,OAAO,IAAI;IACb;EACF;EACA,IAAIF,IAAI,CAACG,OAAO,IAAI,CAACH,IAAI,CAACG,OAAO,CAACC,IAAI,CAACC,MAAM,CAACV,KAAK,CAAC,CAAC,EAAE;IACrD,OAAO,KAAK;EACd;EACA,OAAO,IAAI;AACb;AACA,SAASW,gBAAgBA,CAACX,KAAK,EAAEK,IAAI,EAAE;EACrC,OAAO,IAAIO,OAAO,CAAC,UAACC,OAAO,EAAK;IAC9B,IAAMC,SAAS,GAAGT,IAAI,CAACU,SAAS,CAACf,KAAK,EAAEK,IAAI,CAAC;IAC7C,IAAIV,SAAS,CAACmB,SAAS,CAAC,EAAE;MACxBA,SAAS,CAACE,IAAI,CAACH,OAAO,CAAC;MACvB;IACF;IACAA,OAAO,CAACC,SAAS,CAAC;EACpB,CAAC,CAAC;AACJ;AACA,SAASG,cAAcA,CAACjB,KAAK,EAAEK,IAAI,EAAE;EACnC,IAAQa,OAAO,GAAKb,IAAI,CAAhBa,OAAO;EACf,IAAItB,UAAU,CAACsB,OAAO,CAAC,EAAE;IACvB,OAAOA,OAAO,CAAClB,KAAK,EAAEK,IAAI,CAAC;EAC7B;EACA,OAAOa,OAAO,IAAI,EAAE;AACtB;AACA,SAASC,cAAcA,CAAAC,IAAA,EAAa;EAAA,IAAVC,MAAM,GAAAD,IAAA,CAANC,MAAM;EAC9BA,MAAM,CAACC,SAAS,GAAG,IAAI;AACzB;AACA,SAASC,YAAYA,CAAAC,KAAA,EAAa;EAAA,IAAVH,MAAM,GAAAG,KAAA,CAANH,MAAM;EAC5B,IAAIA,MAAM,CAACC,SAAS,EAAE;IACpBD,MAAM,CAACC,SAAS,GAAG,KAAK;IACxBD,MAAM,CAACI,aAAa,CAAC,IAAIC,KAAK,CAAC,OAAO,CAAC,CAAC;EAC1C;AACF;AACA,SAASC,cAAcA,CAACC,KAAK,EAAEC,QAAQ,EAAE;EACvC,IAAMC,SAAS,GAAGjC,gBAAgB,CAAC,CAAC;EACpC+B,KAAK,CAACG,KAAK,CAACC,MAAM,GAAG,MAAM;EAC3B,IAAIA,MAAM,GAAGJ,KAAK,CAACK,YAAY;EAC/B,IAAIvC,QAAQ,CAACmC,QAAQ,CAAC,EAAE;IACtB,IAAQK,SAAS,GAAgBL,QAAQ,CAAjCK,SAAS;MAAEC,SAAS,GAAKN,QAAQ,CAAtBM,SAAS;IAC5B,IAAID,SAAS,KAAK,KAAK,CAAC,EAAE;MACxBF,MAAM,GAAGI,IAAI,CAACC,GAAG,CAACL,MAAM,EAAEE,SAAS,CAAC;IACtC;IACA,IAAIC,SAAS,KAAK,KAAK,CAAC,EAAE;MACxBH,MAAM,GAAGI,IAAI,CAACE,GAAG,CAACN,MAAM,EAAEG,SAAS,CAAC;IACtC;EACF;EACA,IAAIH,MAAM,EAAE;IACVJ,KAAK,CAACG,KAAK,CAACC,MAAM,MAAAO,MAAA,CAAMP,MAAM,OAAI;IAClClC,gBAAgB,CAACgC,SAAS,CAAC;EAC7B;AACF;AACA,SAASU,YAAYA,CAACC,IAAI,EAAE;EAC1B,IAAIA,IAAI,KAAK,QAAQ,EAAE;IACrB,OAAO;MACLA,IAAI,EAAE,MAAM;MACZC,SAAS,EAAE;IACb,CAAC;EACH;EACA,IAAID,IAAI,KAAK,OAAO,EAAE;IACpB,OAAO;MACLA,IAAI,EAAE,KAAK;MACXC,SAAS,EAAE;IACb,CAAC;EACH;EACA,OAAO;IAAED,IAAI,EAAJA;EAAK,CAAC;AACjB;AACA,SAASE,eAAeA,CAACC,GAAG,EAAE;EAC5B,OAAOC,kBAAA,CAAID,GAAG,EAAEzC,MAAM;AACxB;AACA,SAAS2C,SAASA,CAACF,GAAG,EAAEG,SAAS,EAAE;EACjC,OAAOF,kBAAA,CAAID,GAAG,EAAEI,KAAK,CAAC,CAAC,EAAED,SAAS,CAAC,CAACE,IAAI,CAAC,EAAE,CAAC;AAC9C;AACA,SACEH,SAAS,EACTvB,YAAY,EACZN,cAAc,EACd0B,eAAe,EACf5C,YAAY,EACZyC,YAAY,EACZb,cAAc,EACdhB,gBAAgB,EAChBP,WAAW,EACXe,cAAc"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}