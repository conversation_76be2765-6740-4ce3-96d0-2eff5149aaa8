{"ast": null, "code": "import { createVNode as _createVNode, mergeProps as _mergeProps } from \"vue\";\nimport { ref, watch, getCurrentInstance } from \"vue\";\nimport { extend, isObject, inBrowser, withInstall } from \"../utils/index.mjs\";\nimport { mountComponent, usePopupState } from \"../utils/mount-component.mjs\";\nimport VanToast from \"./Toast.mjs\";\nvar defaultOptions = {\n  icon: \"\",\n  type: \"text\",\n  message: \"\",\n  className: \"\",\n  overlay: false,\n  onClose: void 0,\n  onOpened: void 0,\n  duration: 2e3,\n  teleport: \"body\",\n  iconSize: void 0,\n  iconPrefix: void 0,\n  position: \"middle\",\n  transition: \"van-fade\",\n  forbidClick: false,\n  loadingType: void 0,\n  overlayClass: \"\",\n  overlayStyle: void 0,\n  closeOnClick: false,\n  closeOnClickOverlay: false\n};\nvar queue = [];\nvar allowMultiple = false;\nvar currentOptions = extend({}, defaultOptions);\nvar defaultOptionsMap = /* @__PURE__ */new Map();\nfunction parseOptions(message) {\n  if (isObject(message)) {\n    return message;\n  }\n  return {\n    message: message\n  };\n}\nfunction createInstance() {\n  var _mountComponent = mountComponent({\n      setup: function setup() {\n        var message = ref(\"\");\n        var _usePopupState = usePopupState(),\n          open = _usePopupState.open,\n          state = _usePopupState.state,\n          close = _usePopupState.close,\n          toggle = _usePopupState.toggle;\n        var onClosed = function onClosed() {\n          if (allowMultiple) {\n            queue = queue.filter(function (item) {\n              return item !== instance;\n            });\n            unmount();\n          }\n        };\n        var render = function render() {\n          var attrs = {\n            onClosed: onClosed,\n            \"onUpdate:show\": toggle\n          };\n          return _createVNode(VanToast, _mergeProps(state, attrs), null);\n        };\n        watch(message, function (val) {\n          state.message = val;\n        });\n        getCurrentInstance().render = render;\n        return {\n          open: open,\n          clear: close,\n          message: message\n        };\n      }\n    }),\n    instance = _mountComponent.instance,\n    unmount = _mountComponent.unmount;\n  return instance;\n}\nfunction getInstance() {\n  if (!queue.length || allowMultiple) {\n    var instance = createInstance();\n    queue.push(instance);\n  }\n  return queue[queue.length - 1];\n}\nfunction Toast() {\n  var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  if (!inBrowser) {\n    return {};\n  }\n  var toast = getInstance();\n  var parsedOptions = parseOptions(options);\n  toast.open(extend({}, currentOptions, defaultOptionsMap.get(parsedOptions.type || currentOptions.type), parsedOptions));\n  return toast;\n}\nvar createMethod = function createMethod(type) {\n  return function (options) {\n    return Toast(extend({\n      type: type\n    }, parseOptions(options)));\n  };\n};\nToast.loading = createMethod(\"loading\");\nToast.success = createMethod(\"success\");\nToast.fail = createMethod(\"fail\");\nToast.clear = function (all) {\n  var _a;\n  if (queue.length) {\n    if (all) {\n      queue.forEach(function (toast) {\n        toast.clear();\n      });\n      queue = [];\n    } else if (!allowMultiple) {\n      queue[0].clear();\n    } else {\n      (_a = queue.shift()) == null ? void 0 : _a.clear();\n    }\n  }\n};\nfunction setDefaultOptions(type, options) {\n  if (typeof type === \"string\") {\n    defaultOptionsMap.set(type, options);\n  } else {\n    extend(currentOptions, type);\n  }\n}\nToast.setDefaultOptions = setDefaultOptions;\nToast.resetDefaultOptions = function (type) {\n  if (typeof type === \"string\") {\n    defaultOptionsMap.delete(type);\n  } else {\n    currentOptions = extend({}, defaultOptions);\n    defaultOptionsMap.clear();\n  }\n};\nToast.allowMultiple = function () {\n  var value = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : true;\n  allowMultiple = value;\n};\nToast.install = function (app) {\n  app.use(withInstall(VanToast));\n  app.config.globalProperties.$toast = Toast;\n};\nexport { Toast };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}