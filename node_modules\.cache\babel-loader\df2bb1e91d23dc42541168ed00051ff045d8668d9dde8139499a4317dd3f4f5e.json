{"ast": null, "code": "var BORDER = \"van-hairline\";\nvar BORDER_TOP = \"\".concat(BORDER, \"--top\");\nvar BORDER_LEFT = \"\".concat(BORDER, \"--left\");\nvar BORDER_BOTTOM = \"\".concat(BORDER, \"--bottom\");\nvar BORDER_SURROUND = \"\".concat(BORDER, \"--surround\");\nvar BORDER_TOP_BOTTOM = \"\".concat(BORDER, \"--top-bottom\");\nvar BORDER_UNSET_TOP_BOTTOM = \"\".concat(BORDER, \"-unset--top-bottom\");\nvar HAPTICS_FEEDBACK = \"van-haptics-feedback\";\nvar FORM_KEY = Symbol(\"van-form\");\nexport { BORDER, BORDER_BOTTOM, BORDER_LEFT, BORDER_SURROUND, BORDER_TOP, BORDER_TOP_BOTTOM, BORDER_UNSET_TOP_BOTTOM, FORM_KEY, HAPTICS_FEEDBACK };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}