require('../../../modules/es6.string.trim');
require('../../../modules/es6.string.iterator');
require('../../../modules/es6.string.code-point-at');
require('../../../modules/es6.string.ends-with');
require('../../../modules/es6.string.includes');
require('../../../modules/es6.string.repeat');
require('../../../modules/es6.string.starts-with');
require('../../../modules/es6.regexp.match');
require('../../../modules/es6.regexp.replace');
require('../../../modules/es6.regexp.search');
require('../../../modules/es6.regexp.split');
require('../../../modules/es6.string.anchor');
require('../../../modules/es6.string.big');
require('../../../modules/es6.string.blink');
require('../../../modules/es6.string.bold');
require('../../../modules/es6.string.fixed');
require('../../../modules/es6.string.fontcolor');
require('../../../modules/es6.string.fontsize');
require('../../../modules/es6.string.italics');
require('../../../modules/es6.string.link');
require('../../../modules/es6.string.small');
require('../../../modules/es6.string.strike');
require('../../../modules/es6.string.sub');
require('../../../modules/es6.string.sup');
require('../../../modules/es7.string.at');
require('../../../modules/es7.string.pad-start');
require('../../../modules/es7.string.pad-end');
require('../../../modules/es7.string.trim-left');
require('../../../modules/es7.string.trim-right');
require('../../../modules/es7.string.match-all');
require('../../../modules/core.string.escape-html');
require('../../../modules/core.string.unescape-html');
module.exports = require('../../../modules/_entry-virtual')('String');
