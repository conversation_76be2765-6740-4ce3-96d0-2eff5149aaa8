{"ast": null, "code": "import { resolveComponent as _resolveComponent, openBlock as _openBlock, createBlock as _createBlock, createCommentVNode as _createCommentVNode, withCtx as _withCtx, createVNode as _createVNode, createElementVNode as _createElementVNode, renderList as _renderList, Fragment as _Fragment, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString, normalizeClass as _normalizeClass, createTextVNode as _createTextVNode, pushScopeId as _pushScopeId, popScopeId as _popScopeId } from \"vue\";\nvar _withScopeId = function _withScopeId(n) {\n  return _pushScopeId(\"data-v-37dfd6fc\"), n = n(), _popScopeId(), n;\n};\nvar _hoisted_1 = {\n  class: \"home\"\n};\nvar _hoisted_2 = [\"src\"];\nvar _hoisted_3 = {\n  class: \"lang_box\"\n};\nvar _hoisted_4 = [\"src\"];\nvar _hoisted_5 = {\n  class: \"content\"\n};\nvar _hoisted_6 = [\"src\"];\nvar _hoisted_7 = {\n  class: \"langs\"\n};\nvar _hoisted_8 = [\"onClick\"];\nvar _hoisted_9 = [\"src\"];\nvar _hoisted_10 = {\n  class: \"text\"\n};\nvar _hoisted_11 = {\n  class: \"btn\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_lang_vue = _resolveComponent(\"lang-vue\");\n  var _component_van_nav_bar = _resolveComponent(\"van-nav-bar\");\n  var _component_van_button = _resolveComponent(\"van-button\");\n  var _component_van_dialog = _resolveComponent(\"van-dialog\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_van_nav_bar, {\n    title: $props.title,\n    \"left-arrow\": $props.leftArrow,\n    onClickLeft: _cache[0] || (_cache[0] = function ($event) {\n      return _ctx.$router.go(-1);\n    })\n  }, {\n    right: _withCtx(function () {\n      return [!$props.hideLang ? (_openBlock(), _createBlock(_component_lang_vue, {\n        key: 0,\n        color: \"white\"\n      })) : _createCommentVNode(\"v-if\", true)];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"title\", \"left-arrow\"]), _createElementVNode(\"img\", {\n    src: require('@/assets/images/home/<USER>'),\n    width: \"130\",\n    height: \"130\",\n    alt: \"\",\n    class: \"li_img\"\n  }, null, 8 /* PROPS */, _hoisted_2), _createCommentVNode(\" <img :src=\\\"logo\\\" class=\\\"logo\\\" alt=\\\"\\\" :class=\\\"!leftArrow && 'lo'\\\" width=\\\"80\\\"> \"), _createCommentVNode(\" <div class=\\\"title\\\">{{app_name}}</div> \"), _createVNode(_component_van_dialog, {\n    show: $setup.show,\n    \"onUpdate:show\": _cache[1] || (_cache[1] = function ($event) {\n      return $setup.show = $event;\n    }),\n    showConfirmButton: false\n  }, {\n    default: _withCtx(function () {\n      return [_createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"img\", {\n        src: require('@/assets/images/register/lang_bg.png'),\n        class: \"lang_bg\"\n      }, null, 8 /* PROPS */, _hoisted_4), _createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"img\", {\n        src: require('@/assets/images/register/qiu.png'),\n        class: \"qiu\"\n      }, null, 8 /* PROPS */, _hoisted_6), _createElementVNode(\"div\", _hoisted_7, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.langs, function (item, index) {\n        return _openBlock(), _createElementBlock(\"span\", {\n          class: _normalizeClass([\"li\", $setup.langcheck == item.link && 'check']),\n          key: index,\n          onClick: function onClick($event) {\n            return $setup.handSeletlanguages(item);\n          }\n        }, [_createElementVNode(\"img\", {\n          src: item.image_url,\n          class: \"img\",\n          alt: \"\"\n        }, null, 8 /* PROPS */, _hoisted_9), _createElementVNode(\"span\", _hoisted_10, _toDisplayString(item.name), 1 /* TEXT */)], 10 /* CLASS, PROPS */, _hoisted_8);\n      }), 128 /* KEYED_FRAGMENT */))]), _createElementVNode(\"div\", _hoisted_11, [_createVNode(_component_van_button, {\n        round: \"\",\n        block: \"\",\n        type: \"primary\",\n        onClick: $setup.submitLang\n      }, {\n        default: _withCtx(function () {\n          return [_createTextVNode(_toDisplayString(_ctx.$t('msg.nowQh')), 1 /* TEXT */)];\n        }),\n\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"onClick\"])])])])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"show\"])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_van_nav_bar", "title", "$props", "leftArrow", "onClickLeft", "_cache", "$event", "_ctx", "$router", "go", "right", "_withCtx", "hideLang", "_createBlock", "_component_lang_vue", "color", "_createElementVNode", "src", "require", "width", "height", "alt", "_createCommentVNode", "_component_van_dialog", "show", "$setup", "showConfirmButton", "_hoisted_3", "_hoisted_5", "_hoisted_7", "_Fragment", "_renderList", "langs", "item", "index", "_normalizeClass", "langcheck", "link", "key", "onClick", "handSeletlanguages", "image_url", "_hoisted_10", "_toDisplayString", "name", "_hoisted_11", "_component_van_button", "round", "block", "type", "submitLang", "$t"], "sources": ["C:\\Users\\<USER>\\Desktop\\vue3_3.0\\src\\views\\login\\index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"home\">\r\n    <van-nav-bar :title=\"title\" :left-arrow=\"leftArrow\" @click-left=\"$router.go(-1)\">\r\n        <template #right>\r\n            <lang-vue v-if=\"!hideLang\" color='white'></lang-vue>\r\n        </template>\r\n    </van-nav-bar>\r\n\t  <img :src=\"require('@/assets/images/home/<USER>')\" width=\"130\" height=\"130\" alt=\"\" class=\"li_img\">\r\n    <!-- <img :src=\"logo\" class=\"logo\" alt=\"\" :class=\"!leftArrow && 'lo'\" width=\"80\"> -->\r\n    <!-- <div class=\"title\">{{app_name}}</div> -->\r\n\r\n    <van-dialog v-model:show=\"show\" :showConfirmButton=\"false\">\r\n      <div class=\"lang_box\">\r\n        <img :src=\"require('@/assets/images/register/lang_bg.png')\" class=\"lang_bg\" />\r\n        <div class=\"content\">\r\n            <img :src=\"require('@/assets/images/register/qiu.png')\" class=\"qiu\" />\r\n            <div class=\"langs\">\r\n              <span class=\"li\" :class=\"langcheck==item.link && 'check'\" v-for=\"(item,index) in langs\" :key=\"index\"  @click=\"handSeletlanguages(item)\">\r\n                <img :src=\"item.image_url\" class=\"img\" alt=\"\">\r\n                <span class=\"text\">{{item.name}}</span>\r\n              </span>\r\n            </div>\r\n            <div class=\"btn\">\r\n              <van-button round block type=\"primary\" @click=\"submitLang\">\r\n                {{$t('msg.nowQh')}}\r\n              </van-button>\r\n            </div>\r\n        </div>\r\n      </div>\r\n    </van-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { ref, getCurrentInstance,watch } from 'vue';\r\nimport { useI18n } from 'vue-i18n'\r\nimport store from '../../store/index'\r\n// import logo from '@/assets/images/news/logo.png'\r\nimport langVue from '@/components/lang.vue'\r\nexport default {\r\n  components: {langVue},\r\n  name: 'HomeView',\r\n  props: {\r\n    hideLang: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    title: {\r\n      type: String,\r\n      default: false\r\n    },\r\n    leftArrow: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n  },\r\n  setup(){\r\n    const {proxy} = getCurrentInstance()\r\n    const { locale,t } = useI18n()\r\n    const show = ref(false);\r\n    const langcheck = ref('')\r\n    const langImg = ref('')\r\n    const logo = ref(store.state.baseInfo?.site_icon)\r\n    const app_name = ref(store.state.baseInfo?.app_name)\r\n    langcheck.value = store.state.lang\r\n    langImg.value = store.state.langImg\r\n    const langs = ref(store.state.baseInfo?.languageList)\r\n    const handSeletlanguages = (row) => {\r\n      langcheck.value = row.link\r\n      langImg.value = row.image_url\r\n    }\r\n    const submitLang = () => {\r\n      locale.value = langcheck.value\r\n      store.dispatch('changelang',langcheck.value)\r\n      store.dispatch('changelangImg',langImg.value)\r\n      show.value = false\r\n      console.log(proxy)\r\n      proxy.$Message({ type: 'success', message: t('msg.switch_lang_success') });\r\n    }\r\n    watch(() => store.state.baseInfo,(newVal)=>{\r\n      logo.value = newVal?.site_icon\r\n      langs.value = (newVal?.languageList) || []\r\n    }, { deep: true })\r\n\r\n    return {show,langs,handSeletlanguages,langcheck,submitLang,logo,app_name}\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.home{\r\n  position: relative;\r\n  padding-top: calc(var(--van-nav-bar-height) + 100px);\r\n \r\n        :deep(.van-nav-bar){\r\n            position: fixed !important;\r\n            // top: 0;\r\n            color: #333;\r\n            padding: 10px 0;\r\n            width: 100%;\r\n           // background-color: #0a3cff;\r\n            z-index: 3;\r\n            &::after{\r\n              border-bottom-width: 0;\r\n            }\r\n            .van-nav-bar__left{\r\n                .van-icon{\r\n                    color: #fff;\r\n                }\r\n            }\r\n            .van-nav-bar__title{\r\n                color: #fff;\r\n                // font-weight: 600;\r\n                font-size: 32px;\r\n            }\r\n        }\r\n}\r\n  .bg{\r\n    width: 100%;\r\n  }\r\n  .logo{\r\n    // width: 135px;\r\n    display: block;\r\n    margin: 50px auto 10px;\r\n    position: relative;\r\n    z-index: 2;\r\n    box-shadow: 0 0 5px 0 #cfcffc;\r\n    border-radius: 32px;\r\n    &.lo {\r\n      margin-top: 180px;\r\n    }\r\n  }\r\n  .title{\r\n    font-size: 32px;\r\n    color: #fff;\r\n    width: 60%;\r\n    margin: 0 auto;\r\n  }\r\n  .lang_box{\r\n    width: 100%;\r\n    position: relative;\r\n    padding-top: 80px;\r\n    .lang_bg{\r\n      width: 100%;\r\n      position: absolute;\r\n      top: 0;\r\n      left: 0;\r\n    }\r\n    .content{\r\n      position: relative;\r\n      z-index: 1;\r\n      .qiu{\r\n        width: 175px;\r\n        border-radius: 50%;\r\n        box-shadow: $shadow;\r\n        margin-bottom: 6px;\r\n      }\r\n      .langs{\r\n        margin-bottom: 15px;\r\n        .li{\r\n          padding: 24px 112px;\r\n          display: block;\r\n          text-align: left;\r\n          margin-bottom: 10px;\r\n          &.check{\r\n            box-shadow: $shadow;\r\n          }\r\n          .img{\r\n            width: 80px;\r\n            margin-right: 34px;\r\n            vertical-align: middle;\r\n          }\r\n          .text{\r\n            font-size: 26px;\r\n            color: #666;\r\n          }\r\n        }\r\n      }\r\n      .btn{\r\n        padding: 50px 54px 50px;\r\n      }\r\n    }\r\n  }\r\n</style>\r\n"], "mappings": ";;;;;EACOA,KAAK,EAAC;AAAM;;;EAWRA,KAAK,EAAC;AAAU;;;EAEdA,KAAK,EAAC;AAAS;;;EAEXA,KAAK,EAAC;AAAO;;;;EAGRA,KAAK,EAAC;AAAM;;EAGjBA,KAAK,EAAC;AAAK;;;;;;uBArB1BC,mBAAA,CA6BM,OA7BNC,UA6BM,GA5BJC,YAAA,CAIcC,sBAAA;IAJAC,KAAK,EAAEC,MAAA,CAAAD,KAAK;IAAG,YAAU,EAAEC,MAAA,CAAAC,SAAS;IAAGC,WAAU,EAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAAEC,IAAA,CAAAC,OAAO,CAACC,EAAE;IAAA;;IAC5DC,KAAK,EAAAC,QAAA,CACZ;MAAA,OAAoD,C,CAAnCT,MAAA,CAAAU,QAAQ,I,cAAzBC,YAAA,CAAoDC,mBAAA;;QAAzBC,KAAK,EAAC;;;;8CAG1CC,mBAAA,CAAqG;IAA/FC,GAAG,EAAEC,OAAO;IAAoCC,KAAK,EAAC,KAAK;IAACC,MAAM,EAAC,KAAK;IAACC,GAAG,EAAC,EAAE;IAACzB,KAAK,EAAC;uCAC3F0B,mBAAA,4FAAqF,EACrFA,mBAAA,6CAA8C,EAE9CvB,YAAA,CAkBawB,qBAAA;IAlBOC,IAAI,EAAEC,MAAA,CAAAD,IAAI;;aAAJC,MAAA,CAAAD,IAAI,GAAAlB,MAAA;IAAA;IAAGoB,iBAAiB,EAAE;;sBAClD;MAAA,OAgBM,CAhBNV,mBAAA,CAgBM,OAhBNW,UAgBM,GAfJX,mBAAA,CAA8E;QAAxEC,GAAG,EAAEC,OAAO;QAA0CtB,KAAK,EAAC;2CAClEoB,mBAAA,CAaM,OAbNY,UAaM,GAZFZ,mBAAA,CAAsE;QAAhEC,GAAG,EAAEC,OAAO;QAAsCtB,KAAK,EAAC;2CAC9DoB,mBAAA,CAKM,OALNa,UAKM,I,kBAJJhC,mBAAA,CAGOiC,SAAA,QAAAC,WAAA,CAH0EN,MAAA,CAAAO,KAAK,YAApBC,IAAI,EAACC,KAAK;6BAA5ErC,mBAAA,CAGO;UAHDD,KAAK,EAAAuC,eAAA,EAAC,IAAI,EAASV,MAAA,CAAAW,SAAS,IAAEH,IAAI,CAACI,IAAI;UAA4CC,GAAG,EAAEJ,KAAK;UAAIK,OAAK,WAAAA,QAAAjC,MAAA;YAAA,OAAEmB,MAAA,CAAAe,kBAAkB,CAACP,IAAI;UAAA;YACnIjB,mBAAA,CAA8C;UAAxCC,GAAG,EAAEgB,IAAI,CAACQ,SAAS;UAAE7C,KAAK,EAAC,KAAK;UAACyB,GAAG,EAAC;6CAC3CL,mBAAA,CAAuC,QAAvC0B,WAAuC,EAAAC,gBAAA,CAAlBV,IAAI,CAACW,IAAI,iB;wCAGlC5B,mBAAA,CAIM,OAJN6B,WAIM,GAHJ9C,YAAA,CAEa+C,qBAAA;QAFDC,KAAK,EAAL,EAAK;QAACC,KAAK,EAAL,EAAK;QAACC,IAAI,EAAC,SAAS;QAAEV,OAAK,EAAEd,MAAA,CAAAyB;;0BAC7C;UAAA,OAAmB,C,kCAAjB3C,IAAA,CAAA4C,EAAE,8B"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}