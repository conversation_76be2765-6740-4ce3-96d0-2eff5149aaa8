{"ast": null, "code": "import { inBrowser } from \"./basic.mjs\";\nimport { windowWidth, windowHeight } from \"./dom.mjs\";\nimport { isDef, isNumeric } from \"./validate.mjs\";\nfunction addUnit(value) {\n  if (isDef(value)) {\n    return isNumeric(value) ? \"\".concat(value, \"px\") : String(value);\n  }\n  return void 0;\n}\nfunction getSizeStyle(originSize) {\n  if (isDef(originSize)) {\n    if (Array.isArray(originSize)) {\n      return {\n        width: addUnit(originSize[0]),\n        height: addUnit(originSize[1])\n      };\n    }\n    var size = addUnit(originSize);\n    return {\n      width: size,\n      height: size\n    };\n  }\n}\nfunction getZIndexStyle(zIndex) {\n  var style = {};\n  if (zIndex !== void 0) {\n    style.zIndex = +zIndex;\n  }\n  return style;\n}\nvar rootFontSize;\nfunction getRootFontSize() {\n  if (!rootFontSize) {\n    var doc = document.documentElement;\n    var fontSize = doc.style.fontSize || window.getComputedStyle(doc).fontSize;\n    rootFontSize = parseFloat(fontSize);\n  }\n  return rootFontSize;\n}\nfunction convertRem(value) {\n  value = value.replace(/rem/g, \"\");\n  return +value * getRootFontSize();\n}\nfunction convertVw(value) {\n  value = value.replace(/vw/g, \"\");\n  return +value * windowWidth.value / 100;\n}\nfunction convertVh(value) {\n  value = value.replace(/vh/g, \"\");\n  return +value * windowHeight.value / 100;\n}\nfunction unitToPx(value) {\n  if (typeof value === \"number\") {\n    return value;\n  }\n  if (inBrowser) {\n    if (value.includes(\"rem\")) {\n      return convertRem(value);\n    }\n    if (value.includes(\"vw\")) {\n      return convertVw(value);\n    }\n    if (value.includes(\"vh\")) {\n      return convertVh(value);\n    }\n  }\n  return parseFloat(value);\n}\nvar camelizeRE = /-(\\w)/g;\nvar camelize = function camelize(str) {\n  return str.replace(camelizeRE, function (_, c) {\n    return c.toUpperCase();\n  });\n};\nvar kebabCase = function kebabCase(str) {\n  return str.replace(/([A-Z])/g, \"-$1\").toLowerCase().replace(/^-/, \"\");\n};\nfunction padZero(num) {\n  var targetLength = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 2;\n  var str = num + \"\";\n  while (str.length < targetLength) {\n    str = \"0\" + str;\n  }\n  return str;\n}\nvar clamp = function clamp(num, min, max) {\n  return Math.min(Math.max(num, min), max);\n};\nfunction trimExtraChar(value, _char, regExp) {\n  var index = value.indexOf(_char);\n  if (index === -1) {\n    return value;\n  }\n  if (_char === \"-\" && index !== 0) {\n    return value.slice(0, index);\n  }\n  return value.slice(0, index + 1) + value.slice(index).replace(regExp, \"\");\n}\nfunction formatNumber(value) {\n  var allowDot = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;\n  var allowMinus = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : true;\n  if (allowDot) {\n    value = trimExtraChar(value, \".\", /\\./g);\n  } else {\n    value = value.split(\".\")[0];\n  }\n  if (allowMinus) {\n    value = trimExtraChar(value, \"-\", /-/g);\n  } else {\n    value = value.replace(/-/, \"\");\n  }\n  var regExp = allowDot ? /[^-0-9.]/g : /[^-0-9]/g;\n  return value.replace(regExp, \"\");\n}\nfunction addNumber(num1, num2) {\n  var cardinal = Math.pow(10, 10);\n  return Math.round((num1 + num2) * cardinal) / cardinal;\n}\nexport { addNumber, addUnit, camelize, clamp, formatNumber, getSizeStyle, getZIndexStyle, kebabCase, padZero, unitToPx };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}