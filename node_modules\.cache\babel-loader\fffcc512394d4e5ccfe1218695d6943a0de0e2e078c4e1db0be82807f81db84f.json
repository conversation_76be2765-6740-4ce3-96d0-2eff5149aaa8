{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _TreeSelect from \"./TreeSelect.mjs\";\nvar TreeSelect = withInstall(_TreeSelect);\nvar stdin_default = TreeSelect;\nexport { TreeSelect, stdin_default as default };", "map": {"version": 3, "names": ["withInstall", "_TreeSelect", "TreeSelect", "stdin_default", "default"], "sources": ["C:/Users/<USER>/Desktop/vue3_3.0/node_modules/vant/es/tree-select/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _TreeSelect from \"./TreeSelect.mjs\";\nconst TreeSelect = withInstall(_TreeSelect);\nvar stdin_default = TreeSelect;\nexport {\n  TreeSelect,\n  stdin_default as default\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,WAAW,MAAM,kBAAkB;AAC1C,IAAMC,UAAU,GAAGF,WAAW,CAACC,WAAW,CAAC;AAC3C,IAAIE,aAAa,GAAGD,UAAU;AAC9B,SACEA,UAAU,EACVC,aAAa,IAAIC,OAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}