{"ast": null, "code": "'use strict';\n\nfunction _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\nvar formats = require('./formats');\nvar has = Object.prototype.hasOwnProperty;\nvar isArray = Array.isArray;\nvar hexTable = function () {\n  var array = [];\n  for (var i = 0; i < 256; ++i) {\n    array.push('%' + ((i < 16 ? '0' : '') + i.toString(16)).toUpperCase());\n  }\n  return array;\n}();\nvar compactQueue = function compactQueue(queue) {\n  while (queue.length > 1) {\n    var item = queue.pop();\n    var obj = item.obj[item.prop];\n    if (isArray(obj)) {\n      var compacted = [];\n      for (var j = 0; j < obj.length; ++j) {\n        if (typeof obj[j] !== 'undefined') {\n          compacted.push(obj[j]);\n        }\n      }\n      item.obj[item.prop] = compacted;\n    }\n  }\n};\nvar arrayToObject = function arrayToObject(source, options) {\n  var obj = options && options.plainObjects ? Object.create(null) : {};\n  for (var i = 0; i < source.length; ++i) {\n    if (typeof source[i] !== 'undefined') {\n      obj[i] = source[i];\n    }\n  }\n  return obj;\n};\nvar merge = function merge(target, source, options) {\n  /* eslint no-param-reassign: 0 */\n  if (!source) {\n    return target;\n  }\n  if (_typeof(source) !== 'object') {\n    if (isArray(target)) {\n      target.push(source);\n    } else if (target && _typeof(target) === 'object') {\n      if (options && (options.plainObjects || options.allowPrototypes) || !has.call(Object.prototype, source)) {\n        target[source] = true;\n      }\n    } else {\n      return [target, source];\n    }\n    return target;\n  }\n  if (!target || _typeof(target) !== 'object') {\n    return [target].concat(source);\n  }\n  var mergeTarget = target;\n  if (isArray(target) && !isArray(source)) {\n    mergeTarget = arrayToObject(target, options);\n  }\n  if (isArray(target) && isArray(source)) {\n    source.forEach(function (item, i) {\n      if (has.call(target, i)) {\n        var targetItem = target[i];\n        if (targetItem && _typeof(targetItem) === 'object' && item && _typeof(item) === 'object') {\n          target[i] = merge(targetItem, item, options);\n        } else {\n          target.push(item);\n        }\n      } else {\n        target[i] = item;\n      }\n    });\n    return target;\n  }\n  return Object.keys(source).reduce(function (acc, key) {\n    var value = source[key];\n    if (has.call(acc, key)) {\n      acc[key] = merge(acc[key], value, options);\n    } else {\n      acc[key] = value;\n    }\n    return acc;\n  }, mergeTarget);\n};\nvar assign = function assignSingleSource(target, source) {\n  return Object.keys(source).reduce(function (acc, key) {\n    acc[key] = source[key];\n    return acc;\n  }, target);\n};\nvar decode = function decode(str, decoder, charset) {\n  var strWithoutPlus = str.replace(/\\+/g, ' ');\n  if (charset === 'iso-8859-1') {\n    // unescape never throws, no try...catch needed:\n    return strWithoutPlus.replace(/%[0-9a-f]{2}/gi, unescape);\n  }\n  // utf-8\n  try {\n    return decodeURIComponent(strWithoutPlus);\n  } catch (e) {\n    return strWithoutPlus;\n  }\n};\nvar encode = function encode(str, defaultEncoder, charset, kind, format) {\n  // This code was originally written by Brian White (mscdex) for the io.js core querystring library.\n  // It has been adapted here for stricter adherence to RFC 3986\n  if (str.length === 0) {\n    return str;\n  }\n  var string = str;\n  if (_typeof(str) === 'symbol') {\n    string = Symbol.prototype.toString.call(str);\n  } else if (typeof str !== 'string') {\n    string = String(str);\n  }\n  if (charset === 'iso-8859-1') {\n    return escape(string).replace(/%u[0-9a-f]{4}/gi, function ($0) {\n      return '%26%23' + parseInt($0.slice(2), 16) + '%3B';\n    });\n  }\n  var out = '';\n  for (var i = 0; i < string.length; ++i) {\n    var c = string.charCodeAt(i);\n    if (c === 0x2D // -\n    || c === 0x2E // .\n    || c === 0x5F // _\n    || c === 0x7E // ~\n    || c >= 0x30 && c <= 0x39 // 0-9\n    || c >= 0x41 && c <= 0x5A // a-z\n    || c >= 0x61 && c <= 0x7A // A-Z\n    || format === formats.RFC1738 && (c === 0x28 || c === 0x29) // ( )\n    ) {\n      out += string.charAt(i);\n      continue;\n    }\n    if (c < 0x80) {\n      out = out + hexTable[c];\n      continue;\n    }\n    if (c < 0x800) {\n      out = out + (hexTable[0xC0 | c >> 6] + hexTable[0x80 | c & 0x3F]);\n      continue;\n    }\n    if (c < 0xD800 || c >= 0xE000) {\n      out = out + (hexTable[0xE0 | c >> 12] + hexTable[0x80 | c >> 6 & 0x3F] + hexTable[0x80 | c & 0x3F]);\n      continue;\n    }\n    i += 1;\n    c = 0x10000 + ((c & 0x3FF) << 10 | string.charCodeAt(i) & 0x3FF);\n    /* eslint operator-linebreak: [2, \"before\"] */\n    out += hexTable[0xF0 | c >> 18] + hexTable[0x80 | c >> 12 & 0x3F] + hexTable[0x80 | c >> 6 & 0x3F] + hexTable[0x80 | c & 0x3F];\n  }\n  return out;\n};\nvar compact = function compact(value) {\n  var queue = [{\n    obj: {\n      o: value\n    },\n    prop: 'o'\n  }];\n  var refs = [];\n  for (var i = 0; i < queue.length; ++i) {\n    var item = queue[i];\n    var obj = item.obj[item.prop];\n    var keys = Object.keys(obj);\n    for (var j = 0; j < keys.length; ++j) {\n      var key = keys[j];\n      var val = obj[key];\n      if (_typeof(val) === 'object' && val !== null && refs.indexOf(val) === -1) {\n        queue.push({\n          obj: obj,\n          prop: key\n        });\n        refs.push(val);\n      }\n    }\n  }\n  compactQueue(queue);\n  return value;\n};\nvar isRegExp = function isRegExp(obj) {\n  return Object.prototype.toString.call(obj) === '[object RegExp]';\n};\nvar isBuffer = function isBuffer(obj) {\n  if (!obj || _typeof(obj) !== 'object') {\n    return false;\n  }\n  return !!(obj.constructor && obj.constructor.isBuffer && obj.constructor.isBuffer(obj));\n};\nvar combine = function combine(a, b) {\n  return [].concat(a, b);\n};\nvar maybeMap = function maybeMap(val, fn) {\n  if (isArray(val)) {\n    var mapped = [];\n    for (var i = 0; i < val.length; i += 1) {\n      mapped.push(fn(val[i]));\n    }\n    return mapped;\n  }\n  return fn(val);\n};\nmodule.exports = {\n  arrayToObject: arrayToObject,\n  assign: assign,\n  combine: combine,\n  compact: compact,\n  decode: decode,\n  encode: encode,\n  isBuffer: isBuffer,\n  isRegExp: isRegExp,\n  maybeMap: maybeMap,\n  merge: merge\n};", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}