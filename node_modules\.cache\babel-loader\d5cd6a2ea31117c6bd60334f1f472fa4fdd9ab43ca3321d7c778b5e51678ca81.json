{"ast": null, "code": "function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nfunction _iterableToArrayLimit(arr, i) { var _i = null == arr ? null : \"undefined\" != typeof Symbol && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (null != _i) { var _s, _e, _x, _r, _arr = [], _n = !0, _d = !1; try { if (_x = (_i = _i.call(arr)).next, 0 === i) { if (Object(_i) !== _i) return; _n = !1; } else for (; !(_n = (_s = _x.call(_i)).done) && (_arr.push(_s.value), _arr.length !== i); _n = !0); } catch (err) { _d = !0, _e = err; } finally { try { if (!_n && null != _i.return && (_r = _i.return(), Object(_r) !== _r)) return; } finally { if (_d) throw _e; } } return _arr; } }\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\nimport { createVNode as _createVNode } from \"vue\";\nimport { computed, defineComponent } from \"vue\";\nimport { isDef, truthProp, numericProp, createNamespace } from \"../utils/index.mjs\";\nimport { Badge } from \"../badge/index.mjs\";\nvar _createNamespace = createNamespace(\"tab\"),\n  _createNamespace2 = _slicedToArray(_createNamespace, 2),\n  name = _createNamespace2[0],\n  bem = _createNamespace2[1];\nvar stdin_default = defineComponent({\n  name: name,\n  props: {\n    id: String,\n    dot: Boolean,\n    type: String,\n    color: String,\n    title: String,\n    badge: numericProp,\n    shrink: Boolean,\n    isActive: Boolean,\n    disabled: Boolean,\n    controls: String,\n    scrollable: Boolean,\n    activeColor: String,\n    inactiveColor: String,\n    showZeroBadge: truthProp\n  },\n  setup: function setup(props, _ref) {\n    var slots = _ref.slots;\n    var style = computed(function () {\n      var style2 = {};\n      var type = props.type,\n        color = props.color,\n        disabled = props.disabled,\n        isActive = props.isActive,\n        activeColor = props.activeColor,\n        inactiveColor = props.inactiveColor;\n      var isCard = type === \"card\";\n      if (color && isCard) {\n        style2.borderColor = color;\n        if (!disabled) {\n          if (isActive) {\n            style2.backgroundColor = color;\n          } else {\n            style2.color = color;\n          }\n        }\n      }\n      var titleColor = isActive ? activeColor : inactiveColor;\n      if (titleColor) {\n        style2.color = titleColor;\n      }\n      return style2;\n    });\n    var renderText = function renderText() {\n      var Text = _createVNode(\"span\", {\n        \"class\": bem(\"text\", {\n          ellipsis: !props.scrollable\n        })\n      }, [slots.title ? slots.title() : props.title]);\n      if (props.dot || isDef(props.badge) && props.badge !== \"\") {\n        return _createVNode(Badge, {\n          \"dot\": props.dot,\n          \"content\": props.badge,\n          \"showZero\": props.showZeroBadge\n        }, {\n          default: function _default() {\n            return [Text];\n          }\n        });\n      }\n      return Text;\n    };\n    return function () {\n      return _createVNode(\"div\", {\n        \"id\": props.id,\n        \"role\": \"tab\",\n        \"class\": [bem([props.type, {\n          grow: props.scrollable && !props.shrink,\n          shrink: props.shrink,\n          active: props.isActive,\n          disabled: props.disabled\n        }])],\n        \"style\": style.value,\n        \"tabindex\": props.disabled ? void 0 : props.isActive ? 0 : -1,\n        \"aria-selected\": props.isActive,\n        \"aria-disabled\": props.disabled || void 0,\n        \"aria-controls\": props.controls\n      }, [renderText()]);\n    };\n  }\n});\nexport { stdin_default as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}