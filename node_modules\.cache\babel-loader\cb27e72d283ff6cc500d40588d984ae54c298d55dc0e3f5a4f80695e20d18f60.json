{"ast": null, "code": "'use strict';\n\nfunction _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor); } }\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, \"prototype\", { writable: false }); return Constructor; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return _typeof(key) === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (_typeof(input) !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (_typeof(res) !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\nimport utils from './../utils.js';\nvar InterceptorManager = /*#__PURE__*/function () {\n  function InterceptorManager() {\n    _classCallCheck(this, InterceptorManager);\n    this.handlers = [];\n  }\n\n  /**\n   * Add a new interceptor to the stack\n   *\n   * @param {Function} fulfilled The function to handle `then` for a `Promise`\n   * @param {Function} rejected The function to handle `reject` for a `Promise`\n   *\n   * @return {Number} An ID used to remove interceptor later\n   */\n  _createClass(InterceptorManager, [{\n    key: \"use\",\n    value: function use(fulfilled, rejected, options) {\n      this.handlers.push({\n        fulfilled: fulfilled,\n        rejected: rejected,\n        synchronous: options ? options.synchronous : false,\n        runWhen: options ? options.runWhen : null\n      });\n      return this.handlers.length - 1;\n    }\n\n    /**\n     * Remove an interceptor from the stack\n     *\n     * @param {Number} id The ID that was returned by `use`\n     *\n     * @returns {Boolean} `true` if the interceptor was removed, `false` otherwise\n     */\n  }, {\n    key: \"eject\",\n    value: function eject(id) {\n      if (this.handlers[id]) {\n        this.handlers[id] = null;\n      }\n    }\n\n    /**\n     * Clear all interceptors from the stack\n     *\n     * @returns {void}\n     */\n  }, {\n    key: \"clear\",\n    value: function clear() {\n      if (this.handlers) {\n        this.handlers = [];\n      }\n    }\n\n    /**\n     * Iterate over all the registered interceptors\n     *\n     * This method is particularly useful for skipping over any\n     * interceptors that may have become `null` calling `eject`.\n     *\n     * @param {Function} fn The function to call for each interceptor\n     *\n     * @returns {void}\n     */\n  }, {\n    key: \"forEach\",\n    value: function forEach(fn) {\n      utils.forEach(this.handlers, function forEachHandler(h) {\n        if (h !== null) {\n          fn(h);\n        }\n      });\n    }\n  }]);\n  return InterceptorManager;\n}();\nexport default InterceptorManager;", "map": {"version": 3, "names": ["_typeof", "obj", "Symbol", "iterator", "constructor", "prototype", "_classCallCheck", "instance", "<PERSON><PERSON><PERSON><PERSON>", "TypeError", "_defineProperties", "target", "props", "i", "length", "descriptor", "enumerable", "configurable", "writable", "Object", "defineProperty", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "key", "_createClass", "protoProps", "staticProps", "arg", "_toPrimitive", "String", "input", "hint", "prim", "toPrimitive", "undefined", "res", "call", "Number", "utils", "InterceptorManager", "handlers", "value", "use", "fulfilled", "rejected", "options", "push", "synchronous", "runWhen", "eject", "id", "clear", "for<PERSON>ach", "fn", "forEachHandler", "h"], "sources": ["C:/Users/<USER>/Downloads/Telegram Desktop/vue3_3.0/vue3_3.0/node_modules/axios/lib/core/InterceptorManager.js"], "sourcesContent": ["'use strict';\n\nimport utils from './../utils.js';\n\nclass InterceptorManager {\n  constructor() {\n    this.handlers = [];\n  }\n\n  /**\n   * Add a new interceptor to the stack\n   *\n   * @param {Function} fulfilled The function to handle `then` for a `Promise`\n   * @param {Function} rejected The function to handle `reject` for a `Promise`\n   *\n   * @return {Number} An ID used to remove interceptor later\n   */\n  use(fulfilled, rejected, options) {\n    this.handlers.push({\n      fulfilled,\n      rejected,\n      synchronous: options ? options.synchronous : false,\n      runWhen: options ? options.runWhen : null\n    });\n    return this.handlers.length - 1;\n  }\n\n  /**\n   * Remove an interceptor from the stack\n   *\n   * @param {Number} id The ID that was returned by `use`\n   *\n   * @returns {Boolean} `true` if the interceptor was removed, `false` otherwise\n   */\n  eject(id) {\n    if (this.handlers[id]) {\n      this.handlers[id] = null;\n    }\n  }\n\n  /**\n   * Clear all interceptors from the stack\n   *\n   * @returns {void}\n   */\n  clear() {\n    if (this.handlers) {\n      this.handlers = [];\n    }\n  }\n\n  /**\n   * Iterate over all the registered interceptors\n   *\n   * This method is particularly useful for skipping over any\n   * interceptors that may have become `null` calling `eject`.\n   *\n   * @param {Function} fn The function to call for each interceptor\n   *\n   * @returns {void}\n   */\n  forEach(fn) {\n    utils.forEach(this.handlers, function forEachHandler(h) {\n      if (h !== null) {\n        fn(h);\n      }\n    });\n  }\n}\n\nexport default InterceptorManager;\n"], "mappings": "AAAA,YAAY;;AAAC,SAAAA,QAAAC,GAAA,sCAAAD,OAAA,wBAAAE,MAAA,uBAAAA,MAAA,CAAAC,QAAA,aAAAF,GAAA,kBAAAA,GAAA,gBAAAA,GAAA,WAAAA,GAAA,yBAAAC,MAAA,IAAAD,GAAA,CAAAG,WAAA,KAAAF,MAAA,IAAAD,GAAA,KAAAC,MAAA,CAAAG,SAAA,qBAAAJ,GAAA,KAAAD,OAAA,CAAAC,GAAA;AAAA,SAAAK,gBAAAC,QAAA,EAAAC,WAAA,UAAAD,QAAA,YAAAC,WAAA,eAAAC,SAAA;AAAA,SAAAC,kBAAAC,MAAA,EAAAC,KAAA,aAAAC,CAAA,MAAAA,CAAA,GAAAD,KAAA,CAAAE,MAAA,EAAAD,CAAA,UAAAE,UAAA,GAAAH,KAAA,CAAAC,CAAA,GAAAE,UAAA,CAAAC,UAAA,GAAAD,UAAA,CAAAC,UAAA,WAAAD,UAAA,CAAAE,YAAA,wBAAAF,UAAA,EAAAA,UAAA,CAAAG,QAAA,SAAAC,MAAA,CAAAC,cAAA,CAAAT,MAAA,EAAAU,cAAA,CAAAN,UAAA,CAAAO,GAAA,GAAAP,UAAA;AAAA,SAAAQ,aAAAf,WAAA,EAAAgB,UAAA,EAAAC,WAAA,QAAAD,UAAA,EAAAd,iBAAA,CAAAF,WAAA,CAAAH,SAAA,EAAAmB,UAAA,OAAAC,WAAA,EAAAf,iBAAA,CAAAF,WAAA,EAAAiB,WAAA,GAAAN,MAAA,CAAAC,cAAA,CAAAZ,WAAA,iBAAAU,QAAA,mBAAAV,WAAA;AAAA,SAAAa,eAAAK,GAAA,QAAAJ,GAAA,GAAAK,YAAA,CAAAD,GAAA,oBAAA1B,OAAA,CAAAsB,GAAA,iBAAAA,GAAA,GAAAM,MAAA,CAAAN,GAAA;AAAA,SAAAK,aAAAE,KAAA,EAAAC,IAAA,QAAA9B,OAAA,CAAA6B,KAAA,kBAAAA,KAAA,kBAAAA,KAAA,MAAAE,IAAA,GAAAF,KAAA,CAAA3B,MAAA,CAAA8B,WAAA,OAAAD,IAAA,KAAAE,SAAA,QAAAC,GAAA,GAAAH,IAAA,CAAAI,IAAA,CAAAN,KAAA,EAAAC,IAAA,oBAAA9B,OAAA,CAAAkC,GAAA,uBAAAA,GAAA,YAAAzB,SAAA,4DAAAqB,IAAA,gBAAAF,MAAA,GAAAQ,MAAA,EAAAP,KAAA;AAEb,OAAOQ,KAAK,MAAM,eAAe;AAAC,IAE5BC,kBAAkB;EACtB,SAAAA,mBAAA,EAAc;IAAAhC,eAAA,OAAAgC,kBAAA;IACZ,IAAI,CAACC,QAAQ,GAAG,EAAE;EACpB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EAPEhB,YAAA,CAAAe,kBAAA;IAAAhB,GAAA;IAAAkB,KAAA,EAQA,SAAAC,IAAIC,SAAS,EAAEC,QAAQ,EAAEC,OAAO,EAAE;MAChC,IAAI,CAACL,QAAQ,CAACM,IAAI,CAAC;QACjBH,SAAS,EAATA,SAAS;QACTC,QAAQ,EAARA,QAAQ;QACRG,WAAW,EAAEF,OAAO,GAAGA,OAAO,CAACE,WAAW,GAAG,KAAK;QAClDC,OAAO,EAAEH,OAAO,GAAGA,OAAO,CAACG,OAAO,GAAG;MACvC,CAAC,CAAC;MACF,OAAO,IAAI,CAACR,QAAQ,CAACzB,MAAM,GAAG,CAAC;IACjC;;IAEA;AACF;AACA;AACA;AACA;AACA;AACA;EANE;IAAAQ,GAAA;IAAAkB,KAAA,EAOA,SAAAQ,MAAMC,EAAE,EAAE;MACR,IAAI,IAAI,CAACV,QAAQ,CAACU,EAAE,CAAC,EAAE;QACrB,IAAI,CAACV,QAAQ,CAACU,EAAE,CAAC,GAAG,IAAI;MAC1B;IACF;;IAEA;AACF;AACA;AACA;AACA;EAJE;IAAA3B,GAAA;IAAAkB,KAAA,EAKA,SAAAU,MAAA,EAAQ;MACN,IAAI,IAAI,CAACX,QAAQ,EAAE;QACjB,IAAI,CAACA,QAAQ,GAAG,EAAE;MACpB;IACF;;IAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EATE;IAAAjB,GAAA;IAAAkB,KAAA,EAUA,SAAAW,QAAQC,EAAE,EAAE;MACVf,KAAK,CAACc,OAAO,CAAC,IAAI,CAACZ,QAAQ,EAAE,SAASc,cAAcA,CAACC,CAAC,EAAE;QACtD,IAAIA,CAAC,KAAK,IAAI,EAAE;UACdF,EAAE,CAACE,CAAC,CAAC;QACP;MACF,CAAC,CAAC;IACJ;EAAC;EAAA,OAAAhB,kBAAA;AAAA;AAGH,eAAeA,kBAAkB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}