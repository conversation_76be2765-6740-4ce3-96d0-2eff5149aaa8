{"ast": null, "code": "function _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return _typeof(key) === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (_typeof(input) !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (_typeof(res) !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\nimport loginTop from './index.vue';\nimport { ref, getCurrentInstance } from 'vue';\nimport store from '@/store/index';\nimport { useRouter, useRoute } from 'vue-router';\nimport { do_register, login } from '@/api/login/index';\nimport { Toast } from 'vant';\nexport default {\n  name: 'HomeView',\n  components: {\n    loginTop: loginTop\n  },\n  setup: function setup() {\n    var _route$query;\n    var route = useRoute();\n    var _useRouter = useRouter(),\n      push = _useRouter.push;\n    var _getCurrentInstance = getCurrentInstance(),\n      proxy = _getCurrentInstance.proxy;\n    var invite_code = ref('');\n    if ((_route$query = route.query) !== null && _route$query !== void 0 && _route$query.invite_code) {\n      var _route$query2;\n      invite_code.value = (_route$query2 = route.query) === null || _route$query2 === void 0 ? void 0 : _route$query2.invite_code;\n    }\n    var userName = ref('');\n    var pwd = ref('');\n    var pwd2 = ref('');\n    var depositPwd = ref('');\n    var onSubmit = function onSubmit(values) {\n      if (values.pwd != values.pwd2) {\n        Toast.fail('两次输入的密码不正确');\n        return false;\n      }\n      var json = JSON.parse(JSON.stringify(values));\n      delete json.pwd2;\n      do_register(json).then(function (res) {\n        if (res.code === 0) {\n          proxy.$Message({\n            type: 'success',\n            message: res.info\n          });\n          var info = {\n            userName: userName.value,\n            pwd: pwd.value,\n            pwd2: pwd2.value,\n            depositPwd: depositPwd.value,\n            invite_code: invite_code.value\n          };\n          login(info).then(function (red) {\n            if (red.code === 0) {\n              store.dispatch('changetoken', red.token);\n              store.dispatch('changeuserinfo', red.userinfo || {});\n              proxy.$Message({\n                type: 'success',\n                message: red.info\n              });\n              // 记住密码\n              var useri = _objectSpread(_objectSpread({}, json), {\n                checked: true\n              });\n              store.dispatch('changeUser', useri);\n              push('/');\n            } else {\n              proxy.$Message({\n                type: 'error',\n                message: red.info\n              });\n            }\n          });\n        } else {\n          proxy.$Message({\n            type: 'error',\n            message: res.info\n          });\n        }\n      });\n    };\n    return {\n      userName: userName,\n      pwd: pwd,\n      pwd2: pwd2,\n      depositPwd: depositPwd,\n      invite_code: invite_code,\n      onSubmit: onSubmit\n    };\n  }\n};", "map": {"version": 3, "names": ["loginTop", "ref", "getCurrentInstance", "store", "useRouter", "useRoute", "do_register", "login", "Toast", "name", "components", "setup", "_route$query", "route", "_useRouter", "push", "_getCurrentInstance", "proxy", "invite_code", "query", "_route$query2", "value", "userName", "pwd", "pwd2", "depositPwd", "onSubmit", "values", "fail", "json", "JSON", "parse", "stringify", "then", "res", "code", "$Message", "type", "message", "info", "red", "dispatch", "token", "userinfo", "useri", "_objectSpread", "checked"], "sources": ["C:\\Users\\<USER>\\Downloads\\Telegram Desktop\\vue3_3.0\\vue3_3.0\\src\\views\\login\\register.vue"], "sourcesContent": ["<template>\r\n  <div class=\"homes\">\r\n    <login-top  hide-lang :title=\"$t('msg.register')\" left-arrow></login-top>\r\n    <van-form @submit=\"onSubmit\" style=\"z-index: 11;\">\r\n\r\n      <van-cell-group inset >\r\n        <van-field label-width=\"100\" class=\"zdy\" v-model=\"userName\" name=\"userName\" :label=\"$t('msg.username')\"\r\n          :placeholder=\"$t('msg.input_username')\" :rules=\"[{ required: true, message: $t('msg.input_username') }]\" />\r\n        <van-field label-width=\"100\" v-model=\"pwd\" type=\"password\" name=\"pwd\" :label=\"$t('msg.pwd')\"\r\n          :placeholder=\"$t('msg.pwd')\" :rules=\"[{ required: true, message: $t('msg.input_pwd') }]\" />\r\n        <van-field label-width=\"100\" v-model=\"pwd2\" type=\"password\" name=\"pwd2\" :label=\"$t('msg.true_pwd')\"\r\n          :placeholder=\"$t('msg.true_pwd')\" :rules=\"[{ required: true, message: $t('msg.input_true_pwd') }]\" />\r\n        <van-field label-width=\"100\" v-model=\"depositPwd\" name=\"depositPwd\" :label=\"$t('msg.tx_pwd')\"\r\n          :placeholder=\"$t('msg.tx_pwd')\" :rules=\"[{ required: true, message: $t('msg.input_t_pwd') }]\" />\r\n        <van-field label-width=\"100\" v-model=\"invite_code\" name=\"invite_code\" :label=\"$t('msg.code')\"\r\n          :placeholder=\"$t('msg.code')\" :rules=\"[{ required: true, message: $t('msg.input_code') }]\" />\r\n      </van-cell-group>\r\n      <div class=\"buttons\">\r\n        <van-button round=\"5\" block plain type=\"primary\" native-type=\"submit\" style=\"border-radius: 10px;\">\r\n          {{ $t('msg.register1') }}\r\n        </van-button>\r\n        <div class=\"gore\">\r\n          <span class=\"gores\" @click=\"$router.push({ path: '/login' })\"> {{ $t('msg.login') }} </span>\r\n        </div>\r\n\r\n      </div>\r\n    </van-form>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport loginTop from './index.vue'\r\nimport { ref, getCurrentInstance } from 'vue';\r\nimport store from '@/store/index'\r\nimport { useRouter, useRoute } from 'vue-router';\r\nimport { do_register, login } from '@/api/login/index'\r\nimport { Toast } from 'vant';\r\nexport default {\r\n  name: 'HomeView',\r\n  components: { loginTop },\r\n  setup() {\r\n\r\n    const route = useRoute()\r\n    const { push } = useRouter();\r\n    const { proxy } = getCurrentInstance()\r\n    \r\n    const invite_code = ref('');\r\n    if (route.query?.invite_code) {\r\n      invite_code.value = route.query?.invite_code\r\n    }\r\n    const userName = ref('');\r\n    const pwd = ref('');\r\n    const pwd2 = ref('');\r\n    const depositPwd = ref('');\r\n\r\n    const onSubmit = (values) => {\r\n      if (values.pwd != values.pwd2) {\r\n        Toast.fail('两次输入的密码不正确')\r\n        return false\r\n      }\r\n      const json = JSON.parse(JSON.stringify(values))\r\n      delete json.pwd2\r\n      \r\n      do_register(json).then(res => {\r\n        if (res.code === 0) {\r\n          proxy.$Message({ type: 'success', message: res.info });\r\n          let info = {\r\n            userName: userName.value,\r\n            pwd: pwd.value, \r\n            pwd2: pwd2.value,\r\n            depositPwd: depositPwd.value,\r\n            invite_code: invite_code.value\r\n          } \r\n          login(info).then(red => {\r\n            if (red.code === 0) {\r\n              store.dispatch('changetoken', red.token)\r\n              store.dispatch('changeuserinfo', red.userinfo || {})\r\n              proxy.$Message({ type: 'success', message: red.info });\r\n              // 记住密码\r\n              const useri = { ...json, ...{ checked: true } }\r\n              store.dispatch('changeUser', useri)\r\n              push('/')\r\n            } else {\r\n              proxy.$Message({ type: 'error', message: red.info });\r\n            }\r\n\r\n          })\r\n        } else {\r\n          proxy.$Message({ type: 'error', message: res.info });\r\n        }\r\n      })\r\n    };\r\n\r\n    return {\r\n      userName,\r\n      pwd,\r\n      pwd2,\r\n      depositPwd,\r\n      invite_code,\r\n      onSubmit\r\n    };\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n@import '@/styles/theme.scss';\r\n\r\n:deep .van-form {\r\n  background-color: #fff;\r\n  width: 94%;\r\n  margin: 0 auto;\r\n  color: black;\r\n  border-radius: 20px;\r\n  padding: 10px 0 0;\r\n  margin-top: 1rem;\r\n}\r\n\r\n.gore {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  color: black;\r\n  margin-top: 10px;\r\n}\r\n\r\n.gores {\r\n  color: #009995 !important;\r\n}\r\n\r\n:deep .van-button--plain {\r\n  background-color: #009995 !important;\r\n  color: #fff !important;\r\n  border-radius: 25 px !important;\r\n  border: none !important;\r\n}\r\n\r\n:deep .van-cell {\r\n  border: 1px solid #ccc;\r\n  border-radius: 12px;\r\n  margin-top: 20px;\r\n  padding: 15px 35px !important;\r\n}\r\n\r\n.homes {\r\n  height: 100vh;\r\n  overflow: auto;\r\n  background-image: url('~@/assets/images/bj.png');\r\n  background-size: 100% 100%;\r\n\r\n  :deep(.van-form) {\r\n    position: relative;\r\n    padding-bottom: 40px;\r\n    z-index: 9;\r\n    .van-cell-group--inset {\r\n      padding: 0 50px;\r\n      background-color: initial;\r\n    }\r\n\r\n    .van-ellipsis {\r\n      color: #fff;\r\n    }\r\n\r\n    .van-cell {\r\n      padding: 34px 10px;\r\n      border-bottom: 1px solid var(--van-cell-border-color);\r\n      background-color: initial;\r\n\r\n      &.zdy {\r\n        .van-field__left-icon {\r\n          margin-right: 30px;\r\n        }\r\n      }\r\n\r\n      .van-field__left-icon {\r\n        margin-right: 90px;\r\n\r\n        .van-icon__image {\r\n          height: 42px;\r\n          width: auto;\r\n        }\r\n\r\n        .icon {\r\n          height: 42px;\r\n          width: auto;\r\n          vertical-align: middle;\r\n        }\r\n\r\n        display: flex;\r\n\r\n        .van-dropdown-menu {\r\n          .van-dropdown-menu__bar {\r\n            height: auto;\r\n            background: none;\r\n            box-shadow: none;\r\n          }\r\n\r\n          .van-cell {\r\n            padding: 30px 80px;\r\n          }\r\n        }\r\n      }\r\n\r\n      .van-field__control {\r\n        font-size: 24px;\r\n      }\r\n\r\n      .van-field__label {\r\n        color: #fff;\r\n        display: none;\r\n      }\r\n\r\n      zdy .van-field__label {\r\n        color: #fff;\r\n        display: flex;\r\n      }\r\n\r\n      &::after {\r\n        display: none;\r\n      }\r\n    }\r\n\r\n    .van-checkbox {\r\n      margin: 30px 0 60px 0;\r\n\r\n      .van-checkbox__icon {\r\n        font-size: 50px;\r\n        margin-right: 80px;\r\n\r\n        &.van-checkbox__icon--checked .van-icon {\r\n          background-color: $theme;\r\n          border-color: $theme;\r\n        }\r\n      }\r\n\r\n      .van-checkbox__label {\r\n        font-size: 24px;\r\n      }\r\n    }\r\n\r\n    .buttons {\r\n      padding: 0 50px;\r\n      border-radius: 3px;\r\n\r\n      .van-button {\r\n        font-size: 26px;\r\n        padding: 26px 0;\r\n        height: auto;\r\n        margin-top: 40px;\r\n\r\n        &+.van-button {\r\n          background-color: rgba(255, 255, 255, 0.2);\r\n          border: none;\r\n          color: #fff;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;AA+BA,OAAOA,QAAO,MAAO,aAAY;AACjC,SAASC,GAAG,EAAEC,kBAAiB,QAAS,KAAK;AAC7C,OAAOC,KAAI,MAAO,eAAc;AAChC,SAASC,SAAS,EAAEC,QAAO,QAAS,YAAY;AAChD,SAASC,WAAW,EAAEC,KAAI,QAAS,mBAAkB;AACrD,SAASC,KAAI,QAAS,MAAM;AAC5B,eAAe;EACbC,IAAI,EAAE,UAAU;EAChBC,UAAU,EAAE;IAAEV,QAAO,EAAPA;EAAS,CAAC;EACxBW,KAAK,WAAAA,MAAA,EAAG;IAAA,IAAAC,YAAA;IAEN,IAAMC,KAAI,GAAIR,QAAQ,CAAC;IACvB,IAAAS,UAAA,GAAiBV,SAAS,CAAC,CAAC;MAApBW,IAAG,GAAAD,UAAA,CAAHC,IAAG;IACX,IAAAC,mBAAA,GAAkBd,kBAAkB,CAAC;MAA7Be,KAAI,GAAAD,mBAAA,CAAJC,KAAI;IAEZ,IAAMC,WAAU,GAAIjB,GAAG,CAAC,EAAE,CAAC;IAC3B,KAAAW,YAAA,GAAIC,KAAK,CAACM,KAAK,cAAAP,YAAA,eAAXA,YAAA,CAAaM,WAAW,EAAE;MAAA,IAAAE,aAAA;MAC5BF,WAAW,CAACG,KAAI,IAAAD,aAAA,GAAIP,KAAK,CAACM,KAAK,cAAAC,aAAA,uBAAXA,aAAA,CAAaF,WAAU;IAC7C;IACA,IAAMI,QAAO,GAAIrB,GAAG,CAAC,EAAE,CAAC;IACxB,IAAMsB,GAAE,GAAItB,GAAG,CAAC,EAAE,CAAC;IACnB,IAAMuB,IAAG,GAAIvB,GAAG,CAAC,EAAE,CAAC;IACpB,IAAMwB,UAAS,GAAIxB,GAAG,CAAC,EAAE,CAAC;IAE1B,IAAMyB,QAAO,GAAI,SAAXA,QAAOA,CAAKC,MAAM,EAAK;MAC3B,IAAIA,MAAM,CAACJ,GAAE,IAAKI,MAAM,CAACH,IAAI,EAAE;QAC7BhB,KAAK,CAACoB,IAAI,CAAC,YAAY;QACvB,OAAO,KAAI;MACb;MACA,IAAMC,IAAG,GAAIC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAACL,MAAM,CAAC;MAC9C,OAAOE,IAAI,CAACL,IAAG;MAEflB,WAAW,CAACuB,IAAI,CAAC,CAACI,IAAI,CAAC,UAAAC,GAAE,EAAK;QAC5B,IAAIA,GAAG,CAACC,IAAG,KAAM,CAAC,EAAE;UAClBlB,KAAK,CAACmB,QAAQ,CAAC;YAAEC,IAAI,EAAE,SAAS;YAAEC,OAAO,EAAEJ,GAAG,CAACK;UAAK,CAAC,CAAC;UACtD,IAAIA,IAAG,GAAI;YACTjB,QAAQ,EAAEA,QAAQ,CAACD,KAAK;YACxBE,GAAG,EAAEA,GAAG,CAACF,KAAK;YACdG,IAAI,EAAEA,IAAI,CAACH,KAAK;YAChBI,UAAU,EAAEA,UAAU,CAACJ,KAAK;YAC5BH,WAAW,EAAEA,WAAW,CAACG;UAC3B;UACAd,KAAK,CAACgC,IAAI,CAAC,CAACN,IAAI,CAAC,UAAAO,GAAE,EAAK;YACtB,IAAIA,GAAG,CAACL,IAAG,KAAM,CAAC,EAAE;cAClBhC,KAAK,CAACsC,QAAQ,CAAC,aAAa,EAAED,GAAG,CAACE,KAAK;cACvCvC,KAAK,CAACsC,QAAQ,CAAC,gBAAgB,EAAED,GAAG,CAACG,QAAO,IAAK,CAAC,CAAC;cACnD1B,KAAK,CAACmB,QAAQ,CAAC;gBAAEC,IAAI,EAAE,SAAS;gBAAEC,OAAO,EAAEE,GAAG,CAACD;cAAK,CAAC,CAAC;cACtD;cACA,IAAMK,KAAI,GAAAC,aAAA,CAAAA,aAAA,KAAShB,IAAI,GAAK;gBAAEiB,OAAO,EAAE;cAAK,EAAE;cAC9C3C,KAAK,CAACsC,QAAQ,CAAC,YAAY,EAAEG,KAAK;cAClC7B,IAAI,CAAC,GAAG;YACV,OAAO;cACLE,KAAK,CAACmB,QAAQ,CAAC;gBAAEC,IAAI,EAAE,OAAO;gBAAEC,OAAO,EAAEE,GAAG,CAACD;cAAK,CAAC,CAAC;YACtD;UAEF,CAAC;QACH,OAAO;UACLtB,KAAK,CAACmB,QAAQ,CAAC;YAAEC,IAAI,EAAE,OAAO;YAAEC,OAAO,EAAEJ,GAAG,CAACK;UAAK,CAAC,CAAC;QACtD;MACF,CAAC;IACH,CAAC;IAED,OAAO;MACLjB,QAAQ,EAARA,QAAQ;MACRC,GAAG,EAAHA,GAAG;MACHC,IAAI,EAAJA,IAAI;MACJC,UAAU,EAAVA,UAAU;MACVP,WAAW,EAAXA,WAAW;MACXQ,QAAO,EAAPA;IACF,CAAC;EACH;AACF"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}