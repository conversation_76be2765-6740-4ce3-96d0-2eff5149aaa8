{"ast": null, "code": "function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nfunction _iterableToArrayLimit(arr, i) { var _i = null == arr ? null : \"undefined\" != typeof Symbol && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (null != _i) { var _s, _e, _x, _r, _arr = [], _n = !0, _d = !1; try { if (_x = (_i = _i.call(arr)).next, 0 === i) { if (Object(_i) !== _i) return; _n = !1; } else for (; !(_n = (_s = _x.call(_i)).done) && (_arr.push(_s.value), _arr.length !== i); _n = !0); } catch (err) { _d = !0, _e = err; } finally { try { if (!_n && null != _i.return && (_r = _i.return(), Object(_r) !== _r)) return; } finally { if (_d) throw _e; } } return _arr; } }\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\nimport { createVNode as _createVNode } from \"vue\";\nimport { ref, computed, defineComponent } from \"vue\";\nimport { pick, addUnit, numericProp, setScrollTop, createNamespace, makeRequiredProp } from \"../utils/index.mjs\";\nimport { getMonthEndDay } from \"../datetime-picker/utils.mjs\";\nimport { t, bem, compareDay, getPrevDay, getNextDay, formatMonthTitle } from \"./utils.mjs\";\nimport { useRect, useToggle } from \"@vant/use\";\nimport { useExpose } from \"../composables/use-expose.mjs\";\nimport { useHeight } from \"../composables/use-height.mjs\";\nimport CalendarDay from \"./CalendarDay.mjs\";\nvar _createNamespace = createNamespace(\"calendar-month\"),\n  _createNamespace2 = _slicedToArray(_createNamespace, 1),\n  name = _createNamespace2[0];\nvar calendarMonthProps = {\n  date: makeRequiredProp(Date),\n  type: String,\n  color: String,\n  minDate: makeRequiredProp(Date),\n  maxDate: makeRequiredProp(Date),\n  showMark: Boolean,\n  rowHeight: numericProp,\n  formatter: Function,\n  lazyRender: Boolean,\n  currentDate: [Date, Array],\n  allowSameDay: Boolean,\n  showSubtitle: Boolean,\n  showMonthTitle: Boolean,\n  firstDayOfWeek: Number\n};\nvar stdin_default = defineComponent({\n  name: name,\n  props: calendarMonthProps,\n  emits: [\"click\", \"update-height\"],\n  setup: function setup(props, _ref) {\n    var emit = _ref.emit,\n      slots = _ref.slots;\n    var _useToggle = useToggle(),\n      _useToggle2 = _slicedToArray(_useToggle, 2),\n      visible = _useToggle2[0],\n      setVisible = _useToggle2[1];\n    var daysRef = ref();\n    var monthRef = ref();\n    var height = useHeight(monthRef);\n    var title = computed(function () {\n      return formatMonthTitle(props.date);\n    });\n    var rowHeight = computed(function () {\n      return addUnit(props.rowHeight);\n    });\n    var offset = computed(function () {\n      var realDay = props.date.getDay();\n      if (props.firstDayOfWeek) {\n        return (realDay + 7 - props.firstDayOfWeek) % 7;\n      }\n      return realDay;\n    });\n    var totalDay = computed(function () {\n      return getMonthEndDay(props.date.getFullYear(), props.date.getMonth() + 1);\n    });\n    var shouldRender = computed(function () {\n      return visible.value || !props.lazyRender;\n    });\n    var getTitle = function getTitle() {\n      return title.value;\n    };\n    var getMultipleDayType = function getMultipleDayType(day) {\n      var isSelected = function isSelected(date) {\n        return props.currentDate.some(function (item) {\n          return compareDay(item, date) === 0;\n        });\n      };\n      if (isSelected(day)) {\n        var prevDay = getPrevDay(day);\n        var nextDay = getNextDay(day);\n        var prevSelected = isSelected(prevDay);\n        var nextSelected = isSelected(nextDay);\n        if (prevSelected && nextSelected) {\n          return \"multiple-middle\";\n        }\n        if (prevSelected) {\n          return \"end\";\n        }\n        if (nextSelected) {\n          return \"start\";\n        }\n        return \"multiple-selected\";\n      }\n      return \"\";\n    };\n    var getRangeDayType = function getRangeDayType(day) {\n      var _props$currentDate = _slicedToArray(props.currentDate, 2),\n        startDay = _props$currentDate[0],\n        endDay = _props$currentDate[1];\n      if (!startDay) {\n        return \"\";\n      }\n      var compareToStart = compareDay(day, startDay);\n      if (!endDay) {\n        return compareToStart === 0 ? \"start\" : \"\";\n      }\n      var compareToEnd = compareDay(day, endDay);\n      if (props.allowSameDay && compareToStart === 0 && compareToEnd === 0) {\n        return \"start-end\";\n      }\n      if (compareToStart === 0) {\n        return \"start\";\n      }\n      if (compareToEnd === 0) {\n        return \"end\";\n      }\n      if (compareToStart > 0 && compareToEnd < 0) {\n        return \"middle\";\n      }\n      return \"\";\n    };\n    var getDayType = function getDayType(day) {\n      var type = props.type,\n        minDate = props.minDate,\n        maxDate = props.maxDate,\n        currentDate = props.currentDate;\n      if (compareDay(day, minDate) < 0 || compareDay(day, maxDate) > 0) {\n        return \"disabled\";\n      }\n      if (currentDate === null) {\n        return \"\";\n      }\n      if (Array.isArray(currentDate)) {\n        if (type === \"multiple\") {\n          return getMultipleDayType(day);\n        }\n        if (type === \"range\") {\n          return getRangeDayType(day);\n        }\n      } else if (type === \"single\") {\n        return compareDay(day, currentDate) === 0 ? \"selected\" : \"\";\n      }\n      return \"\";\n    };\n    var getBottomInfo = function getBottomInfo(dayType) {\n      if (props.type === \"range\") {\n        if (dayType === \"start\" || dayType === \"end\") {\n          return t(dayType);\n        }\n        if (dayType === \"start-end\") {\n          return \"\".concat(t(\"start\"), \"/\").concat(t(\"end\"));\n        }\n      }\n    };\n    var renderTitle = function renderTitle() {\n      if (props.showMonthTitle) {\n        return _createVNode(\"div\", {\n          \"class\": bem(\"month-title\")\n        }, [title.value]);\n      }\n    };\n    var renderMark = function renderMark() {\n      if (props.showMark && shouldRender.value) {\n        return _createVNode(\"div\", {\n          \"class\": bem(\"month-mark\")\n        }, [props.date.getMonth() + 1]);\n      }\n    };\n    var placeholders = computed(function () {\n      var count = Math.ceil((totalDay.value + offset.value) / 7);\n      return Array(count).fill({\n        type: \"placeholder\"\n      });\n    });\n    var days = computed(function () {\n      var days2 = [];\n      var year = props.date.getFullYear();\n      var month = props.date.getMonth();\n      for (var day = 1; day <= totalDay.value; day++) {\n        var date = new Date(year, month, day);\n        var type = getDayType(date);\n        var config = {\n          date: date,\n          type: type,\n          text: day,\n          bottomInfo: getBottomInfo(type)\n        };\n        if (props.formatter) {\n          config = props.formatter(config);\n        }\n        days2.push(config);\n      }\n      return days2;\n    });\n    var disabledDays = computed(function () {\n      return days.value.filter(function (day) {\n        return day.type === \"disabled\";\n      });\n    });\n    var scrollToDate = function scrollToDate(body, targetDate) {\n      if (daysRef.value) {\n        var daysRect = useRect(daysRef.value);\n        var totalRows = placeholders.value.length;\n        var currentRow = Math.ceil((targetDate.getDate() + offset.value) / 7);\n        var rowOffset = (currentRow - 1) * daysRect.height / totalRows;\n        setScrollTop(body, daysRect.top + rowOffset + body.scrollTop - useRect(body).top);\n      }\n    };\n    var renderDay = function renderDay(item, index) {\n      return _createVNode(CalendarDay, {\n        \"item\": item,\n        \"index\": index,\n        \"color\": props.color,\n        \"offset\": offset.value,\n        \"rowHeight\": rowHeight.value,\n        \"onClick\": function onClick(item2) {\n          return emit(\"click\", item2);\n        }\n      }, pick(slots, [\"top-info\", \"bottom-info\"]));\n    };\n    var renderDays = function renderDays() {\n      return _createVNode(\"div\", {\n        \"ref\": daysRef,\n        \"role\": \"grid\",\n        \"class\": bem(\"days\")\n      }, [renderMark(), (shouldRender.value ? days : placeholders).value.map(renderDay)]);\n    };\n    useExpose({\n      getTitle: getTitle,\n      getHeight: function getHeight() {\n        return height.value;\n      },\n      setVisible: setVisible,\n      scrollToDate: scrollToDate,\n      disabledDays: disabledDays\n    });\n    return function () {\n      return _createVNode(\"div\", {\n        \"class\": bem(\"month\"),\n        \"ref\": monthRef\n      }, [renderTitle(), renderDays()]);\n    };\n  }\n});\nexport { stdin_default as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}