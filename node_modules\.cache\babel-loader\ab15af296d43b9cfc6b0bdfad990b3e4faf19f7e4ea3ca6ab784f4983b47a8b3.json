{"ast": null, "code": "function _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return _typeof(key) === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (_typeof(input) !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (_typeof(res) !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\nfunction _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nfunction _iterableToArrayLimit(arr, i) { var _i = null == arr ? null : \"undefined\" != typeof Symbol && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (null != _i) { var _s, _e, _x, _r, _arr = [], _n = !0, _d = !1; try { if (_x = (_i = _i.call(arr)).next, 0 === i) { if (Object(_i) !== _i) return; _n = !1; } else for (; !(_n = (_s = _x.call(_i)).done) && (_arr.push(_s.value), _arr.length !== i); _n = !0); } catch (err) { _d = !0, _e = err; } finally { try { if (!_n && null != _i.return && (_r = _i.return(), Object(_r) !== _r)) return; } finally { if (_d) throw _e; } } return _arr; } }\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\nimport { createVNode as _createVNode, mergeProps as _mergeProps } from \"vue\";\nimport { ref, watch, computed, reactive, nextTick, onActivated, defineComponent, getCurrentInstance } from \"vue\";\nimport { pick, isDef, addUnit, isHidden, unitToPx, truthProp, numericProp, windowWidth, getElementTop, makeStringProp, callInterceptor, createNamespace, makeNumericProp, setRootScrollTop, BORDER_TOP_BOTTOM } from \"../utils/index.mjs\";\nimport { scrollLeftTo, scrollTopTo } from \"./utils.mjs\";\nimport { useRect, useChildren, useScrollParent, useEventListener, onMountedOrActivated } from \"@vant/use\";\nimport { useId } from \"../composables/use-id.mjs\";\nimport { route } from \"../composables/use-route.mjs\";\nimport { useRefs } from \"../composables/use-refs.mjs\";\nimport { useExpose } from \"../composables/use-expose.mjs\";\nimport { onPopupReopen } from \"../composables/on-popup-reopen.mjs\";\nimport { Sticky } from \"../sticky/index.mjs\";\nimport TabsTitle from \"./TabsTitle.mjs\";\nimport TabsContent from \"./TabsContent.mjs\";\nvar _createNamespace = createNamespace(\"tabs\"),\n  _createNamespace2 = _slicedToArray(_createNamespace, 2),\n  name = _createNamespace2[0],\n  bem = _createNamespace2[1];\nvar tabsProps = {\n  type: makeStringProp(\"line\"),\n  color: String,\n  border: Boolean,\n  sticky: Boolean,\n  shrink: Boolean,\n  active: makeNumericProp(0),\n  duration: makeNumericProp(0.3),\n  animated: Boolean,\n  ellipsis: truthProp,\n  swipeable: Boolean,\n  scrollspy: Boolean,\n  offsetTop: makeNumericProp(0),\n  background: String,\n  lazyRender: truthProp,\n  lineWidth: numericProp,\n  lineHeight: numericProp,\n  beforeChange: Function,\n  swipeThreshold: makeNumericProp(5),\n  titleActiveColor: String,\n  titleInactiveColor: String\n};\nvar TABS_KEY = Symbol(name);\nvar stdin_default = defineComponent({\n  name: name,\n  props: tabsProps,\n  emits: [\"click\", \"change\", \"scroll\", \"disabled\", \"rendered\", \"click-tab\", \"update:active\"],\n  setup: function setup(props, _ref) {\n    var emit = _ref.emit,\n      slots = _ref.slots;\n    var _a, _b;\n    if (process.env.NODE_ENV !== \"production\") {\n      var props2 = (_b = (_a = getCurrentInstance()) == null ? void 0 : _a.vnode) == null ? void 0 : _b.props;\n      if (props2 && \"onClick\" in props2) {\n        console.warn('[Vant] Tabs: \"click\" event is deprecated, using \"click-tab\" instead.');\n      }\n      if (props2 && \"onDisabled\" in props2) {\n        console.warn('[Vant] Tabs: \"disabled\" event is deprecated, using \"click-tab\" instead.');\n      }\n    }\n    var tabHeight;\n    var lockScroll;\n    var stickyFixed;\n    var root = ref();\n    var navRef = ref();\n    var wrapRef = ref();\n    var contentRef = ref();\n    var id = useId();\n    var scroller = useScrollParent(root);\n    var _useRefs = useRefs(),\n      _useRefs2 = _slicedToArray(_useRefs, 2),\n      titleRefs = _useRefs2[0],\n      setTitleRefs = _useRefs2[1];\n    var _useChildren = useChildren(TABS_KEY),\n      children = _useChildren.children,\n      linkChildren = _useChildren.linkChildren;\n    var state = reactive({\n      inited: false,\n      position: \"\",\n      lineStyle: {},\n      currentIndex: -1\n    });\n    var scrollable = computed(function () {\n      return children.length > props.swipeThreshold || !props.ellipsis || props.shrink;\n    });\n    var navStyle = computed(function () {\n      return {\n        borderColor: props.color,\n        background: props.background\n      };\n    });\n    var getTabName = function getTabName(tab, index) {\n      var _a2;\n      return (_a2 = tab.name) != null ? _a2 : index;\n    };\n    var currentName = computed(function () {\n      var activeTab = children[state.currentIndex];\n      if (activeTab) {\n        return getTabName(activeTab, state.currentIndex);\n      }\n    });\n    var offsetTopPx = computed(function () {\n      return unitToPx(props.offsetTop);\n    });\n    var scrollOffset = computed(function () {\n      if (props.sticky) {\n        return offsetTopPx.value + tabHeight;\n      }\n      return 0;\n    });\n    var scrollIntoView = function scrollIntoView(immediate) {\n      var nav = navRef.value;\n      var titles = titleRefs.value;\n      if (!scrollable.value || !nav || !titles || !titles[state.currentIndex]) {\n        return;\n      }\n      var title = titles[state.currentIndex].$el;\n      var to = title.offsetLeft - (nav.offsetWidth - title.offsetWidth) / 2;\n      scrollLeftTo(nav, to, immediate ? 0 : +props.duration);\n    };\n    var setLine = function setLine() {\n      var shouldAnimate = state.inited;\n      nextTick(function () {\n        var titles = titleRefs.value;\n        if (!titles || !titles[state.currentIndex] || props.type !== \"line\" || isHidden(root.value)) {\n          return;\n        }\n        var title = titles[state.currentIndex].$el;\n        var lineWidth = props.lineWidth,\n          lineHeight = props.lineHeight;\n        var left = title.offsetLeft + title.offsetWidth / 2;\n        var lineStyle = {\n          width: addUnit(lineWidth),\n          backgroundColor: props.color,\n          transform: \"translateX(\".concat(left, \"px) translateX(-50%)\")\n        };\n        if (shouldAnimate) {\n          lineStyle.transitionDuration = \"\".concat(props.duration, \"s\");\n        }\n        if (isDef(lineHeight)) {\n          var height = addUnit(lineHeight);\n          lineStyle.height = height;\n          lineStyle.borderRadius = height;\n        }\n        state.lineStyle = lineStyle;\n      });\n    };\n    var findAvailableTab = function findAvailableTab(index) {\n      var diff = index < state.currentIndex ? -1 : 1;\n      while (index >= 0 && index < children.length) {\n        if (!children[index].disabled) {\n          return index;\n        }\n        index += diff;\n      }\n    };\n    var setCurrentIndex = function setCurrentIndex(currentIndex, skipScrollIntoView) {\n      var newIndex = findAvailableTab(currentIndex);\n      if (!isDef(newIndex)) {\n        return;\n      }\n      var newTab = children[newIndex];\n      var newName = getTabName(newTab, newIndex);\n      var shouldEmitChange = state.currentIndex !== null;\n      if (state.currentIndex !== newIndex) {\n        state.currentIndex = newIndex;\n        if (!skipScrollIntoView) {\n          scrollIntoView();\n        }\n        setLine();\n      }\n      if (newName !== props.active) {\n        emit(\"update:active\", newName);\n        if (shouldEmitChange) {\n          emit(\"change\", newName, newTab.title);\n        }\n      }\n      if (stickyFixed && !props.scrollspy) {\n        setRootScrollTop(Math.ceil(getElementTop(root.value) - offsetTopPx.value));\n      }\n    };\n    var setCurrentIndexByName = function setCurrentIndexByName(name2, skipScrollIntoView) {\n      var matched = children.find(function (tab, index2) {\n        return getTabName(tab, index2) === name2;\n      });\n      var index = matched ? children.indexOf(matched) : 0;\n      setCurrentIndex(index, skipScrollIntoView);\n    };\n    var scrollToCurrentContent = function scrollToCurrentContent() {\n      var immediate = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;\n      if (props.scrollspy) {\n        var target = children[state.currentIndex].$el;\n        if (target && scroller.value) {\n          var to = getElementTop(target, scroller.value) - scrollOffset.value;\n          lockScroll = true;\n          scrollTopTo(scroller.value, to, immediate ? 0 : +props.duration, function () {\n            lockScroll = false;\n          });\n        }\n      }\n    };\n    var onClickTab = function onClickTab(item, index, event) {\n      var _children$index = children[index],\n        title = _children$index.title,\n        disabled = _children$index.disabled;\n      var name2 = getTabName(children[index], index);\n      if (disabled) {\n        emit(\"disabled\", name2, title);\n      } else {\n        callInterceptor(props.beforeChange, {\n          args: [name2],\n          done: function done() {\n            setCurrentIndex(index);\n            scrollToCurrentContent();\n          }\n        });\n        emit(\"click\", name2, title);\n        route(item);\n      }\n      emit(\"click-tab\", {\n        name: name2,\n        title: title,\n        event: event,\n        disabled: disabled\n      });\n    };\n    var onStickyScroll = function onStickyScroll(params) {\n      stickyFixed = params.isFixed;\n      emit(\"scroll\", params);\n    };\n    var scrollTo = function scrollTo(name2) {\n      nextTick(function () {\n        setCurrentIndexByName(name2);\n        scrollToCurrentContent(true);\n      });\n    };\n    var getCurrentIndexOnScroll = function getCurrentIndexOnScroll() {\n      for (var index = 0; index < children.length; index++) {\n        var _useRect = useRect(children[index].$el),\n          top = _useRect.top;\n        if (top > scrollOffset.value) {\n          return index === 0 ? 0 : index - 1;\n        }\n      }\n      return children.length - 1;\n    };\n    var onScroll = function onScroll() {\n      if (props.scrollspy && !lockScroll) {\n        var index = getCurrentIndexOnScroll();\n        setCurrentIndex(index);\n      }\n    };\n    var renderNav = function renderNav() {\n      return children.map(function (item, index) {\n        return _createVNode(TabsTitle, _mergeProps({\n          \"key\": item.id,\n          \"id\": \"\".concat(id, \"-\").concat(index),\n          \"ref\": setTitleRefs(index),\n          \"type\": props.type,\n          \"color\": props.color,\n          \"style\": item.titleStyle,\n          \"class\": item.titleClass,\n          \"shrink\": props.shrink,\n          \"isActive\": index === state.currentIndex,\n          \"controls\": item.id,\n          \"scrollable\": scrollable.value,\n          \"activeColor\": props.titleActiveColor,\n          \"inactiveColor\": props.titleInactiveColor,\n          \"onClick\": function onClick(event) {\n            return onClickTab(item, index, event);\n          }\n        }, pick(item, [\"dot\", \"badge\", \"title\", \"disabled\", \"showZeroBadge\"])), {\n          title: item.$slots.title\n        });\n      });\n    };\n    var renderLine = function renderLine() {\n      if (props.type === \"line\" && children.length) {\n        return _createVNode(\"div\", {\n          \"class\": bem(\"line\"),\n          \"style\": state.lineStyle\n        }, null);\n      }\n    };\n    var renderHeader = function renderHeader() {\n      var _a2, _b2, _c;\n      var type = props.type,\n        border = props.border,\n        sticky = props.sticky;\n      var Header = [_createVNode(\"div\", {\n        \"ref\": sticky ? void 0 : wrapRef,\n        \"class\": [bem(\"wrap\"), _defineProperty({}, BORDER_TOP_BOTTOM, type === \"line\" && border)]\n      }, [_createVNode(\"div\", {\n        \"ref\": navRef,\n        \"role\": \"tablist\",\n        \"class\": bem(\"nav\", [type, {\n          shrink: props.shrink,\n          complete: scrollable.value\n        }]),\n        \"style\": navStyle.value,\n        \"aria-orientation\": \"horizontal\"\n      }, [(_a2 = slots[\"nav-left\"]) == null ? void 0 : _a2.call(slots), renderNav(), renderLine(), (_b2 = slots[\"nav-right\"]) == null ? void 0 : _b2.call(slots)])]), (_c = slots[\"nav-bottom\"]) == null ? void 0 : _c.call(slots)];\n      if (sticky) {\n        return _createVNode(\"div\", {\n          \"ref\": wrapRef\n        }, [Header]);\n      }\n      return Header;\n    };\n    watch([function () {\n      return props.color;\n    }, windowWidth], setLine);\n    watch(function () {\n      return props.active;\n    }, function (value) {\n      if (value !== currentName.value) {\n        setCurrentIndexByName(value);\n      }\n    });\n    watch(function () {\n      return children.length;\n    }, function () {\n      if (state.inited) {\n        setCurrentIndexByName(props.active);\n        setLine();\n        nextTick(function () {\n          scrollIntoView(true);\n        });\n      }\n    });\n    var init = function init() {\n      setCurrentIndexByName(props.active, true);\n      nextTick(function () {\n        state.inited = true;\n        if (wrapRef.value) {\n          tabHeight = useRect(wrapRef.value).height;\n        }\n        scrollIntoView(true);\n      });\n    };\n    var onRendered = function onRendered(name2, title) {\n      return emit(\"rendered\", name2, title);\n    };\n    var resize = function resize() {\n      setLine();\n      nextTick(function () {\n        var _a2, _b2;\n        return (_b2 = (_a2 = contentRef.value) == null ? void 0 : _a2.swipeRef.value) == null ? void 0 : _b2.resize();\n      });\n    };\n    useExpose({\n      resize: resize,\n      scrollTo: scrollTo\n    });\n    onActivated(setLine);\n    onPopupReopen(setLine);\n    onMountedOrActivated(init);\n    useEventListener(\"scroll\", onScroll, {\n      target: scroller,\n      passive: true\n    });\n    linkChildren({\n      id: id,\n      props: props,\n      setLine: setLine,\n      onRendered: onRendered,\n      currentName: currentName,\n      scrollIntoView: scrollIntoView\n    });\n    return function () {\n      return _createVNode(\"div\", {\n        \"ref\": root,\n        \"class\": bem([props.type])\n      }, [props.sticky ? _createVNode(Sticky, {\n        \"container\": root.value,\n        \"offsetTop\": offsetTopPx.value,\n        \"onScroll\": onStickyScroll\n      }, {\n        default: function _default() {\n          return [renderHeader()];\n        }\n      }) : renderHeader(), _createVNode(TabsContent, {\n        \"ref\": contentRef,\n        \"count\": children.length,\n        \"inited\": state.inited,\n        \"animated\": props.animated,\n        \"duration\": props.duration,\n        \"swipeable\": props.swipeable,\n        \"lazyRender\": props.lazyRender,\n        \"currentIndex\": state.currentIndex,\n        \"onChange\": setCurrentIndex\n      }, {\n        default: function _default() {\n          var _a2;\n          return [(_a2 = slots.default) == null ? void 0 : _a2.call(slots)];\n        }\n      })]);\n    };\n  }\n});\nexport { TABS_KEY, stdin_default as default };", "map": {"version": 3, "names": ["createVNode", "_createVNode", "mergeProps", "_mergeProps", "ref", "watch", "computed", "reactive", "nextTick", "onActivated", "defineComponent", "getCurrentInstance", "pick", "isDef", "addUnit", "isHidden", "unitToPx", "truthProp", "numericProp", "windowWidth", "getElementTop", "makeStringProp", "callInterceptor", "createNamespace", "makeNumericProp", "setRootScrollTop", "BORDER_TOP_BOTTOM", "scrollLeftTo", "scrollTopTo", "useRect", "useChildren", "useScrollParent", "useEventListener", "onMountedOrActivated", "useId", "route", "useRefs", "useExpose", "onPopupReopen", "<PERSON>y", "TabsTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_createNamespace", "_createNamespace2", "_slicedToArray", "name", "bem", "tabsProps", "type", "color", "String", "border", "Boolean", "sticky", "shrink", "active", "duration", "animated", "ellipsis", "swipeable", "scrollspy", "offsetTop", "background", "lazy<PERSON>ender", "lineWidth", "lineHeight", "beforeChange", "Function", "swipe<PERSON><PERSON><PERSON><PERSON>", "titleActiveColor", "titleInactiveColor", "TABS_KEY", "Symbol", "stdin_default", "props", "emits", "setup", "_ref", "emit", "slots", "_a", "_b", "process", "env", "NODE_ENV", "props2", "vnode", "console", "warn", "tabHeight", "lockScroll", "stickyFixed", "root", "navRef", "wrapRef", "contentRef", "id", "scroller", "_useRefs", "_useRefs2", "titleRefs", "setTitleRefs", "_useChildren", "children", "linkChildren", "state", "inited", "position", "lineStyle", "currentIndex", "scrollable", "length", "navStyle", "borderColor", "getTabName", "tab", "index", "_a2", "currentName", "activeTab", "offsetTopPx", "scrollOffset", "value", "scrollIntoView", "immediate", "nav", "titles", "title", "$el", "to", "offsetLeft", "offsetWidth", "setLine", "shouldAnimate", "left", "width", "backgroundColor", "transform", "concat", "transitionDuration", "height", "borderRadius", "findAvailableTab", "diff", "disabled", "setCurrentIndex", "skipScrollIntoView", "newIndex", "newTab", "newName", "shouldEmitChange", "Math", "ceil", "setCurrentIndexByName", "name2", "matched", "find", "index2", "indexOf", "scrollToCurrentContent", "arguments", "undefined", "target", "onClickTab", "item", "event", "_children$index", "args", "done", "onStickyScroll", "params", "isFixed", "scrollTo", "getCurrentIndexOnScroll", "_useRect", "top", "onScroll", "renderNav", "map", "titleStyle", "titleClass", "onClick", "$slots", "renderLine", "renderHeader", "_b2", "_c", "Header", "_defineProperty", "complete", "call", "init", "onRendered", "resize", "swipeRef", "passive", "default", "_default"], "sources": ["C:/Users/<USER>/Desktop/vue3_3.0/node_modules/vant/es/tabs/Tabs.mjs"], "sourcesContent": ["import { createVNode as _createVNode, mergeProps as _mergeProps } from \"vue\";\nimport { ref, watch, computed, reactive, nextTick, onActivated, defineComponent, getCurrentInstance } from \"vue\";\nimport { pick, isDef, addUnit, isHidden, unitToPx, truthProp, numericProp, windowWidth, getElementTop, makeStringProp, callInterceptor, createNamespace, makeNumericProp, setRootScrollTop, BORDER_TOP_BOTTOM } from \"../utils/index.mjs\";\nimport { scrollLeftTo, scrollTopTo } from \"./utils.mjs\";\nimport { useRect, useChildren, useScrollParent, useEventListener, onMountedOrActivated } from \"@vant/use\";\nimport { useId } from \"../composables/use-id.mjs\";\nimport { route } from \"../composables/use-route.mjs\";\nimport { useRefs } from \"../composables/use-refs.mjs\";\nimport { useExpose } from \"../composables/use-expose.mjs\";\nimport { onPopupReopen } from \"../composables/on-popup-reopen.mjs\";\nimport { Sticky } from \"../sticky/index.mjs\";\nimport TabsTitle from \"./TabsTitle.mjs\";\nimport TabsContent from \"./TabsContent.mjs\";\nconst [name, bem] = createNamespace(\"tabs\");\nconst tabsProps = {\n  type: makeStringProp(\"line\"),\n  color: String,\n  border: Boolean,\n  sticky: Boolean,\n  shrink: Boolean,\n  active: makeNumericProp(0),\n  duration: makeNumericProp(0.3),\n  animated: Boolean,\n  ellipsis: truthProp,\n  swipeable: Boolean,\n  scrollspy: Boolean,\n  offsetTop: makeNumericProp(0),\n  background: String,\n  lazyRender: truthProp,\n  lineWidth: numericProp,\n  lineHeight: numericProp,\n  beforeChange: Function,\n  swipeThreshold: makeNumericProp(5),\n  titleActiveColor: String,\n  titleInactiveColor: String\n};\nconst TABS_KEY = Symbol(name);\nvar stdin_default = defineComponent({\n  name,\n  props: tabsProps,\n  emits: [\"click\", \"change\", \"scroll\", \"disabled\", \"rendered\", \"click-tab\", \"update:active\"],\n  setup(props, {\n    emit,\n    slots\n  }) {\n    var _a, _b;\n    if (process.env.NODE_ENV !== \"production\") {\n      const props2 = (_b = (_a = getCurrentInstance()) == null ? void 0 : _a.vnode) == null ? void 0 : _b.props;\n      if (props2 && \"onClick\" in props2) {\n        console.warn('[Vant] Tabs: \"click\" event is deprecated, using \"click-tab\" instead.');\n      }\n      if (props2 && \"onDisabled\" in props2) {\n        console.warn('[Vant] Tabs: \"disabled\" event is deprecated, using \"click-tab\" instead.');\n      }\n    }\n    let tabHeight;\n    let lockScroll;\n    let stickyFixed;\n    const root = ref();\n    const navRef = ref();\n    const wrapRef = ref();\n    const contentRef = ref();\n    const id = useId();\n    const scroller = useScrollParent(root);\n    const [titleRefs, setTitleRefs] = useRefs();\n    const {\n      children,\n      linkChildren\n    } = useChildren(TABS_KEY);\n    const state = reactive({\n      inited: false,\n      position: \"\",\n      lineStyle: {},\n      currentIndex: -1\n    });\n    const scrollable = computed(() => children.length > props.swipeThreshold || !props.ellipsis || props.shrink);\n    const navStyle = computed(() => ({\n      borderColor: props.color,\n      background: props.background\n    }));\n    const getTabName = (tab, index) => {\n      var _a2;\n      return (_a2 = tab.name) != null ? _a2 : index;\n    };\n    const currentName = computed(() => {\n      const activeTab = children[state.currentIndex];\n      if (activeTab) {\n        return getTabName(activeTab, state.currentIndex);\n      }\n    });\n    const offsetTopPx = computed(() => unitToPx(props.offsetTop));\n    const scrollOffset = computed(() => {\n      if (props.sticky) {\n        return offsetTopPx.value + tabHeight;\n      }\n      return 0;\n    });\n    const scrollIntoView = (immediate) => {\n      const nav = navRef.value;\n      const titles = titleRefs.value;\n      if (!scrollable.value || !nav || !titles || !titles[state.currentIndex]) {\n        return;\n      }\n      const title = titles[state.currentIndex].$el;\n      const to = title.offsetLeft - (nav.offsetWidth - title.offsetWidth) / 2;\n      scrollLeftTo(nav, to, immediate ? 0 : +props.duration);\n    };\n    const setLine = () => {\n      const shouldAnimate = state.inited;\n      nextTick(() => {\n        const titles = titleRefs.value;\n        if (!titles || !titles[state.currentIndex] || props.type !== \"line\" || isHidden(root.value)) {\n          return;\n        }\n        const title = titles[state.currentIndex].$el;\n        const {\n          lineWidth,\n          lineHeight\n        } = props;\n        const left = title.offsetLeft + title.offsetWidth / 2;\n        const lineStyle = {\n          width: addUnit(lineWidth),\n          backgroundColor: props.color,\n          transform: `translateX(${left}px) translateX(-50%)`\n        };\n        if (shouldAnimate) {\n          lineStyle.transitionDuration = `${props.duration}s`;\n        }\n        if (isDef(lineHeight)) {\n          const height = addUnit(lineHeight);\n          lineStyle.height = height;\n          lineStyle.borderRadius = height;\n        }\n        state.lineStyle = lineStyle;\n      });\n    };\n    const findAvailableTab = (index) => {\n      const diff = index < state.currentIndex ? -1 : 1;\n      while (index >= 0 && index < children.length) {\n        if (!children[index].disabled) {\n          return index;\n        }\n        index += diff;\n      }\n    };\n    const setCurrentIndex = (currentIndex, skipScrollIntoView) => {\n      const newIndex = findAvailableTab(currentIndex);\n      if (!isDef(newIndex)) {\n        return;\n      }\n      const newTab = children[newIndex];\n      const newName = getTabName(newTab, newIndex);\n      const shouldEmitChange = state.currentIndex !== null;\n      if (state.currentIndex !== newIndex) {\n        state.currentIndex = newIndex;\n        if (!skipScrollIntoView) {\n          scrollIntoView();\n        }\n        setLine();\n      }\n      if (newName !== props.active) {\n        emit(\"update:active\", newName);\n        if (shouldEmitChange) {\n          emit(\"change\", newName, newTab.title);\n        }\n      }\n      if (stickyFixed && !props.scrollspy) {\n        setRootScrollTop(Math.ceil(getElementTop(root.value) - offsetTopPx.value));\n      }\n    };\n    const setCurrentIndexByName = (name2, skipScrollIntoView) => {\n      const matched = children.find((tab, index2) => getTabName(tab, index2) === name2);\n      const index = matched ? children.indexOf(matched) : 0;\n      setCurrentIndex(index, skipScrollIntoView);\n    };\n    const scrollToCurrentContent = (immediate = false) => {\n      if (props.scrollspy) {\n        const target = children[state.currentIndex].$el;\n        if (target && scroller.value) {\n          const to = getElementTop(target, scroller.value) - scrollOffset.value;\n          lockScroll = true;\n          scrollTopTo(scroller.value, to, immediate ? 0 : +props.duration, () => {\n            lockScroll = false;\n          });\n        }\n      }\n    };\n    const onClickTab = (item, index, event) => {\n      const {\n        title,\n        disabled\n      } = children[index];\n      const name2 = getTabName(children[index], index);\n      if (disabled) {\n        emit(\"disabled\", name2, title);\n      } else {\n        callInterceptor(props.beforeChange, {\n          args: [name2],\n          done: () => {\n            setCurrentIndex(index);\n            scrollToCurrentContent();\n          }\n        });\n        emit(\"click\", name2, title);\n        route(item);\n      }\n      emit(\"click-tab\", {\n        name: name2,\n        title,\n        event,\n        disabled\n      });\n    };\n    const onStickyScroll = (params) => {\n      stickyFixed = params.isFixed;\n      emit(\"scroll\", params);\n    };\n    const scrollTo = (name2) => {\n      nextTick(() => {\n        setCurrentIndexByName(name2);\n        scrollToCurrentContent(true);\n      });\n    };\n    const getCurrentIndexOnScroll = () => {\n      for (let index = 0; index < children.length; index++) {\n        const {\n          top\n        } = useRect(children[index].$el);\n        if (top > scrollOffset.value) {\n          return index === 0 ? 0 : index - 1;\n        }\n      }\n      return children.length - 1;\n    };\n    const onScroll = () => {\n      if (props.scrollspy && !lockScroll) {\n        const index = getCurrentIndexOnScroll();\n        setCurrentIndex(index);\n      }\n    };\n    const renderNav = () => children.map((item, index) => _createVNode(TabsTitle, _mergeProps({\n      \"key\": item.id,\n      \"id\": `${id}-${index}`,\n      \"ref\": setTitleRefs(index),\n      \"type\": props.type,\n      \"color\": props.color,\n      \"style\": item.titleStyle,\n      \"class\": item.titleClass,\n      \"shrink\": props.shrink,\n      \"isActive\": index === state.currentIndex,\n      \"controls\": item.id,\n      \"scrollable\": scrollable.value,\n      \"activeColor\": props.titleActiveColor,\n      \"inactiveColor\": props.titleInactiveColor,\n      \"onClick\": (event) => onClickTab(item, index, event)\n    }, pick(item, [\"dot\", \"badge\", \"title\", \"disabled\", \"showZeroBadge\"])), {\n      title: item.$slots.title\n    }));\n    const renderLine = () => {\n      if (props.type === \"line\" && children.length) {\n        return _createVNode(\"div\", {\n          \"class\": bem(\"line\"),\n          \"style\": state.lineStyle\n        }, null);\n      }\n    };\n    const renderHeader = () => {\n      var _a2, _b2, _c;\n      const {\n        type,\n        border,\n        sticky\n      } = props;\n      const Header = [_createVNode(\"div\", {\n        \"ref\": sticky ? void 0 : wrapRef,\n        \"class\": [bem(\"wrap\"), {\n          [BORDER_TOP_BOTTOM]: type === \"line\" && border\n        }]\n      }, [_createVNode(\"div\", {\n        \"ref\": navRef,\n        \"role\": \"tablist\",\n        \"class\": bem(\"nav\", [type, {\n          shrink: props.shrink,\n          complete: scrollable.value\n        }]),\n        \"style\": navStyle.value,\n        \"aria-orientation\": \"horizontal\"\n      }, [(_a2 = slots[\"nav-left\"]) == null ? void 0 : _a2.call(slots), renderNav(), renderLine(), (_b2 = slots[\"nav-right\"]) == null ? void 0 : _b2.call(slots)])]), (_c = slots[\"nav-bottom\"]) == null ? void 0 : _c.call(slots)];\n      if (sticky) {\n        return _createVNode(\"div\", {\n          \"ref\": wrapRef\n        }, [Header]);\n      }\n      return Header;\n    };\n    watch([() => props.color, windowWidth], setLine);\n    watch(() => props.active, (value) => {\n      if (value !== currentName.value) {\n        setCurrentIndexByName(value);\n      }\n    });\n    watch(() => children.length, () => {\n      if (state.inited) {\n        setCurrentIndexByName(props.active);\n        setLine();\n        nextTick(() => {\n          scrollIntoView(true);\n        });\n      }\n    });\n    const init = () => {\n      setCurrentIndexByName(props.active, true);\n      nextTick(() => {\n        state.inited = true;\n        if (wrapRef.value) {\n          tabHeight = useRect(wrapRef.value).height;\n        }\n        scrollIntoView(true);\n      });\n    };\n    const onRendered = (name2, title) => emit(\"rendered\", name2, title);\n    const resize = () => {\n      setLine();\n      nextTick(() => {\n        var _a2, _b2;\n        return (_b2 = (_a2 = contentRef.value) == null ? void 0 : _a2.swipeRef.value) == null ? void 0 : _b2.resize();\n      });\n    };\n    useExpose({\n      resize,\n      scrollTo\n    });\n    onActivated(setLine);\n    onPopupReopen(setLine);\n    onMountedOrActivated(init);\n    useEventListener(\"scroll\", onScroll, {\n      target: scroller,\n      passive: true\n    });\n    linkChildren({\n      id,\n      props,\n      setLine,\n      onRendered,\n      currentName,\n      scrollIntoView\n    });\n    return () => _createVNode(\"div\", {\n      \"ref\": root,\n      \"class\": bem([props.type])\n    }, [props.sticky ? _createVNode(Sticky, {\n      \"container\": root.value,\n      \"offsetTop\": offsetTopPx.value,\n      \"onScroll\": onStickyScroll\n    }, {\n      default: () => [renderHeader()]\n    }) : renderHeader(), _createVNode(TabsContent, {\n      \"ref\": contentRef,\n      \"count\": children.length,\n      \"inited\": state.inited,\n      \"animated\": props.animated,\n      \"duration\": props.duration,\n      \"swipeable\": props.swipeable,\n      \"lazyRender\": props.lazyRender,\n      \"currentIndex\": state.currentIndex,\n      \"onChange\": setCurrentIndex\n    }, {\n      default: () => {\n        var _a2;\n        return [(_a2 = slots.default) == null ? void 0 : _a2.call(slots)];\n      }\n    })]);\n  }\n});\nexport {\n  TABS_KEY,\n  stdin_default as default\n};\n"], "mappings": ";;;;;;;;;;AAAA,SAASA,WAAW,IAAIC,YAAY,EAAEC,UAAU,IAAIC,WAAW,QAAQ,KAAK;AAC5E,SAASC,GAAG,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,WAAW,EAAEC,eAAe,EAAEC,kBAAkB,QAAQ,KAAK;AAChH,SAASC,IAAI,EAAEC,KAAK,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,WAAW,EAAEC,aAAa,EAAEC,cAAc,EAAEC,eAAe,EAAEC,eAAe,EAAEC,eAAe,EAAEC,gBAAgB,EAAEC,iBAAiB,QAAQ,oBAAoB;AACzO,SAASC,YAAY,EAAEC,WAAW,QAAQ,aAAa;AACvD,SAASC,OAAO,EAAEC,WAAW,EAAEC,eAAe,EAAEC,gBAAgB,EAAEC,oBAAoB,QAAQ,WAAW;AACzG,SAASC,KAAK,QAAQ,2BAA2B;AACjD,SAASC,KAAK,QAAQ,8BAA8B;AACpD,SAASC,OAAO,QAAQ,6BAA6B;AACrD,SAASC,SAAS,QAAQ,+BAA+B;AACzD,SAASC,aAAa,QAAQ,oCAAoC;AAClE,SAASC,MAAM,QAAQ,qBAAqB;AAC5C,OAAOC,SAAS,MAAM,iBAAiB;AACvC,OAAOC,WAAW,MAAM,mBAAmB;AAC3C,IAAAC,gBAAA,GAAoBnB,eAAe,CAAC,MAAM,CAAC;EAAAoB,iBAAA,GAAAC,cAAA,CAAAF,gBAAA;EAApCG,IAAI,GAAAF,iBAAA;EAAEG,GAAG,GAAAH,iBAAA;AAChB,IAAMI,SAAS,GAAG;EAChBC,IAAI,EAAE3B,cAAc,CAAC,MAAM,CAAC;EAC5B4B,KAAK,EAAEC,MAAM;EACbC,MAAM,EAAEC,OAAO;EACfC,MAAM,EAAED,OAAO;EACfE,MAAM,EAAEF,OAAO;EACfG,MAAM,EAAE/B,eAAe,CAAC,CAAC,CAAC;EAC1BgC,QAAQ,EAAEhC,eAAe,CAAC,GAAG,CAAC;EAC9BiC,QAAQ,EAAEL,OAAO;EACjBM,QAAQ,EAAEzC,SAAS;EACnB0C,SAAS,EAAEP,OAAO;EAClBQ,SAAS,EAAER,OAAO;EAClBS,SAAS,EAAErC,eAAe,CAAC,CAAC,CAAC;EAC7BsC,UAAU,EAAEZ,MAAM;EAClBa,UAAU,EAAE9C,SAAS;EACrB+C,SAAS,EAAE9C,WAAW;EACtB+C,UAAU,EAAE/C,WAAW;EACvBgD,YAAY,EAAEC,QAAQ;EACtBC,cAAc,EAAE5C,eAAe,CAAC,CAAC,CAAC;EAClC6C,gBAAgB,EAAEnB,MAAM;EACxBoB,kBAAkB,EAAEpB;AACtB,CAAC;AACD,IAAMqB,QAAQ,GAAGC,MAAM,CAAC3B,IAAI,CAAC;AAC7B,IAAI4B,aAAa,GAAG/D,eAAe,CAAC;EAClCmC,IAAI,EAAJA,IAAI;EACJ6B,KAAK,EAAE3B,SAAS;EAChB4B,KAAK,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,UAAU,EAAE,UAAU,EAAE,WAAW,EAAE,eAAe,CAAC;EAC1FC,KAAK,WAAAA,MAACF,KAAK,EAAAG,IAAA,EAGR;IAAA,IAFDC,IAAI,GAAAD,IAAA,CAAJC,IAAI;MACJC,KAAK,GAAAF,IAAA,CAALE,KAAK;IAEL,IAAIC,EAAE,EAAEC,EAAE;IACV,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC,IAAMC,MAAM,GAAG,CAACJ,EAAE,GAAG,CAACD,EAAE,GAAGrE,kBAAkB,CAAC,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGqE,EAAE,CAACM,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGL,EAAE,CAACP,KAAK;MACzG,IAAIW,MAAM,IAAI,SAAS,IAAIA,MAAM,EAAE;QACjCE,OAAO,CAACC,IAAI,CAAC,sEAAsE,CAAC;MACtF;MACA,IAAIH,MAAM,IAAI,YAAY,IAAIA,MAAM,EAAE;QACpCE,OAAO,CAACC,IAAI,CAAC,yEAAyE,CAAC;MACzF;IACF;IACA,IAAIC,SAAS;IACb,IAAIC,UAAU;IACd,IAAIC,WAAW;IACf,IAAMC,IAAI,GAAGxF,GAAG,CAAC,CAAC;IAClB,IAAMyF,MAAM,GAAGzF,GAAG,CAAC,CAAC;IACpB,IAAM0F,OAAO,GAAG1F,GAAG,CAAC,CAAC;IACrB,IAAM2F,UAAU,GAAG3F,GAAG,CAAC,CAAC;IACxB,IAAM4F,EAAE,GAAG9D,KAAK,CAAC,CAAC;IAClB,IAAM+D,QAAQ,GAAGlE,eAAe,CAAC6D,IAAI,CAAC;IACtC,IAAAM,QAAA,GAAkC9D,OAAO,CAAC,CAAC;MAAA+D,SAAA,GAAAvD,cAAA,CAAAsD,QAAA;MAApCE,SAAS,GAAAD,SAAA;MAAEE,YAAY,GAAAF,SAAA;IAC9B,IAAAG,YAAA,GAGIxE,WAAW,CAACyC,QAAQ,CAAC;MAFvBgC,QAAQ,GAAAD,YAAA,CAARC,QAAQ;MACRC,YAAY,GAAAF,YAAA,CAAZE,YAAY;IAEd,IAAMC,KAAK,GAAGlG,QAAQ,CAAC;MACrBmG,MAAM,EAAE,KAAK;MACbC,QAAQ,EAAE,EAAE;MACZC,SAAS,EAAE,CAAC,CAAC;MACbC,YAAY,EAAE,CAAC;IACjB,CAAC,CAAC;IACF,IAAMC,UAAU,GAAGxG,QAAQ,CAAC;MAAA,OAAMiG,QAAQ,CAACQ,MAAM,GAAGrC,KAAK,CAACN,cAAc,IAAI,CAACM,KAAK,CAAChB,QAAQ,IAAIgB,KAAK,CAACpB,MAAM;IAAA,EAAC;IAC5G,IAAM0D,QAAQ,GAAG1G,QAAQ,CAAC;MAAA,OAAO;QAC/B2G,WAAW,EAAEvC,KAAK,CAACzB,KAAK;QACxBa,UAAU,EAAEY,KAAK,CAACZ;MACpB,CAAC;IAAA,CAAC,CAAC;IACH,IAAMoD,UAAU,GAAG,SAAbA,UAAUA,CAAIC,GAAG,EAAEC,KAAK,EAAK;MACjC,IAAIC,GAAG;MACP,OAAO,CAACA,GAAG,GAAGF,GAAG,CAACtE,IAAI,KAAK,IAAI,GAAGwE,GAAG,GAAGD,KAAK;IAC/C,CAAC;IACD,IAAME,WAAW,GAAGhH,QAAQ,CAAC,YAAM;MACjC,IAAMiH,SAAS,GAAGhB,QAAQ,CAACE,KAAK,CAACI,YAAY,CAAC;MAC9C,IAAIU,SAAS,EAAE;QACb,OAAOL,UAAU,CAACK,SAAS,EAAEd,KAAK,CAACI,YAAY,CAAC;MAClD;IACF,CAAC,CAAC;IACF,IAAMW,WAAW,GAAGlH,QAAQ,CAAC;MAAA,OAAMU,QAAQ,CAAC0D,KAAK,CAACb,SAAS,CAAC;IAAA,EAAC;IAC7D,IAAM4D,YAAY,GAAGnH,QAAQ,CAAC,YAAM;MAClC,IAAIoE,KAAK,CAACrB,MAAM,EAAE;QAChB,OAAOmE,WAAW,CAACE,KAAK,GAAGjC,SAAS;MACtC;MACA,OAAO,CAAC;IACV,CAAC,CAAC;IACF,IAAMkC,cAAc,GAAG,SAAjBA,cAAcA,CAAIC,SAAS,EAAK;MACpC,IAAMC,GAAG,GAAGhC,MAAM,CAAC6B,KAAK;MACxB,IAAMI,MAAM,GAAG1B,SAAS,CAACsB,KAAK;MAC9B,IAAI,CAACZ,UAAU,CAACY,KAAK,IAAI,CAACG,GAAG,IAAI,CAACC,MAAM,IAAI,CAACA,MAAM,CAACrB,KAAK,CAACI,YAAY,CAAC,EAAE;QACvE;MACF;MACA,IAAMkB,KAAK,GAAGD,MAAM,CAACrB,KAAK,CAACI,YAAY,CAAC,CAACmB,GAAG;MAC5C,IAAMC,EAAE,GAAGF,KAAK,CAACG,UAAU,GAAG,CAACL,GAAG,CAACM,WAAW,GAAGJ,KAAK,CAACI,WAAW,IAAI,CAAC;MACvExG,YAAY,CAACkG,GAAG,EAAEI,EAAE,EAAEL,SAAS,GAAG,CAAC,GAAG,CAAClD,KAAK,CAAClB,QAAQ,CAAC;IACxD,CAAC;IACD,IAAM4E,OAAO,GAAG,SAAVA,OAAOA,CAAA,EAAS;MACpB,IAAMC,aAAa,GAAG5B,KAAK,CAACC,MAAM;MAClClG,QAAQ,CAAC,YAAM;QACb,IAAMsH,MAAM,GAAG1B,SAAS,CAACsB,KAAK;QAC9B,IAAI,CAACI,MAAM,IAAI,CAACA,MAAM,CAACrB,KAAK,CAACI,YAAY,CAAC,IAAInC,KAAK,CAAC1B,IAAI,KAAK,MAAM,IAAIjC,QAAQ,CAAC6E,IAAI,CAAC8B,KAAK,CAAC,EAAE;UAC3F;QACF;QACA,IAAMK,KAAK,GAAGD,MAAM,CAACrB,KAAK,CAACI,YAAY,CAAC,CAACmB,GAAG;QAC5C,IACEhE,SAAS,GAEPU,KAAK,CAFPV,SAAS;UACTC,UAAU,GACRS,KAAK,CADPT,UAAU;QAEZ,IAAMqE,IAAI,GAAGP,KAAK,CAACG,UAAU,GAAGH,KAAK,CAACI,WAAW,GAAG,CAAC;QACrD,IAAMvB,SAAS,GAAG;UAChB2B,KAAK,EAAEzH,OAAO,CAACkD,SAAS,CAAC;UACzBwE,eAAe,EAAE9D,KAAK,CAACzB,KAAK;UAC5BwF,SAAS,gBAAAC,MAAA,CAAgBJ,IAAI;QAC/B,CAAC;QACD,IAAID,aAAa,EAAE;UACjBzB,SAAS,CAAC+B,kBAAkB,MAAAD,MAAA,CAAMhE,KAAK,CAAClB,QAAQ,MAAG;QACrD;QACA,IAAI3C,KAAK,CAACoD,UAAU,CAAC,EAAE;UACrB,IAAM2E,MAAM,GAAG9H,OAAO,CAACmD,UAAU,CAAC;UAClC2C,SAAS,CAACgC,MAAM,GAAGA,MAAM;UACzBhC,SAAS,CAACiC,YAAY,GAAGD,MAAM;QACjC;QACAnC,KAAK,CAACG,SAAS,GAAGA,SAAS;MAC7B,CAAC,CAAC;IACJ,CAAC;IACD,IAAMkC,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAI1B,KAAK,EAAK;MAClC,IAAM2B,IAAI,GAAG3B,KAAK,GAAGX,KAAK,CAACI,YAAY,GAAG,CAAC,CAAC,GAAG,CAAC;MAChD,OAAOO,KAAK,IAAI,CAAC,IAAIA,KAAK,GAAGb,QAAQ,CAACQ,MAAM,EAAE;QAC5C,IAAI,CAACR,QAAQ,CAACa,KAAK,CAAC,CAAC4B,QAAQ,EAAE;UAC7B,OAAO5B,KAAK;QACd;QACAA,KAAK,IAAI2B,IAAI;MACf;IACF,CAAC;IACD,IAAME,eAAe,GAAG,SAAlBA,eAAeA,CAAIpC,YAAY,EAAEqC,kBAAkB,EAAK;MAC5D,IAAMC,QAAQ,GAAGL,gBAAgB,CAACjC,YAAY,CAAC;MAC/C,IAAI,CAAChG,KAAK,CAACsI,QAAQ,CAAC,EAAE;QACpB;MACF;MACA,IAAMC,MAAM,GAAG7C,QAAQ,CAAC4C,QAAQ,CAAC;MACjC,IAAME,OAAO,GAAGnC,UAAU,CAACkC,MAAM,EAAED,QAAQ,CAAC;MAC5C,IAAMG,gBAAgB,GAAG7C,KAAK,CAACI,YAAY,KAAK,IAAI;MACpD,IAAIJ,KAAK,CAACI,YAAY,KAAKsC,QAAQ,EAAE;QACnC1C,KAAK,CAACI,YAAY,GAAGsC,QAAQ;QAC7B,IAAI,CAACD,kBAAkB,EAAE;UACvBvB,cAAc,CAAC,CAAC;QAClB;QACAS,OAAO,CAAC,CAAC;MACX;MACA,IAAIiB,OAAO,KAAK3E,KAAK,CAACnB,MAAM,EAAE;QAC5BuB,IAAI,CAAC,eAAe,EAAEuE,OAAO,CAAC;QAC9B,IAAIC,gBAAgB,EAAE;UACpBxE,IAAI,CAAC,QAAQ,EAAEuE,OAAO,EAAED,MAAM,CAACrB,KAAK,CAAC;QACvC;MACF;MACA,IAAIpC,WAAW,IAAI,CAACjB,KAAK,CAACd,SAAS,EAAE;QACnCnC,gBAAgB,CAAC8H,IAAI,CAACC,IAAI,CAACpI,aAAa,CAACwE,IAAI,CAAC8B,KAAK,CAAC,GAAGF,WAAW,CAACE,KAAK,CAAC,CAAC;MAC5E;IACF,CAAC;IACD,IAAM+B,qBAAqB,GAAG,SAAxBA,qBAAqBA,CAAIC,KAAK,EAAER,kBAAkB,EAAK;MAC3D,IAAMS,OAAO,GAAGpD,QAAQ,CAACqD,IAAI,CAAC,UAACzC,GAAG,EAAE0C,MAAM;QAAA,OAAK3C,UAAU,CAACC,GAAG,EAAE0C,MAAM,CAAC,KAAKH,KAAK;MAAA,EAAC;MACjF,IAAMtC,KAAK,GAAGuC,OAAO,GAAGpD,QAAQ,CAACuD,OAAO,CAACH,OAAO,CAAC,GAAG,CAAC;MACrDV,eAAe,CAAC7B,KAAK,EAAE8B,kBAAkB,CAAC;IAC5C,CAAC;IACD,IAAMa,sBAAsB,GAAG,SAAzBA,sBAAsBA,CAAA,EAA0B;MAAA,IAAtBnC,SAAS,GAAAoC,SAAA,CAAAjD,MAAA,QAAAiD,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,KAAK;MAC/C,IAAItF,KAAK,CAACd,SAAS,EAAE;QACnB,IAAMsG,MAAM,GAAG3D,QAAQ,CAACE,KAAK,CAACI,YAAY,CAAC,CAACmB,GAAG;QAC/C,IAAIkC,MAAM,IAAIjE,QAAQ,CAACyB,KAAK,EAAE;UAC5B,IAAMO,EAAE,GAAG7G,aAAa,CAAC8I,MAAM,EAAEjE,QAAQ,CAACyB,KAAK,CAAC,GAAGD,YAAY,CAACC,KAAK;UACrEhC,UAAU,GAAG,IAAI;UACjB9D,WAAW,CAACqE,QAAQ,CAACyB,KAAK,EAAEO,EAAE,EAAEL,SAAS,GAAG,CAAC,GAAG,CAAClD,KAAK,CAAClB,QAAQ,EAAE,YAAM;YACrEkC,UAAU,GAAG,KAAK;UACpB,CAAC,CAAC;QACJ;MACF;IACF,CAAC;IACD,IAAMyE,UAAU,GAAG,SAAbA,UAAUA,CAAIC,IAAI,EAAEhD,KAAK,EAAEiD,KAAK,EAAK;MACzC,IAAAC,eAAA,GAGI/D,QAAQ,CAACa,KAAK,CAAC;QAFjBW,KAAK,GAAAuC,eAAA,CAALvC,KAAK;QACLiB,QAAQ,GAAAsB,eAAA,CAARtB,QAAQ;MAEV,IAAMU,KAAK,GAAGxC,UAAU,CAACX,QAAQ,CAACa,KAAK,CAAC,EAAEA,KAAK,CAAC;MAChD,IAAI4B,QAAQ,EAAE;QACZlE,IAAI,CAAC,UAAU,EAAE4E,KAAK,EAAE3B,KAAK,CAAC;MAChC,CAAC,MAAM;QACLzG,eAAe,CAACoD,KAAK,CAACR,YAAY,EAAE;UAClCqG,IAAI,EAAE,CAACb,KAAK,CAAC;UACbc,IAAI,EAAE,SAAAA,KAAA,EAAM;YACVvB,eAAe,CAAC7B,KAAK,CAAC;YACtB2C,sBAAsB,CAAC,CAAC;UAC1B;QACF,CAAC,CAAC;QACFjF,IAAI,CAAC,OAAO,EAAE4E,KAAK,EAAE3B,KAAK,CAAC;QAC3B5F,KAAK,CAACiI,IAAI,CAAC;MACb;MACAtF,IAAI,CAAC,WAAW,EAAE;QAChBjC,IAAI,EAAE6G,KAAK;QACX3B,KAAK,EAALA,KAAK;QACLsC,KAAK,EAALA,KAAK;QACLrB,QAAQ,EAARA;MACF,CAAC,CAAC;IACJ,CAAC;IACD,IAAMyB,cAAc,GAAG,SAAjBA,cAAcA,CAAIC,MAAM,EAAK;MACjC/E,WAAW,GAAG+E,MAAM,CAACC,OAAO;MAC5B7F,IAAI,CAAC,QAAQ,EAAE4F,MAAM,CAAC;IACxB,CAAC;IACD,IAAME,QAAQ,GAAG,SAAXA,QAAQA,CAAIlB,KAAK,EAAK;MAC1BlJ,QAAQ,CAAC,YAAM;QACbiJ,qBAAqB,CAACC,KAAK,CAAC;QAC5BK,sBAAsB,CAAC,IAAI,CAAC;MAC9B,CAAC,CAAC;IACJ,CAAC;IACD,IAAMc,uBAAuB,GAAG,SAA1BA,uBAAuBA,CAAA,EAAS;MACpC,KAAK,IAAIzD,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGb,QAAQ,CAACQ,MAAM,EAAEK,KAAK,EAAE,EAAE;QACpD,IAAA0D,QAAA,GAEIjJ,OAAO,CAAC0E,QAAQ,CAACa,KAAK,CAAC,CAACY,GAAG,CAAC;UAD9B+C,GAAG,GAAAD,QAAA,CAAHC,GAAG;QAEL,IAAIA,GAAG,GAAGtD,YAAY,CAACC,KAAK,EAAE;UAC5B,OAAON,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,KAAK,GAAG,CAAC;QACpC;MACF;MACA,OAAOb,QAAQ,CAACQ,MAAM,GAAG,CAAC;IAC5B,CAAC;IACD,IAAMiE,QAAQ,GAAG,SAAXA,QAAQA,CAAA,EAAS;MACrB,IAAItG,KAAK,CAACd,SAAS,IAAI,CAAC8B,UAAU,EAAE;QAClC,IAAM0B,KAAK,GAAGyD,uBAAuB,CAAC,CAAC;QACvC5B,eAAe,CAAC7B,KAAK,CAAC;MACxB;IACF,CAAC;IACD,IAAM6D,SAAS,GAAG,SAAZA,SAASA,CAAA;MAAA,OAAS1E,QAAQ,CAAC2E,GAAG,CAAC,UAACd,IAAI,EAAEhD,KAAK;QAAA,OAAKnH,YAAY,CAACuC,SAAS,EAAErC,WAAW,CAAC;UACxF,KAAK,EAAEiK,IAAI,CAACpE,EAAE;UACd,IAAI,KAAA0C,MAAA,CAAK1C,EAAE,OAAA0C,MAAA,CAAItB,KAAK,CAAE;UACtB,KAAK,EAAEf,YAAY,CAACe,KAAK,CAAC;UAC1B,MAAM,EAAE1C,KAAK,CAAC1B,IAAI;UAClB,OAAO,EAAE0B,KAAK,CAACzB,KAAK;UACpB,OAAO,EAAEmH,IAAI,CAACe,UAAU;UACxB,OAAO,EAAEf,IAAI,CAACgB,UAAU;UACxB,QAAQ,EAAE1G,KAAK,CAACpB,MAAM;UACtB,UAAU,EAAE8D,KAAK,KAAKX,KAAK,CAACI,YAAY;UACxC,UAAU,EAAEuD,IAAI,CAACpE,EAAE;UACnB,YAAY,EAAEc,UAAU,CAACY,KAAK;UAC9B,aAAa,EAAEhD,KAAK,CAACL,gBAAgB;UACrC,eAAe,EAAEK,KAAK,CAACJ,kBAAkB;UACzC,SAAS,EAAE,SAAA+G,QAAChB,KAAK;YAAA,OAAKF,UAAU,CAACC,IAAI,EAAEhD,KAAK,EAAEiD,KAAK,CAAC;UAAA;QACtD,CAAC,EAAEzJ,IAAI,CAACwJ,IAAI,EAAE,CAAC,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,eAAe,CAAC,CAAC,CAAC,EAAE;UACtErC,KAAK,EAAEqC,IAAI,CAACkB,MAAM,CAACvD;QACrB,CAAC,CAAC;MAAA,EAAC;IAAA;IACH,IAAMwD,UAAU,GAAG,SAAbA,UAAUA,CAAA,EAAS;MACvB,IAAI7G,KAAK,CAAC1B,IAAI,KAAK,MAAM,IAAIuD,QAAQ,CAACQ,MAAM,EAAE;QAC5C,OAAO9G,YAAY,CAAC,KAAK,EAAE;UACzB,OAAO,EAAE6C,GAAG,CAAC,MAAM,CAAC;UACpB,OAAO,EAAE2D,KAAK,CAACG;QACjB,CAAC,EAAE,IAAI,CAAC;MACV;IACF,CAAC;IACD,IAAM4E,YAAY,GAAG,SAAfA,YAAYA,CAAA,EAAS;MACzB,IAAInE,GAAG,EAAEoE,GAAG,EAAEC,EAAE;MAChB,IACE1I,IAAI,GAGF0B,KAAK,CAHP1B,IAAI;QACJG,MAAM,GAEJuB,KAAK,CAFPvB,MAAM;QACNE,MAAM,GACJqB,KAAK,CADPrB,MAAM;MAER,IAAMsI,MAAM,GAAG,CAAC1L,YAAY,CAAC,KAAK,EAAE;QAClC,KAAK,EAAEoD,MAAM,GAAG,KAAK,CAAC,GAAGyC,OAAO;QAChC,OAAO,EAAE,CAAChD,GAAG,CAAC,MAAM,CAAC,EAAA8I,eAAA,KAClBlK,iBAAiB,EAAGsB,IAAI,KAAK,MAAM,IAAIG,MAAM;MAElD,CAAC,EAAE,CAAClD,YAAY,CAAC,KAAK,EAAE;QACtB,KAAK,EAAE4F,MAAM;QACb,MAAM,EAAE,SAAS;QACjB,OAAO,EAAE/C,GAAG,CAAC,KAAK,EAAE,CAACE,IAAI,EAAE;UACzBM,MAAM,EAAEoB,KAAK,CAACpB,MAAM;UACpBuI,QAAQ,EAAE/E,UAAU,CAACY;QACvB,CAAC,CAAC,CAAC;QACH,OAAO,EAAEV,QAAQ,CAACU,KAAK;QACvB,kBAAkB,EAAE;MACtB,CAAC,EAAE,CAAC,CAACL,GAAG,GAAGtC,KAAK,CAAC,UAAU,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGsC,GAAG,CAACyE,IAAI,CAAC/G,KAAK,CAAC,EAAEkG,SAAS,CAAC,CAAC,EAAEM,UAAU,CAAC,CAAC,EAAE,CAACE,GAAG,GAAG1G,KAAK,CAAC,WAAW,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG0G,GAAG,CAACK,IAAI,CAAC/G,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC2G,EAAE,GAAG3G,KAAK,CAAC,YAAY,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG2G,EAAE,CAACI,IAAI,CAAC/G,KAAK,CAAC,CAAC;MAC7N,IAAI1B,MAAM,EAAE;QACV,OAAOpD,YAAY,CAAC,KAAK,EAAE;UACzB,KAAK,EAAE6F;QACT,CAAC,EAAE,CAAC6F,MAAM,CAAC,CAAC;MACd;MACA,OAAOA,MAAM;IACf,CAAC;IACDtL,KAAK,CAAC,CAAC;MAAA,OAAMqE,KAAK,CAACzB,KAAK;IAAA,GAAE9B,WAAW,CAAC,EAAEiH,OAAO,CAAC;IAChD/H,KAAK,CAAC;MAAA,OAAMqE,KAAK,CAACnB,MAAM;IAAA,GAAE,UAACmE,KAAK,EAAK;MACnC,IAAIA,KAAK,KAAKJ,WAAW,CAACI,KAAK,EAAE;QAC/B+B,qBAAqB,CAAC/B,KAAK,CAAC;MAC9B;IACF,CAAC,CAAC;IACFrH,KAAK,CAAC;MAAA,OAAMkG,QAAQ,CAACQ,MAAM;IAAA,GAAE,YAAM;MACjC,IAAIN,KAAK,CAACC,MAAM,EAAE;QAChB+C,qBAAqB,CAAC/E,KAAK,CAACnB,MAAM,CAAC;QACnC6E,OAAO,CAAC,CAAC;QACT5H,QAAQ,CAAC,YAAM;UACbmH,cAAc,CAAC,IAAI,CAAC;QACtB,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;IACF,IAAMoE,IAAI,GAAG,SAAPA,IAAIA,CAAA,EAAS;MACjBtC,qBAAqB,CAAC/E,KAAK,CAACnB,MAAM,EAAE,IAAI,CAAC;MACzC/C,QAAQ,CAAC,YAAM;QACbiG,KAAK,CAACC,MAAM,GAAG,IAAI;QACnB,IAAIZ,OAAO,CAAC4B,KAAK,EAAE;UACjBjC,SAAS,GAAG5D,OAAO,CAACiE,OAAO,CAAC4B,KAAK,CAAC,CAACkB,MAAM;QAC3C;QACAjB,cAAc,CAAC,IAAI,CAAC;MACtB,CAAC,CAAC;IACJ,CAAC;IACD,IAAMqE,UAAU,GAAG,SAAbA,UAAUA,CAAItC,KAAK,EAAE3B,KAAK;MAAA,OAAKjD,IAAI,CAAC,UAAU,EAAE4E,KAAK,EAAE3B,KAAK,CAAC;IAAA;IACnE,IAAMkE,MAAM,GAAG,SAATA,MAAMA,CAAA,EAAS;MACnB7D,OAAO,CAAC,CAAC;MACT5H,QAAQ,CAAC,YAAM;QACb,IAAI6G,GAAG,EAAEoE,GAAG;QACZ,OAAO,CAACA,GAAG,GAAG,CAACpE,GAAG,GAAGtB,UAAU,CAAC2B,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGL,GAAG,CAAC6E,QAAQ,CAACxE,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG+D,GAAG,CAACQ,MAAM,CAAC,CAAC;MAC/G,CAAC,CAAC;IACJ,CAAC;IACD5J,SAAS,CAAC;MACR4J,MAAM,EAANA,MAAM;MACNrB,QAAQ,EAARA;IACF,CAAC,CAAC;IACFnK,WAAW,CAAC2H,OAAO,CAAC;IACpB9F,aAAa,CAAC8F,OAAO,CAAC;IACtBnG,oBAAoB,CAAC8J,IAAI,CAAC;IAC1B/J,gBAAgB,CAAC,QAAQ,EAAEgJ,QAAQ,EAAE;MACnCd,MAAM,EAAEjE,QAAQ;MAChBkG,OAAO,EAAE;IACX,CAAC,CAAC;IACF3F,YAAY,CAAC;MACXR,EAAE,EAAFA,EAAE;MACFtB,KAAK,EAALA,KAAK;MACL0D,OAAO,EAAPA,OAAO;MACP4D,UAAU,EAAVA,UAAU;MACV1E,WAAW,EAAXA,WAAW;MACXK,cAAc,EAAdA;IACF,CAAC,CAAC;IACF,OAAO;MAAA,OAAM1H,YAAY,CAAC,KAAK,EAAE;QAC/B,KAAK,EAAE2F,IAAI;QACX,OAAO,EAAE9C,GAAG,CAAC,CAAC4B,KAAK,CAAC1B,IAAI,CAAC;MAC3B,CAAC,EAAE,CAAC0B,KAAK,CAACrB,MAAM,GAAGpD,YAAY,CAACsC,MAAM,EAAE;QACtC,WAAW,EAAEqD,IAAI,CAAC8B,KAAK;QACvB,WAAW,EAAEF,WAAW,CAACE,KAAK;QAC9B,UAAU,EAAE+C;MACd,CAAC,EAAE;QACD2B,OAAO,EAAE,SAAAC,SAAA;UAAA,OAAM,CAACb,YAAY,CAAC,CAAC,CAAC;QAAA;MACjC,CAAC,CAAC,GAAGA,YAAY,CAAC,CAAC,EAAEvL,YAAY,CAACwC,WAAW,EAAE;QAC7C,KAAK,EAAEsD,UAAU;QACjB,OAAO,EAAEQ,QAAQ,CAACQ,MAAM;QACxB,QAAQ,EAAEN,KAAK,CAACC,MAAM;QACtB,UAAU,EAAEhC,KAAK,CAACjB,QAAQ;QAC1B,UAAU,EAAEiB,KAAK,CAAClB,QAAQ;QAC1B,WAAW,EAAEkB,KAAK,CAACf,SAAS;QAC5B,YAAY,EAAEe,KAAK,CAACX,UAAU;QAC9B,cAAc,EAAE0C,KAAK,CAACI,YAAY;QAClC,UAAU,EAAEoC;MACd,CAAC,EAAE;QACDmD,OAAO,EAAE,SAAAC,SAAA,EAAM;UACb,IAAIhF,GAAG;UACP,OAAO,CAAC,CAACA,GAAG,GAAGtC,KAAK,CAACqH,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG/E,GAAG,CAACyE,IAAI,CAAC/G,KAAK,CAAC,CAAC;QACnE;MACF,CAAC,CAAC,CAAC,CAAC;IAAA;EACN;AACF,CAAC,CAAC;AACF,SACER,QAAQ,EACRE,aAAa,IAAI2H,OAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}