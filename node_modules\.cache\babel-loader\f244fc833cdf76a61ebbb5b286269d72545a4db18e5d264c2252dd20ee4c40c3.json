{"ast": null, "code": "function _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return _typeof(key) === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (_typeof(input) !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (_typeof(res) !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\nfunction _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread(); }\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _iterableToArray(iter) { if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter); }\nfunction _arrayWithoutHoles(arr) { if (Array.isArray(arr)) return _arrayLikeToArray(arr); }\nfunction _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nfunction _iterableToArrayLimit(arr, i) { var _i = null == arr ? null : \"undefined\" != typeof Symbol && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (null != _i) { var _s, _e, _x, _r, _arr = [], _n = !0, _d = !1; try { if (_x = (_i = _i.call(arr)).next, 0 === i) { if (Object(_i) !== _i) return; _n = !1; } else for (; !(_n = (_s = _x.call(_i)).done) && (_arr.push(_s.value), _arr.length !== i); _n = !0); } catch (err) { _d = !0, _e = err; } finally { try { if (!_n && null != _i.return && (_r = _i.return(), Object(_r) !== _r)) return; } finally { if (_d) throw _e; } } return _arr; } }\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\nimport { createTextVNode as _createTextVNode, mergeProps as _mergeProps, createVNode as _createVNode } from \"vue\";\nimport { ref, watch, provide, computed, nextTick, reactive, onMounted, defineComponent } from \"vue\";\nimport { isDef, extend, addUnit, toArray, FORM_KEY, numericProp, unknownProp, resetScroll, formatNumber, preventDefault, makeStringProp, makeNumericProp, createNamespace } from \"../utils/index.mjs\";\nimport { cutString, runSyncRule, endComposing, mapInputType, isEmptyValue, startComposing, getRuleMessage, resizeTextarea, getStringLength, runRuleValidator } from \"./utils.mjs\";\nimport { cellSharedProps } from \"../cell/Cell.mjs\";\nimport { useParent, useEventListener, CUSTOM_FIELD_INJECTION_KEY } from \"@vant/use\";\nimport { useId } from \"../composables/use-id.mjs\";\nimport { useExpose } from \"../composables/use-expose.mjs\";\nimport { Icon } from \"../icon/index.mjs\";\nimport { Cell } from \"../cell/index.mjs\";\nvar _createNamespace = createNamespace(\"field\"),\n  _createNamespace2 = _slicedToArray(_createNamespace, 2),\n  name = _createNamespace2[0],\n  bem = _createNamespace2[1];\nvar fieldSharedProps = {\n  id: String,\n  name: String,\n  leftIcon: String,\n  rightIcon: String,\n  autofocus: Boolean,\n  clearable: Boolean,\n  maxlength: numericProp,\n  formatter: Function,\n  clearIcon: makeStringProp(\"clear\"),\n  modelValue: makeNumericProp(\"\"),\n  inputAlign: String,\n  placeholder: String,\n  autocomplete: String,\n  errorMessage: String,\n  enterkeyhint: String,\n  clearTrigger: makeStringProp(\"focus\"),\n  formatTrigger: makeStringProp(\"onChange\"),\n  error: {\n    type: Boolean,\n    default: null\n  },\n  disabled: {\n    type: Boolean,\n    default: null\n  },\n  readonly: {\n    type: Boolean,\n    default: null\n  }\n};\nvar fieldProps = extend({}, cellSharedProps, fieldSharedProps, {\n  rows: numericProp,\n  type: makeStringProp(\"text\"),\n  rules: Array,\n  autosize: [Boolean, Object],\n  labelWidth: numericProp,\n  labelClass: unknownProp,\n  labelAlign: String,\n  showWordLimit: Boolean,\n  errorMessageAlign: String,\n  colon: {\n    type: Boolean,\n    default: null\n  }\n});\nvar stdin_default = defineComponent({\n  name: name,\n  props: fieldProps,\n  emits: [\"blur\", \"focus\", \"clear\", \"keypress\", \"click-input\", \"end-validate\", \"start-validate\", \"click-left-icon\", \"click-right-icon\", \"update:modelValue\"],\n  setup: function setup(props, _ref) {\n    var emit = _ref.emit,\n      slots = _ref.slots;\n    var id = useId();\n    var state = reactive({\n      status: \"unvalidated\",\n      focused: false,\n      validateMessage: \"\"\n    });\n    var inputRef = ref();\n    var clearIconRef = ref();\n    var customValue = ref();\n    var _useParent = useParent(FORM_KEY),\n      form = _useParent.parent;\n    var getModelValue = function getModelValue() {\n      var _a;\n      return String((_a = props.modelValue) != null ? _a : \"\");\n    };\n    var getProp = function getProp(key) {\n      if (isDef(props[key])) {\n        return props[key];\n      }\n      if (form && isDef(form.props[key])) {\n        return form.props[key];\n      }\n    };\n    var showClear = computed(function () {\n      var readonly = getProp(\"readonly\");\n      if (props.clearable && !readonly) {\n        var hasValue = getModelValue() !== \"\";\n        var trigger = props.clearTrigger === \"always\" || props.clearTrigger === \"focus\" && state.focused;\n        return hasValue && trigger;\n      }\n      return false;\n    });\n    var formValue = computed(function () {\n      if (customValue.value && slots.input) {\n        return customValue.value();\n      }\n      return props.modelValue;\n    });\n    var runRules = function runRules(rules) {\n      return rules.reduce(function (promise, rule) {\n        return promise.then(function () {\n          if (state.status === \"failed\") {\n            return;\n          }\n          var value = formValue.value;\n          if (rule.formatter) {\n            value = rule.formatter(value, rule);\n          }\n          if (!runSyncRule(value, rule)) {\n            state.status = \"failed\";\n            state.validateMessage = getRuleMessage(value, rule);\n            return;\n          }\n          if (rule.validator) {\n            if (isEmptyValue(value) && rule.validateEmpty === false) {\n              return;\n            }\n            return runRuleValidator(value, rule).then(function (result) {\n              if (result && typeof result === \"string\") {\n                state.status = \"failed\";\n                state.validateMessage = result;\n              } else if (result === false) {\n                state.status = \"failed\";\n                state.validateMessage = getRuleMessage(value, rule);\n              }\n            });\n          }\n        });\n      }, Promise.resolve());\n    };\n    var resetValidation = function resetValidation() {\n      state.status = \"unvalidated\";\n      state.validateMessage = \"\";\n    };\n    var endValidate = function endValidate() {\n      return emit(\"end-validate\", {\n        status: state.status\n      });\n    };\n    var validate = function validate() {\n      var rules = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : props.rules;\n      return new Promise(function (resolve) {\n        resetValidation();\n        if (rules) {\n          emit(\"start-validate\");\n          runRules(rules).then(function () {\n            if (state.status === \"failed\") {\n              resolve({\n                name: props.name,\n                message: state.validateMessage\n              });\n              endValidate();\n            } else {\n              state.status = \"passed\";\n              resolve();\n              endValidate();\n            }\n          });\n        } else {\n          resolve();\n        }\n      });\n    };\n    var validateWithTrigger = function validateWithTrigger(trigger) {\n      if (form && props.rules) {\n        var validateTrigger = form.props.validateTrigger;\n        var defaultTrigger = toArray(validateTrigger).includes(trigger);\n        var rules = props.rules.filter(function (rule) {\n          if (rule.trigger) {\n            return toArray(rule.trigger).includes(trigger);\n          }\n          return defaultTrigger;\n        });\n        if (rules.length) {\n          validate(rules);\n        }\n      }\n    };\n    var limitValueLength = function limitValueLength(value) {\n      var _a;\n      var maxlength = props.maxlength;\n      if (isDef(maxlength) && getStringLength(value) > maxlength) {\n        var modelValue = getModelValue();\n        if (modelValue && getStringLength(modelValue) === +maxlength) {\n          return modelValue;\n        }\n        var selectionEnd = (_a = inputRef.value) == null ? void 0 : _a.selectionEnd;\n        if (state.focused && selectionEnd) {\n          var valueArr = _toConsumableArray(value);\n          var exceededLength = valueArr.length - +maxlength;\n          valueArr.splice(selectionEnd - exceededLength, exceededLength);\n          return valueArr.join(\"\");\n        }\n        return cutString(value, +maxlength);\n      }\n      return value;\n    };\n    var updateValue = function updateValue(value) {\n      var trigger = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : \"onChange\";\n      var originalValue = value;\n      value = limitValueLength(value);\n      var limitDiffLen = getStringLength(originalValue) - getStringLength(value);\n      if (props.type === \"number\" || props.type === \"digit\") {\n        var isNumber = props.type === \"number\";\n        value = formatNumber(value, isNumber, isNumber);\n      }\n      var formatterDiffLen = 0;\n      if (props.formatter && trigger === props.formatTrigger) {\n        var formatter = props.formatter,\n          maxlength = props.maxlength;\n        value = formatter(value);\n        if (isDef(maxlength) && getStringLength(value) > maxlength) {\n          value = cutString(value, +maxlength);\n        }\n        if (inputRef.value && state.focused) {\n          var selectionEnd = inputRef.value.selectionEnd;\n          var bcoVal = cutString(originalValue, selectionEnd);\n          formatterDiffLen = getStringLength(formatter(bcoVal)) - getStringLength(bcoVal);\n        }\n      }\n      if (inputRef.value && inputRef.value.value !== value) {\n        if (state.focused) {\n          var _inputRef$value = inputRef.value,\n            selectionStart = _inputRef$value.selectionStart,\n            _selectionEnd = _inputRef$value.selectionEnd;\n          inputRef.value.value = value;\n          if (isDef(selectionStart) && isDef(_selectionEnd)) {\n            var valueLen = getStringLength(value);\n            if (limitDiffLen) {\n              selectionStart -= limitDiffLen;\n              _selectionEnd -= limitDiffLen;\n            } else if (formatterDiffLen) {\n              selectionStart += formatterDiffLen;\n              _selectionEnd += formatterDiffLen;\n            }\n            inputRef.value.setSelectionRange(Math.min(selectionStart, valueLen), Math.min(_selectionEnd, valueLen));\n          }\n        } else {\n          inputRef.value.value = value;\n        }\n      }\n      if (value !== props.modelValue) {\n        emit(\"update:modelValue\", value);\n      }\n    };\n    var onInput = function onInput(event) {\n      if (!event.target.composing) {\n        updateValue(event.target.value);\n      }\n    };\n    var blur = function blur() {\n      var _a;\n      return (_a = inputRef.value) == null ? void 0 : _a.blur();\n    };\n    var focus = function focus() {\n      var _a;\n      return (_a = inputRef.value) == null ? void 0 : _a.focus();\n    };\n    var adjustTextareaSize = function adjustTextareaSize() {\n      var input = inputRef.value;\n      if (props.type === \"textarea\" && props.autosize && input) {\n        resizeTextarea(input, props.autosize);\n      }\n    };\n    var onFocus = function onFocus(event) {\n      state.focused = true;\n      emit(\"focus\", event);\n      nextTick(adjustTextareaSize);\n      if (getProp(\"readonly\")) {\n        blur();\n      }\n    };\n    var onBlur = function onBlur(event) {\n      if (getProp(\"readonly\")) {\n        return;\n      }\n      state.focused = false;\n      updateValue(getModelValue(), \"onBlur\");\n      emit(\"blur\", event);\n      validateWithTrigger(\"onBlur\");\n      nextTick(adjustTextareaSize);\n      resetScroll();\n    };\n    var onClickInput = function onClickInput(event) {\n      return emit(\"click-input\", event);\n    };\n    var onClickLeftIcon = function onClickLeftIcon(event) {\n      return emit(\"click-left-icon\", event);\n    };\n    var onClickRightIcon = function onClickRightIcon(event) {\n      return emit(\"click-right-icon\", event);\n    };\n    var onClear = function onClear(event) {\n      preventDefault(event);\n      emit(\"update:modelValue\", \"\");\n      emit(\"clear\", event);\n    };\n    var showError = computed(function () {\n      if (typeof props.error === \"boolean\") {\n        return props.error;\n      }\n      if (form && form.props.showError && state.status === \"failed\") {\n        return true;\n      }\n    });\n    var labelStyle = computed(function () {\n      var labelWidth = getProp(\"labelWidth\");\n      if (labelWidth) {\n        return {\n          width: addUnit(labelWidth)\n        };\n      }\n    });\n    var onKeypress = function onKeypress(event) {\n      var ENTER_CODE = 13;\n      if (event.keyCode === ENTER_CODE) {\n        var submitOnEnter = form && form.props.submitOnEnter;\n        if (!submitOnEnter && props.type !== \"textarea\") {\n          preventDefault(event);\n        }\n        if (props.type === \"search\") {\n          blur();\n        }\n      }\n      emit(\"keypress\", event);\n    };\n    var getInputId = function getInputId() {\n      return props.id || \"\".concat(id, \"-input\");\n    };\n    var getValidationStatus = function getValidationStatus() {\n      return state.status;\n    };\n    var renderInput = function renderInput() {\n      var controlClass = bem(\"control\", [getProp(\"inputAlign\"), {\n        error: showError.value,\n        custom: !!slots.input,\n        \"min-height\": props.type === \"textarea\" && !props.autosize\n      }]);\n      if (slots.input) {\n        return _createVNode(\"div\", {\n          \"class\": controlClass,\n          \"onClick\": onClickInput\n        }, [slots.input()]);\n      }\n      var inputAttrs = {\n        id: getInputId(),\n        ref: inputRef,\n        name: props.name,\n        rows: props.rows !== void 0 ? +props.rows : void 0,\n        class: controlClass,\n        disabled: getProp(\"disabled\"),\n        readonly: getProp(\"readonly\"),\n        autofocus: props.autofocus,\n        placeholder: props.placeholder,\n        autocomplete: props.autocomplete,\n        enterkeyhint: props.enterkeyhint,\n        \"aria-labelledby\": props.label ? \"\".concat(id, \"-label\") : void 0,\n        onBlur: onBlur,\n        onFocus: onFocus,\n        onInput: onInput,\n        onClick: onClickInput,\n        onChange: endComposing,\n        onKeypress: onKeypress,\n        onCompositionend: endComposing,\n        onCompositionstart: startComposing\n      };\n      if (props.type === \"textarea\") {\n        return _createVNode(\"textarea\", inputAttrs, null);\n      }\n      return _createVNode(\"input\", _mergeProps(mapInputType(props.type), inputAttrs), null);\n    };\n    var renderLeftIcon = function renderLeftIcon() {\n      var leftIconSlot = slots[\"left-icon\"];\n      if (props.leftIcon || leftIconSlot) {\n        return _createVNode(\"div\", {\n          \"class\": bem(\"left-icon\"),\n          \"onClick\": onClickLeftIcon\n        }, [leftIconSlot ? leftIconSlot() : _createVNode(Icon, {\n          \"name\": props.leftIcon,\n          \"classPrefix\": props.iconPrefix\n        }, null)]);\n      }\n    };\n    var renderRightIcon = function renderRightIcon() {\n      var rightIconSlot = slots[\"right-icon\"];\n      if (props.rightIcon || rightIconSlot) {\n        return _createVNode(\"div\", {\n          \"class\": bem(\"right-icon\"),\n          \"onClick\": onClickRightIcon\n        }, [rightIconSlot ? rightIconSlot() : _createVNode(Icon, {\n          \"name\": props.rightIcon,\n          \"classPrefix\": props.iconPrefix\n        }, null)]);\n      }\n    };\n    var renderWordLimit = function renderWordLimit() {\n      if (props.showWordLimit && props.maxlength) {\n        var count = getStringLength(getModelValue());\n        return _createVNode(\"div\", {\n          \"class\": bem(\"word-limit\")\n        }, [_createVNode(\"span\", {\n          \"class\": bem(\"word-num\")\n        }, [count]), _createTextVNode(\"/\"), props.maxlength]);\n      }\n    };\n    var renderMessage = function renderMessage() {\n      if (form && form.props.showErrorMessage === false) {\n        return;\n      }\n      var message = props.errorMessage || state.validateMessage;\n      if (message) {\n        var slot = slots[\"error-message\"];\n        var errorMessageAlign = getProp(\"errorMessageAlign\");\n        return _createVNode(\"div\", {\n          \"class\": bem(\"error-message\", errorMessageAlign)\n        }, [slot ? slot({\n          message: message\n        }) : message]);\n      }\n    };\n    var renderLabel = function renderLabel() {\n      var colon = getProp(\"colon\") ? \":\" : \"\";\n      if (slots.label) {\n        return [slots.label(), colon];\n      }\n      if (props.label) {\n        return _createVNode(\"label\", {\n          \"id\": \"\".concat(id, \"-label\"),\n          \"for\": getInputId()\n        }, [props.label + colon]);\n      }\n    };\n    var renderFieldBody = function renderFieldBody() {\n      return [_createVNode(\"div\", {\n        \"class\": bem(\"body\")\n      }, [renderInput(), showClear.value && _createVNode(Icon, {\n        \"ref\": clearIconRef,\n        \"name\": props.clearIcon,\n        \"class\": bem(\"clear\")\n      }, null), renderRightIcon(), slots.button && _createVNode(\"div\", {\n        \"class\": bem(\"button\")\n      }, [slots.button()])]), renderWordLimit(), renderMessage()];\n    };\n    useExpose({\n      blur: blur,\n      focus: focus,\n      validate: validate,\n      formValue: formValue,\n      resetValidation: resetValidation,\n      getValidationStatus: getValidationStatus\n    });\n    provide(CUSTOM_FIELD_INJECTION_KEY, {\n      customValue: customValue,\n      resetValidation: resetValidation,\n      validateWithTrigger: validateWithTrigger\n    });\n    watch(function () {\n      return props.modelValue;\n    }, function () {\n      updateValue(getModelValue());\n      resetValidation();\n      validateWithTrigger(\"onChange\");\n      nextTick(adjustTextareaSize);\n    });\n    onMounted(function () {\n      updateValue(getModelValue(), props.formatTrigger);\n      nextTick(adjustTextareaSize);\n    });\n    useEventListener(\"touchstart\", onClear, {\n      target: computed(function () {\n        var _a;\n        return (_a = clearIconRef.value) == null ? void 0 : _a.$el;\n      })\n    });\n    return function () {\n      var disabled = getProp(\"disabled\");\n      var labelAlign = getProp(\"labelAlign\");\n      var Label = renderLabel();\n      var LeftIcon = renderLeftIcon();\n      return _createVNode(Cell, {\n        \"size\": props.size,\n        \"icon\": props.leftIcon,\n        \"class\": bem(_defineProperty({\n          error: showError.value,\n          disabled: disabled\n        }, \"label-\".concat(labelAlign), labelAlign)),\n        \"center\": props.center,\n        \"border\": props.border,\n        \"isLink\": props.isLink,\n        \"clickable\": props.clickable,\n        \"titleStyle\": labelStyle.value,\n        \"valueClass\": bem(\"value\"),\n        \"titleClass\": [bem(\"label\", [labelAlign, {\n          required: props.required\n        }]), props.labelClass],\n        \"arrowDirection\": props.arrowDirection\n      }, {\n        icon: LeftIcon ? function () {\n          return LeftIcon;\n        } : null,\n        title: Label ? function () {\n          return Label;\n        } : null,\n        value: renderFieldBody,\n        extra: slots.extra\n      });\n    };\n  }\n});\nexport { stdin_default as default, fieldSharedProps };", "map": {"version": 3, "names": ["createTextVNode", "_createTextVNode", "mergeProps", "_mergeProps", "createVNode", "_createVNode", "ref", "watch", "provide", "computed", "nextTick", "reactive", "onMounted", "defineComponent", "isDef", "extend", "addUnit", "toArray", "FORM_KEY", "numericProp", "unknownProp", "resetScroll", "formatNumber", "preventDefault", "makeStringProp", "makeNumericProp", "createNamespace", "cutString", "runSyncRule", "endComposing", "mapInputType", "isEmptyValue", "startComposing", "getRuleMessage", "resizeTextarea", "getStringLength", "runRuleValidator", "cellSharedProps", "useParent", "useEventListener", "CUSTOM_FIELD_INJECTION_KEY", "useId", "useExpose", "Icon", "Cell", "_createNamespace", "_createNamespace2", "_slicedToArray", "name", "bem", "fieldSharedProps", "id", "String", "leftIcon", "rightIcon", "autofocus", "Boolean", "clearable", "maxlength", "formatter", "Function", "clearIcon", "modelValue", "inputAlign", "placeholder", "autocomplete", "errorMessage", "enterkeyhint", "clearTrigger", "formatTrigger", "error", "type", "default", "disabled", "readonly", "fieldProps", "rows", "rules", "Array", "autosize", "Object", "labelWidth", "labelClass", "labelAlign", "showWordLimit", "errorMessageAlign", "colon", "stdin_default", "props", "emits", "setup", "_ref", "emit", "slots", "state", "status", "focused", "validateMessage", "inputRef", "clearIconRef", "customValue", "_useParent", "form", "parent", "getModelValue", "_a", "getProp", "key", "showClear", "hasValue", "trigger", "formValue", "value", "input", "runRules", "reduce", "promise", "rule", "then", "validator", "validateEmpty", "result", "Promise", "resolve", "resetValidation", "endValidate", "validate", "arguments", "length", "undefined", "message", "validateWithTrigger", "validate<PERSON><PERSON>ger", "defaultTrigger", "includes", "filter", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "selectionEnd", "valueArr", "_toConsumableArray", "<PERSON><PERSON><PERSON><PERSON>", "splice", "join", "updateValue", "originalValue", "limitDiffLen", "isNumber", "formatterDiffLen", "bcoVal", "_inputRef$value", "selectionStart", "valueLen", "setSelectionRange", "Math", "min", "onInput", "event", "target", "composing", "blur", "focus", "adjustTextareaSize", "onFocus", "onBlur", "onClickInput", "onClickLeftIcon", "onClickRightIcon", "onClear", "showError", "labelStyle", "width", "onKeypress", "ENTER_CODE", "keyCode", "submitOnEnter", "getInputId", "concat", "getValidationStatus", "renderInput", "controlClass", "custom", "inputAttrs", "class", "label", "onClick", "onChange", "onCompositionend", "onCompositionstart", "renderLeftIcon", "leftIconSlot", "iconPrefix", "renderRightIcon", "rightIconSlot", "renderWordLimit", "count", "renderMessage", "showErrorMessage", "slot", "renderLabel", "renderFieldBody", "button", "$el", "Label", "LeftIcon", "size", "_defineProperty", "center", "border", "isLink", "clickable", "required", "arrowDirection", "icon", "title", "extra"], "sources": ["C:/Users/<USER>/Desktop/vue3_3.0/node_modules/vant/es/field/Field.mjs"], "sourcesContent": ["import { createTextVNode as _createTextVNode, mergeProps as _mergeProps, createVNode as _createVNode } from \"vue\";\nimport { ref, watch, provide, computed, nextTick, reactive, onMounted, defineComponent } from \"vue\";\nimport { isDef, extend, addUnit, toArray, FORM_KEY, numericProp, unknownProp, resetScroll, formatNumber, preventDefault, makeStringProp, makeNumericProp, createNamespace } from \"../utils/index.mjs\";\nimport { cutString, runSyncRule, endComposing, mapInputType, isEmptyValue, startComposing, getRuleMessage, resizeTextarea, getStringLength, runRuleValidator } from \"./utils.mjs\";\nimport { cellSharedProps } from \"../cell/Cell.mjs\";\nimport { useParent, useEventListener, CUSTOM_FIELD_INJECTION_KEY } from \"@vant/use\";\nimport { useId } from \"../composables/use-id.mjs\";\nimport { useExpose } from \"../composables/use-expose.mjs\";\nimport { Icon } from \"../icon/index.mjs\";\nimport { Cell } from \"../cell/index.mjs\";\nconst [name, bem] = createNamespace(\"field\");\nconst fieldSharedProps = {\n  id: String,\n  name: String,\n  leftIcon: String,\n  rightIcon: String,\n  autofocus: Boolean,\n  clearable: Boolean,\n  maxlength: numericProp,\n  formatter: Function,\n  clearIcon: makeStringProp(\"clear\"),\n  modelValue: makeNumericProp(\"\"),\n  inputAlign: String,\n  placeholder: String,\n  autocomplete: String,\n  errorMessage: String,\n  enterkeyhint: String,\n  clearTrigger: makeStringProp(\"focus\"),\n  formatTrigger: makeStringProp(\"onChange\"),\n  error: {\n    type: Boolean,\n    default: null\n  },\n  disabled: {\n    type: Boolean,\n    default: null\n  },\n  readonly: {\n    type: Boolean,\n    default: null\n  }\n};\nconst fieldProps = extend({}, cellSharedProps, fieldSharedProps, {\n  rows: numericProp,\n  type: makeStringProp(\"text\"),\n  rules: Array,\n  autosize: [Boolean, Object],\n  labelWidth: numericProp,\n  labelClass: unknownProp,\n  labelAlign: String,\n  showWordLimit: Boolean,\n  errorMessageAlign: String,\n  colon: {\n    type: Boolean,\n    default: null\n  }\n});\nvar stdin_default = defineComponent({\n  name,\n  props: fieldProps,\n  emits: [\"blur\", \"focus\", \"clear\", \"keypress\", \"click-input\", \"end-validate\", \"start-validate\", \"click-left-icon\", \"click-right-icon\", \"update:modelValue\"],\n  setup(props, {\n    emit,\n    slots\n  }) {\n    const id = useId();\n    const state = reactive({\n      status: \"unvalidated\",\n      focused: false,\n      validateMessage: \"\"\n    });\n    const inputRef = ref();\n    const clearIconRef = ref();\n    const customValue = ref();\n    const {\n      parent: form\n    } = useParent(FORM_KEY);\n    const getModelValue = () => {\n      var _a;\n      return String((_a = props.modelValue) != null ? _a : \"\");\n    };\n    const getProp = (key) => {\n      if (isDef(props[key])) {\n        return props[key];\n      }\n      if (form && isDef(form.props[key])) {\n        return form.props[key];\n      }\n    };\n    const showClear = computed(() => {\n      const readonly = getProp(\"readonly\");\n      if (props.clearable && !readonly) {\n        const hasValue = getModelValue() !== \"\";\n        const trigger = props.clearTrigger === \"always\" || props.clearTrigger === \"focus\" && state.focused;\n        return hasValue && trigger;\n      }\n      return false;\n    });\n    const formValue = computed(() => {\n      if (customValue.value && slots.input) {\n        return customValue.value();\n      }\n      return props.modelValue;\n    });\n    const runRules = (rules) => rules.reduce((promise, rule) => promise.then(() => {\n      if (state.status === \"failed\") {\n        return;\n      }\n      let {\n        value\n      } = formValue;\n      if (rule.formatter) {\n        value = rule.formatter(value, rule);\n      }\n      if (!runSyncRule(value, rule)) {\n        state.status = \"failed\";\n        state.validateMessage = getRuleMessage(value, rule);\n        return;\n      }\n      if (rule.validator) {\n        if (isEmptyValue(value) && rule.validateEmpty === false) {\n          return;\n        }\n        return runRuleValidator(value, rule).then((result) => {\n          if (result && typeof result === \"string\") {\n            state.status = \"failed\";\n            state.validateMessage = result;\n          } else if (result === false) {\n            state.status = \"failed\";\n            state.validateMessage = getRuleMessage(value, rule);\n          }\n        });\n      }\n    }), Promise.resolve());\n    const resetValidation = () => {\n      state.status = \"unvalidated\";\n      state.validateMessage = \"\";\n    };\n    const endValidate = () => emit(\"end-validate\", {\n      status: state.status\n    });\n    const validate = (rules = props.rules) => new Promise((resolve) => {\n      resetValidation();\n      if (rules) {\n        emit(\"start-validate\");\n        runRules(rules).then(() => {\n          if (state.status === \"failed\") {\n            resolve({\n              name: props.name,\n              message: state.validateMessage\n            });\n            endValidate();\n          } else {\n            state.status = \"passed\";\n            resolve();\n            endValidate();\n          }\n        });\n      } else {\n        resolve();\n      }\n    });\n    const validateWithTrigger = (trigger) => {\n      if (form && props.rules) {\n        const {\n          validateTrigger\n        } = form.props;\n        const defaultTrigger = toArray(validateTrigger).includes(trigger);\n        const rules = props.rules.filter((rule) => {\n          if (rule.trigger) {\n            return toArray(rule.trigger).includes(trigger);\n          }\n          return defaultTrigger;\n        });\n        if (rules.length) {\n          validate(rules);\n        }\n      }\n    };\n    const limitValueLength = (value) => {\n      var _a;\n      const {\n        maxlength\n      } = props;\n      if (isDef(maxlength) && getStringLength(value) > maxlength) {\n        const modelValue = getModelValue();\n        if (modelValue && getStringLength(modelValue) === +maxlength) {\n          return modelValue;\n        }\n        const selectionEnd = (_a = inputRef.value) == null ? void 0 : _a.selectionEnd;\n        if (state.focused && selectionEnd) {\n          const valueArr = [...value];\n          const exceededLength = valueArr.length - +maxlength;\n          valueArr.splice(selectionEnd - exceededLength, exceededLength);\n          return valueArr.join(\"\");\n        }\n        return cutString(value, +maxlength);\n      }\n      return value;\n    };\n    const updateValue = (value, trigger = \"onChange\") => {\n      const originalValue = value;\n      value = limitValueLength(value);\n      const limitDiffLen = getStringLength(originalValue) - getStringLength(value);\n      if (props.type === \"number\" || props.type === \"digit\") {\n        const isNumber = props.type === \"number\";\n        value = formatNumber(value, isNumber, isNumber);\n      }\n      let formatterDiffLen = 0;\n      if (props.formatter && trigger === props.formatTrigger) {\n        const {\n          formatter,\n          maxlength\n        } = props;\n        value = formatter(value);\n        if (isDef(maxlength) && getStringLength(value) > maxlength) {\n          value = cutString(value, +maxlength);\n        }\n        if (inputRef.value && state.focused) {\n          const {\n            selectionEnd\n          } = inputRef.value;\n          const bcoVal = cutString(originalValue, selectionEnd);\n          formatterDiffLen = getStringLength(formatter(bcoVal)) - getStringLength(bcoVal);\n        }\n      }\n      if (inputRef.value && inputRef.value.value !== value) {\n        if (state.focused) {\n          let {\n            selectionStart,\n            selectionEnd\n          } = inputRef.value;\n          inputRef.value.value = value;\n          if (isDef(selectionStart) && isDef(selectionEnd)) {\n            const valueLen = getStringLength(value);\n            if (limitDiffLen) {\n              selectionStart -= limitDiffLen;\n              selectionEnd -= limitDiffLen;\n            } else if (formatterDiffLen) {\n              selectionStart += formatterDiffLen;\n              selectionEnd += formatterDiffLen;\n            }\n            inputRef.value.setSelectionRange(Math.min(selectionStart, valueLen), Math.min(selectionEnd, valueLen));\n          }\n        } else {\n          inputRef.value.value = value;\n        }\n      }\n      if (value !== props.modelValue) {\n        emit(\"update:modelValue\", value);\n      }\n    };\n    const onInput = (event) => {\n      if (!event.target.composing) {\n        updateValue(event.target.value);\n      }\n    };\n    const blur = () => {\n      var _a;\n      return (_a = inputRef.value) == null ? void 0 : _a.blur();\n    };\n    const focus = () => {\n      var _a;\n      return (_a = inputRef.value) == null ? void 0 : _a.focus();\n    };\n    const adjustTextareaSize = () => {\n      const input = inputRef.value;\n      if (props.type === \"textarea\" && props.autosize && input) {\n        resizeTextarea(input, props.autosize);\n      }\n    };\n    const onFocus = (event) => {\n      state.focused = true;\n      emit(\"focus\", event);\n      nextTick(adjustTextareaSize);\n      if (getProp(\"readonly\")) {\n        blur();\n      }\n    };\n    const onBlur = (event) => {\n      if (getProp(\"readonly\")) {\n        return;\n      }\n      state.focused = false;\n      updateValue(getModelValue(), \"onBlur\");\n      emit(\"blur\", event);\n      validateWithTrigger(\"onBlur\");\n      nextTick(adjustTextareaSize);\n      resetScroll();\n    };\n    const onClickInput = (event) => emit(\"click-input\", event);\n    const onClickLeftIcon = (event) => emit(\"click-left-icon\", event);\n    const onClickRightIcon = (event) => emit(\"click-right-icon\", event);\n    const onClear = (event) => {\n      preventDefault(event);\n      emit(\"update:modelValue\", \"\");\n      emit(\"clear\", event);\n    };\n    const showError = computed(() => {\n      if (typeof props.error === \"boolean\") {\n        return props.error;\n      }\n      if (form && form.props.showError && state.status === \"failed\") {\n        return true;\n      }\n    });\n    const labelStyle = computed(() => {\n      const labelWidth = getProp(\"labelWidth\");\n      if (labelWidth) {\n        return {\n          width: addUnit(labelWidth)\n        };\n      }\n    });\n    const onKeypress = (event) => {\n      const ENTER_CODE = 13;\n      if (event.keyCode === ENTER_CODE) {\n        const submitOnEnter = form && form.props.submitOnEnter;\n        if (!submitOnEnter && props.type !== \"textarea\") {\n          preventDefault(event);\n        }\n        if (props.type === \"search\") {\n          blur();\n        }\n      }\n      emit(\"keypress\", event);\n    };\n    const getInputId = () => props.id || `${id}-input`;\n    const getValidationStatus = () => state.status;\n    const renderInput = () => {\n      const controlClass = bem(\"control\", [getProp(\"inputAlign\"), {\n        error: showError.value,\n        custom: !!slots.input,\n        \"min-height\": props.type === \"textarea\" && !props.autosize\n      }]);\n      if (slots.input) {\n        return _createVNode(\"div\", {\n          \"class\": controlClass,\n          \"onClick\": onClickInput\n        }, [slots.input()]);\n      }\n      const inputAttrs = {\n        id: getInputId(),\n        ref: inputRef,\n        name: props.name,\n        rows: props.rows !== void 0 ? +props.rows : void 0,\n        class: controlClass,\n        disabled: getProp(\"disabled\"),\n        readonly: getProp(\"readonly\"),\n        autofocus: props.autofocus,\n        placeholder: props.placeholder,\n        autocomplete: props.autocomplete,\n        enterkeyhint: props.enterkeyhint,\n        \"aria-labelledby\": props.label ? `${id}-label` : void 0,\n        onBlur,\n        onFocus,\n        onInput,\n        onClick: onClickInput,\n        onChange: endComposing,\n        onKeypress,\n        onCompositionend: endComposing,\n        onCompositionstart: startComposing\n      };\n      if (props.type === \"textarea\") {\n        return _createVNode(\"textarea\", inputAttrs, null);\n      }\n      return _createVNode(\"input\", _mergeProps(mapInputType(props.type), inputAttrs), null);\n    };\n    const renderLeftIcon = () => {\n      const leftIconSlot = slots[\"left-icon\"];\n      if (props.leftIcon || leftIconSlot) {\n        return _createVNode(\"div\", {\n          \"class\": bem(\"left-icon\"),\n          \"onClick\": onClickLeftIcon\n        }, [leftIconSlot ? leftIconSlot() : _createVNode(Icon, {\n          \"name\": props.leftIcon,\n          \"classPrefix\": props.iconPrefix\n        }, null)]);\n      }\n    };\n    const renderRightIcon = () => {\n      const rightIconSlot = slots[\"right-icon\"];\n      if (props.rightIcon || rightIconSlot) {\n        return _createVNode(\"div\", {\n          \"class\": bem(\"right-icon\"),\n          \"onClick\": onClickRightIcon\n        }, [rightIconSlot ? rightIconSlot() : _createVNode(Icon, {\n          \"name\": props.rightIcon,\n          \"classPrefix\": props.iconPrefix\n        }, null)]);\n      }\n    };\n    const renderWordLimit = () => {\n      if (props.showWordLimit && props.maxlength) {\n        const count = getStringLength(getModelValue());\n        return _createVNode(\"div\", {\n          \"class\": bem(\"word-limit\")\n        }, [_createVNode(\"span\", {\n          \"class\": bem(\"word-num\")\n        }, [count]), _createTextVNode(\"/\"), props.maxlength]);\n      }\n    };\n    const renderMessage = () => {\n      if (form && form.props.showErrorMessage === false) {\n        return;\n      }\n      const message = props.errorMessage || state.validateMessage;\n      if (message) {\n        const slot = slots[\"error-message\"];\n        const errorMessageAlign = getProp(\"errorMessageAlign\");\n        return _createVNode(\"div\", {\n          \"class\": bem(\"error-message\", errorMessageAlign)\n        }, [slot ? slot({\n          message\n        }) : message]);\n      }\n    };\n    const renderLabel = () => {\n      const colon = getProp(\"colon\") ? \":\" : \"\";\n      if (slots.label) {\n        return [slots.label(), colon];\n      }\n      if (props.label) {\n        return _createVNode(\"label\", {\n          \"id\": `${id}-label`,\n          \"for\": getInputId()\n        }, [props.label + colon]);\n      }\n    };\n    const renderFieldBody = () => [_createVNode(\"div\", {\n      \"class\": bem(\"body\")\n    }, [renderInput(), showClear.value && _createVNode(Icon, {\n      \"ref\": clearIconRef,\n      \"name\": props.clearIcon,\n      \"class\": bem(\"clear\")\n    }, null), renderRightIcon(), slots.button && _createVNode(\"div\", {\n      \"class\": bem(\"button\")\n    }, [slots.button()])]), renderWordLimit(), renderMessage()];\n    useExpose({\n      blur,\n      focus,\n      validate,\n      formValue,\n      resetValidation,\n      getValidationStatus\n    });\n    provide(CUSTOM_FIELD_INJECTION_KEY, {\n      customValue,\n      resetValidation,\n      validateWithTrigger\n    });\n    watch(() => props.modelValue, () => {\n      updateValue(getModelValue());\n      resetValidation();\n      validateWithTrigger(\"onChange\");\n      nextTick(adjustTextareaSize);\n    });\n    onMounted(() => {\n      updateValue(getModelValue(), props.formatTrigger);\n      nextTick(adjustTextareaSize);\n    });\n    useEventListener(\"touchstart\", onClear, {\n      target: computed(() => {\n        var _a;\n        return (_a = clearIconRef.value) == null ? void 0 : _a.$el;\n      })\n    });\n    return () => {\n      const disabled = getProp(\"disabled\");\n      const labelAlign = getProp(\"labelAlign\");\n      const Label = renderLabel();\n      const LeftIcon = renderLeftIcon();\n      return _createVNode(Cell, {\n        \"size\": props.size,\n        \"icon\": props.leftIcon,\n        \"class\": bem({\n          error: showError.value,\n          disabled,\n          [`label-${labelAlign}`]: labelAlign\n        }),\n        \"center\": props.center,\n        \"border\": props.border,\n        \"isLink\": props.isLink,\n        \"clickable\": props.clickable,\n        \"titleStyle\": labelStyle.value,\n        \"valueClass\": bem(\"value\"),\n        \"titleClass\": [bem(\"label\", [labelAlign, {\n          required: props.required\n        }]), props.labelClass],\n        \"arrowDirection\": props.arrowDirection\n      }, {\n        icon: LeftIcon ? () => LeftIcon : null,\n        title: Label ? () => Label : null,\n        value: renderFieldBody,\n        extra: slots.extra\n      });\n    };\n  }\n});\nexport {\n  stdin_default as default,\n  fieldSharedProps\n};\n"], "mappings": ";;;;;;;;;;;;;;AAAA,SAASA,eAAe,IAAIC,gBAAgB,EAAEC,UAAU,IAAIC,WAAW,EAAEC,WAAW,IAAIC,YAAY,QAAQ,KAAK;AACjH,SAASC,GAAG,EAAEC,KAAK,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,eAAe,QAAQ,KAAK;AACnG,SAASC,KAAK,EAAEC,MAAM,EAAEC,OAAO,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,WAAW,EAAEC,WAAW,EAAEC,WAAW,EAAEC,YAAY,EAAEC,cAAc,EAAEC,cAAc,EAAEC,eAAe,EAAEC,eAAe,QAAQ,oBAAoB;AACrM,SAASC,SAAS,EAAEC,WAAW,EAAEC,YAAY,EAAEC,YAAY,EAAEC,YAAY,EAAEC,cAAc,EAAEC,cAAc,EAAEC,cAAc,EAAEC,eAAe,EAAEC,gBAAgB,QAAQ,aAAa;AACjL,SAASC,eAAe,QAAQ,kBAAkB;AAClD,SAASC,SAAS,EAAEC,gBAAgB,EAAEC,0BAA0B,QAAQ,WAAW;AACnF,SAASC,KAAK,QAAQ,2BAA2B;AACjD,SAASC,SAAS,QAAQ,+BAA+B;AACzD,SAASC,IAAI,QAAQ,mBAAmB;AACxC,SAASC,IAAI,QAAQ,mBAAmB;AACxC,IAAAC,gBAAA,GAAoBnB,eAAe,CAAC,OAAO,CAAC;EAAAoB,iBAAA,GAAAC,cAAA,CAAAF,gBAAA;EAArCG,IAAI,GAAAF,iBAAA;EAAEG,GAAG,GAAAH,iBAAA;AAChB,IAAMI,gBAAgB,GAAG;EACvBC,EAAE,EAAEC,MAAM;EACVJ,IAAI,EAAEI,MAAM;EACZC,QAAQ,EAAED,MAAM;EAChBE,SAAS,EAAEF,MAAM;EACjBG,SAAS,EAAEC,OAAO;EAClBC,SAAS,EAAED,OAAO;EAClBE,SAAS,EAAEvC,WAAW;EACtBwC,SAAS,EAAEC,QAAQ;EACnBC,SAAS,EAAErC,cAAc,CAAC,OAAO,CAAC;EAClCsC,UAAU,EAAErC,eAAe,CAAC,EAAE,CAAC;EAC/BsC,UAAU,EAAEX,MAAM;EAClBY,WAAW,EAAEZ,MAAM;EACnBa,YAAY,EAAEb,MAAM;EACpBc,YAAY,EAAEd,MAAM;EACpBe,YAAY,EAAEf,MAAM;EACpBgB,YAAY,EAAE5C,cAAc,CAAC,OAAO,CAAC;EACrC6C,aAAa,EAAE7C,cAAc,CAAC,UAAU,CAAC;EACzC8C,KAAK,EAAE;IACLC,IAAI,EAAEf,OAAO;IACbgB,OAAO,EAAE;EACX,CAAC;EACDC,QAAQ,EAAE;IACRF,IAAI,EAAEf,OAAO;IACbgB,OAAO,EAAE;EACX,CAAC;EACDE,QAAQ,EAAE;IACRH,IAAI,EAAEf,OAAO;IACbgB,OAAO,EAAE;EACX;AACF,CAAC;AACD,IAAMG,UAAU,GAAG5D,MAAM,CAAC,CAAC,CAAC,EAAEsB,eAAe,EAAEa,gBAAgB,EAAE;EAC/D0B,IAAI,EAAEzD,WAAW;EACjBoD,IAAI,EAAE/C,cAAc,CAAC,MAAM,CAAC;EAC5BqD,KAAK,EAAEC,KAAK;EACZC,QAAQ,EAAE,CAACvB,OAAO,EAAEwB,MAAM,CAAC;EAC3BC,UAAU,EAAE9D,WAAW;EACvB+D,UAAU,EAAE9D,WAAW;EACvB+D,UAAU,EAAE/B,MAAM;EAClBgC,aAAa,EAAE5B,OAAO;EACtB6B,iBAAiB,EAAEjC,MAAM;EACzBkC,KAAK,EAAE;IACLf,IAAI,EAAEf,OAAO;IACbgB,OAAO,EAAE;EACX;AACF,CAAC,CAAC;AACF,IAAIe,aAAa,GAAG1E,eAAe,CAAC;EAClCmC,IAAI,EAAJA,IAAI;EACJwC,KAAK,EAAEb,UAAU;EACjBc,KAAK,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,aAAa,EAAE,cAAc,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,kBAAkB,EAAE,mBAAmB,CAAC;EAC1JC,KAAK,WAAAA,MAACF,KAAK,EAAAG,IAAA,EAGR;IAAA,IAFDC,IAAI,GAAAD,IAAA,CAAJC,IAAI;MACJC,KAAK,GAAAF,IAAA,CAALE,KAAK;IAEL,IAAM1C,EAAE,GAAGV,KAAK,CAAC,CAAC;IAClB,IAAMqD,KAAK,GAAGnF,QAAQ,CAAC;MACrBoF,MAAM,EAAE,aAAa;MACrBC,OAAO,EAAE,KAAK;MACdC,eAAe,EAAE;IACnB,CAAC,CAAC;IACF,IAAMC,QAAQ,GAAG5F,GAAG,CAAC,CAAC;IACtB,IAAM6F,YAAY,GAAG7F,GAAG,CAAC,CAAC;IAC1B,IAAM8F,WAAW,GAAG9F,GAAG,CAAC,CAAC;IACzB,IAAA+F,UAAA,GAEI/D,SAAS,CAACpB,QAAQ,CAAC;MADboF,IAAI,GAAAD,UAAA,CAAZE,MAAM;IAER,IAAMC,aAAa,GAAG,SAAhBA,aAAaA,CAAA,EAAS;MAC1B,IAAIC,EAAE;MACN,OAAOrD,MAAM,CAAC,CAACqD,EAAE,GAAGjB,KAAK,CAAC1B,UAAU,KAAK,IAAI,GAAG2C,EAAE,GAAG,EAAE,CAAC;IAC1D,CAAC;IACD,IAAMC,OAAO,GAAG,SAAVA,OAAOA,CAAIC,GAAG,EAAK;MACvB,IAAI7F,KAAK,CAAC0E,KAAK,CAACmB,GAAG,CAAC,CAAC,EAAE;QACrB,OAAOnB,KAAK,CAACmB,GAAG,CAAC;MACnB;MACA,IAAIL,IAAI,IAAIxF,KAAK,CAACwF,IAAI,CAACd,KAAK,CAACmB,GAAG,CAAC,CAAC,EAAE;QAClC,OAAOL,IAAI,CAACd,KAAK,CAACmB,GAAG,CAAC;MACxB;IACF,CAAC;IACD,IAAMC,SAAS,GAAGnG,QAAQ,CAAC,YAAM;MAC/B,IAAMiE,QAAQ,GAAGgC,OAAO,CAAC,UAAU,CAAC;MACpC,IAAIlB,KAAK,CAAC/B,SAAS,IAAI,CAACiB,QAAQ,EAAE;QAChC,IAAMmC,QAAQ,GAAGL,aAAa,CAAC,CAAC,KAAK,EAAE;QACvC,IAAMM,OAAO,GAAGtB,KAAK,CAACpB,YAAY,KAAK,QAAQ,IAAIoB,KAAK,CAACpB,YAAY,KAAK,OAAO,IAAI0B,KAAK,CAACE,OAAO;QAClG,OAAOa,QAAQ,IAAIC,OAAO;MAC5B;MACA,OAAO,KAAK;IACd,CAAC,CAAC;IACF,IAAMC,SAAS,GAAGtG,QAAQ,CAAC,YAAM;MAC/B,IAAI2F,WAAW,CAACY,KAAK,IAAInB,KAAK,CAACoB,KAAK,EAAE;QACpC,OAAOb,WAAW,CAACY,KAAK,CAAC,CAAC;MAC5B;MACA,OAAOxB,KAAK,CAAC1B,UAAU;IACzB,CAAC,CAAC;IACF,IAAMoD,QAAQ,GAAG,SAAXA,QAAQA,CAAIrC,KAAK;MAAA,OAAKA,KAAK,CAACsC,MAAM,CAAC,UAACC,OAAO,EAAEC,IAAI;QAAA,OAAKD,OAAO,CAACE,IAAI,CAAC,YAAM;UAC7E,IAAIxB,KAAK,CAACC,MAAM,KAAK,QAAQ,EAAE;YAC7B;UACF;UACA,IACEiB,KAAK,GACHD,SAAS,CADXC,KAAK;UAEP,IAAIK,IAAI,CAAC1D,SAAS,EAAE;YAClBqD,KAAK,GAAGK,IAAI,CAAC1D,SAAS,CAACqD,KAAK,EAAEK,IAAI,CAAC;UACrC;UACA,IAAI,CAACzF,WAAW,CAACoF,KAAK,EAAEK,IAAI,CAAC,EAAE;YAC7BvB,KAAK,CAACC,MAAM,GAAG,QAAQ;YACvBD,KAAK,CAACG,eAAe,GAAGhE,cAAc,CAAC+E,KAAK,EAAEK,IAAI,CAAC;YACnD;UACF;UACA,IAAIA,IAAI,CAACE,SAAS,EAAE;YAClB,IAAIxF,YAAY,CAACiF,KAAK,CAAC,IAAIK,IAAI,CAACG,aAAa,KAAK,KAAK,EAAE;cACvD;YACF;YACA,OAAOpF,gBAAgB,CAAC4E,KAAK,EAAEK,IAAI,CAAC,CAACC,IAAI,CAAC,UAACG,MAAM,EAAK;cACpD,IAAIA,MAAM,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;gBACxC3B,KAAK,CAACC,MAAM,GAAG,QAAQ;gBACvBD,KAAK,CAACG,eAAe,GAAGwB,MAAM;cAChC,CAAC,MAAM,IAAIA,MAAM,KAAK,KAAK,EAAE;gBAC3B3B,KAAK,CAACC,MAAM,GAAG,QAAQ;gBACvBD,KAAK,CAACG,eAAe,GAAGhE,cAAc,CAAC+E,KAAK,EAAEK,IAAI,CAAC;cACrD;YACF,CAAC,CAAC;UACJ;QACF,CAAC,CAAC;MAAA,GAAEK,OAAO,CAACC,OAAO,CAAC,CAAC,CAAC;IAAA;IACtB,IAAMC,eAAe,GAAG,SAAlBA,eAAeA,CAAA,EAAS;MAC5B9B,KAAK,CAACC,MAAM,GAAG,aAAa;MAC5BD,KAAK,CAACG,eAAe,GAAG,EAAE;IAC5B,CAAC;IACD,IAAM4B,WAAW,GAAG,SAAdA,WAAWA,CAAA;MAAA,OAASjC,IAAI,CAAC,cAAc,EAAE;QAC7CG,MAAM,EAAED,KAAK,CAACC;MAChB,CAAC,CAAC;IAAA;IACF,IAAM+B,QAAQ,GAAG,SAAXA,QAAQA,CAAA;MAAA,IAAIjD,KAAK,GAAAkD,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAGvC,KAAK,CAACX,KAAK;MAAA,OAAK,IAAI6C,OAAO,CAAC,UAACC,OAAO,EAAK;QACjEC,eAAe,CAAC,CAAC;QACjB,IAAI/C,KAAK,EAAE;UACTe,IAAI,CAAC,gBAAgB,CAAC;UACtBsB,QAAQ,CAACrC,KAAK,CAAC,CAACyC,IAAI,CAAC,YAAM;YACzB,IAAIxB,KAAK,CAACC,MAAM,KAAK,QAAQ,EAAE;cAC7B4B,OAAO,CAAC;gBACN3E,IAAI,EAAEwC,KAAK,CAACxC,IAAI;gBAChBkF,OAAO,EAAEpC,KAAK,CAACG;cACjB,CAAC,CAAC;cACF4B,WAAW,CAAC,CAAC;YACf,CAAC,MAAM;cACL/B,KAAK,CAACC,MAAM,GAAG,QAAQ;cACvB4B,OAAO,CAAC,CAAC;cACTE,WAAW,CAAC,CAAC;YACf;UACF,CAAC,CAAC;QACJ,CAAC,MAAM;UACLF,OAAO,CAAC,CAAC;QACX;MACF,CAAC,CAAC;IAAA;IACF,IAAMQ,mBAAmB,GAAG,SAAtBA,mBAAmBA,CAAIrB,OAAO,EAAK;MACvC,IAAIR,IAAI,IAAId,KAAK,CAACX,KAAK,EAAE;QACvB,IACEuD,eAAe,GACb9B,IAAI,CAACd,KAAK,CADZ4C,eAAe;QAEjB,IAAMC,cAAc,GAAGpH,OAAO,CAACmH,eAAe,CAAC,CAACE,QAAQ,CAACxB,OAAO,CAAC;QACjE,IAAMjC,KAAK,GAAGW,KAAK,CAACX,KAAK,CAAC0D,MAAM,CAAC,UAAClB,IAAI,EAAK;UACzC,IAAIA,IAAI,CAACP,OAAO,EAAE;YAChB,OAAO7F,OAAO,CAACoG,IAAI,CAACP,OAAO,CAAC,CAACwB,QAAQ,CAACxB,OAAO,CAAC;UAChD;UACA,OAAOuB,cAAc;QACvB,CAAC,CAAC;QACF,IAAIxD,KAAK,CAACmD,MAAM,EAAE;UAChBF,QAAQ,CAACjD,KAAK,CAAC;QACjB;MACF;IACF,CAAC;IACD,IAAM2D,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAIxB,KAAK,EAAK;MAClC,IAAIP,EAAE;MACN,IACE/C,SAAS,GACP8B,KAAK,CADP9B,SAAS;MAEX,IAAI5C,KAAK,CAAC4C,SAAS,CAAC,IAAIvB,eAAe,CAAC6E,KAAK,CAAC,GAAGtD,SAAS,EAAE;QAC1D,IAAMI,UAAU,GAAG0C,aAAa,CAAC,CAAC;QAClC,IAAI1C,UAAU,IAAI3B,eAAe,CAAC2B,UAAU,CAAC,KAAK,CAACJ,SAAS,EAAE;UAC5D,OAAOI,UAAU;QACnB;QACA,IAAM2E,YAAY,GAAG,CAAChC,EAAE,GAAGP,QAAQ,CAACc,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGP,EAAE,CAACgC,YAAY;QAC7E,IAAI3C,KAAK,CAACE,OAAO,IAAIyC,YAAY,EAAE;UACjC,IAAMC,QAAQ,GAAAC,kBAAA,CAAO3B,KAAK,CAAC;UAC3B,IAAM4B,cAAc,GAAGF,QAAQ,CAACV,MAAM,GAAG,CAACtE,SAAS;UACnDgF,QAAQ,CAACG,MAAM,CAACJ,YAAY,GAAGG,cAAc,EAAEA,cAAc,CAAC;UAC9D,OAAOF,QAAQ,CAACI,IAAI,CAAC,EAAE,CAAC;QAC1B;QACA,OAAOnH,SAAS,CAACqF,KAAK,EAAE,CAACtD,SAAS,CAAC;MACrC;MACA,OAAOsD,KAAK;IACd,CAAC;IACD,IAAM+B,WAAW,GAAG,SAAdA,WAAWA,CAAI/B,KAAK,EAA2B;MAAA,IAAzBF,OAAO,GAAAiB,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,UAAU;MAC9C,IAAMiB,aAAa,GAAGhC,KAAK;MAC3BA,KAAK,GAAGwB,gBAAgB,CAACxB,KAAK,CAAC;MAC/B,IAAMiC,YAAY,GAAG9G,eAAe,CAAC6G,aAAa,CAAC,GAAG7G,eAAe,CAAC6E,KAAK,CAAC;MAC5E,IAAIxB,KAAK,CAACjB,IAAI,KAAK,QAAQ,IAAIiB,KAAK,CAACjB,IAAI,KAAK,OAAO,EAAE;QACrD,IAAM2E,QAAQ,GAAG1D,KAAK,CAACjB,IAAI,KAAK,QAAQ;QACxCyC,KAAK,GAAG1F,YAAY,CAAC0F,KAAK,EAAEkC,QAAQ,EAAEA,QAAQ,CAAC;MACjD;MACA,IAAIC,gBAAgB,GAAG,CAAC;MACxB,IAAI3D,KAAK,CAAC7B,SAAS,IAAImD,OAAO,KAAKtB,KAAK,CAACnB,aAAa,EAAE;QACtD,IACEV,SAAS,GAEP6B,KAAK,CAFP7B,SAAS;UACTD,SAAS,GACP8B,KAAK,CADP9B,SAAS;QAEXsD,KAAK,GAAGrD,SAAS,CAACqD,KAAK,CAAC;QACxB,IAAIlG,KAAK,CAAC4C,SAAS,CAAC,IAAIvB,eAAe,CAAC6E,KAAK,CAAC,GAAGtD,SAAS,EAAE;UAC1DsD,KAAK,GAAGrF,SAAS,CAACqF,KAAK,EAAE,CAACtD,SAAS,CAAC;QACtC;QACA,IAAIwC,QAAQ,CAACc,KAAK,IAAIlB,KAAK,CAACE,OAAO,EAAE;UACnC,IACEyC,YAAY,GACVvC,QAAQ,CAACc,KAAK,CADhByB,YAAY;UAEd,IAAMW,MAAM,GAAGzH,SAAS,CAACqH,aAAa,EAAEP,YAAY,CAAC;UACrDU,gBAAgB,GAAGhH,eAAe,CAACwB,SAAS,CAACyF,MAAM,CAAC,CAAC,GAAGjH,eAAe,CAACiH,MAAM,CAAC;QACjF;MACF;MACA,IAAIlD,QAAQ,CAACc,KAAK,IAAId,QAAQ,CAACc,KAAK,CAACA,KAAK,KAAKA,KAAK,EAAE;QACpD,IAAIlB,KAAK,CAACE,OAAO,EAAE;UACjB,IAAAqD,eAAA,GAGInD,QAAQ,CAACc,KAAK;YAFhBsC,cAAc,GAAAD,eAAA,CAAdC,cAAc;YACdb,aAAY,GAAAY,eAAA,CAAZZ,YAAY;UAEdvC,QAAQ,CAACc,KAAK,CAACA,KAAK,GAAGA,KAAK;UAC5B,IAAIlG,KAAK,CAACwI,cAAc,CAAC,IAAIxI,KAAK,CAAC2H,aAAY,CAAC,EAAE;YAChD,IAAMc,QAAQ,GAAGpH,eAAe,CAAC6E,KAAK,CAAC;YACvC,IAAIiC,YAAY,EAAE;cAChBK,cAAc,IAAIL,YAAY;cAC9BR,aAAY,IAAIQ,YAAY;YAC9B,CAAC,MAAM,IAAIE,gBAAgB,EAAE;cAC3BG,cAAc,IAAIH,gBAAgB;cAClCV,aAAY,IAAIU,gBAAgB;YAClC;YACAjD,QAAQ,CAACc,KAAK,CAACwC,iBAAiB,CAACC,IAAI,CAACC,GAAG,CAACJ,cAAc,EAAEC,QAAQ,CAAC,EAAEE,IAAI,CAACC,GAAG,CAACjB,aAAY,EAAEc,QAAQ,CAAC,CAAC;UACxG;QACF,CAAC,MAAM;UACLrD,QAAQ,CAACc,KAAK,CAACA,KAAK,GAAGA,KAAK;QAC9B;MACF;MACA,IAAIA,KAAK,KAAKxB,KAAK,CAAC1B,UAAU,EAAE;QAC9B8B,IAAI,CAAC,mBAAmB,EAAEoB,KAAK,CAAC;MAClC;IACF,CAAC;IACD,IAAM2C,OAAO,GAAG,SAAVA,OAAOA,CAAIC,KAAK,EAAK;MACzB,IAAI,CAACA,KAAK,CAACC,MAAM,CAACC,SAAS,EAAE;QAC3Bf,WAAW,CAACa,KAAK,CAACC,MAAM,CAAC7C,KAAK,CAAC;MACjC;IACF,CAAC;IACD,IAAM+C,IAAI,GAAG,SAAPA,IAAIA,CAAA,EAAS;MACjB,IAAItD,EAAE;MACN,OAAO,CAACA,EAAE,GAAGP,QAAQ,CAACc,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGP,EAAE,CAACsD,IAAI,CAAC,CAAC;IAC3D,CAAC;IACD,IAAMC,KAAK,GAAG,SAARA,KAAKA,CAAA,EAAS;MAClB,IAAIvD,EAAE;MACN,OAAO,CAACA,EAAE,GAAGP,QAAQ,CAACc,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGP,EAAE,CAACuD,KAAK,CAAC,CAAC;IAC5D,CAAC;IACD,IAAMC,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAA,EAAS;MAC/B,IAAMhD,KAAK,GAAGf,QAAQ,CAACc,KAAK;MAC5B,IAAIxB,KAAK,CAACjB,IAAI,KAAK,UAAU,IAAIiB,KAAK,CAACT,QAAQ,IAAIkC,KAAK,EAAE;QACxD/E,cAAc,CAAC+E,KAAK,EAAEzB,KAAK,CAACT,QAAQ,CAAC;MACvC;IACF,CAAC;IACD,IAAMmF,OAAO,GAAG,SAAVA,OAAOA,CAAIN,KAAK,EAAK;MACzB9D,KAAK,CAACE,OAAO,GAAG,IAAI;MACpBJ,IAAI,CAAC,OAAO,EAAEgE,KAAK,CAAC;MACpBlJ,QAAQ,CAACuJ,kBAAkB,CAAC;MAC5B,IAAIvD,OAAO,CAAC,UAAU,CAAC,EAAE;QACvBqD,IAAI,CAAC,CAAC;MACR;IACF,CAAC;IACD,IAAMI,MAAM,GAAG,SAATA,MAAMA,CAAIP,KAAK,EAAK;MACxB,IAAIlD,OAAO,CAAC,UAAU,CAAC,EAAE;QACvB;MACF;MACAZ,KAAK,CAACE,OAAO,GAAG,KAAK;MACrB+C,WAAW,CAACvC,aAAa,CAAC,CAAC,EAAE,QAAQ,CAAC;MACtCZ,IAAI,CAAC,MAAM,EAAEgE,KAAK,CAAC;MACnBzB,mBAAmB,CAAC,QAAQ,CAAC;MAC7BzH,QAAQ,CAACuJ,kBAAkB,CAAC;MAC5B5I,WAAW,CAAC,CAAC;IACf,CAAC;IACD,IAAM+I,YAAY,GAAG,SAAfA,YAAYA,CAAIR,KAAK;MAAA,OAAKhE,IAAI,CAAC,aAAa,EAAEgE,KAAK,CAAC;IAAA;IAC1D,IAAMS,eAAe,GAAG,SAAlBA,eAAeA,CAAIT,KAAK;MAAA,OAAKhE,IAAI,CAAC,iBAAiB,EAAEgE,KAAK,CAAC;IAAA;IACjE,IAAMU,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAIV,KAAK;MAAA,OAAKhE,IAAI,CAAC,kBAAkB,EAAEgE,KAAK,CAAC;IAAA;IACnE,IAAMW,OAAO,GAAG,SAAVA,OAAOA,CAAIX,KAAK,EAAK;MACzBrI,cAAc,CAACqI,KAAK,CAAC;MACrBhE,IAAI,CAAC,mBAAmB,EAAE,EAAE,CAAC;MAC7BA,IAAI,CAAC,OAAO,EAAEgE,KAAK,CAAC;IACtB,CAAC;IACD,IAAMY,SAAS,GAAG/J,QAAQ,CAAC,YAAM;MAC/B,IAAI,OAAO+E,KAAK,CAAClB,KAAK,KAAK,SAAS,EAAE;QACpC,OAAOkB,KAAK,CAAClB,KAAK;MACpB;MACA,IAAIgC,IAAI,IAAIA,IAAI,CAACd,KAAK,CAACgF,SAAS,IAAI1E,KAAK,CAACC,MAAM,KAAK,QAAQ,EAAE;QAC7D,OAAO,IAAI;MACb;IACF,CAAC,CAAC;IACF,IAAM0E,UAAU,GAAGhK,QAAQ,CAAC,YAAM;MAChC,IAAMwE,UAAU,GAAGyB,OAAO,CAAC,YAAY,CAAC;MACxC,IAAIzB,UAAU,EAAE;QACd,OAAO;UACLyF,KAAK,EAAE1J,OAAO,CAACiE,UAAU;QAC3B,CAAC;MACH;IACF,CAAC,CAAC;IACF,IAAM0F,UAAU,GAAG,SAAbA,UAAUA,CAAIf,KAAK,EAAK;MAC5B,IAAMgB,UAAU,GAAG,EAAE;MACrB,IAAIhB,KAAK,CAACiB,OAAO,KAAKD,UAAU,EAAE;QAChC,IAAME,aAAa,GAAGxE,IAAI,IAAIA,IAAI,CAACd,KAAK,CAACsF,aAAa;QACtD,IAAI,CAACA,aAAa,IAAItF,KAAK,CAACjB,IAAI,KAAK,UAAU,EAAE;UAC/ChD,cAAc,CAACqI,KAAK,CAAC;QACvB;QACA,IAAIpE,KAAK,CAACjB,IAAI,KAAK,QAAQ,EAAE;UAC3BwF,IAAI,CAAC,CAAC;QACR;MACF;MACAnE,IAAI,CAAC,UAAU,EAAEgE,KAAK,CAAC;IACzB,CAAC;IACD,IAAMmB,UAAU,GAAG,SAAbA,UAAUA,CAAA;MAAA,OAASvF,KAAK,CAACrC,EAAE,OAAA6H,MAAA,CAAO7H,EAAE,WAAQ;IAAA;IAClD,IAAM8H,mBAAmB,GAAG,SAAtBA,mBAAmBA,CAAA;MAAA,OAASnF,KAAK,CAACC,MAAM;IAAA;IAC9C,IAAMmF,WAAW,GAAG,SAAdA,WAAWA,CAAA,EAAS;MACxB,IAAMC,YAAY,GAAGlI,GAAG,CAAC,SAAS,EAAE,CAACyD,OAAO,CAAC,YAAY,CAAC,EAAE;QAC1DpC,KAAK,EAAEkG,SAAS,CAACxD,KAAK;QACtBoE,MAAM,EAAE,CAAC,CAACvF,KAAK,CAACoB,KAAK;QACrB,YAAY,EAAEzB,KAAK,CAACjB,IAAI,KAAK,UAAU,IAAI,CAACiB,KAAK,CAACT;MACpD,CAAC,CAAC,CAAC;MACH,IAAIc,KAAK,CAACoB,KAAK,EAAE;QACf,OAAO5G,YAAY,CAAC,KAAK,EAAE;UACzB,OAAO,EAAE8K,YAAY;UACrB,SAAS,EAAEf;QACb,CAAC,EAAE,CAACvE,KAAK,CAACoB,KAAK,CAAC,CAAC,CAAC,CAAC;MACrB;MACA,IAAMoE,UAAU,GAAG;QACjBlI,EAAE,EAAE4H,UAAU,CAAC,CAAC;QAChBzK,GAAG,EAAE4F,QAAQ;QACblD,IAAI,EAAEwC,KAAK,CAACxC,IAAI;QAChB4B,IAAI,EAAEY,KAAK,CAACZ,IAAI,KAAK,KAAK,CAAC,GAAG,CAACY,KAAK,CAACZ,IAAI,GAAG,KAAK,CAAC;QAClD0G,KAAK,EAAEH,YAAY;QACnB1G,QAAQ,EAAEiC,OAAO,CAAC,UAAU,CAAC;QAC7BhC,QAAQ,EAAEgC,OAAO,CAAC,UAAU,CAAC;QAC7BnD,SAAS,EAAEiC,KAAK,CAACjC,SAAS;QAC1BS,WAAW,EAAEwB,KAAK,CAACxB,WAAW;QAC9BC,YAAY,EAAEuB,KAAK,CAACvB,YAAY;QAChCE,YAAY,EAAEqB,KAAK,CAACrB,YAAY;QAChC,iBAAiB,EAAEqB,KAAK,CAAC+F,KAAK,MAAAP,MAAA,CAAM7H,EAAE,cAAW,KAAK,CAAC;QACvDgH,MAAM,EAANA,MAAM;QACND,OAAO,EAAPA,OAAO;QACPP,OAAO,EAAPA,OAAO;QACP6B,OAAO,EAAEpB,YAAY;QACrBqB,QAAQ,EAAE5J,YAAY;QACtB8I,UAAU,EAAVA,UAAU;QACVe,gBAAgB,EAAE7J,YAAY;QAC9B8J,kBAAkB,EAAE3J;MACtB,CAAC;MACD,IAAIwD,KAAK,CAACjB,IAAI,KAAK,UAAU,EAAE;QAC7B,OAAOlE,YAAY,CAAC,UAAU,EAAEgL,UAAU,EAAE,IAAI,CAAC;MACnD;MACA,OAAOhL,YAAY,CAAC,OAAO,EAAEF,WAAW,CAAC2B,YAAY,CAAC0D,KAAK,CAACjB,IAAI,CAAC,EAAE8G,UAAU,CAAC,EAAE,IAAI,CAAC;IACvF,CAAC;IACD,IAAMO,cAAc,GAAG,SAAjBA,cAAcA,CAAA,EAAS;MAC3B,IAAMC,YAAY,GAAGhG,KAAK,CAAC,WAAW,CAAC;MACvC,IAAIL,KAAK,CAACnC,QAAQ,IAAIwI,YAAY,EAAE;QAClC,OAAOxL,YAAY,CAAC,KAAK,EAAE;UACzB,OAAO,EAAE4C,GAAG,CAAC,WAAW,CAAC;UACzB,SAAS,EAAEoH;QACb,CAAC,EAAE,CAACwB,YAAY,GAAGA,YAAY,CAAC,CAAC,GAAGxL,YAAY,CAACsC,IAAI,EAAE;UACrD,MAAM,EAAE6C,KAAK,CAACnC,QAAQ;UACtB,aAAa,EAAEmC,KAAK,CAACsG;QACvB,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;MACZ;IACF,CAAC;IACD,IAAMC,eAAe,GAAG,SAAlBA,eAAeA,CAAA,EAAS;MAC5B,IAAMC,aAAa,GAAGnG,KAAK,CAAC,YAAY,CAAC;MACzC,IAAIL,KAAK,CAAClC,SAAS,IAAI0I,aAAa,EAAE;QACpC,OAAO3L,YAAY,CAAC,KAAK,EAAE;UACzB,OAAO,EAAE4C,GAAG,CAAC,YAAY,CAAC;UAC1B,SAAS,EAAEqH;QACb,CAAC,EAAE,CAAC0B,aAAa,GAAGA,aAAa,CAAC,CAAC,GAAG3L,YAAY,CAACsC,IAAI,EAAE;UACvD,MAAM,EAAE6C,KAAK,CAAClC,SAAS;UACvB,aAAa,EAAEkC,KAAK,CAACsG;QACvB,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;MACZ;IACF,CAAC;IACD,IAAMG,eAAe,GAAG,SAAlBA,eAAeA,CAAA,EAAS;MAC5B,IAAIzG,KAAK,CAACJ,aAAa,IAAII,KAAK,CAAC9B,SAAS,EAAE;QAC1C,IAAMwI,KAAK,GAAG/J,eAAe,CAACqE,aAAa,CAAC,CAAC,CAAC;QAC9C,OAAOnG,YAAY,CAAC,KAAK,EAAE;UACzB,OAAO,EAAE4C,GAAG,CAAC,YAAY;QAC3B,CAAC,EAAE,CAAC5C,YAAY,CAAC,MAAM,EAAE;UACvB,OAAO,EAAE4C,GAAG,CAAC,UAAU;QACzB,CAAC,EAAE,CAACiJ,KAAK,CAAC,CAAC,EAAEjM,gBAAgB,CAAC,GAAG,CAAC,EAAEuF,KAAK,CAAC9B,SAAS,CAAC,CAAC;MACvD;IACF,CAAC;IACD,IAAMyI,aAAa,GAAG,SAAhBA,aAAaA,CAAA,EAAS;MAC1B,IAAI7F,IAAI,IAAIA,IAAI,CAACd,KAAK,CAAC4G,gBAAgB,KAAK,KAAK,EAAE;QACjD;MACF;MACA,IAAMlE,OAAO,GAAG1C,KAAK,CAACtB,YAAY,IAAI4B,KAAK,CAACG,eAAe;MAC3D,IAAIiC,OAAO,EAAE;QACX,IAAMmE,IAAI,GAAGxG,KAAK,CAAC,eAAe,CAAC;QACnC,IAAMR,iBAAiB,GAAGqB,OAAO,CAAC,mBAAmB,CAAC;QACtD,OAAOrG,YAAY,CAAC,KAAK,EAAE;UACzB,OAAO,EAAE4C,GAAG,CAAC,eAAe,EAAEoC,iBAAiB;QACjD,CAAC,EAAE,CAACgH,IAAI,GAAGA,IAAI,CAAC;UACdnE,OAAO,EAAPA;QACF,CAAC,CAAC,GAAGA,OAAO,CAAC,CAAC;MAChB;IACF,CAAC;IACD,IAAMoE,WAAW,GAAG,SAAdA,WAAWA,CAAA,EAAS;MACxB,IAAMhH,KAAK,GAAGoB,OAAO,CAAC,OAAO,CAAC,GAAG,GAAG,GAAG,EAAE;MACzC,IAAIb,KAAK,CAAC0F,KAAK,EAAE;QACf,OAAO,CAAC1F,KAAK,CAAC0F,KAAK,CAAC,CAAC,EAAEjG,KAAK,CAAC;MAC/B;MACA,IAAIE,KAAK,CAAC+F,KAAK,EAAE;QACf,OAAOlL,YAAY,CAAC,OAAO,EAAE;UAC3B,IAAI,KAAA2K,MAAA,CAAK7H,EAAE,WAAQ;UACnB,KAAK,EAAE4H,UAAU,CAAC;QACpB,CAAC,EAAE,CAACvF,KAAK,CAAC+F,KAAK,GAAGjG,KAAK,CAAC,CAAC;MAC3B;IACF,CAAC;IACD,IAAMiH,eAAe,GAAG,SAAlBA,eAAeA,CAAA;MAAA,OAAS,CAAClM,YAAY,CAAC,KAAK,EAAE;QACjD,OAAO,EAAE4C,GAAG,CAAC,MAAM;MACrB,CAAC,EAAE,CAACiI,WAAW,CAAC,CAAC,EAAEtE,SAAS,CAACI,KAAK,IAAI3G,YAAY,CAACsC,IAAI,EAAE;QACvD,KAAK,EAAEwD,YAAY;QACnB,MAAM,EAAEX,KAAK,CAAC3B,SAAS;QACvB,OAAO,EAAEZ,GAAG,CAAC,OAAO;MACtB,CAAC,EAAE,IAAI,CAAC,EAAE8I,eAAe,CAAC,CAAC,EAAElG,KAAK,CAAC2G,MAAM,IAAInM,YAAY,CAAC,KAAK,EAAE;QAC/D,OAAO,EAAE4C,GAAG,CAAC,QAAQ;MACvB,CAAC,EAAE,CAAC4C,KAAK,CAAC2G,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEP,eAAe,CAAC,CAAC,EAAEE,aAAa,CAAC,CAAC,CAAC;IAAA;IAC3DzJ,SAAS,CAAC;MACRqH,IAAI,EAAJA,IAAI;MACJC,KAAK,EAALA,KAAK;MACLlC,QAAQ,EAARA,QAAQ;MACRf,SAAS,EAATA,SAAS;MACTa,eAAe,EAAfA,eAAe;MACfqD,mBAAmB,EAAnBA;IACF,CAAC,CAAC;IACFzK,OAAO,CAACgC,0BAA0B,EAAE;MAClC4D,WAAW,EAAXA,WAAW;MACXwB,eAAe,EAAfA,eAAe;MACfO,mBAAmB,EAAnBA;IACF,CAAC,CAAC;IACF5H,KAAK,CAAC;MAAA,OAAMiF,KAAK,CAAC1B,UAAU;IAAA,GAAE,YAAM;MAClCiF,WAAW,CAACvC,aAAa,CAAC,CAAC,CAAC;MAC5BoB,eAAe,CAAC,CAAC;MACjBO,mBAAmB,CAAC,UAAU,CAAC;MAC/BzH,QAAQ,CAACuJ,kBAAkB,CAAC;IAC9B,CAAC,CAAC;IACFrJ,SAAS,CAAC,YAAM;MACdmI,WAAW,CAACvC,aAAa,CAAC,CAAC,EAAEhB,KAAK,CAACnB,aAAa,CAAC;MACjD3D,QAAQ,CAACuJ,kBAAkB,CAAC;IAC9B,CAAC,CAAC;IACF1H,gBAAgB,CAAC,YAAY,EAAEgI,OAAO,EAAE;MACtCV,MAAM,EAAEpJ,QAAQ,CAAC,YAAM;QACrB,IAAIgG,EAAE;QACN,OAAO,CAACA,EAAE,GAAGN,YAAY,CAACa,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGP,EAAE,CAACgG,GAAG;MAC5D,CAAC;IACH,CAAC,CAAC;IACF,OAAO,YAAM;MACX,IAAMhI,QAAQ,GAAGiC,OAAO,CAAC,UAAU,CAAC;MACpC,IAAMvB,UAAU,GAAGuB,OAAO,CAAC,YAAY,CAAC;MACxC,IAAMgG,KAAK,GAAGJ,WAAW,CAAC,CAAC;MAC3B,IAAMK,QAAQ,GAAGf,cAAc,CAAC,CAAC;MACjC,OAAOvL,YAAY,CAACuC,IAAI,EAAE;QACxB,MAAM,EAAE4C,KAAK,CAACoH,IAAI;QAClB,MAAM,EAAEpH,KAAK,CAACnC,QAAQ;QACtB,OAAO,EAAEJ,GAAG,CAAA4J,eAAA;UACVvI,KAAK,EAAEkG,SAAS,CAACxD,KAAK;UACtBvC,QAAQ,EAARA;QAAQ,YAAAuG,MAAA,CACE7F,UAAU,GAAKA,UAAU,CACpC,CAAC;QACF,QAAQ,EAAEK,KAAK,CAACsH,MAAM;QACtB,QAAQ,EAAEtH,KAAK,CAACuH,MAAM;QACtB,QAAQ,EAAEvH,KAAK,CAACwH,MAAM;QACtB,WAAW,EAAExH,KAAK,CAACyH,SAAS;QAC5B,YAAY,EAAExC,UAAU,CAACzD,KAAK;QAC9B,YAAY,EAAE/D,GAAG,CAAC,OAAO,CAAC;QAC1B,YAAY,EAAE,CAACA,GAAG,CAAC,OAAO,EAAE,CAACkC,UAAU,EAAE;UACvC+H,QAAQ,EAAE1H,KAAK,CAAC0H;QAClB,CAAC,CAAC,CAAC,EAAE1H,KAAK,CAACN,UAAU,CAAC;QACtB,gBAAgB,EAAEM,KAAK,CAAC2H;MAC1B,CAAC,EAAE;QACDC,IAAI,EAAET,QAAQ,GAAG;UAAA,OAAMA,QAAQ;QAAA,IAAG,IAAI;QACtCU,KAAK,EAAEX,KAAK,GAAG;UAAA,OAAMA,KAAK;QAAA,IAAG,IAAI;QACjC1F,KAAK,EAAEuF,eAAe;QACtBe,KAAK,EAAEzH,KAAK,CAACyH;MACf,CAAC,CAAC;IACJ,CAAC;EACH;AACF,CAAC,CAAC;AACF,SACE/H,aAAa,IAAIf,OAAO,EACxBtB,gBAAgB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}