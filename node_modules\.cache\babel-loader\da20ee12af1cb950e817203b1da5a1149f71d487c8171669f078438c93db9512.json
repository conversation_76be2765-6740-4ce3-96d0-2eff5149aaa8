{"ast": null, "code": "function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nfunction _iterableToArrayLimit(arr, i) { var _i = null == arr ? null : \"undefined\" != typeof Symbol && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (null != _i) { var _s, _e, _x, _r, _arr = [], _n = !0, _d = !1; try { if (_x = (_i = _i.call(arr)).next, 0 === i) { if (Object(_i) !== _i) return; _n = !1; } else for (; !(_n = (_s = _x.call(_i)).done) && (_arr.push(_s.value), _arr.length !== i); _n = !0); } catch (err) { _d = !0, _e = err; } finally { try { if (!_n && null != _i.return && (_r = _i.return(), Object(_r) !== _r)) return; } finally { if (_d) throw _e; } } return _arr; } }\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\nimport { createVNode as _createVNode } from \"vue\";\nimport { provide, computed, watchEffect, defineComponent } from \"vue\";\nimport { kebabCase, makeStringProp, createNamespace } from \"../utils/index.mjs\";\nimport { setGlobalZIndex } from \"../composables/use-global-z-index.mjs\";\nvar _createNamespace = createNamespace(\"config-provider\"),\n  _createNamespace2 = _slicedToArray(_createNamespace, 2),\n  name = _createNamespace2[0],\n  bem = _createNamespace2[1];\nvar CONFIG_PROVIDER_KEY = Symbol(name);\nvar configProviderProps = {\n  tag: makeStringProp(\"div\"),\n  zIndex: Number,\n  themeVars: Object,\n  iconPrefix: String\n};\nfunction mapThemeVarsToCSSVars(themeVars) {\n  var cssVars = {};\n  Object.keys(themeVars).forEach(function (key) {\n    cssVars[\"--van-\".concat(kebabCase(key))] = themeVars[key];\n  });\n  return cssVars;\n}\nvar stdin_default = defineComponent({\n  name: name,\n  props: configProviderProps,\n  setup: function setup(props, _ref) {\n    var slots = _ref.slots;\n    var style = computed(function () {\n      if (props.themeVars) {\n        return mapThemeVarsToCSSVars(props.themeVars);\n      }\n    });\n    provide(CONFIG_PROVIDER_KEY, props);\n    watchEffect(function () {\n      if (props.zIndex !== void 0) {\n        setGlobalZIndex(props.zIndex);\n      }\n    });\n    return function () {\n      return _createVNode(props.tag, {\n        \"class\": bem(),\n        \"style\": style.value\n      }, {\n        default: function _default() {\n          var _a;\n          return [(_a = slots.default) == null ? void 0 : _a.call(slots)];\n        }\n      });\n    };\n  }\n});\nexport { CONFIG_PROVIDER_KEY, stdin_default as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}