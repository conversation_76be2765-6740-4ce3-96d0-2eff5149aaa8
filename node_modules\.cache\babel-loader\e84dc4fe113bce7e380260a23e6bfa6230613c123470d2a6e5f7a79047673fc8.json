{"ast": null, "code": "import { raf } from \"@vant/use\";\nimport { getScrollTop, setScrollTop } from \"../utils/index.mjs\";\nfunction scrollLeftTo(scroller, to, duration) {\n  var count = 0;\n  var from = scroller.scrollLeft;\n  var frames = duration === 0 ? 1 : Math.round(duration * 1e3 / 16);\n  function animate() {\n    scroller.scrollLeft += (to - from) / frames;\n    if (++count < frames) {\n      raf(animate);\n    }\n  }\n  animate();\n}\nfunction scrollTopTo(scroller, to, duration, callback) {\n  var current = getScrollTop(scroller);\n  var isDown = current < to;\n  var frames = duration === 0 ? 1 : Math.round(duration * 1e3 / 16);\n  var step = (to - current) / frames;\n  function animate() {\n    current += step;\n    if (isDown && current > to || !isDown && current < to) {\n      current = to;\n    }\n    setScrollTop(scroller, current);\n    if (isDown && current < to || !isDown && current > to) {\n      raf(animate);\n    } else if (callback) {\n      raf(callback);\n    }\n  }\n  animate();\n}\nexport { scrollLeftTo, scrollTopTo };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}