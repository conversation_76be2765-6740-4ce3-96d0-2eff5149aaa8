{"ast": null, "code": "import { ref } from 'vue';\nimport { getsupport } from '@/api/tel/index';\nimport { useRouter } from 'vue-router';\nimport store from '@/store/index';\nexport default {\n  setup: function setup() {\n    var _useRouter = useRouter(),\n      push = _useRouter.push;\n    var tel = function tel(row) {\n      window.location.href = row.url;\n    };\n    var clickRight = function clickRight() {\n      push('/message');\n    };\n    store.dispatch('changefooCheck', 'tel');\n    var list = ref([]);\n    getsupport().then(function (res) {\n      if (res.code === 0) {\n        list.value = res.data || [];\n      }\n    });\n    var toTel = function toTel() {\n      if (list.value) {\n        location.href = list.value;\n        // window.open(support.value)\n      }\n    };\n\n    return {\n      tel: tel,\n      list: list,\n      clickRight: clickRight,\n      toTel: toTel\n    };\n  }\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}