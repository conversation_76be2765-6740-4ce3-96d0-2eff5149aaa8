{"ast": null, "code": "import { ref } from 'vue';\nimport footerDemo from '@/components/footer.vue';\nexport default {\n  components: {\n    footerDemo: footerDemo\n  },\n  setup: function setup() {\n    var showFooter = ref(true);\n    var hideFooter = function hideFooter(val) {\n      showFooter.value = !val;\n    };\n    return {\n      hideFooter: hideFooter,\n      showFooter: showFooter\n    };\n  }\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}