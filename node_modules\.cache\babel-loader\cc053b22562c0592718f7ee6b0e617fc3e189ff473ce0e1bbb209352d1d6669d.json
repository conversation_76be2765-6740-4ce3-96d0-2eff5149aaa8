{"ast": null, "code": "function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nfunction _iterableToArrayLimit(arr, i) { var _i = null == arr ? null : \"undefined\" != typeof Symbol && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (null != _i) { var _s, _e, _x, _r, _arr = [], _n = !0, _d = !1; try { if (_x = (_i = _i.call(arr)).next, 0 === i) { if (Object(_i) !== _i) return; _n = !1; } else for (; !(_n = (_s = _x.call(_i)).done) && (_arr.push(_s.value), _arr.length !== i); _n = !0); } catch (err) { _d = !0, _e = err; } finally { try { if (!_n && null != _i.return && (_r = _i.return(), Object(_r) !== _r)) return; } finally { if (_d) throw _e; } } return _arr; } }\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\nimport { createVNode as _createVNode } from \"vue\";\nimport { ref, watch, reactive, computed, onMounted, onActivated, onDeactivated, onBeforeUnmount, defineComponent, nextTick } from \"vue\";\nimport { clamp, isHidden, truthProp, numericProp, windowWidth, windowHeight, preventDefault, createNamespace, makeNumericProp } from \"../utils/index.mjs\";\nimport { doubleRaf, useChildren, useEventListener, usePageVisibility } from \"@vant/use\";\nimport { useTouch } from \"../composables/use-touch.mjs\";\nimport { useExpose } from \"../composables/use-expose.mjs\";\nimport { onPopupReopen } from \"../composables/on-popup-reopen.mjs\";\nvar _createNamespace = createNamespace(\"swipe\"),\n  _createNamespace2 = _slicedToArray(_createNamespace, 2),\n  name = _createNamespace2[0],\n  bem = _createNamespace2[1];\nvar swipeProps = {\n  loop: truthProp,\n  width: numericProp,\n  height: numericProp,\n  vertical: Boolean,\n  autoplay: makeNumericProp(0),\n  duration: makeNumericProp(500),\n  touchable: truthProp,\n  lazyRender: Boolean,\n  initialSwipe: makeNumericProp(0),\n  indicatorColor: String,\n  showIndicators: truthProp,\n  stopPropagation: truthProp\n};\nvar SWIPE_KEY = Symbol(name);\nvar stdin_default = defineComponent({\n  name: name,\n  props: swipeProps,\n  emits: [\"change\"],\n  setup: function setup(props, _ref) {\n    var emit = _ref.emit,\n      slots = _ref.slots;\n    var root = ref();\n    var track = ref();\n    var state = reactive({\n      rect: null,\n      width: 0,\n      height: 0,\n      offset: 0,\n      active: 0,\n      swiping: false\n    });\n    var touch = useTouch();\n    var _useChildren = useChildren(SWIPE_KEY),\n      children = _useChildren.children,\n      linkChildren = _useChildren.linkChildren;\n    var count = computed(function () {\n      return children.length;\n    });\n    var size = computed(function () {\n      return state[props.vertical ? \"height\" : \"width\"];\n    });\n    var delta = computed(function () {\n      return props.vertical ? touch.deltaY.value : touch.deltaX.value;\n    });\n    var minOffset = computed(function () {\n      if (state.rect) {\n        var base = props.vertical ? state.rect.height : state.rect.width;\n        return base - size.value * count.value;\n      }\n      return 0;\n    });\n    var maxCount = computed(function () {\n      return Math.ceil(Math.abs(minOffset.value) / size.value);\n    });\n    var trackSize = computed(function () {\n      return count.value * size.value;\n    });\n    var activeIndicator = computed(function () {\n      return (state.active + count.value) % count.value;\n    });\n    var isCorrectDirection = computed(function () {\n      var expect = props.vertical ? \"vertical\" : \"horizontal\";\n      return touch.direction.value === expect;\n    });\n    var trackStyle = computed(function () {\n      var style = {\n        transitionDuration: \"\".concat(state.swiping ? 0 : props.duration, \"ms\"),\n        transform: \"translate\".concat(props.vertical ? \"Y\" : \"X\", \"(\").concat(state.offset, \"px)\")\n      };\n      if (size.value) {\n        var mainAxis = props.vertical ? \"height\" : \"width\";\n        var crossAxis = props.vertical ? \"width\" : \"height\";\n        style[mainAxis] = \"\".concat(trackSize.value, \"px\");\n        style[crossAxis] = props[crossAxis] ? \"\".concat(props[crossAxis], \"px\") : \"\";\n      }\n      return style;\n    });\n    var getTargetActive = function getTargetActive(pace) {\n      var active = state.active;\n      if (pace) {\n        if (props.loop) {\n          return clamp(active + pace, -1, count.value);\n        }\n        return clamp(active + pace, 0, maxCount.value);\n      }\n      return active;\n    };\n    var getTargetOffset = function getTargetOffset(targetActive) {\n      var offset = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n      var currentPosition = targetActive * size.value;\n      if (!props.loop) {\n        currentPosition = Math.min(currentPosition, -minOffset.value);\n      }\n      var targetOffset = offset - currentPosition;\n      if (!props.loop) {\n        targetOffset = clamp(targetOffset, minOffset.value, 0);\n      }\n      return targetOffset;\n    };\n    var move = function move(_ref2) {\n      var _ref2$pace = _ref2.pace,\n        pace = _ref2$pace === void 0 ? 0 : _ref2$pace,\n        _ref2$offset = _ref2.offset,\n        offset = _ref2$offset === void 0 ? 0 : _ref2$offset,\n        emitChange = _ref2.emitChange;\n      if (count.value <= 1) {\n        return;\n      }\n      var active = state.active;\n      var targetActive = getTargetActive(pace);\n      var targetOffset = getTargetOffset(targetActive, offset);\n      if (props.loop) {\n        if (children[0] && targetOffset !== minOffset.value) {\n          var outRightBound = targetOffset < minOffset.value;\n          children[0].setOffset(outRightBound ? trackSize.value : 0);\n        }\n        if (children[count.value - 1] && targetOffset !== 0) {\n          var outLeftBound = targetOffset > 0;\n          children[count.value - 1].setOffset(outLeftBound ? -trackSize.value : 0);\n        }\n      }\n      state.active = targetActive;\n      state.offset = targetOffset;\n      if (emitChange && targetActive !== active) {\n        emit(\"change\", activeIndicator.value);\n      }\n    };\n    var correctPosition = function correctPosition() {\n      state.swiping = true;\n      if (state.active <= -1) {\n        move({\n          pace: count.value\n        });\n      } else if (state.active >= count.value) {\n        move({\n          pace: -count.value\n        });\n      }\n    };\n    var prev = function prev() {\n      correctPosition();\n      touch.reset();\n      doubleRaf(function () {\n        state.swiping = false;\n        move({\n          pace: -1,\n          emitChange: true\n        });\n      });\n    };\n    var next = function next() {\n      correctPosition();\n      touch.reset();\n      doubleRaf(function () {\n        state.swiping = false;\n        move({\n          pace: 1,\n          emitChange: true\n        });\n      });\n    };\n    var autoplayTimer;\n    var stopAutoplay = function stopAutoplay() {\n      return clearTimeout(autoplayTimer);\n    };\n    var autoplay = function autoplay() {\n      stopAutoplay();\n      if (props.autoplay > 0 && count.value > 1) {\n        autoplayTimer = setTimeout(function () {\n          next();\n          autoplay();\n        }, +props.autoplay);\n      }\n    };\n    var initialize = function initialize() {\n      var active = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : +props.initialSwipe;\n      if (!root.value) {\n        return;\n      }\n      var cb = function cb() {\n        var _a, _b;\n        if (!isHidden(root)) {\n          var rect = {\n            width: root.value.offsetWidth,\n            height: root.value.offsetHeight\n          };\n          state.rect = rect;\n          state.width = +((_a = props.width) != null ? _a : rect.width);\n          state.height = +((_b = props.height) != null ? _b : rect.height);\n        }\n        if (count.value) {\n          active = Math.min(count.value - 1, active);\n        }\n        state.active = active;\n        state.swiping = true;\n        state.offset = getTargetOffset(active);\n        children.forEach(function (swipe) {\n          swipe.setOffset(0);\n        });\n        autoplay();\n      };\n      if (isHidden(root)) {\n        nextTick().then(cb);\n      } else {\n        cb();\n      }\n    };\n    var resize = function resize() {\n      return initialize(state.active);\n    };\n    var touchStartTime;\n    var onTouchStart = function onTouchStart(event) {\n      if (!props.touchable) return;\n      touch.start(event);\n      touchStartTime = Date.now();\n      stopAutoplay();\n      correctPosition();\n    };\n    var onTouchMove = function onTouchMove(event) {\n      if (props.touchable && state.swiping) {\n        touch.move(event);\n        if (isCorrectDirection.value) {\n          var isEdgeTouch = !props.loop && (state.active === 0 && delta.value > 0 || state.active === count.value - 1 && delta.value < 0);\n          if (!isEdgeTouch) {\n            preventDefault(event, props.stopPropagation);\n            move({\n              offset: delta.value\n            });\n          }\n        }\n      }\n    };\n    var onTouchEnd = function onTouchEnd() {\n      if (!props.touchable || !state.swiping) {\n        return;\n      }\n      var duration = Date.now() - touchStartTime;\n      var speed = delta.value / duration;\n      var shouldSwipe = Math.abs(speed) > 0.25 || Math.abs(delta.value) > size.value / 2;\n      if (shouldSwipe && isCorrectDirection.value) {\n        var offset = props.vertical ? touch.offsetY.value : touch.offsetX.value;\n        var pace = 0;\n        if (props.loop) {\n          pace = offset > 0 ? delta.value > 0 ? -1 : 1 : 0;\n        } else {\n          pace = -Math[delta.value > 0 ? \"ceil\" : \"floor\"](delta.value / size.value);\n        }\n        move({\n          pace: pace,\n          emitChange: true\n        });\n      } else if (delta.value) {\n        move({\n          pace: 0\n        });\n      }\n      state.swiping = false;\n      autoplay();\n    };\n    var swipeTo = function swipeTo(index) {\n      var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n      correctPosition();\n      touch.reset();\n      doubleRaf(function () {\n        var targetIndex;\n        if (props.loop && index === count.value) {\n          targetIndex = state.active === 0 ? 0 : index;\n        } else {\n          targetIndex = index % count.value;\n        }\n        if (options.immediate) {\n          doubleRaf(function () {\n            state.swiping = false;\n          });\n        } else {\n          state.swiping = false;\n        }\n        move({\n          pace: targetIndex - state.active,\n          emitChange: true\n        });\n      });\n    };\n    var renderDot = function renderDot(_, index) {\n      var active = index === activeIndicator.value;\n      var style = active ? {\n        backgroundColor: props.indicatorColor\n      } : void 0;\n      return _createVNode(\"i\", {\n        \"style\": style,\n        \"class\": bem(\"indicator\", {\n          active: active\n        })\n      }, null);\n    };\n    var renderIndicator = function renderIndicator() {\n      if (slots.indicator) {\n        return slots.indicator({\n          active: activeIndicator.value,\n          total: count.value\n        });\n      }\n      if (props.showIndicators && count.value > 1) {\n        return _createVNode(\"div\", {\n          \"class\": bem(\"indicators\", {\n            vertical: props.vertical\n          })\n        }, [Array(count.value).fill(\"\").map(renderDot)]);\n      }\n    };\n    useExpose({\n      prev: prev,\n      next: next,\n      state: state,\n      resize: resize,\n      swipeTo: swipeTo\n    });\n    linkChildren({\n      size: size,\n      props: props,\n      count: count,\n      activeIndicator: activeIndicator\n    });\n    watch(function () {\n      return props.initialSwipe;\n    }, function (value) {\n      return initialize(+value);\n    });\n    watch(count, function () {\n      return initialize(state.active);\n    });\n    watch(function () {\n      return props.autoplay;\n    }, autoplay);\n    watch([windowWidth, windowHeight], resize);\n    watch(usePageVisibility(), function (visible) {\n      if (visible === \"visible\") {\n        autoplay();\n      } else {\n        stopAutoplay();\n      }\n    });\n    onMounted(initialize);\n    onActivated(function () {\n      return initialize(state.active);\n    });\n    onPopupReopen(function () {\n      return initialize(state.active);\n    });\n    onDeactivated(stopAutoplay);\n    onBeforeUnmount(stopAutoplay);\n    useEventListener(\"touchmove\", onTouchMove, {\n      target: track\n    });\n    return function () {\n      var _a;\n      return _createVNode(\"div\", {\n        \"ref\": root,\n        \"class\": bem()\n      }, [_createVNode(\"div\", {\n        \"ref\": track,\n        \"style\": trackStyle.value,\n        \"class\": bem(\"track\", {\n          vertical: props.vertical\n        }),\n        \"onTouchstartPassive\": onTouchStart,\n        \"onTouchend\": onTouchEnd,\n        \"onTouchcancel\": onTouchEnd\n      }, [(_a = slots.default) == null ? void 0 : _a.call(slots)]), renderIndicator()]);\n    };\n  }\n});\nexport { SWIPE_KEY, stdin_default as default };", "map": {"version": 3, "names": ["createVNode", "_createVNode", "ref", "watch", "reactive", "computed", "onMounted", "onActivated", "onDeactivated", "onBeforeUnmount", "defineComponent", "nextTick", "clamp", "isHidden", "truthProp", "numericProp", "windowWidth", "windowHeight", "preventDefault", "createNamespace", "makeNumericProp", "doubleRaf", "useChildren", "useEventListener", "usePageVisibility", "useTouch", "useExpose", "onPopupReopen", "_createNamespace", "_createNamespace2", "_slicedToArray", "name", "bem", "swipeProps", "loop", "width", "height", "vertical", "Boolean", "autoplay", "duration", "touchable", "lazy<PERSON>ender", "initialSwipe", "indicatorColor", "String", "showIndicators", "stopPropagation", "SWIPE_KEY", "Symbol", "stdin_default", "props", "emits", "setup", "_ref", "emit", "slots", "root", "track", "state", "rect", "offset", "active", "swiping", "touch", "_useChildren", "children", "linkChildren", "count", "length", "size", "delta", "deltaY", "value", "deltaX", "minOffset", "base", "maxCount", "Math", "ceil", "abs", "trackSize", "activeIndicator", "isCorrectDirection", "expect", "direction", "trackStyle", "style", "transitionDuration", "concat", "transform", "mainAxis", "crossAxis", "getTargetActive", "pace", "getTargetOffset", "targetActive", "arguments", "undefined", "currentPosition", "min", "targetOffset", "move", "_ref2", "_ref2$pace", "_ref2$offset", "emitChange", "outRightBound", "setOffset", "outLeftBound", "correctPosition", "prev", "reset", "next", "autoplayTimer", "stopAutoplay", "clearTimeout", "setTimeout", "initialize", "cb", "_a", "_b", "offsetWidth", "offsetHeight", "for<PERSON>ach", "swipe", "then", "resize", "touchStartTime", "onTouchStart", "event", "start", "Date", "now", "onTouchMove", "isEdgeTouch", "onTouchEnd", "speed", "shouldSwipe", "offsetY", "offsetX", "swipeTo", "index", "options", "targetIndex", "immediate", "renderDot", "_", "backgroundColor", "renderIndicator", "indicator", "total", "Array", "fill", "map", "visible", "target", "default", "call"], "sources": ["C:/Users/<USER>/Downloads/Telegram Desktop/vue3_3.0/vue3_3.0/node_modules/vant/es/swipe/Swipe.mjs"], "sourcesContent": ["import { createVNode as _createVNode } from \"vue\";\nimport { ref, watch, reactive, computed, onMounted, onActivated, onDeactivated, onBeforeUnmount, defineComponent, nextTick } from \"vue\";\nimport { clamp, isHidden, truthProp, numericProp, windowWidth, windowHeight, preventDefault, createNamespace, makeNumericProp } from \"../utils/index.mjs\";\nimport { doubleRaf, useChildren, useEventListener, usePageVisibility } from \"@vant/use\";\nimport { useTouch } from \"../composables/use-touch.mjs\";\nimport { useExpose } from \"../composables/use-expose.mjs\";\nimport { onPopupReopen } from \"../composables/on-popup-reopen.mjs\";\nconst [name, bem] = createNamespace(\"swipe\");\nconst swipeProps = {\n  loop: truthProp,\n  width: numericProp,\n  height: numericProp,\n  vertical: Boolean,\n  autoplay: makeNumericProp(0),\n  duration: makeNumericProp(500),\n  touchable: truthProp,\n  lazyRender: Boolean,\n  initialSwipe: makeNumericProp(0),\n  indicatorColor: String,\n  showIndicators: truthProp,\n  stopPropagation: truthProp\n};\nconst SWIPE_KEY = Symbol(name);\nvar stdin_default = defineComponent({\n  name,\n  props: swipeProps,\n  emits: [\"change\"],\n  setup(props, {\n    emit,\n    slots\n  }) {\n    const root = ref();\n    const track = ref();\n    const state = reactive({\n      rect: null,\n      width: 0,\n      height: 0,\n      offset: 0,\n      active: 0,\n      swiping: false\n    });\n    const touch = useTouch();\n    const {\n      children,\n      linkChildren\n    } = useChildren(SWIPE_KEY);\n    const count = computed(() => children.length);\n    const size = computed(() => state[props.vertical ? \"height\" : \"width\"]);\n    const delta = computed(() => props.vertical ? touch.deltaY.value : touch.deltaX.value);\n    const minOffset = computed(() => {\n      if (state.rect) {\n        const base = props.vertical ? state.rect.height : state.rect.width;\n        return base - size.value * count.value;\n      }\n      return 0;\n    });\n    const maxCount = computed(() => Math.ceil(Math.abs(minOffset.value) / size.value));\n    const trackSize = computed(() => count.value * size.value);\n    const activeIndicator = computed(() => (state.active + count.value) % count.value);\n    const isCorrectDirection = computed(() => {\n      const expect = props.vertical ? \"vertical\" : \"horizontal\";\n      return touch.direction.value === expect;\n    });\n    const trackStyle = computed(() => {\n      const style = {\n        transitionDuration: `${state.swiping ? 0 : props.duration}ms`,\n        transform: `translate${props.vertical ? \"Y\" : \"X\"}(${state.offset}px)`\n      };\n      if (size.value) {\n        const mainAxis = props.vertical ? \"height\" : \"width\";\n        const crossAxis = props.vertical ? \"width\" : \"height\";\n        style[mainAxis] = `${trackSize.value}px`;\n        style[crossAxis] = props[crossAxis] ? `${props[crossAxis]}px` : \"\";\n      }\n      return style;\n    });\n    const getTargetActive = (pace) => {\n      const {\n        active\n      } = state;\n      if (pace) {\n        if (props.loop) {\n          return clamp(active + pace, -1, count.value);\n        }\n        return clamp(active + pace, 0, maxCount.value);\n      }\n      return active;\n    };\n    const getTargetOffset = (targetActive, offset = 0) => {\n      let currentPosition = targetActive * size.value;\n      if (!props.loop) {\n        currentPosition = Math.min(currentPosition, -minOffset.value);\n      }\n      let targetOffset = offset - currentPosition;\n      if (!props.loop) {\n        targetOffset = clamp(targetOffset, minOffset.value, 0);\n      }\n      return targetOffset;\n    };\n    const move = ({\n      pace = 0,\n      offset = 0,\n      emitChange\n    }) => {\n      if (count.value <= 1) {\n        return;\n      }\n      const {\n        active\n      } = state;\n      const targetActive = getTargetActive(pace);\n      const targetOffset = getTargetOffset(targetActive, offset);\n      if (props.loop) {\n        if (children[0] && targetOffset !== minOffset.value) {\n          const outRightBound = targetOffset < minOffset.value;\n          children[0].setOffset(outRightBound ? trackSize.value : 0);\n        }\n        if (children[count.value - 1] && targetOffset !== 0) {\n          const outLeftBound = targetOffset > 0;\n          children[count.value - 1].setOffset(outLeftBound ? -trackSize.value : 0);\n        }\n      }\n      state.active = targetActive;\n      state.offset = targetOffset;\n      if (emitChange && targetActive !== active) {\n        emit(\"change\", activeIndicator.value);\n      }\n    };\n    const correctPosition = () => {\n      state.swiping = true;\n      if (state.active <= -1) {\n        move({\n          pace: count.value\n        });\n      } else if (state.active >= count.value) {\n        move({\n          pace: -count.value\n        });\n      }\n    };\n    const prev = () => {\n      correctPosition();\n      touch.reset();\n      doubleRaf(() => {\n        state.swiping = false;\n        move({\n          pace: -1,\n          emitChange: true\n        });\n      });\n    };\n    const next = () => {\n      correctPosition();\n      touch.reset();\n      doubleRaf(() => {\n        state.swiping = false;\n        move({\n          pace: 1,\n          emitChange: true\n        });\n      });\n    };\n    let autoplayTimer;\n    const stopAutoplay = () => clearTimeout(autoplayTimer);\n    const autoplay = () => {\n      stopAutoplay();\n      if (props.autoplay > 0 && count.value > 1) {\n        autoplayTimer = setTimeout(() => {\n          next();\n          autoplay();\n        }, +props.autoplay);\n      }\n    };\n    const initialize = (active = +props.initialSwipe) => {\n      if (!root.value) {\n        return;\n      }\n      const cb = () => {\n        var _a, _b;\n        if (!isHidden(root)) {\n          const rect = {\n            width: root.value.offsetWidth,\n            height: root.value.offsetHeight\n          };\n          state.rect = rect;\n          state.width = +((_a = props.width) != null ? _a : rect.width);\n          state.height = +((_b = props.height) != null ? _b : rect.height);\n        }\n        if (count.value) {\n          active = Math.min(count.value - 1, active);\n        }\n        state.active = active;\n        state.swiping = true;\n        state.offset = getTargetOffset(active);\n        children.forEach((swipe) => {\n          swipe.setOffset(0);\n        });\n        autoplay();\n      };\n      if (isHidden(root)) {\n        nextTick().then(cb);\n      } else {\n        cb();\n      }\n    };\n    const resize = () => initialize(state.active);\n    let touchStartTime;\n    const onTouchStart = (event) => {\n      if (!props.touchable)\n        return;\n      touch.start(event);\n      touchStartTime = Date.now();\n      stopAutoplay();\n      correctPosition();\n    };\n    const onTouchMove = (event) => {\n      if (props.touchable && state.swiping) {\n        touch.move(event);\n        if (isCorrectDirection.value) {\n          const isEdgeTouch = !props.loop && (state.active === 0 && delta.value > 0 || state.active === count.value - 1 && delta.value < 0);\n          if (!isEdgeTouch) {\n            preventDefault(event, props.stopPropagation);\n            move({\n              offset: delta.value\n            });\n          }\n        }\n      }\n    };\n    const onTouchEnd = () => {\n      if (!props.touchable || !state.swiping) {\n        return;\n      }\n      const duration = Date.now() - touchStartTime;\n      const speed = delta.value / duration;\n      const shouldSwipe = Math.abs(speed) > 0.25 || Math.abs(delta.value) > size.value / 2;\n      if (shouldSwipe && isCorrectDirection.value) {\n        const offset = props.vertical ? touch.offsetY.value : touch.offsetX.value;\n        let pace = 0;\n        if (props.loop) {\n          pace = offset > 0 ? delta.value > 0 ? -1 : 1 : 0;\n        } else {\n          pace = -Math[delta.value > 0 ? \"ceil\" : \"floor\"](delta.value / size.value);\n        }\n        move({\n          pace,\n          emitChange: true\n        });\n      } else if (delta.value) {\n        move({\n          pace: 0\n        });\n      }\n      state.swiping = false;\n      autoplay();\n    };\n    const swipeTo = (index, options = {}) => {\n      correctPosition();\n      touch.reset();\n      doubleRaf(() => {\n        let targetIndex;\n        if (props.loop && index === count.value) {\n          targetIndex = state.active === 0 ? 0 : index;\n        } else {\n          targetIndex = index % count.value;\n        }\n        if (options.immediate) {\n          doubleRaf(() => {\n            state.swiping = false;\n          });\n        } else {\n          state.swiping = false;\n        }\n        move({\n          pace: targetIndex - state.active,\n          emitChange: true\n        });\n      });\n    };\n    const renderDot = (_, index) => {\n      const active = index === activeIndicator.value;\n      const style = active ? {\n        backgroundColor: props.indicatorColor\n      } : void 0;\n      return _createVNode(\"i\", {\n        \"style\": style,\n        \"class\": bem(\"indicator\", {\n          active\n        })\n      }, null);\n    };\n    const renderIndicator = () => {\n      if (slots.indicator) {\n        return slots.indicator({\n          active: activeIndicator.value,\n          total: count.value\n        });\n      }\n      if (props.showIndicators && count.value > 1) {\n        return _createVNode(\"div\", {\n          \"class\": bem(\"indicators\", {\n            vertical: props.vertical\n          })\n        }, [Array(count.value).fill(\"\").map(renderDot)]);\n      }\n    };\n    useExpose({\n      prev,\n      next,\n      state,\n      resize,\n      swipeTo\n    });\n    linkChildren({\n      size,\n      props,\n      count,\n      activeIndicator\n    });\n    watch(() => props.initialSwipe, (value) => initialize(+value));\n    watch(count, () => initialize(state.active));\n    watch(() => props.autoplay, autoplay);\n    watch([windowWidth, windowHeight], resize);\n    watch(usePageVisibility(), (visible) => {\n      if (visible === \"visible\") {\n        autoplay();\n      } else {\n        stopAutoplay();\n      }\n    });\n    onMounted(initialize);\n    onActivated(() => initialize(state.active));\n    onPopupReopen(() => initialize(state.active));\n    onDeactivated(stopAutoplay);\n    onBeforeUnmount(stopAutoplay);\n    useEventListener(\"touchmove\", onTouchMove, {\n      target: track\n    });\n    return () => {\n      var _a;\n      return _createVNode(\"div\", {\n        \"ref\": root,\n        \"class\": bem()\n      }, [_createVNode(\"div\", {\n        \"ref\": track,\n        \"style\": trackStyle.value,\n        \"class\": bem(\"track\", {\n          vertical: props.vertical\n        }),\n        \"onTouchstartPassive\": onTouchStart,\n        \"onTouchend\": onTouchEnd,\n        \"onTouchcancel\": onTouchEnd\n      }, [(_a = slots.default) == null ? void 0 : _a.call(slots)]), renderIndicator()]);\n    };\n  }\n});\nexport {\n  SWIPE_KEY,\n  stdin_default as default\n};\n"], "mappings": ";;;;;;AAAA,SAASA,WAAW,IAAIC,YAAY,QAAQ,KAAK;AACjD,SAASC,GAAG,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,aAAa,EAAEC,eAAe,EAAEC,eAAe,EAAEC,QAAQ,QAAQ,KAAK;AACvI,SAASC,KAAK,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,WAAW,EAAEC,YAAY,EAAEC,cAAc,EAAEC,eAAe,EAAEC,eAAe,QAAQ,oBAAoB;AACzJ,SAASC,SAAS,EAAEC,WAAW,EAAEC,gBAAgB,EAAEC,iBAAiB,QAAQ,WAAW;AACvF,SAASC,QAAQ,QAAQ,8BAA8B;AACvD,SAASC,SAAS,QAAQ,+BAA+B;AACzD,SAASC,aAAa,QAAQ,oCAAoC;AAClE,IAAAC,gBAAA,GAAoBT,eAAe,CAAC,OAAO,CAAC;EAAAU,iBAAA,GAAAC,cAAA,CAAAF,gBAAA;EAArCG,IAAI,GAAAF,iBAAA;EAAEG,GAAG,GAAAH,iBAAA;AAChB,IAAMI,UAAU,GAAG;EACjBC,IAAI,EAAEpB,SAAS;EACfqB,KAAK,EAAEpB,WAAW;EAClBqB,MAAM,EAAErB,WAAW;EACnBsB,QAAQ,EAAEC,OAAO;EACjBC,QAAQ,EAAEnB,eAAe,CAAC,CAAC,CAAC;EAC5BoB,QAAQ,EAAEpB,eAAe,CAAC,GAAG,CAAC;EAC9BqB,SAAS,EAAE3B,SAAS;EACpB4B,UAAU,EAAEJ,OAAO;EACnBK,YAAY,EAAEvB,eAAe,CAAC,CAAC,CAAC;EAChCwB,cAAc,EAAEC,MAAM;EACtBC,cAAc,EAAEhC,SAAS;EACzBiC,eAAe,EAAEjC;AACnB,CAAC;AACD,IAAMkC,SAAS,GAAGC,MAAM,CAAClB,IAAI,CAAC;AAC9B,IAAImB,aAAa,GAAGxC,eAAe,CAAC;EAClCqB,IAAI,EAAJA,IAAI;EACJoB,KAAK,EAAElB,UAAU;EACjBmB,KAAK,EAAE,CAAC,QAAQ,CAAC;EACjBC,KAAK,WAAAA,MAACF,KAAK,EAAAG,IAAA,EAGR;IAAA,IAFDC,IAAI,GAAAD,IAAA,CAAJC,IAAI;MACJC,KAAK,GAAAF,IAAA,CAALE,KAAK;IAEL,IAAMC,IAAI,GAAGvD,GAAG,CAAC,CAAC;IAClB,IAAMwD,KAAK,GAAGxD,GAAG,CAAC,CAAC;IACnB,IAAMyD,KAAK,GAAGvD,QAAQ,CAAC;MACrBwD,IAAI,EAAE,IAAI;MACVzB,KAAK,EAAE,CAAC;MACRC,MAAM,EAAE,CAAC;MACTyB,MAAM,EAAE,CAAC;MACTC,MAAM,EAAE,CAAC;MACTC,OAAO,EAAE;IACX,CAAC,CAAC;IACF,IAAMC,KAAK,GAAGvC,QAAQ,CAAC,CAAC;IACxB,IAAAwC,YAAA,GAGI3C,WAAW,CAAC0B,SAAS,CAAC;MAFxBkB,QAAQ,GAAAD,YAAA,CAARC,QAAQ;MACRC,YAAY,GAAAF,YAAA,CAAZE,YAAY;IAEd,IAAMC,KAAK,GAAG/D,QAAQ,CAAC;MAAA,OAAM6D,QAAQ,CAACG,MAAM;IAAA,EAAC;IAC7C,IAAMC,IAAI,GAAGjE,QAAQ,CAAC;MAAA,OAAMsD,KAAK,CAACR,KAAK,CAACd,QAAQ,GAAG,QAAQ,GAAG,OAAO,CAAC;IAAA,EAAC;IACvE,IAAMkC,KAAK,GAAGlE,QAAQ,CAAC;MAAA,OAAM8C,KAAK,CAACd,QAAQ,GAAG2B,KAAK,CAACQ,MAAM,CAACC,KAAK,GAAGT,KAAK,CAACU,MAAM,CAACD,KAAK;IAAA,EAAC;IACtF,IAAME,SAAS,GAAGtE,QAAQ,CAAC,YAAM;MAC/B,IAAIsD,KAAK,CAACC,IAAI,EAAE;QACd,IAAMgB,IAAI,GAAGzB,KAAK,CAACd,QAAQ,GAAGsB,KAAK,CAACC,IAAI,CAACxB,MAAM,GAAGuB,KAAK,CAACC,IAAI,CAACzB,KAAK;QAClE,OAAOyC,IAAI,GAAGN,IAAI,CAACG,KAAK,GAAGL,KAAK,CAACK,KAAK;MACxC;MACA,OAAO,CAAC;IACV,CAAC,CAAC;IACF,IAAMI,QAAQ,GAAGxE,QAAQ,CAAC;MAAA,OAAMyE,IAAI,CAACC,IAAI,CAACD,IAAI,CAACE,GAAG,CAACL,SAAS,CAACF,KAAK,CAAC,GAAGH,IAAI,CAACG,KAAK,CAAC;IAAA,EAAC;IAClF,IAAMQ,SAAS,GAAG5E,QAAQ,CAAC;MAAA,OAAM+D,KAAK,CAACK,KAAK,GAAGH,IAAI,CAACG,KAAK;IAAA,EAAC;IAC1D,IAAMS,eAAe,GAAG7E,QAAQ,CAAC;MAAA,OAAM,CAACsD,KAAK,CAACG,MAAM,GAAGM,KAAK,CAACK,KAAK,IAAIL,KAAK,CAACK,KAAK;IAAA,EAAC;IAClF,IAAMU,kBAAkB,GAAG9E,QAAQ,CAAC,YAAM;MACxC,IAAM+E,MAAM,GAAGjC,KAAK,CAACd,QAAQ,GAAG,UAAU,GAAG,YAAY;MACzD,OAAO2B,KAAK,CAACqB,SAAS,CAACZ,KAAK,KAAKW,MAAM;IACzC,CAAC,CAAC;IACF,IAAME,UAAU,GAAGjF,QAAQ,CAAC,YAAM;MAChC,IAAMkF,KAAK,GAAG;QACZC,kBAAkB,KAAAC,MAAA,CAAK9B,KAAK,CAACI,OAAO,GAAG,CAAC,GAAGZ,KAAK,CAACX,QAAQ,OAAI;QAC7DkD,SAAS,cAAAD,MAAA,CAActC,KAAK,CAACd,QAAQ,GAAG,GAAG,GAAG,GAAG,OAAAoD,MAAA,CAAI9B,KAAK,CAACE,MAAM;MACnE,CAAC;MACD,IAAIS,IAAI,CAACG,KAAK,EAAE;QACd,IAAMkB,QAAQ,GAAGxC,KAAK,CAACd,QAAQ,GAAG,QAAQ,GAAG,OAAO;QACpD,IAAMuD,SAAS,GAAGzC,KAAK,CAACd,QAAQ,GAAG,OAAO,GAAG,QAAQ;QACrDkD,KAAK,CAACI,QAAQ,CAAC,MAAAF,MAAA,CAAMR,SAAS,CAACR,KAAK,OAAI;QACxCc,KAAK,CAACK,SAAS,CAAC,GAAGzC,KAAK,CAACyC,SAAS,CAAC,MAAAH,MAAA,CAAMtC,KAAK,CAACyC,SAAS,CAAC,UAAO,EAAE;MACpE;MACA,OAAOL,KAAK;IACd,CAAC,CAAC;IACF,IAAMM,eAAe,GAAG,SAAlBA,eAAeA,CAAIC,IAAI,EAAK;MAChC,IACEhC,MAAM,GACJH,KAAK,CADPG,MAAM;MAER,IAAIgC,IAAI,EAAE;QACR,IAAI3C,KAAK,CAACjB,IAAI,EAAE;UACd,OAAOtB,KAAK,CAACkD,MAAM,GAAGgC,IAAI,EAAE,CAAC,CAAC,EAAE1B,KAAK,CAACK,KAAK,CAAC;QAC9C;QACA,OAAO7D,KAAK,CAACkD,MAAM,GAAGgC,IAAI,EAAE,CAAC,EAAEjB,QAAQ,CAACJ,KAAK,CAAC;MAChD;MACA,OAAOX,MAAM;IACf,CAAC;IACD,IAAMiC,eAAe,GAAG,SAAlBA,eAAeA,CAAIC,YAAY,EAAiB;MAAA,IAAfnC,MAAM,GAAAoC,SAAA,CAAA5B,MAAA,QAAA4B,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,CAAC;MAC/C,IAAIE,eAAe,GAAGH,YAAY,GAAG1B,IAAI,CAACG,KAAK;MAC/C,IAAI,CAACtB,KAAK,CAACjB,IAAI,EAAE;QACfiE,eAAe,GAAGrB,IAAI,CAACsB,GAAG,CAACD,eAAe,EAAE,CAACxB,SAAS,CAACF,KAAK,CAAC;MAC/D;MACA,IAAI4B,YAAY,GAAGxC,MAAM,GAAGsC,eAAe;MAC3C,IAAI,CAAChD,KAAK,CAACjB,IAAI,EAAE;QACfmE,YAAY,GAAGzF,KAAK,CAACyF,YAAY,EAAE1B,SAAS,CAACF,KAAK,EAAE,CAAC,CAAC;MACxD;MACA,OAAO4B,YAAY;IACrB,CAAC;IACD,IAAMC,IAAI,GAAG,SAAPA,IAAIA,CAAAC,KAAA,EAIJ;MAAA,IAAAC,UAAA,GAAAD,KAAA,CAHJT,IAAI;QAAJA,IAAI,GAAAU,UAAA,cAAG,CAAC,GAAAA,UAAA;QAAAC,YAAA,GAAAF,KAAA,CACR1C,MAAM;QAANA,MAAM,GAAA4C,YAAA,cAAG,CAAC,GAAAA,YAAA;QACVC,UAAU,GAAAH,KAAA,CAAVG,UAAU;MAEV,IAAItC,KAAK,CAACK,KAAK,IAAI,CAAC,EAAE;QACpB;MACF;MACA,IACEX,MAAM,GACJH,KAAK,CADPG,MAAM;MAER,IAAMkC,YAAY,GAAGH,eAAe,CAACC,IAAI,CAAC;MAC1C,IAAMO,YAAY,GAAGN,eAAe,CAACC,YAAY,EAAEnC,MAAM,CAAC;MAC1D,IAAIV,KAAK,CAACjB,IAAI,EAAE;QACd,IAAIgC,QAAQ,CAAC,CAAC,CAAC,IAAImC,YAAY,KAAK1B,SAAS,CAACF,KAAK,EAAE;UACnD,IAAMkC,aAAa,GAAGN,YAAY,GAAG1B,SAAS,CAACF,KAAK;UACpDP,QAAQ,CAAC,CAAC,CAAC,CAAC0C,SAAS,CAACD,aAAa,GAAG1B,SAAS,CAACR,KAAK,GAAG,CAAC,CAAC;QAC5D;QACA,IAAIP,QAAQ,CAACE,KAAK,CAACK,KAAK,GAAG,CAAC,CAAC,IAAI4B,YAAY,KAAK,CAAC,EAAE;UACnD,IAAMQ,YAAY,GAAGR,YAAY,GAAG,CAAC;UACrCnC,QAAQ,CAACE,KAAK,CAACK,KAAK,GAAG,CAAC,CAAC,CAACmC,SAAS,CAACC,YAAY,GAAG,CAAC5B,SAAS,CAACR,KAAK,GAAG,CAAC,CAAC;QAC1E;MACF;MACAd,KAAK,CAACG,MAAM,GAAGkC,YAAY;MAC3BrC,KAAK,CAACE,MAAM,GAAGwC,YAAY;MAC3B,IAAIK,UAAU,IAAIV,YAAY,KAAKlC,MAAM,EAAE;QACzCP,IAAI,CAAC,QAAQ,EAAE2B,eAAe,CAACT,KAAK,CAAC;MACvC;IACF,CAAC;IACD,IAAMqC,eAAe,GAAG,SAAlBA,eAAeA,CAAA,EAAS;MAC5BnD,KAAK,CAACI,OAAO,GAAG,IAAI;MACpB,IAAIJ,KAAK,CAACG,MAAM,IAAI,CAAC,CAAC,EAAE;QACtBwC,IAAI,CAAC;UACHR,IAAI,EAAE1B,KAAK,CAACK;QACd,CAAC,CAAC;MACJ,CAAC,MAAM,IAAId,KAAK,CAACG,MAAM,IAAIM,KAAK,CAACK,KAAK,EAAE;QACtC6B,IAAI,CAAC;UACHR,IAAI,EAAE,CAAC1B,KAAK,CAACK;QACf,CAAC,CAAC;MACJ;IACF,CAAC;IACD,IAAMsC,IAAI,GAAG,SAAPA,IAAIA,CAAA,EAAS;MACjBD,eAAe,CAAC,CAAC;MACjB9C,KAAK,CAACgD,KAAK,CAAC,CAAC;MACb3F,SAAS,CAAC,YAAM;QACdsC,KAAK,CAACI,OAAO,GAAG,KAAK;QACrBuC,IAAI,CAAC;UACHR,IAAI,EAAE,CAAC,CAAC;UACRY,UAAU,EAAE;QACd,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC;IACD,IAAMO,IAAI,GAAG,SAAPA,IAAIA,CAAA,EAAS;MACjBH,eAAe,CAAC,CAAC;MACjB9C,KAAK,CAACgD,KAAK,CAAC,CAAC;MACb3F,SAAS,CAAC,YAAM;QACdsC,KAAK,CAACI,OAAO,GAAG,KAAK;QACrBuC,IAAI,CAAC;UACHR,IAAI,EAAE,CAAC;UACPY,UAAU,EAAE;QACd,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC;IACD,IAAIQ,aAAa;IACjB,IAAMC,YAAY,GAAG,SAAfA,YAAYA,CAAA;MAAA,OAASC,YAAY,CAACF,aAAa,CAAC;IAAA;IACtD,IAAM3E,QAAQ,GAAG,SAAXA,QAAQA,CAAA,EAAS;MACrB4E,YAAY,CAAC,CAAC;MACd,IAAIhE,KAAK,CAACZ,QAAQ,GAAG,CAAC,IAAI6B,KAAK,CAACK,KAAK,GAAG,CAAC,EAAE;QACzCyC,aAAa,GAAGG,UAAU,CAAC,YAAM;UAC/BJ,IAAI,CAAC,CAAC;UACN1E,QAAQ,CAAC,CAAC;QACZ,CAAC,EAAE,CAACY,KAAK,CAACZ,QAAQ,CAAC;MACrB;IACF,CAAC;IACD,IAAM+E,UAAU,GAAG,SAAbA,UAAUA,CAAA,EAAqC;MAAA,IAAjCxD,MAAM,GAAAmC,SAAA,CAAA5B,MAAA,QAAA4B,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,CAAC9C,KAAK,CAACR,YAAY;MAC9C,IAAI,CAACc,IAAI,CAACgB,KAAK,EAAE;QACf;MACF;MACA,IAAM8C,EAAE,GAAG,SAALA,EAAEA,CAAA,EAAS;QACf,IAAIC,EAAE,EAAEC,EAAE;QACV,IAAI,CAAC5G,QAAQ,CAAC4C,IAAI,CAAC,EAAE;UACnB,IAAMG,IAAI,GAAG;YACXzB,KAAK,EAAEsB,IAAI,CAACgB,KAAK,CAACiD,WAAW;YAC7BtF,MAAM,EAAEqB,IAAI,CAACgB,KAAK,CAACkD;UACrB,CAAC;UACDhE,KAAK,CAACC,IAAI,GAAGA,IAAI;UACjBD,KAAK,CAACxB,KAAK,GAAG,EAAE,CAACqF,EAAE,GAAGrE,KAAK,CAAChB,KAAK,KAAK,IAAI,GAAGqF,EAAE,GAAG5D,IAAI,CAACzB,KAAK,CAAC;UAC7DwB,KAAK,CAACvB,MAAM,GAAG,EAAE,CAACqF,EAAE,GAAGtE,KAAK,CAACf,MAAM,KAAK,IAAI,GAAGqF,EAAE,GAAG7D,IAAI,CAACxB,MAAM,CAAC;QAClE;QACA,IAAIgC,KAAK,CAACK,KAAK,EAAE;UACfX,MAAM,GAAGgB,IAAI,CAACsB,GAAG,CAAChC,KAAK,CAACK,KAAK,GAAG,CAAC,EAAEX,MAAM,CAAC;QAC5C;QACAH,KAAK,CAACG,MAAM,GAAGA,MAAM;QACrBH,KAAK,CAACI,OAAO,GAAG,IAAI;QACpBJ,KAAK,CAACE,MAAM,GAAGkC,eAAe,CAACjC,MAAM,CAAC;QACtCI,QAAQ,CAAC0D,OAAO,CAAC,UAACC,KAAK,EAAK;UAC1BA,KAAK,CAACjB,SAAS,CAAC,CAAC,CAAC;QACpB,CAAC,CAAC;QACFrE,QAAQ,CAAC,CAAC;MACZ,CAAC;MACD,IAAI1B,QAAQ,CAAC4C,IAAI,CAAC,EAAE;QAClB9C,QAAQ,CAAC,CAAC,CAACmH,IAAI,CAACP,EAAE,CAAC;MACrB,CAAC,MAAM;QACLA,EAAE,CAAC,CAAC;MACN;IACF,CAAC;IACD,IAAMQ,MAAM,GAAG,SAATA,MAAMA,CAAA;MAAA,OAAST,UAAU,CAAC3D,KAAK,CAACG,MAAM,CAAC;IAAA;IAC7C,IAAIkE,cAAc;IAClB,IAAMC,YAAY,GAAG,SAAfA,YAAYA,CAAIC,KAAK,EAAK;MAC9B,IAAI,CAAC/E,KAAK,CAACV,SAAS,EAClB;MACFuB,KAAK,CAACmE,KAAK,CAACD,KAAK,CAAC;MAClBF,cAAc,GAAGI,IAAI,CAACC,GAAG,CAAC,CAAC;MAC3BlB,YAAY,CAAC,CAAC;MACdL,eAAe,CAAC,CAAC;IACnB,CAAC;IACD,IAAMwB,WAAW,GAAG,SAAdA,WAAWA,CAAIJ,KAAK,EAAK;MAC7B,IAAI/E,KAAK,CAACV,SAAS,IAAIkB,KAAK,CAACI,OAAO,EAAE;QACpCC,KAAK,CAACsC,IAAI,CAAC4B,KAAK,CAAC;QACjB,IAAI/C,kBAAkB,CAACV,KAAK,EAAE;UAC5B,IAAM8D,WAAW,GAAG,CAACpF,KAAK,CAACjB,IAAI,KAAKyB,KAAK,CAACG,MAAM,KAAK,CAAC,IAAIS,KAAK,CAACE,KAAK,GAAG,CAAC,IAAId,KAAK,CAACG,MAAM,KAAKM,KAAK,CAACK,KAAK,GAAG,CAAC,IAAIF,KAAK,CAACE,KAAK,GAAG,CAAC,CAAC;UACjI,IAAI,CAAC8D,WAAW,EAAE;YAChBrH,cAAc,CAACgH,KAAK,EAAE/E,KAAK,CAACJ,eAAe,CAAC;YAC5CuD,IAAI,CAAC;cACHzC,MAAM,EAAEU,KAAK,CAACE;YAChB,CAAC,CAAC;UACJ;QACF;MACF;IACF,CAAC;IACD,IAAM+D,UAAU,GAAG,SAAbA,UAAUA,CAAA,EAAS;MACvB,IAAI,CAACrF,KAAK,CAACV,SAAS,IAAI,CAACkB,KAAK,CAACI,OAAO,EAAE;QACtC;MACF;MACA,IAAMvB,QAAQ,GAAG4F,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGL,cAAc;MAC5C,IAAMS,KAAK,GAAGlE,KAAK,CAACE,KAAK,GAAGjC,QAAQ;MACpC,IAAMkG,WAAW,GAAG5D,IAAI,CAACE,GAAG,CAACyD,KAAK,CAAC,GAAG,IAAI,IAAI3D,IAAI,CAACE,GAAG,CAACT,KAAK,CAACE,KAAK,CAAC,GAAGH,IAAI,CAACG,KAAK,GAAG,CAAC;MACpF,IAAIiE,WAAW,IAAIvD,kBAAkB,CAACV,KAAK,EAAE;QAC3C,IAAMZ,MAAM,GAAGV,KAAK,CAACd,QAAQ,GAAG2B,KAAK,CAAC2E,OAAO,CAAClE,KAAK,GAAGT,KAAK,CAAC4E,OAAO,CAACnE,KAAK;QACzE,IAAIqB,IAAI,GAAG,CAAC;QACZ,IAAI3C,KAAK,CAACjB,IAAI,EAAE;UACd4D,IAAI,GAAGjC,MAAM,GAAG,CAAC,GAAGU,KAAK,CAACE,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;QAClD,CAAC,MAAM;UACLqB,IAAI,GAAG,CAAChB,IAAI,CAACP,KAAK,CAACE,KAAK,GAAG,CAAC,GAAG,MAAM,GAAG,OAAO,CAAC,CAACF,KAAK,CAACE,KAAK,GAAGH,IAAI,CAACG,KAAK,CAAC;QAC5E;QACA6B,IAAI,CAAC;UACHR,IAAI,EAAJA,IAAI;UACJY,UAAU,EAAE;QACd,CAAC,CAAC;MACJ,CAAC,MAAM,IAAInC,KAAK,CAACE,KAAK,EAAE;QACtB6B,IAAI,CAAC;UACHR,IAAI,EAAE;QACR,CAAC,CAAC;MACJ;MACAnC,KAAK,CAACI,OAAO,GAAG,KAAK;MACrBxB,QAAQ,CAAC,CAAC;IACZ,CAAC;IACD,IAAMsG,OAAO,GAAG,SAAVA,OAAOA,CAAIC,KAAK,EAAmB;MAAA,IAAjBC,OAAO,GAAA9C,SAAA,CAAA5B,MAAA,QAAA4B,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,CAAC,CAAC;MAClCa,eAAe,CAAC,CAAC;MACjB9C,KAAK,CAACgD,KAAK,CAAC,CAAC;MACb3F,SAAS,CAAC,YAAM;QACd,IAAI2H,WAAW;QACf,IAAI7F,KAAK,CAACjB,IAAI,IAAI4G,KAAK,KAAK1E,KAAK,CAACK,KAAK,EAAE;UACvCuE,WAAW,GAAGrF,KAAK,CAACG,MAAM,KAAK,CAAC,GAAG,CAAC,GAAGgF,KAAK;QAC9C,CAAC,MAAM;UACLE,WAAW,GAAGF,KAAK,GAAG1E,KAAK,CAACK,KAAK;QACnC;QACA,IAAIsE,OAAO,CAACE,SAAS,EAAE;UACrB5H,SAAS,CAAC,YAAM;YACdsC,KAAK,CAACI,OAAO,GAAG,KAAK;UACvB,CAAC,CAAC;QACJ,CAAC,MAAM;UACLJ,KAAK,CAACI,OAAO,GAAG,KAAK;QACvB;QACAuC,IAAI,CAAC;UACHR,IAAI,EAAEkD,WAAW,GAAGrF,KAAK,CAACG,MAAM;UAChC4C,UAAU,EAAE;QACd,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC;IACD,IAAMwC,SAAS,GAAG,SAAZA,SAASA,CAAIC,CAAC,EAAEL,KAAK,EAAK;MAC9B,IAAMhF,MAAM,GAAGgF,KAAK,KAAK5D,eAAe,CAACT,KAAK;MAC9C,IAAMc,KAAK,GAAGzB,MAAM,GAAG;QACrBsF,eAAe,EAAEjG,KAAK,CAACP;MACzB,CAAC,GAAG,KAAK,CAAC;MACV,OAAO3C,YAAY,CAAC,GAAG,EAAE;QACvB,OAAO,EAAEsF,KAAK;QACd,OAAO,EAAEvD,GAAG,CAAC,WAAW,EAAE;UACxB8B,MAAM,EAANA;QACF,CAAC;MACH,CAAC,EAAE,IAAI,CAAC;IACV,CAAC;IACD,IAAMuF,eAAe,GAAG,SAAlBA,eAAeA,CAAA,EAAS;MAC5B,IAAI7F,KAAK,CAAC8F,SAAS,EAAE;QACnB,OAAO9F,KAAK,CAAC8F,SAAS,CAAC;UACrBxF,MAAM,EAAEoB,eAAe,CAACT,KAAK;UAC7B8E,KAAK,EAAEnF,KAAK,CAACK;QACf,CAAC,CAAC;MACJ;MACA,IAAItB,KAAK,CAACL,cAAc,IAAIsB,KAAK,CAACK,KAAK,GAAG,CAAC,EAAE;QAC3C,OAAOxE,YAAY,CAAC,KAAK,EAAE;UACzB,OAAO,EAAE+B,GAAG,CAAC,YAAY,EAAE;YACzBK,QAAQ,EAAEc,KAAK,CAACd;UAClB,CAAC;QACH,CAAC,EAAE,CAACmH,KAAK,CAACpF,KAAK,CAACK,KAAK,CAAC,CAACgF,IAAI,CAAC,EAAE,CAAC,CAACC,GAAG,CAACR,SAAS,CAAC,CAAC,CAAC;MAClD;IACF,CAAC;IACDxH,SAAS,CAAC;MACRqF,IAAI,EAAJA,IAAI;MACJE,IAAI,EAAJA,IAAI;MACJtD,KAAK,EAALA,KAAK;MACLoE,MAAM,EAANA,MAAM;MACNc,OAAO,EAAPA;IACF,CAAC,CAAC;IACF1E,YAAY,CAAC;MACXG,IAAI,EAAJA,IAAI;MACJnB,KAAK,EAALA,KAAK;MACLiB,KAAK,EAALA,KAAK;MACLc,eAAe,EAAfA;IACF,CAAC,CAAC;IACF/E,KAAK,CAAC;MAAA,OAAMgD,KAAK,CAACR,YAAY;IAAA,GAAE,UAAC8B,KAAK;MAAA,OAAK6C,UAAU,CAAC,CAAC7C,KAAK,CAAC;IAAA,EAAC;IAC9DtE,KAAK,CAACiE,KAAK,EAAE;MAAA,OAAMkD,UAAU,CAAC3D,KAAK,CAACG,MAAM,CAAC;IAAA,EAAC;IAC5C3D,KAAK,CAAC;MAAA,OAAMgD,KAAK,CAACZ,QAAQ;IAAA,GAAEA,QAAQ,CAAC;IACrCpC,KAAK,CAAC,CAACa,WAAW,EAAEC,YAAY,CAAC,EAAE8G,MAAM,CAAC;IAC1C5H,KAAK,CAACqB,iBAAiB,CAAC,CAAC,EAAE,UAACmI,OAAO,EAAK;MACtC,IAAIA,OAAO,KAAK,SAAS,EAAE;QACzBpH,QAAQ,CAAC,CAAC;MACZ,CAAC,MAAM;QACL4E,YAAY,CAAC,CAAC;MAChB;IACF,CAAC,CAAC;IACF7G,SAAS,CAACgH,UAAU,CAAC;IACrB/G,WAAW,CAAC;MAAA,OAAM+G,UAAU,CAAC3D,KAAK,CAACG,MAAM,CAAC;IAAA,EAAC;IAC3CnC,aAAa,CAAC;MAAA,OAAM2F,UAAU,CAAC3D,KAAK,CAACG,MAAM,CAAC;IAAA,EAAC;IAC7CtD,aAAa,CAAC2G,YAAY,CAAC;IAC3B1G,eAAe,CAAC0G,YAAY,CAAC;IAC7B5F,gBAAgB,CAAC,WAAW,EAAE+G,WAAW,EAAE;MACzCsB,MAAM,EAAElG;IACV,CAAC,CAAC;IACF,OAAO,YAAM;MACX,IAAI8D,EAAE;MACN,OAAOvH,YAAY,CAAC,KAAK,EAAE;QACzB,KAAK,EAAEwD,IAAI;QACX,OAAO,EAAEzB,GAAG,CAAC;MACf,CAAC,EAAE,CAAC/B,YAAY,CAAC,KAAK,EAAE;QACtB,KAAK,EAAEyD,KAAK;QACZ,OAAO,EAAE4B,UAAU,CAACb,KAAK;QACzB,OAAO,EAAEzC,GAAG,CAAC,OAAO,EAAE;UACpBK,QAAQ,EAAEc,KAAK,CAACd;QAClB,CAAC,CAAC;QACF,qBAAqB,EAAE4F,YAAY;QACnC,YAAY,EAAEO,UAAU;QACxB,eAAe,EAAEA;MACnB,CAAC,EAAE,CAAC,CAAChB,EAAE,GAAGhE,KAAK,CAACqG,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGrC,EAAE,CAACsC,IAAI,CAACtG,KAAK,CAAC,CAAC,CAAC,EAAE6F,eAAe,CAAC,CAAC,CAAC,CAAC;IACnF,CAAC;EACH;AACF,CAAC,CAAC;AACF,SACErG,SAAS,EACTE,aAAa,IAAI2G,OAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}