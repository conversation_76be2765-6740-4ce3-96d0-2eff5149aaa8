{"ast": null, "code": "import { getCurrentInstance } from \"vue\";\nvar current = 0;\nfunction useId() {\n  var vm = getCurrentInstance();\n  var _ref = (vm == null ? void 0 : vm.type) || {},\n    _ref$name = _ref.name,\n    name = _ref$name === void 0 ? \"unknown\" : _ref$name;\n  if (process.env.NODE_ENV === \"test\") {\n    return name;\n  }\n  return \"\".concat(name, \"-\").concat(++current);\n}\nexport { useId };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}