{"ast": null, "code": "function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nfunction _iterableToArrayLimit(arr, i) { var _i = null == arr ? null : \"undefined\" != typeof Symbol && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (null != _i) { var _s, _e, _x, _r, _arr = [], _n = !0, _d = !1; try { if (_x = (_i = _i.call(arr)).next, 0 === i) { if (Object(_i) !== _i) return; _n = !1; } else for (; !(_n = (_s = _x.call(_i)).done) && (_arr.push(_s.value), _arr.length !== i); _n = !0); } catch (err) { _d = !0, _e = err; } finally { try { if (!_n && null != _i.return && (_r = _i.return(), Object(_r) !== _r)) return; } finally { if (_d) throw _e; } } return _arr; } }\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\nimport { mergeProps as _mergeProps, createVNode as _createVNode } from \"vue\";\nimport { defineComponent } from \"vue\";\nimport { addUnit, truthProp, numericProp, getSizeStyle, makeStringProp, makeNumericProp, createNamespace } from \"../utils/index.mjs\";\nvar _createNamespace = createNamespace(\"skeleton\"),\n  _createNamespace2 = _slicedToArray(_createNamespace, 2),\n  name = _createNamespace2[0],\n  bem = _createNamespace2[1];\nvar DEFAULT_ROW_WIDTH = \"100%\";\nvar DEFAULT_LAST_ROW_WIDTH = \"60%\";\nvar skeletonProps = {\n  row: makeNumericProp(0),\n  title: Boolean,\n  round: Boolean,\n  avatar: Boolean,\n  loading: truthProp,\n  animate: truthProp,\n  avatarSize: numericProp,\n  titleWidth: numericProp,\n  avatarShape: makeStringProp(\"round\"),\n  rowWidth: {\n    type: [Number, String, Array],\n    default: DEFAULT_ROW_WIDTH\n  }\n};\nvar stdin_default = defineComponent({\n  name: name,\n  inheritAttrs: false,\n  props: skeletonProps,\n  setup: function setup(props, _ref) {\n    var slots = _ref.slots,\n      attrs = _ref.attrs;\n    var renderAvatar = function renderAvatar() {\n      if (props.avatar) {\n        return _createVNode(\"div\", {\n          \"class\": bem(\"avatar\", props.avatarShape),\n          \"style\": getSizeStyle(props.avatarSize)\n        }, null);\n      }\n    };\n    var renderTitle = function renderTitle() {\n      if (props.title) {\n        return _createVNode(\"h3\", {\n          \"class\": bem(\"title\"),\n          \"style\": {\n            width: addUnit(props.titleWidth)\n          }\n        }, null);\n      }\n    };\n    var getRowWidth = function getRowWidth(index) {\n      var rowWidth = props.rowWidth;\n      if (rowWidth === DEFAULT_ROW_WIDTH && index === +props.row - 1) {\n        return DEFAULT_LAST_ROW_WIDTH;\n      }\n      if (Array.isArray(rowWidth)) {\n        return rowWidth[index];\n      }\n      return rowWidth;\n    };\n    var renderRows = function renderRows() {\n      return Array(+props.row).fill(\"\").map(function (_, i) {\n        return _createVNode(\"div\", {\n          \"class\": bem(\"row\"),\n          \"style\": {\n            width: addUnit(getRowWidth(i))\n          }\n        }, null);\n      });\n    };\n    return function () {\n      var _a;\n      if (!props.loading) {\n        return (_a = slots.default) == null ? void 0 : _a.call(slots);\n      }\n      return _createVNode(\"div\", _mergeProps({\n        \"class\": bem({\n          animate: props.animate,\n          round: props.round\n        })\n      }, attrs), [renderAvatar(), _createVNode(\"div\", {\n        \"class\": bem(\"content\")\n      }, [renderTitle(), renderRows()])]);\n    };\n  }\n});\nexport { stdin_default as default };", "map": {"version": 3, "names": ["mergeProps", "_mergeProps", "createVNode", "_createVNode", "defineComponent", "addUnit", "truthProp", "numericProp", "getSizeStyle", "makeStringProp", "makeNumericProp", "createNamespace", "_createNamespace", "_createNamespace2", "_slicedToArray", "name", "bem", "DEFAULT_ROW_WIDTH", "DEFAULT_LAST_ROW_WIDTH", "skeletonProps", "row", "title", "Boolean", "round", "avatar", "loading", "animate", "avatarSize", "titleWidth", "avatar<PERSON><PERSON><PERSON>", "row<PERSON>id<PERSON>", "type", "Number", "String", "Array", "default", "stdin_default", "inheritAttrs", "props", "setup", "_ref", "slots", "attrs", "renderAvat<PERSON>", "renderTitle", "width", "getRowWidth", "index", "isArray", "renderRows", "fill", "map", "_", "i", "_a", "call"], "sources": ["C:/Users/<USER>/Downloads/Telegram Desktop/vue3_3.0/vue3_3.0/node_modules/vant/es/skeleton/Skeleton.mjs"], "sourcesContent": ["import { mergeProps as _mergeProps, createVNode as _createVNode } from \"vue\";\nimport { defineComponent } from \"vue\";\nimport { addUnit, truthProp, numericProp, getSizeStyle, makeStringProp, makeNumericProp, createNamespace } from \"../utils/index.mjs\";\nconst [name, bem] = createNamespace(\"skeleton\");\nconst DEFAULT_ROW_WIDTH = \"100%\";\nconst DEFAULT_LAST_ROW_WIDTH = \"60%\";\nconst skeletonProps = {\n  row: makeNumericProp(0),\n  title: <PERSON><PERSON><PERSON>,\n  round: Boolean,\n  avatar: Boolean,\n  loading: truthProp,\n  animate: truthProp,\n  avatarSize: numericProp,\n  titleWidth: numericProp,\n  avatarShape: makeStringProp(\"round\"),\n  rowWidth: {\n    type: [Number, String, Array],\n    default: DEFAULT_ROW_WIDTH\n  }\n};\nvar stdin_default = defineComponent({\n  name,\n  inheritAttrs: false,\n  props: skeletonProps,\n  setup(props, {\n    slots,\n    attrs\n  }) {\n    const renderAvatar = () => {\n      if (props.avatar) {\n        return _createVNode(\"div\", {\n          \"class\": bem(\"avatar\", props.avatarShape),\n          \"style\": getSizeStyle(props.avatarSize)\n        }, null);\n      }\n    };\n    const renderTitle = () => {\n      if (props.title) {\n        return _createVNode(\"h3\", {\n          \"class\": bem(\"title\"),\n          \"style\": {\n            width: addUnit(props.titleWidth)\n          }\n        }, null);\n      }\n    };\n    const getRowWidth = (index) => {\n      const {\n        rowWidth\n      } = props;\n      if (rowWidth === DEFAULT_ROW_WIDTH && index === +props.row - 1) {\n        return DEFAULT_LAST_ROW_WIDTH;\n      }\n      if (Array.isArray(rowWidth)) {\n        return rowWidth[index];\n      }\n      return rowWidth;\n    };\n    const renderRows = () => Array(+props.row).fill(\"\").map((_, i) => _createVNode(\"div\", {\n      \"class\": bem(\"row\"),\n      \"style\": {\n        width: addUnit(getRowWidth(i))\n      }\n    }, null));\n    return () => {\n      var _a;\n      if (!props.loading) {\n        return (_a = slots.default) == null ? void 0 : _a.call(slots);\n      }\n      return _createVNode(\"div\", _mergeProps({\n        \"class\": bem({\n          animate: props.animate,\n          round: props.round\n        })\n      }, attrs), [renderAvatar(), _createVNode(\"div\", {\n        \"class\": bem(\"content\")\n      }, [renderTitle(), renderRows()])]);\n    };\n  }\n});\nexport {\n  stdin_default as default\n};\n"], "mappings": ";;;;;;AAAA,SAASA,UAAU,IAAIC,WAAW,EAAEC,WAAW,IAAIC,YAAY,QAAQ,KAAK;AAC5E,SAASC,eAAe,QAAQ,KAAK;AACrC,SAASC,OAAO,EAAEC,SAAS,EAAEC,WAAW,EAAEC,YAAY,EAAEC,cAAc,EAAEC,eAAe,EAAEC,eAAe,QAAQ,oBAAoB;AACpI,IAAAC,gBAAA,GAAoBD,eAAe,CAAC,UAAU,CAAC;EAAAE,iBAAA,GAAAC,cAAA,CAAAF,gBAAA;EAAxCG,IAAI,GAAAF,iBAAA;EAAEG,GAAG,GAAAH,iBAAA;AAChB,IAAMI,iBAAiB,GAAG,MAAM;AAChC,IAAMC,sBAAsB,GAAG,KAAK;AACpC,IAAMC,aAAa,GAAG;EACpBC,GAAG,EAAEV,eAAe,CAAC,CAAC,CAAC;EACvBW,KAAK,EAAEC,OAAO;EACdC,KAAK,EAAED,OAAO;EACdE,MAAM,EAAEF,OAAO;EACfG,OAAO,EAAEnB,SAAS;EAClBoB,OAAO,EAAEpB,SAAS;EAClBqB,UAAU,EAAEpB,WAAW;EACvBqB,UAAU,EAAErB,WAAW;EACvBsB,WAAW,EAAEpB,cAAc,CAAC,OAAO,CAAC;EACpCqB,QAAQ,EAAE;IACRC,IAAI,EAAE,CAACC,MAAM,EAAEC,MAAM,EAAEC,KAAK,CAAC;IAC7BC,OAAO,EAAElB;EACX;AACF,CAAC;AACD,IAAImB,aAAa,GAAGhC,eAAe,CAAC;EAClCW,IAAI,EAAJA,IAAI;EACJsB,YAAY,EAAE,KAAK;EACnBC,KAAK,EAAEnB,aAAa;EACpBoB,KAAK,WAAAA,MAACD,KAAK,EAAAE,IAAA,EAGR;IAAA,IAFDC,KAAK,GAAAD,IAAA,CAALC,KAAK;MACLC,KAAK,GAAAF,IAAA,CAALE,KAAK;IAEL,IAAMC,YAAY,GAAG,SAAfA,YAAYA,CAAA,EAAS;MACzB,IAAIL,KAAK,CAACd,MAAM,EAAE;QAChB,OAAOrB,YAAY,CAAC,KAAK,EAAE;UACzB,OAAO,EAAEa,GAAG,CAAC,QAAQ,EAAEsB,KAAK,CAACT,WAAW,CAAC;UACzC,OAAO,EAAErB,YAAY,CAAC8B,KAAK,CAACX,UAAU;QACxC,CAAC,EAAE,IAAI,CAAC;MACV;IACF,CAAC;IACD,IAAMiB,WAAW,GAAG,SAAdA,WAAWA,CAAA,EAAS;MACxB,IAAIN,KAAK,CAACjB,KAAK,EAAE;QACf,OAAOlB,YAAY,CAAC,IAAI,EAAE;UACxB,OAAO,EAAEa,GAAG,CAAC,OAAO,CAAC;UACrB,OAAO,EAAE;YACP6B,KAAK,EAAExC,OAAO,CAACiC,KAAK,CAACV,UAAU;UACjC;QACF,CAAC,EAAE,IAAI,CAAC;MACV;IACF,CAAC;IACD,IAAMkB,WAAW,GAAG,SAAdA,WAAWA,CAAIC,KAAK,EAAK;MAC7B,IACEjB,QAAQ,GACNQ,KAAK,CADPR,QAAQ;MAEV,IAAIA,QAAQ,KAAKb,iBAAiB,IAAI8B,KAAK,KAAK,CAACT,KAAK,CAAClB,GAAG,GAAG,CAAC,EAAE;QAC9D,OAAOF,sBAAsB;MAC/B;MACA,IAAIgB,KAAK,CAACc,OAAO,CAAClB,QAAQ,CAAC,EAAE;QAC3B,OAAOA,QAAQ,CAACiB,KAAK,CAAC;MACxB;MACA,OAAOjB,QAAQ;IACjB,CAAC;IACD,IAAMmB,UAAU,GAAG,SAAbA,UAAUA,CAAA;MAAA,OAASf,KAAK,CAAC,CAACI,KAAK,CAAClB,GAAG,CAAC,CAAC8B,IAAI,CAAC,EAAE,CAAC,CAACC,GAAG,CAAC,UAACC,CAAC,EAAEC,CAAC;QAAA,OAAKlD,YAAY,CAAC,KAAK,EAAE;UACpF,OAAO,EAAEa,GAAG,CAAC,KAAK,CAAC;UACnB,OAAO,EAAE;YACP6B,KAAK,EAAExC,OAAO,CAACyC,WAAW,CAACO,CAAC,CAAC;UAC/B;QACF,CAAC,EAAE,IAAI,CAAC;MAAA,EAAC;IAAA;IACT,OAAO,YAAM;MACX,IAAIC,EAAE;MACN,IAAI,CAAChB,KAAK,CAACb,OAAO,EAAE;QAClB,OAAO,CAAC6B,EAAE,GAAGb,KAAK,CAACN,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGmB,EAAE,CAACC,IAAI,CAACd,KAAK,CAAC;MAC/D;MACA,OAAOtC,YAAY,CAAC,KAAK,EAAEF,WAAW,CAAC;QACrC,OAAO,EAAEe,GAAG,CAAC;UACXU,OAAO,EAAEY,KAAK,CAACZ,OAAO;UACtBH,KAAK,EAAEe,KAAK,CAACf;QACf,CAAC;MACH,CAAC,EAAEmB,KAAK,CAAC,EAAE,CAACC,YAAY,CAAC,CAAC,EAAExC,YAAY,CAAC,KAAK,EAAE;QAC9C,OAAO,EAAEa,GAAG,CAAC,SAAS;MACxB,CAAC,EAAE,CAAC4B,WAAW,CAAC,CAAC,EAAEK,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrC,CAAC;EACH;AACF,CAAC,CAAC;AACF,SACEb,aAAa,IAAID,OAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}