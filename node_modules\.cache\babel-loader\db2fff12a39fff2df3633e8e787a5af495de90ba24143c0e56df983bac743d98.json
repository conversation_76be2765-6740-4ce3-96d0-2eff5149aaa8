{"ast": null, "code": "var supported;\nvar perf;\nexport function isPerformanceSupported() {\n  var _a;\n  if (supported !== undefined) {\n    return supported;\n  }\n  if (typeof window !== 'undefined' && window.performance) {\n    supported = true;\n    perf = window.performance;\n  } else if (typeof global !== 'undefined' && ((_a = global.perf_hooks) === null || _a === void 0 ? void 0 : _a.performance)) {\n    supported = true;\n    perf = global.perf_hooks.performance;\n  } else {\n    supported = false;\n  }\n  return supported;\n}\nexport function now() {\n  return isPerformanceSupported() ? perf.now() : Date.now();\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}