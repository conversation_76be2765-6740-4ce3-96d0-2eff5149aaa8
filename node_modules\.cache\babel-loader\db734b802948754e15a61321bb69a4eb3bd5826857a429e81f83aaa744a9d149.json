{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _Tag from \"./Tag.mjs\";\nvar Tag = withInstall(_Tag);\nvar stdin_default = Tag;\nexport { Tag, stdin_default as default };", "map": {"version": 3, "names": ["withInstall", "_Tag", "Tag", "stdin_default", "default"], "sources": ["C:/Users/<USER>/Downloads/Telegram Desktop/vue3_3.0/vue3_3.0/node_modules/vant/es/tag/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _Tag from \"./Tag.mjs\";\nconst Tag = withInstall(_Tag);\nvar stdin_default = Tag;\nexport {\n  Tag,\n  stdin_default as default\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,IAAI,MAAM,WAAW;AAC5B,IAAMC,GAAG,GAAGF,WAAW,CAACC,IAAI,CAAC;AAC7B,IAAIE,aAAa,GAAGD,GAAG;AACvB,SACEA,GAAG,EACHC,aAAa,IAAIC,OAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}