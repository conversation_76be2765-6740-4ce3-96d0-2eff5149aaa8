{"ast": null, "code": "function _createForOfIteratorHelper(o, allowArrayLike) { var it = typeof Symbol !== \"undefined\" && o[Symbol.iterator] || o[\"@@iterator\"]; if (!it) { if (Array.isArray(o) || (it = _unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === \"number\") { if (it) o = it; var i = 0; var F = function F() {}; return { s: F, n: function n() { if (i >= o.length) return { done: true }; return { done: false, value: o[i++] }; }, e: function e(_e) { throw _e; }, f: F }; } throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); } var normalCompletion = true, didErr = false, err; return { s: function s() { it = it.call(o); }, n: function n() { var step = it.next(); normalCompletion = step.done; return step; }, e: function e(_e2) { didErr = true; err = _e2; }, f: function f() { try { if (!normalCompletion && it.return != null) it.return(); } finally { if (didErr) throw err; } } }; }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nfunction _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __export = function __export(target, all) {\n  for (var name in all) __defProp(target, name, {\n    get: all[name],\n    enumerable: true\n  });\n};\nvar __copyProps = function __copyProps(to, from, except, desc) {\n  if (from && _typeof(from) === \"object\" || typeof from === \"function\") {\n    var _iterator = _createForOfIteratorHelper(__getOwnPropNames(from)),\n      _step;\n    try {\n      var _loop = function _loop() {\n        var key = _step.value;\n        if (!__hasOwnProp.call(to, key) && key !== except) __defProp(to, key, {\n          get: function get() {\n            return from[key];\n          },\n          enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable\n        });\n      };\n      for (_iterator.s(); !(_step = _iterator.n()).done;) {\n        _loop();\n      }\n    } catch (err) {\n      _iterator.e(err);\n    } finally {\n      _iterator.f();\n    }\n  }\n  return to;\n};\nvar __toCommonJS = function __toCommonJS(mod) {\n  return __copyProps(__defProp({}, \"__esModule\", {\n    value: true\n  }), mod);\n};\nvar stdin_exports = {};\n__export(stdin_exports, {\n  default: function _default() {\n    return stdin_default;\n  }\n});\nmodule.exports = __toCommonJS(stdin_exports);\nvar stdin_default = {\n  name: \"\\u09A8\\u09BE\\u09AE\",\n  tel: \"\\u09AB\\u09CB\\u09A8\",\n  save: \"\\u09B8\\u0982\\u09B0\\u0995\\u09CD\\u09B7\\u09A3 \\u0995\\u09B0\\u09C1\\u09A8\",\n  confirm: \"\\u09A8\\u09BF\\u09B6\\u09CD\\u099A\\u09BF\\u09A4 \\u0995\\u09B0\\u09C1\\u09A8\",\n  cancel: \"\\u09AC\\u09BE\\u09A4\\u09BF\\u09B2\",\n  delete: \"\\u09AE\\u09C1\\u099B\\u09C1\\u09A8\",\n  loading: \"\\u09B2\\u09CB\\u09A1 \\u09B9\\u099A\\u09CD\\u099B\\u09C7...\",\n  noCoupon: \"\\u0995\\u09CB\\u09A8 \\u0995\\u09C1\\u09AA\\u09A8 \\u09A8\\u09C7\\u0987\",\n  nameEmpty: \"\\u0985\\u09A8\\u09C1\\u0997\\u09CD\\u09B0\\u09B9 \\u0995\\u09B0\\u09C7 \\u09A8\\u09BE\\u09AE\\u099F\\u09BF \\u09AA\\u09C2\\u09B0\\u09A3 \\u0995\\u09B0\\u09C1\\u09A8\",\n  addContact: \"\\u09AF\\u09CB\\u0997\\u09BE\\u09AF\\u09CB\\u0997 \\u09AF\\u09CB\\u0997 \\u0995\\u09B0\\u09C1\\u09A8\",\n  telInvalid: \"\\u09AC\\u09BF\\u0995\\u09C3\\u09A4 \\u09AB\\u09CB\\u09A8 \\u09A8\\u09AE\\u09CD\\u09AC\\u09B0\",\n  vanCalendar: {\n    end: \"\\u09B6\\u09C7\\u09B7\",\n    start: \"\\u09B6\\u09C1\\u09B0\\u09C1\",\n    title: \"\\u0995\\u09CD\\u09AF\\u09BE\\u09B2\\u09C7\\u09A8\\u09CD\\u09A1\\u09BE\\u09B0\",\n    weekdays: [\"\\u09B0\\u09AC\\u09BF\\u09AC\\u09BE\\u09B0\", \"\\u09B8\\u09CB\\u09AE\\u09AC\\u09BE\\u09B0\", \"\\u09AE\\u0999\\u09CD\\u0997\\u09B2\\u09AC\\u09BE\\u09B0\", \"\\u09AC\\u09C1\\u09A7\\u09AC\\u09BE\\u09B0\", \"\\u09AC\\u09C3\\u09B9\\u09B8\\u09CD\\u09AA\\u09A4\\u09BF\\u09AC\\u09BE\\u09B0\", \"\\u09B6\\u09C1\\u0995\\u09CD\\u09B0\\u09AC\\u09BE\\u09B0\", \"\\u09B6\\u09A8\\u09BF\\u09AC\\u09BE\\u09B0\"],\n    monthTitle: function monthTitle(year, month) {\n      return \"\".concat(year, \"/\").concat(month);\n    },\n    rangePrompt: function rangePrompt(maxRange) {\n      return \"\".concat(maxRange, \" \\u09A6\\u09BF\\u09A8\\u09C7\\u09B0 \\u09AC\\u09C7\\u09B6\\u09BF \\u09A8\\u09BF\\u09B0\\u09CD\\u09AC\\u09BE\\u099A\\u09A8 \\u0995\\u09B0\\u09AC\\u09C7\\u09A8 \\u09A8\\u09BE\");\n    }\n  },\n  vanCascader: {\n    select: \"\\u09A8\\u09BF\\u09B0\\u09CD\\u09AC\\u09BE\\u099A\\u09A8\"\n  },\n  vanPagination: {\n    prev: \"\\u09AA\\u09C2\\u09B0\\u09CD\\u09AC\\u09AC\\u09B0\\u09CD\\u09A4\\u09C0\",\n    next: \"\\u09AA\\u09B0\\u09AC\\u09B0\\u09CD\\u09A4\\u09C0\"\n  },\n  vanPullRefresh: {\n    pulling: \"\\u09B0\\u09BF\\u09AB\\u09CD\\u09B0\\u09C7\\u09B6 \\u0995\\u09B0\\u09A4\\u09C7 \\u099F\\u09BE\\u09A8\\u09C1\\u09A8...\",\n    loosing: \"\\u09B0\\u09BF\\u09AB\\u09CD\\u09B0\\u09C7\\u09B6 \\u0995\\u09B0\\u09A4\\u09C7 \\u0986\\u09B2\\u0997\\u09BE...\"\n  },\n  vanSubmitBar: {\n    label: \"\\u09AE\\u09CB\\u099F:\"\n  },\n  vanCoupon: {\n    unlimited: \"\\u0986\\u09A8\\u09B2\\u09BF\\u09AE\\u09BF\\u099F\\u09C7\\u09A1\",\n    discount: function discount(_discount) {\n      return \"\".concat(_discount * 10, \"% \\u099B\\u09BE\\u09A1\\u09BC\");\n    },\n    condition: function condition(_condition) {\n      return \"\\u0985\\u09A8\\u09CD\\u09A4\\u09A4 \".concat(_condition);\n    }\n  },\n  vanCouponCell: {\n    title: \"\\u0995\\u09C1\\u09AA\\u09A8\",\n    count: function count(_count) {\n      return \"\\u0986\\u09AA\\u09A8\\u09BE\\u09B0 \".concat(_count, \" \\u0995\\u09C1\\u09AA\\u09A8 \\u0986\\u099B\\u09C7\");\n    }\n  },\n  vanCouponList: {\n    exchange: \"\\u09AC\\u09BF\\u09A8\\u09BF\\u09AE\\u09AF\\u09BC\",\n    close: \"\\u09AC\\u09A8\\u09CD\\u09A7\",\n    enable: \"\\u0989\\u09AA\\u09B2\\u09AD\\u09CD\\u09AF\",\n    disabled: \"\\u0985\\u09A8\\u09C1\\u09AA\\u09B2\\u09AC\\u09CD\\u09A7\",\n    placeholder: \"\\u0995\\u09C1\\u09AA\\u09A8 \\u0995\\u09CB\\u09A1\"\n  },\n  vanAddressEdit: {\n    area: \"\\u098F\\u09B0\\u09BF\\u09AF\\u09BC\\u09BE\",\n    postal: \"\\u09A1\\u09BE\\u0995\",\n    areaEmpty: \"\\u0985\\u09A8\\u09C1\\u0997\\u09CD\\u09B0\\u09B9 \\u0995\\u09B0\\u09C7 \\u098F\\u0995\\u099F\\u09BF \\u09B0\\u09BF\\u09B8\\u09BF\\u09AD\\u09BF\\u0982 \\u098F\\u09B2\\u09BE\\u0995\\u09BE \\u09A8\\u09BF\\u09B0\\u09CD\\u09AC\\u09BE\\u099A\\u09A8 \\u0995\\u09B0\\u09C1\\u09A8\",\n    addressEmpty: \"\\u09A0\\u09BF\\u0995\\u09BE\\u09A8\\u09BE \\u0996\\u09BE\\u09B2\\u09BF \\u09B9\\u09A4\\u09C7 \\u09AA\\u09BE\\u09B0\\u09C7 \\u09A8\\u09BE\",\n    postalEmpty: \"\\u09AD\\u09C1\\u09B2 \\u09AA\\u09CB\\u09B8\\u09CD\\u099F\\u09BE\\u09B2 \\u0995\\u09CB\\u09A1\",\n    addressDetail: \"\\u09A0\\u09BF\\u0995\\u09BE\\u09A8\\u09BE\",\n    defaultAddress: \"\\u09A1\\u09BF\\u09AB\\u09B2\\u09CD\\u099F \\u09A0\\u09BF\\u0995\\u09BE\\u09A8\\u09BE \\u09B9\\u09BF\\u09B8\\u09BE\\u09AC\\u09C7 \\u09B8\\u09C7\\u099F \\u0995\\u09B0\\u09C1\\u09A8\"\n  },\n  vanAddressList: {\n    add: \"\\u09A8\\u09A4\\u09C1\\u09A8 \\u09A0\\u09BF\\u0995\\u09BE\\u09A8\\u09BE \\u09AF\\u09CB\\u0997 \\u0995\\u09B0\\u09C1\\u09A8\"\n  }\n};", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}