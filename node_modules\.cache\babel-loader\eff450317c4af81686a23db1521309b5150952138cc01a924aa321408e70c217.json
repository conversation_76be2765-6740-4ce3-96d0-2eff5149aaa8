{"ast": null, "code": "import { createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, toDisplayString as _toDisplayString, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, createTextVNode as _createTextVNode, pushScopeId as _pushScopeId, popScopeId as _popScopeId } from \"vue\";\nvar _withScopeId = function _withScopeId(n) {\n  return _pushScopeId(\"data-v-9be86664\"), n = n(), _popScopeId(), n;\n};\nvar _hoisted_1 = {\n  class: \"tel\"\n};\nvar _hoisted_2 = [\"src\"];\nvar _hoisted_3 = {\n  class: \"bg\"\n};\nvar _hoisted_4 = {\n  class: \"span\"\n};\nvar _hoisted_5 = {\n  class: \"tent\"\n};\nvar _hoisted_6 = {\n  class: \"right\"\n};\nvar _hoisted_7 = {\n  class: \"flex\"\n};\nvar _hoisted_8 = {\n  class: \"title\"\n};\nvar _hoisted_9 = {\n  class: \"time\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_van_nav_bar = _resolveComponent(\"van-nav-bar\");\n  var _component_van_button = _resolveComponent(\"van-button\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_van_nav_bar, {\n    onClickRight: $setup.clickRight\n  }, {\n    right: _withCtx(function () {\n      return [_createElementVNode(\"img\", {\n        src: require('@/assets/images/news/msg3.png'),\n        width: \"26.5\",\n        alt: \"\"\n      }, null, 8, _hoisted_2)];\n    }),\n    _: 1\n  }, 8, [\"onClickRight\"]), _createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"span\", _hoisted_4, _toDisplayString(_ctx.$t('msg.kffw')), 1)]), _createElementVNode(\"div\", _hoisted_5, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.list, function (item, index) {\n    return _openBlock(), _createElementBlock(\"div\", {\n      class: \"box\",\n      key: index\n    }, [_createElementVNode(\"div\", _hoisted_6, [_createElementVNode(\"div\", _hoisted_7, [_createElementVNode(\"div\", _hoisted_8, _toDisplayString(item.username), 1), _createElementVNode(\"div\", _hoisted_9, _toDisplayString(item.btime) + \"——\" + _toDisplayString(item.etime), 1), _createVNode(_component_van_button, {\n      round: \"\",\n      block: \"\",\n      type: \"primary\",\n      onClick: function onClick($event) {\n        return $setup.tel(item);\n      }\n    }, {\n      default: _withCtx(function () {\n        return [_createTextVNode(_toDisplayString(_ctx.$t('msg.ljzx')), 1)];\n      }),\n      _: 2\n    }, 1032, [\"onClick\"])])])]);\n  }), 128))])]);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}