{"ast": null, "code": "function _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return _typeof(key) === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (_typeof(input) !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (_typeof(res) !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\nfunction _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nfunction _iterableToArrayLimit(arr, i) { var _i = null == arr ? null : \"undefined\" != typeof Symbol && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (null != _i) { var _s, _e, _x, _r, _arr = [], _n = !0, _d = !1; try { if (_x = (_i = _i.call(arr)).next, 0 === i) { if (Object(_i) !== _i) return; _n = !1; } else for (; !(_n = (_s = _x.call(_i)).done) && (_arr.push(_s.value), _arr.length !== i); _n = !0); } catch (err) { _d = !0, _e = err; } finally { try { if (!_n && null != _i.return && (_r = _i.return(), Object(_r) !== _r)) return; } finally { if (_d) throw _e; } } return _arr; } }\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\nimport { createVNode as _createVNode } from \"vue\";\nimport { defineComponent } from \"vue\";\nimport { addUnit, truthProp, numericProp, BORDER_LEFT, makeStringProp, BORDER_SURROUND, createNamespace, makeNumericProp } from \"../utils/index.mjs\";\nvar _createNamespace = createNamespace(\"password-input\"),\n  _createNamespace2 = _slicedToArray(_createNamespace, 2),\n  name = _createNamespace2[0],\n  bem = _createNamespace2[1];\nvar passwordInputProps = {\n  info: String,\n  mask: truthProp,\n  value: makeStringProp(\"\"),\n  gutter: numericProp,\n  length: makeNumericProp(6),\n  focused: Boolean,\n  errorInfo: String\n};\nvar stdin_default = defineComponent({\n  name: name,\n  props: passwordInputProps,\n  emits: [\"focus\"],\n  setup: function setup(props, _ref) {\n    var emit = _ref.emit;\n    var onTouchStart = function onTouchStart(event) {\n      event.stopPropagation();\n      emit(\"focus\", event);\n    };\n    var renderPoints = function renderPoints() {\n      var Points = [];\n      var mask = props.mask,\n        value = props.value,\n        length = props.length,\n        gutter = props.gutter,\n        focused = props.focused;\n      for (var i = 0; i < length; i++) {\n        var _char = value[i];\n        var showBorder = i !== 0 && !gutter;\n        var showCursor = focused && i === value.length;\n        var style = void 0;\n        if (i !== 0 && gutter) {\n          style = {\n            marginLeft: addUnit(gutter)\n          };\n        }\n        Points.push(_createVNode(\"li\", {\n          \"class\": [_defineProperty({}, BORDER_LEFT, showBorder), bem(\"item\", {\n            focus: showCursor\n          })],\n          \"style\": style\n        }, [mask ? _createVNode(\"i\", {\n          \"style\": {\n            visibility: _char ? \"visible\" : \"hidden\"\n          }\n        }, null) : _char, showCursor && _createVNode(\"div\", {\n          \"class\": bem(\"cursor\")\n        }, null)]));\n      }\n      return Points;\n    };\n    return function () {\n      var info = props.errorInfo || props.info;\n      return _createVNode(\"div\", {\n        \"class\": bem()\n      }, [_createVNode(\"ul\", {\n        \"class\": [bem(\"security\"), _defineProperty({}, BORDER_SURROUND, !props.gutter)],\n        \"onTouchstartPassive\": onTouchStart\n      }, [renderPoints()]), info && _createVNode(\"div\", {\n        \"class\": bem(props.errorInfo ? \"error-info\" : \"info\")\n      }, [info])]);\n    };\n  }\n});\nexport { stdin_default as default };", "map": {"version": 3, "names": ["createVNode", "_createVNode", "defineComponent", "addUnit", "truthProp", "numericProp", "BORDER_LEFT", "makeStringProp", "BORDER_SURROUND", "createNamespace", "makeNumericProp", "_createNamespace", "_createNamespace2", "_slicedToArray", "name", "bem", "passwordInputProps", "info", "String", "mask", "value", "gutter", "length", "focused", "Boolean", "errorInfo", "stdin_default", "props", "emits", "setup", "_ref", "emit", "onTouchStart", "event", "stopPropagation", "renderPoints", "Points", "i", "char", "showBorder", "showCursor", "style", "marginLeft", "push", "_defineProperty", "focus", "visibility", "default"], "sources": ["C:/Users/<USER>/Downloads/Telegram Desktop/vue3_3.0/vue3_3.0/node_modules/vant/es/password-input/PasswordInput.mjs"], "sourcesContent": ["import { createVNode as _createVNode } from \"vue\";\nimport { defineComponent } from \"vue\";\nimport { addUnit, truthProp, numericProp, BORDER_LEFT, makeStringProp, BORDER_SURROUND, createNamespace, makeNumericProp } from \"../utils/index.mjs\";\nconst [name, bem] = createNamespace(\"password-input\");\nconst passwordInputProps = {\n  info: String,\n  mask: truthProp,\n  value: makeStringProp(\"\"),\n  gutter: numericProp,\n  length: makeNumericProp(6),\n  focused: Boolean,\n  errorInfo: String\n};\nvar stdin_default = defineComponent({\n  name,\n  props: passwordInputProps,\n  emits: [\"focus\"],\n  setup(props, {\n    emit\n  }) {\n    const onTouchStart = (event) => {\n      event.stopPropagation();\n      emit(\"focus\", event);\n    };\n    const renderPoints = () => {\n      const Points = [];\n      const {\n        mask,\n        value,\n        length,\n        gutter,\n        focused\n      } = props;\n      for (let i = 0; i < length; i++) {\n        const char = value[i];\n        const showBorder = i !== 0 && !gutter;\n        const showCursor = focused && i === value.length;\n        let style;\n        if (i !== 0 && gutter) {\n          style = {\n            marginLeft: addUnit(gutter)\n          };\n        }\n        Points.push(_createVNode(\"li\", {\n          \"class\": [{\n            [BORDER_LEFT]: showBorder\n          }, bem(\"item\", {\n            focus: showCursor\n          })],\n          \"style\": style\n        }, [mask ? _createVNode(\"i\", {\n          \"style\": {\n            visibility: char ? \"visible\" : \"hidden\"\n          }\n        }, null) : char, showCursor && _createVNode(\"div\", {\n          \"class\": bem(\"cursor\")\n        }, null)]));\n      }\n      return Points;\n    };\n    return () => {\n      const info = props.errorInfo || props.info;\n      return _createVNode(\"div\", {\n        \"class\": bem()\n      }, [_createVNode(\"ul\", {\n        \"class\": [bem(\"security\"), {\n          [BORDER_SURROUND]: !props.gutter\n        }],\n        \"onTouchstartPassive\": onTouchStart\n      }, [renderPoints()]), info && _createVNode(\"div\", {\n        \"class\": bem(props.errorInfo ? \"error-info\" : \"info\")\n      }, [info])]);\n    };\n  }\n});\nexport {\n  stdin_default as default\n};\n"], "mappings": ";;;;;;;;;;AAAA,SAASA,WAAW,IAAIC,YAAY,QAAQ,KAAK;AACjD,SAASC,eAAe,QAAQ,KAAK;AACrC,SAASC,OAAO,EAAEC,SAAS,EAAEC,WAAW,EAAEC,WAAW,EAAEC,cAAc,EAAEC,eAAe,EAAEC,eAAe,EAAEC,eAAe,QAAQ,oBAAoB;AACpJ,IAAAC,gBAAA,GAAoBF,eAAe,CAAC,gBAAgB,CAAC;EAAAG,iBAAA,GAAAC,cAAA,CAAAF,gBAAA;EAA9CG,IAAI,GAAAF,iBAAA;EAAEG,GAAG,GAAAH,iBAAA;AAChB,IAAMI,kBAAkB,GAAG;EACzBC,IAAI,EAAEC,MAAM;EACZC,IAAI,EAAEf,SAAS;EACfgB,KAAK,EAAEb,cAAc,CAAC,EAAE,CAAC;EACzBc,MAAM,EAAEhB,WAAW;EACnBiB,MAAM,EAAEZ,eAAe,CAAC,CAAC,CAAC;EAC1Ba,OAAO,EAAEC,OAAO;EAChBC,SAAS,EAAEP;AACb,CAAC;AACD,IAAIQ,aAAa,GAAGxB,eAAe,CAAC;EAClCY,IAAI,EAAJA,IAAI;EACJa,KAAK,EAAEX,kBAAkB;EACzBY,KAAK,EAAE,CAAC,OAAO,CAAC;EAChBC,KAAK,WAAAA,MAACF,KAAK,EAAAG,IAAA,EAER;IAAA,IADDC,IAAI,GAAAD,IAAA,CAAJC,IAAI;IAEJ,IAAMC,YAAY,GAAG,SAAfA,YAAYA,CAAIC,KAAK,EAAK;MAC9BA,KAAK,CAACC,eAAe,CAAC,CAAC;MACvBH,IAAI,CAAC,OAAO,EAAEE,KAAK,CAAC;IACtB,CAAC;IACD,IAAME,YAAY,GAAG,SAAfA,YAAYA,CAAA,EAAS;MACzB,IAAMC,MAAM,GAAG,EAAE;MACjB,IACEjB,IAAI,GAKFQ,KAAK,CALPR,IAAI;QACJC,KAAK,GAIHO,KAAK,CAJPP,KAAK;QACLE,MAAM,GAGJK,KAAK,CAHPL,MAAM;QACND,MAAM,GAEJM,KAAK,CAFPN,MAAM;QACNE,OAAO,GACLI,KAAK,CADPJ,OAAO;MAET,KAAK,IAAIc,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGf,MAAM,EAAEe,CAAC,EAAE,EAAE;QAC/B,IAAMC,KAAI,GAAGlB,KAAK,CAACiB,CAAC,CAAC;QACrB,IAAME,UAAU,GAAGF,CAAC,KAAK,CAAC,IAAI,CAAChB,MAAM;QACrC,IAAMmB,UAAU,GAAGjB,OAAO,IAAIc,CAAC,KAAKjB,KAAK,CAACE,MAAM;QAChD,IAAImB,KAAK;QACT,IAAIJ,CAAC,KAAK,CAAC,IAAIhB,MAAM,EAAE;UACrBoB,KAAK,GAAG;YACNC,UAAU,EAAEvC,OAAO,CAACkB,MAAM;UAC5B,CAAC;QACH;QACAe,MAAM,CAACO,IAAI,CAAC1C,YAAY,CAAC,IAAI,EAAE;UAC7B,OAAO,EAAE,CAAA2C,eAAA,KACNtC,WAAW,EAAGiC,UAAU,GACxBxB,GAAG,CAAC,MAAM,EAAE;YACb8B,KAAK,EAAEL;UACT,CAAC,CAAC,CAAC;UACH,OAAO,EAAEC;QACX,CAAC,EAAE,CAACtB,IAAI,GAAGlB,YAAY,CAAC,GAAG,EAAE;UAC3B,OAAO,EAAE;YACP6C,UAAU,EAAER,KAAI,GAAG,SAAS,GAAG;UACjC;QACF,CAAC,EAAE,IAAI,CAAC,GAAGA,KAAI,EAAEE,UAAU,IAAIvC,YAAY,CAAC,KAAK,EAAE;UACjD,OAAO,EAAEc,GAAG,CAAC,QAAQ;QACvB,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;MACb;MACA,OAAOqB,MAAM;IACf,CAAC;IACD,OAAO,YAAM;MACX,IAAMnB,IAAI,GAAGU,KAAK,CAACF,SAAS,IAAIE,KAAK,CAACV,IAAI;MAC1C,OAAOhB,YAAY,CAAC,KAAK,EAAE;QACzB,OAAO,EAAEc,GAAG,CAAC;MACf,CAAC,EAAE,CAACd,YAAY,CAAC,IAAI,EAAE;QACrB,OAAO,EAAE,CAACc,GAAG,CAAC,UAAU,CAAC,EAAA6B,eAAA,KACtBpC,eAAe,EAAG,CAACmB,KAAK,CAACN,MAAM,EAChC;QACF,qBAAqB,EAAEW;MACzB,CAAC,EAAE,CAACG,YAAY,CAAC,CAAC,CAAC,CAAC,EAAElB,IAAI,IAAIhB,YAAY,CAAC,KAAK,EAAE;QAChD,OAAO,EAAEc,GAAG,CAACY,KAAK,CAACF,SAAS,GAAG,YAAY,GAAG,MAAM;MACtD,CAAC,EAAE,CAACR,IAAI,CAAC,CAAC,CAAC,CAAC;IACd,CAAC;EACH;AACF,CAAC,CAAC;AACF,SACES,aAAa,IAAIqB,OAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}