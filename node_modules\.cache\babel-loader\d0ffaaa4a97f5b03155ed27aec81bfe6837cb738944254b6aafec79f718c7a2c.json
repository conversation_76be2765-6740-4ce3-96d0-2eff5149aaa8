{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _Space from \"./Space.mjs\";\nvar Space = withInstall(_Space);\nvar stdin_default = Space;\nexport { Space, stdin_default as default };", "map": {"version": 3, "names": ["withInstall", "_Space", "Space", "stdin_default", "default"], "sources": ["C:/Users/<USER>/Downloads/Telegram Desktop/vue3_3.0/vue3_3.0/node_modules/vant/es/space/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _Space from \"./Space.mjs\";\nconst Space = withInstall(_Space);\nvar stdin_default = Space;\nexport {\n  Space,\n  stdin_default as default\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,MAAM,MAAM,aAAa;AAChC,IAAMC,KAAK,GAAGF,WAAW,CAACC,MAAM,CAAC;AACjC,IAAIE,aAAa,GAAGD,KAAK;AACzB,SACEA,KAAK,EACLC,aAAa,IAAIC,OAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}