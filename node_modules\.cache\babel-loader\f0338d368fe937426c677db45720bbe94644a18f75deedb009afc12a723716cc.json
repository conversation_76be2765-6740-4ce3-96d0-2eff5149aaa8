{"ast": null, "code": "import { createVNode as _createVNode, mergeProps as _mergeProps } from \"vue\";\nimport { extend, inBrowser, withInst<PERSON> } from \"../utils/index.mjs\";\nimport { mountComponent, usePopupState } from \"../utils/mount-component.mjs\";\nimport VanImagePreview from \"./ImagePreview.mjs\";\nvar instance;\nvar defaultConfig = {\n  loop: true,\n  images: [],\n  maxZoom: 3,\n  minZoom: 1 / 3,\n  onScale: void 0,\n  onClose: void 0,\n  onChange: void 0,\n  teleport: \"body\",\n  className: \"\",\n  showIndex: true,\n  closeable: false,\n  closeIcon: \"clear\",\n  transition: void 0,\n  beforeClose: void 0,\n  overlayStyle: void 0,\n  overlayClass: void 0,\n  startPosition: 0,\n  swipeDuration: 300,\n  showIndicators: false,\n  closeOnPopstate: true,\n  closeIconPosition: \"top-right\"\n};\nfunction initInstance() {\n  var _mountComponent = mountComponent({\n    setup: function setup() {\n      var _usePopupState = usePopupState(),\n        state = _usePopupState.state,\n        toggle = _usePopupState.toggle;\n      var onClosed = function onClosed() {\n        state.images = [];\n      };\n      return function () {\n        return _createVNode(VanImagePreview, _mergeProps(state, {\n          \"onClosed\": onClosed,\n          \"onUpdate:show\": toggle\n        }), null);\n      };\n    }\n  });\n  instance = _mountComponent.instance;\n}\nvar ImagePreview = function ImagePreview(options) {\n  var startPosition = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n  if (!inBrowser) {\n    return;\n  }\n  if (!instance) {\n    initInstance();\n  }\n  options = Array.isArray(options) ? {\n    images: options,\n    startPosition: startPosition\n  } : options;\n  instance.open(extend({}, defaultConfig, options));\n  return instance;\n};\nImagePreview.Component = withInstall(VanImagePreview);\nImagePreview.install = function (app) {\n  app.use(ImagePreview.Component);\n};\nexport { ImagePreview };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}