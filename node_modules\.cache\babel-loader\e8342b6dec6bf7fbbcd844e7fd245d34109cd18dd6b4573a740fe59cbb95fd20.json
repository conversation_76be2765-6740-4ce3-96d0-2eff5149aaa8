{"ast": null, "code": "function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nfunction _iterableToArrayLimit(arr, i) { var _i = null == arr ? null : \"undefined\" != typeof Symbol && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (null != _i) { var _s, _e, _x, _r, _arr = [], _n = !0, _d = !1; try { if (_x = (_i = _i.call(arr)).next, 0 === i) { if (Object(_i) !== _i) return; _n = !1; } else for (; !(_n = (_s = _x.call(_i)).done) && (_arr.push(_s.value), _arr.length !== i); _n = !0); } catch (err) { _d = !0, _e = err; } finally { try { if (!_n && null != _i.return && (_r = _i.return(), Object(_r) !== _r)) return; } finally { if (_d) throw _e; } } return _arr; } }\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\nimport { createVNode as _createVNode, mergeProps as _mergeProps } from \"vue\";\nimport { defineComponent } from \"vue\";\nimport { extend, numericProp, createNamespace } from \"../utils/index.mjs\";\nimport { SIDEBAR_KEY } from \"../sidebar/Sidebar.mjs\";\nimport { useParent } from \"@vant/use\";\nimport { useRoute, routeProps } from \"../composables/use-route.mjs\";\nimport { Badge } from \"../badge/index.mjs\";\nvar _createNamespace = createNamespace(\"sidebar-item\"),\n  _createNamespace2 = _slicedToArray(_createNamespace, 2),\n  name = _createNamespace2[0],\n  bem = _createNamespace2[1];\nvar sidebarItemProps = extend({}, routeProps, {\n  dot: Boolean,\n  title: String,\n  badge: numericProp,\n  disabled: Boolean,\n  badgeProps: Object\n});\nvar stdin_default = defineComponent({\n  name: name,\n  props: sidebarItemProps,\n  emits: [\"click\"],\n  setup: function setup(props, _ref) {\n    var emit = _ref.emit,\n      slots = _ref.slots;\n    var route = useRoute();\n    var _useParent = useParent(SIDEBAR_KEY),\n      parent = _useParent.parent,\n      index = _useParent.index;\n    if (!parent) {\n      if (process.env.NODE_ENV !== \"production\") {\n        console.error(\"[Vant] <SidebarItem> must be a child component of <Sidebar>.\");\n      }\n      return;\n    }\n    var onClick = function onClick() {\n      if (props.disabled) {\n        return;\n      }\n      emit(\"click\", index.value);\n      parent.setActive(index.value);\n      route();\n    };\n    return function () {\n      var dot = props.dot,\n        badge = props.badge,\n        title = props.title,\n        disabled = props.disabled;\n      var selected = index.value === parent.getActive();\n      return _createVNode(\"div\", {\n        \"role\": \"tab\",\n        \"class\": bem({\n          select: selected,\n          disabled: disabled\n        }),\n        \"tabindex\": disabled ? void 0 : 0,\n        \"aria-selected\": selected,\n        \"onClick\": onClick\n      }, [_createVNode(Badge, _mergeProps({\n        \"dot\": dot,\n        \"class\": bem(\"text\"),\n        \"content\": badge\n      }, props.badgeProps), {\n        default: function _default() {\n          return [slots.title ? slots.title() : title];\n        }\n      })]);\n    };\n  }\n});\nexport { stdin_default as default };", "map": {"version": 3, "names": ["createVNode", "_createVNode", "mergeProps", "_mergeProps", "defineComponent", "extend", "numericProp", "createNamespace", "SIDEBAR_KEY", "useParent", "useRoute", "routeProps", "Badge", "_createNamespace", "_createNamespace2", "_slicedToArray", "name", "bem", "sidebarItemProps", "dot", "Boolean", "title", "String", "badge", "disabled", "badgeProps", "Object", "stdin_default", "props", "emits", "setup", "_ref", "emit", "slots", "route", "_useParent", "parent", "index", "process", "env", "NODE_ENV", "console", "error", "onClick", "value", "setActive", "selected", "getActive", "select", "default", "_default"], "sources": ["C:/Users/<USER>/Downloads/Telegram Desktop/vue3_3.0/vue3_3.0/node_modules/vant/es/sidebar-item/SidebarItem.mjs"], "sourcesContent": ["import { createVNode as _createVNode, mergeProps as _mergeProps } from \"vue\";\nimport { defineComponent } from \"vue\";\nimport { extend, numericProp, createNamespace } from \"../utils/index.mjs\";\nimport { SIDEBAR_KEY } from \"../sidebar/Sidebar.mjs\";\nimport { useParent } from \"@vant/use\";\nimport { useRoute, routeProps } from \"../composables/use-route.mjs\";\nimport { Badge } from \"../badge/index.mjs\";\nconst [name, bem] = createNamespace(\"sidebar-item\");\nconst sidebarItemProps = extend({}, routeProps, {\n  dot: Boolean,\n  title: String,\n  badge: numericProp,\n  disabled: Boolean,\n  badgeProps: Object\n});\nvar stdin_default = defineComponent({\n  name,\n  props: sidebarItemProps,\n  emits: [\"click\"],\n  setup(props, {\n    emit,\n    slots\n  }) {\n    const route = useRoute();\n    const {\n      parent,\n      index\n    } = useParent(SIDEBAR_KEY);\n    if (!parent) {\n      if (process.env.NODE_ENV !== \"production\") {\n        console.error(\"[Vant] <SidebarItem> must be a child component of <Sidebar>.\");\n      }\n      return;\n    }\n    const onClick = () => {\n      if (props.disabled) {\n        return;\n      }\n      emit(\"click\", index.value);\n      parent.setActive(index.value);\n      route();\n    };\n    return () => {\n      const {\n        dot,\n        badge,\n        title,\n        disabled\n      } = props;\n      const selected = index.value === parent.getActive();\n      return _createVNode(\"div\", {\n        \"role\": \"tab\",\n        \"class\": bem({\n          select: selected,\n          disabled\n        }),\n        \"tabindex\": disabled ? void 0 : 0,\n        \"aria-selected\": selected,\n        \"onClick\": onClick\n      }, [_createVNode(Badge, _mergeProps({\n        \"dot\": dot,\n        \"class\": bem(\"text\"),\n        \"content\": badge\n      }, props.badgeProps), {\n        default: () => [slots.title ? slots.title() : title]\n      })]);\n    };\n  }\n});\nexport {\n  stdin_default as default\n};\n"], "mappings": ";;;;;;AAAA,SAASA,WAAW,IAAIC,YAAY,EAAEC,UAAU,IAAIC,WAAW,QAAQ,KAAK;AAC5E,SAASC,eAAe,QAAQ,KAAK;AACrC,SAASC,MAAM,EAAEC,WAAW,EAAEC,eAAe,QAAQ,oBAAoB;AACzE,SAASC,WAAW,QAAQ,wBAAwB;AACpD,SAASC,SAAS,QAAQ,WAAW;AACrC,SAASC,QAAQ,EAAEC,UAAU,QAAQ,8BAA8B;AACnE,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,IAAAC,gBAAA,GAAoBN,eAAe,CAAC,cAAc,CAAC;EAAAO,iBAAA,GAAAC,cAAA,CAAAF,gBAAA;EAA5CG,IAAI,GAAAF,iBAAA;EAAEG,GAAG,GAAAH,iBAAA;AAChB,IAAMI,gBAAgB,GAAGb,MAAM,CAAC,CAAC,CAAC,EAAEM,UAAU,EAAE;EAC9CQ,GAAG,EAAEC,OAAO;EACZC,KAAK,EAAEC,MAAM;EACbC,KAAK,EAAEjB,WAAW;EAClBkB,QAAQ,EAAEJ,OAAO;EACjBK,UAAU,EAAEC;AACd,CAAC,CAAC;AACF,IAAIC,aAAa,GAAGvB,eAAe,CAAC;EAClCY,IAAI,EAAJA,IAAI;EACJY,KAAK,EAAEV,gBAAgB;EACvBW,KAAK,EAAE,CAAC,OAAO,CAAC;EAChBC,KAAK,WAAAA,MAACF,KAAK,EAAAG,IAAA,EAGR;IAAA,IAFDC,IAAI,GAAAD,IAAA,CAAJC,IAAI;MACJC,KAAK,GAAAF,IAAA,CAALE,KAAK;IAEL,IAAMC,KAAK,GAAGxB,QAAQ,CAAC,CAAC;IACxB,IAAAyB,UAAA,GAGI1B,SAAS,CAACD,WAAW,CAAC;MAFxB4B,MAAM,GAAAD,UAAA,CAANC,MAAM;MACNC,KAAK,GAAAF,UAAA,CAALE,KAAK;IAEP,IAAI,CAACD,MAAM,EAAE;MACX,IAAIE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzCC,OAAO,CAACC,KAAK,CAAC,8DAA8D,CAAC;MAC/E;MACA;IACF;IACA,IAAMC,OAAO,GAAG,SAAVA,OAAOA,CAAA,EAAS;MACpB,IAAIf,KAAK,CAACJ,QAAQ,EAAE;QAClB;MACF;MACAQ,IAAI,CAAC,OAAO,EAAEK,KAAK,CAACO,KAAK,CAAC;MAC1BR,MAAM,CAACS,SAAS,CAACR,KAAK,CAACO,KAAK,CAAC;MAC7BV,KAAK,CAAC,CAAC;IACT,CAAC;IACD,OAAO,YAAM;MACX,IACEf,GAAG,GAIDS,KAAK,CAJPT,GAAG;QACHI,KAAK,GAGHK,KAAK,CAHPL,KAAK;QACLF,KAAK,GAEHO,KAAK,CAFPP,KAAK;QACLG,QAAQ,GACNI,KAAK,CADPJ,QAAQ;MAEV,IAAMsB,QAAQ,GAAGT,KAAK,CAACO,KAAK,KAAKR,MAAM,CAACW,SAAS,CAAC,CAAC;MACnD,OAAO9C,YAAY,CAAC,KAAK,EAAE;QACzB,MAAM,EAAE,KAAK;QACb,OAAO,EAAEgB,GAAG,CAAC;UACX+B,MAAM,EAAEF,QAAQ;UAChBtB,QAAQ,EAARA;QACF,CAAC,CAAC;QACF,UAAU,EAAEA,QAAQ,GAAG,KAAK,CAAC,GAAG,CAAC;QACjC,eAAe,EAAEsB,QAAQ;QACzB,SAAS,EAAEH;MACb,CAAC,EAAE,CAAC1C,YAAY,CAACW,KAAK,EAAET,WAAW,CAAC;QAClC,KAAK,EAAEgB,GAAG;QACV,OAAO,EAAEF,GAAG,CAAC,MAAM,CAAC;QACpB,SAAS,EAAEM;MACb,CAAC,EAAEK,KAAK,CAACH,UAAU,CAAC,EAAE;QACpBwB,OAAO,EAAE,SAAAC,SAAA;UAAA,OAAM,CAACjB,KAAK,CAACZ,KAAK,GAAGY,KAAK,CAACZ,KAAK,CAAC,CAAC,GAAGA,KAAK,CAAC;QAAA;MACtD,CAAC,CAAC,CAAC,CAAC;IACN,CAAC;EACH;AACF,CAAC,CAAC;AACF,SACEM,aAAa,IAAIsB,OAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}