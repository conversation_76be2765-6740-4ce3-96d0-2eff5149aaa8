{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, toDisplayString as _toDisplayString, createElementVNode as _createElementVNode, createTextVNode as _createTextVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, pushScopeId as _pushScopeId, popScopeId as _popScopeId } from \"vue\";\nvar _withScopeId = function _withScopeId(n) {\n  return _pushScopeId(\"data-v-87bff1aa\"), n = n(), _popScopeId(), n;\n};\nvar _hoisted_1 = {\n  class: \"tel home\"\n};\nvar _hoisted_2 = {\n  class: \"box\"\n};\nvar _hoisted_3 = {\n  class: \"box_t\"\n};\nvar _hoisted_4 = {\n  class: \"box_tlt\"\n};\nvar _hoisted_5 = [\"src\"];\nvar _hoisted_6 = {\n  class: \"box_fot\"\n};\nvar _hoisted_7 = {\n  class: \"box_text\"\n};\nvar _hoisted_8 = {\n  class: \"qr-code qr\",\n  ref: \"qrCodeUrl\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_van_nav_bar = _resolveComponent(\"van-nav-bar\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_van_nav_bar, {\n    title: _ctx.$t('msg.code'),\n    background: \"#ffffff\",\n    \"title-style\": \"color:black; font-size: 16px;\",\n    \"left-arrow\": \"\",\n    onClickLeft: _cache[0] || (_cache[0] = function ($event) {\n      return _ctx.$router.go(-1);\n    })\n  }, null, 8, [\"title\"]), _createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"div\", _hoisted_4, [_createTextVNode(_toDisplayString(_ctx.$t('msg.code')) + \" : \", 1), _createElementVNode(\"span\", null, _toDisplayString($setup.info.invite_code), 1), _createElementVNode(\"img\", {\n    src: require('@/assets/images/copy.svg'),\n    alt: \"\",\n    onClick: _cache[1] || (_cache[1] = function ($event) {\n      var _$setup$info;\n      return $setup.copy((_$setup$info = $setup.info) === null || _$setup$info === void 0 ? void 0 : _$setup$info.url);\n    })\n  }, null, 8, _hoisted_5)]), _createElementVNode(\"div\", _hoisted_6, [_createElementVNode(\"div\", _hoisted_7, _toDisplayString(_ctx.$t('msg.yqhylqyj')), 1), _createElementVNode(\"div\", _hoisted_8, null, 512)]), _createElementVNode(\"div\", {\n    class: \"cop\",\n    onClick: _cache[2] || (_cache[2] = function ($event) {\n      var _$setup$info2;\n      return $setup.copy((_$setup$info2 = $setup.info) === null || _$setup$info2 === void 0 ? void 0 : _$setup$info2.url);\n    })\n  }, _toDisplayString(_ctx.$t('msg.fzyqlj')), 1)])])]);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}