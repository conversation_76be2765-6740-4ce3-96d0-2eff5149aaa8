{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _Col from \"./Col.mjs\";\nvar Col = withInstall(_Col);\nvar stdin_default = Col;\nexport { Col, stdin_default as default };", "map": {"version": 3, "names": ["withInstall", "_Col", "Col", "stdin_default", "default"], "sources": ["C:/Users/<USER>/Desktop/vue3_3.0/node_modules/vant/es/col/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _Col from \"./Col.mjs\";\nconst Col = withInstall(_Col);\nvar stdin_default = Col;\nexport {\n  Col,\n  stdin_default as default\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,IAAI,MAAM,WAAW;AAC5B,IAAMC,GAAG,GAAGF,WAAW,CAACC,IAAI,CAAC;AAC7B,IAAIE,aAAa,GAAGD,GAAG;AACvB,SACEA,GAAG,EACHC,aAAa,IAAIC,OAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}