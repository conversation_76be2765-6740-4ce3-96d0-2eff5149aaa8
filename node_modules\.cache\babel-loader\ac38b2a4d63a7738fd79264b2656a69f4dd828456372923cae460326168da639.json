{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _ShareSheet from \"./ShareSheet.mjs\";\nvar ShareSheet = withInstall(_ShareSheet);\nvar stdin_default = ShareSheet;\nexport { ShareSheet, stdin_default as default };", "map": {"version": 3, "names": ["withInstall", "_ShareSheet", "ShareSheet", "stdin_default", "default"], "sources": ["C:/Users/<USER>/Downloads/Telegram Desktop/vue3_3.0/vue3_3.0/node_modules/vant/es/share-sheet/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _ShareSheet from \"./ShareSheet.mjs\";\nconst ShareSheet = withInstall(_ShareSheet);\nvar stdin_default = ShareSheet;\nexport {\n  ShareSheet,\n  stdin_default as default\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,WAAW,MAAM,kBAAkB;AAC1C,IAAMC,UAAU,GAAGF,WAAW,CAACC,WAAW,CAAC;AAC3C,IAAIE,aAAa,GAAGD,UAAU;AAC9B,SACEA,UAAU,EACVC,aAAa,IAAIC,OAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}