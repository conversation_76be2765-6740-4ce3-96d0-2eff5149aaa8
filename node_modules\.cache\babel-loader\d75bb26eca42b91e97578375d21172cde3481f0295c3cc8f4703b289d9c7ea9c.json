{"ast": null, "code": "function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nfunction _iterableToArrayLimit(arr, i) { var _i = null == arr ? null : \"undefined\" != typeof Symbol && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (null != _i) { var _s, _e, _x, _r, _arr = [], _n = !0, _d = !1; try { if (_x = (_i = _i.call(arr)).next, 0 === i) { if (Object(_i) !== _i) return; _n = !1; } else for (; !(_n = (_s = _x.call(_i)).done) && (_arr.push(_s.value), _arr.length !== i); _n = !0); } catch (err) { _d = !0, _e = err; } finally { try { if (!_n && null != _i.return && (_r = _i.return(), Object(_r) !== _r)) return; } finally { if (_d) throw _e; } } return _arr; } }\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\nimport { createVNode as _createVNode } from \"vue\";\nimport { watch, computed, defineComponent } from \"vue\";\nimport { truthProp, makeStringProp, makeNumericProp, createNamespace } from \"../utils/index.mjs\";\nimport { parseFormat } from \"./utils.mjs\";\nimport { useCountDown } from \"@vant/use\";\nimport { useExpose } from \"../composables/use-expose.mjs\";\nvar _createNamespace = createNamespace(\"count-down\"),\n  _createNamespace2 = _slicedToArray(_createNamespace, 2),\n  name = _createNamespace2[0],\n  bem = _createNamespace2[1];\nvar countDownProps = {\n  time: makeNumericProp(0),\n  format: makeStringProp(\"HH:mm:ss\"),\n  autoStart: truthProp,\n  millisecond: Boolean\n};\nvar stdin_default = defineComponent({\n  name: name,\n  props: countDownProps,\n  emits: [\"change\", \"finish\"],\n  setup: function setup(props, _ref) {\n    var emit = _ref.emit,\n      slots = _ref.slots;\n    var _useCountDown = useCountDown({\n        time: +props.time,\n        millisecond: props.millisecond,\n        onChange: function onChange(current2) {\n          return emit(\"change\", current2);\n        },\n        onFinish: function onFinish() {\n          return emit(\"finish\");\n        }\n      }),\n      start = _useCountDown.start,\n      pause = _useCountDown.pause,\n      reset = _useCountDown.reset,\n      current = _useCountDown.current;\n    var timeText = computed(function () {\n      return parseFormat(props.format, current.value);\n    });\n    var resetTime = function resetTime() {\n      reset(+props.time);\n      if (props.autoStart) {\n        start();\n      }\n    };\n    watch(function () {\n      return props.time;\n    }, resetTime, {\n      immediate: true\n    });\n    useExpose({\n      start: start,\n      pause: pause,\n      reset: resetTime\n    });\n    return function () {\n      return _createVNode(\"div\", {\n        \"role\": \"timer\",\n        \"class\": bem()\n      }, [slots.default ? slots.default(current.value) : timeText.value]);\n    };\n  }\n});\nexport { stdin_default as default };", "map": {"version": 3, "names": ["createVNode", "_createVNode", "watch", "computed", "defineComponent", "truthProp", "makeStringProp", "makeNumericProp", "createNamespace", "parseFormat", "useCountDown", "useExpose", "_createNamespace", "_createNamespace2", "_slicedToArray", "name", "bem", "countDownProps", "time", "format", "autoStart", "millisecond", "Boolean", "stdin_default", "props", "emits", "setup", "_ref", "emit", "slots", "_useCountDown", "onChange", "current2", "onFinish", "start", "pause", "reset", "current", "timeText", "value", "resetTime", "immediate", "default"], "sources": ["C:/Users/<USER>/Desktop/vue3_3.0/node_modules/vant/es/count-down/CountDown.mjs"], "sourcesContent": ["import { createVNode as _createVNode } from \"vue\";\nimport { watch, computed, defineComponent } from \"vue\";\nimport { truthProp, makeStringProp, makeNumericProp, createNamespace } from \"../utils/index.mjs\";\nimport { parseFormat } from \"./utils.mjs\";\nimport { useCountDown } from \"@vant/use\";\nimport { useExpose } from \"../composables/use-expose.mjs\";\nconst [name, bem] = createNamespace(\"count-down\");\nconst countDownProps = {\n  time: makeNumericProp(0),\n  format: makeStringProp(\"HH:mm:ss\"),\n  autoStart: truthProp,\n  millisecond: Boolean\n};\nvar stdin_default = defineComponent({\n  name,\n  props: countDownProps,\n  emits: [\"change\", \"finish\"],\n  setup(props, {\n    emit,\n    slots\n  }) {\n    const {\n      start,\n      pause,\n      reset,\n      current\n    } = useCountDown({\n      time: +props.time,\n      millisecond: props.millisecond,\n      onChange: (current2) => emit(\"change\", current2),\n      onFinish: () => emit(\"finish\")\n    });\n    const timeText = computed(() => parseFormat(props.format, current.value));\n    const resetTime = () => {\n      reset(+props.time);\n      if (props.autoStart) {\n        start();\n      }\n    };\n    watch(() => props.time, resetTime, {\n      immediate: true\n    });\n    useExpose({\n      start,\n      pause,\n      reset: resetTime\n    });\n    return () => _createVNode(\"div\", {\n      \"role\": \"timer\",\n      \"class\": bem()\n    }, [slots.default ? slots.default(current.value) : timeText.value]);\n  }\n});\nexport {\n  stdin_default as default\n};\n"], "mappings": ";;;;;;AAAA,SAASA,WAAW,IAAIC,YAAY,QAAQ,KAAK;AACjD,SAASC,KAAK,EAAEC,QAAQ,EAAEC,eAAe,QAAQ,KAAK;AACtD,SAASC,SAAS,EAAEC,cAAc,EAAEC,eAAe,EAAEC,eAAe,QAAQ,oBAAoB;AAChG,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,YAAY,QAAQ,WAAW;AACxC,SAASC,SAAS,QAAQ,+BAA+B;AACzD,IAAAC,gBAAA,GAAoBJ,eAAe,CAAC,YAAY,CAAC;EAAAK,iBAAA,GAAAC,cAAA,CAAAF,gBAAA;EAA1CG,IAAI,GAAAF,iBAAA;EAAEG,GAAG,GAAAH,iBAAA;AAChB,IAAMI,cAAc,GAAG;EACrBC,IAAI,EAAEX,eAAe,CAAC,CAAC,CAAC;EACxBY,MAAM,EAAEb,cAAc,CAAC,UAAU,CAAC;EAClCc,SAAS,EAAEf,SAAS;EACpBgB,WAAW,EAAEC;AACf,CAAC;AACD,IAAIC,aAAa,GAAGnB,eAAe,CAAC;EAClCW,IAAI,EAAJA,IAAI;EACJS,KAAK,EAAEP,cAAc;EACrBQ,KAAK,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC;EAC3BC,KAAK,WAAAA,MAACF,KAAK,EAAAG,IAAA,EAGR;IAAA,IAFDC,IAAI,GAAAD,IAAA,CAAJC,IAAI;MACJC,KAAK,GAAAF,IAAA,CAALE,KAAK;IAEL,IAAAC,aAAA,GAKIpB,YAAY,CAAC;QACfQ,IAAI,EAAE,CAACM,KAAK,CAACN,IAAI;QACjBG,WAAW,EAAEG,KAAK,CAACH,WAAW;QAC9BU,QAAQ,EAAE,SAAAA,SAACC,QAAQ;UAAA,OAAKJ,IAAI,CAAC,QAAQ,EAAEI,QAAQ,CAAC;QAAA;QAChDC,QAAQ,EAAE,SAAAA,SAAA;UAAA,OAAML,IAAI,CAAC,QAAQ,CAAC;QAAA;MAChC,CAAC,CAAC;MATAM,KAAK,GAAAJ,aAAA,CAALI,KAAK;MACLC,KAAK,GAAAL,aAAA,CAALK,KAAK;MACLC,KAAK,GAAAN,aAAA,CAALM,KAAK;MACLC,OAAO,GAAAP,aAAA,CAAPO,OAAO;IAOT,IAAMC,QAAQ,GAAGnC,QAAQ,CAAC;MAAA,OAAMM,WAAW,CAACe,KAAK,CAACL,MAAM,EAAEkB,OAAO,CAACE,KAAK,CAAC;IAAA,EAAC;IACzE,IAAMC,SAAS,GAAG,SAAZA,SAASA,CAAA,EAAS;MACtBJ,KAAK,CAAC,CAACZ,KAAK,CAACN,IAAI,CAAC;MAClB,IAAIM,KAAK,CAACJ,SAAS,EAAE;QACnBc,KAAK,CAAC,CAAC;MACT;IACF,CAAC;IACDhC,KAAK,CAAC;MAAA,OAAMsB,KAAK,CAACN,IAAI;IAAA,GAAEsB,SAAS,EAAE;MACjCC,SAAS,EAAE;IACb,CAAC,CAAC;IACF9B,SAAS,CAAC;MACRuB,KAAK,EAALA,KAAK;MACLC,KAAK,EAALA,KAAK;MACLC,KAAK,EAAEI;IACT,CAAC,CAAC;IACF,OAAO;MAAA,OAAMvC,YAAY,CAAC,KAAK,EAAE;QAC/B,MAAM,EAAE,OAAO;QACf,OAAO,EAAEe,GAAG,CAAC;MACf,CAAC,EAAE,CAACa,KAAK,CAACa,OAAO,GAAGb,KAAK,CAACa,OAAO,CAACL,OAAO,CAACE,KAAK,CAAC,GAAGD,QAAQ,CAACC,KAAK,CAAC,CAAC;IAAA;EACrE;AACF,CAAC,CAAC;AACF,SACEhB,aAAa,IAAImB,OAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}