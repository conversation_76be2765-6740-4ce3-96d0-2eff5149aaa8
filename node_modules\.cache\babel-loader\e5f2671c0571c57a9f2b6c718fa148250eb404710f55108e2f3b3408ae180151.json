{"ast": null, "code": "function _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return _typeof(key) === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (_typeof(input) !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (_typeof(res) !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\nfunction _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread(); }\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _iterableToArray(iter) { if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter); }\nfunction _arrayWithoutHoles(arr) { if (Array.isArray(arr)) return _arrayLikeToArray(arr); }\nfunction _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nfunction _iterableToArrayLimit(arr, i) { var _i = null == arr ? null : \"undefined\" != typeof Symbol && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (null != _i) { var _s, _e, _x, _r, _arr = [], _n = !0, _d = !1; try { if (_x = (_i = _i.call(arr)).next, 0 === i) { if (Object(_i) !== _i) return; _n = !1; } else for (; !(_n = (_s = _x.call(_i)).done) && (_arr.push(_s.value), _arr.length !== i); _n = !0); } catch (err) { _d = !0, _e = err; } finally { try { if (!_n && null != _i.return && (_r = _i.return(), Object(_r) !== _r)) return; } finally { if (_d) throw _e; } } return _arr; } }\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\nimport { createVNode as _createVNode } from \"vue\";\nimport { computed, defineComponent, Fragment } from \"vue\";\nimport { createNamespace } from \"../utils/index.mjs\";\nvar _createNamespace = createNamespace(\"space\"),\n  _createNamespace2 = _slicedToArray(_createNamespace, 2),\n  name = _createNamespace2[0],\n  bem = _createNamespace2[1];\nvar spaceProps = {\n  align: String,\n  direction: {\n    type: String,\n    default: \"horizontal\"\n  },\n  size: {\n    type: [Number, String, Array],\n    default: 8\n  },\n  wrap: Boolean,\n  fill: Boolean\n};\nfunction filterEmpty() {\n  var children = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];\n  var nodes = [];\n  children.forEach(function (child) {\n    if (Array.isArray(child)) {\n      nodes.push.apply(nodes, _toConsumableArray(child));\n    } else if (child.type === Fragment) {\n      nodes.push.apply(nodes, _toConsumableArray(filterEmpty(child.children)));\n    } else {\n      nodes.push(child);\n    }\n  });\n  return nodes.filter(function (c) {\n    var _a;\n    return !(c && (typeof Comment !== \"undefined\" && c.type === Comment || c.type === Fragment && ((_a = c.children) == null ? void 0 : _a.length) === 0 || c.type === Text && c.children.trim() === \"\"));\n  });\n}\nvar stdin_default = defineComponent({\n  name: name,\n  props: spaceProps,\n  setup: function setup(props, _ref) {\n    var slots = _ref.slots;\n    var mergedAlign = computed(function () {\n      var _a;\n      return (_a = props.align) != null ? _a : props.direction === \"horizontal\" ? \"center\" : \"\";\n    });\n    var getMargin = function getMargin(size) {\n      if (typeof size === \"number\") {\n        return size + \"px\";\n      }\n      return size;\n    };\n    var getMarginStyle = function getMarginStyle(isLast) {\n      var style = {};\n      var marginRight = \"\".concat(getMargin(Array.isArray(props.size) ? props.size[0] : props.size));\n      var marginBottom = \"\".concat(getMargin(Array.isArray(props.size) ? props.size[1] : props.size));\n      if (isLast) {\n        return props.wrap ? {\n          marginBottom: marginBottom\n        } : {};\n      }\n      if (props.direction === \"horizontal\") {\n        style.marginRight = marginRight;\n      }\n      if (props.direction === \"vertical\" || props.wrap) {\n        style.marginBottom = marginBottom;\n      }\n      return style;\n    };\n    return function () {\n      var _bem;\n      var _a;\n      var children = filterEmpty((_a = slots.default) == null ? void 0 : _a.call(slots));\n      return _createVNode(\"div\", {\n        \"class\": [bem((_bem = {}, _defineProperty(_bem, props.direction, props.direction), _defineProperty(_bem, \"align-\".concat(mergedAlign.value), mergedAlign.value), _defineProperty(_bem, \"wrap\", props.wrap), _defineProperty(_bem, \"fill\", props.fill), _bem))]\n      }, [children.map(function (c, i) {\n        return _createVNode(\"div\", {\n          \"key\": \"item-\".concat(i),\n          \"class\": \"\".concat(name, \"-item\"),\n          \"style\": getMarginStyle(i === children.length - 1)\n        }, [c]);\n      })]);\n    };\n  }\n});\nexport { stdin_default as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}