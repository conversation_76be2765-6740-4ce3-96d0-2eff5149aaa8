{"ast": null, "code": "import { noop } from \"./basic.mjs\";\nimport { isPromise } from \"./validate.mjs\";\nfunction callInterceptor(interceptor, _ref) {\n  var _ref$args = _ref.args,\n    args = _ref$args === void 0 ? [] : _ref$args,\n    done = _ref.done,\n    canceled = _ref.canceled;\n  if (interceptor) {\n    var returnVal = interceptor.apply(null, args);\n    if (isPromise(returnVal)) {\n      returnVal.then(function (value) {\n        if (value) {\n          done();\n        } else if (canceled) {\n          canceled();\n        }\n      }).catch(noop);\n    } else if (returnVal) {\n      done();\n    } else if (canceled) {\n      canceled();\n    }\n  } else {\n    done();\n  }\n}\nexport { callInterceptor };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}