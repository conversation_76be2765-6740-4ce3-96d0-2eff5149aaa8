{"ast": null, "code": "import { createVNode as _createVNode } from \"vue\";\nimport { defineComponent } from \"vue\";\nimport { t, bem, isImageFile } from \"./utils.mjs\";\nimport { isDef, extend, numericProp, getSizeStyle, callInterceptor, makeRequiredProp } from \"../utils/index.mjs\";\nimport { Icon } from \"../icon/index.mjs\";\nimport { Image } from \"../image/index.mjs\";\nimport { Loading } from \"../loading/index.mjs\";\nvar stdin_default = defineComponent({\n  props: {\n    name: numericProp,\n    item: makeRequiredProp(Object),\n    index: Number,\n    imageFit: String,\n    lazyLoad: Boolean,\n    deletable: Boolean,\n    previewSize: [Number, String, Array],\n    beforeDelete: Function\n  },\n  emits: [\"delete\", \"preview\"],\n  setup: function setup(props, _ref) {\n    var emit = _ref.emit,\n      slots = _ref.slots;\n    var renderMask = function renderMask() {\n      var _props$item = props.item,\n        status = _props$item.status,\n        message = _props$item.message;\n      if (status === \"uploading\" || status === \"failed\") {\n        var MaskIcon = status === \"failed\" ? _createVNode(Icon, {\n          \"name\": \"close\",\n          \"class\": bem(\"mask-icon\")\n        }, null) : _createVNode(Loading, {\n          \"class\": bem(\"loading\")\n        }, null);\n        var showMessage = isDef(message) && message !== \"\";\n        return _createVNode(\"div\", {\n          \"class\": bem(\"mask\")\n        }, [MaskIcon, showMessage && _createVNode(\"div\", {\n          \"class\": bem(\"mask-message\")\n        }, [message])]);\n      }\n    };\n    var onDelete = function onDelete(event) {\n      var name = props.name,\n        item = props.item,\n        index = props.index,\n        beforeDelete = props.beforeDelete;\n      event.stopPropagation();\n      callInterceptor(beforeDelete, {\n        args: [item, {\n          name: name,\n          index: index\n        }],\n        done: function done() {\n          return emit(\"delete\");\n        }\n      });\n    };\n    var onPreview = function onPreview() {\n      return emit(\"preview\");\n    };\n    var renderDeleteIcon = function renderDeleteIcon() {\n      if (props.deletable && props.item.status !== \"uploading\") {\n        var slot = slots[\"preview-delete\"];\n        return _createVNode(\"div\", {\n          \"role\": \"button\",\n          \"class\": bem(\"preview-delete\", {\n            shadow: !slot\n          }),\n          \"tabindex\": 0,\n          \"aria-label\": t(\"delete\"),\n          \"onClick\": onDelete\n        }, [slot ? slot() : _createVNode(Icon, {\n          \"name\": \"cross\",\n          \"class\": bem(\"preview-delete-icon\")\n        }, null)]);\n      }\n    };\n    var renderCover = function renderCover() {\n      if (slots[\"preview-cover\"]) {\n        var index = props.index,\n          item = props.item;\n        return _createVNode(\"div\", {\n          \"class\": bem(\"preview-cover\")\n        }, [slots[\"preview-cover\"](extend({\n          index: index\n        }, item))]);\n      }\n    };\n    var renderPreview = function renderPreview() {\n      var item = props.item,\n        lazyLoad = props.lazyLoad,\n        imageFit = props.imageFit,\n        previewSize = props.previewSize;\n      if (isImageFile(item)) {\n        return _createVNode(Image, {\n          \"fit\": imageFit,\n          \"src\": item.content || item.url,\n          \"class\": bem(\"preview-image\"),\n          \"width\": Array.isArray(previewSize) ? previewSize[0] : previewSize,\n          \"height\": Array.isArray(previewSize) ? previewSize[1] : previewSize,\n          \"lazyLoad\": lazyLoad,\n          \"onClick\": onPreview\n        }, {\n          default: renderCover\n        });\n      }\n      return _createVNode(\"div\", {\n        \"class\": bem(\"file\"),\n        \"style\": getSizeStyle(props.previewSize)\n      }, [_createVNode(Icon, {\n        \"class\": bem(\"file-icon\"),\n        \"name\": \"description\"\n      }, null), _createVNode(\"div\", {\n        \"class\": [bem(\"file-name\"), \"van-ellipsis\"]\n      }, [item.file ? item.file.name : item.url]), renderCover()]);\n    };\n    return function () {\n      return _createVNode(\"div\", {\n        \"class\": bem(\"preview\")\n      }, [renderPreview(), renderMask(), renderDeleteIcon()]);\n    };\n  }\n});\nexport { stdin_default as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}