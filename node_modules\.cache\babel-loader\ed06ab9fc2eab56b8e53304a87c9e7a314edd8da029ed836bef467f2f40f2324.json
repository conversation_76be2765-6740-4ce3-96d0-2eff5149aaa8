{"ast": null, "code": "function _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\nfunction _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread(); }\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _iterableToArray(iter) { if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter); }\nfunction _arrayWithoutHoles(arr) { if (Array.isArray(arr)) return _arrayLikeToArray(arr); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return _typeof(key) === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (_typeof(input) !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (_typeof(res) !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\nimport { reactive, ref, getCurrentInstance } from 'vue';\nimport store from '@/store/index';\nimport { get_recharge, get_recharge2 } from '@/api/home/<USER>';\nimport { useRouter, useRoute } from 'vue-router';\nimport { useI18n } from 'vue-i18n';\nimport { bank_recharge } from '@/api/home/<USER>';\nexport default {\n  name: 'HomeView',\n  setup: function setup() {\n    var _store$state$baseInfo, _route$query;\n    var _useI18n = useI18n(),\n      t = _useI18n.t;\n    var _useRouter = useRouter(),\n      push = _useRouter.push,\n      back = _useRouter.back;\n    var route = useRoute();\n    var _getCurrentInstance = getCurrentInstance(),\n      proxy = _getCurrentInstance.proxy;\n    var paypassword = ref('');\n    var master_bank = ref('');\n    var info = ref({});\n    var currency = ref((_store$state$baseInfo = store.state.baseInfo) === null || _store$state$baseInfo === void 0 ? void 0 : _store$state$baseInfo.currency);\n    var vip_info = ref({});\n    var pay = ref([]);\n    var checked = ref(true);\n    get_recharge({\n      vip_id: (_route$query = route.query) === null || _route$query === void 0 ? void 0 : _route$query.vip\n    }).then(function (res) {\n      var _res$data, _res$data2, _res$data3;\n      vip_info.value = _objectSpread({}, ((_res$data = res.data) === null || _res$data === void 0 ? void 0 : _res$data.vip_info) || {});\n      master_bank.value = ((_res$data2 = res.data) === null || _res$data2 === void 0 ? void 0 : _res$data2.master_bank) || '';\n      pay.value = _toConsumableArray(((_res$data3 = res.data) === null || _res$data3 === void 0 ? void 0 : _res$data3.pay) || {});\n    });\n    var clickLeft = function clickLeft() {\n      push('/self');\n    };\n    var clickRight = function clickRight() {\n      push('/tel');\n    };\n    // next_cz checked\n\n    var onSubmit1 = function onSubmit1() {\n      var _pay$value, _vip_info$value;\n      var info = (_pay$value = pay.value) === null || _pay$value === void 0 ? void 0 : _pay$value.find(function (rr) {\n        return rr.id == checked.value;\n      });\n      var json = {\n        num: (_vip_info$value = vip_info.value) === null || _vip_info$value === void 0 ? void 0 : _vip_info$value.num,\n        payId: info === null || info === void 0 ? void 0 : info.id\n      };\n      bank_recharge(json).then(function (res) {\n        if (res.code === 0) {\n          proxy.$Message({\n            type: 'success',\n            message: res.info\n          });\n          back();\n        } else {\n          proxy.$Message({\n            type: 'error',\n            message: res.info\n          });\n        }\n      });\n    };\n    var onSubmit = function onSubmit() {\n      var _vip_info$value2, _route$query2;\n      if (master_bank.value != 1) {\n        onSubmit1();\n        return false;\n      }\n      var json = {\n        num: (_vip_info$value2 = vip_info.value) === null || _vip_info$value2 === void 0 ? void 0 : _vip_info$value2.num,\n        vip_id: (_route$query2 = route.query) === null || _route$query2 === void 0 ? void 0 : _route$query2.vip\n      };\n      bank_recharge(json).then(function (res) {\n        if (res.code === 0) {\n          proxy.$Message({\n            type: 'success',\n            message: res.info\n          });\n          back();\n        } else {\n          proxy.$Message({\n            type: 'error',\n            message: res.info\n          });\n        }\n      });\n    };\n    return {\n      checked: checked,\n      vip_info: vip_info,\n      pay: pay,\n      master_bank: master_bank,\n      onSubmit: onSubmit,\n      onSubmit1: onSubmit1,\n      clickLeft: clickLeft,\n      clickRight: clickRight,\n      info: info,\n      currency: currency\n    };\n  }\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}