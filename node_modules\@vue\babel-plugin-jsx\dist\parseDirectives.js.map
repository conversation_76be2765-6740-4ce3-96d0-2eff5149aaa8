{"version": 3, "file": "parseDirectives.js", "sourceRoot": "", "sources": ["../src/parseDirectives.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAA,gDAAkC;AAElC,mCAA2C;AAK3C;;;;GAIG;AACH,MAAM,OAAO,GAAG,CAAC,IAAmC,EAAE,EAAE;IACtD,MAAM,QAAQ,GAAG,IAAI;SAClB,GAAG,CAAC,YAAY,CAAC;SACjB,IAAI,CAAC,CAAC,SAAS,EAAE,EAAE;QAClB,IAAI,CAAC,CAAC,CAAC,cAAc,CAAC,SAAS,CAAC,EAAE;YAChC,OAAO,KAAK,CAAC;SACd;QACD,OAAO,CAAC,CAAC,eAAe,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;eACzC,SAAS,CAAC,GAAG,CAAC,MAAM,CAA+B,CAAC,IAAI,CAAC,IAAI,KAAK,MAAM,CAAC;IACjF,CAAC,CAAyC,CAAC;IAE7C,OAAO,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC;AACtD,CAAC,CAAC;AAEF,MAAM,cAAc,GAAG,CAAC,KAAU,EAAY,EAAE,CAAC,CAC/C,CAAC,CAAC,iBAAiB,CAAC,KAAK,CAAC;IACxB,CAAC,CAAC,KAAK,CAAC,QAAQ;SACb,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;SACpD,MAAM,CAAC,OAAO,CAAC;IAClB,CAAC,CAAC,EAAE,CAAC,CAAC;AAEV,MAAM,eAAe,GAAG,CAAC,MAOxB,EAAE,EAAE;;IACH,MAAM,EACJ,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,EAAE,WAAW,GACrC,GAAG,MAAM,CAAC;IACX,MAAM,IAAI,GAAwC,EAAE,CAAC;IACrD,MAAM,IAAI,GAAmB,EAAE,CAAC;IAChC,MAAM,YAAY,GAAkB,EAAE,CAAC;IAEvC,IAAI,aAAa,CAAC;IAClB,IAAI,iBAAiB,CAAC;IACtB,IAAI,kBAAkB,CAAC;IACvB,IAAI,WAAW,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;QACjC,CAAC,aAAa,EAAE,iBAAiB,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAC5D,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;QAC9C,iBAAiB,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;QAC7C,kBAAkB,GAAG,iBAAiB,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;KAC5D;SAAM;QACL,MAAM,mBAAmB,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACnD,aAAa,GAAG,mBAAmB,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC;QAClD,kBAAkB,GAAG,mBAAmB,CAAC;KAC1C;IACD,aAAa,GAAG,aAAa;SAC1B,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC;SACjB,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC;SACjB,OAAO,CAAC,KAAK,EAAE,CAAC,CAAS,EAAE,EAAE,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC;IAElD,IAAI,iBAAiB,EAAE;QACrB,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC,CAAC;KAC/C;IAED,MAAM,SAAS,GAAG,aAAa,KAAK,QAAQ,CAAC;IAC7C,MAAM,QAAQ,GAAG,aAAa,KAAK,OAAO,CAAC;IAC3C,IAAI,QAAQ,IAAI,CAAC,CAAC,CAAC,wBAAwB,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,EAAE;QAC9D,MAAM,IAAI,KAAK,CAAC,oDAAoD,CAAC,CAAC;KACvE;IAED,IAAI,SAAS,IAAI,CAAC,WAAW,EAAE;QAC7B,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAC;KAC/D;IAED,MAAM,aAAa,GAAG,CAAC,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC,QAAQ,CAAC,aAAa,CAAC;WAC7E,CAAC,QAAQ,IAAI,CAAC,WAAW,CAAC,CAAC;IAEhC,IAAI,SAAS,GAAG,kBAAkB,CAAC;IAEnC,IAAI,CAAC,CAAC,iBAAiB,CAAC,KAAK,CAAC,EAAE;QAC9B,MAAM,YAAY,GAAG,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,QAAS,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;QAE3D,YAAY,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YAC/B,IAAI,SAAS,IAAI,CAAC,CAAC,CAAC,iBAAiB,CAAC,OAAO,CAAC,EAAE;gBAC9C,MAAM,IAAI,KAAK,CAAC,sDAAsD,CAAC,CAAC;aACzE;YAED,MAAM,EAAE,QAAQ,EAAE,GAAG,OAA4B,CAAC;YAClD,MAAM,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,CAAC,GAAG,QAAQ,CAAC;YAExC,IAAI,MAAM,IAAI,CAAC,CAAC,CAAC,iBAAiB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,eAAe,CAAC,MAAM,CAAC,EAAE;gBACxE,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBAClB,SAAS,GAAG,cAAc,CAAC,KAA0B,CAAC,CAAC;aACxD;iBAAM,IAAI,CAAC,CAAC,iBAAiB,CAAC,MAAM,CAAC,EAAE;gBACtC,IAAI,CAAC,aAAa,EAAE;oBAClB,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC;iBAC5B;gBACD,SAAS,GAAG,cAAc,CAAC,MAAM,CAAC,CAAC;aACpC;iBAAM,IAAI,CAAC,aAAa,EAAE;gBACzB,oDAAoD;gBACpD,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC;aAC5B;YACD,YAAY,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC;YACtC,IAAI,CAAC,IAAI,CAAC,KAAqB,CAAC,CAAC;QACnC,CAAC,CAAC,CAAC;KACJ;SAAM,IAAI,QAAQ,IAAI,CAAC,aAAa,EAAE;QACrC,0BAA0B;QAC1B,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC;QAC3B,YAAY,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,kBAAkB,CAAC,CAAC,CAAC;KAChD;SAAM;QACL,YAAY,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,kBAAkB,CAAC,CAAC,CAAC;KAChD;IAED,OAAO;QACL,aAAa;QACb,SAAS,EAAE,YAAY;QACvB,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;QACpC,IAAI;QACJ,SAAS,EAAE,aAAa,CAAC,CAAC,CAAC;YACzB,gBAAgB,CAAC,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,aAAa,CAAC;YACjD,IAAI,CAAC,CAAC,CAAC,IAAI,KAAK;YAChB,CAAA,MAAA,YAAY,CAAC,CAAC,CAAC,0CAAE,IAAI;gBACnB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC;gBACjE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;YACX,CAAC,CAAC,CAAA,MAAA,YAAY,CAAC,CAAC,CAAC,0CAAE,IAAI,CAAA,IAAI,CAAC,CAAC,gBAAgB,CAC3C,CAAC,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CACtB,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC,CAAC,cAAc,CAC5B,CAAC,CAAC,UAAU,CAAC,QAAQ,CAAC,EACtB,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CACvB,CACF,CACF;SACF,CAAC,MAAM,CAAC,OAAO,CAAmB,CAAC,CAAC,CAAC,SAAS;KAChD,CAAC;AACJ,CAAC,CAAC;AAEF,MAAM,gBAAgB,GAAG,CACvB,IAA8B,EAC9B,KAAY,EACZ,GAAQ,EACR,aAAqB,EACrB,EAAE;;IACF,IAAI,aAAa,KAAK,MAAM,EAAE;QAC5B,OAAO,IAAA,wBAAgB,EAAC,KAAK,EAAE,OAAO,CAAC,CAAC;KACzC;IACD,IAAI,aAAa,KAAK,OAAO,EAAE;QAC7B,IAAI,UAAU,CAAC;QACf,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,UAA2C,CAAC,CAAC;QACvE,QAAS,GAAuB,CAAC,KAAK,EAAE;YACtC,KAAK,QAAQ;gBACX,UAAU,GAAG,IAAA,wBAAgB,EAAC,KAAK,EAAE,cAAc,CAAC,CAAC;gBACrD,MAAM;YACR,KAAK,UAAU;gBACb,UAAU,GAAG,IAAA,wBAAgB,EAAC,KAAK,EAAE,YAAY,CAAC,CAAC;gBACnD,MAAM;YACR;gBACE,IAAI,CAAC,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;oBACpC,QAAQ,MAAC,IAAwB,0CAAE,KAAK,EAAE;wBACxC,KAAK,UAAU;4BACb,UAAU,GAAG,IAAA,wBAAgB,EAAC,KAAK,EAAE,gBAAgB,CAAC,CAAC;4BACvD,MAAM;wBACR,KAAK,OAAO;4BACV,UAAU,GAAG,IAAA,wBAAgB,EAAC,KAAK,EAAE,aAAa,CAAC,CAAC;4BACpD,MAAM;wBACR;4BACE,UAAU,GAAG,IAAA,wBAAgB,EAAC,KAAK,EAAE,YAAY,CAAC,CAAC;qBACtD;iBACF;qBAAM;oBACL,UAAU,GAAG,IAAA,wBAAgB,EAAC,KAAK,EAAE,eAAe,CAAC,CAAC;iBACvD;SACJ;QACD,OAAO,UAAU,CAAC;KACnB;IACD,OAAO,CAAC,CAAC,cAAc,CACrB,IAAA,wBAAgB,EAAC,KAAK,EAAE,kBAAkB,CAAC,EAAE;QAC3C,CAAC,CAAC,aAAa,CAAC,aAAa,CAAC;KAC/B,CACF,CAAC;AACJ,CAAC,CAAC;AAEF,kBAAe,eAAe,CAAC"}