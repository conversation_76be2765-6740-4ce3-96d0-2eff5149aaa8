{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, createCommentVNode as _createCommentVNode, createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, pushScopeId as _pushScopeId, popScopeId as _popScopeId } from \"vue\";\nvar _withScopeId = function _withScopeId(n) {\n  return _pushScopeId(\"data-v-57a46677\"), n = n(), _popScopeId(), n;\n};\nvar _hoisted_1 = {\n  class: \"home\"\n};\nvar _hoisted_2 = [\"innerHTML\"];\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_van_nav_bar = _resolveComponent(\"van-nav-bar\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_van_nav_bar, {\n    title: $setup.title,\n    \"left-arrow\": \"\",\n    onClickLeft: _cache[0] || (_cache[0] = function ($event) {\n      return _ctx.$router.go(-1);\n    })\n  }, null, 8 /* PROPS */, [\"title\"]), _createCommentVNode(\" <van-nav-bar\\r\\n\\t\\t     :title=\\\"$t('msg.jiaoyi')\\\"\\r\\n\\t\\t\\t background=\\\"#ffffff\\\"\\r\\n\\t\\t\\t title-style=\\\"color: black; font-size: 16px;\\\"\\r\\n\\t\\t   ></van-nav-bar> \"), _createElementVNode(\"div\", {\n    class: \"content\",\n    innerHTML: $setup.content\n  }, null, 8 /* PROPS */, _hoisted_2)]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_van_nav_bar", "title", "$setup", "onClickLeft", "_cache", "$event", "_ctx", "$router", "go", "_createCommentVNode", "_createElementVNode", "innerHTML", "content"], "sources": ["C:\\Users\\<USER>\\Downloads\\Telegram Desktop\\vue3_3.0\\vue3_3.0\\src\\views\\index\\components\\content.vue"], "sourcesContent": ["<template>\r\n    <div class=\"home\">\r\n        <van-nav-bar :title=\"title\"  left-arrow @click-left=\"$router.go(-1)\"></van-nav-bar>\r\n\r\n        <!-- <van-nav-bar\r\n\t\t     :title=\"$t('msg.jiaoyi')\"\r\n\t\t\t background=\"#ffffff\"\r\n\t\t\t title-style=\"color: black; font-size: 16px;\"\r\n\t\t   ></van-nav-bar> -->\r\n\r\n        <div class=\"content\" v-html=\"content\"></div>\r\n    </div>\r\n</template>\r\n<script>\r\n    import { reactive, ref,getCurrentInstance } from 'vue';\r\n    import store from '@/store/index'\r\n    import { useRouter,useRoute } from 'vue-router';\r\n    import {getdetailbyid} from '@/api/home/<USER>'\r\n    export default {\r\n        name: 'HomeView',\r\n        setup() {\r\n            const { push } = useRouter();\r\n            const route = useRoute();\r\n            const id = route.query?.id\r\n            const title = route.query?.title\r\n            const content = ref('')\r\n\r\n            getdetailbyid(route.query?.id).then(res => {\r\n                content.value = res.data?.content\r\n            })\r\n\r\n            return {id,title,content}\r\n        }\r\n    }\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n@import '@/styles/theme.scss';\r\n\r\n\r\n// .content{\r\n//     background: #f5f5f5;\r\n// }\r\n\r\n.home{\r\n    background: #f5f5f5;\r\n    overflow: hidden !important;\r\n    :deep(.van-nav-bar){\r\n        background-color: #fff;\r\n        color: #000;\r\n        .van-nav-bar__left{\r\n            .van-icon{\r\n                color: #000;\r\n            }\r\n        }\r\n        .van-nav-bar__title{\r\n            color: #000;\r\n        }\r\n        .van-nav-bar__right{\r\n            img{\r\n                height: 42px;\r\n            }\r\n        }\r\n    }\r\n    .content{\r\n        margin: 40px 30px;\r\n        box-shadow: $shadow;\r\n        text-align: left;\r\n        padding: 40px 30px;\r\n        font-size: 30px;\r\n        color: #333;\r\n        line-height: 1.8;\r\n        overflow: auto;\r\n        border-radius: 12px;\r\n        background-color: #fff;\r\n        flex: 1;\r\n    }\r\n}\r\n\r\nimg{\r\n    width: 100% !important;\r\n}\r\n\r\n</style>"], "mappings": ";;;;;EACSA,KAAK,EAAC;AAAM;;;;uBAAjBC,mBAAA,CAUM,OAVNC,UAUM,GATFC,YAAA,CAAmFC,sBAAA;IAArEC,KAAK,EAAEC,MAAA,CAAAD,KAAK;IAAG,YAAU,EAAV,EAAU;IAAEE,WAAU,EAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAAEC,IAAA,CAAAC,OAAO,CAACC,EAAE;IAAA;sCAE/DC,mBAAA,8KAIgB,EAEhBC,mBAAA,CAA4C;IAAvCd,KAAK,EAAC,SAAS;IAACe,SAAgB,EAART,MAAA,CAAAU"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}