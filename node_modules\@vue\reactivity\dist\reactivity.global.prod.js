var VueReactivity=function(t){"use strict";function e(t,e){const n=Object.create(null),s=t.split(",");for(let i=0;i<s.length;i++)n[s[i]]=!0;return e?t=>!!n[t.toLowerCase()]:t=>!!n[t]}const n=()=>{},s=Object.assign,i=Object.prototype.hasOwnProperty,r=(t,e)=>i.call(t,e),c=Array.isArray,o=t=>"[object Map]"===h(t),u=t=>"symbol"==typeof t,a=t=>null!==t&&"object"==typeof t,l=Object.prototype.toString,h=t=>l.call(t),f=t=>"string"==typeof t&&"NaN"!==t&&"-"!==t[0]&&""+parseInt(t,10)===t,_=(t,e)=>!Object.is(t,e);let d;const p=[];class v{constructor(t=!1){this.active=!0,this.effects=[],this.cleanups=[],!t&&d&&(this.parent=d,this.index=(d.scopes||(d.scopes=[])).push(this)-1)}run(t){if(this.active)try{return this.on(),t()}finally{this.off()}}on(){this.active&&(p.push(this),d=this)}off(){this.active&&(p.pop(),d=p[p.length-1])}stop(t){if(this.active){if(this.effects.forEach((t=>t.stop())),this.cleanups.forEach((t=>t())),this.scopes&&this.scopes.forEach((t=>t.stop(!0))),this.parent&&!t){const t=this.parent.scopes.pop();t&&t!==this&&(this.parent.scopes[this.index]=t,t.index=this.index)}this.active=!1}}}function g(t,e){(e=e||d)&&e.active&&e.effects.push(t)}const y=t=>{const e=new Set(t);return e.w=0,e.n=0,e},w=t=>(t.w&m)>0,R=t=>(t.n&m)>0,b=new WeakMap;let k=0,m=1;const E=[];let S;const j=Symbol(""),O=Symbol("");class x{constructor(t,e=null,n){this.fn=t,this.scheduler=e,this.active=!0,this.deps=[],g(this,n)}run(){if(!this.active)return this.fn();if(!E.includes(this))try{return E.push(S=this),W(),m=1<<++k,k<=30?(({deps:t})=>{if(t.length)for(let e=0;e<t.length;e++)t[e].w|=m})(this):P(this),this.fn()}finally{k<=30&&(t=>{const{deps:e}=t;if(e.length){let n=0;for(let s=0;s<e.length;s++){const i=e[s];w(i)&&!R(i)?i.delete(t):e[n++]=i,i.w&=~m,i.n&=~m}e.length=n}})(this),m=1<<--k,A(),E.pop();const t=E.length;S=t>0?E[t-1]:void 0}}stop(){this.active&&(P(this),this.onStop&&this.onStop(),this.active=!1)}}function P(t){const{deps:e}=t;if(e.length){for(let n=0;n<e.length;n++)e[n].delete(t);e.length=0}}let M=!0;const z=[];function V(){z.push(M),M=!1}function W(){z.push(M),M=!0}function A(){const t=z.pop();M=void 0===t||t}function T(t,e,n){if(!N())return;let s=b.get(t);s||b.set(t,s=new Map);let i=s.get(n);i||s.set(n,i=y()),C(i)}function N(){return M&&void 0!==S}function C(t,e){let n=!1;k<=30?R(t)||(t.n|=m,n=!w(t)):n=!t.has(S),n&&(t.add(S),S.deps.push(t))}function I(t,e,n,s,i,r){const u=b.get(t);if(!u)return;let a=[];if("clear"===e)a=[...u.values()];else if("length"===n&&c(t))u.forEach(((t,e)=>{("length"===e||e>=s)&&a.push(t)}));else switch(void 0!==n&&a.push(u.get(n)),e){case"add":c(t)?f(n)&&a.push(u.get("length")):(a.push(u.get(j)),o(t)&&a.push(u.get(O)));break;case"delete":c(t)||(a.push(u.get(j)),o(t)&&a.push(u.get(O)));break;case"set":o(t)&&a.push(u.get(j))}if(1===a.length)a[0]&&K(a[0]);else{const t=[];for(const e of a)e&&t.push(...e);K(y(t))}}function K(t,e){for(const n of c(t)?t:[...t])(n!==S||n.allowRecurse)&&(n.scheduler?n.scheduler():n.run())}const B=e("__proto__,__v_isRef,__isVue"),D=new Set(Object.getOwnPropertyNames(Symbol).map((t=>Symbol[t])).filter(u)),L=J(),Y=J(!1,!0),q=J(!0),F=J(!0,!0),G=H();function H(){const t={};return["includes","indexOf","lastIndexOf"].forEach((e=>{t[e]=function(...t){const n=Vt(this);for(let e=0,i=this.length;e<i;e++)T(n,0,e+"");const s=n[e](...t);return-1===s||!1===s?n[e](...t.map(Vt)):s}})),["push","pop","shift","unshift","splice"].forEach((e=>{t[e]=function(...t){V();const n=Vt(this)[e].apply(this,t);return A(),n}})),t}function J(t=!1,e=!1){return function(n,s,i){if("__v_isReactive"===s)return!t;if("__v_isReadonly"===s)return t;if("__v_raw"===s&&i===(t?e?St:Et:e?mt:kt).get(n))return n;const o=c(n);if(!t&&o&&r(G,s))return Reflect.get(G,s,i);const l=Reflect.get(n,s,i);if(u(s)?D.has(s):B(s))return l;if(t||T(n,0,s),e)return l;if(Ct(l)){return!o||!f(s)?l.value:l}return a(l)?t?xt(l):Ot(l):l}}function Q(t=!1){return function(e,n,s,i){let o=e[n];if(!t&&!zt(s)&&(s=Vt(s),o=Vt(o),!c(e)&&Ct(o)&&!Ct(s)))return o.value=s,!0;const u=c(e)&&f(n)?Number(n)<e.length:r(e,n),a=Reflect.set(e,n,s,i);return e===Vt(i)&&(u?_(s,o)&&I(e,"set",n,s):I(e,"add",n,s)),a}}const U={get:L,set:Q(),deleteProperty:function(t,e){const n=r(t,e),s=Reflect.deleteProperty(t,e);return s&&n&&I(t,"delete",e,void 0),s},has:function(t,e){const n=Reflect.has(t,e);return u(e)&&D.has(e)||T(t,0,e),n},ownKeys:function(t){return T(t,0,c(t)?"length":j),Reflect.ownKeys(t)}},X={get:q,set:(t,e)=>!0,deleteProperty:(t,e)=>!0},Z=s({},U,{get:Y,set:Q(!0)}),$=s({},X,{get:F}),tt=t=>t,et=t=>Reflect.getPrototypeOf(t);function nt(t,e,n=!1,s=!1){const i=Vt(t=t.__v_raw),r=Vt(e);e!==r&&!n&&T(i,0,e),!n&&T(i,0,r);const{has:c}=et(i),o=s?tt:n?At:Wt;return c.call(i,e)?o(t.get(e)):c.call(i,r)?o(t.get(r)):void(t!==i&&t.get(e))}function st(t,e=!1){const n=this.__v_raw,s=Vt(n),i=Vt(t);return t!==i&&!e&&T(s,0,t),!e&&T(s,0,i),t===i?n.has(t):n.has(t)||n.has(i)}function it(t,e=!1){return t=t.__v_raw,!e&&T(Vt(t),0,j),Reflect.get(t,"size",t)}function rt(t){t=Vt(t);const e=Vt(this);return et(e).has.call(e,t)||(e.add(t),I(e,"add",t,t)),this}function ct(t,e){e=Vt(e);const n=Vt(this),{has:s,get:i}=et(n);let r=s.call(n,t);r||(t=Vt(t),r=s.call(n,t));const c=i.call(n,t);return n.set(t,e),r?_(e,c)&&I(n,"set",t,e):I(n,"add",t,e),this}function ot(t){const e=Vt(this),{has:n,get:s}=et(e);let i=n.call(e,t);i||(t=Vt(t),i=n.call(e,t)),s&&s.call(e,t);const r=e.delete(t);return i&&I(e,"delete",t,void 0),r}function ut(){const t=Vt(this),e=0!==t.size,n=t.clear();return e&&I(t,"clear",void 0,void 0),n}function at(t,e){return function(n,s){const i=this,r=i.__v_raw,c=Vt(r),o=e?tt:t?At:Wt;return!t&&T(c,0,j),r.forEach(((t,e)=>n.call(s,o(t),o(e),i)))}}function lt(t,e,n){return function(...s){const i=this.__v_raw,r=Vt(i),c=o(r),u="entries"===t||t===Symbol.iterator&&c,a="keys"===t&&c,l=i[t](...s),h=n?tt:e?At:Wt;return!e&&T(r,0,a?O:j),{next(){const{value:t,done:e}=l.next();return e?{value:t,done:e}:{value:u?[h(t[0]),h(t[1])]:h(t),done:e}},[Symbol.iterator](){return this}}}}function ht(t){return function(...e){return"delete"!==t&&this}}function ft(){const t={get(t){return nt(this,t)},get size(){return it(this)},has:st,add:rt,set:ct,delete:ot,clear:ut,forEach:at(!1,!1)},e={get(t){return nt(this,t,!1,!0)},get size(){return it(this)},has:st,add:rt,set:ct,delete:ot,clear:ut,forEach:at(!1,!0)},n={get(t){return nt(this,t,!0)},get size(){return it(this,!0)},has(t){return st.call(this,t,!0)},add:ht("add"),set:ht("set"),delete:ht("delete"),clear:ht("clear"),forEach:at(!0,!1)},s={get(t){return nt(this,t,!0,!0)},get size(){return it(this,!0)},has(t){return st.call(this,t,!0)},add:ht("add"),set:ht("set"),delete:ht("delete"),clear:ht("clear"),forEach:at(!0,!0)};return["keys","values","entries",Symbol.iterator].forEach((i=>{t[i]=lt(i,!1,!1),n[i]=lt(i,!0,!1),e[i]=lt(i,!1,!0),s[i]=lt(i,!0,!0)})),[t,n,e,s]}const[_t,dt,pt,vt]=ft();function gt(t,e){const n=e?t?vt:pt:t?dt:_t;return(e,s,i)=>"__v_isReactive"===s?!t:"__v_isReadonly"===s?t:"__v_raw"===s?e:Reflect.get(r(n,s)&&s in e?n:e,s,i)}const yt={get:gt(!1,!1)},wt={get:gt(!1,!0)},Rt={get:gt(!0,!1)},bt={get:gt(!0,!0)},kt=new WeakMap,mt=new WeakMap,Et=new WeakMap,St=new WeakMap;function jt(t){return t.__v_skip||!Object.isExtensible(t)?0:function(t){switch(t){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}((t=>h(t).slice(8,-1))(t))}function Ot(t){return t&&t.__v_isReadonly?t:Pt(t,!1,U,yt,kt)}function xt(t){return Pt(t,!0,X,Rt,Et)}function Pt(t,e,n,s,i){if(!a(t))return t;if(t.__v_raw&&(!e||!t.__v_isReactive))return t;const r=i.get(t);if(r)return r;const c=jt(t);if(0===c)return t;const o=new Proxy(t,2===c?s:n);return i.set(t,o),o}function Mt(t){return zt(t)?Mt(t.__v_raw):!(!t||!t.__v_isReactive)}function zt(t){return!(!t||!t.__v_isReadonly)}function Vt(t){const e=t&&t.__v_raw;return e?Vt(e):t}const Wt=t=>a(t)?Ot(t):t,At=t=>a(t)?xt(t):t;function Tt(t){N()&&((t=Vt(t)).dep||(t.dep=y()),C(t.dep))}function Nt(t,e){(t=Vt(t)).dep&&K(t.dep)}function Ct(t){return Boolean(t&&!0===t.__v_isRef)}function It(t,e){return Ct(t)?t:new Kt(t,e)}class Kt{constructor(t,e){this._shallow=e,this.dep=void 0,this.__v_isRef=!0,this._rawValue=e?t:Vt(t),this._value=e?t:Wt(t)}get value(){return Tt(this),this._value}set value(t){t=this._shallow?t:Vt(t),_(t,this._rawValue)&&(this._rawValue=t,this._value=this._shallow?t:Wt(t),Nt(this))}}function Bt(t){return Ct(t)?t.value:t}const Dt={get:(t,e,n)=>Bt(Reflect.get(t,e,n)),set:(t,e,n,s)=>{const i=t[e];return Ct(i)&&!Ct(n)?(i.value=n,!0):Reflect.set(t,e,n,s)}};class Lt{constructor(t){this.dep=void 0,this.__v_isRef=!0;const{get:e,set:n}=t((()=>Tt(this)),(()=>Nt(this)));this._get=e,this._set=n}get value(){return this._get()}set value(t){this._set(t)}}class Yt{constructor(t,e,n){this._object=t,this._key=e,this._defaultValue=n,this.__v_isRef=!0}get value(){const t=this._object[this._key];return void 0===t?this._defaultValue:t}set value(t){this._object[this._key]=t}}function qt(t,e,n){const s=t[e];return Ct(s)?s:new Yt(t,e,n)}class Ft{constructor(t,e,n){this._setter=e,this.dep=void 0,this._dirty=!0,this.__v_isRef=!0,this.effect=new x(t,(()=>{this._dirty||(this._dirty=!0,Nt(this))})),this.__v_isReadonly=n}get value(){const t=Vt(this);return Tt(t),t._dirty&&(t._dirty=!1,t._value=t.effect.run()),t._value}set value(t){this._setter(t)}}var Gt;const Ht=Promise.resolve(),Jt=[];let Qt=!1;const Ut=()=>{for(let t=0;t<Jt.length;t++)Jt[t]();Jt.length=0,Qt=!1};class Xt{constructor(t){let e;this.dep=void 0,this._dirty=!0,this.__v_isRef=!0,this[Gt]=!0;let n=!1,s=!1;this.effect=new x(t,(t=>{if(this.dep){if(t)e=this._value,n=!0;else if(!s){const t=n?e:this._value;s=!0,n=!1,Jt.push((()=>{this.effect.active&&this._get()!==t&&Nt(this),s=!1})),Qt||(Qt=!0,Ht.then(Ut))}for(const t of this.dep)t.computed&&t.scheduler(!0)}this._dirty=!0})),this.effect.computed=!0}_get(){return this._dirty?(this._dirty=!1,this._value=this.effect.run()):this._value}get value(){return Tt(this),Vt(this)._get()}}return Gt="__v_isReadonly",t.EffectScope=v,t.ITERATE_KEY=j,t.ReactiveEffect=x,t.computed=function(t,e){let s,i;const r="function"==typeof t;return r?(s=t,i=n):(s=t.get,i=t.set),new Ft(s,i,r||!i)},t.customRef=function(t){return new Lt(t)},t.deferredComputed=function(t){return new Xt(t)},t.effect=function(t,e){t.effect&&(t=t.effect.fn);const n=new x(t);e&&(s(n,e),e.scope&&g(n,e.scope)),e&&e.lazy||n.run();const i=n.run.bind(n);return i.effect=n,i},t.effectScope=function(t){return new v(t)},t.enableTracking=W,t.getCurrentScope=function(){return d},t.isProxy=function(t){return Mt(t)||zt(t)},t.isReactive=Mt,t.isReadonly=zt,t.isRef=Ct,t.markRaw=function(t){return((t,e,n)=>{Object.defineProperty(t,e,{configurable:!0,enumerable:!1,value:n})})(t,"__v_skip",!0),t},t.onScopeDispose=function(t){d&&d.cleanups.push(t)},t.pauseTracking=V,t.proxyRefs=function(t){return Mt(t)?t:new Proxy(t,Dt)},t.reactive=Ot,t.readonly=xt,t.ref=function(t){return It(t,!1)},t.resetTracking=A,t.shallowReactive=function(t){return Pt(t,!1,Z,wt,mt)},t.shallowReadonly=function(t){return Pt(t,!0,$,bt,St)},t.shallowRef=function(t){return It(t,!0)},t.stop=function(t){t.effect.stop()},t.toRaw=Vt,t.toRef=qt,t.toRefs=function(t){const e=c(t)?new Array(t.length):{};for(const n in t)e[n]=qt(t,n);return e},t.track=T,t.trigger=I,t.triggerRef=function(t){Nt(t)},t.unref=Bt,Object.defineProperty(t,"__esModule",{value:!0}),t}({});
