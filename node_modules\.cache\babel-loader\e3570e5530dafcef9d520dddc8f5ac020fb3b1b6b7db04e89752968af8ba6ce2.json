{"ast": null, "code": "// ../../node_modules/.pnpm/@popperjs+core@2.11.6/node_modules/@popperjs/core/lib/dom-utils/getWindow.js\nfunction getWindow(node) {\n  if (node == null) {\n    return window;\n  }\n  if (node.toString() !== \"[object Window]\") {\n    var ownerDocument = node.ownerDocument;\n    return ownerDocument ? ownerDocument.defaultView || window : window;\n  }\n  return node;\n}\n\n// ../../node_modules/.pnpm/@popperjs+core@2.11.6/node_modules/@popperjs/core/lib/dom-utils/instanceOf.js\nfunction isElement(node) {\n  var OwnElement = getWindow(node).Element;\n  return node instanceof OwnElement || node instanceof Element;\n}\nfunction isHTMLElement(node) {\n  var OwnElement = getWindow(node).HTMLElement;\n  return node instanceof OwnElement || node instanceof HTMLElement;\n}\nfunction isShadowRoot(node) {\n  if (typeof ShadowRoot === \"undefined\") {\n    return false;\n  }\n  var OwnElement = getWindow(node).ShadowRoot;\n  return node instanceof OwnElement || node instanceof ShadowRoot;\n}\n\n// ../../node_modules/.pnpm/@popperjs+core@2.11.6/node_modules/@popperjs/core/lib/utils/math.js\nvar round = Math.round;\n\n// ../../node_modules/.pnpm/@popperjs+core@2.11.6/node_modules/@popperjs/core/lib/utils/userAgent.js\nfunction getUAString() {\n  var uaData = navigator.userAgentData;\n  if (uaData != null && uaData.brands) {\n    return uaData.brands.map(function (item) {\n      return item.brand + \"/\" + item.version;\n    }).join(\" \");\n  }\n  return navigator.userAgent;\n}\n\n// ../../node_modules/.pnpm/@popperjs+core@2.11.6/node_modules/@popperjs/core/lib/dom-utils/isLayoutViewport.js\nfunction isLayoutViewport() {\n  return !/^((?!chrome|android).)*safari/i.test(getUAString());\n}\n\n// ../../node_modules/.pnpm/@popperjs+core@2.11.6/node_modules/@popperjs/core/lib/dom-utils/getBoundingClientRect.js\nfunction getBoundingClientRect(element, includeScale, isFixedStrategy) {\n  if (includeScale === void 0) {\n    includeScale = false;\n  }\n  if (isFixedStrategy === void 0) {\n    isFixedStrategy = false;\n  }\n  var clientRect = element.getBoundingClientRect();\n  var scaleX = 1;\n  var scaleY = 1;\n  if (includeScale && isHTMLElement(element)) {\n    scaleX = element.offsetWidth > 0 ? round(clientRect.width) / element.offsetWidth || 1 : 1;\n    scaleY = element.offsetHeight > 0 ? round(clientRect.height) / element.offsetHeight || 1 : 1;\n  }\n  var _ref = isElement(element) ? getWindow(element) : window,\n    visualViewport = _ref.visualViewport;\n  var addVisualOffsets = !isLayoutViewport() && isFixedStrategy;\n  var x = (clientRect.left + (addVisualOffsets && visualViewport ? visualViewport.offsetLeft : 0)) / scaleX;\n  var y = (clientRect.top + (addVisualOffsets && visualViewport ? visualViewport.offsetTop : 0)) / scaleY;\n  var width = clientRect.width / scaleX;\n  var height = clientRect.height / scaleY;\n  return {\n    width: width,\n    height: height,\n    top: y,\n    right: x + width,\n    bottom: y + height,\n    left: x,\n    x: x,\n    y: y\n  };\n}\n\n// ../../node_modules/.pnpm/@popperjs+core@2.11.6/node_modules/@popperjs/core/lib/dom-utils/getWindowScroll.js\nfunction getWindowScroll(node) {\n  var win = getWindow(node);\n  var scrollLeft = win.pageXOffset;\n  var scrollTop = win.pageYOffset;\n  return {\n    scrollLeft: scrollLeft,\n    scrollTop: scrollTop\n  };\n}\n\n// ../../node_modules/.pnpm/@popperjs+core@2.11.6/node_modules/@popperjs/core/lib/dom-utils/getHTMLElementScroll.js\nfunction getHTMLElementScroll(element) {\n  return {\n    scrollLeft: element.scrollLeft,\n    scrollTop: element.scrollTop\n  };\n}\n\n// ../../node_modules/.pnpm/@popperjs+core@2.11.6/node_modules/@popperjs/core/lib/dom-utils/getNodeScroll.js\nfunction getNodeScroll(node) {\n  if (node === getWindow(node) || !isHTMLElement(node)) {\n    return getWindowScroll(node);\n  } else {\n    return getHTMLElementScroll(node);\n  }\n}\n\n// ../../node_modules/.pnpm/@popperjs+core@2.11.6/node_modules/@popperjs/core/lib/dom-utils/getNodeName.js\nfunction getNodeName(element) {\n  return element ? (element.nodeName || \"\").toLowerCase() : null;\n}\n\n// ../../node_modules/.pnpm/@popperjs+core@2.11.6/node_modules/@popperjs/core/lib/dom-utils/getDocumentElement.js\nfunction getDocumentElement(element) {\n  return ((isElement(element) ? element.ownerDocument : element.document) || window.document).documentElement;\n}\n\n// ../../node_modules/.pnpm/@popperjs+core@2.11.6/node_modules/@popperjs/core/lib/dom-utils/getWindowScrollBarX.js\nfunction getWindowScrollBarX(element) {\n  return getBoundingClientRect(getDocumentElement(element)).left + getWindowScroll(element).scrollLeft;\n}\n\n// ../../node_modules/.pnpm/@popperjs+core@2.11.6/node_modules/@popperjs/core/lib/dom-utils/getComputedStyle.js\nfunction getComputedStyle(element) {\n  return getWindow(element).getComputedStyle(element);\n}\n\n// ../../node_modules/.pnpm/@popperjs+core@2.11.6/node_modules/@popperjs/core/lib/dom-utils/isScrollParent.js\nfunction isScrollParent(element) {\n  var _getComputedStyle = getComputedStyle(element),\n    overflow = _getComputedStyle.overflow,\n    overflowX = _getComputedStyle.overflowX,\n    overflowY = _getComputedStyle.overflowY;\n  return /auto|scroll|overlay|hidden/.test(overflow + overflowY + overflowX);\n}\n\n// ../../node_modules/.pnpm/@popperjs+core@2.11.6/node_modules/@popperjs/core/lib/dom-utils/getCompositeRect.js\nfunction isElementScaled(element) {\n  var rect = element.getBoundingClientRect();\n  var scaleX = round(rect.width) / element.offsetWidth || 1;\n  var scaleY = round(rect.height) / element.offsetHeight || 1;\n  return scaleX !== 1 || scaleY !== 1;\n}\nfunction getCompositeRect(elementOrVirtualElement, offsetParent, isFixed) {\n  if (isFixed === void 0) {\n    isFixed = false;\n  }\n  var isOffsetParentAnElement = isHTMLElement(offsetParent);\n  var offsetParentIsScaled = isHTMLElement(offsetParent) && isElementScaled(offsetParent);\n  var documentElement = getDocumentElement(offsetParent);\n  var rect = getBoundingClientRect(elementOrVirtualElement, offsetParentIsScaled, isFixed);\n  var scroll = {\n    scrollLeft: 0,\n    scrollTop: 0\n  };\n  var offsets = {\n    x: 0,\n    y: 0\n  };\n  if (isOffsetParentAnElement || !isOffsetParentAnElement && !isFixed) {\n    if (getNodeName(offsetParent) !== \"body\" || isScrollParent(documentElement)) {\n      scroll = getNodeScroll(offsetParent);\n    }\n    if (isHTMLElement(offsetParent)) {\n      offsets = getBoundingClientRect(offsetParent, true);\n      offsets.x += offsetParent.clientLeft;\n      offsets.y += offsetParent.clientTop;\n    } else if (documentElement) {\n      offsets.x = getWindowScrollBarX(documentElement);\n    }\n  }\n  return {\n    x: rect.left + scroll.scrollLeft - offsets.x,\n    y: rect.top + scroll.scrollTop - offsets.y,\n    width: rect.width,\n    height: rect.height\n  };\n}\n\n// ../../node_modules/.pnpm/@popperjs+core@2.11.6/node_modules/@popperjs/core/lib/dom-utils/getLayoutRect.js\nfunction getLayoutRect(element) {\n  var clientRect = getBoundingClientRect(element);\n  var width = element.offsetWidth;\n  var height = element.offsetHeight;\n  if (Math.abs(clientRect.width - width) <= 1) {\n    width = clientRect.width;\n  }\n  if (Math.abs(clientRect.height - height) <= 1) {\n    height = clientRect.height;\n  }\n  return {\n    x: element.offsetLeft,\n    y: element.offsetTop,\n    width: width,\n    height: height\n  };\n}\n\n// ../../node_modules/.pnpm/@popperjs+core@2.11.6/node_modules/@popperjs/core/lib/dom-utils/getParentNode.js\nfunction getParentNode(element) {\n  if (getNodeName(element) === \"html\") {\n    return element;\n  }\n  return element.assignedSlot || element.parentNode || (isShadowRoot(element) ? element.host : null) || getDocumentElement(element);\n}\n\n// ../../node_modules/.pnpm/@popperjs+core@2.11.6/node_modules/@popperjs/core/lib/dom-utils/getScrollParent.js\nfunction getScrollParent(node) {\n  if ([\"html\", \"body\", \"#document\"].indexOf(getNodeName(node)) >= 0) {\n    return node.ownerDocument.body;\n  }\n  if (isHTMLElement(node) && isScrollParent(node)) {\n    return node;\n  }\n  return getScrollParent(getParentNode(node));\n}\n\n// ../../node_modules/.pnpm/@popperjs+core@2.11.6/node_modules/@popperjs/core/lib/dom-utils/listScrollParents.js\nfunction listScrollParents(element, list) {\n  var _element$ownerDocumen;\n  if (list === void 0) {\n    list = [];\n  }\n  var scrollParent = getScrollParent(element);\n  var isBody = scrollParent === ((_element$ownerDocumen = element.ownerDocument) == null ? void 0 : _element$ownerDocumen.body);\n  var win = getWindow(scrollParent);\n  var target = isBody ? [win].concat(win.visualViewport || [], isScrollParent(scrollParent) ? scrollParent : []) : scrollParent;\n  var updatedList = list.concat(target);\n  return isBody ? updatedList : updatedList.concat(listScrollParents(getParentNode(target)));\n}\n\n// ../../node_modules/.pnpm/@popperjs+core@2.11.6/node_modules/@popperjs/core/lib/dom-utils/isTableElement.js\nfunction isTableElement(element) {\n  return [\"table\", \"td\", \"th\"].indexOf(getNodeName(element)) >= 0;\n}\n\n// ../../node_modules/.pnpm/@popperjs+core@2.11.6/node_modules/@popperjs/core/lib/dom-utils/getOffsetParent.js\nfunction getTrueOffsetParent(element) {\n  if (!isHTMLElement(element) || getComputedStyle(element).position === \"fixed\") {\n    return null;\n  }\n  return element.offsetParent;\n}\nfunction getContainingBlock(element) {\n  var isFirefox = /firefox/i.test(getUAString());\n  var isIE = /Trident/i.test(getUAString());\n  if (isIE && isHTMLElement(element)) {\n    var elementCss = getComputedStyle(element);\n    if (elementCss.position === \"fixed\") {\n      return null;\n    }\n  }\n  var currentNode = getParentNode(element);\n  if (isShadowRoot(currentNode)) {\n    currentNode = currentNode.host;\n  }\n  while (isHTMLElement(currentNode) && [\"html\", \"body\"].indexOf(getNodeName(currentNode)) < 0) {\n    var css = getComputedStyle(currentNode);\n    if (css.transform !== \"none\" || css.perspective !== \"none\" || css.contain === \"paint\" || [\"transform\", \"perspective\"].indexOf(css.willChange) !== -1 || isFirefox && css.willChange === \"filter\" || isFirefox && css.filter && css.filter !== \"none\") {\n      return currentNode;\n    } else {\n      currentNode = currentNode.parentNode;\n    }\n  }\n  return null;\n}\nfunction getOffsetParent(element) {\n  var window2 = getWindow(element);\n  var offsetParent = getTrueOffsetParent(element);\n  while (offsetParent && isTableElement(offsetParent) && getComputedStyle(offsetParent).position === \"static\") {\n    offsetParent = getTrueOffsetParent(offsetParent);\n  }\n  if (offsetParent && (getNodeName(offsetParent) === \"html\" || getNodeName(offsetParent) === \"body\" && getComputedStyle(offsetParent).position === \"static\")) {\n    return window2;\n  }\n  return offsetParent || getContainingBlock(element) || window2;\n}\n\n// ../../node_modules/.pnpm/@popperjs+core@2.11.6/node_modules/@popperjs/core/lib/enums.js\nvar top = \"top\";\nvar bottom = \"bottom\";\nvar right = \"right\";\nvar left = \"left\";\nvar auto = \"auto\";\nvar basePlacements = [top, bottom, right, left];\nvar start = \"start\";\nvar end = \"end\";\nvar placements = /* @__PURE__ */[].concat(basePlacements, [auto]).reduce(function (acc, placement) {\n  return acc.concat([placement, placement + \"-\" + start, placement + \"-\" + end]);\n}, []);\nvar beforeRead = \"beforeRead\";\nvar read = \"read\";\nvar afterRead = \"afterRead\";\nvar beforeMain = \"beforeMain\";\nvar main = \"main\";\nvar afterMain = \"afterMain\";\nvar beforeWrite = \"beforeWrite\";\nvar write = \"write\";\nvar afterWrite = \"afterWrite\";\nvar modifierPhases = [beforeRead, read, afterRead, beforeMain, main, afterMain, beforeWrite, write, afterWrite];\n\n// ../../node_modules/.pnpm/@popperjs+core@2.11.6/node_modules/@popperjs/core/lib/utils/orderModifiers.js\nfunction order(modifiers) {\n  var map = /* @__PURE__ */new Map();\n  var visited = /* @__PURE__ */new Set();\n  var result = [];\n  modifiers.forEach(function (modifier) {\n    map.set(modifier.name, modifier);\n  });\n  function sort(modifier) {\n    visited.add(modifier.name);\n    var requires = [].concat(modifier.requires || [], modifier.requiresIfExists || []);\n    requires.forEach(function (dep) {\n      if (!visited.has(dep)) {\n        var depModifier = map.get(dep);\n        if (depModifier) {\n          sort(depModifier);\n        }\n      }\n    });\n    result.push(modifier);\n  }\n  modifiers.forEach(function (modifier) {\n    if (!visited.has(modifier.name)) {\n      sort(modifier);\n    }\n  });\n  return result;\n}\nfunction orderModifiers(modifiers) {\n  var orderedModifiers = order(modifiers);\n  return modifierPhases.reduce(function (acc, phase) {\n    return acc.concat(orderedModifiers.filter(function (modifier) {\n      return modifier.phase === phase;\n    }));\n  }, []);\n}\n\n// ../../node_modules/.pnpm/@popperjs+core@2.11.6/node_modules/@popperjs/core/lib/utils/debounce.js\nfunction debounce(fn2) {\n  var pending;\n  return function () {\n    if (!pending) {\n      pending = new Promise(function (resolve) {\n        Promise.resolve().then(function () {\n          pending = void 0;\n          resolve(fn2());\n        });\n      });\n    }\n    return pending;\n  };\n}\n\n// ../../node_modules/.pnpm/@popperjs+core@2.11.6/node_modules/@popperjs/core/lib/utils/format.js\nfunction format(str) {\n  for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n    args[_key - 1] = arguments[_key];\n  }\n  return [].concat(args).reduce(function (p, c) {\n    return p.replace(/%s/, c);\n  }, str);\n}\n\n// ../../node_modules/.pnpm/@popperjs+core@2.11.6/node_modules/@popperjs/core/lib/utils/validateModifiers.js\nvar INVALID_MODIFIER_ERROR = 'Popper: modifier \"%s\" provided an invalid %s property, expected %s but got %s';\nvar MISSING_DEPENDENCY_ERROR = 'Popper: modifier \"%s\" requires \"%s\", but \"%s\" modifier is not available';\nvar VALID_PROPERTIES = [\"name\", \"enabled\", \"phase\", \"fn\", \"effect\", \"requires\", \"options\"];\nfunction validateModifiers(modifiers) {\n  modifiers.forEach(function (modifier) {\n    [].concat(Object.keys(modifier), VALID_PROPERTIES).filter(function (value, index, self) {\n      return self.indexOf(value) === index;\n    }).forEach(function (key) {\n      switch (key) {\n        case \"name\":\n          if (typeof modifier.name !== \"string\") {}\n          break;\n        case \"enabled\":\n          if (typeof modifier.enabled !== \"boolean\") {}\n          break;\n        case \"phase\":\n          if (modifierPhases.indexOf(modifier.phase) < 0) {}\n          break;\n        case \"fn\":\n          if (typeof modifier.fn !== \"function\") {}\n          break;\n        case \"effect\":\n          if (modifier.effect != null && typeof modifier.effect !== \"function\") {}\n          break;\n        case \"requires\":\n          if (modifier.requires != null && !Array.isArray(modifier.requires)) {}\n          break;\n        case \"requiresIfExists\":\n          if (!Array.isArray(modifier.requiresIfExists)) {}\n          break;\n        case \"options\":\n        case \"data\":\n          break;\n        default:\n      }\n      modifier.requires && modifier.requires.forEach(function (requirement) {\n        if (modifiers.find(function (mod) {\n          return mod.name === requirement;\n        }) == null) {}\n      });\n    });\n  });\n}\n\n// ../../node_modules/.pnpm/@popperjs+core@2.11.6/node_modules/@popperjs/core/lib/utils/uniqueBy.js\nfunction uniqueBy(arr, fn2) {\n  var identifiers = /* @__PURE__ */new Set();\n  return arr.filter(function (item) {\n    var identifier = fn2(item);\n    if (!identifiers.has(identifier)) {\n      identifiers.add(identifier);\n      return true;\n    }\n  });\n}\n\n// ../../node_modules/.pnpm/@popperjs+core@2.11.6/node_modules/@popperjs/core/lib/utils/getBasePlacement.js\nfunction getBasePlacement(placement) {\n  return placement.split(\"-\")[0];\n}\n\n// ../../node_modules/.pnpm/@popperjs+core@2.11.6/node_modules/@popperjs/core/lib/utils/mergeByName.js\nfunction mergeByName(modifiers) {\n  var merged = modifiers.reduce(function (merged2, current) {\n    var existing = merged2[current.name];\n    merged2[current.name] = existing ? Object.assign({}, existing, current, {\n      options: Object.assign({}, existing.options, current.options),\n      data: Object.assign({}, existing.data, current.data)\n    }) : current;\n    return merged2;\n  }, {});\n  return Object.keys(merged).map(function (key) {\n    return merged[key];\n  });\n}\n\n// ../../node_modules/.pnpm/@popperjs+core@2.11.6/node_modules/@popperjs/core/lib/utils/getVariation.js\nfunction getVariation(placement) {\n  return placement.split(\"-\")[1];\n}\n\n// ../../node_modules/.pnpm/@popperjs+core@2.11.6/node_modules/@popperjs/core/lib/utils/getMainAxisFromPlacement.js\nfunction getMainAxisFromPlacement(placement) {\n  return [\"top\", \"bottom\"].indexOf(placement) >= 0 ? \"x\" : \"y\";\n}\n\n// ../../node_modules/.pnpm/@popperjs+core@2.11.6/node_modules/@popperjs/core/lib/utils/computeOffsets.js\nfunction computeOffsets(_ref) {\n  var reference = _ref.reference,\n    element = _ref.element,\n    placement = _ref.placement;\n  var basePlacement = placement ? getBasePlacement(placement) : null;\n  var variation = placement ? getVariation(placement) : null;\n  var commonX = reference.x + reference.width / 2 - element.width / 2;\n  var commonY = reference.y + reference.height / 2 - element.height / 2;\n  var offsets;\n  switch (basePlacement) {\n    case top:\n      offsets = {\n        x: commonX,\n        y: reference.y - element.height\n      };\n      break;\n    case bottom:\n      offsets = {\n        x: commonX,\n        y: reference.y + reference.height\n      };\n      break;\n    case right:\n      offsets = {\n        x: reference.x + reference.width,\n        y: commonY\n      };\n      break;\n    case left:\n      offsets = {\n        x: reference.x - element.width,\n        y: commonY\n      };\n      break;\n    default:\n      offsets = {\n        x: reference.x,\n        y: reference.y\n      };\n  }\n  var mainAxis = basePlacement ? getMainAxisFromPlacement(basePlacement) : null;\n  if (mainAxis != null) {\n    var len = mainAxis === \"y\" ? \"height\" : \"width\";\n    switch (variation) {\n      case start:\n        offsets[mainAxis] = offsets[mainAxis] - (reference[len] / 2 - element[len] / 2);\n        break;\n      case end:\n        offsets[mainAxis] = offsets[mainAxis] + (reference[len] / 2 - element[len] / 2);\n        break;\n      default:\n    }\n  }\n  return offsets;\n}\n\n// ../../node_modules/.pnpm/@popperjs+core@2.11.6/node_modules/@popperjs/core/lib/createPopper.js\nvar INVALID_ELEMENT_ERROR = \"Popper: Invalid reference or popper argument provided. They must be either a DOM element or virtual element.\";\nvar INFINITE_LOOP_ERROR = \"Popper: An infinite loop in the modifiers cycle has been detected! The cycle has been interrupted to prevent a browser crash.\";\nvar DEFAULT_OPTIONS = {\n  placement: \"bottom\",\n  modifiers: [],\n  strategy: \"absolute\"\n};\nfunction areValidElements() {\n  for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n    args[_key] = arguments[_key];\n  }\n  return !args.some(function (element) {\n    return !(element && typeof element.getBoundingClientRect === \"function\");\n  });\n}\nfunction popperGenerator(generatorOptions) {\n  if (generatorOptions === void 0) {\n    generatorOptions = {};\n  }\n  var _generatorOptions = generatorOptions,\n    _generatorOptions$def = _generatorOptions.defaultModifiers,\n    defaultModifiers2 = _generatorOptions$def === void 0 ? [] : _generatorOptions$def,\n    _generatorOptions$def2 = _generatorOptions.defaultOptions,\n    defaultOptions = _generatorOptions$def2 === void 0 ? DEFAULT_OPTIONS : _generatorOptions$def2;\n  return function createPopper2(reference, popper, options) {\n    if (options === void 0) {\n      options = defaultOptions;\n    }\n    var state = {\n      placement: \"bottom\",\n      orderedModifiers: [],\n      options: Object.assign({}, DEFAULT_OPTIONS, defaultOptions),\n      modifiersData: {},\n      elements: {\n        reference: reference,\n        popper: popper\n      },\n      attributes: {},\n      styles: {}\n    };\n    var effectCleanupFns = [];\n    var isDestroyed = false;\n    var instance = {\n      state: state,\n      setOptions: function setOptions(setOptionsAction) {\n        var options2 = typeof setOptionsAction === \"function\" ? setOptionsAction(state.options) : setOptionsAction;\n        cleanupModifierEffects();\n        state.options = Object.assign({}, defaultOptions, state.options, options2);\n        state.scrollParents = {\n          reference: isElement(reference) ? listScrollParents(reference) : reference.contextElement ? listScrollParents(reference.contextElement) : [],\n          popper: listScrollParents(popper)\n        };\n        var orderedModifiers = orderModifiers(mergeByName([].concat(defaultModifiers2, state.options.modifiers)));\n        state.orderedModifiers = orderedModifiers.filter(function (m) {\n          return m.enabled;\n        });\n        if (true) {\n          var modifiers = uniqueBy([].concat(orderedModifiers, state.options.modifiers), function (_ref) {\n            var name = _ref.name;\n            return name;\n          });\n          validateModifiers(modifiers);\n          if (getBasePlacement(state.options.placement) === auto) {\n            var flipModifier = state.orderedModifiers.find(function (_ref2) {\n              var name = _ref2.name;\n              return name === \"flip\";\n            });\n            if (!flipModifier) {}\n          }\n          var _getComputedStyle = getComputedStyle(popper),\n            marginTop = _getComputedStyle.marginTop,\n            marginRight = _getComputedStyle.marginRight,\n            marginBottom = _getComputedStyle.marginBottom,\n            marginLeft = _getComputedStyle.marginLeft;\n          if ([marginTop, marginRight, marginBottom, marginLeft].some(function (margin) {\n            return parseFloat(margin);\n          })) {}\n        }\n        runModifierEffects();\n        return instance.update();\n      },\n      forceUpdate: function forceUpdate() {\n        if (isDestroyed) {\n          return;\n        }\n        var _state$elements = state.elements,\n          reference2 = _state$elements.reference,\n          popper2 = _state$elements.popper;\n        if (!areValidElements(reference2, popper2)) {\n          if (true) {}\n          return;\n        }\n        state.rects = {\n          reference: getCompositeRect(reference2, getOffsetParent(popper2), state.options.strategy === \"fixed\"),\n          popper: getLayoutRect(popper2)\n        };\n        state.reset = false;\n        state.placement = state.options.placement;\n        state.orderedModifiers.forEach(function (modifier) {\n          return state.modifiersData[modifier.name] = Object.assign({}, modifier.data);\n        });\n        var __debug_loops__ = 0;\n        for (var index = 0; index < state.orderedModifiers.length; index++) {\n          if (true) {\n            __debug_loops__ += 1;\n            if (__debug_loops__ > 100) {\n              break;\n            }\n          }\n          if (state.reset === true) {\n            state.reset = false;\n            index = -1;\n            continue;\n          }\n          var _state$orderedModifie = state.orderedModifiers[index],\n            fn2 = _state$orderedModifie.fn,\n            _state$orderedModifie2 = _state$orderedModifie.options,\n            _options = _state$orderedModifie2 === void 0 ? {} : _state$orderedModifie2,\n            name = _state$orderedModifie.name;\n          if (typeof fn2 === \"function\") {\n            state = fn2({\n              state: state,\n              options: _options,\n              name: name,\n              instance: instance\n            }) || state;\n          }\n        }\n      },\n      update: debounce(function () {\n        return new Promise(function (resolve) {\n          instance.forceUpdate();\n          resolve(state);\n        });\n      }),\n      destroy: function destroy() {\n        cleanupModifierEffects();\n        isDestroyed = true;\n      }\n    };\n    if (!areValidElements(reference, popper)) {\n      if (true) {}\n      return instance;\n    }\n    instance.setOptions(options).then(function (state2) {\n      if (!isDestroyed && options.onFirstUpdate) {\n        options.onFirstUpdate(state2);\n      }\n    });\n    function runModifierEffects() {\n      state.orderedModifiers.forEach(function (_ref3) {\n        var name = _ref3.name,\n          _ref3$options = _ref3.options,\n          options2 = _ref3$options === void 0 ? {} : _ref3$options,\n          effect3 = _ref3.effect;\n        if (typeof effect3 === \"function\") {\n          var cleanupFn = effect3({\n            state: state,\n            name: name,\n            instance: instance,\n            options: options2\n          });\n          var noopFn = function noopFn2() {};\n          effectCleanupFns.push(cleanupFn || noopFn);\n        }\n      });\n    }\n    function cleanupModifierEffects() {\n      effectCleanupFns.forEach(function (fn2) {\n        return fn2();\n      });\n      effectCleanupFns = [];\n    }\n    return instance;\n  };\n}\n\n// ../../node_modules/.pnpm/@popperjs+core@2.11.6/node_modules/@popperjs/core/lib/modifiers/eventListeners.js\nvar passive = {\n  passive: true\n};\nfunction effect(_ref) {\n  var state = _ref.state,\n    instance = _ref.instance,\n    options = _ref.options;\n  var _options$scroll = options.scroll,\n    scroll = _options$scroll === void 0 ? true : _options$scroll,\n    _options$resize = options.resize,\n    resize = _options$resize === void 0 ? true : _options$resize;\n  var window2 = getWindow(state.elements.popper);\n  var scrollParents = [].concat(state.scrollParents.reference, state.scrollParents.popper);\n  if (scroll) {\n    scrollParents.forEach(function (scrollParent) {\n      scrollParent.addEventListener(\"scroll\", instance.update, passive);\n    });\n  }\n  if (resize) {\n    window2.addEventListener(\"resize\", instance.update, passive);\n  }\n  return function () {\n    if (scroll) {\n      scrollParents.forEach(function (scrollParent) {\n        scrollParent.removeEventListener(\"scroll\", instance.update, passive);\n      });\n    }\n    if (resize) {\n      window2.removeEventListener(\"resize\", instance.update, passive);\n    }\n  };\n}\nvar eventListeners_default = {\n  name: \"eventListeners\",\n  enabled: true,\n  phase: \"write\",\n  fn: function fn() {},\n  effect: effect,\n  data: {}\n};\n\n// ../../node_modules/.pnpm/@popperjs+core@2.11.6/node_modules/@popperjs/core/lib/modifiers/popperOffsets.js\nfunction popperOffsets(_ref) {\n  var state = _ref.state,\n    name = _ref.name;\n  state.modifiersData[name] = computeOffsets({\n    reference: state.rects.reference,\n    element: state.rects.popper,\n    strategy: \"absolute\",\n    placement: state.placement\n  });\n}\nvar popperOffsets_default = {\n  name: \"popperOffsets\",\n  enabled: true,\n  phase: \"read\",\n  fn: popperOffsets,\n  data: {}\n};\n\n// ../../node_modules/.pnpm/@popperjs+core@2.11.6/node_modules/@popperjs/core/lib/modifiers/computeStyles.js\nvar unsetSides = {\n  top: \"auto\",\n  right: \"auto\",\n  bottom: \"auto\",\n  left: \"auto\"\n};\nfunction roundOffsetsByDPR(_ref) {\n  var x = _ref.x,\n    y = _ref.y;\n  var win = window;\n  var dpr = win.devicePixelRatio || 1;\n  return {\n    x: round(x * dpr) / dpr || 0,\n    y: round(y * dpr) / dpr || 0\n  };\n}\nfunction mapToStyles(_ref2) {\n  var _Object$assign2;\n  var popper = _ref2.popper,\n    popperRect = _ref2.popperRect,\n    placement = _ref2.placement,\n    variation = _ref2.variation,\n    offsets = _ref2.offsets,\n    position = _ref2.position,\n    gpuAcceleration = _ref2.gpuAcceleration,\n    adaptive = _ref2.adaptive,\n    roundOffsets = _ref2.roundOffsets,\n    isFixed = _ref2.isFixed;\n  var _offsets$x = offsets.x,\n    x = _offsets$x === void 0 ? 0 : _offsets$x,\n    _offsets$y = offsets.y,\n    y = _offsets$y === void 0 ? 0 : _offsets$y;\n  var _ref3 = typeof roundOffsets === \"function\" ? roundOffsets({\n    x: x,\n    y: y\n  }) : {\n    x: x,\n    y: y\n  };\n  x = _ref3.x;\n  y = _ref3.y;\n  var hasX = offsets.hasOwnProperty(\"x\");\n  var hasY = offsets.hasOwnProperty(\"y\");\n  var sideX = left;\n  var sideY = top;\n  var win = window;\n  if (adaptive) {\n    var offsetParent = getOffsetParent(popper);\n    var heightProp = \"clientHeight\";\n    var widthProp = \"clientWidth\";\n    if (offsetParent === getWindow(popper)) {\n      offsetParent = getDocumentElement(popper);\n      if (getComputedStyle(offsetParent).position !== \"static\" && position === \"absolute\") {\n        heightProp = \"scrollHeight\";\n        widthProp = \"scrollWidth\";\n      }\n    }\n    offsetParent = offsetParent;\n    if (placement === top || (placement === left || placement === right) && variation === end) {\n      sideY = bottom;\n      var offsetY = isFixed && offsetParent === win && win.visualViewport ? win.visualViewport.height : offsetParent[heightProp];\n      y -= offsetY - popperRect.height;\n      y *= gpuAcceleration ? 1 : -1;\n    }\n    if (placement === left || (placement === top || placement === bottom) && variation === end) {\n      sideX = right;\n      var offsetX = isFixed && offsetParent === win && win.visualViewport ? win.visualViewport.width : offsetParent[widthProp];\n      x -= offsetX - popperRect.width;\n      x *= gpuAcceleration ? 1 : -1;\n    }\n  }\n  var commonStyles = Object.assign({\n    position: position\n  }, adaptive && unsetSides);\n  var _ref4 = roundOffsets === true ? roundOffsetsByDPR({\n    x: x,\n    y: y\n  }) : {\n    x: x,\n    y: y\n  };\n  x = _ref4.x;\n  y = _ref4.y;\n  if (gpuAcceleration) {\n    var _Object$assign;\n    return Object.assign({}, commonStyles, (_Object$assign = {}, _Object$assign[sideY] = hasY ? \"0\" : \"\", _Object$assign[sideX] = hasX ? \"0\" : \"\", _Object$assign.transform = (win.devicePixelRatio || 1) <= 1 ? \"translate(\" + x + \"px, \" + y + \"px)\" : \"translate3d(\" + x + \"px, \" + y + \"px, 0)\", _Object$assign));\n  }\n  return Object.assign({}, commonStyles, (_Object$assign2 = {}, _Object$assign2[sideY] = hasY ? y + \"px\" : \"\", _Object$assign2[sideX] = hasX ? x + \"px\" : \"\", _Object$assign2.transform = \"\", _Object$assign2));\n}\nfunction computeStyles(_ref5) {\n  var state = _ref5.state,\n    options = _ref5.options;\n  var _options$gpuAccelerat = options.gpuAcceleration,\n    gpuAcceleration = _options$gpuAccelerat === void 0 ? true : _options$gpuAccelerat,\n    _options$adaptive = options.adaptive,\n    adaptive = _options$adaptive === void 0 ? true : _options$adaptive,\n    _options$roundOffsets = options.roundOffsets,\n    roundOffsets = _options$roundOffsets === void 0 ? true : _options$roundOffsets;\n  if (true) {\n    var transitionProperty = getComputedStyle(state.elements.popper).transitionProperty || \"\";\n    if (adaptive && [\"transform\", \"top\", \"right\", \"bottom\", \"left\"].some(function (property) {\n      return transitionProperty.indexOf(property) >= 0;\n    })) {}\n  }\n  var commonStyles = {\n    placement: getBasePlacement(state.placement),\n    variation: getVariation(state.placement),\n    popper: state.elements.popper,\n    popperRect: state.rects.popper,\n    gpuAcceleration: gpuAcceleration,\n    isFixed: state.options.strategy === \"fixed\"\n  };\n  if (state.modifiersData.popperOffsets != null) {\n    state.styles.popper = Object.assign({}, state.styles.popper, mapToStyles(Object.assign({}, commonStyles, {\n      offsets: state.modifiersData.popperOffsets,\n      position: state.options.strategy,\n      adaptive: adaptive,\n      roundOffsets: roundOffsets\n    })));\n  }\n  if (state.modifiersData.arrow != null) {\n    state.styles.arrow = Object.assign({}, state.styles.arrow, mapToStyles(Object.assign({}, commonStyles, {\n      offsets: state.modifiersData.arrow,\n      position: \"absolute\",\n      adaptive: false,\n      roundOffsets: roundOffsets\n    })));\n  }\n  state.attributes.popper = Object.assign({}, state.attributes.popper, {\n    \"data-popper-placement\": state.placement\n  });\n}\nvar computeStyles_default = {\n  name: \"computeStyles\",\n  enabled: true,\n  phase: \"beforeWrite\",\n  fn: computeStyles,\n  data: {}\n};\n\n// ../../node_modules/.pnpm/@popperjs+core@2.11.6/node_modules/@popperjs/core/lib/modifiers/applyStyles.js\nfunction applyStyles(_ref) {\n  var state = _ref.state;\n  Object.keys(state.elements).forEach(function (name) {\n    var style = state.styles[name] || {};\n    var attributes = state.attributes[name] || {};\n    var element = state.elements[name];\n    if (!isHTMLElement(element) || !getNodeName(element)) {\n      return;\n    }\n    Object.assign(element.style, style);\n    Object.keys(attributes).forEach(function (name2) {\n      var value = attributes[name2];\n      if (value === false) {\n        element.removeAttribute(name2);\n      } else {\n        element.setAttribute(name2, value === true ? \"\" : value);\n      }\n    });\n  });\n}\nfunction effect2(_ref2) {\n  var state = _ref2.state;\n  var initialStyles = {\n    popper: {\n      position: state.options.strategy,\n      left: \"0\",\n      top: \"0\",\n      margin: \"0\"\n    },\n    arrow: {\n      position: \"absolute\"\n    },\n    reference: {}\n  };\n  Object.assign(state.elements.popper.style, initialStyles.popper);\n  state.styles = initialStyles;\n  if (state.elements.arrow) {\n    Object.assign(state.elements.arrow.style, initialStyles.arrow);\n  }\n  return function () {\n    Object.keys(state.elements).forEach(function (name) {\n      var element = state.elements[name];\n      var attributes = state.attributes[name] || {};\n      var styleProperties = Object.keys(state.styles.hasOwnProperty(name) ? state.styles[name] : initialStyles[name]);\n      var style = styleProperties.reduce(function (style2, property) {\n        style2[property] = \"\";\n        return style2;\n      }, {});\n      if (!isHTMLElement(element) || !getNodeName(element)) {\n        return;\n      }\n      Object.assign(element.style, style);\n      Object.keys(attributes).forEach(function (attribute) {\n        element.removeAttribute(attribute);\n      });\n    });\n  };\n}\nvar applyStyles_default = {\n  name: \"applyStyles\",\n  enabled: true,\n  phase: \"write\",\n  fn: applyStyles,\n  effect: effect2,\n  requires: [\"computeStyles\"]\n};\n\n// ../../node_modules/.pnpm/@popperjs+core@2.11.6/node_modules/@popperjs/core/lib/popper-lite.js\nvar defaultModifiers = [eventListeners_default, popperOffsets_default, computeStyles_default, applyStyles_default];\nvar createPopper = /* @__PURE__ */popperGenerator({\n  defaultModifiers: defaultModifiers\n});\n\n// ../../node_modules/.pnpm/@popperjs+core@2.11.6/node_modules/@popperjs/core/lib/modifiers/offset.js\nfunction distanceAndSkiddingToXY(placement, rects, offset2) {\n  var basePlacement = getBasePlacement(placement);\n  var invertDistance = [left, top].indexOf(basePlacement) >= 0 ? -1 : 1;\n  var _ref = typeof offset2 === \"function\" ? offset2(Object.assign({}, rects, {\n      placement: placement\n    })) : offset2,\n    skidding = _ref[0],\n    distance = _ref[1];\n  skidding = skidding || 0;\n  distance = (distance || 0) * invertDistance;\n  return [left, right].indexOf(basePlacement) >= 0 ? {\n    x: distance,\n    y: skidding\n  } : {\n    x: skidding,\n    y: distance\n  };\n}\nfunction offset(_ref2) {\n  var state = _ref2.state,\n    options = _ref2.options,\n    name = _ref2.name;\n  var _options$offset = options.offset,\n    offset2 = _options$offset === void 0 ? [0, 0] : _options$offset;\n  var data = placements.reduce(function (acc, placement) {\n    acc[placement] = distanceAndSkiddingToXY(placement, state.rects, offset2);\n    return acc;\n  }, {});\n  var _data$state$placement = data[state.placement],\n    x = _data$state$placement.x,\n    y = _data$state$placement.y;\n  if (state.modifiersData.popperOffsets != null) {\n    state.modifiersData.popperOffsets.x += x;\n    state.modifiersData.popperOffsets.y += y;\n  }\n  state.modifiersData[name] = data;\n}\nvar offset_default = {\n  name: \"offset\",\n  enabled: true,\n  phase: \"main\",\n  requires: [\"popperOffsets\"],\n  fn: offset\n};\nexport { createPopper, offset_default as offsetModifier };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}