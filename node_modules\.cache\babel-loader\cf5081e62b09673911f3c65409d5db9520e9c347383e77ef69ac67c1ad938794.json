{"ast": null, "code": "import { ref, reactive, watch } from 'vue';\nimport store from '@/store/index';\nimport { useRouter } from 'vue-router';\nexport default {\n  setup: function setup() {\n    var _useRouter = useRouter(),\n      push = _useRouter.push;\n    var fooCheck = ref(store.state.fooCheck);\n    var list = reactive([{\n      img: require('@/assets/images/footer/home1.svg'),\n      img_check: require('@/assets/images/footer/home2.svg'),\n      key: 'home',\n      name: 'home'\n    }, {\n      img: require('@/assets/images/footer/pages1.svg'),\n      img_check: require('@/assets/images/footer/pages2.svg'),\n      key: 'order',\n      name: 'order'\n    }, {\n      img: require('@/assets/images/footer/test2.svg'),\n      img_check: require('@/assets/images/footer/test2.svg'),\n      key: 'obj',\n      name: 'jiaoyi'\n    }, {\n      img: require('@/assets/images/footer/team1.svg'),\n      img_check: require('@/assets/images/footer/team2.svg'),\n      key: 'team',\n      name: 'team'\n    }, {\n      img: require('@/assets/images/footer/user1.svg'),\n      img_check: require('@/assets/images/footer/user2.svg'),\n      key: 'self',\n      name: 'self'\n    }]);\n    var checkList = function checkList(key) {\n      fooCheck.value = key;\n      store.dispatch('changefooCheck', fooCheck.value);\n    };\n    watch(fooCheck, function (newValue) {\n      push('/' + newValue);\n    });\n    return {\n      list: list,\n      fooCheck: fooCheck,\n      checkList: checkList\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "reactive", "watch", "store", "useRouter", "setup", "_useRouter", "push", "foo<PERSON>heck", "state", "list", "img", "require", "img_check", "key", "name", "checkList", "value", "dispatch", "newValue"], "sources": ["C:\\Users\\<USER>\\Desktop\\vue3_3.0\\src\\components\\footer.vue"], "sourcesContent": ["<template>\r\n    <div class=\"footer\">\r\n        <div class=\"f_li\" v-for=\"item in list\" :key=\"item.key\" :class=\"item.key == 'obj' && 'obj'\" @click=\"checkList(item.key)\">\r\n            <div class=\"span\">\r\n                <img :src=\"fooCheck == item.key ? item.img_check : item.img\" class=\"img\" alt=\"\">\r\n                <span class=\"text\"  :class=\"fooCheck == item.key && 'check'\">{{$t('msg.'+item.name)}}</span>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</template>\r\n<script>\r\nimport { ref,reactive,watch } from 'vue';\r\nimport store from '@/store/index'\r\nimport { useRouter } from 'vue-router';\r\nexport default {\r\n    setup(){\r\n        const { push } = useRouter();\r\n        const fooCheck = ref(store.state.fooCheck)\r\n        const list = reactive([\r\n            {\r\n                img: require('@/assets/images/footer/home1.svg'),\r\n                img_check: require('@/assets/images/footer/home2.svg'),\r\n                key: 'home'\r\n                ,name: 'home',\r\n            },\r\n            {\r\n                img: require('@/assets/images/footer/pages1.svg'),\r\n                img_check: require('@/assets/images/footer/pages2.svg'),\r\n                key: 'order'\r\n                ,name: 'order',\r\n            },\r\n            {\r\n                img: require('@/assets/images/footer/test2.svg'),\r\n                img_check: require('@/assets/images/footer/test2.svg'),\r\n                key: 'obj'\r\n                ,name: 'jiaoyi',\r\n            },\r\n            {\r\n                img: require('@/assets/images/footer/team1.svg'),\r\n                img_check: require('@/assets/images/footer/team2.svg'),\r\n                key: 'team'\r\n                ,name: 'team',\r\n            },\r\n            {\r\n                img: require('@/assets/images/footer/user1.svg'),\r\n                img_check: require('@/assets/images/footer/user2.svg'),\r\n                key: 'self'\r\n                ,name: 'self',\r\n            },\r\n        ])\r\n        const checkList = (key) => {\r\n            fooCheck.value = key\r\n            store.dispatch('changefooCheck',fooCheck.value)\r\n        }\r\n        watch(fooCheck,(newValue)=>{\r\n            push('/'+newValue)\r\n        })\r\n        return {list,fooCheck,checkList}\r\n    }\r\n}\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n    .footer{\r\n        display: flex;\r\n        // height: 130px;\r\n        // background-image: url('~@/assets/images/footer/bg.png');\r\n        // background-size: 100% 100%;\r\n        position: fixed;\r\n        width: 100%;\r\n        bottom: -7px;\r\n        left: 0;\r\n        padding:15px 0 1.333333vw;\r\n        z-index: 999;\r\n        background: #fff;\r\n        filter: drop-shadow(0 0 4px #bbb);\r\n        .f_li{\r\n            flex:1;\r\n            display: flex;\r\n         \r\n            // width: 54px;\r\n            // padding-top: 55px;\r\n            // height: 100px;\r\n            // &.obj{\r\n            //     width: auto;\r\n            //     position: relative;\r\n            //     padding-top: 0;\r\n            //     margin-top: -50px;\r\n            //     .span{\r\n            //         // position: absolute;\r\n            //         // left: 0;\r\n            //         // top: 0;\r\n            //         // transform: translateY(-50%);\r\n            //         background: #fff;\r\n            //         border-radius: 50%;\r\n            //         padding: 5px;\r\n            //         height: 100px;\r\n            //         .img{\r\n            //             height: 100%;\r\n            //             width: auto;\r\n            //         }\r\n            //     }\r\n            // }\r\n            .span{\r\n                height: 100%;\r\n                width: 100%;\r\n                position: relative;\r\n                display: flex;\r\n                flex-direction: column;\r\n                justify-content: center;\r\n                align-items: center;\r\n                text-align: center;\r\n                .img{\r\n                    height: 1.5rem;\r\n                    width: 2.6rem;\r\n                    margin: 0 auto 15px;\r\n                }\r\n                .text{\r\n                    font-size: 20px;\r\n                    color: #a19fa8;\r\n                    // margin-top: -10px;\r\n                    // position: absolute;\r\n                    // left: 50%;\r\n                    // transform: translate(-50%);\r\n                    // white-space: nowrap;\r\n                    white-space: wrap;\r\n                    line-height: 1;\r\n                    margin-bottom: 5px;\r\n                    // height: 55px;\r\n                    &.check{\r\n                        color: #000;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n</style>"], "mappings": "AAWA,SAASA,GAAG,EAACC,QAAQ,EAACC,KAAI,QAAS,KAAK;AACxC,OAAOC,KAAI,MAAO,eAAc;AAChC,SAASC,SAAQ,QAAS,YAAY;AACtC,eAAe;EACXC,KAAK,WAAAA,MAAA,EAAE;IACH,IAAAC,UAAA,GAAiBF,SAAS,CAAC,CAAC;MAApBG,IAAG,GAAAD,UAAA,CAAHC,IAAG;IACX,IAAMC,QAAO,GAAIR,GAAG,CAACG,KAAK,CAACM,KAAK,CAACD,QAAQ;IACzC,IAAME,IAAG,GAAIT,QAAQ,CAAC,CAClB;MACIU,GAAG,EAAEC,OAAO,CAAC,kCAAkC,CAAC;MAChDC,SAAS,EAAED,OAAO,CAAC,kCAAkC,CAAC;MACtDE,GAAG,EAAE,MAAK;MACTC,IAAI,EAAE;IACX,CAAC,EACD;MACIJ,GAAG,EAAEC,OAAO,CAAC,mCAAmC,CAAC;MACjDC,SAAS,EAAED,OAAO,CAAC,mCAAmC,CAAC;MACvDE,GAAG,EAAE,OAAM;MACVC,IAAI,EAAE;IACX,CAAC,EACD;MACIJ,GAAG,EAAEC,OAAO,CAAC,kCAAkC,CAAC;MAChDC,SAAS,EAAED,OAAO,CAAC,kCAAkC,CAAC;MACtDE,GAAG,EAAE,KAAI;MACRC,IAAI,EAAE;IACX,CAAC,EACD;MACIJ,GAAG,EAAEC,OAAO,CAAC,kCAAkC,CAAC;MAChDC,SAAS,EAAED,OAAO,CAAC,kCAAkC,CAAC;MACtDE,GAAG,EAAE,MAAK;MACTC,IAAI,EAAE;IACX,CAAC,EACD;MACIJ,GAAG,EAAEC,OAAO,CAAC,kCAAkC,CAAC;MAChDC,SAAS,EAAED,OAAO,CAAC,kCAAkC,CAAC;MACtDE,GAAG,EAAE,MAAK;MACTC,IAAI,EAAE;IACX,CAAC,CACJ;IACD,IAAMC,SAAQ,GAAI,SAAZA,SAAQA,CAAKF,GAAG,EAAK;MACvBN,QAAQ,CAACS,KAAI,GAAIH,GAAE;MACnBX,KAAK,CAACe,QAAQ,CAAC,gBAAgB,EAACV,QAAQ,CAACS,KAAK;IAClD;IACAf,KAAK,CAACM,QAAQ,EAAC,UAACW,QAAQ,EAAG;MACvBZ,IAAI,CAAC,GAAG,GAACY,QAAQ;IACrB,CAAC;IACD,OAAO;MAACT,IAAI,EAAJA,IAAI;MAACF,QAAQ,EAARA,QAAQ;MAACQ,SAAS,EAATA;IAAS;EACnC;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}