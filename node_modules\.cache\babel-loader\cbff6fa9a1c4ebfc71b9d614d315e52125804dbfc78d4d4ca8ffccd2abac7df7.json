{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, openBlock as _openBlock, createBlock as _createBlock } from \"vue\";\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_router_view = _resolveComponent(\"router-view\");\n  var _component_my_scroll = _resolveComponent(\"my-scroll\");\n  return _openBlock(), _createBlock(_component_my_scroll, null, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_router_view)];\n    }),\n    _: 1 /* STABLE */\n  });\n}", "map": {"version": 3, "names": ["_createBlock", "_component_my_scroll", "_createVNode", "_component_router_view"], "sources": ["C:\\Users\\<USER>\\Downloads\\Telegram Desktop\\vue3_3.0\\vue3_3.0\\src\\App.vue"], "sourcesContent": ["<template>\r\n  <my-scroll>\r\n    <router-view/>\r\n  </my-scroll>\r\n</template>\r\n<script>\r\nimport {common_parameters} from '@/api/login/index'\r\nimport { useI18n } from 'vue-i18n'\r\nimport store from '@/store/index'\r\nimport {vantLocales} from '@/i18n/i18n';\r\nimport myScroll from './components/scroll.vue'\r\n\r\nexport default {\r\n  components:{myScroll},\r\n  setup(){\r\n    const { locale } = useI18n()\r\n    // const {proxy} = getCurrentInstance()\r\n     const changeFavicon = link => {\r\n        let $favicon = document.querySelector('link[rel=\"icon\"]');\r\n        // If a <link rel=\"icon\"> element already exists,\r\n        // change its href to the given link.\r\n        if ($favicon !== null) {\r\n          $favicon.href = link;\r\n          // Otherwise, create a new element and append it to <head>.\r\n        } else {\r\n          $favicon = document.createElement(\"link\");\r\n          $favicon.rel = \"icon\";\r\n          $favicon.href = link;\r\n          document.head.appendChild($favicon);\r\n        }\r\n      };\r\n      Promise.resolve(common_parameters()).then(res=>{\r\n        if(res.code === 0) {\r\n          const info = JSON.parse(JSON.stringify(res.data))\r\n            locale.value = info.language\r\n            let languageList = info.languageList\r\n            let json = languageList.find(rr => rr.link == info.language)\r\n            let langImg = json && json.image_url\r\n            store.dispatch('changelang',info.language)\r\n            store.dispatch('changelangImg',langImg)\r\n            store.dispatch('changebaseInfo',info)\r\n            changeFavicon(info.site_icon); // 动态修改网站图标\r\n            document.title = info.app_name; // 动态修改网站标题\r\n            vantLocales(info.language)\r\n        }\r\n        }).catch(err=>{\r\n          console.log(err)\r\n      })\r\n\r\n    return {}\r\n  }\r\n}\r\n</script>\r\n<style lang='scss'>\r\n#app {\r\n  font-family:\"PingFang SC,Helvetica Neue,Helvetica,Arial,Hiragino Sans GB,Heiti SC,Microsoft YaHei,WenQuanYi Micro Hei,sans-serif\"!important;\r\n  -webkit-font-smoothing: antialiased;\r\n  -moz-osx-font-smoothing: grayscale;\r\n  text-align: center;\r\n  color: $textColor;\r\n  // background-image: linear-gradient(#d4dff5,#f3f7fd);\r\n  background-image: #fff;\r\n  height: 100vh;\r\n}\r\n\r\n</style>\r\n"], "mappings": ";;;;uBACEA,YAAA,CAEYC,oBAAA;sBADV;MAAA,OAAc,CAAdC,YAAA,CAAcC,sBAAA,E"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}