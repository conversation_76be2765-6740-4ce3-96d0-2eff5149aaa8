{"ast": null, "code": "function _createForOfIteratorHelper(o, allowArrayLike) { var it = typeof Symbol !== \"undefined\" && o[Symbol.iterator] || o[\"@@iterator\"]; if (!it) { if (Array.isArray(o) || (it = _unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === \"number\") { if (it) o = it; var i = 0; var F = function F() {}; return { s: F, n: function n() { if (i >= o.length) return { done: true }; return { done: false, value: o[i++] }; }, e: function e(_e) { throw _e; }, f: F }; } throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); } var normalCompletion = true, didErr = false, err; return { s: function s() { it = it.call(o); }, n: function n() { var step = it.next(); normalCompletion = step.done; return step; }, e: function e(_e2) { didErr = true; err = _e2; }, f: function f() { try { if (!normalCompletion && it.return != null) it.return(); } finally { if (didErr) throw err; } } }; }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nfunction _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __export = function __export(target, all) {\n  for (var name in all) __defProp(target, name, {\n    get: all[name],\n    enumerable: true\n  });\n};\nvar __copyProps = function __copyProps(to, from, except, desc) {\n  if (from && _typeof(from) === \"object\" || typeof from === \"function\") {\n    var _iterator = _createForOfIteratorHelper(__getOwnPropNames(from)),\n      _step;\n    try {\n      var _loop = function _loop() {\n        var key = _step.value;\n        if (!__hasOwnProp.call(to, key) && key !== except) __defProp(to, key, {\n          get: function get() {\n            return from[key];\n          },\n          enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable\n        });\n      };\n      for (_iterator.s(); !(_step = _iterator.n()).done;) {\n        _loop();\n      }\n    } catch (err) {\n      _iterator.e(err);\n    } finally {\n      _iterator.f();\n    }\n  }\n  return to;\n};\nvar __toCommonJS = function __toCommonJS(mod) {\n  return __copyProps(__defProp({}, \"__esModule\", {\n    value: true\n  }), mod);\n};\nvar stdin_exports = {};\n__export(stdin_exports, {\n  default: function _default() {\n    return stdin_default;\n  }\n});\nmodule.exports = __toCommonJS(stdin_exports);\nvar stdin_default = {\n  name: \"Name\",\n  tel: \"Telefon\",\n  save: \"Speichern\",\n  confirm: \"Best\\xE4tigen\",\n  cancel: \"Abbrechen\",\n  delete: \"L\\xF6schen\",\n  loading: \"Laden...\",\n  noCoupon: \"Keine Coupons\",\n  nameEmpty: \"Bitte den Name angeben\",\n  addContact: \"Neuen Kontakt hinzuf\\xFCgen\",\n  telInvalid: \"Ung\\xFCltige Telefonnummer\",\n  vanCalendar: {\n    end: \"Ende\",\n    start: \"Start\",\n    title: \"Kalender\",\n    weekdays: [\"So\", \"Mo\", \"Di\", \"Mo\", \"Do\", \"Fr\", \"Sa\"],\n    monthTitle: function monthTitle(year, month) {\n      return \"\".concat(year, \"/\").concat(month);\n    },\n    rangePrompt: function rangePrompt(maxRange) {\n      return \"W\\xE4hle nicht mehr als \".concat(maxRange, \" Tage\");\n    }\n  },\n  vanCascader: {\n    select: \"W\\xE4hlen\"\n  },\n  vanPagination: {\n    prev: \"Vorherige\",\n    next: \"N\\xE4chste\"\n  },\n  vanPullRefresh: {\n    pulling: \"Zum Aktualisieren herunterziehen...\",\n    loosing: \"Loslassen zum Aktualisieren...\"\n  },\n  vanSubmitBar: {\n    label: \"Total:\"\n  },\n  vanCoupon: {\n    unlimited: \"Unbegrenzt\",\n    discount: function discount(_discount) {\n      return \"\".concat(_discount * 10, \"% Rabatt\");\n    },\n    condition: function condition(_condition) {\n      return \"Mindestens \".concat(_condition);\n    }\n  },\n  vanCouponCell: {\n    title: \"Coupon\",\n    count: function count(_count) {\n      return \"Du hast \".concat(_count, \" Coupons\");\n    }\n  },\n  vanCouponList: {\n    exchange: \"Austauschen\",\n    close: \"Schlie\\xDFen\",\n    enable: \"Verf\\xFCgbar\",\n    disabled: \"Nicht verf\\xFCgbar\",\n    placeholder: \"Couponcode\"\n  },\n  vanAddressEdit: {\n    area: \"Standort\",\n    postal: \"PLZ\",\n    areaEmpty: \"Bitte deinen Ort angeben\",\n    addressEmpty: \"Adresse darf nicht leer sein\",\n    postalEmpty: \"Falsche Postleitzahl\",\n    addressDetail: \"Adresse\",\n    defaultAddress: \"Als Standardadresse festgelegen\"\n  },\n  vanAddressList: {\n    add: \"Neue Adresse hinzuf\\xFCgen\"\n  }\n};", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}