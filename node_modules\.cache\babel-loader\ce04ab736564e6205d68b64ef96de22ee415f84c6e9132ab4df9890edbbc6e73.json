{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _SwipeItem from \"./SwipeItem.mjs\";\nvar SwipeItem = withInstall(_SwipeItem);\nvar stdin_default = SwipeItem;\nexport { SwipeItem, stdin_default as default };", "map": {"version": 3, "names": ["withInstall", "_SwipeItem", "SwipeItem", "stdin_default", "default"], "sources": ["C:/Users/<USER>/Desktop/vue3_3.0/node_modules/vant/es/swipe-item/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _SwipeItem from \"./SwipeItem.mjs\";\nconst SwipeItem = withInstall(_SwipeItem);\nvar stdin_default = SwipeItem;\nexport {\n  SwipeItem,\n  stdin_default as default\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,UAAU,MAAM,iBAAiB;AACxC,IAAMC,SAAS,GAAGF,WAAW,CAACC,UAAU,CAAC;AACzC,IAAIE,aAAa,GAAGD,SAAS;AAC7B,SACEA,SAAS,EACTC,aAAa,IAAIC,OAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}