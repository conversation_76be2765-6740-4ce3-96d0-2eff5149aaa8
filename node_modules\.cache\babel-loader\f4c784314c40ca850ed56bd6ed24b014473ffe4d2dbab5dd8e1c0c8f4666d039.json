{"ast": null, "code": "import Clipboard from 'clipboard';\nexport default (function (opts) {\n  // default appendToBody true\n  var appendToBody = (opts === null || opts === void 0 ? void 0 : opts.appendToBody) === undefined ? true : opts.appendToBody;\n  return {\n    toClipboard: function toClipboard(_text, container) {\n      return new Promise(function (resolve, reject) {\n        // make fake element\n        var fakeEl = document.createElement('button');\n        // setup a new Clipboard.js\n        var clipboard = new Clipboard(fakeEl, {\n          text: function text() {\n            return _text;\n          },\n          action: function action() {\n            return 'copy';\n          },\n          container: container !== undefined ? container : document.body\n        });\n        clipboard.on('success', function (e) {\n          clipboard.destroy();\n          resolve(e);\n        });\n        clipboard.on('error', function (e) {\n          clipboard.destroy();\n          reject(e);\n        });\n        // appendToBody fixes IE\n        if (appendToBody) document.body.appendChild(fakeEl);\n        // simulate click\n        fakeEl.click();\n        // remove from body if appended\n        if (appendToBody) document.body.removeChild(fakeEl);\n      });\n    }\n  };\n});", "map": {"version": 3, "names": ["Clipboard", "opts", "appendToBody", "undefined", "toClipboard", "text", "container", "Promise", "resolve", "reject", "fakeEl", "document", "createElement", "clipboard", "action", "body", "on", "e", "destroy", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["../../src/index.ts"], "sourcesContent": [null], "mappings": "AAAA,OAAOA,SAAS,MAAM,WAAW;AAOjC,gBAAe,UAACC,IAAc,EAAI;EAChC;EACA,IAAMC,YAAY,GAAG,CAAAD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEC,YAAY,MAAKC,SAAS,GAAG,IAAI,GAAGF,IAAI,CAACC,YAAY;EAChF,OAAO;IACLE,WAAW,WAAAA,YAACC,KAAY,EAAEC,SAAuB;MAC/C,OAAO,IAAIC,OAAO,CAAC,UAACC,OAAO,EAAEC,MAAM,EAAI;QACrC;QACA,IAAMC,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;QAC/C;QACA,IAAMC,SAAS,GAAG,IAAIb,SAAS,CAACU,MAAM,EAAE;UACtCL,IAAI,EAAE,SAAAA,KAAA;YAAA,OAAMA,KAAI;UAAA;UAChBS,MAAM,EAAE,SAAAA,OAAA;YAAA,OAAM,MAAM;UAAA;UACpBR,SAAS,EAAEA,SAAS,KAAKH,SAAS,GAAGG,SAAS,GAAGK,QAAQ,CAACI;SAC3D,CAAC;QACFF,SAAS,CAACG,EAAE,CAAC,SAAS,EAAE,UAACC,CAAC,EAAI;UAC5BJ,SAAS,CAACK,OAAO,EAAE;UACnBV,OAAO,CAACS,CAAC,CAAC;QACZ,CAAC,CAAC;QACFJ,SAAS,CAACG,EAAE,CAAC,OAAO,EAAE,UAACC,CAAC,EAAI;UAC1BJ,SAAS,CAACK,OAAO,EAAE;UACnBT,MAAM,CAACQ,CAAC,CAAC;QACX,CAAC,CAAC;QACF;QACA,IAAIf,YAAY,EAAES,QAAQ,CAACI,IAAI,CAACI,WAAW,CAACT,MAAM,CAAC;QACnD;QACAA,MAAM,CAACU,KAAK,EAAE;QACd;QACA,IAAIlB,YAAY,EAAES,QAAQ,CAACI,IAAI,CAACM,WAAW,CAACX,MAAM,CAAC;MACrD,CAAC,CAAC;IACJ;GACD;AACH,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}