{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _Slider from \"./Slider.mjs\";\nvar Slider = withInstall(_Slider);\nvar stdin_default = Slider;\nexport { Slider, stdin_default as default };", "map": {"version": 3, "names": ["withInstall", "_<PERSON><PERSON><PERSON>", "Slide<PERSON>", "stdin_default", "default"], "sources": ["C:/Users/<USER>/Desktop/vue3_3.0/node_modules/vant/es/slider/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _Slider from \"./Slider.mjs\";\nconst Slider = withInstall(_Slider);\nvar stdin_default = Slider;\nexport {\n  Slider,\n  stdin_default as default\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,OAAO,MAAM,cAAc;AAClC,IAAMC,MAAM,GAAGF,WAAW,CAACC,OAAO,CAAC;AACnC,IAAIE,aAAa,GAAGD,MAAM;AAC1B,SACEA,MAAM,EACNC,aAAa,IAAIC,OAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}