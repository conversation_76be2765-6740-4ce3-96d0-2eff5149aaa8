{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _Step from \"./Step.mjs\";\nvar Step = withInstall(_Step);\nvar stdin_default = Step;\nexport { Step, stdin_default as default };", "map": {"version": 3, "names": ["withInstall", "_Step", "Step", "stdin_default", "default"], "sources": ["C:/Users/<USER>/Downloads/Telegram Desktop/vue3_3.0/vue3_3.0/node_modules/vant/es/step/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _Step from \"./Step.mjs\";\nconst Step = withInstall(_Step);\nvar stdin_default = Step;\nexport {\n  Step,\n  stdin_default as default\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,KAAK,MAAM,YAAY;AAC9B,IAAMC,IAAI,GAAGF,WAAW,CAACC,KAAK,CAAC;AAC/B,IAAIE,aAAa,GAAGD,IAAI;AACxB,SACEA,IAAI,EACJC,aAAa,IAAIC,OAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}