{"ast": null, "code": "import axios from 'axios';\nimport router from \"@/router\";\nimport { Notify, Toast } from 'vant';\nimport { i18n } from '@/i18n/i18n';\nimport Message from '@/components/message.js';\nconsole.log(i18n);\nvar t = i18n.global.t;\n\n// 创建一个 axios 实例\nvar service = axios.create({\n  // baseURL: 'https://shop7779.com/index/', // 所有的请求地址前缀部分\n  // baseURL: 'https://amazonbrazill.world/index/', // 所有的请求地址前缀部分\n  baseURL: window.config.api,\n  // 所有的请求地址前缀部分/\n  // baseURL: 'https://ok77168.space/index/', // 所有的请求地址前缀部分\n  // baseURL: 'http://www.jake1006.space/index/', // 所有的请求地址前缀部分\n  timeout: 60000,\n  // 请求超时时间毫秒\n  withCredentials: true // 异步请求携带cookie\n});\n\n// 添加请求拦截器\n// var load = null;\nservice.interceptors.request.use(function (config) {\n  // 在发送请求之前做些什么\n  // 1.获取token\n  config.headers['Access-Control-Allow-Credentials'] = true;\n  var token = localStorage.getItem('token');\n  if (token) {\n    // 2.将值传递到服务器\n    config.headers['token'] = token;\n  }\n  // 1.获取语种\n  var lang = localStorage.getItem('lang');\n  if (lang) {\n    // 2.将值传递到服务器\n    config.headers['language'] = lang;\n  }\n  // load=Toast.loading({\n  // \tduration: 0,\n  // \tmessage: t('msg.jzz')+'...',\n  // \tforbidClick: true,\n  //   });\n  return config;\n}, function (error) {\n  // 对请求错误做些什么\n  console.log(error);\n  return Promise.reject(error);\n});\n\n// 添加响应拦截器\nservice.interceptors.response.use(function (response) {\n  // Toast.clear()\n  // 2xx 范围内的状态码都会触发该函数。\n  // 对响应数据做点什么\n  // dataAxios 是 axios 返回数据中的 data\n  var dataAxios = response.data;\n  // 这个状态码是和后端约定的\n  var code = dataAxios.code;\n  if (code == -400) {\n    Message({\n      type: 'error',\n      message: dataAxios.info\n    });\n    router.push('/login');\n  }\n  return response;\n}, function (error) {\n  // 超出 2xx 范围的状态码都会触发该函数。\n  // 对响应错误做点什么\n  // Toast.clear();  // 清除加载\n  console.log(error);\n  return Promise.reject(error);\n});\nexport default service;", "map": {"version": 3, "names": ["axios", "router", "Notify", "Toast", "i18n", "Message", "console", "log", "t", "global", "service", "create", "baseURL", "window", "config", "api", "timeout", "withCredentials", "interceptors", "request", "use", "headers", "token", "localStorage", "getItem", "lang", "error", "Promise", "reject", "response", "dataAxios", "data", "code", "type", "message", "info", "push"], "sources": ["C:/Users/<USER>/Desktop/vue3_3.0/src/request/index.js"], "sourcesContent": ["import axios from 'axios'\r\nimport router from \"@/router\";\r\nimport { Notify,Toast } from 'vant';\r\nimport {i18n} from '@/i18n/i18n';\r\nimport Message from '@/components/message.js'\r\nconsole.log(i18n)\r\nconst { t } = i18n.global;\r\n\r\n// 创建一个 axios 实例\r\nconst service = axios.create({\r\n\t// baseURL: 'https://shop7779.com/index/', // 所有的请求地址前缀部分\r\n\t// baseURL: 'https://amazonbrazill.world/index/', // 所有的请求地址前缀部分\r\n\tbaseURL: window.config.api, // 所有的请求地址前缀部分/\r\n\t// baseURL: 'https://ok77168.space/index/', // 所有的请求地址前缀部分\r\n\t// baseURL: 'http://www.jake1006.space/index/', // 所有的请求地址前缀部分\r\n\ttimeout: 60000, // 请求超时时间毫秒\r\n\twithCredentials: true, // 异步请求携带cookie\r\n})\r\n\r\n// 添加请求拦截器\r\n// var load = null;\r\nservice.interceptors.request.use(\r\n\tfunction (config) {\r\n\t\t// 在发送请求之前做些什么\r\n        // 1.获取token\r\n\t\tconfig.headers['Access-Control-Allow-Credentials']=true\r\n        var token = localStorage.getItem('token')\r\n        if (token) {\r\n            // 2.将值传递到服务器\r\n            config.headers['token'] = token\r\n        }\r\n        // 1.获取语种\r\n        var lang = localStorage.getItem('lang')\r\n        if (lang) {\r\n            // 2.将值传递到服务器\r\n            config.headers['language'] = lang\r\n        }\r\n\t\t// load=Toast.loading({\r\n\t\t// \tduration: 0,\r\n\t\t// \tmessage: t('msg.jzz')+'...',\r\n\t\t// \tforbidClick: true,\r\n\t\t//   });\r\n\t\treturn config\r\n\t},\r\n\tfunction (error) {\r\n\t\t// 对请求错误做些什么\r\n\t\tconsole.log(error)\r\n\t\treturn Promise.reject(error)\r\n\t}\r\n)\r\n\r\n// 添加响应拦截器\r\nservice.interceptors.response.use(\r\n\tfunction (response) {\r\n\t\t// Toast.clear()\r\n\t\t// 2xx 范围内的状态码都会触发该函数。\r\n\t\t// 对响应数据做点什么\r\n\t\t// dataAxios 是 axios 返回数据中的 data\r\n\t\tconst dataAxios = response.data\r\n\t\t// 这个状态码是和后端约定的\r\n\t\tconst code = dataAxios.code\r\n\t\tif(code == -400) {\r\n\t\t\tMessage({ type: 'error', message:dataAxios.info});\r\n\t\t\trouter.push('/login')\r\n\t\t}\r\n\t\treturn response\r\n\t},\r\n\tfunction (error) {\r\n\t\t// 超出 2xx 范围的状态码都会触发该函数。\r\n\t\t// 对响应错误做点什么\r\n  \t\t// Toast.clear();  // 清除加载\r\n\t\tconsole.log(error)\r\n\t\treturn Promise.reject(error)\r\n\t}\r\n)\r\nexport default service\r\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,UAAU;AAC7B,SAASC,MAAM,EAACC,KAAK,QAAQ,MAAM;AACnC,SAAQC,IAAI,QAAO,aAAa;AAChC,OAAOC,OAAO,MAAM,yBAAyB;AAC7CC,OAAO,CAACC,GAAG,CAACH,IAAI,CAAC;AACjB,IAAQI,CAAC,GAAKJ,IAAI,CAACK,MAAM,CAAjBD,CAAC;;AAET;AACA,IAAME,OAAO,GAAGV,KAAK,CAACW,MAAM,CAAC;EAC5B;EACA;EACAC,OAAO,EAAEC,MAAM,CAACC,MAAM,CAACC,GAAG;EAAE;EAC5B;EACA;EACAC,OAAO,EAAE,KAAK;EAAE;EAChBC,eAAe,EAAE,IAAI,CAAE;AACxB,CAAC,CAAC;;AAEF;AACA;AACAP,OAAO,CAACQ,YAAY,CAACC,OAAO,CAACC,GAAG,CAC/B,UAAUN,MAAM,EAAE;EACjB;EACM;EACNA,MAAM,CAACO,OAAO,CAAC,kCAAkC,CAAC,GAAC,IAAI;EACjD,IAAIC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EACzC,IAAIF,KAAK,EAAE;IACP;IACAR,MAAM,CAACO,OAAO,CAAC,OAAO,CAAC,GAAGC,KAAK;EACnC;EACA;EACA,IAAIG,IAAI,GAAGF,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;EACvC,IAAIC,IAAI,EAAE;IACN;IACAX,MAAM,CAACO,OAAO,CAAC,UAAU,CAAC,GAAGI,IAAI;EACrC;EACN;EACA;EACA;EACA;EACA;EACA,OAAOX,MAAM;AACd,CAAC,EACD,UAAUY,KAAK,EAAE;EAChB;EACApB,OAAO,CAACC,GAAG,CAACmB,KAAK,CAAC;EAClB,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC7B,CACD,CAAC;;AAED;AACAhB,OAAO,CAACQ,YAAY,CAACW,QAAQ,CAACT,GAAG,CAChC,UAAUS,QAAQ,EAAE;EACnB;EACA;EACA;EACA;EACA,IAAMC,SAAS,GAAGD,QAAQ,CAACE,IAAI;EAC/B;EACA,IAAMC,IAAI,GAAGF,SAAS,CAACE,IAAI;EAC3B,IAAGA,IAAI,IAAI,CAAC,GAAG,EAAE;IAChB3B,OAAO,CAAC;MAAE4B,IAAI,EAAE,OAAO;MAAEC,OAAO,EAACJ,SAAS,CAACK;IAAI,CAAC,CAAC;IACjDlC,MAAM,CAACmC,IAAI,CAAC,QAAQ,CAAC;EACtB;EACA,OAAOP,QAAQ;AAChB,CAAC,EACD,UAAUH,KAAK,EAAE;EAChB;EACA;EACE;EACFpB,OAAO,CAACC,GAAG,CAACmB,KAAK,CAAC;EAClB,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC7B,CACD,CAAC;AACD,eAAehB,OAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}