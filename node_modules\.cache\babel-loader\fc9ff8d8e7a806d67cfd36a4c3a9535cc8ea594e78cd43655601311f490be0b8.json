{"ast": null, "code": "import { reactive, ref, getCurrentInstance } from 'vue';\nimport store from '@/store/index';\nimport { do_deposit, bind_bank } from '@/api/self/index.js';\nimport { useRouter, useRoute } from 'vue-router';\nimport { useI18n } from 'vue-i18n';\nimport { getdetailbyid } from '@/api/home/<USER>';\nimport { Dialog } from 'vant';\nexport default {\n  name: 'HomeView',\n  setup: function setup() {\n    var _store$state$baseInfo, _store$state$userinfo, _store$state$userinfo2, _store$state$baseInfo2;\n    var _useI18n = useI18n(),\n      t = _useI18n.t;\n    var _useRouter = useRouter(),\n      push = _useRouter.push;\n    var route = useRoute();\n    var _getCurrentInstance = getCurrentInstance(),\n      proxy = _getCurrentInstance.proxy;\n    var paypassword = ref('');\n    var info = ref({});\n    var currency = ref((_store$state$baseInfo = store.state.baseInfo) === null || _store$state$baseInfo === void 0 ? void 0 : _store$state$baseInfo.currency);\n    var tel = ref((_store$state$userinfo = store.state.userinfo) === null || _store$state$userinfo === void 0 ? void 0 : _store$state$userinfo.tel);\n    var infoa = ref(store.state.objInfo);\n    var types = ref('1');\n    var content = ref('');\n    var bank_code = ref('');\n    bind_bank().then(function (res) {\n      if (res.code === 0) {\n        var _res$data$info;\n        bank_code.value = (_res$data$info = res.data.info) === null || _res$data$info === void 0 ? void 0 : _res$data$info.bank_type;\n      }\n    });\n    getdetailbyid(14).then(function (res) {\n      var _res$data;\n      content.value = (_res$data = res.data) === null || _res$data === void 0 ? void 0 : _res$data.content;\n    });\n    var money_check = ref();\n    var money = ref((_store$state$userinfo2 = store.state.userinfo) === null || _store$state$userinfo2 === void 0 ? void 0 : _store$state$userinfo2.balance);\n    var aa = ref(store.state.userinfo);\n    console.log(aa);\n    var moneys = ref((_store$state$baseInfo2 = store.state.baseInfo) === null || _store$state$baseInfo2 === void 0 ? void 0 : _store$state$baseInfo2.recharge_money_list);\n    var clickLeft = function clickLeft() {\n      push('/self');\n    };\n    var clickRight = function clickRight() {\n      push('/tel');\n    };\n    var onSubmit = function onSubmit(values) {\n      console.log(values);\n      // if (!bank_code.value) {\n      // console.log(bank_code)\n      //     Dialog.confirm({\n      //         confirmButtonText:t('msg.queren'),\n      //         cancelButtonText:t('msg.quxiao'),\n      //         title: '',\n      //     message:\n      //         t('msg.tjtkxx'),\n      //     })\n      //     .then(() => {\n      //         // on confirm\n      //         push('/bingbank')\n      //     })\n      //     .catch(() => {\n      //         // on cancel\n      //     });\n      //     return false\n      // }\n      var json = {\n        num: money_check.value == 0 ? money.value : money_check.value,\n        type: 'bank',\n        paypassword: values.paypassword\n        // types: types.value,\n      };\n\n      do_deposit(json).then(function (res) {\n        if (res.code === 0) {\n          proxy.$Message({\n            type: 'success',\n            message: res.info\n          });\n          push('/self');\n        } else {\n          proxy.$Message({\n            type: 'error',\n            message: res.info\n          });\n        }\n      });\n    };\n    console.log(money);\n    return {\n      paypassword: paypassword,\n      types: types,\n      onSubmit: onSubmit,\n      clickLeft: clickLeft,\n      bank_code: bank_code,\n      clickRight: clickRight,\n      info: info,\n      money: money,\n      currency: currency,\n      money_check: money_check,\n      moneys: moneys,\n      content: content,\n      infoa: infoa,\n      tel: tel\n    };\n  }\n};", "map": {"version": 3, "names": ["reactive", "ref", "getCurrentInstance", "store", "do_deposit", "bind_bank", "useRouter", "useRoute", "useI18n", "getdetailbyid", "Dialog", "name", "setup", "_store$state$baseInfo", "_store$state$userinfo", "_store$state$userinfo2", "_store$state$baseInfo2", "_useI18n", "t", "_useRouter", "push", "route", "_getCurrentInstance", "proxy", "paypassword", "info", "currency", "state", "baseInfo", "tel", "userinfo", "infoa", "objInfo", "types", "content", "bank_code", "then", "res", "code", "_res$data$info", "value", "data", "bank_type", "_res$data", "money_check", "money", "balance", "aa", "console", "log", "moneys", "recharge_money_list", "clickLeft", "clickRight", "onSubmit", "values", "json", "num", "type", "$Message", "message"], "sources": ["C:\\Users\\<USER>\\Desktop\\vue3_3.0\\src\\views\\self\\components\\drawing.vue"], "sourcesContent": ["<template>\r\n  <div class=\"home\">\r\n    <van-nav-bar :title=\"$t('msg.tikuan')\" left-arrow @click-left=\"$router.go(-1)\" @click-right=\"clickRight\">\r\n        <template #right>\r\n            <img :src=\"require('@/assets/images/self/hank/tel.png')\" class=\"img\" alt=\"\">\r\n        </template>\r\n    </van-nav-bar>\r\n    <van-form @submit=\"onSubmit\">\r\n      <van-cell-group inset>\r\n          <div class=\"ktx\">\r\n              <div class=\"b\">{{currency}} {{money}}</div>\r\n              <div class=\"t\">{{$t('msg.my_yu_e')}}</div>\r\n          </div>\r\n          <div class=\"check_money\">\r\n           <!--   <div class=\"text\">\r\n                  <span class=\"tel\">{{$t('msg.phone')}}</span>\r\n                  <span>{{tel}}</span>\r\n              </div> -->\r\n            <!--  <div class=\"text\">\r\n                  <span>{{$t('msg.input_yhxz')}}</span>\r\n              </div> -->\r\n             <!-- <div class=\"text\">\r\n                <van-radio-group v-model=\"checked\">\r\n                    <van-radio>\r\n                        <div class=\"label\">{{$t('msg.khlx')}} {{bank_code}}</div>\r\n                    </van-radio>\r\n                </van-radio-group>\r\n              </div> -->\r\n              <div class=\"text\">\r\n                  <van-radio-group v-model=\"types\" direction=\"horizontal\">\r\n                   <!-- <van-radio name=\"2\">{{$t('msg.bank_tx')}}</van-radio> -->\r\n                    <van-radio name=\"1\">{{$t('msg.usdt_tx')}}</van-radio>\r\n                  </van-radio-group>\r\n              </div>\r\n           <!--   <span class=\"span\" :class=\"(money_check == item&&!!money_check && 'check ') +  (!item && ' not_b')\" @click=\"function(){if(item){money_check = item}}\" v-for=\"(item,index) in moneys\" :key=\"index\">{{item}}</span>\r\n              <span class=\"span\" :class=\"(money_check == money && 'check not_b') \" @click=\"money_check=money\">{{$t('msg.all_tx')}}</span> -->\r\n          </div>\r\n        <div class=\"tixian_money\">{{$t('msg.tixian_money')}}</div>\r\n        <van-field\r\n          class=\"zdy\"\r\n          v-model=\"money_check\"\r\n          :placeholder=\"$t('msg.tixian_money')\"\r\n        />\r\n        <van-field\r\n          class=\"zdy\"\r\n          v-model=\"paypassword\"\r\n          type=\"password\"\r\n          name=\"paypassword\"\r\n          :placeholder=\"$t('msg.tx_pwd')\"\r\n          :rules=\"[{ required: true, message: $t('msg.input_tx_pwd') }]\"\r\n        />\r\n      </van-cell-group>\r\n      <div class=\"buttons\">\r\n        <van-button round block type=\"primary\" native-type=\"submit\">\r\n          {{$t('msg.true_tx')}}\r\n        </van-button>\r\n      </div>\r\n      <div class=\"text_b\" v-html=\"content\">\r\n      </div>\r\n    </van-form>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { reactive, ref,getCurrentInstance } from 'vue';\r\nimport store from '@/store/index'\r\nimport {do_deposit, bind_bank} from '@/api/self/index.js'\r\nimport { useRouter,useRoute } from 'vue-router';\r\nimport { useI18n } from 'vue-i18n'\r\nimport {getdetailbyid} from '@/api/home/<USER>'\r\nimport { Dialog } from 'vant'\r\nexport default {\r\n  name: 'HomeView',\r\n  setup() {\r\n    const { t } = useI18n()\r\n    const { push } = useRouter();\r\n    const route = useRoute();\r\n    const {proxy} = getCurrentInstance()\r\n    const paypassword = ref('')\r\n    const info = ref({})\r\n    const currency = ref(store.state.baseInfo?.currency)\r\n    const tel = ref(store.state.userinfo?.tel)\r\n\tconst infoa = ref(store.state.objInfo)\r\n    const types = ref('1')\r\n    const content = ref('')\r\n    const bank_code = ref('')\r\n    bind_bank().then(res => {\r\n        if(res.code === 0) {\r\n            bank_code.value = res.data.info?.bank_type\r\n        }\r\n    })\r\n\r\n    getdetailbyid(14).then(res => {\r\n        content.value = res.data?.content\r\n    })\r\n\r\n    const money_check = ref()\r\n   const money = ref(store.state.userinfo?.balance)\r\n   var aa = ref(store.state.userinfo)\r\n   console.log(aa)\r\n    const moneys = ref(store.state.baseInfo?.recharge_money_list)\r\n\r\n    \r\n    const clickLeft = () => {\r\n        push('/self')\r\n    }\r\n    const clickRight = () => {\r\n        push('/tel')\r\n    }\r\n    \r\n\r\n    const onSubmit = (values) => {\r\n        console.log(values)\r\n        // if (!bank_code.value) {\r\n        // console.log(bank_code)\r\n        //     Dialog.confirm({\r\n        //         confirmButtonText:t('msg.queren'),\r\n        //         cancelButtonText:t('msg.quxiao'),\r\n        //         title: '',\r\n        //     message:\r\n        //         t('msg.tjtkxx'),\r\n        //     })\r\n        //     .then(() => {\r\n        //         // on confirm\r\n        //         push('/bingbank')\r\n        //     })\r\n        //     .catch(() => {\r\n        //         // on cancel\r\n        //     });\r\n        //     return false\r\n        // }\r\n        let json = {\r\n            num: money_check.value ==0 ? money.value : money_check.value,\r\n            type: 'bank',\r\n            paypassword: values.paypassword,\r\n            // types: types.value,\r\n        }\r\n        do_deposit(json).then(res => {\r\n            if(res.code === 0) {\r\n                proxy.$Message({ type: 'success', message:res.info});\r\n                push('/self')\r\n            } else {\r\n                proxy.$Message({ type: 'error', message:res.info});\r\n            }\r\n        })\r\n    };\r\nconsole.log(money)\r\n\r\n\r\n    return {\r\n        paypassword,\r\n        types,\r\n        onSubmit,\r\n        clickLeft,\r\n        bank_code,\r\n        clickRight,\r\n        info,\r\n        money,\r\n        currency,\r\n        money_check,\r\n        moneys,\r\n        content,\r\n\t\tinfoa,\r\n        tel\r\n    };\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n@import '@/styles/theme.scss';\r\n.home{\r\n    // background-image: url('~@/assets/images/home/<USER>') !important;\r\n    background-color: #f5f5f5;\r\n    border-radius: 0;\r\n}\r\n.home{\r\n    :deep(.van-nav-bar){\r\n        background-color: #fff;\r\n        // background-color: $theme;\r\n        color: #000;\r\n        .van-nav-bar__left{\r\n            .van-icon{\r\n                color: #000;\r\n            }\r\n        }\r\n        .van-nav-bar__title{\r\n            color: #000;\r\n            \r\n        }\r\n        .van-nav-bar__right{\r\n            img{\r\n                height: 42px;\r\n            }\r\n        }\r\n    }\r\n    :deep(.van-form){\r\n        padding: 40px 0 0;\r\n\r\n        .van-cell.van-cell--clickable{\r\n            border-left: 5px solid $theme;\r\n            padding: 32px;\r\n            text-align: left;\r\n            margin: 20px 0;\r\n            border-bottom: none;\r\n            box-shadow: $shadow;\r\n            .van-cell__right-icon{\r\n                color: $theme;\r\n            }\r\n        }\r\n        .van-cell-group--inset{\r\n            padding: 0 30px;\r\n            background-color: initial;\r\n        }\r\n        .van-cell{\r\n            padding: 23px 10px;\r\n            border-bottom: 1px solid  var(--van-cell-border-color);\r\n            &.zdy {\r\n                margin-bottom: 20px;\r\n                border-radius: 40px;\r\n                padding-left: 30px;\r\n            }\r\n            .van-field__left-icon{\r\n                width:90px;\r\n                text-align: center;\r\n                .van-icon__image{\r\n                    height: 42px;\r\n                    width: auto;\r\n                }\r\n                .icon{\r\n                    height: 42px;\r\n                    width: auto;\r\n                    vertical-align:middle;\r\n                }\r\n                .van-dropdown-menu{\r\n                  .van-dropdown-menu__bar{\r\n                    height: auto;\r\n                    background: none;\r\n                    box-shadow: none;\r\n                  }\r\n                  .van-cell{\r\n                    padding: 30px 80px;\r\n                  }\r\n                }\r\n            }\r\n            .van-field__control{\r\n                font-size: 24px;\r\n            }\r\n            &::after {\r\n                display: none;\r\n            }\r\n        }\r\n        .van-checkbox{\r\n            margin: 30px 0 60px 0;\r\n            .van-checkbox__icon{\r\n                font-size: 50px;\r\n                margin-right: 80px;\r\n                &.van-checkbox__icon--checked .van-icon{\r\n                    background-color:$theme;\r\n                    border-color:$theme;\r\n                }\r\n            }\r\n            .van-checkbox__label{\r\n                font-size: 24px;\r\n            }\r\n        }\r\n        .text_b{\r\n            margin:70px 60px 40px;\r\n            font-size: 27px;\r\n            color: #333;\r\n            text-align: left;\r\n            line-height: 1.5;\r\n            .tex{\r\n                margin-top: 20px;\r\n            }\r\n        }\r\n        .buttons{\r\n            padding: 0 76px;\r\n            .van-button{\r\n                font-size: 28px;\r\n                padding: 20px 0;\r\n                height: auto;\r\n                background: #000;\r\n                border: none;\r\n                color: #fff;\r\n            }\r\n            .van-button--plain{\r\n                margin-top: 40px;\r\n            }\r\n        }\r\n        .tixian_money{\r\n            text-align: left;\r\n            font-size: 30px;\r\n            margin-bottom: 20px;\r\n            color: #333;\r\n        }\r\n        .ktx{\r\n            width: 100%;\r\n            height: 190px;\r\n            border-radius: 20px;\r\n            padding: 24px 50px;\r\n            text-align: left;\r\n            // margin-bottom: 35px;\r\n            background-color: #fe2c55;\r\n            text-align: center;\r\n            .t{\r\n                font-size: 20px;\r\n                color: #fff;\r\n                margin-bottom: 10px;\r\n                opacity: 0.7;\r\n            }\r\n            .b{\r\n                font-size: 50px;\r\n                color: #fefefe;\r\n                margin-bottom: 20px;\r\n            }\r\n        }\r\n        .check_money{\r\n            display: flex;\r\n            flex-wrap: wrap;\r\n            margin-bottom: 40px;\r\n            background-color: #fff;\r\n            padding: 24px;\r\n            border-radius: 20px;\r\n            color: #333;\r\n            .text{\r\n                display: flex;\r\n                width: 100%;\r\n                text-align: left;\r\n                font-size: 28px;\r\n                margin-bottom: 25px;\r\n                span{\r\n                    flex: 1;\r\n                    &.tel{\r\n                        color: #999;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    :deep(.van-){\r\n        .van-dialog__content{\r\n            padding: 50px;\r\n        }\r\n        .van-dialog__footer{\r\n            .van-dialog__confirm{\r\n                color: $theme;\r\n            }\r\n        }\r\n    }\r\n}\r\n</style>\r\n"], "mappings": "AAgEA,SAASA,QAAQ,EAAEC,GAAG,EAACC,kBAAiB,QAAS,KAAK;AACtD,OAAOC,KAAI,MAAO,eAAc;AAChC,SAAQC,UAAU,EAAEC,SAAS,QAAO,qBAAoB;AACxD,SAASC,SAAS,EAACC,QAAO,QAAS,YAAY;AAC/C,SAASC,OAAM,QAAS,UAAS;AACjC,SAAQC,aAAa,QAAO,qBAAoB;AAChD,SAASC,MAAK,QAAS,MAAK;AAC5B,eAAe;EACbC,IAAI,EAAE,UAAU;EAChBC,KAAK,WAAAA,MAAA,EAAG;IAAA,IAAAC,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA;IACN,IAAAC,QAAA,GAAcT,OAAO,CAAC;MAAdU,CAAA,GAAAD,QAAA,CAAAC,CAAA;IACR,IAAAC,UAAA,GAAiBb,SAAS,CAAC,CAAC;MAApBc,IAAG,GAAAD,UAAA,CAAHC,IAAG;IACX,IAAMC,KAAI,GAAId,QAAQ,CAAC,CAAC;IACxB,IAAAe,mBAAA,GAAgBpB,kBAAkB,CAAC;MAA5BqB,KAAK,GAAAD,mBAAA,CAALC,KAAK;IACZ,IAAMC,WAAU,GAAIvB,GAAG,CAAC,EAAE;IAC1B,IAAMwB,IAAG,GAAIxB,GAAG,CAAC,CAAC,CAAC;IACnB,IAAMyB,QAAO,GAAIzB,GAAG,EAAAY,qBAAA,GAACV,KAAK,CAACwB,KAAK,CAACC,QAAQ,cAAAf,qBAAA,uBAApBA,qBAAA,CAAsBa,QAAQ;IACnD,IAAMG,GAAE,GAAI5B,GAAG,EAAAa,qBAAA,GAACX,KAAK,CAACwB,KAAK,CAACG,QAAQ,cAAAhB,qBAAA,uBAApBA,qBAAA,CAAsBe,GAAG;IAC5C,IAAME,KAAI,GAAI9B,GAAG,CAACE,KAAK,CAACwB,KAAK,CAACK,OAAO;IAClC,IAAMC,KAAI,GAAIhC,GAAG,CAAC,GAAG;IACrB,IAAMiC,OAAM,GAAIjC,GAAG,CAAC,EAAE;IACtB,IAAMkC,SAAQ,GAAIlC,GAAG,CAAC,EAAE;IACxBI,SAAS,CAAC,CAAC,CAAC+B,IAAI,CAAC,UAAAC,GAAE,EAAK;MACpB,IAAGA,GAAG,CAACC,IAAG,KAAM,CAAC,EAAE;QAAA,IAAAC,cAAA;QACfJ,SAAS,CAACK,KAAI,IAAAD,cAAA,GAAIF,GAAG,CAACI,IAAI,CAAChB,IAAI,cAAAc,cAAA,uBAAbA,cAAA,CAAeG,SAAQ;MAC7C;IACJ,CAAC;IAEDjC,aAAa,CAAC,EAAE,CAAC,CAAC2B,IAAI,CAAC,UAAAC,GAAE,EAAK;MAAA,IAAAM,SAAA;MAC1BT,OAAO,CAACM,KAAI,IAAAG,SAAA,GAAIN,GAAG,CAACI,IAAI,cAAAE,SAAA,uBAARA,SAAA,CAAUT,OAAM;IACpC,CAAC;IAED,IAAMU,WAAU,GAAI3C,GAAG,CAAC;IACzB,IAAM4C,KAAI,GAAI5C,GAAG,EAAAc,sBAAA,GAACZ,KAAK,CAACwB,KAAK,CAACG,QAAQ,cAAAf,sBAAA,uBAApBA,sBAAA,CAAsB+B,OAAO;IAC/C,IAAIC,EAAC,GAAI9C,GAAG,CAACE,KAAK,CAACwB,KAAK,CAACG,QAAQ;IACjCkB,OAAO,CAACC,GAAG,CAACF,EAAE;IACb,IAAMG,MAAK,GAAIjD,GAAG,EAAAe,sBAAA,GAACb,KAAK,CAACwB,KAAK,CAACC,QAAQ,cAAAZ,sBAAA,uBAApBA,sBAAA,CAAsBmC,mBAAmB;IAG5D,IAAMC,SAAQ,GAAI,SAAZA,SAAQA,CAAA,EAAU;MACpBhC,IAAI,CAAC,OAAO;IAChB;IACA,IAAMiC,UAAS,GAAI,SAAbA,UAASA,CAAA,EAAU;MACrBjC,IAAI,CAAC,MAAM;IACf;IAGA,IAAMkC,QAAO,GAAI,SAAXA,QAAOA,CAAKC,MAAM,EAAK;MACzBP,OAAO,CAACC,GAAG,CAACM,MAAM;MAClB;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAIC,IAAG,GAAI;QACPC,GAAG,EAAEb,WAAW,CAACJ,KAAI,IAAI,IAAIK,KAAK,CAACL,KAAI,GAAII,WAAW,CAACJ,KAAK;QAC5DkB,IAAI,EAAE,MAAM;QACZlC,WAAW,EAAE+B,MAAM,CAAC/B;QACpB;MACJ;;MACApB,UAAU,CAACoD,IAAI,CAAC,CAACpB,IAAI,CAAC,UAAAC,GAAE,EAAK;QACzB,IAAGA,GAAG,CAACC,IAAG,KAAM,CAAC,EAAE;UACff,KAAK,CAACoC,QAAQ,CAAC;YAAED,IAAI,EAAE,SAAS;YAAEE,OAAO,EAACvB,GAAG,CAACZ;UAAI,CAAC,CAAC;UACpDL,IAAI,CAAC,OAAO;QAChB,OAAO;UACHG,KAAK,CAACoC,QAAQ,CAAC;YAAED,IAAI,EAAE,OAAO;YAAEE,OAAO,EAACvB,GAAG,CAACZ;UAAI,CAAC,CAAC;QACtD;MACJ,CAAC;IACL,CAAC;IACLuB,OAAO,CAACC,GAAG,CAACJ,KAAK;IAGb,OAAO;MACHrB,WAAW,EAAXA,WAAW;MACXS,KAAK,EAALA,KAAK;MACLqB,QAAQ,EAARA,QAAQ;MACRF,SAAS,EAATA,SAAS;MACTjB,SAAS,EAATA,SAAS;MACTkB,UAAU,EAAVA,UAAU;MACV5B,IAAI,EAAJA,IAAI;MACJoB,KAAK,EAALA,KAAK;MACLnB,QAAQ,EAARA,QAAQ;MACRkB,WAAW,EAAXA,WAAW;MACXM,MAAM,EAANA,MAAM;MACNhB,OAAO,EAAPA,OAAO;MACbH,KAAK,EAALA,KAAK;MACCF,GAAE,EAAFA;IACJ,CAAC;EACH;AACF"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}