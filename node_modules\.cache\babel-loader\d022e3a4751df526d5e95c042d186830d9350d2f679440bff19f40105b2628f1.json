{"ast": null, "code": "import { ref, getCurrentInstance, watch } from 'vue';\nimport { useI18n } from 'vue-i18n';\nimport store from '../../store/index';\n// import logo from '@/assets/images/news/logo.png'\nimport langVue from '@/components/lang.vue';\nexport default {\n  components: {\n    langVue: langVue\n  },\n  name: 'HomeView',\n  props: {\n    hideLang: {\n      type: Boolean,\n      default: false\n    },\n    title: {\n      type: String,\n      default: ''\n    },\n    leftArrow: {\n      type: Boolean,\n      default: false\n    }\n  },\n  setup: function setup() {\n    var _store$state$baseInfo, _store$state$baseInfo2, _store$state$baseInfo3;\n    var _getCurrentInstance = getCurrentInstance(),\n      proxy = _getCurrentInstance.proxy;\n    var _useI18n = useI18n(),\n      locale = _useI18n.locale,\n      t = _useI18n.t;\n    var show = ref(false);\n    var langcheck = ref('');\n    var langImg = ref('');\n    var logo = ref((_store$state$baseInfo = store.state.baseInfo) === null || _store$state$baseInfo === void 0 ? void 0 : _store$state$baseInfo.site_icon);\n    var app_name = ref((_store$state$baseInfo2 = store.state.baseInfo) === null || _store$state$baseInfo2 === void 0 ? void 0 : _store$state$baseInfo2.app_name);\n    langcheck.value = store.state.lang;\n    langImg.value = store.state.langImg;\n    var langs = ref((_store$state$baseInfo3 = store.state.baseInfo) === null || _store$state$baseInfo3 === void 0 ? void 0 : _store$state$baseInfo3.languageList);\n    var handSeletlanguages = function handSeletlanguages(row) {\n      langcheck.value = row.link;\n      langImg.value = row.image_url;\n    };\n    var submitLang = function submitLang() {\n      locale.value = langcheck.value;\n      store.dispatch('changelang', langcheck.value);\n      store.dispatch('changelangImg', langImg.value);\n      show.value = false;\n      console.log(proxy);\n      proxy.$Message({\n        type: 'success',\n        message: t('msg.switch_lang_success')\n      });\n    };\n    watch(function () {\n      return store.state.baseInfo;\n    }, function (newVal) {\n      logo.value = newVal === null || newVal === void 0 ? void 0 : newVal.site_icon;\n      langs.value = (newVal === null || newVal === void 0 ? void 0 : newVal.languageList) || [];\n    }, {\n      deep: true\n    });\n    return {\n      show: show,\n      langs: langs,\n      handSeletlanguages: handSeletlanguages,\n      langcheck: langcheck,\n      submitLang: submitLang,\n      logo: logo,\n      app_name: app_name\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "getCurrentInstance", "watch", "useI18n", "store", "langVue", "components", "name", "props", "hideLang", "type", "Boolean", "default", "title", "String", "leftArrow", "setup", "_store$state$baseInfo", "_store$state$baseInfo2", "_store$state$baseInfo3", "_getCurrentInstance", "proxy", "_useI18n", "locale", "t", "show", "langcheck", "langImg", "logo", "state", "baseInfo", "site_icon", "app_name", "value", "lang", "langs", "languageList", "handSeletlanguages", "row", "link", "image_url", "submitLang", "dispatch", "console", "log", "$Message", "message", "newVal", "deep"], "sources": ["C:\\Users\\<USER>\\Downloads\\Telegram Desktop\\vue3_3.0\\vue3_3.0\\src\\views\\login\\index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"home\">\r\n    <van-nav-bar :title=\"title\" :left-arrow=\"leftArrow\" @click-left=\"$router.go(-1)\">\r\n        <template #right>\r\n            <lang-vue v-if=\"!hideLang\" color='white'></lang-vue>\r\n        </template>\r\n    </van-nav-bar>\r\n\t  <img :src=\"require('@/assets/images/home/<USER>')\" width=\"130\" height=\"130\" alt=\"\" class=\"li_img\">\r\n    <!-- <img :src=\"logo\" class=\"logo\" alt=\"\" :class=\"!leftArrow && 'lo'\" width=\"80\"> -->\r\n    <!-- <div class=\"title\">{{app_name}}</div> -->\r\n\r\n    <van-dialog v-model:show=\"show\" :showConfirmButton=\"false\" class=\"lang-dialog\">\r\n      <div class=\"lang_box\">\r\n        <img :src=\"require('@/assets/images/register/lang_bg.png')\" class=\"lang_bg\" />\r\n        <div class=\"content\">\r\n            <img :src=\"require('@/assets/images/register/qiu.png')\" class=\"qiu\" />\r\n            <div class=\"langs\">\r\n              <span class=\"li\" :class=\"langcheck==item.link && 'check'\" v-for=\"(item,index) in langs\" :key=\"index\"  @click=\"handSeletlanguages(item)\">\r\n                <img :src=\"item.image_url\" class=\"img\" alt=\"\">\r\n                <span class=\"text\">{{item.name}}</span>\r\n              </span>\r\n            </div>\r\n            <div class=\"btn\">\r\n              <van-button round block type=\"primary\" @click=\"submitLang\">\r\n                {{$t('msg.nowQh')}}\r\n              </van-button>\r\n            </div>\r\n        </div>\r\n      </div>\r\n    </van-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { ref, getCurrentInstance,watch } from 'vue';\r\nimport { useI18n } from 'vue-i18n'\r\nimport store from '../../store/index'\r\n// import logo from '@/assets/images/news/logo.png'\r\nimport langVue from '@/components/lang.vue'\r\nexport default {\r\n  components: {langVue},\r\n  name: 'HomeView',\r\n  props: {\r\n    hideLang: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    title: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    leftArrow: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n  },\r\n  setup(){\r\n    const {proxy} = getCurrentInstance()\r\n    const { locale,t } = useI18n()\r\n    const show = ref(false);\r\n    const langcheck = ref('')\r\n    const langImg = ref('')\r\n    const logo = ref(store.state.baseInfo?.site_icon)\r\n    const app_name = ref(store.state.baseInfo?.app_name)\r\n    langcheck.value = store.state.lang\r\n    langImg.value = store.state.langImg\r\n    const langs = ref(store.state.baseInfo?.languageList)\r\n    const handSeletlanguages = (row) => {\r\n      langcheck.value = row.link\r\n      langImg.value = row.image_url\r\n    }\r\n    const submitLang = () => {\r\n      locale.value = langcheck.value\r\n      store.dispatch('changelang',langcheck.value)\r\n      store.dispatch('changelangImg',langImg.value)\r\n      show.value = false\r\n      console.log(proxy)\r\n      proxy.$Message({ type: 'success', message: t('msg.switch_lang_success') });\r\n    }\r\n    watch(() => store.state.baseInfo,(newVal)=>{\r\n      logo.value = newVal?.site_icon\r\n      langs.value = (newVal?.languageList) || []\r\n    }, { deep: true })\r\n\r\n    return {show,langs,handSeletlanguages,langcheck,submitLang,logo,app_name}\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.home{\r\n  position: relative;\r\n  padding-top: calc(var(--van-nav-bar-height) + 100px);\r\n \r\n        :deep(.van-nav-bar){\r\n            position: fixed !important;\r\n            // top: 0;\r\n            color: #333;\r\n            padding: 10px 0;\r\n            width: 100%;\r\n           // background-color: #0a3cff;\r\n            z-index: 3;\r\n            &::after{\r\n              border-bottom-width: 0;\r\n            }\r\n            .van-nav-bar__left{\r\n                .van-icon{\r\n                    color: #fff;\r\n                }\r\n            }\r\n            .van-nav-bar__title{\r\n                color: #fff;\r\n                // font-weight: 600;\r\n                font-size: 32px;\r\n            }\r\n        }\r\n}\r\n  .bg{\r\n    width: 100%;\r\n  }\r\n  .logo{\r\n    // width: 135px;\r\n    display: block;\r\n    margin: 50px auto 10px;\r\n    position: relative;\r\n    z-index: 2;\r\n    box-shadow: 0 0 5px 0 #cfcffc;\r\n    border-radius: 32px;\r\n    &.lo {\r\n      margin-top: 180px;\r\n    }\r\n  }\r\n  .title{\r\n    font-size: 32px;\r\n    color: #fff;\r\n    width: 60%;\r\n    margin: 0 auto;\r\n  }\r\n  :deep(.lang-dialog) {\r\n    .van-dialog__content {\r\n      max-height: 80vh;\r\n      overflow-y: auto;\r\n    }\r\n  }\r\n  .lang_box{\r\n    width: 100%;\r\n    position: relative;\r\n    padding-top: 80px;\r\n    .lang_bg{\r\n      width: 100%;\r\n      position: absolute;\r\n      top: 0;\r\n      left: 0;\r\n    }\r\n    .content{\r\n      position: relative;\r\n      z-index: 1;\r\n      .qiu{\r\n        width: 175px;\r\n        border-radius: 50%;\r\n        box-shadow: $shadow;\r\n        margin-bottom: 6px;\r\n      }\r\n      .langs{\r\n        margin-bottom: 15px;\r\n        max-height: 50vh;\r\n        overflow-y: auto;\r\n        -webkit-overflow-scrolling: touch;\r\n        .li{\r\n          padding: 24px 112px;\r\n          display: block;\r\n          text-align: left;\r\n          margin-bottom: 10px;\r\n          &.check{\r\n            box-shadow: $shadow;\r\n          }\r\n          .img{\r\n            width: 80px;\r\n            margin-right: 34px;\r\n            vertical-align: middle;\r\n          }\r\n          .text{\r\n            font-size: 26px;\r\n            color: #666;\r\n          }\r\n        }\r\n      }\r\n      .btn{\r\n        padding: 50px 54px 50px;\r\n      }\r\n    }\r\n  }\r\n</style>\r\n"], "mappings": "AAkCA,SAASA,GAAG,EAAEC,kBAAkB,EAACC,KAAI,QAAS,KAAK;AACnD,SAASC,OAAM,QAAS,UAAS;AACjC,OAAOC,KAAI,MAAO,mBAAkB;AACpC;AACA,OAAOC,OAAM,MAAO,uBAAsB;AAC1C,eAAe;EACbC,UAAU,EAAE;IAACD,OAAO,EAAPA;EAAO,CAAC;EACrBE,IAAI,EAAE,UAAU;EAChBC,KAAK,EAAE;IACLC,QAAQ,EAAE;MACRC,IAAI,EAAEC,OAAO;MACbC,OAAO,EAAE;IACX,CAAC;IACDC,KAAK,EAAE;MACLH,IAAI,EAAEI,MAAM;MACZF,OAAO,EAAE;IACX,CAAC;IACDG,SAAS,EAAE;MACTL,IAAI,EAAEC,OAAO;MACbC,OAAO,EAAE;IACX;EACF,CAAC;EACDI,KAAK,WAAAA,MAAA,EAAE;IAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA;IACL,IAAAC,mBAAA,GAAgBnB,kBAAkB,CAAC;MAA5BoB,KAAK,GAAAD,mBAAA,CAALC,KAAK;IACZ,IAAAC,QAAA,GAAqBnB,OAAO,CAAC;MAArBoB,MAAM,GAAAD,QAAA,CAANC,MAAM;MAACC,CAAA,GAAAF,QAAA,CAAAE,CAAA;IACf,IAAMC,IAAG,GAAIzB,GAAG,CAAC,KAAK,CAAC;IACvB,IAAM0B,SAAQ,GAAI1B,GAAG,CAAC,EAAE;IACxB,IAAM2B,OAAM,GAAI3B,GAAG,CAAC,EAAE;IACtB,IAAM4B,IAAG,GAAI5B,GAAG,EAAAiB,qBAAA,GAACb,KAAK,CAACyB,KAAK,CAACC,QAAQ,cAAAb,qBAAA,uBAApBA,qBAAA,CAAsBc,SAAS;IAChD,IAAMC,QAAO,GAAIhC,GAAG,EAAAkB,sBAAA,GAACd,KAAK,CAACyB,KAAK,CAACC,QAAQ,cAAAZ,sBAAA,uBAApBA,sBAAA,CAAsBc,QAAQ;IACnDN,SAAS,CAACO,KAAI,GAAI7B,KAAK,CAACyB,KAAK,CAACK,IAAG;IACjCP,OAAO,CAACM,KAAI,GAAI7B,KAAK,CAACyB,KAAK,CAACF,OAAM;IAClC,IAAMQ,KAAI,GAAInC,GAAG,EAAAmB,sBAAA,GAACf,KAAK,CAACyB,KAAK,CAACC,QAAQ,cAAAX,sBAAA,uBAApBA,sBAAA,CAAsBiB,YAAY;IACpD,IAAMC,kBAAiB,GAAI,SAArBA,kBAAiBA,CAAKC,GAAG,EAAK;MAClCZ,SAAS,CAACO,KAAI,GAAIK,GAAG,CAACC,IAAG;MACzBZ,OAAO,CAACM,KAAI,GAAIK,GAAG,CAACE,SAAQ;IAC9B;IACA,IAAMC,UAAS,GAAI,SAAbA,UAASA,CAAA,EAAU;MACvBlB,MAAM,CAACU,KAAI,GAAIP,SAAS,CAACO,KAAI;MAC7B7B,KAAK,CAACsC,QAAQ,CAAC,YAAY,EAAChB,SAAS,CAACO,KAAK;MAC3C7B,KAAK,CAACsC,QAAQ,CAAC,eAAe,EAACf,OAAO,CAACM,KAAK;MAC5CR,IAAI,CAACQ,KAAI,GAAI,KAAI;MACjBU,OAAO,CAACC,GAAG,CAACvB,KAAK;MACjBA,KAAK,CAACwB,QAAQ,CAAC;QAAEnC,IAAI,EAAE,SAAS;QAAEoC,OAAO,EAAEtB,CAAC,CAAC,yBAAyB;MAAE,CAAC,CAAC;IAC5E;IACAtB,KAAK,CAAC;MAAA,OAAME,KAAK,CAACyB,KAAK,CAACC,QAAQ;IAAA,GAAC,UAACiB,MAAM,EAAG;MACzCnB,IAAI,CAACK,KAAI,GAAIc,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEhB,SAAQ;MAC7BI,KAAK,CAACF,KAAI,GAAI,CAACc,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEX,YAAY,KAAK,EAAC;IAC3C,CAAC,EAAE;MAAEY,IAAI,EAAE;IAAK,CAAC;IAEjB,OAAO;MAACvB,IAAI,EAAJA,IAAI;MAACU,KAAK,EAALA,KAAK;MAACE,kBAAkB,EAAlBA,kBAAkB;MAACX,SAAS,EAATA,SAAS;MAACe,UAAU,EAAVA,UAAU;MAACb,IAAI,EAAJA,IAAI;MAACI,QAAQ,EAARA;IAAQ;EAC1E;AACF"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}