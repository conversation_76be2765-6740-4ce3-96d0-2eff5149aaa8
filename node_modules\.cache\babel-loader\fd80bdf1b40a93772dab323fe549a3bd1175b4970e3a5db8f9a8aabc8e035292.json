{"ast": null, "code": "function _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return _typeof(key) === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (_typeof(input) !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (_typeof(res) !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\nimport loginTop from './index.vue';\nimport { ref, getCurrentInstance, watch, computed } from 'vue';\nimport store from '@/store/index';\nimport { useRouter, useRoute } from 'vue-router';\nimport { do_register, login } from '@/api/login/index';\nimport { Toast } from 'vant';\nexport default {\n  name: 'HomeView',\n  components: {\n    loginTop: loginTop\n  },\n  setup: function setup() {\n    var _route$query, _route$query2, _baseInfo$value;\n    var route = useRoute();\n    var qv = ((_route$query = route.query) === null || _route$query === void 0 ? void 0 : _route$query.data) || 44;\n    var _useRouter = useRouter(),\n      push = _useRouter.push;\n    var _getCurrentInstance = getCurrentInstance(),\n      proxy = _getCurrentInstance.proxy;\n    var baseInfo = ref(store.state.baseInfo);\n    var invite_code = ref('');\n    if ((_route$query2 = route.query) !== null && _route$query2 !== void 0 && _route$query2.invite_code) {\n      var _route$query3;\n      invite_code.value = (_route$query3 = route.query) === null || _route$query3 === void 0 ? void 0 : _route$query3.invite_code;\n    }\n    var tel = ref('');\n    var pwd = ref('');\n    var pwd2 = ref('');\n    var depositPwd = ref('');\n    var userName = ref('');\n    var option = ref(((_baseInfo$value = baseInfo.value) === null || _baseInfo$value === void 0 ? void 0 : _baseInfo$value.area_code) || []);\n    var area_code = ref(option.value.map(function (rr) {\n      return {\n        text: rr,\n        value: rr\n      };\n    }));\n    var onSubmit = function onSubmit(values) {\n      // console.log(values)\n      // console.log(qv)\n      if (values.pwd != values.pwd2) {\n        Toast.fail('两次输入的密码不正确');\n        return false;\n      }\n      var json = JSON.parse(JSON.stringify(values));\n      delete json.pwd2;\n      json.qv = qv;\n      do_register(json).then(function (res) {\n        if (res.code === 0) {\n          proxy.$Message({\n            type: 'success',\n            message: res.info\n          });\n          var info = {\n            tel: tel.value,\n            pwd: pwd.value,\n            qv: qv_v\n          };\n          login(info).then(function (red) {\n            if (red.code === 0) {\n              store.dispatch('changetoken', red.token);\n              store.dispatch('changeuserinfo', red.userinfo || {});\n              proxy.$Message({\n                type: 'success',\n                message: red.info\n              });\n              // 记住密码\n              var useri = _objectSpread(_objectSpread({}, json), {\n                checked: true\n              });\n              store.dispatch('changeUser', useri);\n              push('/');\n            } else {\n              proxy.$Message({\n                type: 'error',\n                message: red.info\n              });\n            }\n          });\n        } else {\n          proxy.$Message({\n            type: 'error',\n            message: res.info\n          });\n        }\n      });\n    };\n    return {\n      tel: tel,\n      pwd: pwd,\n      pwd2: pwd2,\n      depositPwd: depositPwd,\n      userName: userName,\n      invite_code: invite_code,\n      onSubmit: onSubmit,\n      area_code: area_code,\n      qv: qv\n    };\n  }\n};", "map": {"version": 3, "names": ["loginTop", "ref", "getCurrentInstance", "watch", "computed", "store", "useRouter", "useRoute", "do_register", "login", "Toast", "name", "components", "setup", "_route$query", "_route$query2", "_baseInfo$value", "route", "qv", "query", "data", "_useRouter", "push", "_getCurrentInstance", "proxy", "baseInfo", "state", "invite_code", "_route$query3", "value", "tel", "pwd", "pwd2", "depositPwd", "userName", "option", "area_code", "map", "rr", "text", "onSubmit", "values", "fail", "json", "JSON", "parse", "stringify", "then", "res", "code", "$Message", "type", "message", "info", "qv_v", "red", "dispatch", "token", "userinfo", "useri", "_objectSpread", "checked"], "sources": ["C:\\Users\\<USER>\\Downloads\\Telegram Desktop\\vue3_3.0\\vue3_3.0\\src\\views\\login\\register.vue"], "sourcesContent": ["<template>\r\n  <div class=\"homes\">\r\n    <login-top  hide-lang :title=\"$t('msg.register')\" left-arrow></login-top>\r\n    <van-form @submit=\"onSubmit\" style=\"z-index: 11;\">\r\n\r\n      <div class=\"select\">\r\n        <div @click=\"$router.push({ path: '/area',query:{data:2} })\" class=\"selects\" name=\"qv\">+{{ qv }}</div>\r\n        <!-- <select class=\"selects\" name=\"qv\" v-model=\"qv\">\r\n          <option :value=\"v.value\" v-for=\"v in area_code\">{{ v.text }}</option>\r\n\r\n        </select> -->\r\n        <!-- <img class=\"show\" :src=\"require('@/assets/images/home/<USER>')\" alt=\"\">\r\n        <img class=\"chose\" :src=\"require('@/assets/images/home/<USER>')\" alt=\"\"> -->\r\n      </div>\r\n      <van-cell-group inset >\r\n        <van-field label-width=\"100\" class=\"zdy ent\" v-model=\"tel\" name=\"tel\" :label=\"$t('msg.phone')\"\r\n          :placeholder=\"$t('msg.phone')\" :rules=\"[{ required: true, message: $t('msg.input_phone') }]\">\r\n       \r\n       \r\n          <!-- <template #left-icon>\r\n            <van-dropdown-menu active-color=\"#6833ff\">\r\n              <van-dropdown-item v-model=\"qv\" :options=\"area_code\" />\r\n            </van-dropdown-menu>\r\n          </template> -->\r\n        </van-field>\r\n       \r\n        <!-- <van-field\r\n         label-width=\"100\"\r\n          v-model=\"userName\"\r\n          name=\"userName\"\r\n          :placeholder=\"$t('msg.input_username')\"\r\n          :rules=\"[{ required: true, message: $t('msg.input_username') }]\"\r\n        /> -->\r\n        <van-field label-width=\"100\" v-model=\"pwd\" type=\"password\" name=\"pwd\" :label=\"$t('msg.pwd')\"\r\n          :placeholder=\"$t('msg.pwd')\" :rules=\"[{ required: true, message: $t('msg.input_pwd') }]\" />\r\n        <van-field label-width=\"100\" v-model=\"pwd2\" type=\"password\" name=\"pwd2\" :label=\"$t('msg.true_pwd')\"\r\n          :placeholder=\"$t('msg.true_pwd')\" :rules=\"[{ required: true, message: $t('msg.input_true_pwd') }]\" />\r\n        <van-field label-width=\"100\" v-model=\"depositPwd\" name=\"depositPwd\" :label=\"$t('msg.tx_pwd')\"\r\n          :placeholder=\"$t('msg.tx_pwd')\" :rules=\"[{ required: true, message: $t('msg.input_t_pwd') }]\" />\r\n          <van-field label-width=\"100\" v-model=\"invite_code\" name=\"invite_code\" :label=\"$t('msg.code')\"\r\n          :placeholder=\"$t('msg.code')\" :rules=\"[{ required: true, message: $t('msg.input_code') }]\" />\r\n      </van-cell-group>\r\n      <div class=\"buttons\">\r\n        <van-button round=\"5\" block plain type=\"primary\" native-type=\"submit\" style=\"border-radius: 10px;\">\r\n          {{ $t('msg.register1') }}\r\n        </van-button>\r\n        <!-- <van-button round block plain  type=\"primary\" @click=\"$router.push({path: '/login'})\">\r\n          {{$t('msg.login')}}\r\n        </van-button> -->\r\n        <div class=\"gore\">\r\n          <!-- <span>已有账号</span> -->\r\n          <span class=\"gores\" @click=\"$router.push({ path: '/login' })\"> {{ $t('msg.login') }} </span>\r\n        </div>\r\n\r\n      </div>\r\n    </van-form>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport loginTop from './index.vue'\r\nimport { ref, getCurrentInstance, watch, computed } from 'vue';\r\nimport store from '@/store/index'\r\nimport { useRouter, useRoute } from 'vue-router';\r\nimport { do_register, login } from '@/api/login/index'\r\nimport { Toast } from 'vant';\r\nexport default {\r\n  name: 'HomeView',\r\n  components: { loginTop },\r\n  setup() {\r\n\r\n    const route = useRoute()\r\n\t\r\n    const qv = route.query?.data || 44;\r\n\r\n    const { push } = useRouter();\r\n   \r\n    const { proxy } = getCurrentInstance()\r\n    const baseInfo = ref(store.state.baseInfo)\r\n\r\n\r\n    const invite_code = ref('');\r\n    if (route.query?.invite_code) {\r\n      invite_code.value = route.query?.invite_code\r\n    }\r\n    const tel = ref('');\r\n    const pwd = ref('');\r\n    const pwd2 = ref('');\r\n    const depositPwd = ref('');\r\n    const userName = ref('');\r\n    const option = ref((baseInfo.value?.area_code) || [])\r\n    const area_code = ref(option.value.map(rr => { return { text: rr, value: rr } }))\r\n    const onSubmit = (values) => {\r\n      // console.log(values)\r\n      // console.log(qv)\r\n      if (values.pwd != values.pwd2) {\r\n        Toast.fail('两次输入的密码不正确')\r\n        return false\r\n      }\r\n      const json = JSON.parse(JSON.stringify(values))\r\n      delete json.pwd2\r\n      json.qv = qv\r\n      do_register(json).then(res => {\r\n        if (res.code === 0) {\r\n          proxy.$Message({ type: 'success', message: res.info });\r\n          let info = {\r\n            tel: tel.value,\r\n            pwd: pwd.value,\r\n            qv: qv_v,\r\n          }\r\n          login(info).then(red => {\r\n            if (red.code === 0) {\r\n              store.dispatch('changetoken', red.token)\r\n              store.dispatch('changeuserinfo', red.userinfo || {})\r\n              proxy.$Message({ type: 'success', message: red.info });\r\n              // 记住密码\r\n              const useri = { ...json, ...{ checked: true } }\r\n              store.dispatch('changeUser', useri)\r\n              push('/')\r\n            } else {\r\n              proxy.$Message({ type: 'error', message: red.info });\r\n            }\r\n\r\n          })\r\n        } else {\r\n          proxy.$Message({ type: 'error', message: res.info });\r\n        }\r\n      })\r\n    };\r\n\r\n\r\n    return {\r\n      tel,\r\n      pwd,\r\n      pwd2,\r\n      depositPwd,\r\n      userName,\r\n      invite_code,\r\n      onSubmit,\r\n      area_code,\r\n      qv\r\n    };\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n@import '@/styles/theme.scss';\r\n\r\n:deep .van-form {\r\n  background-color: #fff;\r\n  width: 94%;\r\n\r\n  margin: 0 auto;\r\n  color: black;\r\n  border-radius: 20px;\r\n  padding: 10px 0 0;\r\n  margin-top: 1rem;\r\n}\r\n\r\n.gore {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  color: black;\r\n  margin-top: 10px;\r\n}\r\n\r\n.gores {\r\n  color: #009995 !important;\r\n}\r\n\r\n.select {\r\n  position: absolute;\r\n  top: 48px;\r\n  left: 70px;\r\n  z-index: 100;\r\n\r\n}\r\n.ent :deep .van-field__body{\r\n\tpadding-left: 100px !important;\r\n}\r\n.selects {\r\n  border: none;\r\n  font-size: 30px;\r\n}\r\n\r\n:deep .van-button--plain {\r\n  background-color: #009995 !important;\r\n  color: #fff !important;\r\n  border-radius: 25 px !important;\r\n  border: none !important;\r\n}\r\n\r\n:deep .van-cell {\r\n  border: 1px solid #ccc;\r\n  border-radius: 12px;\r\n  margin-top: 20px;\r\n  padding: 15px 35px !important;\r\n}\r\n\r\n.show,\r\n.chose {\r\n  position: absolute;\r\n\r\n}\r\n\r\n.chose {\r\n  width: 40px;\r\n  height: 40px;\r\n  top: 0px;\r\n  left: 520px;\r\n}\r\n\r\n.show {\r\n  width: 65px;\r\n  height: 65px;\r\n  top: 90px;\r\n  left: 510px;\r\n}\r\n\r\n.homes {\r\n  height: 100vh;\r\n  overflow: auto;\r\n  background-image: url('~@/assets/images/bj.png');\r\n  background-size: 100% 100%;\r\n\r\n  :deep(.van-form) {\r\n    position: relative;\r\n    padding-bottom: 40px;\r\n    z-index: 9;\r\n    .van-cell-group--inset {\r\n      padding: 0 50px;\r\n      background-color: initial;\r\n    }\r\n\r\n    .van-ellipsis {\r\n      color: #fff;\r\n    }\r\n\r\n    .van-cell {\r\n      padding: 34px 10px;\r\n      border-bottom: 1px solid var(--van-cell-border-color);\r\n      background-color: initial;\r\n\r\n      &.zdy {\r\n        .van-field__left-icon {\r\n          margin-right: 30px;\r\n        }\r\n      }\r\n\r\n      .van-field__left-icon {\r\n        margin-right: 90px;\r\n\r\n        .van-icon__image {\r\n          height: 42px;\r\n          width: auto;\r\n        }\r\n\r\n        .icon {\r\n          height: 42px;\r\n          width: auto;\r\n          vertical-align: middle;\r\n        }\r\n\r\n        display: flex;\r\n\r\n        .van-dropdown-menu {\r\n          .van-dropdown-menu__bar {\r\n            height: auto;\r\n            background: none;\r\n            box-shadow: none;\r\n          }\r\n\r\n          .van-cell {\r\n            padding: 30px 80px;\r\n          }\r\n        }\r\n      }\r\n\r\n      .van-field__control {\r\n        font-size: 24px;\r\n        \r\n        // margin-left: 80px;\r\n      }\r\n\r\n      // .zdy .van-field__control{\r\n      //   margin-left: 30px !important;\r\n      // }\r\n\r\n      .van-field__label {\r\n        color: #fff;\r\n        display: none;\r\n      }\r\n\r\n      zdy .van-field__label {\r\n        color: #fff;\r\n        display: flex;\r\n      }\r\n\r\n      &::after {\r\n        display: none;\r\n      }\r\n    }\r\n\r\n    .van-checkbox {\r\n      margin: 30px 0 60px 0;\r\n\r\n      .van-checkbox__icon {\r\n        font-size: 50px;\r\n        margin-right: 80px;\r\n\r\n        &.van-checkbox__icon--checked .van-icon {\r\n          background-color: $theme;\r\n          border-color: $theme;\r\n        }\r\n      }\r\n\r\n      .van-checkbox__label {\r\n        font-size: 24px;\r\n      }\r\n    }\r\n\r\n    .buttons {\r\n      padding: 0 50px;\r\n      border-radius: 3px;\r\n\r\n      .van-button {\r\n        font-size: 26px;\r\n        padding: 26px 0;\r\n        height: auto;\r\n        margin-top: 40px;\r\n\r\n        &+.van-button {\r\n          background-color: rgba(255, 255, 255, 0.2);\r\n          border: none;\r\n          color: #fff;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;AA4DA,OAAOA,QAAO,MAAO,aAAY;AACjC,SAASC,GAAG,EAAEC,kBAAkB,EAAEC,KAAK,EAAEC,QAAO,QAAS,KAAK;AAC9D,OAAOC,KAAI,MAAO,eAAc;AAChC,SAASC,SAAS,EAAEC,QAAO,QAAS,YAAY;AAChD,SAASC,WAAW,EAAEC,KAAI,QAAS,mBAAkB;AACrD,SAASC,KAAI,QAAS,MAAM;AAC5B,eAAe;EACbC,IAAI,EAAE,UAAU;EAChBC,UAAU,EAAE;IAAEZ,QAAO,EAAPA;EAAS,CAAC;EACxBa,KAAK,WAAAA,MAAA,EAAG;IAAA,IAAAC,YAAA,EAAAC,aAAA,EAAAC,eAAA;IAEN,IAAMC,KAAI,GAAIV,QAAQ,CAAC;IAEvB,IAAMW,EAAC,GAAI,EAAAJ,YAAA,GAAAG,KAAK,CAACE,KAAK,cAAAL,YAAA,uBAAXA,YAAA,CAAaM,IAAG,KAAK,EAAE;IAElC,IAAAC,UAAA,GAAiBf,SAAS,CAAC,CAAC;MAApBgB,IAAG,GAAAD,UAAA,CAAHC,IAAG;IAEX,IAAAC,mBAAA,GAAkBrB,kBAAkB,CAAC;MAA7BsB,KAAI,GAAAD,mBAAA,CAAJC,KAAI;IACZ,IAAMC,QAAO,GAAIxB,GAAG,CAACI,KAAK,CAACqB,KAAK,CAACD,QAAQ;IAGzC,IAAME,WAAU,GAAI1B,GAAG,CAAC,EAAE,CAAC;IAC3B,KAAAc,aAAA,GAAIE,KAAK,CAACE,KAAK,cAAAJ,aAAA,eAAXA,aAAA,CAAaY,WAAW,EAAE;MAAA,IAAAC,aAAA;MAC5BD,WAAW,CAACE,KAAI,IAAAD,aAAA,GAAIX,KAAK,CAACE,KAAK,cAAAS,aAAA,uBAAXA,aAAA,CAAaD,WAAU;IAC7C;IACA,IAAMG,GAAE,GAAI7B,GAAG,CAAC,EAAE,CAAC;IACnB,IAAM8B,GAAE,GAAI9B,GAAG,CAAC,EAAE,CAAC;IACnB,IAAM+B,IAAG,GAAI/B,GAAG,CAAC,EAAE,CAAC;IACpB,IAAMgC,UAAS,GAAIhC,GAAG,CAAC,EAAE,CAAC;IAC1B,IAAMiC,QAAO,GAAIjC,GAAG,CAAC,EAAE,CAAC;IACxB,IAAMkC,MAAK,GAAIlC,GAAG,CAAC,EAAAe,eAAA,GAACS,QAAQ,CAACI,KAAK,cAAAb,eAAA,uBAAdA,eAAA,CAAgBoB,SAAS,KAAK,EAAE;IACpD,IAAMA,SAAQ,GAAInC,GAAG,CAACkC,MAAM,CAACN,KAAK,CAACQ,GAAG,CAAC,UAAAC,EAAC,EAAK;MAAE,OAAO;QAAEC,IAAI,EAAED,EAAE;QAAET,KAAK,EAAES;MAAG;IAAE,CAAC,CAAC;IAChF,IAAME,QAAO,GAAI,SAAXA,QAAOA,CAAKC,MAAM,EAAK;MAC3B;MACA;MACA,IAAIA,MAAM,CAACV,GAAE,IAAKU,MAAM,CAACT,IAAI,EAAE;QAC7BtB,KAAK,CAACgC,IAAI,CAAC,YAAY;QACvB,OAAO,KAAI;MACb;MACA,IAAMC,IAAG,GAAIC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAACL,MAAM,CAAC;MAC9C,OAAOE,IAAI,CAACX,IAAG;MACfW,IAAI,CAACzB,EAAC,GAAIA,EAAC;MACXV,WAAW,CAACmC,IAAI,CAAC,CAACI,IAAI,CAAC,UAAAC,GAAE,EAAK;QAC5B,IAAIA,GAAG,CAACC,IAAG,KAAM,CAAC,EAAE;UAClBzB,KAAK,CAAC0B,QAAQ,CAAC;YAAEC,IAAI,EAAE,SAAS;YAAEC,OAAO,EAAEJ,GAAG,CAACK;UAAK,CAAC,CAAC;UACtD,IAAIA,IAAG,GAAI;YACTvB,GAAG,EAAEA,GAAG,CAACD,KAAK;YACdE,GAAG,EAAEA,GAAG,CAACF,KAAK;YACdX,EAAE,EAAEoC;UACN;UACA7C,KAAK,CAAC4C,IAAI,CAAC,CAACN,IAAI,CAAC,UAAAQ,GAAE,EAAK;YACtB,IAAIA,GAAG,CAACN,IAAG,KAAM,CAAC,EAAE;cAClB5C,KAAK,CAACmD,QAAQ,CAAC,aAAa,EAAED,GAAG,CAACE,KAAK;cACvCpD,KAAK,CAACmD,QAAQ,CAAC,gBAAgB,EAAED,GAAG,CAACG,QAAO,IAAK,CAAC,CAAC;cACnDlC,KAAK,CAAC0B,QAAQ,CAAC;gBAAEC,IAAI,EAAE,SAAS;gBAAEC,OAAO,EAAEG,GAAG,CAACF;cAAK,CAAC,CAAC;cACtD;cACA,IAAMM,KAAI,GAAAC,aAAA,CAAAA,aAAA,KAASjB,IAAI,GAAK;gBAAEkB,OAAO,EAAE;cAAK,EAAE;cAC9CxD,KAAK,CAACmD,QAAQ,CAAC,YAAY,EAAEG,KAAK;cAClCrC,IAAI,CAAC,GAAG;YACV,OAAO;cACLE,KAAK,CAAC0B,QAAQ,CAAC;gBAAEC,IAAI,EAAE,OAAO;gBAAEC,OAAO,EAAEG,GAAG,CAACF;cAAK,CAAC,CAAC;YACtD;UAEF,CAAC;QACH,OAAO;UACL7B,KAAK,CAAC0B,QAAQ,CAAC;YAAEC,IAAI,EAAE,OAAO;YAAEC,OAAO,EAAEJ,GAAG,CAACK;UAAK,CAAC,CAAC;QACtD;MACF,CAAC;IACH,CAAC;IAGD,OAAO;MACLvB,GAAG,EAAHA,GAAG;MACHC,GAAG,EAAHA,GAAG;MACHC,IAAI,EAAJA,IAAI;MACJC,UAAU,EAAVA,UAAU;MACVC,QAAQ,EAARA,QAAQ;MACRP,WAAW,EAAXA,WAAW;MACXa,QAAQ,EAARA,QAAQ;MACRJ,SAAS,EAATA,SAAS;MACTlB,EAAC,EAADA;IACF,CAAC;EACH;AACF"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}