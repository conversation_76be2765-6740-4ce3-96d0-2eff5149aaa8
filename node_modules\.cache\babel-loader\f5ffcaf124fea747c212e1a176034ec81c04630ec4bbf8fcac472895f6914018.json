{"ast": null, "code": "var _errorMessages;\nfunction _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\nfunction _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread(); }\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _iterableToArray(iter) { if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter); }\nfunction _arrayWithoutHoles(arr) { if (Array.isArray(arr)) return _arrayLikeToArray(arr); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return _typeof(key) === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (_typeof(input) !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (_typeof(res) !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\n/*!\n  * message-compiler v9.3.0-beta.6\n  * (c) 2022 kazuya kawaguchi\n  * Released under the MIT License.\n  */\nimport { format, assign, isString } from '@intlify/shared';\nvar CompileErrorCodes = {\n  // tokenizer error codes\n  EXPECTED_TOKEN: 1,\n  INVALID_TOKEN_IN_PLACEHOLDER: 2,\n  UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER: 3,\n  UNKNOWN_ESCAPE_SEQUENCE: 4,\n  INVALID_UNICODE_ESCAPE_SEQUENCE: 5,\n  UNBALANCED_CLOSING_BRACE: 6,\n  UNTERMINATED_CLOSING_BRACE: 7,\n  EMPTY_PLACEHOLDER: 8,\n  NOT_ALLOW_NEST_PLACEHOLDER: 9,\n  INVALID_LINKED_FORMAT: 10,\n  // parser error codes\n  MUST_HAVE_MESSAGES_IN_PLURAL: 11,\n  UNEXPECTED_EMPTY_LINKED_MODIFIER: 12,\n  UNEXPECTED_EMPTY_LINKED_KEY: 13,\n  UNEXPECTED_LEXICAL_ANALYSIS: 14,\n  // Special value for higher-order compilers to pick up the last code\n  // to avoid collision of error codes. This should always be kept as the last\n  // item.\n  __EXTEND_POINT__: 15\n};\n/** @internal */\nvar errorMessages = (_errorMessages = {}, _defineProperty(_errorMessages, CompileErrorCodes.EXPECTED_TOKEN, \"Expected token: '{0}'\"), _defineProperty(_errorMessages, CompileErrorCodes.INVALID_TOKEN_IN_PLACEHOLDER, \"Invalid token in placeholder: '{0}'\"), _defineProperty(_errorMessages, CompileErrorCodes.UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER, \"Unterminated single quote in placeholder\"), _defineProperty(_errorMessages, CompileErrorCodes.UNKNOWN_ESCAPE_SEQUENCE, \"Unknown escape sequence: \\\\{0}\"), _defineProperty(_errorMessages, CompileErrorCodes.INVALID_UNICODE_ESCAPE_SEQUENCE, \"Invalid unicode escape sequence: {0}\"), _defineProperty(_errorMessages, CompileErrorCodes.UNBALANCED_CLOSING_BRACE, \"Unbalanced closing brace\"), _defineProperty(_errorMessages, CompileErrorCodes.UNTERMINATED_CLOSING_BRACE, \"Unterminated closing brace\"), _defineProperty(_errorMessages, CompileErrorCodes.EMPTY_PLACEHOLDER, \"Empty placeholder\"), _defineProperty(_errorMessages, CompileErrorCodes.NOT_ALLOW_NEST_PLACEHOLDER, \"Not allowed nest placeholder\"), _defineProperty(_errorMessages, CompileErrorCodes.INVALID_LINKED_FORMAT, \"Invalid linked format\"), _defineProperty(_errorMessages, CompileErrorCodes.MUST_HAVE_MESSAGES_IN_PLURAL, \"Plural must have messages\"), _defineProperty(_errorMessages, CompileErrorCodes.UNEXPECTED_EMPTY_LINKED_MODIFIER, \"Unexpected empty linked modifier\"), _defineProperty(_errorMessages, CompileErrorCodes.UNEXPECTED_EMPTY_LINKED_KEY, \"Unexpected empty linked key\"), _defineProperty(_errorMessages, CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS, \"Unexpected lexical analysis in token: '{0}'\"), _errorMessages);\nfunction createCompileError(code, loc) {\n  var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n  var domain = options.domain,\n    messages = options.messages,\n    args = options.args;\n  var msg = process.env.NODE_ENV !== 'production' ? format.apply(void 0, [(messages || errorMessages)[code] || ''].concat(_toConsumableArray(args || []))) : code;\n  var error = new SyntaxError(String(msg));\n  error.code = code;\n  if (loc) {\n    error.location = loc;\n  }\n  error.domain = domain;\n  return error;\n}\n/** @internal */\nfunction defaultOnError(error) {\n  throw error;\n}\nvar LocationStub = {\n  start: {\n    line: 1,\n    column: 1,\n    offset: 0\n  },\n  end: {\n    line: 1,\n    column: 1,\n    offset: 0\n  }\n};\nfunction createPosition(line, column, offset) {\n  return {\n    line: line,\n    column: column,\n    offset: offset\n  };\n}\nfunction createLocation(start, end, source) {\n  var loc = {\n    start: start,\n    end: end\n  };\n  if (source != null) {\n    loc.source = source;\n  }\n  return loc;\n}\nvar CHAR_SP = ' ';\nvar CHAR_CR = '\\r';\nvar CHAR_LF = '\\n';\nvar CHAR_LS = String.fromCharCode(0x2028);\nvar CHAR_PS = String.fromCharCode(0x2029);\nfunction createScanner(str) {\n  var _buf = str;\n  var _index = 0;\n  var _line = 1;\n  var _column = 1;\n  var _peekOffset = 0;\n  var isCRLF = function isCRLF(index) {\n    return _buf[index] === CHAR_CR && _buf[index + 1] === CHAR_LF;\n  };\n  var isLF = function isLF(index) {\n    return _buf[index] === CHAR_LF;\n  };\n  var isPS = function isPS(index) {\n    return _buf[index] === CHAR_PS;\n  };\n  var isLS = function isLS(index) {\n    return _buf[index] === CHAR_LS;\n  };\n  var isLineEnd = function isLineEnd(index) {\n    return isCRLF(index) || isLF(index) || isPS(index) || isLS(index);\n  };\n  var index = function index() {\n    return _index;\n  };\n  var line = function line() {\n    return _line;\n  };\n  var column = function column() {\n    return _column;\n  };\n  var peekOffset = function peekOffset() {\n    return _peekOffset;\n  };\n  var charAt = function charAt(offset) {\n    return isCRLF(offset) || isPS(offset) || isLS(offset) ? CHAR_LF : _buf[offset];\n  };\n  var currentChar = function currentChar() {\n    return charAt(_index);\n  };\n  var currentPeek = function currentPeek() {\n    return charAt(_index + _peekOffset);\n  };\n  function next() {\n    _peekOffset = 0;\n    if (isLineEnd(_index)) {\n      _line++;\n      _column = 0;\n    }\n    if (isCRLF(_index)) {\n      _index++;\n    }\n    _index++;\n    _column++;\n    return _buf[_index];\n  }\n  function peek() {\n    if (isCRLF(_index + _peekOffset)) {\n      _peekOffset++;\n    }\n    _peekOffset++;\n    return _buf[_index + _peekOffset];\n  }\n  function reset() {\n    _index = 0;\n    _line = 1;\n    _column = 1;\n    _peekOffset = 0;\n  }\n  function resetPeek() {\n    var offset = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0;\n    _peekOffset = offset;\n  }\n  function skipToPeek() {\n    var target = _index + _peekOffset;\n    // eslint-disable-next-line no-unmodified-loop-condition\n    while (target !== _index) {\n      next();\n    }\n    _peekOffset = 0;\n  }\n  return {\n    index: index,\n    line: line,\n    column: column,\n    peekOffset: peekOffset,\n    charAt: charAt,\n    currentChar: currentChar,\n    currentPeek: currentPeek,\n    next: next,\n    peek: peek,\n    reset: reset,\n    resetPeek: resetPeek,\n    skipToPeek: skipToPeek\n  };\n}\nvar EOF = undefined;\nvar LITERAL_DELIMITER = \"'\";\nvar ERROR_DOMAIN$1 = 'tokenizer';\nfunction createTokenizer(source) {\n  var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  var location = options.location !== false;\n  var _scnr = createScanner(source);\n  var currentOffset = function currentOffset() {\n    return _scnr.index();\n  };\n  var currentPosition = function currentPosition() {\n    return createPosition(_scnr.line(), _scnr.column(), _scnr.index());\n  };\n  var _initLoc = currentPosition();\n  var _initOffset = currentOffset();\n  var _context = {\n    currentType: 14 /* EOF */,\n    offset: _initOffset,\n    startLoc: _initLoc,\n    endLoc: _initLoc,\n    lastType: 14 /* EOF */,\n    lastOffset: _initOffset,\n    lastStartLoc: _initLoc,\n    lastEndLoc: _initLoc,\n    braceNest: 0,\n    inLinked: false,\n    text: ''\n  };\n  var context = function context() {\n    return _context;\n  };\n  var onError = options.onError;\n  function emitError(code, pos, offset) {\n    var ctx = context();\n    pos.column += offset;\n    pos.offset += offset;\n    if (onError) {\n      var loc = createLocation(ctx.startLoc, pos);\n      for (var _len = arguments.length, args = new Array(_len > 3 ? _len - 3 : 0), _key = 3; _key < _len; _key++) {\n        args[_key - 3] = arguments[_key];\n      }\n      var err = createCompileError(code, loc, {\n        domain: ERROR_DOMAIN$1,\n        args: args\n      });\n      onError(err);\n    }\n  }\n  function getToken(context, type, value) {\n    context.endLoc = currentPosition();\n    context.currentType = type;\n    var token = {\n      type: type\n    };\n    if (location) {\n      token.loc = createLocation(context.startLoc, context.endLoc);\n    }\n    if (value != null) {\n      token.value = value;\n    }\n    return token;\n  }\n  var getEndToken = function getEndToken(context) {\n    return getToken(context, 14 /* EOF */);\n  };\n  function eat(scnr, ch) {\n    if (scnr.currentChar() === ch) {\n      scnr.next();\n      return ch;\n    } else {\n      emitError(CompileErrorCodes.EXPECTED_TOKEN, currentPosition(), 0, ch);\n      return '';\n    }\n  }\n  function peekSpaces(scnr) {\n    var buf = '';\n    while (scnr.currentPeek() === CHAR_SP || scnr.currentPeek() === CHAR_LF) {\n      buf += scnr.currentPeek();\n      scnr.peek();\n    }\n    return buf;\n  }\n  function skipSpaces(scnr) {\n    var buf = peekSpaces(scnr);\n    scnr.skipToPeek();\n    return buf;\n  }\n  function isIdentifierStart(ch) {\n    if (ch === EOF) {\n      return false;\n    }\n    var cc = ch.charCodeAt(0);\n    return cc >= 97 && cc <= 122 ||\n    // a-z\n    cc >= 65 && cc <= 90 ||\n    // A-Z\n    cc === 95 // _\n    ;\n  }\n\n  function isNumberStart(ch) {\n    if (ch === EOF) {\n      return false;\n    }\n    var cc = ch.charCodeAt(0);\n    return cc >= 48 && cc <= 57; // 0-9\n  }\n\n  function isNamedIdentifierStart(scnr, context) {\n    var currentType = context.currentType;\n    if (currentType !== 2 /* BraceLeft */) {\n      return false;\n    }\n    peekSpaces(scnr);\n    var ret = isIdentifierStart(scnr.currentPeek());\n    scnr.resetPeek();\n    return ret;\n  }\n  function isListIdentifierStart(scnr, context) {\n    var currentType = context.currentType;\n    if (currentType !== 2 /* BraceLeft */) {\n      return false;\n    }\n    peekSpaces(scnr);\n    var ch = scnr.currentPeek() === '-' ? scnr.peek() : scnr.currentPeek();\n    var ret = isNumberStart(ch);\n    scnr.resetPeek();\n    return ret;\n  }\n  function isLiteralStart(scnr, context) {\n    var currentType = context.currentType;\n    if (currentType !== 2 /* BraceLeft */) {\n      return false;\n    }\n    peekSpaces(scnr);\n    var ret = scnr.currentPeek() === LITERAL_DELIMITER;\n    scnr.resetPeek();\n    return ret;\n  }\n  function isLinkedDotStart(scnr, context) {\n    var currentType = context.currentType;\n    if (currentType !== 8 /* LinkedAlias */) {\n      return false;\n    }\n    peekSpaces(scnr);\n    var ret = scnr.currentPeek() === \".\" /* LinkedDot */;\n    scnr.resetPeek();\n    return ret;\n  }\n  function isLinkedModifierStart(scnr, context) {\n    var currentType = context.currentType;\n    if (currentType !== 9 /* LinkedDot */) {\n      return false;\n    }\n    peekSpaces(scnr);\n    var ret = isIdentifierStart(scnr.currentPeek());\n    scnr.resetPeek();\n    return ret;\n  }\n  function isLinkedDelimiterStart(scnr, context) {\n    var currentType = context.currentType;\n    if (!(currentType === 8 /* LinkedAlias */ || currentType === 12 /* LinkedModifier */)) {\n      return false;\n    }\n    peekSpaces(scnr);\n    var ret = scnr.currentPeek() === \":\" /* LinkedDelimiter */;\n    scnr.resetPeek();\n    return ret;\n  }\n  function isLinkedReferStart(scnr, context) {\n    var currentType = context.currentType;\n    if (currentType !== 10 /* LinkedDelimiter */) {\n      return false;\n    }\n    var fn = function fn() {\n      var ch = scnr.currentPeek();\n      if (ch === \"{\" /* BraceLeft */) {\n        return isIdentifierStart(scnr.peek());\n      } else if (ch === \"@\" /* LinkedAlias */ || ch === \"%\" /* Modulo */ || ch === \"|\" /* Pipe */ || ch === \":\" /* LinkedDelimiter */ || ch === \".\" /* LinkedDot */ || ch === CHAR_SP || !ch) {\n        return false;\n      } else if (ch === CHAR_LF) {\n        scnr.peek();\n        return fn();\n      } else {\n        // other characters\n        return isIdentifierStart(ch);\n      }\n    };\n    var ret = fn();\n    scnr.resetPeek();\n    return ret;\n  }\n  function isPluralStart(scnr) {\n    peekSpaces(scnr);\n    var ret = scnr.currentPeek() === \"|\" /* Pipe */;\n    scnr.resetPeek();\n    return ret;\n  }\n  function detectModuloStart(scnr) {\n    var spaces = peekSpaces(scnr);\n    var ret = scnr.currentPeek() === \"%\" /* Modulo */ && scnr.peek() === \"{\" /* BraceLeft */;\n    scnr.resetPeek();\n    return {\n      isModulo: ret,\n      hasSpace: spaces.length > 0\n    };\n  }\n  function isTextStart(scnr) {\n    var reset = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;\n    var fn = function fn() {\n      var hasSpace = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;\n      var prev = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : '';\n      var detectModulo = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n      var ch = scnr.currentPeek();\n      if (ch === \"{\" /* BraceLeft */) {\n        return prev === \"%\" /* Modulo */ ? false : hasSpace;\n      } else if (ch === \"@\" /* LinkedAlias */ || !ch) {\n        return prev === \"%\" /* Modulo */ ? true : hasSpace;\n      } else if (ch === \"%\" /* Modulo */) {\n        scnr.peek();\n        return fn(hasSpace, \"%\" /* Modulo */, true);\n      } else if (ch === \"|\" /* Pipe */) {\n        return prev === \"%\" /* Modulo */ || detectModulo ? true : !(prev === CHAR_SP || prev === CHAR_LF);\n      } else if (ch === CHAR_SP) {\n        scnr.peek();\n        return fn(true, CHAR_SP, detectModulo);\n      } else if (ch === CHAR_LF) {\n        scnr.peek();\n        return fn(true, CHAR_LF, detectModulo);\n      } else {\n        return true;\n      }\n    };\n    var ret = fn();\n    reset && scnr.resetPeek();\n    return ret;\n  }\n  function takeChar(scnr, fn) {\n    var ch = scnr.currentChar();\n    if (ch === EOF) {\n      return EOF;\n    }\n    if (fn(ch)) {\n      scnr.next();\n      return ch;\n    }\n    return null;\n  }\n  function takeIdentifierChar(scnr) {\n    var closure = function closure(ch) {\n      var cc = ch.charCodeAt(0);\n      return cc >= 97 && cc <= 122 ||\n      // a-z\n      cc >= 65 && cc <= 90 ||\n      // A-Z\n      cc >= 48 && cc <= 57 ||\n      // 0-9\n      cc === 95 ||\n      // _\n      cc === 36 // $\n      ;\n    };\n\n    return takeChar(scnr, closure);\n  }\n  function takeDigit(scnr) {\n    var closure = function closure(ch) {\n      var cc = ch.charCodeAt(0);\n      return cc >= 48 && cc <= 57; // 0-9\n    };\n\n    return takeChar(scnr, closure);\n  }\n  function takeHexDigit(scnr) {\n    var closure = function closure(ch) {\n      var cc = ch.charCodeAt(0);\n      return cc >= 48 && cc <= 57 ||\n      // 0-9\n      cc >= 65 && cc <= 70 ||\n      // A-F\n      cc >= 97 && cc <= 102; // a-f\n    };\n\n    return takeChar(scnr, closure);\n  }\n  function getDigits(scnr) {\n    var ch = '';\n    var num = '';\n    while (ch = takeDigit(scnr)) {\n      num += ch;\n    }\n    return num;\n  }\n  function readModulo(scnr) {\n    skipSpaces(scnr);\n    var ch = scnr.currentChar();\n    if (ch !== \"%\" /* Modulo */) {\n      emitError(CompileErrorCodes.EXPECTED_TOKEN, currentPosition(), 0, ch);\n    }\n    scnr.next();\n    return \"%\" /* Modulo */;\n  }\n\n  function readText(scnr) {\n    var buf = '';\n    while (true) {\n      var ch = scnr.currentChar();\n      if (ch === \"{\" /* BraceLeft */ || ch === \"}\" /* BraceRight */ || ch === \"@\" /* LinkedAlias */ || ch === \"|\" /* Pipe */ || !ch) {\n        break;\n      } else if (ch === \"%\" /* Modulo */) {\n        if (isTextStart(scnr)) {\n          buf += ch;\n          scnr.next();\n        } else {\n          break;\n        }\n      } else if (ch === CHAR_SP || ch === CHAR_LF) {\n        if (isTextStart(scnr)) {\n          buf += ch;\n          scnr.next();\n        } else if (isPluralStart(scnr)) {\n          break;\n        } else {\n          buf += ch;\n          scnr.next();\n        }\n      } else {\n        buf += ch;\n        scnr.next();\n      }\n    }\n    return buf;\n  }\n  function readNamedIdentifier(scnr) {\n    skipSpaces(scnr);\n    var ch = '';\n    var name = '';\n    while (ch = takeIdentifierChar(scnr)) {\n      name += ch;\n    }\n    if (scnr.currentChar() === EOF) {\n      emitError(CompileErrorCodes.UNTERMINATED_CLOSING_BRACE, currentPosition(), 0);\n    }\n    return name;\n  }\n  function readListIdentifier(scnr) {\n    skipSpaces(scnr);\n    var value = '';\n    if (scnr.currentChar() === '-') {\n      scnr.next();\n      value += \"-\".concat(getDigits(scnr));\n    } else {\n      value += getDigits(scnr);\n    }\n    if (scnr.currentChar() === EOF) {\n      emitError(CompileErrorCodes.UNTERMINATED_CLOSING_BRACE, currentPosition(), 0);\n    }\n    return value;\n  }\n  function readLiteral(scnr) {\n    skipSpaces(scnr);\n    eat(scnr, \"'\");\n    var ch = '';\n    var literal = '';\n    var fn = function fn(x) {\n      return x !== LITERAL_DELIMITER && x !== CHAR_LF;\n    };\n    while (ch = takeChar(scnr, fn)) {\n      if (ch === '\\\\') {\n        literal += readEscapeSequence(scnr);\n      } else {\n        literal += ch;\n      }\n    }\n    var current = scnr.currentChar();\n    if (current === CHAR_LF || current === EOF) {\n      emitError(CompileErrorCodes.UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER, currentPosition(), 0);\n      // TODO: Is it correct really?\n      if (current === CHAR_LF) {\n        scnr.next();\n        eat(scnr, \"'\");\n      }\n      return literal;\n    }\n    eat(scnr, \"'\");\n    return literal;\n  }\n  function readEscapeSequence(scnr) {\n    var ch = scnr.currentChar();\n    switch (ch) {\n      case '\\\\':\n      case \"'\":\n        scnr.next();\n        return \"\\\\\".concat(ch);\n      case 'u':\n        return readUnicodeEscapeSequence(scnr, ch, 4);\n      case 'U':\n        return readUnicodeEscapeSequence(scnr, ch, 6);\n      default:\n        emitError(CompileErrorCodes.UNKNOWN_ESCAPE_SEQUENCE, currentPosition(), 0, ch);\n        return '';\n    }\n  }\n  function readUnicodeEscapeSequence(scnr, unicode, digits) {\n    eat(scnr, unicode);\n    var sequence = '';\n    for (var i = 0; i < digits; i++) {\n      var ch = takeHexDigit(scnr);\n      if (!ch) {\n        emitError(CompileErrorCodes.INVALID_UNICODE_ESCAPE_SEQUENCE, currentPosition(), 0, \"\\\\\".concat(unicode).concat(sequence).concat(scnr.currentChar()));\n        break;\n      }\n      sequence += ch;\n    }\n    return \"\\\\\".concat(unicode).concat(sequence);\n  }\n  function readInvalidIdentifier(scnr) {\n    skipSpaces(scnr);\n    var ch = '';\n    var identifiers = '';\n    var closure = function closure(ch) {\n      return ch !== \"{\" /* BraceLeft */ && ch !== \"}\" /* BraceRight */ && ch !== CHAR_SP && ch !== CHAR_LF;\n    };\n    while (ch = takeChar(scnr, closure)) {\n      identifiers += ch;\n    }\n    return identifiers;\n  }\n  function readLinkedModifier(scnr) {\n    var ch = '';\n    var name = '';\n    while (ch = takeIdentifierChar(scnr)) {\n      name += ch;\n    }\n    return name;\n  }\n  function readLinkedRefer(scnr) {\n    var fn = function fn() {\n      var detect = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;\n      var buf = arguments.length > 1 ? arguments[1] : undefined;\n      var ch = scnr.currentChar();\n      if (ch === \"{\" /* BraceLeft */ || ch === \"%\" /* Modulo */ || ch === \"@\" /* LinkedAlias */ || ch === \"|\" /* Pipe */ || !ch) {\n        return buf;\n      } else if (ch === CHAR_SP) {\n        return buf;\n      } else if (ch === CHAR_LF) {\n        buf += ch;\n        scnr.next();\n        return fn(detect, buf);\n      } else {\n        buf += ch;\n        scnr.next();\n        return fn(true, buf);\n      }\n    };\n    return fn(false, '');\n  }\n  function readPlural(scnr) {\n    skipSpaces(scnr);\n    var plural = eat(scnr, \"|\" /* Pipe */);\n    skipSpaces(scnr);\n    return plural;\n  }\n  // TODO: We need refactoring of token parsing ...\n  function readTokenInPlaceholder(scnr, context) {\n    var token = null;\n    var ch = scnr.currentChar();\n    switch (ch) {\n      case \"{\" /* BraceLeft */:\n        if (context.braceNest >= 1) {\n          emitError(CompileErrorCodes.NOT_ALLOW_NEST_PLACEHOLDER, currentPosition(), 0);\n        }\n        scnr.next();\n        token = getToken(context, 2 /* BraceLeft */, \"{\" /* BraceLeft */);\n        skipSpaces(scnr);\n        context.braceNest++;\n        return token;\n      case \"}\" /* BraceRight */:\n        if (context.braceNest > 0 && context.currentType === 2 /* BraceLeft */) {\n          emitError(CompileErrorCodes.EMPTY_PLACEHOLDER, currentPosition(), 0);\n        }\n        scnr.next();\n        token = getToken(context, 3 /* BraceRight */, \"}\" /* BraceRight */);\n        context.braceNest--;\n        context.braceNest > 0 && skipSpaces(scnr);\n        if (context.inLinked && context.braceNest === 0) {\n          context.inLinked = false;\n        }\n        return token;\n      case \"@\" /* LinkedAlias */:\n        if (context.braceNest > 0) {\n          emitError(CompileErrorCodes.UNTERMINATED_CLOSING_BRACE, currentPosition(), 0);\n        }\n        token = readTokenInLinked(scnr, context) || getEndToken(context);\n        context.braceNest = 0;\n        return token;\n      default:\n        var validNamedIdentifier = true;\n        var validListIdentifier = true;\n        var validLiteral = true;\n        if (isPluralStart(scnr)) {\n          if (context.braceNest > 0) {\n            emitError(CompileErrorCodes.UNTERMINATED_CLOSING_BRACE, currentPosition(), 0);\n          }\n          token = getToken(context, 1 /* Pipe */, readPlural(scnr));\n          // reset\n          context.braceNest = 0;\n          context.inLinked = false;\n          return token;\n        }\n        if (context.braceNest > 0 && (context.currentType === 5 /* Named */ || context.currentType === 6 /* List */ || context.currentType === 7 /* Literal */)) {\n          emitError(CompileErrorCodes.UNTERMINATED_CLOSING_BRACE, currentPosition(), 0);\n          context.braceNest = 0;\n          return readToken(scnr, context);\n        }\n        if (validNamedIdentifier = isNamedIdentifierStart(scnr, context)) {\n          token = getToken(context, 5 /* Named */, readNamedIdentifier(scnr));\n          skipSpaces(scnr);\n          return token;\n        }\n        if (validListIdentifier = isListIdentifierStart(scnr, context)) {\n          token = getToken(context, 6 /* List */, readListIdentifier(scnr));\n          skipSpaces(scnr);\n          return token;\n        }\n        if (validLiteral = isLiteralStart(scnr, context)) {\n          token = getToken(context, 7 /* Literal */, readLiteral(scnr));\n          skipSpaces(scnr);\n          return token;\n        }\n        if (!validNamedIdentifier && !validListIdentifier && !validLiteral) {\n          // TODO: we should be re-designed invalid cases, when we will extend message syntax near the future ...\n          token = getToken(context, 13 /* InvalidPlace */, readInvalidIdentifier(scnr));\n          emitError(CompileErrorCodes.INVALID_TOKEN_IN_PLACEHOLDER, currentPosition(), 0, token.value);\n          skipSpaces(scnr);\n          return token;\n        }\n        break;\n    }\n    return token;\n  }\n  // TODO: We need refactoring of token parsing ...\n  function readTokenInLinked(scnr, context) {\n    var currentType = context.currentType;\n    var token = null;\n    var ch = scnr.currentChar();\n    if ((currentType === 8 /* LinkedAlias */ || currentType === 9 /* LinkedDot */ || currentType === 12 /* LinkedModifier */ || currentType === 10 /* LinkedDelimiter */) && (ch === CHAR_LF || ch === CHAR_SP)) {\n      emitError(CompileErrorCodes.INVALID_LINKED_FORMAT, currentPosition(), 0);\n    }\n    switch (ch) {\n      case \"@\" /* LinkedAlias */:\n        scnr.next();\n        token = getToken(context, 8 /* LinkedAlias */, \"@\" /* LinkedAlias */);\n        context.inLinked = true;\n        return token;\n      case \".\" /* LinkedDot */:\n        skipSpaces(scnr);\n        scnr.next();\n        return getToken(context, 9 /* LinkedDot */, \".\" /* LinkedDot */);\n      case \":\" /* LinkedDelimiter */:\n        skipSpaces(scnr);\n        scnr.next();\n        return getToken(context, 10 /* LinkedDelimiter */, \":\" /* LinkedDelimiter */);\n      default:\n        if (isPluralStart(scnr)) {\n          token = getToken(context, 1 /* Pipe */, readPlural(scnr));\n          // reset\n          context.braceNest = 0;\n          context.inLinked = false;\n          return token;\n        }\n        if (isLinkedDotStart(scnr, context) || isLinkedDelimiterStart(scnr, context)) {\n          skipSpaces(scnr);\n          return readTokenInLinked(scnr, context);\n        }\n        if (isLinkedModifierStart(scnr, context)) {\n          skipSpaces(scnr);\n          return getToken(context, 12 /* LinkedModifier */, readLinkedModifier(scnr));\n        }\n        if (isLinkedReferStart(scnr, context)) {\n          skipSpaces(scnr);\n          if (ch === \"{\" /* BraceLeft */) {\n            // scan the placeholder\n            return readTokenInPlaceholder(scnr, context) || token;\n          } else {\n            return getToken(context, 11 /* LinkedKey */, readLinkedRefer(scnr));\n          }\n        }\n        if (currentType === 8 /* LinkedAlias */) {\n          emitError(CompileErrorCodes.INVALID_LINKED_FORMAT, currentPosition(), 0);\n        }\n        context.braceNest = 0;\n        context.inLinked = false;\n        return readToken(scnr, context);\n    }\n  }\n  // TODO: We need refactoring of token parsing ...\n  function readToken(scnr, context) {\n    var token = {\n      type: 14 /* EOF */\n    };\n    if (context.braceNest > 0) {\n      return readTokenInPlaceholder(scnr, context) || getEndToken(context);\n    }\n    if (context.inLinked) {\n      return readTokenInLinked(scnr, context) || getEndToken(context);\n    }\n    var ch = scnr.currentChar();\n    switch (ch) {\n      case \"{\" /* BraceLeft */:\n        return readTokenInPlaceholder(scnr, context) || getEndToken(context);\n      case \"}\" /* BraceRight */:\n        emitError(CompileErrorCodes.UNBALANCED_CLOSING_BRACE, currentPosition(), 0);\n        scnr.next();\n        return getToken(context, 3 /* BraceRight */, \"}\" /* BraceRight */);\n      case \"@\" /* LinkedAlias */:\n        return readTokenInLinked(scnr, context) || getEndToken(context);\n      default:\n        if (isPluralStart(scnr)) {\n          token = getToken(context, 1 /* Pipe */, readPlural(scnr));\n          // reset\n          context.braceNest = 0;\n          context.inLinked = false;\n          return token;\n        }\n        var _detectModuloStart = detectModuloStart(scnr),\n          isModulo = _detectModuloStart.isModulo,\n          hasSpace = _detectModuloStart.hasSpace;\n        if (isModulo) {\n          return hasSpace ? getToken(context, 0 /* Text */, readText(scnr)) : getToken(context, 4 /* Modulo */, readModulo(scnr));\n        }\n        if (isTextStart(scnr)) {\n          return getToken(context, 0 /* Text */, readText(scnr));\n        }\n        break;\n    }\n    return token;\n  }\n  function nextToken() {\n    var currentType = _context.currentType,\n      offset = _context.offset,\n      startLoc = _context.startLoc,\n      endLoc = _context.endLoc;\n    _context.lastType = currentType;\n    _context.lastOffset = offset;\n    _context.lastStartLoc = startLoc;\n    _context.lastEndLoc = endLoc;\n    _context.offset = currentOffset();\n    _context.startLoc = currentPosition();\n    if (_scnr.currentChar() === EOF) {\n      return getToken(_context, 14 /* EOF */);\n    }\n\n    return readToken(_scnr, _context);\n  }\n  return {\n    nextToken: nextToken,\n    currentOffset: currentOffset,\n    currentPosition: currentPosition,\n    context: context\n  };\n}\nvar ERROR_DOMAIN = 'parser';\n// Backslash backslash, backslash quote, uHHHH, UHHHHHH.\nvar KNOWN_ESCAPES = /(?:\\\\\\\\|\\\\'|\\\\u([0-9a-fA-F]{4})|\\\\U([0-9a-fA-F]{6}))/g;\nfunction fromEscapeSequence(match, codePoint4, codePoint6) {\n  switch (match) {\n    case \"\\\\\\\\\":\n      return \"\\\\\";\n    case \"\\\\'\":\n      return \"'\";\n    default:\n      {\n        var codePoint = parseInt(codePoint4 || codePoint6, 16);\n        if (codePoint <= 0xd7ff || codePoint >= 0xe000) {\n          return String.fromCodePoint(codePoint);\n        }\n        // invalid ...\n        // Replace them with U+FFFD REPLACEMENT CHARACTER.\n        return '�';\n      }\n  }\n}\nfunction createParser() {\n  var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  var location = options.location !== false;\n  var onError = options.onError;\n  function emitError(tokenzer, code, start, offset) {\n    var end = tokenzer.currentPosition();\n    end.offset += offset;\n    end.column += offset;\n    if (onError) {\n      var loc = createLocation(start, end);\n      for (var _len2 = arguments.length, args = new Array(_len2 > 4 ? _len2 - 4 : 0), _key2 = 4; _key2 < _len2; _key2++) {\n        args[_key2 - 4] = arguments[_key2];\n      }\n      var err = createCompileError(code, loc, {\n        domain: ERROR_DOMAIN,\n        args: args\n      });\n      onError(err);\n    }\n  }\n  function startNode(type, offset, loc) {\n    var node = {\n      type: type,\n      start: offset,\n      end: offset\n    };\n    if (location) {\n      node.loc = {\n        start: loc,\n        end: loc\n      };\n    }\n    return node;\n  }\n  function endNode(node, offset, pos, type) {\n    node.end = offset;\n    if (type) {\n      node.type = type;\n    }\n    if (location && node.loc) {\n      node.loc.end = pos;\n    }\n  }\n  function parseText(tokenizer, value) {\n    var context = tokenizer.context();\n    var node = startNode(3 /* Text */, context.offset, context.startLoc);\n    node.value = value;\n    endNode(node, tokenizer.currentOffset(), tokenizer.currentPosition());\n    return node;\n  }\n  function parseList(tokenizer, index) {\n    var context = tokenizer.context();\n    var offset = context.lastOffset,\n      loc = context.lastStartLoc; // get brace left loc\n    var node = startNode(5 /* List */, offset, loc);\n    node.index = parseInt(index, 10);\n    tokenizer.nextToken(); // skip brach right\n    endNode(node, tokenizer.currentOffset(), tokenizer.currentPosition());\n    return node;\n  }\n  function parseNamed(tokenizer, key) {\n    var context = tokenizer.context();\n    var offset = context.lastOffset,\n      loc = context.lastStartLoc; // get brace left loc\n    var node = startNode(4 /* Named */, offset, loc);\n    node.key = key;\n    tokenizer.nextToken(); // skip brach right\n    endNode(node, tokenizer.currentOffset(), tokenizer.currentPosition());\n    return node;\n  }\n  function parseLiteral(tokenizer, value) {\n    var context = tokenizer.context();\n    var offset = context.lastOffset,\n      loc = context.lastStartLoc; // get brace left loc\n    var node = startNode(9 /* Literal */, offset, loc);\n    node.value = value.replace(KNOWN_ESCAPES, fromEscapeSequence);\n    tokenizer.nextToken(); // skip brach right\n    endNode(node, tokenizer.currentOffset(), tokenizer.currentPosition());\n    return node;\n  }\n  function parseLinkedModifier(tokenizer) {\n    var token = tokenizer.nextToken();\n    var context = tokenizer.context();\n    var offset = context.lastOffset,\n      loc = context.lastStartLoc; // get linked dot loc\n    var node = startNode(8 /* LinkedModifier */, offset, loc);\n    if (token.type !== 12 /* LinkedModifier */) {\n      // empty modifier\n      emitError(tokenizer, CompileErrorCodes.UNEXPECTED_EMPTY_LINKED_MODIFIER, context.lastStartLoc, 0);\n      node.value = '';\n      endNode(node, offset, loc);\n      return {\n        nextConsumeToken: token,\n        node: node\n      };\n    }\n    // check token\n    if (token.value == null) {\n      emitError(tokenizer, CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS, context.lastStartLoc, 0, getTokenCaption(token));\n    }\n    node.value = token.value || '';\n    endNode(node, tokenizer.currentOffset(), tokenizer.currentPosition());\n    return {\n      node: node\n    };\n  }\n  function parseLinkedKey(tokenizer, value) {\n    var context = tokenizer.context();\n    var node = startNode(7 /* LinkedKey */, context.offset, context.startLoc);\n    node.value = value;\n    endNode(node, tokenizer.currentOffset(), tokenizer.currentPosition());\n    return node;\n  }\n  function parseLinked(tokenizer) {\n    var context = tokenizer.context();\n    var linkedNode = startNode(6 /* Linked */, context.offset, context.startLoc);\n    var token = tokenizer.nextToken();\n    if (token.type === 9 /* LinkedDot */) {\n      var parsed = parseLinkedModifier(tokenizer);\n      linkedNode.modifier = parsed.node;\n      token = parsed.nextConsumeToken || tokenizer.nextToken();\n    }\n    // asset check token\n    if (token.type !== 10 /* LinkedDelimiter */) {\n      emitError(tokenizer, CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS, context.lastStartLoc, 0, getTokenCaption(token));\n    }\n    token = tokenizer.nextToken();\n    // skip brace left\n    if (token.type === 2 /* BraceLeft */) {\n      token = tokenizer.nextToken();\n    }\n    switch (token.type) {\n      case 11 /* LinkedKey */:\n        if (token.value == null) {\n          emitError(tokenizer, CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS, context.lastStartLoc, 0, getTokenCaption(token));\n        }\n        linkedNode.key = parseLinkedKey(tokenizer, token.value || '');\n        break;\n      case 5 /* Named */:\n        if (token.value == null) {\n          emitError(tokenizer, CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS, context.lastStartLoc, 0, getTokenCaption(token));\n        }\n        linkedNode.key = parseNamed(tokenizer, token.value || '');\n        break;\n      case 6 /* List */:\n        if (token.value == null) {\n          emitError(tokenizer, CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS, context.lastStartLoc, 0, getTokenCaption(token));\n        }\n        linkedNode.key = parseList(tokenizer, token.value || '');\n        break;\n      case 7 /* Literal */:\n        if (token.value == null) {\n          emitError(tokenizer, CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS, context.lastStartLoc, 0, getTokenCaption(token));\n        }\n        linkedNode.key = parseLiteral(tokenizer, token.value || '');\n        break;\n      default:\n        // empty key\n        emitError(tokenizer, CompileErrorCodes.UNEXPECTED_EMPTY_LINKED_KEY, context.lastStartLoc, 0);\n        var nextContext = tokenizer.context();\n        var emptyLinkedKeyNode = startNode(7 /* LinkedKey */, nextContext.offset, nextContext.startLoc);\n        emptyLinkedKeyNode.value = '';\n        endNode(emptyLinkedKeyNode, nextContext.offset, nextContext.startLoc);\n        linkedNode.key = emptyLinkedKeyNode;\n        endNode(linkedNode, nextContext.offset, nextContext.startLoc);\n        return {\n          nextConsumeToken: token,\n          node: linkedNode\n        };\n    }\n    endNode(linkedNode, tokenizer.currentOffset(), tokenizer.currentPosition());\n    return {\n      node: linkedNode\n    };\n  }\n  function parseMessage(tokenizer) {\n    var context = tokenizer.context();\n    var startOffset = context.currentType === 1 /* Pipe */ ? tokenizer.currentOffset() : context.offset;\n    var startLoc = context.currentType === 1 /* Pipe */ ? context.endLoc : context.startLoc;\n    var node = startNode(2 /* Message */, startOffset, startLoc);\n    node.items = [];\n    var nextToken = null;\n    do {\n      var token = nextToken || tokenizer.nextToken();\n      nextToken = null;\n      switch (token.type) {\n        case 0 /* Text */:\n          if (token.value == null) {\n            emitError(tokenizer, CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS, context.lastStartLoc, 0, getTokenCaption(token));\n          }\n          node.items.push(parseText(tokenizer, token.value || ''));\n          break;\n        case 6 /* List */:\n          if (token.value == null) {\n            emitError(tokenizer, CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS, context.lastStartLoc, 0, getTokenCaption(token));\n          }\n          node.items.push(parseList(tokenizer, token.value || ''));\n          break;\n        case 5 /* Named */:\n          if (token.value == null) {\n            emitError(tokenizer, CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS, context.lastStartLoc, 0, getTokenCaption(token));\n          }\n          node.items.push(parseNamed(tokenizer, token.value || ''));\n          break;\n        case 7 /* Literal */:\n          if (token.value == null) {\n            emitError(tokenizer, CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS, context.lastStartLoc, 0, getTokenCaption(token));\n          }\n          node.items.push(parseLiteral(tokenizer, token.value || ''));\n          break;\n        case 8 /* LinkedAlias */:\n          var parsed = parseLinked(tokenizer);\n          node.items.push(parsed.node);\n          nextToken = parsed.nextConsumeToken || null;\n          break;\n      }\n    } while (context.currentType !== 14 /* EOF */ && context.currentType !== 1 /* Pipe */);\n    // adjust message node loc\n    var endOffset = context.currentType === 1 /* Pipe */ ? context.lastOffset : tokenizer.currentOffset();\n    var endLoc = context.currentType === 1 /* Pipe */ ? context.lastEndLoc : tokenizer.currentPosition();\n    endNode(node, endOffset, endLoc);\n    return node;\n  }\n  function parsePlural(tokenizer, offset, loc, msgNode) {\n    var context = tokenizer.context();\n    var hasEmptyMessage = msgNode.items.length === 0;\n    var node = startNode(1 /* Plural */, offset, loc);\n    node.cases = [];\n    node.cases.push(msgNode);\n    do {\n      var msg = parseMessage(tokenizer);\n      if (!hasEmptyMessage) {\n        hasEmptyMessage = msg.items.length === 0;\n      }\n      node.cases.push(msg);\n    } while (context.currentType !== 14 /* EOF */);\n    if (hasEmptyMessage) {\n      emitError(tokenizer, CompileErrorCodes.MUST_HAVE_MESSAGES_IN_PLURAL, loc, 0);\n    }\n    endNode(node, tokenizer.currentOffset(), tokenizer.currentPosition());\n    return node;\n  }\n  function parseResource(tokenizer) {\n    var context = tokenizer.context();\n    var offset = context.offset,\n      startLoc = context.startLoc;\n    var msgNode = parseMessage(tokenizer);\n    if (context.currentType === 14 /* EOF */) {\n      return msgNode;\n    } else {\n      return parsePlural(tokenizer, offset, startLoc, msgNode);\n    }\n  }\n  function parse(source) {\n    var tokenizer = createTokenizer(source, assign({}, options));\n    var context = tokenizer.context();\n    var node = startNode(0 /* Resource */, context.offset, context.startLoc);\n    if (location && node.loc) {\n      node.loc.source = source;\n    }\n    node.body = parseResource(tokenizer);\n    // assert whether achieved to EOF\n    if (context.currentType !== 14 /* EOF */) {\n      emitError(tokenizer, CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS, context.lastStartLoc, 0, source[context.offset] || '');\n    }\n    endNode(node, tokenizer.currentOffset(), tokenizer.currentPosition());\n    return node;\n  }\n  return {\n    parse: parse\n  };\n}\nfunction getTokenCaption(token) {\n  if (token.type === 14 /* EOF */) {\n    return 'EOF';\n  }\n  var name = (token.value || '').replace(/\\r?\\n/g, '\\\\n');\n  return name.length > 10 ? name.slice(0, 9) + '…' : name;\n}\nfunction createTransformer(ast) {\n  var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  var _context = {\n    ast: ast,\n    helpers: new Set()\n  };\n  var context = function context() {\n    return _context;\n  };\n  var helper = function helper(name) {\n    _context.helpers.add(name);\n    return name;\n  };\n  return {\n    context: context,\n    helper: helper\n  };\n}\nfunction traverseNodes(nodes, transformer) {\n  for (var i = 0; i < nodes.length; i++) {\n    traverseNode(nodes[i], transformer);\n  }\n}\nfunction traverseNode(node, transformer) {\n  // TODO: if we need pre-hook of transform, should be implemented to here\n  switch (node.type) {\n    case 1 /* Plural */:\n      traverseNodes(node.cases, transformer);\n      transformer.helper(\"plural\" /* PLURAL */);\n      break;\n    case 2 /* Message */:\n      traverseNodes(node.items, transformer);\n      break;\n    case 6 /* Linked */:\n      var linked = node;\n      traverseNode(linked.key, transformer);\n      transformer.helper(\"linked\" /* LINKED */);\n      transformer.helper(\"type\" /* TYPE */);\n      break;\n    case 5 /* List */:\n      transformer.helper(\"interpolate\" /* INTERPOLATE */);\n      transformer.helper(\"list\" /* LIST */);\n      break;\n    case 4 /* Named */:\n      transformer.helper(\"interpolate\" /* INTERPOLATE */);\n      transformer.helper(\"named\" /* NAMED */);\n      break;\n  }\n  // TODO: if we need post-hook of transform, should be implemented to here\n}\n// transform AST\nfunction transform(ast) {\n  var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  var transformer = createTransformer(ast);\n  transformer.helper(\"normalize\" /* NORMALIZE */);\n  // traverse\n  ast.body && traverseNode(ast.body, transformer);\n  // set meta information\n  var context = transformer.context();\n  ast.helpers = Array.from(context.helpers);\n}\nfunction createCodeGenerator(ast, options) {\n  var sourceMap = options.sourceMap,\n    filename = options.filename,\n    breakLineCode = options.breakLineCode,\n    _needIndent = options.needIndent;\n  var _context = {\n    source: ast.loc.source,\n    filename: filename,\n    code: '',\n    column: 1,\n    line: 1,\n    offset: 0,\n    map: undefined,\n    breakLineCode: breakLineCode,\n    needIndent: _needIndent,\n    indentLevel: 0\n  };\n  var context = function context() {\n    return _context;\n  };\n  function push(code, node) {\n    _context.code += code;\n  }\n  function _newline(n) {\n    var withBreakLine = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;\n    var _breakLineCode = withBreakLine ? breakLineCode : '';\n    push(_needIndent ? _breakLineCode + \"  \".repeat(n) : _breakLineCode);\n  }\n  function indent() {\n    var withNewLine = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : true;\n    var level = ++_context.indentLevel;\n    withNewLine && _newline(level);\n  }\n  function deindent() {\n    var withNewLine = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : true;\n    var level = --_context.indentLevel;\n    withNewLine && _newline(level);\n  }\n  function newline() {\n    _newline(_context.indentLevel);\n  }\n  var helper = function helper(key) {\n    return \"_\".concat(key);\n  };\n  var needIndent = function needIndent() {\n    return _context.needIndent;\n  };\n  return {\n    context: context,\n    push: push,\n    indent: indent,\n    deindent: deindent,\n    newline: newline,\n    helper: helper,\n    needIndent: needIndent\n  };\n}\nfunction generateLinkedNode(generator, node) {\n  var helper = generator.helper;\n  generator.push(\"\".concat(helper(\"linked\" /* LINKED */), \"(\"));\n  generateNode(generator, node.key);\n  if (node.modifier) {\n    generator.push(\", \");\n    generateNode(generator, node.modifier);\n    generator.push(\", _type\");\n  } else {\n    generator.push(\", undefined, _type\");\n  }\n  generator.push(\")\");\n}\nfunction generateMessageNode(generator, node) {\n  var helper = generator.helper,\n    needIndent = generator.needIndent;\n  generator.push(\"\".concat(helper(\"normalize\" /* NORMALIZE */), \"([\"));\n  generator.indent(needIndent());\n  var length = node.items.length;\n  for (var i = 0; i < length; i++) {\n    generateNode(generator, node.items[i]);\n    if (i === length - 1) {\n      break;\n    }\n    generator.push(', ');\n  }\n  generator.deindent(needIndent());\n  generator.push('])');\n}\nfunction generatePluralNode(generator, node) {\n  var helper = generator.helper,\n    needIndent = generator.needIndent;\n  if (node.cases.length > 1) {\n    generator.push(\"\".concat(helper(\"plural\" /* PLURAL */), \"([\"));\n    generator.indent(needIndent());\n    var length = node.cases.length;\n    for (var i = 0; i < length; i++) {\n      generateNode(generator, node.cases[i]);\n      if (i === length - 1) {\n        break;\n      }\n      generator.push(', ');\n    }\n    generator.deindent(needIndent());\n    generator.push(\"])\");\n  }\n}\nfunction generateResource(generator, node) {\n  if (node.body) {\n    generateNode(generator, node.body);\n  } else {\n    generator.push('null');\n  }\n}\nfunction generateNode(generator, node) {\n  var helper = generator.helper;\n  switch (node.type) {\n    case 0 /* Resource */:\n      generateResource(generator, node);\n      break;\n    case 1 /* Plural */:\n      generatePluralNode(generator, node);\n      break;\n    case 2 /* Message */:\n      generateMessageNode(generator, node);\n      break;\n    case 6 /* Linked */:\n      generateLinkedNode(generator, node);\n      break;\n    case 8 /* LinkedModifier */:\n      generator.push(JSON.stringify(node.value), node);\n      break;\n    case 7 /* LinkedKey */:\n      generator.push(JSON.stringify(node.value), node);\n      break;\n    case 5 /* List */:\n      generator.push(\"\".concat(helper(\"interpolate\" /* INTERPOLATE */), \"(\").concat(helper(\"list\" /* LIST */), \"(\").concat(node.index, \"))\"), node);\n      break;\n    case 4 /* Named */:\n      generator.push(\"\".concat(helper(\"interpolate\" /* INTERPOLATE */), \"(\").concat(helper(\"named\" /* NAMED */), \"(\").concat(JSON.stringify(node.key), \"))\"), node);\n      break;\n    case 9 /* Literal */:\n      generator.push(JSON.stringify(node.value), node);\n      break;\n    case 3 /* Text */:\n      generator.push(JSON.stringify(node.value), node);\n      break;\n    default:\n      if (process.env.NODE_ENV !== 'production') {\n        throw new Error(\"unhandled codegen node type: \".concat(node.type));\n      }\n  }\n}\n// generate code from AST\nvar generate = function generate(ast) {\n  var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  var mode = isString(options.mode) ? options.mode : 'normal';\n  var filename = isString(options.filename) ? options.filename : 'message.intl';\n  var sourceMap = !!options.sourceMap;\n  // prettier-ignore\n  var breakLineCode = options.breakLineCode != null ? options.breakLineCode : mode === 'arrow' ? ';' : '\\n';\n  var needIndent = options.needIndent ? options.needIndent : mode !== 'arrow';\n  var helpers = ast.helpers || [];\n  var generator = createCodeGenerator(ast, {\n    mode: mode,\n    filename: filename,\n    sourceMap: sourceMap,\n    breakLineCode: breakLineCode,\n    needIndent: needIndent\n  });\n  generator.push(mode === 'normal' ? \"function __msg__ (ctx) {\" : \"(ctx) => {\");\n  generator.indent(needIndent);\n  if (helpers.length > 0) {\n    generator.push(\"const { \".concat(helpers.map(function (s) {\n      return \"\".concat(s, \": _\").concat(s);\n    }).join(', '), \" } = ctx\"));\n    generator.newline();\n  }\n  generator.push(\"return \");\n  generateNode(generator, ast);\n  generator.deindent(needIndent);\n  generator.push(\"}\");\n  var _generator$context = generator.context(),\n    code = _generator$context.code,\n    map = _generator$context.map;\n  return {\n    ast: ast,\n    code: code,\n    map: map ? map.toJSON() : undefined // eslint-disable-line @typescript-eslint/no-explicit-any\n  };\n};\n\nfunction baseCompile(source) {\n  var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  var assignedOptions = assign({}, options);\n  // parse source codes\n  var parser = createParser(assignedOptions);\n  var ast = parser.parse(source);\n  // transform ASTs\n  transform(ast, assignedOptions);\n  // generate javascript codes\n  return generate(ast, assignedOptions);\n}\nexport { CompileErrorCodes, ERROR_DOMAIN, LocationStub, baseCompile, createCompileError, createLocation, createParser, createPosition, defaultOnError, errorMessages };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}