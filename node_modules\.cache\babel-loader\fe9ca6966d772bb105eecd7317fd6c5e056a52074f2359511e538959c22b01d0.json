{"ast": null, "code": "import { createApp } from 'vue';\nimport App from './App.vue';\nimport router from './router';\nimport store from './store';\nimport vant from 'vant';\nimport vue3SeamlessScroll from \"vue3-seamless-scroll\";\nimport Message from '@/components/message.js';\n// import { Locale } from 'vant'\n// // 引入英文语言包\n// import enUS from 'vant/es/locale/lang/en-US';\n// Locale.use('en-US', enUS);\n// 2. 引入组件样式\nimport 'vant/lib/index.css';\nimport { i18n, vantLocales } from '@/i18n/i18n';\nvar app = createApp(App);\napp.config.globalProperties.$Message = Message;\nvantLocales(i18n.locale);\napp.use(vant).use(vue3SeamlessScroll).use(i18n).use(store).use(router).mount('#app');", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}