{"ast": null, "code": "import { createVNode as _createVNode, mergeProps as _mergeProps } from \"vue\";\nimport { extend, isObject, inBrowser, withInstall } from \"../utils/index.mjs\";\nimport { mountComponent, usePopupState } from \"../utils/mount-component.mjs\";\nimport VanNotify from \"./Notify.mjs\";\nvar timer;\nvar instance;\nvar parseOptions = function parseOptions(message) {\n  return isObject(message) ? message : {\n    message: message\n  };\n};\nfunction initInstance() {\n  var _mountComponent = mountComponent({\n    setup: function setup() {\n      var _usePopupState = usePopupState(),\n        state = _usePopupState.state,\n        toggle = _usePopupState.toggle;\n      return function () {\n        return _createVNode(VanNotify, _mergeProps(state, {\n          \"onUpdate:show\": toggle\n        }), null);\n      };\n    }\n  });\n  instance = _mountComponent.instance;\n}\nfunction Notify(options) {\n  if (!inBrowser) {\n    return;\n  }\n  if (!instance) {\n    initInstance();\n  }\n  options = extend({}, Notify.currentOptions, parseOptions(options));\n  instance.open(options);\n  clearTimeout(timer);\n  if (options.duration > 0) {\n    timer = window.setTimeout(Notify.clear, options.duration);\n  }\n  return instance;\n}\nvar getDefaultOptions = function getDefaultOptions() {\n  return {\n    type: \"danger\",\n    color: void 0,\n    message: \"\",\n    onClose: void 0,\n    onClick: void 0,\n    onOpened: void 0,\n    duration: 3e3,\n    position: void 0,\n    className: \"\",\n    lockScroll: false,\n    background: void 0\n  };\n};\nNotify.clear = function () {\n  if (instance) {\n    instance.toggle(false);\n  }\n};\nNotify.currentOptions = getDefaultOptions();\nNotify.setDefaultOptions = function (options) {\n  extend(Notify.currentOptions, options);\n};\nNotify.resetDefaultOptions = function () {\n  Notify.currentOptions = getDefaultOptions();\n};\nNotify.Component = withInstall(VanNotify);\nNotify.install = function (app) {\n  app.use(Notify.Component);\n  app.config.globalProperties.$notify = Notify;\n};\nexport { Notify };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}