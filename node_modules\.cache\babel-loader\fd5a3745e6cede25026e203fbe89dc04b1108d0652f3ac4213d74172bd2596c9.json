{"ast": null, "code": "function _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return _typeof(key) === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (_typeof(input) !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (_typeof(res) !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\nfunction _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nfunction _iterableToArrayLimit(arr, i) { var _i = null == arr ? null : \"undefined\" != typeof Symbol && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (null != _i) { var _s, _e, _x, _r, _arr = [], _n = !0, _d = !1; try { if (_x = (_i = _i.call(arr)).next, 0 === i) { if (Object(_i) !== _i) return; _n = !1; } else for (; !(_n = (_s = _x.call(_i)).done) && (_arr.push(_s.value), _arr.length !== i); _n = !0); } catch (err) { _d = !0, _e = err; } finally { try { if (!_n && null != _i.return && (_r = _i.return(), Object(_r) !== _r)) return; } finally { if (_d) throw _e; } } return _arr; } }\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\nimport { createVNode as _createVNode } from \"vue\";\nimport { defineComponent } from \"vue\";\nimport { truthProp, createNamespace, BORDER_TOP_BOTTOM } from \"../utils/index.mjs\";\nimport { useChildren } from \"@vant/use\";\nimport { useExpose } from \"../composables/use-expose.mjs\";\nvar _createNamespace = createNamespace(\"collapse\"),\n  _createNamespace2 = _slicedToArray(_createNamespace, 2),\n  name = _createNamespace2[0],\n  bem = _createNamespace2[1];\nvar COLLAPSE_KEY = Symbol(name);\nvar collapseProps = {\n  border: truthProp,\n  accordion: Boolean,\n  modelValue: {\n    type: [String, Number, Array],\n    default: \"\"\n  }\n};\nfunction validateModelValue(modelValue, accordion) {\n  if (accordion && Array.isArray(modelValue)) {\n    return false;\n  }\n  if (!accordion && !Array.isArray(modelValue)) {\n    return false;\n  }\n  return true;\n}\nvar stdin_default = defineComponent({\n  name: name,\n  props: collapseProps,\n  emits: [\"change\", \"update:modelValue\"],\n  setup: function setup(props, _ref) {\n    var emit = _ref.emit,\n      slots = _ref.slots;\n    var _useChildren = useChildren(COLLAPSE_KEY),\n      linkChildren = _useChildren.linkChildren,\n      children = _useChildren.children;\n    var updateName = function updateName(name2) {\n      emit(\"change\", name2);\n      emit(\"update:modelValue\", name2);\n    };\n    var toggle = function toggle(name2, expanded) {\n      var accordion = props.accordion,\n        modelValue = props.modelValue;\n      if (accordion) {\n        updateName(name2 === modelValue ? \"\" : name2);\n      } else if (expanded) {\n        updateName(modelValue.concat(name2));\n      } else {\n        updateName(modelValue.filter(function (activeName) {\n          return activeName !== name2;\n        }));\n      }\n    };\n    var toggleAll = function toggleAll() {\n      var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n      if (props.accordion) {\n        return;\n      }\n      if (typeof options === \"boolean\") {\n        options = {\n          expanded: options\n        };\n      }\n      var _options = options,\n        expanded = _options.expanded,\n        skipDisabled = _options.skipDisabled;\n      var expandedChildren = children.filter(function (item) {\n        if (item.disabled && skipDisabled) {\n          return item.expanded.value;\n        }\n        return expanded != null ? expanded : !item.expanded.value;\n      });\n      var names = expandedChildren.map(function (item) {\n        return item.itemName.value;\n      });\n      updateName(names);\n    };\n    var isExpanded = function isExpanded(name2) {\n      var accordion = props.accordion,\n        modelValue = props.modelValue;\n      if (process.env.NODE_ENV !== \"production\" && !validateModelValue(modelValue, accordion)) {\n        return false;\n      }\n      return accordion ? modelValue === name2 : modelValue.includes(name2);\n    };\n    useExpose({\n      toggleAll: toggleAll\n    });\n    linkChildren({\n      toggle: toggle,\n      isExpanded: isExpanded\n    });\n    return function () {\n      var _a;\n      return _createVNode(\"div\", {\n        \"class\": [bem(), _defineProperty({}, BORDER_TOP_BOTTOM, props.border)]\n      }, [(_a = slots.default) == null ? void 0 : _a.call(slots)]);\n    };\n  }\n});\nexport { COLLAPSE_KEY, stdin_default as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}