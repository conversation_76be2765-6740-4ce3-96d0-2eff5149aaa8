{"name": "@vue/devtools-api", "version": "6.5.0", "description": "Interact with the Vue devtools from the page", "main": "lib/cjs/index.js", "browser": "lib/esm/index.js", "module": "lib/esm/index.js", "types": "lib/esm/index.d.ts", "sideEffects": false, "author": {"name": "<PERSON>"}, "files": ["lib/esm", "lib/cjs"], "license": "MIT", "repository": {"url": "https://github.com/vuejs/vue-devtools.git", "type": "git", "directory": "packages/api"}, "publishConfig": {"access": "public"}, "scripts": {"build": "rimraf lib && yarn build:esm && yarn build:cjs", "build:esm": "tsc --module es2015 --outDir lib/esm -d", "build:cjs": "tsc --module commonjs --outDir lib/cjs", "build:watch": "yarn tsc --module es2015 --outDir lib/esm -d -w --sourceMap"}, "devDependencies": {"@types/node": "^13.9.1", "@types/webpack-env": "^1.15.1", "typescript": "^4.5.2"}}