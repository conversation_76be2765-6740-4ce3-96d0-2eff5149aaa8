{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, toDisplayString as _toDisplayString, createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, createTextVNode as _createTextVNode, withCtx as _withCtx, pushScopeId as _pushScopeId, popScopeId as _popScopeId } from \"vue\";\nvar _withScopeId = function _withScopeId(n) {\n  return _pushScopeId(\"data-v-76fda38a\"), n = n(), _popScopeId(), n;\n};\nvar _hoisted_1 = {\n  class: \"home\"\n};\nvar _hoisted_2 = {\n  key: 0,\n  class: \"box_bank\"\n};\nvar _hoisted_3 = {\n  key: 0,\n  class: \"li\"\n};\nvar _hoisted_4 = {\n  class: \"span\"\n};\nvar _hoisted_5 = {\n  class: \"span\"\n};\nvar _hoisted_6 = {\n  key: 1,\n  class: \"li\"\n};\nvar _hoisted_7 = {\n  class: \"span\"\n};\nvar _hoisted_8 = {\n  class: \"span\"\n};\nvar _hoisted_9 = {\n  key: 2,\n  class: \"li\"\n};\nvar _hoisted_10 = {\n  class: \"span\"\n};\nvar _hoisted_11 = {\n  class: \"span\"\n};\nvar _hoisted_12 = {\n  key: 3,\n  class: \"li\"\n};\nvar _hoisted_13 = {\n  class: \"span\"\n};\nvar _hoisted_14 = {\n  class: \"span\"\n};\nvar _hoisted_15 = {\n  key: 4,\n  class: \"li\"\n};\nvar _hoisted_16 = {\n  class: \"span\"\n};\nvar _hoisted_17 = {\n  class: \"span\"\n};\nvar _hoisted_18 = {\n  key: 5,\n  class: \"li\"\n};\nvar _hoisted_19 = {\n  class: \"span\"\n};\nvar _hoisted_20 = {\n  class: \"span\"\n};\nvar _hoisted_21 = {\n  key: 1,\n  class: \"not_box_bank\"\n};\nvar _hoisted_22 = {\n  class: \"khlx\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_van_nav_bar = _resolveComponent(\"van-nav-bar\");\n  var _component_van_button = _resolveComponent(\"van-button\");\n  var _component_van_empty = _resolveComponent(\"van-empty\");\n  var _component_van_picker = _resolveComponent(\"van-picker\");\n  var _component_van_popup = _resolveComponent(\"van-popup\");\n  var _component_van_cell = _resolveComponent(\"van-cell\");\n  var _component_van_field = _resolveComponent(\"van-field\");\n  var _component_van_cell_group = _resolveComponent(\"van-cell-group\");\n  var _component_van_form = _resolveComponent(\"van-form\");\n  var _component_van_dialog = _resolveComponent(\"van-dialog\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_van_nav_bar, {\n    title: _ctx.$t('msg.tkxx'),\n    \"left-arrow\": \"\",\n    onClickLeft: _cache[0] || (_cache[0] = function ($event) {\n      return _ctx.$router.go(-1);\n    })\n  }, null, 8, [\"title\"]), $setup.info && Object.keys($setup.info).length > 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_2, [$setup.py_status !== 2 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_3, [_createElementVNode(\"span\", _hoisted_4, _toDisplayString(_ctx.$t(\"msg.khlx\")) + \"：\", 1), _createElementVNode(\"span\", _hoisted_5, _toDisplayString($setup.bank_type), 1)])) : _createCommentVNode(\"\", true), $setup.py_status !== 2 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_6, [_createElementVNode(\"span\", _hoisted_7, _toDisplayString(_ctx.$t(\"msg.khxm\")) + \"：\", 1), _createElementVNode(\"span\", _hoisted_8, _toDisplayString($setup.username), 1)])) : _createCommentVNode(\"\", true), $setup.py_status !== 2 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_9, [_createElementVNode(\"span\", _hoisted_10, _toDisplayString(_ctx.$t(\"msg.yhkh\")) + \"：\", 1), _createElementVNode(\"span\", _hoisted_11, _toDisplayString($setup.id_number), 1)])) : _createCommentVNode(\"\", true), $setup.py_status !== 2 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_12, [_createElementVNode(\"span\", _hoisted_13, _toDisplayString(_ctx.$t(\"msg.ylsjh\")) + \"：\", 1), _createElementVNode(\"span\", _hoisted_14, _toDisplayString($setup.tel), 1)])) : _createCommentVNode(\"\", true), $setup.py_status == 2 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_15, [_createElementVNode(\"span\", _hoisted_16, _toDisplayString(_ctx.$t(\"msg.usdt_type\")) + \"：\", 1), _createElementVNode(\"span\", _hoisted_17, _toDisplayString($setup.usdt_type), 1)])) : _createCommentVNode(\"\", true), $setup.py_status == 2 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_18, [_createElementVNode(\"span\", _hoisted_19, _toDisplayString(_ctx.$t(\"msg.usdt_address\")) + \"：\", 1), _createElementVNode(\"span\", _hoisted_20, _toDisplayString($setup.usdt_diz), 1)])) : _createCommentVNode(\"\", true), _createVNode(_component_van_button, {\n    round: \"\",\n    block: \"\",\n    type: \"primary\",\n    onClick: _cache[1] || (_cache[1] = function ($event) {\n      return $setup.showDialog();\n    })\n  }, {\n    default: _withCtx(function () {\n      return [_createTextVNode(_toDisplayString(_ctx.$t(\"msg.edit\")), 1)];\n    }),\n    _: 1\n  })])) : (_openBlock(), _createElementBlock(\"div\", _hoisted_21, [_createVNode(_component_van_empty, {\n    description: _ctx.$t('msg.not_data')\n  }, null, 8, [\"description\"]), _createVNode(_component_van_button, {\n    round: \"\",\n    block: \"\",\n    type: \"primary\",\n    class: \"not\",\n    onClick: _cache[2] || (_cache[2] = function ($event) {\n      return $setup.showDialog();\n    }),\n    style: {\n      \"background\": \"#000\",\n      \"color\": \"#fff\",\n      \"border\": \"none\"\n    }\n  }, {\n    default: _withCtx(function () {\n      return [_createTextVNode(_toDisplayString(_ctx.$t(\"msg.add\")), 1)];\n    }),\n    _: 1\n  })])), _createVNode(_component_van_popup, {\n    show: $setup.showHank,\n    \"onUpdate:show\": _cache[4] || (_cache[4] = function ($event) {\n      return $setup.showHank = $event;\n    }),\n    position: \"bottom\"\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_van_picker, {\n        columns: $setup.bank_list,\n        onConfirm: $setup.onConfirm,\n        onCancel: _cache[3] || (_cache[3] = function ($event) {\n          return $setup.showHank = false;\n        }),\n        \"confirm-button-text\": _ctx.$t('msg.yes'),\n        \"cancel-button-text\": _ctx.$t('msg.quxiao')\n      }, null, 8, [\"columns\", \"onConfirm\", \"confirm-button-text\", \"cancel-button-text\"])];\n    }),\n    _: 1\n  }, 8, [\"show\"]), _createVNode(_component_van_popup, {\n    show: $setup.showType,\n    \"onUpdate:show\": _cache[6] || (_cache[6] = function ($event) {\n      return $setup.showType = $event;\n    }),\n    position: \"bottom\"\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_van_picker, {\n        columns: $setup.tondao_type,\n        onConfirm: $setup.onConfirm1,\n        onCancel: _cache[5] || (_cache[5] = function ($event) {\n          return $setup.showType = false;\n        }),\n        \"confirm-button-text\": _ctx.$t('msg.yes'),\n        \"cancel-button-text\": _ctx.$t('msg.quxiao')\n      }, null, 8, [\"columns\", \"onConfirm\", \"confirm-button-text\", \"cancel-button-text\"])];\n    }),\n    _: 1\n  }, 8, [\"show\"]), _createVNode(_component_van_dialog, {\n    show: $setup.showUsdt,\n    \"onUpdate:show\": _cache[10] || (_cache[10] = function ($event) {\n      return $setup.showUsdt = $event;\n    }),\n    title: _ctx.$t('msg.tkxx'),\n    onConfirm: $setup.confirmPwd,\n    confirmButtonText: _ctx.$t('msg.queren'),\n    closeOnClickOverlay: \"\"\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_van_form, null, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_van_cell_group, {\n            inset: \"\"\n          }, {\n            default: _withCtx(function () {\n              return [_createVNode(_component_van_cell, {\n                onClick: _cache[7] || (_cache[7] = function ($event) {\n                  return $setup.showHank = true;\n                }),\n                name: \"usdt_type\"\n              }, {\n                title: _withCtx(function () {\n                  return [_createElementVNode(\"span\", _hoisted_22, _toDisplayString(_ctx.$t(\"msg.usdt_type\")), 1), _createTextVNode(\" \" + _toDisplayString($setup.usdt_type), 1)];\n                }),\n                _: 1\n              }), _createVNode(_component_van_field, {\n                class: \"zdy\",\n                modelValue: $setup.usdt_diz,\n                \"onUpdate:modelValue\": _cache[8] || (_cache[8] = function ($event) {\n                  return $setup.usdt_diz = $event;\n                }),\n                label: _ctx.$t('msg.usdt_address'),\n                name: \"usdt_diz\",\n                placeholder: _ctx.$t('msg.usdt_address'),\n                rules: [{\n                  required: true,\n                  message: _ctx.$t('msg.input_usdt_address')\n                }]\n              }, null, 8, [\"modelValue\", \"label\", \"placeholder\", \"rules\"]), _createVNode(_component_van_field, {\n                modelValue: $setup.paypassword,\n                \"onUpdate:modelValue\": _cache[9] || (_cache[9] = function ($event) {\n                  return $setup.paypassword = $event;\n                }),\n                label: _ctx.$t('msg.tx_pwd'),\n                type: \"password\",\n                placeholder: _ctx.$t('msg.input_tx_pwd')\n              }, null, 8, [\"modelValue\", \"label\", \"placeholder\"])];\n            }),\n            _: 1\n          })];\n        }),\n        _: 1\n      })];\n    }),\n    _: 1\n  }, 8, [\"show\", \"title\", \"onConfirm\", \"confirmButtonText\"])]);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}