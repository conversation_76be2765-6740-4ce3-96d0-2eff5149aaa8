{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _Loading from \"./Loading.mjs\";\nvar Loading = withInstall(_Loading);\nvar stdin_default = Loading;\nexport { Loading, stdin_default as default };", "map": {"version": 3, "names": ["withInstall", "_Loading", "Loading", "stdin_default", "default"], "sources": ["C:/Users/<USER>/Downloads/Telegram Desktop/vue3_3.0/vue3_3.0/node_modules/vant/es/loading/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _Loading from \"./Loading.mjs\";\nconst Loading = withInstall(_Loading);\nvar stdin_default = Loading;\nexport {\n  Loading,\n  stdin_default as default\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,QAAQ,MAAM,eAAe;AACpC,IAAMC,OAAO,GAAGF,WAAW,CAACC,QAAQ,CAAC;AACrC,IAAIE,aAAa,GAAGD,OAAO;AAC3B,SACEA,OAAO,EACPC,aAAa,IAAIC,OAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}