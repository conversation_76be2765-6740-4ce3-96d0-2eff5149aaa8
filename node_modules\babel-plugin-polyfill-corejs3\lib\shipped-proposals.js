"use strict";

exports.__esModule = true;
exports.default = void 0;

// This file is automatically generated by scripts/build-corejs3-shipped-proposals.mjs
var _default = new Set(["esnext.array.from-async", "esnext.array.group", "esnext.array.group-to-map", "esnext.array-buffer.detached", "esnext.array-buffer.transfer", "esnext.array-buffer.transfer-to-fixed-length", "esnext.json.is-raw-json", "esnext.json.parse", "esnext.json.raw-json", "esnext.set.difference.v2", "esnext.set.intersection.v2", "esnext.set.is-disjoint-from.v2", "esnext.set.is-subset-of.v2", "esnext.set.is-superset-of.v2", "esnext.set.symmetric-difference.v2", "esnext.set.union.v2", "esnext.string.is-well-formed", "esnext.string.to-well-formed"]);

exports.default = _default;