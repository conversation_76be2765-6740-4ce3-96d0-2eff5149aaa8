{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _Sidebar from \"./Sidebar.mjs\";\nvar Sidebar = withInstall(_Sidebar);\nvar stdin_default = Sidebar;\nexport { Sidebar, stdin_default as default };", "map": {"version": 3, "names": ["withInstall", "_Sidebar", "Sidebar", "stdin_default", "default"], "sources": ["C:/Users/<USER>/Desktop/vue3_3.0/node_modules/vant/es/sidebar/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _Sidebar from \"./Sidebar.mjs\";\nconst Sidebar = withInstall(_Sidebar);\nvar stdin_default = Sidebar;\nexport {\n  Sidebar,\n  stdin_default as default\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,QAAQ,MAAM,eAAe;AACpC,IAAMC,OAAO,GAAGF,WAAW,CAACC,QAAQ,CAAC;AACrC,IAAIE,aAAa,GAAGD,OAAO;AAC3B,SACEA,OAAO,EACPC,aAAa,IAAIC,OAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}