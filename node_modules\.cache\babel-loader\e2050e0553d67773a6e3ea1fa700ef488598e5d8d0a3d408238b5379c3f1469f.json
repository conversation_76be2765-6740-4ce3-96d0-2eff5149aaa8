{"ast": null, "code": "var unknownProp = null;\nvar numericProp = [Number, String];\nvar truthProp = {\n  type: Boolean,\n  default: true\n};\nvar makeRequiredProp = function makeRequiredProp(type) {\n  return {\n    type: type,\n    required: true\n  };\n};\nvar makeArrayProp = function makeArrayProp() {\n  return {\n    type: Array,\n    default: function _default() {\n      return [];\n    }\n  };\n};\nvar makeNumberProp = function makeNumberProp(defaultVal) {\n  return {\n    type: Number,\n    default: defaultVal\n  };\n};\nvar makeNumericProp = function makeNumericProp(defaultVal) {\n  return {\n    type: numericProp,\n    default: defaultVal\n  };\n};\nvar makeStringProp = function makeStringProp(defaultVal) {\n  return {\n    type: String,\n    default: defaultVal\n  };\n};\nexport { makeArrayProp, makeNumberProp, makeNumericProp, makeRequiredProp, makeStringProp, numericProp, truthProp, unknownProp };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}