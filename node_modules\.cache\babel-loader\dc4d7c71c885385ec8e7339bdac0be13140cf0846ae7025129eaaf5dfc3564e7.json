{"ast": null, "code": "'use strict';\n\nfunction _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\nfunction _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nfunction _iterableToArrayLimit(arr, i) { var _i = null == arr ? null : \"undefined\" != typeof Symbol && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (null != _i) { var _s, _e, _x, _r, _arr = [], _n = !0, _d = !1; try { if (_x = (_i = _i.call(arr)).next, 0 === i) { if (Object(_i) !== _i) return; _n = !1; } else for (; !(_n = (_s = _x.call(_i)).done) && (_arr.push(_s.value), _arr.length !== i); _n = !0); } catch (err) { _d = !0, _e = err; } finally { try { if (!_n && null != _i.return && (_r = _i.return(), Object(_r) !== _r)) return; } finally { if (_d) throw _e; } } return _arr; } }\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor); } }\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, \"prototype\", { writable: false }); return Constructor; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return _typeof(key) === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (_typeof(input) !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (_typeof(res) !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\nimport utils from '../utils.js';\nimport parseHeaders from '../helpers/parseHeaders.js';\nvar $internals = Symbol('internals');\nfunction normalizeHeader(header) {\n  return header && String(header).trim().toLowerCase();\n}\nfunction normalizeValue(value) {\n  if (value === false || value == null) {\n    return value;\n  }\n  return utils.isArray(value) ? value.map(normalizeValue) : String(value);\n}\nfunction parseTokens(str) {\n  var tokens = Object.create(null);\n  var tokensRE = /([^\\s,;=]+)\\s*(?:=\\s*([^,;]+))?/g;\n  var match;\n  while (match = tokensRE.exec(str)) {\n    tokens[match[1]] = match[2];\n  }\n  return tokens;\n}\nvar isValidHeaderName = function isValidHeaderName(str) {\n  return /^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(str.trim());\n};\nfunction matchHeaderValue(context, value, header, filter, isHeaderNameFilter) {\n  if (utils.isFunction(filter)) {\n    return filter.call(this, value, header);\n  }\n  if (isHeaderNameFilter) {\n    value = header;\n  }\n  if (!utils.isString(value)) return;\n  if (utils.isString(filter)) {\n    return value.indexOf(filter) !== -1;\n  }\n  if (utils.isRegExp(filter)) {\n    return filter.test(value);\n  }\n}\nfunction formatHeader(header) {\n  return header.trim().toLowerCase().replace(/([a-z\\d])(\\w*)/g, function (w, _char, str) {\n    return _char.toUpperCase() + str;\n  });\n}\nfunction buildAccessors(obj, header) {\n  var accessorName = utils.toCamelCase(' ' + header);\n  ['get', 'set', 'has'].forEach(function (methodName) {\n    Object.defineProperty(obj, methodName + accessorName, {\n      value: function value(arg1, arg2, arg3) {\n        return this[methodName].call(this, header, arg1, arg2, arg3);\n      },\n      configurable: true\n    });\n  });\n}\nvar AxiosHeaders = /*#__PURE__*/function (_Symbol$iterator, _Symbol$toStringTag) {\n  function AxiosHeaders(headers) {\n    _classCallCheck(this, AxiosHeaders);\n    headers && this.set(headers);\n  }\n  _createClass(AxiosHeaders, [{\n    key: \"set\",\n    value: function set(header, valueOrRewrite, rewrite) {\n      var self = this;\n      function setHeader(_value, _header, _rewrite) {\n        var lHeader = normalizeHeader(_header);\n        if (!lHeader) {\n          throw new Error('header name must be a non-empty string');\n        }\n        var key = utils.findKey(self, lHeader);\n        if (!key || self[key] === undefined || _rewrite === true || _rewrite === undefined && self[key] !== false) {\n          self[key || _header] = normalizeValue(_value);\n        }\n      }\n      var setHeaders = function setHeaders(headers, _rewrite) {\n        return utils.forEach(headers, function (_value, _header) {\n          return setHeader(_value, _header, _rewrite);\n        });\n      };\n      if (utils.isPlainObject(header) || header instanceof this.constructor) {\n        setHeaders(header, valueOrRewrite);\n      } else if (utils.isString(header) && (header = header.trim()) && !isValidHeaderName(header)) {\n        setHeaders(parseHeaders(header), valueOrRewrite);\n      } else {\n        header != null && setHeader(valueOrRewrite, header, rewrite);\n      }\n      return this;\n    }\n  }, {\n    key: \"get\",\n    value: function get(header, parser) {\n      header = normalizeHeader(header);\n      if (header) {\n        var key = utils.findKey(this, header);\n        if (key) {\n          var value = this[key];\n          if (!parser) {\n            return value;\n          }\n          if (parser === true) {\n            return parseTokens(value);\n          }\n          if (utils.isFunction(parser)) {\n            return parser.call(this, value, key);\n          }\n          if (utils.isRegExp(parser)) {\n            return parser.exec(value);\n          }\n          throw new TypeError('parser must be boolean|regexp|function');\n        }\n      }\n    }\n  }, {\n    key: \"has\",\n    value: function has(header, matcher) {\n      header = normalizeHeader(header);\n      if (header) {\n        var key = utils.findKey(this, header);\n        return !!(key && this[key] !== undefined && (!matcher || matchHeaderValue(this, this[key], key, matcher)));\n      }\n      return false;\n    }\n  }, {\n    key: \"delete\",\n    value: function _delete(header, matcher) {\n      var self = this;\n      var deleted = false;\n      function deleteHeader(_header) {\n        _header = normalizeHeader(_header);\n        if (_header) {\n          var key = utils.findKey(self, _header);\n          if (key && (!matcher || matchHeaderValue(self, self[key], key, matcher))) {\n            delete self[key];\n            deleted = true;\n          }\n        }\n      }\n      if (utils.isArray(header)) {\n        header.forEach(deleteHeader);\n      } else {\n        deleteHeader(header);\n      }\n      return deleted;\n    }\n  }, {\n    key: \"clear\",\n    value: function clear(matcher) {\n      var keys = Object.keys(this);\n      var i = keys.length;\n      var deleted = false;\n      while (i--) {\n        var key = keys[i];\n        if (!matcher || matchHeaderValue(this, this[key], key, matcher, true)) {\n          delete this[key];\n          deleted = true;\n        }\n      }\n      return deleted;\n    }\n  }, {\n    key: \"normalize\",\n    value: function normalize(format) {\n      var self = this;\n      var headers = {};\n      utils.forEach(this, function (value, header) {\n        var key = utils.findKey(headers, header);\n        if (key) {\n          self[key] = normalizeValue(value);\n          delete self[header];\n          return;\n        }\n        var normalized = format ? formatHeader(header) : String(header).trim();\n        if (normalized !== header) {\n          delete self[header];\n        }\n        self[normalized] = normalizeValue(value);\n        headers[normalized] = true;\n      });\n      return this;\n    }\n  }, {\n    key: \"concat\",\n    value: function concat() {\n      var _this$constructor;\n      for (var _len = arguments.length, targets = new Array(_len), _key = 0; _key < _len; _key++) {\n        targets[_key] = arguments[_key];\n      }\n      return (_this$constructor = this.constructor).concat.apply(_this$constructor, [this].concat(targets));\n    }\n  }, {\n    key: \"toJSON\",\n    value: function toJSON(asStrings) {\n      var obj = Object.create(null);\n      utils.forEach(this, function (value, header) {\n        value != null && value !== false && (obj[header] = asStrings && utils.isArray(value) ? value.join(', ') : value);\n      });\n      return obj;\n    }\n  }, {\n    key: _Symbol$iterator,\n    value: function value() {\n      return Object.entries(this.toJSON())[Symbol.iterator]();\n    }\n  }, {\n    key: \"toString\",\n    value: function toString() {\n      return Object.entries(this.toJSON()).map(function (_ref) {\n        var _ref2 = _slicedToArray(_ref, 2),\n          header = _ref2[0],\n          value = _ref2[1];\n        return header + ': ' + value;\n      }).join('\\n');\n    }\n  }, {\n    key: _Symbol$toStringTag,\n    get: function get() {\n      return 'AxiosHeaders';\n    }\n  }], [{\n    key: \"from\",\n    value: function from(thing) {\n      return thing instanceof this ? thing : new this(thing);\n    }\n  }, {\n    key: \"concat\",\n    value: function concat(first) {\n      var computed = new this(first);\n      for (var _len2 = arguments.length, targets = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n        targets[_key2 - 1] = arguments[_key2];\n      }\n      targets.forEach(function (target) {\n        return computed.set(target);\n      });\n      return computed;\n    }\n  }, {\n    key: \"accessor\",\n    value: function accessor(header) {\n      var internals = this[$internals] = this[$internals] = {\n        accessors: {}\n      };\n      var accessors = internals.accessors;\n      var prototype = this.prototype;\n      function defineAccessor(_header) {\n        var lHeader = normalizeHeader(_header);\n        if (!accessors[lHeader]) {\n          buildAccessors(prototype, _header);\n          accessors[lHeader] = true;\n        }\n      }\n      utils.isArray(header) ? header.forEach(defineAccessor) : defineAccessor(header);\n      return this;\n    }\n  }]);\n  return AxiosHeaders;\n}(Symbol.iterator, Symbol.toStringTag);\nAxiosHeaders.accessor(['Content-Type', 'Content-Length', 'Accept', 'Accept-Encoding', 'User-Agent', 'Authorization']);\nutils.freezeMethods(AxiosHeaders.prototype);\nutils.freezeMethods(AxiosHeaders);\nexport default AxiosHeaders;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}