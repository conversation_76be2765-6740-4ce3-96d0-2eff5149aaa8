{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString, createElementVNode as _createElementVNode, createCommentVNode as _createCommentVNode, normalizeClass as _normalizeClass, pushScopeId as _pushScopeId, popScopeId as _popScopeId } from \"vue\";\nvar _withScopeId = function _withScopeId(n) {\n  return _pushScopeId(\"data-v-5e88f844\"), n = n(), _popScopeId(), n;\n};\nvar _hoisted_1 = {\n  class: \"tel home\"\n};\nvar _hoisted_2 = {\n  class: \"l\"\n};\nvar _hoisted_3 = {\n  class: \"time\"\n};\nvar _hoisted_4 = {\n  class: \"tag\"\n};\nvar _hoisted_5 = {\n  class: \"c\"\n};\nvar _hoisted_6 = {\n  class: \"time\"\n};\nvar _hoisted_7 = {\n  class: \"tag\"\n};\nvar _hoisted_8 = [\"onClick\"];\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_van_icon = _resolveComponent(\"van-icon\");\n  var _component_van_nav_bar = _resolveComponent(\"van-nav-bar\");\n  var _component_van_list = _resolveComponent(\"van-list\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_van_nav_bar, {\n    title: _ctx.$t('msg.zrjl'),\n    \"left-arrow\": \"\",\n    onClickLeft: _cache[0] || (_cache[0] = function ($event) {\n      return _ctx.$router.go(-1);\n    }),\n    onClickRight: $setup.clickRight\n  }, {\n    right: _withCtx(function () {\n      return [_createVNode(_component_van_icon, {\n        name: \"comment-o\",\n        size: \"18\"\n      })];\n    }),\n    _: 1\n  }, 8, [\"title\", \"onClickRight\"]), _createVNode(_component_van_list, {\n    loading: $setup.loading,\n    \"onUpdate:loading\": _cache[1] || (_cache[1] = function ($event) {\n      return $setup.loading = $event;\n    }),\n    finished: $setup.finished,\n    \"finished-text\": _ctx.$t('msg.not_move'),\n    onLoad: $setup.getCW\n  }, {\n    default: _withCtx(function () {\n      return [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.list, function (item, index) {\n        return _openBlock(), _createElementBlock(\"div\", {\n          class: _normalizeClass([\"address\", item.is_qu && 'mb30']),\n          key: index\n        }, [_createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, _toDisplayString(_ctx.$t('msg.zrlxb') + item.day + _ctx.$t('msg.day') + item.bili * 100 + '%'), 1), _createElementVNode(\"div\", _hoisted_4, _toDisplayString(item.num), 1)]), _createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"div\", _hoisted_6, _toDisplayString(_ctx.$t('msg.crsj')) + \"：\" + _toDisplayString($setup.formatTime('', item.addtime)), 1), _createElementVNode(\"div\", _hoisted_7, _toDisplayString(_ctx.$t('msg.zrlxb')), 1)]), !item.is_qu ? (_openBlock(), _createElementBlock(\"div\", {\n          key: 0,\n          class: \"r\",\n          onClick: function onClick($event) {\n            return $setup.qu_money(item);\n          }\n        }, _toDisplayString(_ctx.$t('msg.out_money')), 9, _hoisted_8)) : _createCommentVNode(\"\", true)], 2);\n      }), 128))];\n    }),\n    _: 1\n  }, 8, [\"loading\", \"finished\", \"finished-text\", \"onLoad\"])]);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}