{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _ActionSheet from \"./ActionSheet.mjs\";\nvar ActionSheet = withInstall(_ActionSheet);\nvar stdin_default = ActionSheet;\nexport { ActionSheet, stdin_default as default };", "map": {"version": 3, "names": ["withInstall", "_ActionSheet", "ActionSheet", "stdin_default", "default"], "sources": ["C:/Users/<USER>/Downloads/Telegram Desktop/vue3_3.0/vue3_3.0/node_modules/vant/es/action-sheet/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _ActionSheet from \"./ActionSheet.mjs\";\nconst ActionSheet = withInstall(_ActionSheet);\nvar stdin_default = ActionSheet;\nexport {\n  ActionSheet,\n  stdin_default as default\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,YAAY,MAAM,mBAAmB;AAC5C,IAAMC,WAAW,GAAGF,WAAW,CAACC,YAAY,CAAC;AAC7C,IAAIE,aAAa,GAAGD,WAAW;AAC/B,SACEA,WAAW,EACXC,aAAa,IAAIC,OAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}