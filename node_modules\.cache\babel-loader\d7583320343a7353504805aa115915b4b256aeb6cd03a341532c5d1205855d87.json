{"ast": null, "code": "import { useRect } from \"@vant/use\";\nimport { ref, onMounted, nextTick } from \"vue\";\nimport { onPopupReopen } from \"./on-popup-reopen.mjs\";\nvar useHeight = function useHeight(element, withSafeArea) {\n  var height = ref();\n  var setHeight = function setHeight() {\n    height.value = useRect(element).height;\n  };\n  onMounted(function () {\n    nextTick(setHeight);\n    if (withSafeArea) {\n      for (var i = 1; i <= 3; i++) {\n        setTimeout(setHeight, 100 * i);\n      }\n    }\n  });\n  onPopupReopen(function () {\n    return nextTick(setHeight);\n  });\n  return height;\n};\nexport { useHeight };", "map": {"version": 3, "names": ["useRect", "ref", "onMounted", "nextTick", "onPopupReopen", "useHeight", "element", "withSafeArea", "height", "setHeight", "value", "i", "setTimeout"], "sources": ["C:/Users/<USER>/Desktop/vue3_3.0/node_modules/vant/es/composables/use-height.mjs"], "sourcesContent": ["import { useRect } from \"@vant/use\";\nimport { ref, onMounted, nextTick } from \"vue\";\nimport { onPopupReopen } from \"./on-popup-reopen.mjs\";\nconst useHeight = (element, withSafeArea) => {\n  const height = ref();\n  const setHeight = () => {\n    height.value = useRect(element).height;\n  };\n  onMounted(() => {\n    nextTick(setHeight);\n    if (withSafeArea) {\n      for (let i = 1; i <= 3; i++) {\n        setTimeout(setHeight, 100 * i);\n      }\n    }\n  });\n  onPopupReopen(() => nextTick(setHeight));\n  return height;\n};\nexport {\n  useHeight\n};\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,WAAW;AACnC,SAASC,GAAG,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,KAAK;AAC9C,SAASC,aAAa,QAAQ,uBAAuB;AACrD,IAAMC,SAAS,GAAG,SAAZA,SAASA,CAAIC,OAAO,EAAEC,YAAY,EAAK;EAC3C,IAAMC,MAAM,GAAGP,GAAG,CAAC,CAAC;EACpB,IAAMQ,SAAS,GAAG,SAAZA,SAASA,CAAA,EAAS;IACtBD,MAAM,CAACE,KAAK,GAAGV,OAAO,CAACM,OAAO,CAAC,CAACE,MAAM;EACxC,CAAC;EACDN,SAAS,CAAC,YAAM;IACdC,QAAQ,CAACM,SAAS,CAAC;IACnB,IAAIF,YAAY,EAAE;MAChB,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;QAC3BC,UAAU,CAACH,SAAS,EAAE,GAAG,GAAGE,CAAC,CAAC;MAChC;IACF;EACF,CAAC,CAAC;EACFP,aAAa,CAAC;IAAA,OAAMD,QAAQ,CAACM,SAAS,CAAC;EAAA,EAAC;EACxC,OAAOD,MAAM;AACf,CAAC;AACD,SACEH,SAAS"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}