{"ast": null, "code": "function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nfunction _iterableToArrayLimit(arr, i) { var _i = null == arr ? null : \"undefined\" != typeof Symbol && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (null != _i) { var _s, _e, _x, _r, _arr = [], _n = !0, _d = !1; try { if (_x = (_i = _i.call(arr)).next, 0 === i) { if (Object(_i) !== _i) return; _n = !1; } else for (; !(_n = (_s = _x.call(_i)).done) && (_arr.push(_s.value), _arr.length !== i); _n = !0); } catch (err) { _d = !0, _e = err; } finally { try { if (!_n && null != _i.return && (_r = _i.return(), Object(_r) !== _r)) return; } finally { if (_d) throw _e; } } return _arr; } }\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\nimport { createVNode as _createVNode } from \"vue\";\nimport { computed, defineComponent } from \"vue\";\nimport { makeStringProp, createNamespace, makeRequiredProp } from \"../utils/index.mjs\";\nimport { getDate, formatAmount, formatDiscount } from \"./utils.mjs\";\nimport { Checkbox } from \"../checkbox/index.mjs\";\nvar _createNamespace = createNamespace(\"coupon\"),\n  _createNamespace2 = _slicedToArray(_createNamespace, 3),\n  name = _createNamespace2[0],\n  bem = _createNamespace2[1],\n  t = _createNamespace2[2];\nvar stdin_default = defineComponent({\n  name: name,\n  props: {\n    chosen: Boolean,\n    coupon: makeRequiredProp(Object),\n    disabled: Boolean,\n    currency: makeStringProp(\"\\xA5\")\n  },\n  setup: function setup(props) {\n    var validPeriod = computed(function () {\n      var _props$coupon = props.coupon,\n        startAt = _props$coupon.startAt,\n        endAt = _props$coupon.endAt;\n      return \"\".concat(getDate(startAt), \" - \").concat(getDate(endAt));\n    });\n    var faceAmount = computed(function () {\n      var coupon = props.coupon,\n        currency = props.currency;\n      if (coupon.valueDesc) {\n        return [coupon.valueDesc, _createVNode(\"span\", null, [coupon.unitDesc || \"\"])];\n      }\n      if (coupon.denominations) {\n        var denominations = formatAmount(coupon.denominations);\n        return [_createVNode(\"span\", null, [currency]), \" \".concat(denominations)];\n      }\n      if (coupon.discount) {\n        return t(\"discount\", formatDiscount(coupon.discount));\n      }\n      return \"\";\n    });\n    var conditionMessage = computed(function () {\n      var condition = formatAmount(props.coupon.originCondition || 0);\n      return condition === \"0\" ? t(\"unlimited\") : t(\"condition\", condition);\n    });\n    return function () {\n      var chosen = props.chosen,\n        coupon = props.coupon,\n        disabled = props.disabled;\n      var description = disabled && coupon.reason || coupon.description;\n      return _createVNode(\"div\", {\n        \"class\": bem({\n          disabled: disabled\n        })\n      }, [_createVNode(\"div\", {\n        \"class\": bem(\"content\")\n      }, [_createVNode(\"div\", {\n        \"class\": bem(\"head\")\n      }, [_createVNode(\"h2\", {\n        \"class\": bem(\"amount\")\n      }, [faceAmount.value]), _createVNode(\"p\", {\n        \"class\": bem(\"condition\")\n      }, [coupon.condition || conditionMessage.value])]), _createVNode(\"div\", {\n        \"class\": bem(\"body\")\n      }, [_createVNode(\"p\", {\n        \"class\": bem(\"name\")\n      }, [coupon.name]), _createVNode(\"p\", {\n        \"class\": bem(\"valid\")\n      }, [validPeriod.value]), !disabled && _createVNode(Checkbox, {\n        \"class\": bem(\"corner\"),\n        \"modelValue\": chosen\n      }, null)])]), description && _createVNode(\"p\", {\n        \"class\": bem(\"description\")\n      }, [description])]);\n    };\n  }\n});\nexport { stdin_default as default };", "map": {"version": 3, "names": ["createVNode", "_createVNode", "computed", "defineComponent", "makeStringProp", "createNamespace", "makeRequiredProp", "getDate", "formatAmount", "formatDiscount", "Checkbox", "_createNamespace", "_createNamespace2", "_slicedToArray", "name", "bem", "t", "stdin_default", "props", "chosen", "Boolean", "coupon", "Object", "disabled", "currency", "setup", "validPeriod", "_props$coupon", "startAt", "endAt", "concat", "faceAmount", "valueDesc", "unitDesc", "denominations", "discount", "conditionMessage", "condition", "originCondition", "description", "reason", "value", "default"], "sources": ["C:/Users/<USER>/Desktop/vue3_3.0/node_modules/vant/es/coupon/Coupon.mjs"], "sourcesContent": ["import { createVNode as _createVNode } from \"vue\";\nimport { computed, defineComponent } from \"vue\";\nimport { makeStringProp, createNamespace, makeRequiredProp } from \"../utils/index.mjs\";\nimport { getDate, formatAmount, formatDiscount } from \"./utils.mjs\";\nimport { Checkbox } from \"../checkbox/index.mjs\";\nconst [name, bem, t] = createNamespace(\"coupon\");\nvar stdin_default = defineComponent({\n  name,\n  props: {\n    chosen: Boolean,\n    coupon: makeRequiredProp(Object),\n    disabled: Boolean,\n    currency: makeStringProp(\"\\xA5\")\n  },\n  setup(props) {\n    const validPeriod = computed(() => {\n      const {\n        startAt,\n        endAt\n      } = props.coupon;\n      return `${getDate(startAt)} - ${getDate(endAt)}`;\n    });\n    const faceAmount = computed(() => {\n      const {\n        coupon,\n        currency\n      } = props;\n      if (coupon.valueDesc) {\n        return [coupon.valueDesc, _createVNode(\"span\", null, [coupon.unitDesc || \"\"])];\n      }\n      if (coupon.denominations) {\n        const denominations = formatAmount(coupon.denominations);\n        return [_createVNode(\"span\", null, [currency]), ` ${denominations}`];\n      }\n      if (coupon.discount) {\n        return t(\"discount\", formatDiscount(coupon.discount));\n      }\n      return \"\";\n    });\n    const conditionMessage = computed(() => {\n      const condition = formatAmount(props.coupon.originCondition || 0);\n      return condition === \"0\" ? t(\"unlimited\") : t(\"condition\", condition);\n    });\n    return () => {\n      const {\n        chosen,\n        coupon,\n        disabled\n      } = props;\n      const description = disabled && coupon.reason || coupon.description;\n      return _createVNode(\"div\", {\n        \"class\": bem({\n          disabled\n        })\n      }, [_createVNode(\"div\", {\n        \"class\": bem(\"content\")\n      }, [_createVNode(\"div\", {\n        \"class\": bem(\"head\")\n      }, [_createVNode(\"h2\", {\n        \"class\": bem(\"amount\")\n      }, [faceAmount.value]), _createVNode(\"p\", {\n        \"class\": bem(\"condition\")\n      }, [coupon.condition || conditionMessage.value])]), _createVNode(\"div\", {\n        \"class\": bem(\"body\")\n      }, [_createVNode(\"p\", {\n        \"class\": bem(\"name\")\n      }, [coupon.name]), _createVNode(\"p\", {\n        \"class\": bem(\"valid\")\n      }, [validPeriod.value]), !disabled && _createVNode(Checkbox, {\n        \"class\": bem(\"corner\"),\n        \"modelValue\": chosen\n      }, null)])]), description && _createVNode(\"p\", {\n        \"class\": bem(\"description\")\n      }, [description])]);\n    };\n  }\n});\nexport {\n  stdin_default as default\n};\n"], "mappings": ";;;;;;AAAA,SAASA,WAAW,IAAIC,YAAY,QAAQ,KAAK;AACjD,SAASC,QAAQ,EAAEC,eAAe,QAAQ,KAAK;AAC/C,SAASC,cAAc,EAAEC,eAAe,EAAEC,gBAAgB,QAAQ,oBAAoB;AACtF,SAASC,OAAO,EAAEC,YAAY,EAAEC,cAAc,QAAQ,aAAa;AACnE,SAASC,QAAQ,QAAQ,uBAAuB;AAChD,IAAAC,gBAAA,GAAuBN,eAAe,CAAC,QAAQ,CAAC;EAAAO,iBAAA,GAAAC,cAAA,CAAAF,gBAAA;EAAzCG,IAAI,GAAAF,iBAAA;EAAEG,GAAG,GAAAH,iBAAA;EAAEI,CAAC,GAAAJ,iBAAA;AACnB,IAAIK,aAAa,GAAGd,eAAe,CAAC;EAClCW,IAAI,EAAJA,IAAI;EACJI,KAAK,EAAE;IACLC,MAAM,EAAEC,OAAO;IACfC,MAAM,EAAEf,gBAAgB,CAACgB,MAAM,CAAC;IAChCC,QAAQ,EAAEH,OAAO;IACjBI,QAAQ,EAAEpB,cAAc,CAAC,MAAM;EACjC,CAAC;EACDqB,KAAK,WAAAA,MAACP,KAAK,EAAE;IACX,IAAMQ,WAAW,GAAGxB,QAAQ,CAAC,YAAM;MACjC,IAAAyB,aAAA,GAGIT,KAAK,CAACG,MAAM;QAFdO,OAAO,GAAAD,aAAA,CAAPC,OAAO;QACPC,KAAK,GAAAF,aAAA,CAALE,KAAK;MAEP,UAAAC,MAAA,CAAUvB,OAAO,CAACqB,OAAO,CAAC,SAAAE,MAAA,CAAMvB,OAAO,CAACsB,KAAK,CAAC;IAChD,CAAC,CAAC;IACF,IAAME,UAAU,GAAG7B,QAAQ,CAAC,YAAM;MAChC,IACEmB,MAAM,GAEJH,KAAK,CAFPG,MAAM;QACNG,QAAQ,GACNN,KAAK,CADPM,QAAQ;MAEV,IAAIH,MAAM,CAACW,SAAS,EAAE;QACpB,OAAO,CAACX,MAAM,CAACW,SAAS,EAAE/B,YAAY,CAAC,MAAM,EAAE,IAAI,EAAE,CAACoB,MAAM,CAACY,QAAQ,IAAI,EAAE,CAAC,CAAC,CAAC;MAChF;MACA,IAAIZ,MAAM,CAACa,aAAa,EAAE;QACxB,IAAMA,aAAa,GAAG1B,YAAY,CAACa,MAAM,CAACa,aAAa,CAAC;QACxD,OAAO,CAACjC,YAAY,CAAC,MAAM,EAAE,IAAI,EAAE,CAACuB,QAAQ,CAAC,CAAC,MAAAM,MAAA,CAAMI,aAAa,EAAG;MACtE;MACA,IAAIb,MAAM,CAACc,QAAQ,EAAE;QACnB,OAAOnB,CAAC,CAAC,UAAU,EAAEP,cAAc,CAACY,MAAM,CAACc,QAAQ,CAAC,CAAC;MACvD;MACA,OAAO,EAAE;IACX,CAAC,CAAC;IACF,IAAMC,gBAAgB,GAAGlC,QAAQ,CAAC,YAAM;MACtC,IAAMmC,SAAS,GAAG7B,YAAY,CAACU,KAAK,CAACG,MAAM,CAACiB,eAAe,IAAI,CAAC,CAAC;MACjE,OAAOD,SAAS,KAAK,GAAG,GAAGrB,CAAC,CAAC,WAAW,CAAC,GAAGA,CAAC,CAAC,WAAW,EAAEqB,SAAS,CAAC;IACvE,CAAC,CAAC;IACF,OAAO,YAAM;MACX,IACElB,MAAM,GAGJD,KAAK,CAHPC,MAAM;QACNE,MAAM,GAEJH,KAAK,CAFPG,MAAM;QACNE,QAAQ,GACNL,KAAK,CADPK,QAAQ;MAEV,IAAMgB,WAAW,GAAGhB,QAAQ,IAAIF,MAAM,CAACmB,MAAM,IAAInB,MAAM,CAACkB,WAAW;MACnE,OAAOtC,YAAY,CAAC,KAAK,EAAE;QACzB,OAAO,EAAEc,GAAG,CAAC;UACXQ,QAAQ,EAARA;QACF,CAAC;MACH,CAAC,EAAE,CAACtB,YAAY,CAAC,KAAK,EAAE;QACtB,OAAO,EAAEc,GAAG,CAAC,SAAS;MACxB,CAAC,EAAE,CAACd,YAAY,CAAC,KAAK,EAAE;QACtB,OAAO,EAAEc,GAAG,CAAC,MAAM;MACrB,CAAC,EAAE,CAACd,YAAY,CAAC,IAAI,EAAE;QACrB,OAAO,EAAEc,GAAG,CAAC,QAAQ;MACvB,CAAC,EAAE,CAACgB,UAAU,CAACU,KAAK,CAAC,CAAC,EAAExC,YAAY,CAAC,GAAG,EAAE;QACxC,OAAO,EAAEc,GAAG,CAAC,WAAW;MAC1B,CAAC,EAAE,CAACM,MAAM,CAACgB,SAAS,IAAID,gBAAgB,CAACK,KAAK,CAAC,CAAC,CAAC,CAAC,EAAExC,YAAY,CAAC,KAAK,EAAE;QACtE,OAAO,EAAEc,GAAG,CAAC,MAAM;MACrB,CAAC,EAAE,CAACd,YAAY,CAAC,GAAG,EAAE;QACpB,OAAO,EAAEc,GAAG,CAAC,MAAM;MACrB,CAAC,EAAE,CAACM,MAAM,CAACP,IAAI,CAAC,CAAC,EAAEb,YAAY,CAAC,GAAG,EAAE;QACnC,OAAO,EAAEc,GAAG,CAAC,OAAO;MACtB,CAAC,EAAE,CAACW,WAAW,CAACe,KAAK,CAAC,CAAC,EAAE,CAAClB,QAAQ,IAAItB,YAAY,CAACS,QAAQ,EAAE;QAC3D,OAAO,EAAEK,GAAG,CAAC,QAAQ,CAAC;QACtB,YAAY,EAAEI;MAChB,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEoB,WAAW,IAAItC,YAAY,CAAC,GAAG,EAAE;QAC7C,OAAO,EAAEc,GAAG,CAAC,aAAa;MAC5B,CAAC,EAAE,CAACwB,WAAW,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC;EACH;AACF,CAAC,CAAC;AACF,SACEtB,aAAa,IAAIyB,OAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}