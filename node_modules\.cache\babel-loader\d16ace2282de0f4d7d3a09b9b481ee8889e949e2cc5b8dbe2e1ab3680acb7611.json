{"ast": null, "code": "import { ref } from 'vue';\nimport store from '@/store/index';\nimport { vantLocales } from '@/i18n/i18n';\nimport { useI18n } from 'vue-i18n';\nexport default {\n  name: 'LanguageSwitcher',\n  props: {\n    color: String\n  },\n  setup: function setup() {\n    var _store$state$baseInfo;\n    // 语言切换\n    var _useI18n = useI18n(),\n      locale = _useI18n.locale;\n    var langcheck = ref(store.state.lang);\n    var langImg = ref('');\n    langImg.value = store.state.langImg;\n    var langs = ref((_store$state$baseInfo = store.state.baseInfo) === null || _store$state$baseInfo === void 0 ? void 0 : _store$state$baseInfo.languageList);\n    var show = ref(false);\n    var showLang = function showLang() {\n      langcheck.value = store.state.lang;\n      show.value = true;\n    };\n    var handSeletlanguages = function handSeletlanguages(row) {\n      langcheck.value = row.link;\n      langImg.value = row.image_url;\n      submitLang();\n    };\n    var submitLang = function submitLang() {\n      locale.value = langcheck.value;\n      store.dispatch('changelang', langcheck.value);\n      store.dispatch('changelangImg', langImg.value);\n      vantLocales(locale.value);\n      show.value = false;\n    };\n    submitLang();\n    return {\n      show: show,\n      submitLang: submitLang,\n      handSeletlanguages: handSeletlanguages,\n      langs: langs,\n      showLang: showLang,\n      langcheck: langcheck\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "store", "vantLocales", "useI18n", "name", "props", "color", "String", "setup", "_store$state$baseInfo", "_useI18n", "locale", "langcheck", "state", "lang", "langImg", "value", "langs", "baseInfo", "languageList", "show", "showLang", "handSeletlanguages", "row", "link", "image_url", "submitLang", "dispatch"], "sources": ["C:\\Users\\<USER>\\Downloads\\Telegram Desktop\\vue3_3.0\\vue3_3.0\\src\\components\\lang.vue"], "sourcesContent": ["<template>\n    <div class=\"footer\">\n        <img :src=\"require('@/assets/images/lang'+(color == 'white' ? 1 : '')+'.png')\" class=\"lang\" height=\"27\" width=\"27\" alt=\"\" @click=\"showLang()\">\n        <div v-if=\"show\">\n            <van-dialog v-model:show=\"show\" :showConfirmButton=\"false\" closeOnClickOverlay class=\"lang-dialog\">\n                <div class=\"lang_box\">\n                    <!-- <img :src=\"require('@/assets/images/register/lang_bg.png')\" class=\"lang_bg\" /> -->\n                    <div class=\"title\">{{$t('msg.check_lang')}}</div>\n                    <div class=\"content\">\n                        <!-- <img :src=\"require('@/assets/images/register/qiu.png')\" class=\"qiu\" /> -->\n                        <div class=\"langs\">\n                            <span class=\"li\" :class=\"langcheck==item.link && 'check'\" v-for=\"(item,index) in langs\" :key=\"index\"  @click=\"handSeletlanguages(item)\">\n                                <img :src=\"item.image_url\" class=\"img\" height=\"18\" width=\"27\" alt=\"\">\n                                <span class=\"text\">{{item.name}}</span>\n                            </span>\n                        </div>\n                        <div class=\"btn\">\n                        <!-- <van-button round block type=\"primary\" @click=\"submitLang\">\n                            {{$t('msg.nowQh')}}\n                        </van-button> -->\n                        </div>\n                    </div>\n                </div>\n            </van-dialog>\n        </div>\n    </div>\n</template>\n<script>\nimport { ref } from 'vue';\nimport store from '@/store/index'\nimport {vantLocales} from '@/i18n/i18n';\nimport { useI18n } from 'vue-i18n'\nexport default {\n    name: 'LanguageSwitcher',\n    props: {\n        color: String\n    },\n    setup(){\n        // 语言切换\n        const { locale } = useI18n()\n        const langcheck = ref(store.state.lang)\n        const langImg = ref('')\n        \n        langImg.value = store.state.langImg\n        const langs = ref(store.state.baseInfo?.languageList)\n\n        const show = ref(false);\n\n        const showLang = () => {\n            langcheck.value = store.state.lang\n            show.value = true\n        }\n        \n        const handSeletlanguages = (row) => {\n            langcheck.value = row.link\n            langImg.value = row.image_url\n            submitLang()\n        }\n        \n        const submitLang = () => {\n            locale.value = langcheck.value\n            store.dispatch('changelang', langcheck.value)\n            store.dispatch('changelangImg', langImg.value)\n            vantLocales(locale.value)\n            show.value = false\n        }\n      \n        submitLang()\n        return {show, submitLang, handSeletlanguages, langs, showLang, langcheck}\n    }\n}\n</script>\n<style lang=\"scss\" scoped>\n    :deep(.lang-dialog) {\n        .van-dialog__content {\n            max-height: 80vh;\n            overflow-y: auto;\n        }\n    }\n    .lang_box{\n        width: 100%;\n        position: relative;\n        padding-top: 60px;\n        .lang_title {\n            margin-bottom: 40px;\n        }\n        .lang_bg{\n        width: 100%;\n        position: absolute;\n        top: 0;\n        left: 0;\n        }\n        .content{\n        position: relative;\n        z-index: 1;\n        text-align: center;\n        .qiu{\n            width: 175px;\n            border-radius: 50%;\n            box-shadow: $shadow;\n            margin-bottom: 6px;\n        }\n        .langs{\n            margin-bottom: 15px;\n            max-height: 50vh;\n            overflow-y: auto;\n            -webkit-overflow-scrolling: touch;\n            border: 1px solid #ccc;\n            margin: 24px;\n            border-radius: 24px;\n            .li{\n                padding: 24px;\n                display: block;\n                text-align: left;\n                border-bottom: 1px solid #ccc;\n                &:last-child{\n                    border-bottom: none;\n                }\n                &.ctn{\n                    padding: 24px;\n                }\n                &.check{\n                    background-color: #ccc;\n                }\n                .img{\n                    margin-right: 34px;\n                    vertical-align: middle;\n                }\n                .text{\n                    font-size: 26px;\n                    color:$textColor;\n                }\n            }\n        }\n        .btn{\n            padding: 50px 54px 50px;\n        }\n        }\n    }\n</style>"], "mappings": "AA4BA,SAASA,GAAE,QAAS,KAAK;AACzB,OAAOC,KAAI,MAAO,eAAc;AAChC,SAAQC,WAAW,QAAO,aAAa;AACvC,SAASC,OAAM,QAAS,UAAS;AACjC,eAAe;EACXC,IAAI,EAAE,kBAAkB;EACxBC,KAAK,EAAE;IACHC,KAAK,EAAEC;EACX,CAAC;EACDC,KAAK,WAAAA,MAAA,EAAE;IAAA,IAAAC,qBAAA;IACH;IACA,IAAAC,QAAA,GAAmBP,OAAO,CAAC;MAAnBQ,MAAK,GAAAD,QAAA,CAALC,MAAK;IACb,IAAMC,SAAQ,GAAIZ,GAAG,CAACC,KAAK,CAACY,KAAK,CAACC,IAAI;IACtC,IAAMC,OAAM,GAAIf,GAAG,CAAC,EAAE;IAEtBe,OAAO,CAACC,KAAI,GAAIf,KAAK,CAACY,KAAK,CAACE,OAAM;IAClC,IAAME,KAAI,GAAIjB,GAAG,EAAAS,qBAAA,GAACR,KAAK,CAACY,KAAK,CAACK,QAAQ,cAAAT,qBAAA,uBAApBA,qBAAA,CAAsBU,YAAY;IAEpD,IAAMC,IAAG,GAAIpB,GAAG,CAAC,KAAK,CAAC;IAEvB,IAAMqB,QAAO,GAAI,SAAXA,QAAOA,CAAA,EAAU;MACnBT,SAAS,CAACI,KAAI,GAAIf,KAAK,CAACY,KAAK,CAACC,IAAG;MACjCM,IAAI,CAACJ,KAAI,GAAI,IAAG;IACpB;IAEA,IAAMM,kBAAiB,GAAI,SAArBA,kBAAiBA,CAAKC,GAAG,EAAK;MAChCX,SAAS,CAACI,KAAI,GAAIO,GAAG,CAACC,IAAG;MACzBT,OAAO,CAACC,KAAI,GAAIO,GAAG,CAACE,SAAQ;MAC5BC,UAAU,CAAC;IACf;IAEA,IAAMA,UAAS,GAAI,SAAbA,UAASA,CAAA,EAAU;MACrBf,MAAM,CAACK,KAAI,GAAIJ,SAAS,CAACI,KAAI;MAC7Bf,KAAK,CAAC0B,QAAQ,CAAC,YAAY,EAAEf,SAAS,CAACI,KAAK;MAC5Cf,KAAK,CAAC0B,QAAQ,CAAC,eAAe,EAAEZ,OAAO,CAACC,KAAK;MAC7Cd,WAAW,CAACS,MAAM,CAACK,KAAK;MACxBI,IAAI,CAACJ,KAAI,GAAI,KAAI;IACrB;IAEAU,UAAU,CAAC;IACX,OAAO;MAACN,IAAI,EAAJA,IAAI;MAAEM,UAAU,EAAVA,UAAU;MAAEJ,kBAAkB,EAAlBA,kBAAkB;MAAEL,KAAK,EAALA,KAAK;MAAEI,QAAQ,EAARA,QAAQ;MAAET,SAAS,EAATA;IAAS;EAC5E;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}