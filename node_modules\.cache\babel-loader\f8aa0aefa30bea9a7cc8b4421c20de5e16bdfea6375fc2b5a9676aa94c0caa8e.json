{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, toDisplayString as _toDisplayString, createElementVNode as _createElementVNode, createTextVNode as _createTextVNode, createCommentVNode as _createCommentVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, pushScopeId as _pushScopeId, popScopeId as _popScopeId } from \"vue\";\nvar _withScopeId = function _withScopeId(n) {\n  return _pushScopeId(\"data-v-21e2bc42\"), n = n(), _popScopeId(), n;\n};\nvar _hoisted_1 = {\n  class: \"tel home\"\n};\nvar _hoisted_2 = {\n  class: \"box\"\n};\nvar _hoisted_3 = {\n  class: \"box_t\"\n};\nvar _hoisted_4 = {\n  class: \"box_tlt\"\n};\nvar _hoisted_5 = [\"src\"];\nvar _hoisted_6 = {\n  class: \"box_fot\"\n};\nvar _hoisted_7 = {\n  class: \"box_text\"\n};\nvar _hoisted_8 = {\n  class: \"qr-code qr\",\n  ref: \"qrCodeUrl\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_van_nav_bar = _resolveComponent(\"van-nav-bar\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_van_nav_bar, {\n    title: _ctx.$t('msg.code'),\n    background: \"#ffffff\",\n    \"title-style\": \"color:black; font-size: 16px;\",\n    \"left-arrow\": \"\",\n    onClickLeft: _cache[0] || (_cache[0] = function ($event) {\n      return _ctx.$router.go(-1);\n    })\n  }, null, 8 /* PROPS */, [\"title\"]), _createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"div\", _hoisted_4, [_createTextVNode(_toDisplayString(_ctx.$t('msg.code')) + \" : \", 1 /* TEXT */), _createElementVNode(\"span\", null, _toDisplayString($setup.info.invite_code), 1 /* TEXT */), _createElementVNode(\"img\", {\n    src: require('@/assets/images/copy.svg'),\n    alt: \"\",\n    onClick: _cache[1] || (_cache[1] = function ($event) {\n      var _$setup$info;\n      return $setup.copy((_$setup$info = $setup.info) === null || _$setup$info === void 0 ? void 0 : _$setup$info.url);\n    })\n  }, null, 8 /* PROPS */, _hoisted_5)]), _createElementVNode(\"div\", _hoisted_6, [_createElementVNode(\"div\", _hoisted_7, _toDisplayString(_ctx.$t('msg.yqhylqyj')), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_8, null, 512 /* NEED_PATCH */)]), _createElementVNode(\"div\", {\n    class: \"cop\",\n    onClick: _cache[2] || (_cache[2] = function ($event) {\n      var _$setup$info2;\n      return $setup.copy((_$setup$info2 = $setup.info) === null || _$setup$info2 === void 0 ? void 0 : _$setup$info2.url);\n    })\n  }, _toDisplayString(_ctx.$t('msg.fzyqlj')), 1 /* TEXT */)])]), _createCommentVNode(\"  \\r\\n        <div class=\\\"yqhy\\\">{{$t('msg.yqhylqyj')}}</div>\\r\\n        <div class=\\\"content\\\">\\r\\n            <div class=\\\"top\\\">\\r\\n                <div class=\\\"title\\\">\\r\\n                    <img :src=\\\"require('@/assets/images/news/money.png')\\\" alt=\\\"\\\" class=\\\"img\\\" width=\\\"20\\\">\\r\\n                    <img :src=\\\"require('@/assets/images/news/money.png')\\\" alt=\\\"\\\" class=\\\"img\\\" width=\\\"20\\\">\\r\\n                    <img :src=\\\"require('@/assets/images/news/money.png')\\\" alt=\\\"\\\" class=\\\"img\\\" width=\\\"20\\\">\\r\\n                </div>\\r\\n                <div class=\\\"c\\\" v-html=\\\"a_content\\\"></div>\\r\\n                <div class=\\\"b\\\">\\r\\n                    {{$t('msg.tgm')}} <span class=\\\"span\\\">{{info.invite_code}}</span>\\r\\n                </div>\\r\\n                <div class=\\\"bottom\\\" @click=\\\"copy(info?.url)\\\">{{$t('msg.fzyqlj')}}</div>\\r\\n            </div>\\r\\n        </div>\\r\\n\\t\\t\")]);\n}", "map": {"version": 3, "names": ["class", "ref", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_van_nav_bar", "title", "_ctx", "$t", "background", "onClickLeft", "_cache", "$event", "$router", "go", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_toDisplayString", "$setup", "info", "invite_code", "src", "require", "alt", "onClick", "_$setup$info", "copy", "url", "_hoisted_6", "_hoisted_7", "_hoisted_8", "_$setup$info2", "_createCommentVNode"], "sources": ["C:\\Users\\<USER>\\Downloads\\Telegram Desktop\\vue3_3.0\\vue3_3.0\\src\\views\\self\\components\\share.vue"], "sourcesContent": ["<template>\r\n    <div class=\"tel home\">\r\n\t\t<van-nav-bar\r\n\t\t     :title=\"$t('msg.code')\"\r\n\t\t\t background=\"#ffffff\"\r\n\t\t\t title-style=\"color:black; font-size: 16px;\"\r\n\t\t\t left-arrow \r\n\t\t\t @click-left=\"$router.go(-1)\"\r\n\t\t>\r\n\t\t</van-nav-bar>\r\n\t\t<div class=\"box\">\r\n\t\t\t<div class=\"box_t\">\r\n\t\t\t\t<div class=\"box_tlt\">\r\n\t\t\t\t\t{{$t('msg.code')}} :&nbsp;<span>{{info.invite_code}}</span><img  :src=\"require('@/assets/images/copy.svg')\" alt=\"\" @click=\"copy(info?.url)\">\r\n                    \r\n\t\t\t\t</div>\r\n\t\t\t\t<div class=\"box_fot\">\r\n\t\t\t\t\t<div class=\"box_text\">{{$t('msg.yqhylqyj')}}</div>\r\n\t\t\t\t\t<div class=\"qr-code qr\" ref=\"qrCodeUrl\"></div>\r\n\t\t\t\t</div>\r\n\r\n \r\n\r\n\t\t\t\t<div class=\"cop\" @click=\"copy(info?.url)\">\r\n\t\t\t\t\t{{$t('msg.fzyqlj')}}\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\t\t\r\n\t\t\r\n\t\t\r\n\t\t\r\n\t\t\r\n\t\t\r\n\t\t<!--  \r\n        <div class=\"yqhy\">{{$t('msg.yqhylqyj')}}</div>\r\n        <div class=\"content\">\r\n            <div class=\"top\">\r\n                <div class=\"title\">\r\n                    <img :src=\"require('@/assets/images/news/money.png')\" alt=\"\" class=\"img\" width=\"20\">\r\n                    <img :src=\"require('@/assets/images/news/money.png')\" alt=\"\" class=\"img\" width=\"20\">\r\n                    <img :src=\"require('@/assets/images/news/money.png')\" alt=\"\" class=\"img\" width=\"20\">\r\n                </div>\r\n                <div class=\"c\" v-html=\"a_content\"></div>\r\n                <div class=\"b\">\r\n                    {{$t('msg.tgm')}} <span class=\"span\">{{info.invite_code}}</span>\r\n                </div>\r\n                <div class=\"bottom\" @click=\"copy(info?.url)\">{{$t('msg.fzyqlj')}}</div>\r\n            </div>\r\n        </div>\r\n\t\t-->\r\n    </div> \r\n</template>\r\n<script>\r\nimport { ref, getCurrentInstance } from 'vue';\r\nimport { get_invite } from '@/api/self/index'\r\nimport { useRouter } from 'vue-router';\r\nimport { useI18n } from 'vue-i18n'\r\nimport store from '@/store/index'\r\nimport {formatTime} from '@/api/format.js'\r\n\r\nimport QRCode from 'qrcodejs2';\r\n// import {getdetailbyid} from '@/api/home/<USER>'\r\n// 复制函数\r\nimport useClipboard from 'vue-clipboard3';\r\nvar url = '';\r\nexport default {\r\n    name: 'ShareInvite',\r\n    setup(props, ctx){\r\n        const {proxy} = getCurrentInstance()\r\n        const { t } = useI18n()\r\n        const { toClipboard } = useClipboard();\r\n        const { push } = useRouter();\r\n        const a_content = ref('')\r\n        const info = ref({})\r\n        ctx.emit('hideFooter',true)\r\n        const clickLeft = () => {\r\n            push('/self')\r\n        }\r\n        get_invite().then(res => {\r\n           \r\n            info.value = {...res.data}\r\n            console.log(info,111111)\r\n            a_content.value = res.data?.invite_msg\r\n            \r\n            // 确保URL包含完整网址\r\n            const baseUrl = window.location.origin;\r\n            if (info.value.url && !info.value.url.startsWith('http')) {\r\n                info.value.url = baseUrl + '/register?invite_code=' + info.value.invite_code;\r\n            }\r\n            url = info.value.url;\r\n        })\r\n        // getdetailbyid(1).then(res => {\r\n        // })\r\n        const copy = (value) => {\r\n            try {\r\n                // 确保复制的链接包含完整网址\r\n                const baseUrl = window.location.origin;\r\n                let copyValue = value;\r\n                if (value && !value.startsWith('http')) {\r\n                    copyValue = baseUrl + '/register?invite_code=' + info.value.invite_code;\r\n                }\r\n                toClipboard(copyValue);\r\n                proxy.$Message({ type: 'success', message:t('msg.copy_s')});\r\n            } catch (e) {\r\n                proxy.$Message({ type: 'error', message:t('msg.copy_b')});\r\n            }\r\n        }\r\n\r\n        // new QRCode(this.$refs.qrCodeUrl, {\r\n        //     text: '#qrcode', // 需要转换为二维码的内容\r\n        //     width: 100,\r\n        //     height: 100,\r\n        //     colorDark: '#000000',\r\n        //     colorLight: '#ffffff',\r\n        //     correctLevel: QRCode.CorrectLevel.H\r\n        // })\r\n\r\n        \r\n\r\n        return {clickLeft,a_content,copy,info}\r\n    },\r\n\r\n    mounted() {\r\n        let taht = this\r\n        let dsq = setInterval(function(){\r\n            if(url !=''){\r\n                // 确保二维码链接包含完整网址\r\n                let qrUrl = url;\r\n                if (!qrUrl.startsWith('http')) {\r\n                    const baseUrl = window.location.origin;\r\n                    qrUrl = baseUrl + '/register?invite_code=' + taht.info.invite_code;\r\n                }\r\n                \r\n                new QRCode(taht.$refs.qrCodeUrl, {\r\n                    text: qrUrl, // 二维码的内容\r\n                    width: 150,\r\n                    height: 150,\r\n                    colorDark: '#000',\r\n                    colorLight: '#fff',\r\n                    correctLevel: QRCode.CorrectLevel.H\r\n                });\r\n                clearInterval(dsq)\r\n            }\r\n        },500)\r\n        \r\n        \r\n    }\r\n\r\n}\r\n\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\r\n.qr img{\r\n    width: 100px !important;\r\n    height: 100px !important;\r\n}\r\n\t.box_tlt{\r\n\t\theight: 110px !important;\r\n\t\tpadding-left: 30px;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: left;\r\n\t\tcolor: #ffffff;\r\n\t\timg{\r\n\t\t\twidth: 40px;\r\n\t\t\theight: 40px;\r\n\t\t\tmargin-left: 30px;\r\n\t\t}\r\n\t}\r\n.qr{\r\n\tmargin: 25px 0;\r\n\twidth: 400px;\r\n\theight: 400px;\r\n\tbackground-image: url(\"~@/assets/images/home/<USER>\");\r\n\tbackground-size: cover;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n}\r\n.box_text{\r\n\tdisplay: block;\r\n\tfont-size: 25px;\r\n\tfont-weight: 800;\r\n\tpadding: 50px 20px;\r\n}\r\n.cop{\r\n\tmargin: 0 0 15px;\r\n\t    width: 70%;\r\n\t    height: 70px;\r\n\t    background-color: #000;\r\n\t    color: #fff;\r\n\t    border-radius: 20px;\r\n\t    display: flex;\r\n\t    align-items: center;\r\n\t    justify-content: center;\r\n\t\tmargin: 50px auto;\r\n}\r\n.box{\r\n\tdisplay: block;\r\n\tbox-sizing: border-box;\r\n\twidth: 100%;\r\n\tpadding: 15px;\r\n\t.box_t{\r\n\t\twidth: 88%;\r\n\t\theight: auto;\r\n\t\tbackground-color: #fff;\r\n\t\tborder-radius: 25px;\r\n\t\toverflow: hidden;\r\n\t\tbox-shadow: rgba(41,5,5,.15) 0 0 20px;\r\n\t\toverflow: hidden;\r\n\t\tmargin:  0 auto;\r\n\t\t.box_tlt{\r\n\t\t\twidth: 100%;\r\n\t\t\theight: 60px;\r\n\t\t\tbackground-color: #000;\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tcolor: #fff;\r\n\t\t}\r\n\t\t.box_fot{\r\n\t\t\tpadding: 0 20px;\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: space-around;\r\n\t\t\tflex-direction: column;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.tel{\r\n    overflow: hidden;\r\n    // background-image: url(\"~@/assets/images/home/<USER>\");\r\n    // background-size: 100% 100%;\r\n    position: relative;\r\n\tbackground-color: #f5f5f5;\r\n    .yqhy {\r\n        height: 400px;\r\n        width: 100%;\r\n        background-color: $theme;\r\n        display: flex;\r\n        flex-direction: column;\r\n        justify-content: center;\r\n        color: #fff;\r\n        font-size: 50px;\r\n        padding-top: 60px;\r\n    }\r\n    :deep(.van-nav-bar){\r\n\t\tpadding: 0;\r\n\t\tbackground-color: #ffffff;\r\n        position: absolute !important;\r\n        width: 100%;\r\n        background-color: inherit;\r\n        color: #000000;\r\n        z-index: 0;\r\n\t\t.van-nav-bar__content{\r\n\t\t\tbackground-color: #ffffff;\r\n\t\t}\r\n\t\t.van-nav-bar__left{\r\n\t\t\t\r\n\t\t\t.van-icon{\r\n\t\t\t\tcolor: #000000 !important;\r\n\t\t\t}\r\n\t\t}\r\n        .van-nav-bar__left{\r\n            .van-icon{\r\n                color: #fff;\r\n            }\r\n        }\r\n        .van-nav-bar__title{\r\n            color: #060606;\r\n        }\r\n        .van-nav-bar__right{\r\n            .van-icon{\r\n                color: #fff;\r\n            }\r\n        }\r\n    }\r\n    .content{\r\n        width: 100%;\r\n        padding: 0 25px;\r\n        border-radius: 30px;\r\n        flex: 1;\r\n        overflow: auto;\r\n        // background-color: #fff;\r\n        .bottom{\r\n            width: 100%;\r\n            height: 74px;\r\n            line-height: 74px;\r\n            font-size: 32px;\r\n            background-color: $theme;\r\n            color: #fff;\r\n            border-radius: 6px;\r\n            margin-top: 20px;\r\n        }\r\n        .top{\r\n            padding:20px;\r\n            background-color: #fff;\r\n            border-radius: 20px;\r\n            .title{\r\n                margin-bottom: 20px;\r\n            }\r\n            .c{\r\n                font-size: 22px;\r\n                line-height: 2;\r\n                text-indent: 2em;\r\n                color: #666;\r\n                margin-bottom: 20px;\r\n            }\r\n            .b{\r\n                width: 290px;\r\n                margin: 0 auto;\r\n                font-size: 26px;\r\n                color: #333;\r\n                position: relative;\r\n                .span{\r\n                    margin-left: 5px;\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n</style>"], "mappings": ";;;;;EACSA,KAAK,EAAC;AAAU;;EASlBA,KAAK,EAAC;AAAK;;EACVA,KAAK,EAAC;AAAO;;EACZA,KAAK,EAAC;AAAS;;;EAIfA,KAAK,EAAC;AAAS;;EACdA,KAAK,EAAC;AAAU;;EAChBA,KAAK,EAAC,YAAY;EAACC,GAAG,EAAC;;;;uBAjB7BC,mBAAA,CAkDM,OAlDNC,UAkDM,GAjDRC,YAAA,CAOcC,sBAAA;IANRC,KAAK,EAAEC,IAAA,CAAAC,EAAE;IACbC,UAAU,EAAC,SAAS;IACpB,aAAW,EAAC,+BAA+B;IAC3C,YAAU,EAAV,EAAU;IACTC,WAAU,EAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAAEL,IAAA,CAAAM,OAAO,CAACC,EAAE;IAAA;sCAGzBC,mBAAA,CAiBM,OAjBNC,UAiBM,GAhBLD,mBAAA,CAeM,OAfNE,UAeM,GAdLF,mBAAA,CAGM,OAHNG,UAGM,G,kCAFHX,IAAA,CAAAC,EAAE,gBAAc,KAAQ,iBAAAO,mBAAA,CAAiC,cAAAI,gBAAA,CAAzBC,MAAA,CAAAC,IAAI,CAACC,WAAW,kBAASP,mBAAA,CAAiF;IAA1EQ,GAAG,EAAEC,OAAO;IAA8BC,GAAG,EAAC,EAAE;IAAEC,OAAK,EAAAf,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,IAAAe,YAAA;MAAA,OAAEP,MAAA,CAAAQ,IAAI,EAAAD,YAAA,GAACP,MAAA,CAAAC,IAAI,cAAAM,YAAA,uBAAJA,YAAA,CAAME,GAAG;IAAA;yCAG1Id,mBAAA,CAGM,OAHNe,UAGM,GAFLf,mBAAA,CAAkD,OAAlDgB,UAAkD,EAAAZ,gBAAA,CAA1BZ,IAAA,CAAAC,EAAE,kCAC1BO,mBAAA,CAA8C,OAA9CiB,UAA8C,8B,GAK/CjB,mBAAA,CAEM;IAFDf,KAAK,EAAC,KAAK;IAAE0B,OAAK,EAAAf,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,IAAAqB,aAAA;MAAA,OAAEb,MAAA,CAAAQ,IAAI,EAAAK,aAAA,GAACb,MAAA,CAAAC,IAAI,cAAAY,aAAA,uBAAJA,aAAA,CAAMJ,GAAG;IAAA;sBACpCtB,IAAA,CAAAC,EAAE,+B,KAUP0B,mBAAA,+4BAgBG,C"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}