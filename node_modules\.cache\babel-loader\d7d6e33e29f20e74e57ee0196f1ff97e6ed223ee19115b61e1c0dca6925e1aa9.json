{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _Popover from \"./Popover.mjs\";\nvar Popover = withInstall(_Popover);\nvar stdin_default = Popover;\nexport { Popover, stdin_default as default };", "map": {"version": 3, "names": ["withInstall", "_Popover", "Popover", "stdin_default", "default"], "sources": ["C:/Users/<USER>/Downloads/Telegram Desktop/vue3_3.0/vue3_3.0/node_modules/vant/es/popover/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _Popover from \"./Popover.mjs\";\nconst Popover = withInstall(_Popover);\nvar stdin_default = Popover;\nexport {\n  Popover,\n  stdin_default as default\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,QAAQ,MAAM,eAAe;AACpC,IAAMC,OAAO,GAAGF,WAAW,CAACC,QAAQ,CAAC;AACrC,IAAIE,aAAa,GAAGD,OAAO;AAC3B,SACEA,OAAO,EACPC,aAAa,IAAIC,OAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}