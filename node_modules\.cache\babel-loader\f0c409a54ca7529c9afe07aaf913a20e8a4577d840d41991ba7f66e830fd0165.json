{"ast": null, "code": "function _createForOfIteratorHelper(o, allowArrayLike) { var it = typeof Symbol !== \"undefined\" && o[Symbol.iterator] || o[\"@@iterator\"]; if (!it) { if (Array.isArray(o) || (it = _unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === \"number\") { if (it) o = it; var i = 0; var F = function F() {}; return { s: F, n: function n() { if (i >= o.length) return { done: true }; return { done: false, value: o[i++] }; }, e: function e(_e) { throw _e; }, f: F }; } throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); } var normalCompletion = true, didErr = false, err; return { s: function s() { it = it.call(o); }, n: function n() { var step = it.next(); normalCompletion = step.done; return step; }, e: function e(_e2) { didErr = true; err = _e2; }, f: function f() { try { if (!normalCompletion && it.return != null) it.return(); } finally { if (didErr) throw err; } } }; }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nfunction _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __export = function __export(target, all) {\n  for (var name in all) __defProp(target, name, {\n    get: all[name],\n    enumerable: true\n  });\n};\nvar __copyProps = function __copyProps(to, from, except, desc) {\n  if (from && _typeof(from) === \"object\" || typeof from === \"function\") {\n    var _iterator = _createForOfIteratorHelper(__getOwnPropNames(from)),\n      _step;\n    try {\n      var _loop = function _loop() {\n        var key = _step.value;\n        if (!__hasOwnProp.call(to, key) && key !== except) __defProp(to, key, {\n          get: function get() {\n            return from[key];\n          },\n          enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable\n        });\n      };\n      for (_iterator.s(); !(_step = _iterator.n()).done;) {\n        _loop();\n      }\n    } catch (err) {\n      _iterator.e(err);\n    } finally {\n      _iterator.f();\n    }\n  }\n  return to;\n};\nvar __toCommonJS = function __toCommonJS(mod) {\n  return __copyProps(__defProp({}, \"__esModule\", {\n    value: true\n  }), mod);\n};\nvar stdin_exports = {};\n__export(stdin_exports, {\n  default: function _default() {\n    return stdin_default;\n  }\n});\nmodule.exports = __toCommonJS(stdin_exports);\nvar stdin_default = {\n  name: \"\\u0E0A\\u0E37\\u0E48\\u0E2D\",\n  tel: \"\\u0E42\\u0E17\\u0E23\\u0E28\\u0E31\\u0E1E\\u0E17\\u0E4C\",\n  save: \"\\u0E1A\\u0E31\\u0E19\\u0E17\\u0E36\\u0E01\",\n  confirm: \"\\u0E22\\u0E37\\u0E19\\u0E22\\u0E31\\u0E19\",\n  cancel: \"\\u0E22\\u0E01\\u0E40\\u0E25\\u0E34\\u0E01\",\n  delete: \"\\u0E25\\u0E1A\",\n  loading: \"\\u0E01\\u0E33\\u0E25\\u0E31\\u0E07\\u0E42\\u0E2B\\u0E25\\u0E14...\",\n  noCoupon: \"\\u0E44\\u0E21\\u0E48\\u0E21\\u0E35\\u0E04\\u0E39\\u0E1B\\u0E2D\\u0E07\",\n  nameEmpty: \"\\u0E01\\u0E23\\u0E38\\u0E13\\u0E32\\u0E01\\u0E23\\u0E2D\\u0E01\\u0E0A\\u0E37\\u0E48\\u0E2D\\u0E02\\u0E2D\\u0E07\\u0E04\\u0E38\\u0E13\",\n  addContact: \"\\u0E40\\u0E1E\\u0E34\\u0E48\\u0E21\\u0E1C\\u0E39\\u0E49\\u0E15\\u0E34\\u0E14\\u0E15\\u0E48\\u0E2D\",\n  telInvalid: \"\\u0E01\\u0E23\\u0E38\\u0E13\\u0E32\\u0E01\\u0E23\\u0E2D\\u0E01\\u0E2B\\u0E21\\u0E32\\u0E22\\u0E40\\u0E25\\u0E02\\u0E42\\u0E17\\u0E23\\u0E28\\u0E31\\u0E1E\\u0E17\\u0E4C\\u0E17\\u0E35\\u0E48\\u0E16\\u0E39\\u0E01\\u0E15\\u0E49\\u0E2D\\u0E07\",\n  vanCalendar: {\n    end: \"\\u0E08\\u0E1A\",\n    start: \"\\u0E40\\u0E23\\u0E34\\u0E48\\u0E21\",\n    title: \"\\u0E01\\u0E32\\u0E23\\u0E40\\u0E25\\u0E37\\u0E2D\\u0E01\\u0E27\\u0E31\\u0E19\\u0E17\\u0E35\\u0E48\",\n    weekdays: [\"\\u0E2D\\u0E32\", \"\\u0E08\", \"\\u0E2D\", \"\\u0E1E\", \"\\u0E1E\\u0E24\", \"\\u0E28\", \"\\u0E2A\"],\n    monthTitle: function monthTitle(year, month) {\n      return \"\".concat(year, \"\\u0E1B\\u0E35\").concat(month, \"\\u0E40\\u0E14\\u0E37\\u0E2D\\u0E19\");\n    },\n    rangePrompt: function rangePrompt(maxRange) {\n      return \"\\u0E08\\u0E33\\u0E19\\u0E27\\u0E19\\u0E27\\u0E31\\u0E19\\u0E17\\u0E35\\u0E48\\u0E40\\u0E25\\u0E37\\u0E2D\\u0E01\\u0E15\\u0E49\\u0E2D\\u0E07\\u0E44\\u0E21\\u0E48\\u0E40\\u0E01\\u0E34\\u0E19 \".concat(maxRange, \" \\u0E27\\u0E31\\u0E19\");\n    }\n  },\n  vanCascader: {\n    select: \"\\u0E42\\u0E1B\\u0E23\\u0E14\\u0E40\\u0E25\\u0E37\\u0E2D\\u0E01\"\n  },\n  vanPagination: {\n    prev: \"\\u0E2B\\u0E19\\u0E49\\u0E32\\u0E17\\u0E35\\u0E48\\u0E41\\u0E25\\u0E49\\u0E27\",\n    next: \"\\u0E2B\\u0E19\\u0E49\\u0E32\\u0E15\\u0E48\\u0E2D\\u0E44\\u0E1B\"\n  },\n  vanPullRefresh: {\n    pulling: \"\\u0E14\\u0E36\\u0E07\\u0E25\\u0E07\\u0E40\\u0E1E\\u0E37\\u0E48\\u0E2D\\u0E23\\u0E35\\u0E40\\u0E1F\\u0E23\\u0E0A...\",\n    loosing: \"\\u0E1B\\u0E25\\u0E48\\u0E2D\\u0E22\\u0E40\\u0E1E\\u0E37\\u0E48\\u0E2D\\u0E23\\u0E35\\u0E40\\u0E1F\\u0E23\\u0E0A...\"\n  },\n  vanSubmitBar: {\n    label: \"\\u0E23\\u0E27\\u0E21:\"\n  },\n  vanCoupon: {\n    unlimited: \"\\u0E44\\u0E21\\u0E48 \\u0E08\\u0E33\\u0E01\\u0E31\\u0E14\",\n    discount: function discount(_discount) {\n      return \"\\u0E25\\u0E14\".concat(_discount);\n    },\n    condition: function condition(_condition) {\n      return \"\\u0E21\\u0E35\\u0E08\\u0E33\\u0E2B\\u0E19\\u0E48\\u0E32\\u0E22\\u0E43\\u0E19\\u0E23\\u0E32\\u0E04\\u0E32 \".concat(_condition, \" \\u0E01\\u0E27\\u0E48\\u0E32\\u0E2B\\u0E22\\u0E27\\u0E19\");\n    }\n  },\n  vanCouponCell: {\n    title: \"\\u0E04\\u0E39\\u0E1B\\u0E2D\\u0E07\",\n    count: function count(_count) {\n      return \"\\u0E21\\u0E35\\u0E23\\u0E39\\u0E1B\\u0E20\\u0E32\\u0E1E \".concat(_count, \" \\u0E23\\u0E39\\u0E1B\");\n    }\n  },\n  vanCouponList: {\n    exchange: \"\\u0E41\\u0E25\\u0E01\\u0E40\\u0E1B\\u0E25\\u0E35\\u0E48\\u0E22\\u0E19\",\n    close: \"\\u0E44\\u0E21\\u0E48\\u0E44\\u0E14\\u0E49\\u0E43\\u0E0A\\u0E49\",\n    enable: \"\\u0E1E\\u0E23\\u0E49\\u0E2D\\u0E21\\u0E43\\u0E0A\\u0E49\\u0E07\\u0E32\\u0E19\",\n    disabled: \"\\u0E44\\u0E21\\u0E48\\u0E1E\\u0E23\\u0E49\\u0E2D\\u0E21\\u0E43\\u0E0A\\u0E49\\u0E07\\u0E32\\u0E19\",\n    placeholder: \"\\u0E01\\u0E23\\u0E38\\u0E13\\u0E32\\u0E01\\u0E23\\u0E2D\\u0E01\\u0E23\\u0E2B\\u0E31\\u0E2A\\u0E04\\u0E39\\u0E1B\\u0E2D\\u0E07\"\n  },\n  vanAddressEdit: {\n    area: \"\\u0E1E\\u0E37\\u0E49\\u0E19\\u0E17\\u0E35\\u0E48\",\n    postal: \"\\u0E23\\u0E2B\\u0E31\\u0E2A\\u0E44\\u0E1B\\u0E23\\u0E29\\u0E13\\u0E35\\u0E22\\u0E4C\",\n    areaEmpty: \"\\u0E42\\u0E1B\\u0E23\\u0E14\\u0E40\\u0E25\\u0E37\\u0E2D\\u0E01\\u0E20\\u0E39\\u0E21\\u0E34\\u0E20\\u0E32\\u0E04\",\n    addressEmpty: \"\\u0E01\\u0E23\\u0E38\\u0E13\\u0E32\\u0E01\\u0E23\\u0E2D\\u0E01\\u0E17\\u0E35\\u0E48\\u0E2D\\u0E22\\u0E39\\u0E48\\u0E42\\u0E14\\u0E22\\u0E25\\u0E30\\u0E40\\u0E2D\\u0E35\\u0E22\\u0E14\",\n    postalEmpty: \"\\u0E23\\u0E39\\u0E1B\\u0E41\\u0E1A\\u0E1A\\u0E23\\u0E2B\\u0E31\\u0E2A\\u0E44\\u0E1B\\u0E23\\u0E29\\u0E13\\u0E35\\u0E22\\u0E4C\\u0E44\\u0E21\\u0E48\\u0E16\\u0E39\\u0E01\\u0E15\\u0E49\\u0E2D\\u0E07\",\n    addressDetail: \"\\u0E17\\u0E35\\u0E48\\u0E2D\\u0E22\\u0E39\\u0E48\",\n    defaultAddress: \"\\u0E15\\u0E31\\u0E49\\u0E07\\u0E40\\u0E1B\\u0E47\\u0E19\\u0E17\\u0E35\\u0E48\\u0E2D\\u0E22\\u0E39\\u0E48\\u0E08\\u0E31\\u0E14\\u0E2A\\u0E48\\u0E07\\u0E40\\u0E23\\u0E34\\u0E48\\u0E21\\u0E15\\u0E49\\u0E19\"\n  },\n  vanAddressList: {\n    add: \"\\u0E40\\u0E1E\\u0E34\\u0E48\\u0E21\\u0E17\\u0E35\\u0E48\\u0E2D\\u0E22\\u0E39\\u0E48\"\n  }\n};", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}