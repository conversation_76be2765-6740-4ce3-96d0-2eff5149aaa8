{"ast": null, "code": "function _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return _typeof(key) === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (_typeof(input) !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (_typeof(res) !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\nfunction _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\nfunction _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nfunction _iterableToArrayLimit(arr, i) { var _i = null == arr ? null : \"undefined\" != typeof Symbol && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (null != _i) { var _s, _e, _x, _r, _arr = [], _n = !0, _d = !1; try { if (_x = (_i = _i.call(arr)).next, 0 === i) { if (Object(_i) !== _i) return; _n = !1; } else for (; !(_n = (_s = _x.call(_i)).done) && (_arr.push(_s.value), _arr.length !== i); _n = !0); } catch (err) { _d = !0, _e = err; } finally { try { if (!_n && null != _i.return && (_r = _i.return(), Object(_r) !== _r)) return; } finally { if (_d) throw _e; } } return _arr; } }\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\nimport { createVNode as _createVNode } from \"vue\";\nimport { ref, watch, computed, defineComponent } from \"vue\";\nimport { extend, unitToPx, truthProp, makeArrayProp, preventDefault, makeStringProp, makeNumericProp, createNamespace, HAPTICS_FEEDBACK, BORDER_UNSET_TOP_BOTTOM } from \"../utils/index.mjs\";\nimport { useChildren, useEventListener } from \"@vant/use\";\nimport { useExpose } from \"../composables/use-expose.mjs\";\nimport { Loading } from \"../loading/index.mjs\";\nimport Column, { PICKER_KEY } from \"./PickerColumn.mjs\";\nvar _createNamespace = createNamespace(\"picker\"),\n  _createNamespace2 = _slicedToArray(_createNamespace, 3),\n  name = _createNamespace2[0],\n  bem = _createNamespace2[1],\n  t = _createNamespace2[2];\nvar pickerSharedProps = {\n  title: String,\n  loading: Boolean,\n  readonly: Boolean,\n  allowHtml: Boolean,\n  itemHeight: makeNumericProp(44),\n  showToolbar: truthProp,\n  swipeDuration: makeNumericProp(1e3),\n  visibleItemCount: makeNumericProp(6),\n  cancelButtonText: String,\n  confirmButtonText: String\n};\nvar pickerProps = extend({}, pickerSharedProps, {\n  columns: makeArrayProp(),\n  valueKey: String,\n  defaultIndex: makeNumericProp(0),\n  toolbarPosition: makeStringProp(\"top\"),\n  columnsFieldNames: Object\n});\nvar stdin_default = defineComponent({\n  name: name,\n  props: pickerProps,\n  emits: [\"confirm\", \"cancel\", \"change\"],\n  setup: function setup(props, _ref) {\n    var emit = _ref.emit,\n      slots = _ref.slots;\n    if (process.env.NODE_ENV !== \"production\") {\n      if (slots.default) {}\n      if (props.valueKey) {}\n    }\n    var hasOptions = ref(false);\n    var columnsRef = ref();\n    var formattedColumns = ref([]);\n    var columnsFieldNames = computed(function () {\n      var columnsFieldNames2 = props.columnsFieldNames;\n      return {\n        text: (columnsFieldNames2 == null ? void 0 : columnsFieldNames2.text) || props.valueKey || \"text\",\n        values: (columnsFieldNames2 == null ? void 0 : columnsFieldNames2.values) || \"values\",\n        children: (columnsFieldNames2 == null ? void 0 : columnsFieldNames2.children) || \"children\"\n      };\n    });\n    var _useChildren = useChildren(PICKER_KEY),\n      children = _useChildren.children,\n      linkChildren = _useChildren.linkChildren;\n    linkChildren();\n    var itemHeight = computed(function () {\n      return unitToPx(props.itemHeight);\n    });\n    var dataType = computed(function () {\n      var firstColumn = props.columns[0];\n      if (_typeof(firstColumn) === \"object\") {\n        if (columnsFieldNames.value.children in firstColumn) {\n          return \"cascade\";\n        }\n        if (columnsFieldNames.value.values in firstColumn) {\n          return \"object\";\n        }\n      }\n      return \"plain\";\n    });\n    var formatCascade = function formatCascade() {\n      var _a;\n      var formatted = [];\n      var cursor = _defineProperty({}, columnsFieldNames.value.children, props.columns);\n      while (cursor && cursor[columnsFieldNames.value.children]) {\n        var _formatted$push;\n        var children2 = cursor[columnsFieldNames.value.children];\n        var defaultIndex = (_a = cursor.defaultIndex) != null ? _a : +props.defaultIndex;\n        while (children2[defaultIndex] && children2[defaultIndex].disabled) {\n          if (defaultIndex < children2.length - 1) {\n            defaultIndex++;\n          } else {\n            defaultIndex = 0;\n            break;\n          }\n        }\n        formatted.push((_formatted$push = {}, _defineProperty(_formatted$push, columnsFieldNames.value.values, cursor[columnsFieldNames.value.children]), _defineProperty(_formatted$push, \"className\", cursor.className), _defineProperty(_formatted$push, \"defaultIndex\", defaultIndex), _formatted$push));\n        cursor = children2[defaultIndex];\n      }\n      formattedColumns.value = formatted;\n    };\n    var format = function format() {\n      var columns = props.columns;\n      if (dataType.value === \"plain\") {\n        formattedColumns.value = [_defineProperty({}, columnsFieldNames.value.values, columns)];\n      } else if (dataType.value === \"cascade\") {\n        formatCascade();\n      } else {\n        formattedColumns.value = columns;\n      }\n      hasOptions.value = formattedColumns.value.some(function (item) {\n        return item[columnsFieldNames.value.values] && item[columnsFieldNames.value.values].length !== 0;\n      }) || children.some(function (item) {\n        return item.hasOptions;\n      });\n    };\n    var getIndexes = function getIndexes() {\n      return children.map(function (child) {\n        return child.state.index;\n      });\n    };\n    var setColumnValues = function setColumnValues(index, options) {\n      var column = children[index];\n      if (column) {\n        column.setOptions(options);\n        hasOptions.value = true;\n      }\n    };\n    var onCascadeChange = function onCascadeChange(columnIndex) {\n      var cursor = _defineProperty({}, columnsFieldNames.value.children, props.columns);\n      var indexes = getIndexes();\n      for (var i = 0; i <= columnIndex; i++) {\n        cursor = cursor[columnsFieldNames.value.children][indexes[i]];\n      }\n      while (cursor && cursor[columnsFieldNames.value.children]) {\n        columnIndex++;\n        setColumnValues(columnIndex, cursor[columnsFieldNames.value.children]);\n        cursor = cursor[columnsFieldNames.value.children][cursor.defaultIndex || 0];\n      }\n    };\n    var getChild = function getChild(index) {\n      return children[index];\n    };\n    var getColumnValue = function getColumnValue(index) {\n      var column = getChild(index);\n      if (column) {\n        return column.getValue();\n      }\n    };\n    var setColumnValue = function setColumnValue(index, value) {\n      var column = getChild(index);\n      if (column) {\n        column.setValue(value);\n        if (dataType.value === \"cascade\") {\n          onCascadeChange(index);\n        }\n      }\n    };\n    var getColumnIndex = function getColumnIndex(index) {\n      var column = getChild(index);\n      if (column) {\n        return column.state.index;\n      }\n    };\n    var setColumnIndex = function setColumnIndex(columnIndex, optionIndex) {\n      var column = getChild(columnIndex);\n      if (column) {\n        column.setIndex(optionIndex);\n        if (dataType.value === \"cascade\") {\n          onCascadeChange(columnIndex);\n        }\n      }\n    };\n    var getColumnValues = function getColumnValues(index) {\n      var column = getChild(index);\n      if (column) {\n        return column.state.options;\n      }\n    };\n    var getValues = function getValues() {\n      return children.map(function (child) {\n        return child.getValue();\n      });\n    };\n    var setValues = function setValues(values) {\n      values.forEach(function (value, index) {\n        setColumnValue(index, value);\n      });\n    };\n    var setIndexes = function setIndexes(indexes) {\n      indexes.forEach(function (optionIndex, columnIndex) {\n        setColumnIndex(columnIndex, optionIndex);\n      });\n    };\n    var emitAction = function emitAction(event) {\n      if (dataType.value === \"plain\") {\n        emit(event, getColumnValue(0), getColumnIndex(0));\n      } else {\n        emit(event, getValues(), getIndexes());\n      }\n    };\n    var _onChange = function onChange(columnIndex) {\n      if (dataType.value === \"cascade\") {\n        onCascadeChange(columnIndex);\n      }\n      if (dataType.value === \"plain\") {\n        emit(\"change\", getColumnValue(0), getColumnIndex(0));\n      } else {\n        emit(\"change\", getValues(), columnIndex);\n      }\n    };\n    var confirm = function confirm() {\n      children.forEach(function (child) {\n        return child.stopMomentum();\n      });\n      emitAction(\"confirm\");\n    };\n    var cancel = function cancel() {\n      return emitAction(\"cancel\");\n    };\n    var renderTitle = function renderTitle() {\n      if (slots.title) {\n        return slots.title();\n      }\n      if (props.title) {\n        return _createVNode(\"div\", {\n          \"class\": [bem(\"title\"), \"van-ellipsis\"]\n        }, [props.title]);\n      }\n    };\n    var renderCancel = function renderCancel() {\n      var text = props.cancelButtonText || t(\"cancel\");\n      return _createVNode(\"button\", {\n        \"type\": \"button\",\n        \"class\": [bem(\"cancel\"), HAPTICS_FEEDBACK],\n        \"onClick\": cancel\n      }, [slots.cancel ? slots.cancel() : text]);\n    };\n    var renderConfirm = function renderConfirm() {\n      var text = props.confirmButtonText || t(\"confirm\");\n      return _createVNode(\"button\", {\n        \"type\": \"button\",\n        \"class\": [bem(\"confirm\"), HAPTICS_FEEDBACK],\n        \"onClick\": confirm\n      }, [slots.confirm ? slots.confirm() : text]);\n    };\n    var renderToolbar = function renderToolbar() {\n      if (props.showToolbar) {\n        var slot = slots.toolbar || slots.default;\n        return _createVNode(\"div\", {\n          \"class\": bem(\"toolbar\")\n        }, [slot ? slot() : [renderCancel(), renderTitle(), renderConfirm()]]);\n      }\n    };\n    var renderColumnItems = function renderColumnItems() {\n      return formattedColumns.value.map(function (item, columnIndex) {\n        var _a;\n        return _createVNode(Column, {\n          \"textKey\": columnsFieldNames.value.text,\n          \"readonly\": props.readonly,\n          \"allowHtml\": props.allowHtml,\n          \"className\": item.className,\n          \"itemHeight\": itemHeight.value,\n          \"defaultIndex\": (_a = item.defaultIndex) != null ? _a : +props.defaultIndex,\n          \"swipeDuration\": props.swipeDuration,\n          \"initialOptions\": item[columnsFieldNames.value.values],\n          \"visibleItemCount\": props.visibleItemCount,\n          \"onChange\": function onChange() {\n            return _onChange(columnIndex);\n          }\n        }, {\n          option: slots.option\n        });\n      });\n    };\n    var renderMask = function renderMask(wrapHeight) {\n      if (hasOptions.value) {\n        var frameStyle = {\n          height: \"\".concat(itemHeight.value, \"px\")\n        };\n        var maskStyle = {\n          backgroundSize: \"100% \".concat((wrapHeight - itemHeight.value) / 2, \"px\")\n        };\n        return [_createVNode(\"div\", {\n          \"class\": bem(\"mask\"),\n          \"style\": maskStyle\n        }, null), _createVNode(\"div\", {\n          \"class\": [BORDER_UNSET_TOP_BOTTOM, bem(\"frame\")],\n          \"style\": frameStyle\n        }, null)];\n      }\n    };\n    var renderColumns = function renderColumns() {\n      var wrapHeight = itemHeight.value * +props.visibleItemCount;\n      var columnsStyle = {\n        height: \"\".concat(wrapHeight, \"px\")\n      };\n      return _createVNode(\"div\", {\n        \"ref\": columnsRef,\n        \"class\": bem(\"columns\"),\n        \"style\": columnsStyle\n      }, [renderColumnItems(), renderMask(wrapHeight)]);\n    };\n    watch(function () {\n      return props.columns;\n    }, format, {\n      immediate: true\n    });\n    useEventListener(\"touchmove\", preventDefault, {\n      target: columnsRef\n    });\n    useExpose({\n      confirm: confirm,\n      getValues: getValues,\n      setValues: setValues,\n      getIndexes: getIndexes,\n      setIndexes: setIndexes,\n      getColumnIndex: getColumnIndex,\n      setColumnIndex: setColumnIndex,\n      getColumnValue: getColumnValue,\n      setColumnValue: setColumnValue,\n      getColumnValues: getColumnValues,\n      setColumnValues: setColumnValues\n    });\n    return function () {\n      var _a, _b;\n      return _createVNode(\"div\", {\n        \"class\": bem()\n      }, [props.toolbarPosition === \"top\" ? renderToolbar() : null, props.loading ? _createVNode(Loading, {\n        \"class\": bem(\"loading\")\n      }, null) : null, (_a = slots[\"columns-top\"]) == null ? void 0 : _a.call(slots), renderColumns(), (_b = slots[\"columns-bottom\"]) == null ? void 0 : _b.call(slots), props.toolbarPosition === \"bottom\" ? renderToolbar() : null]);\n    };\n  }\n});\nexport { stdin_default as default, pickerSharedProps };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}