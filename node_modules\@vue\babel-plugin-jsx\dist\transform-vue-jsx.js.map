{"version": 3, "file": "transform-vue-jsx.js", "sourceRoot": "", "sources": ["../src/transform-vue-jsx.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAAA,gDAAkC;AAElC,wEAA0D;AAC1D,mCAeiB;AAGjB,wEAAgD;AAGhD,MAAM,OAAO,GAAG,eAAe,CAAC;AAIhC,MAAM,oBAAoB,GAAG,CAC3B,IAA8B,EAC9B,KAAY,EAGV,EAAE;IACJ,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;IACpC,IAAI,SAAS,CAAC,YAAY,EAAE,EAAE;QAC5B,OAAO,mBAAmB,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;KAC9C;IACD,IAAI,SAAS,CAAC,eAAe,EAAE,EAAE;QAC/B,OAAO,SAAS,CAAC,IAAI,CAAC;KACvB;IACD,IAAI,SAAS,CAAC,wBAAwB,EAAE,EAAE;QACxC,OAAO,IAAA,uCAA+B,EAAC,SAAS,CAAC,CAAC;KACnD;IAED,OAAO,IAAI,CAAC;AACd,CAAC,CAAC;AAEF,MAAM,UAAU,GAAG,CAAC,IAA4B,EAAE,KAAY,EAAE,EAAE;IAChE,MAAM,GAAG,GAAG,IAAA,cAAM,EAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IAChC,MAAM,WAAW,GAAG,IAAA,wBAAgB,EAAC,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC,EAAE,KAAK,CAAC,CAAC;IACxE,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;IAC3D,MAAM,UAAU,GAAwB,EAAE,CAAC;IAC3C,MAAM,gBAAgB,GAAG,IAAI,GAAG,EAAU,CAAC;IAE3C,IAAI,KAAK,GAAU,IAAI,CAAC;IACxB,IAAI,SAAS,GAAG,CAAC,CAAC;IAElB,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;QACtB,OAAO;YACL,GAAG;YACH,WAAW;YACX,KAAK;YACL,KAAK,EAAE,CAAC,CAAC,WAAW,EAAE;YACtB,UAAU;YACV,SAAS;YACT,gBAAgB;SACjB,CAAC;KACH;IAED,IAAI,UAAU,GAAuB,EAAE,CAAC;IAExC,qBAAqB;IACrB,IAAI,MAAM,GAAG,KAAK,CAAC;IACnB,IAAI,eAAe,GAAG,KAAK,CAAC;IAC5B,IAAI,eAAe,GAAG,KAAK,CAAC;IAC5B,IAAI,wBAAwB,GAAG,KAAK,CAAC;IACrC,IAAI,cAAc,GAAG,KAAK,CAAC;IAE3B,MAAM,SAAS,GAA6D,EAAE,CAAC;IAC/E,MAAM,EAAE,UAAU,GAAG,IAAI,EAAE,GAAG,KAAK,CAAC,IAAI,CAAC;IACzC,KAAK;SACF,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;QAChB,IAAI,IAAI,CAAC,cAAc,EAAE,EAAE;YACzB,IAAI,IAAI,GAAG,IAAA,2BAAmB,EAAC,IAAI,CAAC,CAAC;YAErC,MAAM,cAAc,GAAG,oBAAoB,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;YAEzD,IAAI,CAAC,IAAA,kBAAU,EAAC,cAAc,CAAC,IAAI,IAAI,KAAK,KAAK,EAAE;gBACjD,IACE,CAAC,WAAW;uBACT,IAAA,YAAI,EAAC,IAAI,CAAC;oBACb,iEAAiE;oBACjE,uBAAuB;uBACpB,IAAI,CAAC,WAAW,EAAE,KAAK,SAAS;oBACnC,wBAAwB;uBACrB,IAAI,KAAK,qBAAqB,EACjC;oBACA,wBAAwB,GAAG,IAAI,CAAC;iBACjC;gBAED,IAAI,IAAI,KAAK,KAAK,EAAE;oBAClB,MAAM,GAAG,IAAI,CAAC;iBACf;qBAAM,IAAI,IAAI,KAAK,OAAO,IAAI,CAAC,WAAW,EAAE;oBAC3C,eAAe,GAAG,IAAI,CAAC;iBACxB;qBAAM,IAAI,IAAI,KAAK,OAAO,IAAI,CAAC,WAAW,EAAE;oBAC3C,eAAe,GAAG,IAAI,CAAC;iBACxB;qBAAM,IACL,IAAI,KAAK,KAAK;uBACX,CAAC,IAAA,mBAAW,EAAC,IAAI,CAAC;uBAClB,IAAI,KAAK,IAAI,EAChB;oBACA,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;iBAC5B;aACF;YACD,IAAI,KAAK,CAAC,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,UAAU,CAAC,EAAE;gBACpE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,aAAa,CAAC,EAAE;oBAC7B,KAAK,CAAC,GAAG,CAAC,aAAa,EAAE,IAAA,kCAAU,EACjC,IAAI,EACJ,oCAAoC,EACpC,EAAE,QAAQ,EAAE,cAAc,EAAE,CAC7B,CAAC,CAAC;iBACJ;gBACD,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,cAAc,CAC7B,KAAK,CAAC,GAAG,CAAC,aAAa,CAAC,EACxB,CAAC,cAAc,IAAI,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAC3C,CAAC,CAAC;gBACH,OAAO;aACR;YACD,IAAI,IAAA,mBAAW,EAAC,IAAI,CAAC,EAAE;gBACrB,MAAM,EACJ,SAAS,EAAE,SAAS,EAAE,MAAM,EAAE,IAAI,EAAE,aAAa,GAClD,GAAG,IAAA,yBAAe,EAAC;oBAClB,GAAG;oBACH,WAAW;oBACX,IAAI;oBACJ,IAAI,EAAE,IAAI;oBACV,KAAK;oBACL,KAAK,EAAE,cAAc;iBACtB,CAAC,CAAC;gBAEH,IAAI,aAAa,KAAK,OAAO,EAAE;oBAC7B,KAAK,GAAG,cAAuB,CAAC;oBAChC,OAAO;iBACR;gBACD,IAAI,SAAS,EAAE;oBACb,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC,CAAC;iBAC/C;qBAAM,IAAI,aAAa,KAAK,MAAM,EAAE;oBACnC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,cAAc,CAC9B,CAAC,CAAC,aAAa,CAAC,WAAW,CAAC,EAC5B,MAAM,CAAC,CAAC,CAAQ,CACjB,CAAC,CAAC;oBACH,gBAAgB,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;iBACnC;qBAAM,IAAI,aAAa,KAAK,MAAM,EAAE;oBACnC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,cAAc,CAC9B,CAAC,CAAC,aAAa,CAAC,aAAa,CAAC,EAC9B,MAAM,CAAC,CAAC,CAAQ,CACjB,CAAC,CAAC;oBACH,gBAAgB,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;iBACrC;gBAED,IAAI,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,QAAQ,CAAC,aAAa,CAAC,EAAE;oBAC/C,MAAM,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE;;wBAC9B,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;wBAC7B,+BAA+B;wBAC/B,MAAM,SAAS,GAAG,QAAQ,IAAI,CAAC,CAAC,CAAC,eAAe,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;wBAEzF,iDAAiD;wBACjD,IAAI,CAAC,SAAS,EAAE;4BACd,UAAU,CAAC,IAAI,CACb,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,aAAa,CAAC,QAAQ,CAAC;gCACxC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE,KAAY,EAAE,SAAS,CAAC,CACvE,CAAC;4BACF,IAAI,CAAC,SAAS,EAAE;gCACd,gBAAgB,CAAC,GAAG,CAAC,CAAA,MAAC,QAA4B,0CAAE,KAAK,KAAI,YAAY,CAAC,CAAC;6BAC5E;4BAED,IAAI,MAAA,SAAS,CAAC,KAAK,CAAC,0CAAE,IAAI,EAAE;gCAC1B,UAAU,CAAC,IAAI,CACb,CAAC,CAAC,cAAc,CACd,SAAS;oCACP,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,GAAG,EAAE,QAAQ,EAAE,CAAC,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC;oCACjE,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,GAAG,CAAA,MAAC,QAA4B,0CAAE,KAAK,KAAI,OAAO,WAAW,CAAC,EAClF,CAAC,CAAC,gBAAgB,CAChB,CAAC,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC,CAAC,cAAc,CACtD,CAAC,CAAC,aAAa,CAAC,QAAQ,CAAC,EACzB,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CACvB,CAAC,CACH,EACD,SAAS,CACV,CACF,CAAC;6BACH;yBACF;wBAED,MAAM,UAAU,GAAG,SAAS;4BAC1B,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,GAAG,EAAE,CAAC,CAAC,aAAa,CAAC,UAAU,CAAC,EAAE,QAAQ,CAAC;4BAChE,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,YAAY,CAAA,MAAC,QAA4B,0CAAE,KAAK,KAAI,YAAY,EAAE,CAAC,CAAC;wBAExF,UAAU,CAAC,IAAI,CACb,CAAC,CAAC,cAAc,CACd,UAAU,EACV,CAAC,CAAC,uBAAuB,CACvB,CAAC,CAAC,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,EACxB,CAAC,CAAC,oBAAoB,CAAC,GAAG,EAAE,KAAY,EAAE,CAAC,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,CAClE,EACD,SAAS,CACV,CACF,CAAC;wBAEF,IAAI,CAAC,SAAS,EAAE;4BACd,gBAAgB,CAAC,GAAG,CAAE,UAA8B,CAAC,KAAK,CAAC,CAAC;yBAC7D;6BAAM;4BACL,cAAc,GAAG,IAAI,CAAC;yBACvB;oBACH,CAAC,CAAC,CAAC;iBACJ;aACF;iBAAM;gBACL,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;oBACvB,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,cAAc,EAAE,EAAE,CAAC,SAAS,cAAc,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;iBAC9F;gBACD,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,cAAc,CAC9B,CAAC,CAAC,aAAa,CAAC,IAAI,CAAC,EACrB,cAAc,IAAI,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CACzC,CAAC,CAAC;aACJ;SACF;aAAM;YACL,IAAI,UAAU,CAAC,MAAM,IAAI,UAAU,EAAE;gBACnC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,gBAAgB,CAAC,IAAA,wBAAgB,EAAC,UAAU,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC;gBAC7E,UAAU,GAAG,EAAE,CAAC;aACjB;YAED,qBAAqB;YACrB,cAAc,GAAG,IAAI,CAAC;YACtB,IAAA,mCAA2B,EACzB,IAAgB,EAChB,IAAsC,EACtC,UAAU,EACV,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,UAAU,CACpC,CAAC;SACH;IACH,CAAC,CAAC,CAAC;IAEL,qBAAqB;IACrB,IAAI,cAAc,EAAE;QAClB,SAAS,uBAAyB,CAAC;KACpC;SAAM;QACL,IAAI,eAAe,EAAE;YACnB,SAAS,iBAAoB,CAAC;SAC/B;QACD,IAAI,eAAe,EAAE;YACnB,SAAS,iBAAoB,CAAC;SAC/B;QACD,IAAI,gBAAgB,CAAC,IAAI,EAAE;YACzB,SAAS,iBAAoB,CAAC;SAC/B;QACD,IAAI,wBAAwB,EAAE;YAC5B,SAAS,2BAA6B,CAAC;SACxC;KACF;IAED,IACE,CAAC,SAAS,KAAK,CAAC,IAAI,SAAS,4BAA8B,CAAC;WACzD,CAAC,MAAM,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,EACpC;QACA,SAAS,wBAAyB,CAAC;KACpC;IAED,IAAI,eAAe,GAAgD,CAAC,CAAC,WAAW,EAAE,CAAC;IACnF,IAAI,SAAS,CAAC,MAAM,EAAE;QACpB,IAAI,UAAU,CAAC,MAAM,EAAE;YACrB,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,gBAAgB,CAAC,IAAA,wBAAgB,EAAC,UAAU,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC;SAC9E;QACD,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;YACxB,eAAe,GAAG,CAAC,CAAC,cAAc,CAChC,IAAA,wBAAgB,EAAC,KAAK,EAAE,YAAY,CAAC,EACrC,SAAS,CACV,CAAC;SACH;aAAM;YACL,uCAAuC;YACvC,eAAe,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;SAChC;KACF;SAAM,IAAI,UAAU,CAAC,MAAM,EAAE;QAC5B,4BAA4B;QAC5B,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE;YAC/D,eAAe,GAAI,UAAU,CAAC,CAAC,CAAgC,CAAC,QAAQ,CAAC;SAC1E;aAAM;YACL,eAAe,GAAG,CAAC,CAAC,gBAAgB,CAAC,IAAA,wBAAgB,EAAC,UAAU,EAAE,UAAU,CAAC,CAAC,CAAC;SAChF;KACF;IAED,OAAO;QACL,GAAG;QACH,KAAK,EAAE,eAAe;QACtB,WAAW;QACX,KAAK;QACL,UAAU;QACV,SAAS;QACT,gBAAgB;KACjB,CAAC;AACJ,CAAC,CAAC;AAEF;;;;GAIG;AACH,MAAM,WAAW,GAAG,CAClB,KAMG,EACH,KAAY,EACI,EAAE,CAAC,KAAK;KACvB,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;IACZ,IAAI,IAAI,CAAC,SAAS,EAAE,EAAE;QACpB,MAAM,eAAe,GAAG,IAAA,wBAAgB,EAAC,IAAI,CAAC,CAAC;QAC/C,IAAI,eAAe,EAAE;YACnB,OAAO,CAAC,CAAC,cAAc,CAAC,IAAA,wBAAgB,EAAC,KAAK,EAAE,iBAAiB,CAAC,EAAE,CAAC,eAAe,CAAC,CAAC,CAAC;SACxF;QACD,OAAO,eAAe,CAAC;KACxB;IACD,IAAI,IAAI,CAAC,wBAAwB,EAAE,EAAE;QACnC,MAAM,UAAU,GAAG,IAAA,uCAA+B,EAAC,IAAI,CAAC,CAAC;QAEzD,IAAI,CAAC,CAAC,YAAY,CAAC,UAAU,CAAC,EAAE;YAC9B,MAAM,EAAE,IAAI,EAAE,GAAG,UAAU,CAAC;YAC5B,MAAM,EAAE,cAAc,GAAG,EAAE,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;YAClE,cAAc,CAAC,OAAO,CAAC,CAAC,aAAa,EAAE,EAAE;gBACvC,IAAA,kBAAU,EAAC,aAAa,EAAE,IAAI,kBAAoB,CAAC;YACrD,CAAC,CAAC,CAAC;SACJ;QAED,OAAO,UAAU,CAAC;KACnB;IACD,IAAI,CAAC,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE;QAC5B,OAAO,IAAA,+BAAuB,EAAC,IAAkC,CAAC,CAAC;KACpE;IACD,IAAI,IAAI,CAAC,gBAAgB,EAAE,EAAE;QAC3B,OAAQ,IAAmC,CAAC,IAAI,CAAC;KAClD;IACD,IAAI,IAAI,CAAC,YAAY,EAAE,EAAE;QACvB,OAAO,mBAAmB,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;KACzC;IACD,MAAM,IAAI,KAAK,CAAC,gBAAgB,IAAI,CAAC,IAAI,mBAAmB,CAAC,CAAC;AAChE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,KAAU,EAAE,EAAE,CAAC,CACzB,KAAK,KAAK,SAAS;OAChB,KAAK,KAAK,IAAI;OACd,CAAC,CAAC,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAClC,CAAQ,CAAC,CAAC;AAEb,MAAM,mBAAmB,GAAG,CAC1B,IAA4B,EAC5B,KAAY,EACM,EAAE;IACpB,MAAM,QAAQ,GAAG,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE,KAAK,CAAC,CAAC;IAC1D,MAAM,EACJ,GAAG,EACH,KAAK,EACL,WAAW,EACX,UAAU,EACV,SAAS,EACT,gBAAgB,EAChB,KAAK,GACN,GAAG,UAAU,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IAE5B,MAAM,EAAE,QAAQ,GAAG,KAAK,EAAE,GAAG,KAAK,CAAC,IAAI,CAAC;IAExC,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,kBAAoB,CAAC;IAC9D,IAAI,UAAU,CAAC;IAEf,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,IAAI,KAAK,EAAE;QAChC;;;;UAIE;QACF,UAAU,GAAG,WAAW;YACtB,CAAC,CAAC,QAAQ,CAAC,MAAM;gBACf,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC;oBACnB,CAAC,CAAC,QAAQ,CAAC,MAAM,IAAI,CAAC,CAAC,cAAc,CACnC,CAAC,CAAC,UAAU,CAAC,SAAS,CAAC,EACvB,CAAC,CAAC,uBAAuB,CAAC,EAAE,EAAE,CAAC,CAAC,eAAe,CAAC,IAAA,iBAAS,EAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC,CAC5E;oBACD,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CACV,CAAC,CAAC,kBAAkB,CAAC,KAAK,CAAC;wBACzB,CAAC,CAAE,KAA6B,CAAC,UAAU;wBAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,KAAM,CAAC,CAAC,CAC9B,CAAC,CAAC,CAAC,EAAE,CAAC;oBACP,QAAQ,IAAI,CAAC,CAAC,cAAc,CAC1B,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,EACjB,CAAC,CAAC,cAAc,CAAC,QAAQ,CAAC,CAC3B;iBACF,CAAC,MAAM,CAAC,OAAc,CAAC,CAAC;gBACzB,CAAC,CAAC,KAAK;YACT,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;KACjC;SAAM,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;QAChC;;WAEG;QACH,MAAM,EAAE,iBAAiB,GAAG,IAAI,EAAE,GAAG,KAAK,CAAC,IAAI,CAAC;QAChD,MAAM,KAAK,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;QAC1B,MAAM,gBAAgB,GAAG,CAAC,CAAC,gBAAgB,CAAC;YAC1C,CAAC,CAAC,cAAc,CACd,CAAC,CAAC,UAAU,CAAC,SAAS,CAAC,EACvB,CAAC,CAAC,uBAAuB,CAAC,EAAE,EAAE,CAAC,CAAC,eAAe,CAAC,IAAA,iBAAS,EAAC,IAAI,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAC3E;YACD,QAAQ,IAAI,CAAC,CAAC,cAAc,CAC1B,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,EACjB,CAAC,CAAC,cAAc,CAAC,QAAQ,CAAC,CACpB;SACT,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC;QACnB,IAAI,CAAC,CAAC,YAAY,CAAC,KAAK,CAAC,IAAI,WAAW,EAAE;YACxC,UAAU,GAAG,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC,qBAAqB,CACtD,CAAC,CAAC,cAAc,CAAC,KAAK,CAAC,GAAG,CAAC,qCAAqC,CAAC,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,EAC7E,KAAK,EACL,gBAAgB,CACjB,CAAC,CAAC,CAAC,gBAAgB,CAAC;SACtB;aAAM,IACL,CAAC,CAAC,gBAAgB,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,GAAG,IAAI,WAAW,EACrD,EAAE,kEAAkE;YACpE,IAAI,iBAAiB,EAAE;gBACrB,MAAM,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC;gBACvB,MAAM,MAAM,GAAG,KAAK,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC;gBACnD,IAAI,KAAK,EAAE;oBACT,KAAK,CAAC,IAAI,CAAC;wBACT,EAAE,EAAE,MAAM;wBACV,IAAI,EAAE,KAAK;qBACZ,CAAC,CAAC;iBACJ;gBACD,MAAM,SAAS,GAAG,CAAC,CAAC,gBAAgB,CAAC;oBACnC,CAAC,CAAC,cAAc,CACd,CAAC,CAAC,UAAU,CAAC,SAAS,CAAC,EACvB,CAAC,CAAC,uBAAuB,CAAC,EAAE,EAAE,CAAC,CAAC,eAAe,CAAC,IAAA,iBAAS,EAAC,IAAI,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAC5E,EAAE,QAAQ,IAAI,CAAC,CAAC,cAAc,CAC7B,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,EACjB,CAAC,CAAC,cAAc,CAAC,QAAQ,CAAC,CACpB;iBACT,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC;gBACnB,MAAM,UAAU,GAAG,CAAC,CAAC,oBAAoB,CAAC,GAAG,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;gBAC9D,MAAM,SAAS,GAAG,CAAC,CAAC,cAAc,CAChC,KAAK,CAAC,GAAG,CAAC,qCAAqC,CAAC,EAAE,EAClD,CAAC,UAAU,CAAC,CACb,CAAC;gBACF,UAAU,GAAG,CAAC,CAAC,qBAAqB,CAClC,SAAS,EACT,MAAM,EACN,SAAS,CACV,CAAC;aACH;iBAAM;gBACL,UAAU,GAAG,gBAAgB,CAAC;aAC/B;SACF;aAAM,IAAI,CAAC,CAAC,oBAAoB,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,yBAAyB,CAAC,KAAK,CAAC,EAAE;YAC9E,UAAU,GAAG,CAAC,CAAC,gBAAgB,CAAC;gBAC9B,CAAC,CAAC,cAAc,CACd,CAAC,CAAC,UAAU,CAAC,SAAS,CAAC,EACvB,KAAK,CACN;aACF,CAAC,CAAC;SACJ;aAAM,IAAI,CAAC,CAAC,kBAAkB,CAAC,KAAK,CAAC,EAAE;YACtC,UAAU,GAAG,CAAC,CAAC,gBAAgB,CAAC;gBAC9B,GAAG,KAAK,CAAC,UAAU;gBACnB,QAAQ,IAAI,CAAC,CAAC,cAAc,CAC1B,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,EACjB,CAAC,CAAC,cAAc,CAAC,QAAQ,CAAC,CAC3B;aACF,CAAC,MAAM,CAAC,OAAc,CAAC,CAAC,CAAC;SAC3B;aAAM;YACL,UAAU,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC;gBAC5C,CAAC,CAAC,cAAc,CACd,CAAC,CAAC,UAAU,CAAC,SAAS,CAAC,EACvB,CAAC,CAAC,uBAAuB,CAAC,EAAE,EAAE,CAAC,CAAC,eAAe,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAC1D;aACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;SACjC;KACF;IAED,MAAM,WAAW,GAAG,CAAC,CAAC,cAAc,CAAC,IAAA,wBAAgB,EAAC,KAAK,EAAE,aAAa,CAAC,EAAE;QAC3E,GAAG;QACH,KAAK;QACL,UAAU,IAAI,CAAC,CAAC,WAAW,EAAE;QAC7B,CAAC,CAAC,SAAS,IAAI,QAAQ,IAAI,CAAC,CAAC,cAAc,CAAC,SAAS,CAAC;QACtD,CAAC,CAAC,gBAAgB,CAAC,IAAI,IAAI,QAAQ;eAChC,CAAC,CAAC,eAAe,CAClB,CAAC,GAAG,gBAAgB,CAAC,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAClE;KACF,CAAC,MAAM,CAAC,OAAqC,CAAC,CAAC,CAAC;IAEjD,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE;QACtB,OAAO,WAAW,CAAC;KACpB;IAED,OAAO,CAAC,CAAC,cAAc,CAAC,IAAA,wBAAgB,EAAC,KAAK,EAAE,gBAAgB,CAAC,EAAE;QACjE,WAAW;QACX,CAAC,CAAC,eAAe,CAAC,UAAU,CAAC;KAC9B,CAAC,CAAC;AACL,CAAC,CAAC;AAEF,kBAAe,CAAC;IACd,UAAU,EAAE;QACV,IAAI,CAAC,IAA4B,EAAE,KAAY;YAC7C,IAAI,CAAC,WAAW,CAAC,mBAAmB,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC;QACrD,CAAC;KACF;CACF,CAAC,CAAC"}