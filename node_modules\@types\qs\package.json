{"name": "@types/qs", "version": "6.9.7", "description": "TypeScript definitions for qs", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/qs", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/RWander", "githubUsername": "R<PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/leonyu", "githubUsername": "leonyu"}, {"name": "<PERSON>", "url": "https://github.com/tehbelinda", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/zyml", "githubUsername": "zyml"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/artursvonda", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/dpsmith3", "githubUsername": "dpsmith3"}, {"name": "<PERSON>", "url": "https://github.com/hperrin", "githubUsername": "<PERSON><PERSON>rin"}, {"name": "<PERSON>", "url": "https://github.com/ljharb", "githubUsername": "lj<PERSON>b"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/qs"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "b33fed3eed022f94c7db53593571f370eaa77aa17b3e302dc1bd77304f03e56c", "typeScriptVersion": "3.6"}