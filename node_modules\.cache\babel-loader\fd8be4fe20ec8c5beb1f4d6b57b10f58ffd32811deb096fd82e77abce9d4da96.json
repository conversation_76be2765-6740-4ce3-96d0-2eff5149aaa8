{"ast": null, "code": "var _ErrorTypeStrings;\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return _typeof(key) === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (_typeof(input) !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (_typeof(res) !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\nfunction _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\nfunction _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _iterableToArrayLimit(arr, i) { var _i = null == arr ? null : \"undefined\" != typeof Symbol && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (null != _i) { var _s, _e, _x, _r, _arr = [], _n = !0, _d = !1; try { if (_x = (_i = _i.call(arr)).next, 0 === i) { if (Object(_i) !== _i) return; _n = !1; } else for (; !(_n = (_s = _x.call(_i)).done) && (_arr.push(_s.value), _arr.length !== i); _n = !0); } catch (err) { _d = !0, _e = err; } finally { try { if (!_n && null != _i.return && (_r = _i.return(), Object(_r) !== _r)) return; } finally { if (_d) throw _e; } } return _arr; } }\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\nfunction _createForOfIteratorHelper(o, allowArrayLike) { var it = typeof Symbol !== \"undefined\" && o[Symbol.iterator] || o[\"@@iterator\"]; if (!it) { if (Array.isArray(o) || (it = _unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === \"number\") { if (it) o = it; var i = 0; var F = function F() {}; return { s: F, n: function n() { if (i >= o.length) return { done: true }; return { done: false, value: o[i++] }; }, e: function e(_e2) { throw _e2; }, f: F }; } throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); } var normalCompletion = true, didErr = false, err; return { s: function s() { it = it.call(o); }, n: function n() { var step = it.next(); normalCompletion = step.done; return step; }, e: function e(_e3) { didErr = true; err = _e3; }, f: function f() { try { if (!normalCompletion && it.return != null) it.return(); } finally { if (didErr) throw err; } } }; }\nfunction _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread(); }\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _iterableToArray(iter) { if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter); }\nfunction _arrayWithoutHoles(arr) { if (Array.isArray(arr)) return _arrayLikeToArray(arr); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nimport { toRaw, ref, pauseTracking, resetTracking, reactive, computed, isRef, shallowReactive, trigger, ReactiveEffect, isProxy, shallowReadonly, track, EffectScope, markRaw, proxyRefs, isReactive, isReadonly } from '@vue/reactivity';\nexport { EffectScope, ReactiveEffect, computed, customRef, effect, effectScope, getCurrentScope, isProxy, isReactive, isReadonly, isRef, markRaw, onScopeDispose, proxyRefs, reactive, readonly, ref, shallowReactive, shallowReadonly, shallowRef, stop, toRaw, toRef, toRefs, triggerRef, unref } from '@vue/reactivity';\nimport { getGlobalThis, extend, EMPTY_OBJ, toHandlerKey, isFunction, toNumber, hyphenate, camelize, isArray, isOn, hasOwn, isModelListener, isObject, remove, isString, invokeArrayFns, isPromise, NOOP, def, isReservedProp, EMPTY_ARR, capitalize, toRawType, makeMap, NO, normalizeClass, normalizeStyle, isGloballyWhitelisted, hasChanged, isSet, isMap, isPlainObject } from '@vue/shared';\nexport { camelize, capitalize, normalizeClass, normalizeProps, normalizeStyle, toDisplayString, toHandlerKey } from '@vue/shared';\n\n/* eslint-disable no-restricted-globals */\nvar isHmrUpdating = false;\nvar hmrDirtyComponents = new Set();\n// Expose the HMR runtime on the global object\n// This makes it entirely tree-shakable without polluting the exports and makes\n// it easier to be used in toolings like vue-loader\n// Note: for a component to be eligible for HMR it also needs the __hmrId option\n// to be set so that its instances can be registered / removed.\nif (process.env.NODE_ENV !== 'production') {\n  getGlobalThis().__VUE_HMR_RUNTIME__ = {\n    createRecord: tryWrap(createRecord),\n    rerender: tryWrap(rerender),\n    reload: tryWrap(reload)\n  };\n}\nvar map = new Map();\nfunction registerHMR(instance) {\n  var id = instance.type.__hmrId;\n  var record = map.get(id);\n  if (!record) {\n    createRecord(id, instance.type);\n    record = map.get(id);\n  }\n  record.instances.add(instance);\n}\nfunction unregisterHMR(instance) {\n  map.get(instance.type.__hmrId).instances.delete(instance);\n}\nfunction createRecord(id, initialDef) {\n  if (map.has(id)) {\n    return false;\n  }\n  map.set(id, {\n    initialDef: normalizeClassComponent(initialDef),\n    instances: new Set()\n  });\n  return true;\n}\nfunction normalizeClassComponent(component) {\n  return isClassComponent(component) ? component.__vccOpts : component;\n}\nfunction rerender(id, newRender) {\n  var record = map.get(id);\n  if (!record) {\n    return;\n  }\n  // update initial record (for not-yet-rendered component)\n  record.initialDef.render = newRender;\n  _toConsumableArray(record.instances).forEach(function (instance) {\n    if (newRender) {\n      instance.render = newRender;\n      normalizeClassComponent(instance.type).render = newRender;\n    }\n    instance.renderCache = [];\n    // this flag forces child components with slot content to update\n    isHmrUpdating = true;\n    instance.update();\n    isHmrUpdating = false;\n  });\n}\nfunction reload(id, newComp) {\n  var record = map.get(id);\n  if (!record) return;\n  newComp = normalizeClassComponent(newComp);\n  // update initial def (for not-yet-rendered components)\n  updateComponentDef(record.initialDef, newComp);\n  // create a snapshot which avoids the set being mutated during updates\n  var instances = _toConsumableArray(record.instances);\n  var _iterator = _createForOfIteratorHelper(instances),\n    _step;\n  try {\n    for (_iterator.s(); !(_step = _iterator.n()).done;) {\n      var instance = _step.value;\n      var oldComp = normalizeClassComponent(instance.type);\n      if (!hmrDirtyComponents.has(oldComp)) {\n        // 1. Update existing comp definition to match new one\n        if (oldComp !== record.initialDef) {\n          updateComponentDef(oldComp, newComp);\n        }\n        // 2. mark definition dirty. This forces the renderer to replace the\n        // component on patch.\n        hmrDirtyComponents.add(oldComp);\n      }\n      // 3. invalidate options resolution cache\n      instance.appContext.optionsCache.delete(instance.type);\n      // 4. actually update\n      if (instance.ceReload) {\n        // custom element\n        hmrDirtyComponents.add(oldComp);\n        instance.ceReload(newComp.styles);\n        hmrDirtyComponents.delete(oldComp);\n      } else if (instance.parent) {\n        // 4. Force the parent instance to re-render. This will cause all updated\n        // components to be unmounted and re-mounted. Queue the update so that we\n        // don't end up forcing the same parent to re-render multiple times.\n        queueJob(instance.parent.update);\n        // instance is the inner component of an async custom element\n        // invoke to reset styles\n        if (instance.parent.type.__asyncLoader && instance.parent.ceReload) {\n          instance.parent.ceReload(newComp.styles);\n        }\n      } else if (instance.appContext.reload) {\n        // root instance mounted via createApp() has a reload method\n        instance.appContext.reload();\n      } else if (typeof window !== 'undefined') {\n        // root instance inside tree created via raw render(). Force reload.\n        window.location.reload();\n      } else {}\n    }\n    // 5. make sure to cleanup dirty hmr components after update\n  } catch (err) {\n    _iterator.e(err);\n  } finally {\n    _iterator.f();\n  }\n  queuePostFlushCb(function () {\n    var _iterator2 = _createForOfIteratorHelper(instances),\n      _step2;\n    try {\n      for (_iterator2.s(); !(_step2 = _iterator2.n()).done;) {\n        var instance = _step2.value;\n        hmrDirtyComponents.delete(normalizeClassComponent(instance.type));\n      }\n    } catch (err) {\n      _iterator2.e(err);\n    } finally {\n      _iterator2.f();\n    }\n  });\n}\nfunction updateComponentDef(oldComp, newComp) {\n  extend(oldComp, newComp);\n  for (var key in oldComp) {\n    if (key !== '__file' && !(key in newComp)) {\n      delete oldComp[key];\n    }\n  }\n}\nfunction tryWrap(fn) {\n  return function (id, arg) {\n    try {\n      return fn(id, arg);\n    } catch (e) {}\n  };\n}\nvar devtools;\nvar buffer = [];\nvar devtoolsNotInstalled = false;\nfunction emit(event) {\n  for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n    args[_key - 1] = arguments[_key];\n  }\n  if (devtools) {\n    var _devtools;\n    (_devtools = devtools).emit.apply(_devtools, [event].concat(args));\n  } else if (!devtoolsNotInstalled) {\n    buffer.push({\n      event: event,\n      args: args\n    });\n  }\n}\nfunction setDevtoolsHook(hook, target) {\n  var _a, _b;\n  devtools = hook;\n  if (devtools) {\n    devtools.enabled = true;\n    buffer.forEach(function (_ref) {\n      var _devtools2;\n      var event = _ref.event,\n        args = _ref.args;\n      return (_devtools2 = devtools).emit.apply(_devtools2, [event].concat(_toConsumableArray(args)));\n    });\n    buffer = [];\n  } else if (\n  // handle late devtools injection - only do this if we are in an actual\n  // browser environment to avoid the timer handle stalling test runner exit\n  // (#4815)\n  // eslint-disable-next-line no-restricted-globals\n  typeof window !== 'undefined' &&\n  // some envs mock window but not fully\n  window.HTMLElement &&\n  // also exclude jsdom\n  !((_b = (_a = window.navigator) === null || _a === void 0 ? void 0 : _a.userAgent) === null || _b === void 0 ? void 0 : _b.includes('jsdom'))) {\n    var replay = target.__VUE_DEVTOOLS_HOOK_REPLAY__ = target.__VUE_DEVTOOLS_HOOK_REPLAY__ || [];\n    replay.push(function (newHook) {\n      setDevtoolsHook(newHook, target);\n    });\n    // clear buffer after 3s - the user probably doesn't have devtools installed\n    // at all, and keeping the buffer will cause memory leaks (#4738)\n    setTimeout(function () {\n      if (!devtools) {\n        target.__VUE_DEVTOOLS_HOOK_REPLAY__ = null;\n        devtoolsNotInstalled = true;\n        buffer = [];\n      }\n    }, 3000);\n  } else {\n    // non-browser env, assume not installed\n    devtoolsNotInstalled = true;\n    buffer = [];\n  }\n}\nfunction devtoolsInitApp(app, version) {\n  emit(\"app:init\" /* APP_INIT */, app, version, {\n    Fragment: Fragment,\n    Text: Text,\n    Comment: Comment,\n    Static: Static\n  });\n}\nfunction devtoolsUnmountApp(app) {\n  emit(\"app:unmount\" /* APP_UNMOUNT */, app);\n}\nvar devtoolsComponentAdded = /*#__PURE__*/createDevtoolsComponentHook(\"component:added\" /* COMPONENT_ADDED */);\nvar devtoolsComponentUpdated = /*#__PURE__*/createDevtoolsComponentHook(\"component:updated\" /* COMPONENT_UPDATED */);\nvar devtoolsComponentRemoved = /*#__PURE__*/createDevtoolsComponentHook(\"component:removed\" /* COMPONENT_REMOVED */);\nfunction createDevtoolsComponentHook(hook) {\n  return function (component) {\n    emit(hook, component.appContext.app, component.uid, component.parent ? component.parent.uid : undefined, component);\n  };\n}\nvar devtoolsPerfStart = /*#__PURE__*/createDevtoolsPerformanceHook(\"perf:start\" /* PERFORMANCE_START */);\nvar devtoolsPerfEnd = /*#__PURE__*/createDevtoolsPerformanceHook(\"perf:end\" /* PERFORMANCE_END */);\nfunction createDevtoolsPerformanceHook(hook) {\n  return function (component, type, time) {\n    emit(hook, component.appContext.app, component.uid, component, type, time);\n  };\n}\nfunction devtoolsComponentEmit(component, event, params) {\n  emit(\"component:emit\" /* COMPONENT_EMIT */, component.appContext.app, component, event, params);\n}\nfunction emit$1(instance, event) {\n  var props = instance.vnode.props || EMPTY_OBJ;\n  for (var _len2 = arguments.length, rawArgs = new Array(_len2 > 2 ? _len2 - 2 : 0), _key2 = 2; _key2 < _len2; _key2++) {\n    rawArgs[_key2 - 2] = arguments[_key2];\n  }\n  if (process.env.NODE_ENV !== 'production') {\n    var emitsOptions = instance.emitsOptions,\n      _instance$propsOption = _slicedToArray(instance.propsOptions, 1),\n      propsOptions = _instance$propsOption[0];\n    if (emitsOptions) {\n      if (!(event in emitsOptions) && !false) {\n        if (!propsOptions || !(toHandlerKey(event) in propsOptions)) {\n          warn(\"Component emitted event \\\"\".concat(event, \"\\\" but it is neither declared in \") + \"the emits option nor as an \\\"\".concat(toHandlerKey(event), \"\\\" prop.\"));\n        }\n      } else {\n        var validator = emitsOptions[event];\n        if (isFunction(validator)) {\n          var isValid = validator.apply(void 0, rawArgs);\n          if (!isValid) {\n            warn(\"Invalid event arguments: event validation failed for event \\\"\".concat(event, \"\\\".\"));\n          }\n        }\n      }\n    }\n  }\n  var args = rawArgs;\n  var isModelListener = event.startsWith('update:');\n  // for v-model update:xxx events, apply modifiers on args\n  var modelArg = isModelListener && event.slice(7);\n  if (modelArg && modelArg in props) {\n    var modifiersKey = \"\".concat(modelArg === 'modelValue' ? 'model' : modelArg, \"Modifiers\");\n    var _ref2 = props[modifiersKey] || EMPTY_OBJ,\n      number = _ref2.number,\n      trim = _ref2.trim;\n    if (trim) {\n      args = rawArgs.map(function (a) {\n        return a.trim();\n      });\n    } else if (number) {\n      args = rawArgs.map(toNumber);\n    }\n  }\n  if (process.env.NODE_ENV !== 'production' || __VUE_PROD_DEVTOOLS__) {\n    devtoolsComponentEmit(instance, event, args);\n  }\n  if (process.env.NODE_ENV !== 'production') {\n    var lowerCaseEvent = event.toLowerCase();\n    if (lowerCaseEvent !== event && props[toHandlerKey(lowerCaseEvent)]) {\n      warn(\"Event \\\"\".concat(lowerCaseEvent, \"\\\" is emitted in component \") + \"\".concat(formatComponentName(instance, instance.type), \" but the handler is registered for \\\"\").concat(event, \"\\\". \") + \"Note that HTML attributes are case-insensitive and you cannot use \" + \"v-on to listen to camelCase events when using in-DOM templates. \" + \"You should probably use \\\"\".concat(hyphenate(event), \"\\\" instead of \\\"\").concat(event, \"\\\".\"));\n    }\n  }\n  var handlerName;\n  var handler = props[handlerName = toHandlerKey(event)] ||\n  // also try camelCase event handler (#2249)\n  props[handlerName = toHandlerKey(camelize(event))];\n  // for v-model update:xxx events, also trigger kebab-case equivalent\n  // for props passed via kebab-case\n  if (!handler && isModelListener) {\n    handler = props[handlerName = toHandlerKey(hyphenate(event))];\n  }\n  if (handler) {\n    callWithAsyncErrorHandling(handler, instance, 6 /* COMPONENT_EVENT_HANDLER */, args);\n  }\n  var onceHandler = props[handlerName + \"Once\"];\n  if (onceHandler) {\n    if (!instance.emitted) {\n      instance.emitted = {};\n    } else if (instance.emitted[handlerName]) {\n      return;\n    }\n    instance.emitted[handlerName] = true;\n    callWithAsyncErrorHandling(onceHandler, instance, 6 /* COMPONENT_EVENT_HANDLER */, args);\n  }\n}\nfunction normalizeEmitsOptions(comp, appContext) {\n  var asMixin = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n  var cache = appContext.emitsCache;\n  var cached = cache.get(comp);\n  if (cached !== undefined) {\n    return cached;\n  }\n  var raw = comp.emits;\n  var normalized = {};\n  // apply mixin/extends props\n  var hasExtends = false;\n  if (__VUE_OPTIONS_API__ && !isFunction(comp)) {\n    var extendEmits = function extendEmits(raw) {\n      var normalizedFromExtend = normalizeEmitsOptions(raw, appContext, true);\n      if (normalizedFromExtend) {\n        hasExtends = true;\n        extend(normalized, normalizedFromExtend);\n      }\n    };\n    if (!asMixin && appContext.mixins.length) {\n      appContext.mixins.forEach(extendEmits);\n    }\n    if (comp.extends) {\n      extendEmits(comp.extends);\n    }\n    if (comp.mixins) {\n      comp.mixins.forEach(extendEmits);\n    }\n  }\n  if (!raw && !hasExtends) {\n    cache.set(comp, null);\n    return null;\n  }\n  if (isArray(raw)) {\n    raw.forEach(function (key) {\n      return normalized[key] = null;\n    });\n  } else {\n    extend(normalized, raw);\n  }\n  cache.set(comp, normalized);\n  return normalized;\n}\n// Check if an incoming prop key is a declared emit event listener.\n// e.g. With `emits: { click: null }`, props named `onClick` and `onclick` are\n// both considered matched listeners.\nfunction isEmitListener(options, key) {\n  if (!options || !isOn(key)) {\n    return false;\n  }\n  key = key.slice(2).replace(/Once$/, '');\n  return hasOwn(options, key[0].toLowerCase() + key.slice(1)) || hasOwn(options, hyphenate(key)) || hasOwn(options, key);\n}\n\n/**\r\n * mark the current rendering instance for asset resolution (e.g.\r\n * resolveComponent, resolveDirective) during render\r\n */\nvar currentRenderingInstance = null;\nvar currentScopeId = null;\n/**\r\n * Note: rendering calls maybe nested. The function returns the parent rendering\r\n * instance if present, which should be restored after the render is done:\r\n *\r\n * ```js\r\n * const prev = setCurrentRenderingInstance(i)\r\n * // ...render\r\n * setCurrentRenderingInstance(prev)\r\n * ```\r\n */\nfunction setCurrentRenderingInstance(instance) {\n  var prev = currentRenderingInstance;\n  currentRenderingInstance = instance;\n  currentScopeId = instance && instance.type.__scopeId || null;\n  return prev;\n}\n/**\r\n * Set scope id when creating hoisted vnodes.\r\n * @private compiler helper\r\n */\nfunction pushScopeId(id) {\n  currentScopeId = id;\n}\n/**\r\n * Technically we no longer need this after 3.0.8 but we need to keep the same\r\n * API for backwards compat w/ code generated by compilers.\r\n * @private\r\n */\nfunction popScopeId() {\n  currentScopeId = null;\n}\n/**\r\n * Only for backwards compat\r\n * @private\r\n */\nvar withScopeId = function withScopeId(_id) {\n  return withCtx;\n};\n/**\r\n * Wrap a slot function to memoize current rendering instance\r\n * @private compiler helper\r\n */\nfunction withCtx(fn) {\n  var ctx = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : currentRenderingInstance;\n  var isNonScopedSlot // false only\n  = arguments.length > 2 ? arguments[2] : undefined;\n  if (!ctx) return fn;\n  // already normalized\n  if (fn._n) {\n    return fn;\n  }\n  var renderFnWithContext = function renderFnWithContext() {\n    // If a user calls a compiled slot inside a template expression (#1745), it\n    // can mess up block tracking, so by default we disable block tracking and\n    // force bail out when invoking a compiled slot (indicated by the ._d flag).\n    // This isn't necessary if rendering a compiled `<slot>`, so we flip the\n    // ._d flag off when invoking the wrapped fn inside `renderSlot`.\n    if (renderFnWithContext._d) {\n      setBlockTracking(-1);\n    }\n    var prevInstance = setCurrentRenderingInstance(ctx);\n    var res = fn.apply(void 0, arguments);\n    setCurrentRenderingInstance(prevInstance);\n    if (renderFnWithContext._d) {\n      setBlockTracking(1);\n    }\n    if (process.env.NODE_ENV !== 'production' || __VUE_PROD_DEVTOOLS__) {\n      devtoolsComponentUpdated(ctx);\n    }\n    return res;\n  };\n  // mark normalized to avoid duplicated wrapping\n  renderFnWithContext._n = true;\n  // mark this as compiled by default\n  // this is used in vnode.ts -> normalizeChildren() to set the slot\n  // rendering flag.\n  renderFnWithContext._c = true;\n  // disable block tracking by default\n  renderFnWithContext._d = true;\n  return renderFnWithContext;\n}\n\n/**\r\n * dev only flag to track whether $attrs was used during render.\r\n * If $attrs was used during render then the warning for failed attrs\r\n * fallthrough can be suppressed.\r\n */\nvar accessedAttrs = false;\nfunction markAttrsAccessed() {\n  accessedAttrs = true;\n}\nfunction renderComponentRoot(instance) {\n  var Component = instance.type,\n    vnode = instance.vnode,\n    proxy = instance.proxy,\n    withProxy = instance.withProxy,\n    props = instance.props,\n    _instance$propsOption2 = _slicedToArray(instance.propsOptions, 1),\n    propsOptions = _instance$propsOption2[0],\n    slots = instance.slots,\n    attrs = instance.attrs,\n    emit = instance.emit,\n    render = instance.render,\n    renderCache = instance.renderCache,\n    data = instance.data,\n    setupState = instance.setupState,\n    ctx = instance.ctx,\n    inheritAttrs = instance.inheritAttrs;\n  var result;\n  var fallthroughAttrs;\n  var prev = setCurrentRenderingInstance(instance);\n  if (process.env.NODE_ENV !== 'production') {\n    accessedAttrs = false;\n  }\n  try {\n    if (vnode.shapeFlag & 4 /* STATEFUL_COMPONENT */) {\n      // withProxy is a proxy with a different `has` trap only for\n      // runtime-compiled render functions using `with` block.\n      var proxyToUse = withProxy || proxy;\n      result = normalizeVNode(render.call(proxyToUse, proxyToUse, renderCache, props, setupState, data, ctx));\n      fallthroughAttrs = attrs;\n    } else {\n      // functional\n      var _render = Component;\n      // in dev, mark attrs accessed if optional props (attrs === props)\n      if (process.env.NODE_ENV !== 'production' && attrs === props) {\n        markAttrsAccessed();\n      }\n      result = normalizeVNode(_render.length > 1 ? _render(props, process.env.NODE_ENV !== 'production' ? {\n        get attrs() {\n          markAttrsAccessed();\n          return attrs;\n        },\n        slots: slots,\n        emit: emit\n      } : {\n        attrs: attrs,\n        slots: slots,\n        emit: emit\n      }) : _render(props, null /* we know it doesn't need it */));\n      fallthroughAttrs = Component.props ? attrs : getFunctionalFallthrough(attrs);\n    }\n  } catch (err) {\n    blockStack.length = 0;\n    handleError(err, instance, 1 /* RENDER_FUNCTION */);\n    result = createVNode(Comment);\n  }\n  // attr merging\n  // in dev mode, comments are preserved, and it's possible for a template\n  // to have comments along side the root element which makes it a fragment\n  var root = result;\n  var setRoot = undefined;\n  if (process.env.NODE_ENV !== 'production' && result.patchFlag > 0 && result.patchFlag & 2048 /* DEV_ROOT_FRAGMENT */) {\n    var _getChildRoot = getChildRoot(result);\n    var _getChildRoot2 = _slicedToArray(_getChildRoot, 2);\n    root = _getChildRoot2[0];\n    setRoot = _getChildRoot2[1];\n  }\n  if (fallthroughAttrs && inheritAttrs !== false) {\n    var keys = Object.keys(fallthroughAttrs);\n    var _root = root,\n      shapeFlag = _root.shapeFlag;\n    if (keys.length) {\n      if (shapeFlag & (1 /* ELEMENT */ | 6 /* COMPONENT */)) {\n        if (propsOptions && keys.some(isModelListener)) {\n          // If a v-model listener (onUpdate:xxx) has a corresponding declared\n          // prop, it indicates this component expects to handle v-model and\n          // it should not fallthrough.\n          // related: #1543, #1643, #1989\n          fallthroughAttrs = filterModelListeners(fallthroughAttrs, propsOptions);\n        }\n        root = cloneVNode(root, fallthroughAttrs);\n      } else if (process.env.NODE_ENV !== 'production' && !accessedAttrs && root.type !== Comment) {\n        var allAttrs = Object.keys(attrs);\n        var eventAttrs = [];\n        var extraAttrs = [];\n        for (var i = 0, l = allAttrs.length; i < l; i++) {\n          var key = allAttrs[i];\n          if (isOn(key)) {\n            // ignore v-model handlers when they fail to fallthrough\n            if (!isModelListener(key)) {\n              // remove `on`, lowercase first letter to reflect event casing\n              // accurately\n              eventAttrs.push(key[2].toLowerCase() + key.slice(3));\n            }\n          } else {\n            extraAttrs.push(key);\n          }\n        }\n        if (extraAttrs.length) {\n          warn(\"Extraneous non-props attributes (\" + \"\".concat(extraAttrs.join(', '), \") \") + \"were passed to component but could not be automatically inherited \" + \"because component renders fragment or text root nodes.\");\n        }\n        if (eventAttrs.length) {\n          warn(\"Extraneous non-emits event listeners (\" + \"\".concat(eventAttrs.join(', '), \") \") + \"were passed to component but could not be automatically inherited \" + \"because component renders fragment or text root nodes. \" + \"If the listener is intended to be a component custom event listener only, \" + \"declare it using the \\\"emits\\\" option.\");\n        }\n      }\n    }\n  }\n  // inherit directives\n  if (vnode.dirs) {\n    if (process.env.NODE_ENV !== 'production' && !isElementRoot(root)) {\n      warn(\"Runtime directive used on component with non-element root node. \" + \"The directives will not function as intended.\");\n    }\n    root.dirs = root.dirs ? root.dirs.concat(vnode.dirs) : vnode.dirs;\n  }\n  // inherit transition data\n  if (vnode.transition) {\n    if (process.env.NODE_ENV !== 'production' && !isElementRoot(root)) {\n      warn(\"Component inside <Transition> renders non-element root node \" + \"that cannot be animated.\");\n    }\n    root.transition = vnode.transition;\n  }\n  if (process.env.NODE_ENV !== 'production' && setRoot) {\n    setRoot(root);\n  } else {\n    result = root;\n  }\n  setCurrentRenderingInstance(prev);\n  return result;\n}\n/**\r\n * dev only\r\n * In dev mode, template root level comments are rendered, which turns the\r\n * template into a fragment root, but we need to locate the single element\r\n * root for attrs and scope id processing.\r\n */\nvar getChildRoot = function getChildRoot(vnode) {\n  var rawChildren = vnode.children;\n  var dynamicChildren = vnode.dynamicChildren;\n  var childRoot = filterSingleRoot(rawChildren);\n  if (!childRoot) {\n    return [vnode, undefined];\n  }\n  var index = rawChildren.indexOf(childRoot);\n  var dynamicIndex = dynamicChildren ? dynamicChildren.indexOf(childRoot) : -1;\n  var setRoot = function setRoot(updatedRoot) {\n    rawChildren[index] = updatedRoot;\n    if (dynamicChildren) {\n      if (dynamicIndex > -1) {\n        dynamicChildren[dynamicIndex] = updatedRoot;\n      } else if (updatedRoot.patchFlag > 0) {\n        vnode.dynamicChildren = [].concat(_toConsumableArray(dynamicChildren), [updatedRoot]);\n      }\n    }\n  };\n  return [normalizeVNode(childRoot), setRoot];\n};\nfunction filterSingleRoot(children) {\n  var singleRoot;\n  for (var i = 0; i < children.length; i++) {\n    var child = children[i];\n    if (isVNode(child)) {\n      // ignore user comment\n      if (child.type !== Comment || child.children === 'v-if') {\n        if (singleRoot) {\n          // has more than 1 non-comment child, return now\n          return;\n        } else {\n          singleRoot = child;\n        }\n      }\n    } else {\n      return;\n    }\n  }\n  return singleRoot;\n}\nvar getFunctionalFallthrough = function getFunctionalFallthrough(attrs) {\n  var res;\n  for (var key in attrs) {\n    if (key === 'class' || key === 'style' || isOn(key)) {\n      (res || (res = {}))[key] = attrs[key];\n    }\n  }\n  return res;\n};\nvar filterModelListeners = function filterModelListeners(attrs, props) {\n  var res = {};\n  for (var key in attrs) {\n    if (!isModelListener(key) || !(key.slice(9) in props)) {\n      res[key] = attrs[key];\n    }\n  }\n  return res;\n};\nvar isElementRoot = function isElementRoot(vnode) {\n  return vnode.shapeFlag & (6 /* COMPONENT */ | 1 /* ELEMENT */) || vnode.type === Comment // potential v-if branch switch\n  ;\n};\n\nfunction shouldUpdateComponent(prevVNode, nextVNode, optimized) {\n  var prevProps = prevVNode.props,\n    prevChildren = prevVNode.children,\n    component = prevVNode.component;\n  var nextProps = nextVNode.props,\n    nextChildren = nextVNode.children,\n    patchFlag = nextVNode.patchFlag;\n  var emits = component.emitsOptions;\n  // Parent component's render function was hot-updated. Since this may have\n  // caused the child component's slots content to have changed, we need to\n  // force the child to update as well.\n  if (process.env.NODE_ENV !== 'production' && (prevChildren || nextChildren) && isHmrUpdating) {\n    return true;\n  }\n  // force child update for runtime directive or transition on component vnode.\n  if (nextVNode.dirs || nextVNode.transition) {\n    return true;\n  }\n  if (optimized && patchFlag >= 0) {\n    if (patchFlag & 1024 /* DYNAMIC_SLOTS */) {\n      // slot content that references values that might have changed,\n      // e.g. in a v-for\n      return true;\n    }\n    if (patchFlag & 16 /* FULL_PROPS */) {\n      if (!prevProps) {\n        return !!nextProps;\n      }\n      // presence of this flag indicates props are always non-null\n      return hasPropsChanged(prevProps, nextProps, emits);\n    } else if (patchFlag & 8 /* PROPS */) {\n      var dynamicProps = nextVNode.dynamicProps;\n      for (var i = 0; i < dynamicProps.length; i++) {\n        var key = dynamicProps[i];\n        if (nextProps[key] !== prevProps[key] && !isEmitListener(emits, key)) {\n          return true;\n        }\n      }\n    }\n  } else {\n    // this path is only taken by manually written render functions\n    // so presence of any children leads to a forced update\n    if (prevChildren || nextChildren) {\n      if (!nextChildren || !nextChildren.$stable) {\n        return true;\n      }\n    }\n    if (prevProps === nextProps) {\n      return false;\n    }\n    if (!prevProps) {\n      return !!nextProps;\n    }\n    if (!nextProps) {\n      return true;\n    }\n    return hasPropsChanged(prevProps, nextProps, emits);\n  }\n  return false;\n}\nfunction hasPropsChanged(prevProps, nextProps, emitsOptions) {\n  var nextKeys = Object.keys(nextProps);\n  if (nextKeys.length !== Object.keys(prevProps).length) {\n    return true;\n  }\n  for (var i = 0; i < nextKeys.length; i++) {\n    var key = nextKeys[i];\n    if (nextProps[key] !== prevProps[key] && !isEmitListener(emitsOptions, key)) {\n      return true;\n    }\n  }\n  return false;\n}\nfunction updateHOCHostEl(_ref3, el // HostNode\n) {\n  var vnode = _ref3.vnode,\n    parent = _ref3.parent;\n  while (parent && parent.subTree === vnode) {\n    (vnode = parent.vnode).el = el;\n    parent = parent.parent;\n  }\n}\nvar isSuspense = function isSuspense(type) {\n  return type.__isSuspense;\n};\n// Suspense exposes a component-like API, and is treated like a component\n// in the compiler, but internally it's a special built-in type that hooks\n// directly into the renderer.\nvar SuspenseImpl = {\n  name: 'Suspense',\n  // In order to make Suspense tree-shakable, we need to avoid importing it\n  // directly in the renderer. The renderer checks for the __isSuspense flag\n  // on a vnode's type and calls the `process` method, passing in renderer\n  // internals.\n  __isSuspense: true,\n  process: function process(n1, n2, container, anchor, parentComponent, parentSuspense, isSVG, slotScopeIds, optimized,\n  // platform-specific impl passed from renderer\n  rendererInternals) {\n    if (n1 == null) {\n      mountSuspense(n2, container, anchor, parentComponent, parentSuspense, isSVG, slotScopeIds, optimized, rendererInternals);\n    } else {\n      patchSuspense(n1, n2, container, anchor, parentComponent, isSVG, slotScopeIds, optimized, rendererInternals);\n    }\n  },\n  hydrate: hydrateSuspense,\n  create: createSuspenseBoundary,\n  normalize: normalizeSuspenseChildren\n};\n// Force-casted public typing for h and TSX props inference\nvar Suspense = SuspenseImpl;\nfunction triggerEvent(vnode, name) {\n  var eventListener = vnode.props && vnode.props[name];\n  if (isFunction(eventListener)) {\n    eventListener();\n  }\n}\nfunction mountSuspense(vnode, container, anchor, parentComponent, parentSuspense, isSVG, slotScopeIds, optimized, rendererInternals) {\n  var patch = rendererInternals.p,\n    createElement = rendererInternals.o.createElement;\n  var hiddenContainer = createElement('div');\n  var suspense = vnode.suspense = createSuspenseBoundary(vnode, parentSuspense, parentComponent, container, hiddenContainer, anchor, isSVG, slotScopeIds, optimized, rendererInternals);\n  // start mounting the content subtree in an off-dom container\n  patch(null, suspense.pendingBranch = vnode.ssContent, hiddenContainer, null, parentComponent, suspense, isSVG, slotScopeIds);\n  // now check if we have encountered any async deps\n  if (suspense.deps > 0) {\n    // has async\n    // invoke @fallback event\n    triggerEvent(vnode, 'onPending');\n    triggerEvent(vnode, 'onFallback');\n    // mount the fallback tree\n    patch(null, vnode.ssFallback, container, anchor, parentComponent, null,\n    // fallback tree will not have suspense context\n    isSVG, slotScopeIds);\n    setActiveBranch(suspense, vnode.ssFallback);\n  } else {\n    // Suspense has no async deps. Just resolve.\n    suspense.resolve();\n  }\n}\nfunction patchSuspense(n1, n2, container, anchor, parentComponent, isSVG, slotScopeIds, optimized, _ref4) {\n  var patch = _ref4.p,\n    unmount = _ref4.um,\n    createElement = _ref4.o.createElement;\n  var suspense = n2.suspense = n1.suspense;\n  suspense.vnode = n2;\n  n2.el = n1.el;\n  var newBranch = n2.ssContent;\n  var newFallback = n2.ssFallback;\n  var activeBranch = suspense.activeBranch,\n    pendingBranch = suspense.pendingBranch,\n    isInFallback = suspense.isInFallback,\n    isHydrating = suspense.isHydrating;\n  if (pendingBranch) {\n    suspense.pendingBranch = newBranch;\n    if (isSameVNodeType(newBranch, pendingBranch)) {\n      // same root type but content may have changed.\n      patch(pendingBranch, newBranch, suspense.hiddenContainer, null, parentComponent, suspense, isSVG, slotScopeIds, optimized);\n      if (suspense.deps <= 0) {\n        suspense.resolve();\n      } else if (isInFallback) {\n        patch(activeBranch, newFallback, container, anchor, parentComponent, null,\n        // fallback tree will not have suspense context\n        isSVG, slotScopeIds, optimized);\n        setActiveBranch(suspense, newFallback);\n      }\n    } else {\n      // toggled before pending tree is resolved\n      suspense.pendingId++;\n      if (isHydrating) {\n        // if toggled before hydration is finished, the current DOM tree is\n        // no longer valid. set it as the active branch so it will be unmounted\n        // when resolved\n        suspense.isHydrating = false;\n        suspense.activeBranch = pendingBranch;\n      } else {\n        unmount(pendingBranch, parentComponent, suspense);\n      }\n      // increment pending ID. this is used to invalidate async callbacks\n      // reset suspense state\n      suspense.deps = 0;\n      // discard effects from pending branch\n      suspense.effects.length = 0;\n      // discard previous container\n      suspense.hiddenContainer = createElement('div');\n      if (isInFallback) {\n        // already in fallback state\n        patch(null, newBranch, suspense.hiddenContainer, null, parentComponent, suspense, isSVG, slotScopeIds, optimized);\n        if (suspense.deps <= 0) {\n          suspense.resolve();\n        } else {\n          patch(activeBranch, newFallback, container, anchor, parentComponent, null,\n          // fallback tree will not have suspense context\n          isSVG, slotScopeIds, optimized);\n          setActiveBranch(suspense, newFallback);\n        }\n      } else if (activeBranch && isSameVNodeType(newBranch, activeBranch)) {\n        // toggled \"back\" to current active branch\n        patch(activeBranch, newBranch, container, anchor, parentComponent, suspense, isSVG, slotScopeIds, optimized);\n        // force resolve\n        suspense.resolve(true);\n      } else {\n        // switched to a 3rd branch\n        patch(null, newBranch, suspense.hiddenContainer, null, parentComponent, suspense, isSVG, slotScopeIds, optimized);\n        if (suspense.deps <= 0) {\n          suspense.resolve();\n        }\n      }\n    }\n  } else {\n    if (activeBranch && isSameVNodeType(newBranch, activeBranch)) {\n      // root did not change, just normal patch\n      patch(activeBranch, newBranch, container, anchor, parentComponent, suspense, isSVG, slotScopeIds, optimized);\n      setActiveBranch(suspense, newBranch);\n    } else {\n      // root node toggled\n      // invoke @pending event\n      triggerEvent(n2, 'onPending');\n      // mount pending branch in off-dom container\n      suspense.pendingBranch = newBranch;\n      suspense.pendingId++;\n      patch(null, newBranch, suspense.hiddenContainer, null, parentComponent, suspense, isSVG, slotScopeIds, optimized);\n      if (suspense.deps <= 0) {\n        // incoming branch has no async deps, resolve now.\n        suspense.resolve();\n      } else {\n        var timeout = suspense.timeout,\n          pendingId = suspense.pendingId;\n        if (timeout > 0) {\n          setTimeout(function () {\n            if (suspense.pendingId === pendingId) {\n              suspense.fallback(newFallback);\n            }\n          }, timeout);\n        } else if (timeout === 0) {\n          suspense.fallback(newFallback);\n        }\n      }\n    }\n  }\n}\nvar hasWarned = false;\nfunction createSuspenseBoundary(vnode, parent, parentComponent, container, hiddenContainer, anchor, isSVG, slotScopeIds, optimized, rendererInternals) {\n  var isHydrating = arguments.length > 10 && arguments[10] !== undefined ? arguments[10] : false;\n  /* istanbul ignore if */\n  if (process.env.NODE_ENV !== 'production' && !false && !hasWarned) {\n    hasWarned = true;\n    // @ts-ignore `console.info` cannot be null error\n  }\n\n  var patch = rendererInternals.p,\n    _move = rendererInternals.m,\n    _unmount2 = rendererInternals.um,\n    _next = rendererInternals.n,\n    _rendererInternals$o = rendererInternals.o,\n    parentNode = _rendererInternals$o.parentNode,\n    remove = _rendererInternals$o.remove;\n  var timeout = toNumber(vnode.props && vnode.props.timeout);\n  var suspense = {\n    vnode: vnode,\n    parent: parent,\n    parentComponent: parentComponent,\n    isSVG: isSVG,\n    container: container,\n    hiddenContainer: hiddenContainer,\n    anchor: anchor,\n    deps: 0,\n    pendingId: 0,\n    timeout: typeof timeout === 'number' ? timeout : -1,\n    activeBranch: null,\n    pendingBranch: null,\n    isInFallback: true,\n    isHydrating: isHydrating,\n    isUnmounted: false,\n    effects: [],\n    resolve: function resolve() {\n      var resume = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;\n      if (process.env.NODE_ENV !== 'production') {\n        if (!resume && !suspense.pendingBranch) {\n          throw new Error(\"suspense.resolve() is called without a pending branch.\");\n        }\n        if (suspense.isUnmounted) {\n          throw new Error(\"suspense.resolve() is called on an already unmounted suspense boundary.\");\n        }\n      }\n      var vnode = suspense.vnode,\n        activeBranch = suspense.activeBranch,\n        pendingBranch = suspense.pendingBranch,\n        pendingId = suspense.pendingId,\n        effects = suspense.effects,\n        parentComponent = suspense.parentComponent,\n        container = suspense.container;\n      if (suspense.isHydrating) {\n        suspense.isHydrating = false;\n      } else if (!resume) {\n        var delayEnter = activeBranch && pendingBranch.transition && pendingBranch.transition.mode === 'out-in';\n        if (delayEnter) {\n          activeBranch.transition.afterLeave = function () {\n            if (pendingId === suspense.pendingId) {\n              _move(pendingBranch, container, _anchor, 0 /* ENTER */);\n            }\n          };\n        }\n        // this is initial anchor on mount\n        var _anchor = suspense.anchor;\n        // unmount current active tree\n        if (activeBranch) {\n          // if the fallback tree was mounted, it may have been moved\n          // as part of a parent suspense. get the latest anchor for insertion\n          _anchor = _next(activeBranch);\n          _unmount2(activeBranch, parentComponent, suspense, true);\n        }\n        if (!delayEnter) {\n          // move content from off-dom container to actual container\n          _move(pendingBranch, container, _anchor, 0 /* ENTER */);\n        }\n      }\n\n      setActiveBranch(suspense, pendingBranch);\n      suspense.pendingBranch = null;\n      suspense.isInFallback = false;\n      // flush buffered effects\n      // check if there is a pending parent suspense\n      var parent = suspense.parent;\n      var hasUnresolvedAncestor = false;\n      while (parent) {\n        if (parent.pendingBranch) {\n          var _parent$effects;\n          // found a pending parent suspense, merge buffered post jobs\n          // into that parent\n          (_parent$effects = parent.effects).push.apply(_parent$effects, _toConsumableArray(effects));\n          hasUnresolvedAncestor = true;\n          break;\n        }\n        parent = parent.parent;\n      }\n      // no pending parent suspense, flush all jobs\n      if (!hasUnresolvedAncestor) {\n        queuePostFlushCb(effects);\n      }\n      suspense.effects = [];\n      // invoke @resolve event\n      triggerEvent(vnode, 'onResolve');\n    },\n    fallback: function fallback(fallbackVNode) {\n      if (!suspense.pendingBranch) {\n        return;\n      }\n      var vnode = suspense.vnode,\n        activeBranch = suspense.activeBranch,\n        parentComponent = suspense.parentComponent,\n        container = suspense.container,\n        isSVG = suspense.isSVG;\n      // invoke @fallback event\n      triggerEvent(vnode, 'onFallback');\n      var anchor = _next(activeBranch);\n      var mountFallback = function mountFallback() {\n        if (!suspense.isInFallback) {\n          return;\n        }\n        // mount the fallback tree\n        patch(null, fallbackVNode, container, anchor, parentComponent, null,\n        // fallback tree will not have suspense context\n        isSVG, slotScopeIds, optimized);\n        setActiveBranch(suspense, fallbackVNode);\n      };\n      var delayEnter = fallbackVNode.transition && fallbackVNode.transition.mode === 'out-in';\n      if (delayEnter) {\n        activeBranch.transition.afterLeave = mountFallback;\n      }\n      suspense.isInFallback = true;\n      // unmount current active branch\n      _unmount2(activeBranch, parentComponent, null,\n      // no suspense so unmount hooks fire now\n      true // shouldRemove\n      );\n\n      if (!delayEnter) {\n        mountFallback();\n      }\n    },\n    move: function move(container, anchor, type) {\n      suspense.activeBranch && _move(suspense.activeBranch, container, anchor, type);\n      suspense.container = container;\n    },\n    next: function next() {\n      return suspense.activeBranch && _next(suspense.activeBranch);\n    },\n    registerDep: function registerDep(instance, setupRenderEffect) {\n      var isInPendingSuspense = !!suspense.pendingBranch;\n      if (isInPendingSuspense) {\n        suspense.deps++;\n      }\n      var hydratedEl = instance.vnode.el;\n      instance.asyncDep.catch(function (err) {\n        handleError(err, instance, 0 /* SETUP_FUNCTION */);\n      }).then(function (asyncSetupResult) {\n        // retry when the setup() promise resolves.\n        // component may have been unmounted before resolve.\n        if (instance.isUnmounted || suspense.isUnmounted || suspense.pendingId !== instance.suspenseId) {\n          return;\n        }\n        // retry from this component\n        instance.asyncResolved = true;\n        var vnode = instance.vnode;\n        if (process.env.NODE_ENV !== 'production') {\n          pushWarningContext(vnode);\n        }\n        handleSetupResult(instance, asyncSetupResult, false);\n        if (hydratedEl) {\n          // vnode may have been replaced if an update happened before the\n          // async dep is resolved.\n          vnode.el = hydratedEl;\n        }\n        var placeholder = !hydratedEl && instance.subTree.el;\n        setupRenderEffect(instance, vnode,\n        // component may have been moved before resolve.\n        // if this is not a hydration, instance.subTree will be the comment\n        // placeholder.\n        parentNode(hydratedEl || instance.subTree.el),\n        // anchor will not be used if this is hydration, so only need to\n        // consider the comment placeholder case.\n        hydratedEl ? null : _next(instance.subTree), suspense, isSVG, optimized);\n        if (placeholder) {\n          remove(placeholder);\n        }\n        updateHOCHostEl(instance, vnode.el);\n        if (process.env.NODE_ENV !== 'production') {\n          popWarningContext();\n        }\n        // only decrease deps count if suspense is not already resolved\n        if (isInPendingSuspense && --suspense.deps === 0) {\n          suspense.resolve();\n        }\n      });\n    },\n    unmount: function unmount(parentSuspense, doRemove) {\n      suspense.isUnmounted = true;\n      if (suspense.activeBranch) {\n        _unmount2(suspense.activeBranch, parentComponent, parentSuspense, doRemove);\n      }\n      if (suspense.pendingBranch) {\n        _unmount2(suspense.pendingBranch, parentComponent, parentSuspense, doRemove);\n      }\n    }\n  };\n  return suspense;\n}\nfunction hydrateSuspense(node, vnode, parentComponent, parentSuspense, isSVG, slotScopeIds, optimized, rendererInternals, hydrateNode) {\n  /* eslint-disable no-restricted-globals */\n  var suspense = vnode.suspense = createSuspenseBoundary(vnode, parentSuspense, parentComponent, node.parentNode, document.createElement('div'), null, isSVG, slotScopeIds, optimized, rendererInternals, true /* hydrating */);\n  // there are two possible scenarios for server-rendered suspense:\n  // - success: ssr content should be fully resolved\n  // - failure: ssr content should be the fallback branch.\n  // however, on the client we don't really know if it has failed or not\n  // attempt to hydrate the DOM assuming it has succeeded, but we still\n  // need to construct a suspense boundary first\n  var result = hydrateNode(node, suspense.pendingBranch = vnode.ssContent, parentComponent, suspense, slotScopeIds, optimized);\n  if (suspense.deps === 0) {\n    suspense.resolve();\n  }\n  return result;\n  /* eslint-enable no-restricted-globals */\n}\n\nfunction normalizeSuspenseChildren(vnode) {\n  var shapeFlag = vnode.shapeFlag,\n    children = vnode.children;\n  var isSlotChildren = shapeFlag & 32 /* SLOTS_CHILDREN */;\n  vnode.ssContent = normalizeSuspenseSlot(isSlotChildren ? children.default : children);\n  vnode.ssFallback = isSlotChildren ? normalizeSuspenseSlot(children.fallback) : createVNode(Comment);\n}\nfunction normalizeSuspenseSlot(s) {\n  var block;\n  if (isFunction(s)) {\n    var trackBlock = isBlockTreeEnabled && s._c;\n    if (trackBlock) {\n      // disableTracking: false\n      // allow block tracking for compiled slots\n      // (see ./componentRenderContext.ts)\n      s._d = false;\n      openBlock();\n    }\n    s = s();\n    if (trackBlock) {\n      s._d = true;\n      block = currentBlock;\n      closeBlock();\n    }\n  }\n  if (isArray(s)) {\n    var singleChild = filterSingleRoot(s);\n    if (process.env.NODE_ENV !== 'production' && !singleChild) {\n      warn(\"<Suspense> slots expect a single root node.\");\n    }\n    s = singleChild;\n  }\n  s = normalizeVNode(s);\n  if (block && !s.dynamicChildren) {\n    s.dynamicChildren = block.filter(function (c) {\n      return c !== s;\n    });\n  }\n  return s;\n}\nfunction queueEffectWithSuspense(fn, suspense) {\n  if (suspense && suspense.pendingBranch) {\n    if (isArray(fn)) {\n      var _suspense$effects;\n      (_suspense$effects = suspense.effects).push.apply(_suspense$effects, _toConsumableArray(fn));\n    } else {\n      suspense.effects.push(fn);\n    }\n  } else {\n    queuePostFlushCb(fn);\n  }\n}\nfunction setActiveBranch(suspense, branch) {\n  suspense.activeBranch = branch;\n  var vnode = suspense.vnode,\n    parentComponent = suspense.parentComponent;\n  var el = vnode.el = branch.el;\n  // in case suspense is the root node of a component,\n  // recursively update the HOC el\n  if (parentComponent && parentComponent.subTree === vnode) {\n    parentComponent.vnode.el = el;\n    updateHOCHostEl(parentComponent, el);\n  }\n}\nfunction provide(key, value) {\n  if (!currentInstance) {\n    if (process.env.NODE_ENV !== 'production') {\n      warn(\"provide() can only be used inside setup().\");\n    }\n  } else {\n    var provides = currentInstance.provides;\n    // by default an instance inherits its parent's provides object\n    // but when it needs to provide values of its own, it creates its\n    // own provides object using parent provides object as prototype.\n    // this way in `inject` we can simply look up injections from direct\n    // parent and let the prototype chain do the work.\n    var parentProvides = currentInstance.parent && currentInstance.parent.provides;\n    if (parentProvides === provides) {\n      provides = currentInstance.provides = Object.create(parentProvides);\n    }\n    // TS doesn't allow symbol as index type\n    provides[key] = value;\n  }\n}\nfunction inject(key, defaultValue) {\n  var treatDefaultAsFactory = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n  // fallback to `currentRenderingInstance` so that this can be called in\n  // a functional component\n  var instance = currentInstance || currentRenderingInstance;\n  if (instance) {\n    // #2400\n    // to support `app.use` plugins,\n    // fallback to appContext's `provides` if the intance is at root\n    var provides = instance.parent == null ? instance.vnode.appContext && instance.vnode.appContext.provides : instance.parent.provides;\n    if (provides && key in provides) {\n      // TS doesn't allow symbol as index type\n      return provides[key];\n    } else if (arguments.length > 1) {\n      return treatDefaultAsFactory && isFunction(defaultValue) ? defaultValue.call(instance.proxy) : defaultValue;\n    } else if (process.env.NODE_ENV !== 'production') {\n      warn(\"injection \\\"\".concat(String(key), \"\\\" not found.\"));\n    }\n  } else if (process.env.NODE_ENV !== 'production') {\n    warn(\"inject() can only be used inside setup() or functional components.\");\n  }\n}\nfunction useTransitionState() {\n  var state = {\n    isMounted: false,\n    isLeaving: false,\n    isUnmounting: false,\n    leavingVNodes: new Map()\n  };\n  onMounted(function () {\n    state.isMounted = true;\n  });\n  onBeforeUnmount(function () {\n    state.isUnmounting = true;\n  });\n  return state;\n}\nvar TransitionHookValidator = [Function, Array];\nvar BaseTransitionImpl = {\n  name: \"BaseTransition\",\n  props: {\n    mode: String,\n    appear: Boolean,\n    persisted: Boolean,\n    // enter\n    onBeforeEnter: TransitionHookValidator,\n    onEnter: TransitionHookValidator,\n    onAfterEnter: TransitionHookValidator,\n    onEnterCancelled: TransitionHookValidator,\n    // leave\n    onBeforeLeave: TransitionHookValidator,\n    onLeave: TransitionHookValidator,\n    onAfterLeave: TransitionHookValidator,\n    onLeaveCancelled: TransitionHookValidator,\n    // appear\n    onBeforeAppear: TransitionHookValidator,\n    onAppear: TransitionHookValidator,\n    onAfterAppear: TransitionHookValidator,\n    onAppearCancelled: TransitionHookValidator\n  },\n  setup: function setup(props, _ref5) {\n    var slots = _ref5.slots;\n    var instance = getCurrentInstance();\n    var state = useTransitionState();\n    var prevTransitionKey;\n    return function () {\n      var children = slots.default && getTransitionRawChildren(slots.default(), true);\n      if (!children || !children.length) {\n        return;\n      }\n      // warn multiple elements\n      if (process.env.NODE_ENV !== 'production' && children.length > 1) {\n        warn('<transition> can only be used on a single element or component. Use ' + '<transition-group> for lists.');\n      }\n      // there's no need to track reactivity for these props so use the raw\n      // props for a bit better perf\n      var rawProps = toRaw(props);\n      var mode = rawProps.mode;\n      // check mode\n      if (process.env.NODE_ENV !== 'production' && mode && mode !== 'in-out' && mode !== 'out-in' && mode !== 'default') {\n        warn(\"invalid <transition> mode: \".concat(mode));\n      }\n      // at this point children has a guaranteed length of 1.\n      var child = children[0];\n      if (state.isLeaving) {\n        return emptyPlaceholder(child);\n      }\n      // in the case of <transition><keep-alive/></transition>, we need to\n      // compare the type of the kept-alive children.\n      var innerChild = getKeepAliveChild(child);\n      if (!innerChild) {\n        return emptyPlaceholder(child);\n      }\n      var enterHooks = resolveTransitionHooks(innerChild, rawProps, state, instance);\n      setTransitionHooks(innerChild, enterHooks);\n      var oldChild = instance.subTree;\n      var oldInnerChild = oldChild && getKeepAliveChild(oldChild);\n      var transitionKeyChanged = false;\n      var getTransitionKey = innerChild.type.getTransitionKey;\n      if (getTransitionKey) {\n        var key = getTransitionKey();\n        if (prevTransitionKey === undefined) {\n          prevTransitionKey = key;\n        } else if (key !== prevTransitionKey) {\n          prevTransitionKey = key;\n          transitionKeyChanged = true;\n        }\n      }\n      // handle mode\n      if (oldInnerChild && oldInnerChild.type !== Comment && (!isSameVNodeType(innerChild, oldInnerChild) || transitionKeyChanged)) {\n        var leavingHooks = resolveTransitionHooks(oldInnerChild, rawProps, state, instance);\n        // update old tree's hooks in case of dynamic transition\n        setTransitionHooks(oldInnerChild, leavingHooks);\n        // switching between different views\n        if (mode === 'out-in') {\n          state.isLeaving = true;\n          // return placeholder node and queue update when leave finishes\n          leavingHooks.afterLeave = function () {\n            state.isLeaving = false;\n            instance.update();\n          };\n          return emptyPlaceholder(child);\n        } else if (mode === 'in-out' && innerChild.type !== Comment) {\n          leavingHooks.delayLeave = function (el, earlyRemove, delayedLeave) {\n            var leavingVNodesCache = getLeavingNodesForType(state, oldInnerChild);\n            leavingVNodesCache[String(oldInnerChild.key)] = oldInnerChild;\n            // early removal callback\n            el._leaveCb = function () {\n              earlyRemove();\n              el._leaveCb = undefined;\n              delete enterHooks.delayedLeave;\n            };\n            enterHooks.delayedLeave = delayedLeave;\n          };\n        }\n      }\n      return child;\n    };\n  }\n};\n// export the public type for h/tsx inference\n// also to avoid inline import() in generated d.ts files\nvar BaseTransition = BaseTransitionImpl;\nfunction getLeavingNodesForType(state, vnode) {\n  var leavingVNodes = state.leavingVNodes;\n  var leavingVNodesCache = leavingVNodes.get(vnode.type);\n  if (!leavingVNodesCache) {\n    leavingVNodesCache = Object.create(null);\n    leavingVNodes.set(vnode.type, leavingVNodesCache);\n  }\n  return leavingVNodesCache;\n}\n// The transition hooks are attached to the vnode as vnode.transition\n// and will be called at appropriate timing in the renderer.\nfunction resolveTransitionHooks(vnode, props, state, instance) {\n  var appear = props.appear,\n    mode = props.mode,\n    _props$persisted = props.persisted,\n    persisted = _props$persisted === void 0 ? false : _props$persisted,\n    onBeforeEnter = props.onBeforeEnter,\n    onEnter = props.onEnter,\n    onAfterEnter = props.onAfterEnter,\n    onEnterCancelled = props.onEnterCancelled,\n    onBeforeLeave = props.onBeforeLeave,\n    onLeave = props.onLeave,\n    onAfterLeave = props.onAfterLeave,\n    onLeaveCancelled = props.onLeaveCancelled,\n    onBeforeAppear = props.onBeforeAppear,\n    onAppear = props.onAppear,\n    onAfterAppear = props.onAfterAppear,\n    onAppearCancelled = props.onAppearCancelled;\n  var key = String(vnode.key);\n  var leavingVNodesCache = getLeavingNodesForType(state, vnode);\n  var callHook = function callHook(hook, args) {\n    hook && callWithAsyncErrorHandling(hook, instance, 9 /* TRANSITION_HOOK */, args);\n  };\n  var hooks = {\n    mode: mode,\n    persisted: persisted,\n    beforeEnter: function beforeEnter(el) {\n      var hook = onBeforeEnter;\n      if (!state.isMounted) {\n        if (appear) {\n          hook = onBeforeAppear || onBeforeEnter;\n        } else {\n          return;\n        }\n      }\n      // for same element (v-show)\n      if (el._leaveCb) {\n        el._leaveCb(true /* cancelled */);\n      }\n      // for toggled element with same key (v-if)\n      var leavingVNode = leavingVNodesCache[key];\n      if (leavingVNode && isSameVNodeType(vnode, leavingVNode) && leavingVNode.el._leaveCb) {\n        // force early removal (not cancelled)\n        leavingVNode.el._leaveCb();\n      }\n      callHook(hook, [el]);\n    },\n    enter: function enter(el) {\n      var hook = onEnter;\n      var afterHook = onAfterEnter;\n      var cancelHook = onEnterCancelled;\n      if (!state.isMounted) {\n        if (appear) {\n          hook = onAppear || onEnter;\n          afterHook = onAfterAppear || onAfterEnter;\n          cancelHook = onAppearCancelled || onEnterCancelled;\n        } else {\n          return;\n        }\n      }\n      var called = false;\n      var done = el._enterCb = function (cancelled) {\n        if (called) return;\n        called = true;\n        if (cancelled) {\n          callHook(cancelHook, [el]);\n        } else {\n          callHook(afterHook, [el]);\n        }\n        if (hooks.delayedLeave) {\n          hooks.delayedLeave();\n        }\n        el._enterCb = undefined;\n      };\n      if (hook) {\n        hook(el, done);\n        if (hook.length <= 1) {\n          done();\n        }\n      } else {\n        done();\n      }\n    },\n    leave: function leave(el, remove) {\n      var key = String(vnode.key);\n      if (el._enterCb) {\n        el._enterCb(true /* cancelled */);\n      }\n\n      if (state.isUnmounting) {\n        return remove();\n      }\n      callHook(onBeforeLeave, [el]);\n      var called = false;\n      var done = el._leaveCb = function (cancelled) {\n        if (called) return;\n        called = true;\n        remove();\n        if (cancelled) {\n          callHook(onLeaveCancelled, [el]);\n        } else {\n          callHook(onAfterLeave, [el]);\n        }\n        el._leaveCb = undefined;\n        if (leavingVNodesCache[key] === vnode) {\n          delete leavingVNodesCache[key];\n        }\n      };\n      leavingVNodesCache[key] = vnode;\n      if (onLeave) {\n        onLeave(el, done);\n        if (onLeave.length <= 1) {\n          done();\n        }\n      } else {\n        done();\n      }\n    },\n    clone: function clone(vnode) {\n      return resolveTransitionHooks(vnode, props, state, instance);\n    }\n  };\n  return hooks;\n}\n// the placeholder really only handles one special case: KeepAlive\n// in the case of a KeepAlive in a leave phase we need to return a KeepAlive\n// placeholder with empty content to avoid the KeepAlive instance from being\n// unmounted.\nfunction emptyPlaceholder(vnode) {\n  if (isKeepAlive(vnode)) {\n    vnode = cloneVNode(vnode);\n    vnode.children = null;\n    return vnode;\n  }\n}\nfunction getKeepAliveChild(vnode) {\n  return isKeepAlive(vnode) ? vnode.children ? vnode.children[0] : undefined : vnode;\n}\nfunction setTransitionHooks(vnode, hooks) {\n  if (vnode.shapeFlag & 6 /* COMPONENT */ && vnode.component) {\n    setTransitionHooks(vnode.component.subTree, hooks);\n  } else if (vnode.shapeFlag & 128 /* SUSPENSE */) {\n    vnode.ssContent.transition = hooks.clone(vnode.ssContent);\n    vnode.ssFallback.transition = hooks.clone(vnode.ssFallback);\n  } else {\n    vnode.transition = hooks;\n  }\n}\nfunction getTransitionRawChildren(children) {\n  var keepComment = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n  var ret = [];\n  var keyedFragmentCount = 0;\n  for (var i = 0; i < children.length; i++) {\n    var child = children[i];\n    // handle fragment children case, e.g. v-for\n    if (child.type === Fragment) {\n      if (child.patchFlag & 128 /* KEYED_FRAGMENT */) keyedFragmentCount++;\n      ret = ret.concat(getTransitionRawChildren(child.children, keepComment));\n    }\n    // comment placeholders should be skipped, e.g. v-if\n    else if (keepComment || child.type !== Comment) {\n      ret.push(child);\n    }\n  }\n  // #1126 if a transition children list contains multiple sub fragments, these\n  // fragments will be merged into a flat children array. Since each v-for\n  // fragment may contain different static bindings inside, we need to de-op\n  // these children to force full diffs to ensure correct behavior.\n  if (keyedFragmentCount > 1) {\n    for (var _i2 = 0; _i2 < ret.length; _i2++) {\n      ret[_i2].patchFlag = -2 /* BAIL */;\n    }\n  }\n\n  return ret;\n}\n\n// implementation, close to no-op\nfunction defineComponent(options) {\n  return isFunction(options) ? {\n    setup: options,\n    name: options.name\n  } : options;\n}\nvar isAsyncWrapper = function isAsyncWrapper(i) {\n  return !!i.type.__asyncLoader;\n};\nfunction defineAsyncComponent(source) {\n  if (isFunction(source)) {\n    source = {\n      loader: source\n    };\n  }\n  var _source = source,\n    loader = _source.loader,\n    loadingComponent = _source.loadingComponent,\n    errorComponent = _source.errorComponent,\n    _source$delay = _source.delay,\n    delay = _source$delay === void 0 ? 200 : _source$delay,\n    timeout = _source.timeout,\n    _source$suspensible = _source.suspensible,\n    suspensible = _source$suspensible === void 0 ? true : _source$suspensible,\n    userOnError = _source.onError;\n  var pendingRequest = null;\n  var resolvedComp;\n  var retries = 0;\n  var retry = function retry() {\n    retries++;\n    pendingRequest = null;\n    return load();\n  };\n  var load = function load() {\n    var thisRequest;\n    return pendingRequest || (thisRequest = pendingRequest = loader().catch(function (err) {\n      err = err instanceof Error ? err : new Error(String(err));\n      if (userOnError) {\n        return new Promise(function (resolve, reject) {\n          var userRetry = function userRetry() {\n            return resolve(retry());\n          };\n          var userFail = function userFail() {\n            return reject(err);\n          };\n          userOnError(err, userRetry, userFail, retries + 1);\n        });\n      } else {\n        throw err;\n      }\n    }).then(function (comp) {\n      if (thisRequest !== pendingRequest && pendingRequest) {\n        return pendingRequest;\n      }\n      if (process.env.NODE_ENV !== 'production' && !comp) {\n        warn(\"Async component loader resolved to undefined. \" + \"If you are using retry(), make sure to return its return value.\");\n      }\n      // interop module default\n      if (comp && (comp.__esModule || comp[Symbol.toStringTag] === 'Module')) {\n        comp = comp.default;\n      }\n      if (process.env.NODE_ENV !== 'production' && comp && !isObject(comp) && !isFunction(comp)) {\n        throw new Error(\"Invalid async component load result: \".concat(comp));\n      }\n      resolvedComp = comp;\n      return comp;\n    }));\n  };\n  return defineComponent({\n    name: 'AsyncComponentWrapper',\n    __asyncLoader: load,\n    get __asyncResolved() {\n      return resolvedComp;\n    },\n    setup: function setup() {\n      var instance = currentInstance;\n      // already resolved\n      if (resolvedComp) {\n        return function () {\n          return createInnerComp(resolvedComp, instance);\n        };\n      }\n      var onError = function onError(err) {\n        pendingRequest = null;\n        handleError(err, instance, 13 /* ASYNC_COMPONENT_LOADER */, !errorComponent /* do not throw in dev if user provided error component */);\n      };\n      // suspense-controlled or SSR.\n      if (suspensible && instance.suspense || isInSSRComponentSetup) {\n        return load().then(function (comp) {\n          return function () {\n            return createInnerComp(comp, instance);\n          };\n        }).catch(function (err) {\n          onError(err);\n          return function () {\n            return errorComponent ? createVNode(errorComponent, {\n              error: err\n            }) : null;\n          };\n        });\n      }\n      var loaded = ref(false);\n      var error = ref();\n      var delayed = ref(!!delay);\n      if (delay) {\n        setTimeout(function () {\n          delayed.value = false;\n        }, delay);\n      }\n      if (timeout != null) {\n        setTimeout(function () {\n          if (!loaded.value && !error.value) {\n            var err = new Error(\"Async component timed out after \".concat(timeout, \"ms.\"));\n            onError(err);\n            error.value = err;\n          }\n        }, timeout);\n      }\n      load().then(function () {\n        loaded.value = true;\n        if (instance.parent && isKeepAlive(instance.parent.vnode)) {\n          // parent is keep-alive, force update so the loaded component's\n          // name is taken into account\n          queueJob(instance.parent.update);\n        }\n      }).catch(function (err) {\n        onError(err);\n        error.value = err;\n      });\n      return function () {\n        if (loaded.value && resolvedComp) {\n          return createInnerComp(resolvedComp, instance);\n        } else if (error.value && errorComponent) {\n          return createVNode(errorComponent, {\n            error: error.value\n          });\n        } else if (loadingComponent && !delayed.value) {\n          return createVNode(loadingComponent);\n        }\n      };\n    }\n  });\n}\nfunction createInnerComp(comp, _ref6) {\n  var _ref6$vnode = _ref6.vnode,\n    ref = _ref6$vnode.ref,\n    props = _ref6$vnode.props,\n    children = _ref6$vnode.children;\n  var vnode = createVNode(comp, props, children);\n  // ensure inner component inherits the async wrapper's ref owner\n  vnode.ref = ref;\n  return vnode;\n}\nvar isKeepAlive = function isKeepAlive(vnode) {\n  return vnode.type.__isKeepAlive;\n};\nvar KeepAliveImpl = {\n  name: \"KeepAlive\",\n  // Marker for special handling inside the renderer. We are not using a ===\n  // check directly on KeepAlive in the renderer, because importing it directly\n  // would prevent it from being tree-shaken.\n  __isKeepAlive: true,\n  props: {\n    include: [String, RegExp, Array],\n    exclude: [String, RegExp, Array],\n    max: [String, Number]\n  },\n  setup: function setup(props, _ref7) {\n    var slots = _ref7.slots;\n    var instance = getCurrentInstance();\n    // KeepAlive communicates with the instantiated renderer via the\n    // ctx where the renderer passes in its internals,\n    // and the KeepAlive instance exposes activate/deactivate implementations.\n    // The whole point of this is to avoid importing KeepAlive directly in the\n    // renderer to facilitate tree-shaking.\n    var sharedContext = instance.ctx;\n    // if the internal renderer is not registered, it indicates that this is server-side rendering,\n    // for KeepAlive, we just need to render its children\n    if (!sharedContext.renderer) {\n      return slots.default;\n    }\n    var cache = new Map();\n    var keys = new Set();\n    var current = null;\n    if (process.env.NODE_ENV !== 'production' || __VUE_PROD_DEVTOOLS__) {\n      instance.__v_cache = cache;\n    }\n    var parentSuspense = instance.suspense;\n    var _sharedContext$render = sharedContext.renderer,\n      patch = _sharedContext$render.p,\n      move = _sharedContext$render.m,\n      _unmount = _sharedContext$render.um,\n      createElement = _sharedContext$render.o.createElement;\n    var storageContainer = createElement('div');\n    sharedContext.activate = function (vnode, container, anchor, isSVG, optimized) {\n      var instance = vnode.component;\n      move(vnode, container, anchor, 0 /* ENTER */, parentSuspense);\n      // in case props have changed\n      patch(instance.vnode, vnode, container, anchor, instance, parentSuspense, isSVG, vnode.slotScopeIds, optimized);\n      queuePostRenderEffect(function () {\n        instance.isDeactivated = false;\n        if (instance.a) {\n          invokeArrayFns(instance.a);\n        }\n        var vnodeHook = vnode.props && vnode.props.onVnodeMounted;\n        if (vnodeHook) {\n          invokeVNodeHook(vnodeHook, instance.parent, vnode);\n        }\n      }, parentSuspense);\n      if (process.env.NODE_ENV !== 'production' || __VUE_PROD_DEVTOOLS__) {\n        // Update components tree\n        devtoolsComponentAdded(instance);\n      }\n    };\n    sharedContext.deactivate = function (vnode) {\n      var instance = vnode.component;\n      move(vnode, storageContainer, null, 1 /* LEAVE */, parentSuspense);\n      queuePostRenderEffect(function () {\n        if (instance.da) {\n          invokeArrayFns(instance.da);\n        }\n        var vnodeHook = vnode.props && vnode.props.onVnodeUnmounted;\n        if (vnodeHook) {\n          invokeVNodeHook(vnodeHook, instance.parent, vnode);\n        }\n        instance.isDeactivated = true;\n      }, parentSuspense);\n      if (process.env.NODE_ENV !== 'production' || __VUE_PROD_DEVTOOLS__) {\n        // Update components tree\n        devtoolsComponentAdded(instance);\n      }\n    };\n    function unmount(vnode) {\n      // reset the shapeFlag so it can be properly unmounted\n      resetShapeFlag(vnode);\n      _unmount(vnode, instance, parentSuspense);\n    }\n    function pruneCache(filter) {\n      cache.forEach(function (vnode, key) {\n        var name = getComponentName(vnode.type);\n        if (name && (!filter || !filter(name))) {\n          pruneCacheEntry(key);\n        }\n      });\n    }\n    function pruneCacheEntry(key) {\n      var cached = cache.get(key);\n      if (!current || cached.type !== current.type) {\n        unmount(cached);\n      } else if (current) {\n        // current active instance should no longer be kept-alive.\n        // we can't unmount it now but it might be later, so reset its flag now.\n        resetShapeFlag(current);\n      }\n      cache.delete(key);\n      keys.delete(key);\n    }\n    // prune cache on include/exclude prop change\n    watch(function () {\n      return [props.include, props.exclude];\n    }, function (_ref8) {\n      var _ref9 = _slicedToArray(_ref8, 2),\n        include = _ref9[0],\n        exclude = _ref9[1];\n      include && pruneCache(function (name) {\n        return matches(include, name);\n      });\n      exclude && pruneCache(function (name) {\n        return !matches(exclude, name);\n      });\n    },\n    // prune post-render after `current` has been updated\n    {\n      flush: 'post',\n      deep: true\n    });\n    // cache sub tree after render\n    var pendingCacheKey = null;\n    var cacheSubtree = function cacheSubtree() {\n      // fix #1621, the pendingCacheKey could be 0\n      if (pendingCacheKey != null) {\n        cache.set(pendingCacheKey, getInnerChild(instance.subTree));\n      }\n    };\n    onMounted(cacheSubtree);\n    onUpdated(cacheSubtree);\n    onBeforeUnmount(function () {\n      cache.forEach(function (cached) {\n        var subTree = instance.subTree,\n          suspense = instance.suspense;\n        var vnode = getInnerChild(subTree);\n        if (cached.type === vnode.type) {\n          // current instance will be unmounted as part of keep-alive's unmount\n          resetShapeFlag(vnode);\n          // but invoke its deactivated hook here\n          var da = vnode.component.da;\n          da && queuePostRenderEffect(da, suspense);\n          return;\n        }\n        unmount(cached);\n      });\n    });\n    return function () {\n      pendingCacheKey = null;\n      if (!slots.default) {\n        return null;\n      }\n      var children = slots.default();\n      var rawVNode = children[0];\n      if (children.length > 1) {\n        if (process.env.NODE_ENV !== 'production') {\n          warn(\"KeepAlive should contain exactly one component child.\");\n        }\n        current = null;\n        return children;\n      } else if (!isVNode(rawVNode) || !(rawVNode.shapeFlag & 4 /* STATEFUL_COMPONENT */) && !(rawVNode.shapeFlag & 128 /* SUSPENSE */)) {\n        current = null;\n        return rawVNode;\n      }\n      var vnode = getInnerChild(rawVNode);\n      var comp = vnode.type;\n      // for async components, name check should be based in its loaded\n      // inner component if available\n      var name = getComponentName(isAsyncWrapper(vnode) ? vnode.type.__asyncResolved || {} : comp);\n      var include = props.include,\n        exclude = props.exclude,\n        max = props.max;\n      if (include && (!name || !matches(include, name)) || exclude && name && matches(exclude, name)) {\n        current = vnode;\n        return rawVNode;\n      }\n      var key = vnode.key == null ? comp : vnode.key;\n      var cachedVNode = cache.get(key);\n      // clone vnode if it's reused because we are going to mutate it\n      if (vnode.el) {\n        vnode = cloneVNode(vnode);\n        if (rawVNode.shapeFlag & 128 /* SUSPENSE */) {\n          rawVNode.ssContent = vnode;\n        }\n      }\n      // #1513 it's possible for the returned vnode to be cloned due to attr\n      // fallthrough or scopeId, so the vnode here may not be the final vnode\n      // that is mounted. Instead of caching it directly, we store the pending\n      // key and cache `instance.subTree` (the normalized vnode) in\n      // beforeMount/beforeUpdate hooks.\n      pendingCacheKey = key;\n      if (cachedVNode) {\n        // copy over mounted state\n        vnode.el = cachedVNode.el;\n        vnode.component = cachedVNode.component;\n        if (vnode.transition) {\n          // recursively update transition hooks on subTree\n          setTransitionHooks(vnode, vnode.transition);\n        }\n        // avoid vnode being mounted as fresh\n        vnode.shapeFlag |= 512 /* COMPONENT_KEPT_ALIVE */;\n        // make this key the freshest\n        keys.delete(key);\n        keys.add(key);\n      } else {\n        keys.add(key);\n        // prune oldest entry\n        if (max && keys.size > parseInt(max, 10)) {\n          pruneCacheEntry(keys.values().next().value);\n        }\n      }\n      // avoid vnode being unmounted\n      vnode.shapeFlag |= 256 /* COMPONENT_SHOULD_KEEP_ALIVE */;\n      current = vnode;\n      return rawVNode;\n    };\n  }\n};\n// export the public type for h/tsx inference\n// also to avoid inline import() in generated d.ts files\nvar KeepAlive = KeepAliveImpl;\nfunction matches(pattern, name) {\n  if (isArray(pattern)) {\n    return pattern.some(function (p) {\n      return matches(p, name);\n    });\n  } else if (isString(pattern)) {\n    return pattern.split(',').indexOf(name) > -1;\n  } else if (pattern.test) {\n    return pattern.test(name);\n  }\n  /* istanbul ignore next */\n  return false;\n}\nfunction onActivated(hook, target) {\n  registerKeepAliveHook(hook, \"a\" /* ACTIVATED */, target);\n}\nfunction onDeactivated(hook, target) {\n  registerKeepAliveHook(hook, \"da\" /* DEACTIVATED */, target);\n}\nfunction registerKeepAliveHook(hook, type) {\n  var target = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : currentInstance;\n  // cache the deactivate branch check wrapper for injected hooks so the same\n  // hook can be properly deduped by the scheduler. \"__wdc\" stands for \"with\n  // deactivation check\".\n  var wrappedHook = hook.__wdc || (hook.__wdc = function () {\n    // only fire the hook if the target instance is NOT in a deactivated branch.\n    var current = target;\n    while (current) {\n      if (current.isDeactivated) {\n        return;\n      }\n      current = current.parent;\n    }\n    return hook();\n  });\n  injectHook(type, wrappedHook, target);\n  // In addition to registering it on the target instance, we walk up the parent\n  // chain and register it on all ancestor instances that are keep-alive roots.\n  // This avoids the need to walk the entire component tree when invoking these\n  // hooks, and more importantly, avoids the need to track child components in\n  // arrays.\n  if (target) {\n    var current = target.parent;\n    while (current && current.parent) {\n      if (isKeepAlive(current.parent.vnode)) {\n        injectToKeepAliveRoot(wrappedHook, type, target, current);\n      }\n      current = current.parent;\n    }\n  }\n}\nfunction injectToKeepAliveRoot(hook, type, target, keepAliveRoot) {\n  // injectHook wraps the original for error handling, so make sure to remove\n  // the wrapped version.\n  var injected = injectHook(type, hook, keepAliveRoot, true /* prepend */);\n  onUnmounted(function () {\n    remove(keepAliveRoot[type], injected);\n  }, target);\n}\nfunction resetShapeFlag(vnode) {\n  var shapeFlag = vnode.shapeFlag;\n  if (shapeFlag & 256 /* COMPONENT_SHOULD_KEEP_ALIVE */) {\n    shapeFlag -= 256 /* COMPONENT_SHOULD_KEEP_ALIVE */;\n  }\n\n  if (shapeFlag & 512 /* COMPONENT_KEPT_ALIVE */) {\n    shapeFlag -= 512 /* COMPONENT_KEPT_ALIVE */;\n  }\n\n  vnode.shapeFlag = shapeFlag;\n}\nfunction getInnerChild(vnode) {\n  return vnode.shapeFlag & 128 /* SUSPENSE */ ? vnode.ssContent : vnode;\n}\nfunction injectHook(type, hook) {\n  var target = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : currentInstance;\n  var prepend = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : false;\n  if (target) {\n    var hooks = target[type] || (target[type] = []);\n    // cache the error handling wrapper for injected hooks so the same hook\n    // can be properly deduped by the scheduler. \"__weh\" stands for \"with error\n    // handling\".\n    var wrappedHook = hook.__weh || (hook.__weh = function () {\n      if (target.isUnmounted) {\n        return;\n      }\n      // disable tracking inside all lifecycle hooks\n      // since they can potentially be called inside effects.\n      pauseTracking();\n      // Set currentInstance during hook invocation.\n      // This assumes the hook does not synchronously trigger other hooks, which\n      // can only be false when the user does something really funky.\n      setCurrentInstance(target);\n      for (var _len3 = arguments.length, args = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {\n        args[_key3] = arguments[_key3];\n      }\n      var res = callWithAsyncErrorHandling(hook, target, type, args);\n      unsetCurrentInstance();\n      resetTracking();\n      return res;\n    });\n    if (prepend) {\n      hooks.unshift(wrappedHook);\n    } else {\n      hooks.push(wrappedHook);\n    }\n    return wrappedHook;\n  } else if (process.env.NODE_ENV !== 'production') {\n    var apiName = toHandlerKey(ErrorTypeStrings[type].replace(/ hook$/, ''));\n    warn(\"\".concat(apiName, \" is called when there is no active component instance to be \") + \"associated with. \" + \"Lifecycle injection APIs can only be used during execution of setup().\" + (\" If you are using async setup(), make sure to register lifecycle \" + \"hooks before the first await statement.\"));\n  }\n}\nvar createHook = function createHook(lifecycle) {\n  return function (hook) {\n    var target = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : currentInstance;\n    return (\n      // post-create lifecycle registrations are noops during SSR (except for serverPrefetch)\n      (!isInSSRComponentSetup || lifecycle === \"sp\" /* SERVER_PREFETCH */) && injectHook(lifecycle, hook, target)\n    );\n  };\n};\nvar onBeforeMount = createHook(\"bm\" /* BEFORE_MOUNT */);\nvar onMounted = createHook(\"m\" /* MOUNTED */);\nvar onBeforeUpdate = createHook(\"bu\" /* BEFORE_UPDATE */);\nvar onUpdated = createHook(\"u\" /* UPDATED */);\nvar onBeforeUnmount = createHook(\"bum\" /* BEFORE_UNMOUNT */);\nvar onUnmounted = createHook(\"um\" /* UNMOUNTED */);\nvar onServerPrefetch = createHook(\"sp\" /* SERVER_PREFETCH */);\nvar onRenderTriggered = createHook(\"rtg\" /* RENDER_TRIGGERED */);\nvar onRenderTracked = createHook(\"rtc\" /* RENDER_TRACKED */);\nfunction onErrorCaptured(hook) {\n  var target = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : currentInstance;\n  injectHook(\"ec\" /* ERROR_CAPTURED */, hook, target);\n}\nfunction createDuplicateChecker() {\n  var cache = Object.create(null);\n  return function (type, key) {\n    if (cache[key]) {\n      warn(\"\".concat(type, \" property \\\"\").concat(key, \"\\\" is already defined in \").concat(cache[key], \".\"));\n    } else {\n      cache[key] = type;\n    }\n  };\n}\nvar shouldCacheAccess = true;\nfunction applyOptions(instance) {\n  var options = resolveMergedOptions(instance);\n  var publicThis = instance.proxy;\n  var ctx = instance.ctx;\n  // do not cache property access on public proxy during state initialization\n  shouldCacheAccess = false;\n  // call beforeCreate first before accessing other options since\n  // the hook may mutate resolved options (#2791)\n  if (options.beforeCreate) {\n    callHook(options.beforeCreate, instance, \"bc\" /* BEFORE_CREATE */);\n  }\n\n  var dataOptions = options.data,\n    computedOptions = options.computed,\n    methods = options.methods,\n    watchOptions = options.watch,\n    provideOptions = options.provide,\n    injectOptions = options.inject,\n    created = options.created,\n    beforeMount = options.beforeMount,\n    mounted = options.mounted,\n    beforeUpdate = options.beforeUpdate,\n    updated = options.updated,\n    activated = options.activated,\n    deactivated = options.deactivated,\n    beforeDestroy = options.beforeDestroy,\n    beforeUnmount = options.beforeUnmount,\n    destroyed = options.destroyed,\n    unmounted = options.unmounted,\n    render = options.render,\n    renderTracked = options.renderTracked,\n    renderTriggered = options.renderTriggered,\n    errorCaptured = options.errorCaptured,\n    serverPrefetch = options.serverPrefetch,\n    expose = options.expose,\n    inheritAttrs = options.inheritAttrs,\n    components = options.components,\n    directives = options.directives,\n    filters = options.filters;\n  var checkDuplicateProperties = process.env.NODE_ENV !== 'production' ? createDuplicateChecker() : null;\n  if (process.env.NODE_ENV !== 'production') {\n    var _instance$propsOption3 = _slicedToArray(instance.propsOptions, 1),\n      propsOptions = _instance$propsOption3[0];\n    if (propsOptions) {\n      for (var key in propsOptions) {\n        checkDuplicateProperties(\"Props\" /* PROPS */, key);\n      }\n    }\n  }\n  // options initialization order (to be consistent with Vue 2):\n  // - props (already done outside of this function)\n  // - inject\n  // - methods\n  // - data (deferred since it relies on `this` access)\n  // - computed\n  // - watch (deferred since it relies on `this` access)\n  if (injectOptions) {\n    resolveInjections(injectOptions, ctx, checkDuplicateProperties, instance.appContext.config.unwrapInjectedRef);\n  }\n  if (methods) {\n    for (var _key4 in methods) {\n      var methodHandler = methods[_key4];\n      if (isFunction(methodHandler)) {\n        // In dev mode, we use the `createRenderContext` function to define\n        // methods to the proxy target, and those are read-only but\n        // reconfigurable, so it needs to be redefined here\n        if (process.env.NODE_ENV !== 'production') {\n          Object.defineProperty(ctx, _key4, {\n            value: methodHandler.bind(publicThis),\n            configurable: true,\n            enumerable: true,\n            writable: true\n          });\n        } else {\n          ctx[_key4] = methodHandler.bind(publicThis);\n        }\n        if (process.env.NODE_ENV !== 'production') {\n          checkDuplicateProperties(\"Methods\" /* METHODS */, _key4);\n        }\n      } else if (process.env.NODE_ENV !== 'production') {\n        warn(\"Method \\\"\".concat(_key4, \"\\\" has type \\\"\").concat(_typeof(methodHandler), \"\\\" in the component definition. \") + \"Did you reference the function correctly?\");\n      }\n    }\n  }\n  if (dataOptions) {\n    if (process.env.NODE_ENV !== 'production' && !isFunction(dataOptions)) {\n      warn(\"The data option must be a function. \" + \"Plain object usage is no longer supported.\");\n    }\n    var data = dataOptions.call(publicThis, publicThis);\n    if (process.env.NODE_ENV !== 'production' && isPromise(data)) {\n      warn(\"data() returned a Promise - note data() cannot be async; If you \" + \"intend to perform data fetching before component renders, use \" + \"async setup() + <Suspense>.\");\n    }\n    if (!isObject(data)) {\n      process.env.NODE_ENV !== 'production' && warn(\"data() should return an object.\");\n    } else {\n      instance.data = reactive(data);\n      if (process.env.NODE_ENV !== 'production') {\n        var _loop = function _loop(_key5) {\n          checkDuplicateProperties(\"Data\" /* DATA */, _key5);\n          // expose data on ctx during dev\n          if (_key5[0] !== '$' && _key5[0] !== '_') {\n            Object.defineProperty(ctx, _key5, {\n              configurable: true,\n              enumerable: true,\n              get: function get() {\n                return data[_key5];\n              },\n              set: NOOP\n            });\n          }\n        };\n        for (var _key5 in data) {\n          _loop(_key5);\n        }\n      }\n    }\n  }\n  // state initialization complete at this point - start caching access\n  shouldCacheAccess = true;\n  if (computedOptions) {\n    var _loop2 = function _loop2(_key6) {\n      var opt = computedOptions[_key6];\n      var get = isFunction(opt) ? opt.bind(publicThis, publicThis) : isFunction(opt.get) ? opt.get.bind(publicThis, publicThis) : NOOP;\n      if (process.env.NODE_ENV !== 'production' && get === NOOP) {\n        warn(\"Computed property \\\"\".concat(_key6, \"\\\" has no getter.\"));\n      }\n      var set = !isFunction(opt) && isFunction(opt.set) ? opt.set.bind(publicThis) : process.env.NODE_ENV !== 'production' ? function () {\n        warn(\"Write operation failed: computed property \\\"\".concat(_key6, \"\\\" is readonly.\"));\n      } : NOOP;\n      var c = computed({\n        get: get,\n        set: set\n      });\n      Object.defineProperty(ctx, _key6, {\n        enumerable: true,\n        configurable: true,\n        get: function get() {\n          return c.value;\n        },\n        set: function set(v) {\n          return c.value = v;\n        }\n      });\n      if (process.env.NODE_ENV !== 'production') {\n        checkDuplicateProperties(\"Computed\" /* COMPUTED */, _key6);\n      }\n    };\n    for (var _key6 in computedOptions) {\n      _loop2(_key6);\n    }\n  }\n  if (watchOptions) {\n    for (var _key7 in watchOptions) {\n      createWatcher(watchOptions[_key7], ctx, publicThis, _key7);\n    }\n  }\n  if (provideOptions) {\n    var provides = isFunction(provideOptions) ? provideOptions.call(publicThis) : provideOptions;\n    Reflect.ownKeys(provides).forEach(function (key) {\n      provide(key, provides[key]);\n    });\n  }\n  if (created) {\n    callHook(created, instance, \"c\" /* CREATED */);\n  }\n\n  function registerLifecycleHook(register, hook) {\n    if (isArray(hook)) {\n      hook.forEach(function (_hook) {\n        return register(_hook.bind(publicThis));\n      });\n    } else if (hook) {\n      register(hook.bind(publicThis));\n    }\n  }\n  registerLifecycleHook(onBeforeMount, beforeMount);\n  registerLifecycleHook(onMounted, mounted);\n  registerLifecycleHook(onBeforeUpdate, beforeUpdate);\n  registerLifecycleHook(onUpdated, updated);\n  registerLifecycleHook(onActivated, activated);\n  registerLifecycleHook(onDeactivated, deactivated);\n  registerLifecycleHook(onErrorCaptured, errorCaptured);\n  registerLifecycleHook(onRenderTracked, renderTracked);\n  registerLifecycleHook(onRenderTriggered, renderTriggered);\n  registerLifecycleHook(onBeforeUnmount, beforeUnmount);\n  registerLifecycleHook(onUnmounted, unmounted);\n  registerLifecycleHook(onServerPrefetch, serverPrefetch);\n  if (isArray(expose)) {\n    if (expose.length) {\n      var exposed = instance.exposed || (instance.exposed = {});\n      expose.forEach(function (key) {\n        Object.defineProperty(exposed, key, {\n          get: function get() {\n            return publicThis[key];\n          },\n          set: function set(val) {\n            return publicThis[key] = val;\n          }\n        });\n      });\n    } else if (!instance.exposed) {\n      instance.exposed = {};\n    }\n  }\n  // options that are handled when creating the instance but also need to be\n  // applied from mixins\n  if (render && instance.render === NOOP) {\n    instance.render = render;\n  }\n  if (inheritAttrs != null) {\n    instance.inheritAttrs = inheritAttrs;\n  }\n  // asset options.\n  if (components) instance.components = components;\n  if (directives) instance.directives = directives;\n}\nfunction resolveInjections(injectOptions, ctx) {\n  var checkDuplicateProperties = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : NOOP;\n  var unwrapRef = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : false;\n  if (isArray(injectOptions)) {\n    injectOptions = normalizeInject(injectOptions);\n  }\n  var _loop3 = function _loop3() {\n    var opt = injectOptions[key];\n    var injected;\n    if (isObject(opt)) {\n      if ('default' in opt) {\n        injected = inject(opt.from || key, opt.default, true /* treat default function as factory */);\n      } else {\n        injected = inject(opt.from || key);\n      }\n    } else {\n      injected = inject(opt);\n    }\n    if (isRef(injected)) {\n      // TODO remove the check in 3.3\n      if (unwrapRef) {\n        Object.defineProperty(ctx, key, {\n          enumerable: true,\n          configurable: true,\n          get: function get() {\n            return injected.value;\n          },\n          set: function set(v) {\n            return injected.value = v;\n          }\n        });\n      } else {\n        if (process.env.NODE_ENV !== 'production') {\n          warn(\"injected property \\\"\".concat(key, \"\\\" is a ref and will be auto-unwrapped \") + \"and no longer needs `.value` in the next minor release. \" + \"To opt-in to the new behavior now, \" + \"set `app.config.unwrapInjectedRef = true` (this config is \" + \"temporary and will not be needed in the future.)\");\n        }\n        ctx[key] = injected;\n      }\n    } else {\n      ctx[key] = injected;\n    }\n    if (process.env.NODE_ENV !== 'production') {\n      checkDuplicateProperties(\"Inject\" /* INJECT */, key);\n    }\n  };\n  for (var key in injectOptions) {\n    _loop3();\n  }\n}\nfunction callHook(hook, instance, type) {\n  callWithAsyncErrorHandling(isArray(hook) ? hook.map(function (h) {\n    return h.bind(instance.proxy);\n  }) : hook.bind(instance.proxy), instance, type);\n}\nfunction createWatcher(raw, ctx, publicThis, key) {\n  var getter = key.includes('.') ? createPathGetter(publicThis, key) : function () {\n    return publicThis[key];\n  };\n  if (isString(raw)) {\n    var handler = ctx[raw];\n    if (isFunction(handler)) {\n      watch(getter, handler);\n    } else if (process.env.NODE_ENV !== 'production') {\n      warn(\"Invalid watch handler specified by key \\\"\".concat(raw, \"\\\"\"), handler);\n    }\n  } else if (isFunction(raw)) {\n    watch(getter, raw.bind(publicThis));\n  } else if (isObject(raw)) {\n    if (isArray(raw)) {\n      raw.forEach(function (r) {\n        return createWatcher(r, ctx, publicThis, key);\n      });\n    } else {\n      var _handler = isFunction(raw.handler) ? raw.handler.bind(publicThis) : ctx[raw.handler];\n      if (isFunction(_handler)) {\n        watch(getter, _handler, raw);\n      } else if (process.env.NODE_ENV !== 'production') {\n        warn(\"Invalid watch handler specified by key \\\"\".concat(raw.handler, \"\\\"\"), _handler);\n      }\n    }\n  } else if (process.env.NODE_ENV !== 'production') {\n    warn(\"Invalid watch option: \\\"\".concat(key, \"\\\"\"), raw);\n  }\n}\n/**\r\n * Resolve merged options and cache it on the component.\r\n * This is done only once per-component since the merging does not involve\r\n * instances.\r\n */\nfunction resolveMergedOptions(instance) {\n  var base = instance.type;\n  var mixins = base.mixins,\n    extendsOptions = base.extends;\n  var _instance$appContext = instance.appContext,\n    globalMixins = _instance$appContext.mixins,\n    cache = _instance$appContext.optionsCache,\n    optionMergeStrategies = _instance$appContext.config.optionMergeStrategies;\n  var cached = cache.get(base);\n  var resolved;\n  if (cached) {\n    resolved = cached;\n  } else if (!globalMixins.length && !mixins && !extendsOptions) {\n    {\n      resolved = base;\n    }\n  } else {\n    resolved = {};\n    if (globalMixins.length) {\n      globalMixins.forEach(function (m) {\n        return mergeOptions(resolved, m, optionMergeStrategies, true);\n      });\n    }\n    mergeOptions(resolved, base, optionMergeStrategies);\n  }\n  cache.set(base, resolved);\n  return resolved;\n}\nfunction mergeOptions(to, from, strats) {\n  var asMixin = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : false;\n  var mixins = from.mixins,\n    extendsOptions = from.extends;\n  if (extendsOptions) {\n    mergeOptions(to, extendsOptions, strats, true);\n  }\n  if (mixins) {\n    mixins.forEach(function (m) {\n      return mergeOptions(to, m, strats, true);\n    });\n  }\n  for (var key in from) {\n    if (asMixin && key === 'expose') {\n      process.env.NODE_ENV !== 'production' && warn(\"\\\"expose\\\" option is ignored when declared in mixins or extends. \" + \"It should only be declared in the base component itself.\");\n    } else {\n      var strat = internalOptionMergeStrats[key] || strats && strats[key];\n      to[key] = strat ? strat(to[key], from[key]) : from[key];\n    }\n  }\n  return to;\n}\nvar internalOptionMergeStrats = {\n  data: mergeDataFn,\n  props: mergeObjectOptions,\n  emits: mergeObjectOptions,\n  // objects\n  methods: mergeObjectOptions,\n  computed: mergeObjectOptions,\n  // lifecycle\n  beforeCreate: mergeAsArray,\n  created: mergeAsArray,\n  beforeMount: mergeAsArray,\n  mounted: mergeAsArray,\n  beforeUpdate: mergeAsArray,\n  updated: mergeAsArray,\n  beforeDestroy: mergeAsArray,\n  beforeUnmount: mergeAsArray,\n  destroyed: mergeAsArray,\n  unmounted: mergeAsArray,\n  activated: mergeAsArray,\n  deactivated: mergeAsArray,\n  errorCaptured: mergeAsArray,\n  serverPrefetch: mergeAsArray,\n  // assets\n  components: mergeObjectOptions,\n  directives: mergeObjectOptions,\n  // watch\n  watch: mergeWatchOptions,\n  // provide / inject\n  provide: mergeDataFn,\n  inject: mergeInject\n};\nfunction mergeDataFn(to, from) {\n  if (!from) {\n    return to;\n  }\n  if (!to) {\n    return from;\n  }\n  return function mergedDataFn() {\n    return extend(isFunction(to) ? to.call(this, this) : to, isFunction(from) ? from.call(this, this) : from);\n  };\n}\nfunction mergeInject(to, from) {\n  return mergeObjectOptions(normalizeInject(to), normalizeInject(from));\n}\nfunction normalizeInject(raw) {\n  if (isArray(raw)) {\n    var res = {};\n    for (var i = 0; i < raw.length; i++) {\n      res[raw[i]] = raw[i];\n    }\n    return res;\n  }\n  return raw;\n}\nfunction mergeAsArray(to, from) {\n  return to ? _toConsumableArray(new Set([].concat(to, from))) : from;\n}\nfunction mergeObjectOptions(to, from) {\n  return to ? extend(extend(Object.create(null), to), from) : from;\n}\nfunction mergeWatchOptions(to, from) {\n  if (!to) return from;\n  if (!from) return to;\n  var merged = extend(Object.create(null), to);\n  for (var key in from) {\n    merged[key] = mergeAsArray(to[key], from[key]);\n  }\n  return merged;\n}\nfunction initProps(instance, rawProps, isStateful) {\n  var isSSR = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : false;\n  var props = {};\n  var attrs = {};\n  def(attrs, InternalObjectKey, 1);\n  instance.propsDefaults = Object.create(null);\n  setFullProps(instance, rawProps, props, attrs);\n  // ensure all declared prop keys are present\n  for (var key in instance.propsOptions[0]) {\n    if (!(key in props)) {\n      props[key] = undefined;\n    }\n  }\n  // validation\n  if (process.env.NODE_ENV !== 'production') {\n    validateProps(rawProps || {}, props, instance);\n  }\n  if (isStateful) {\n    // stateful\n    instance.props = isSSR ? props : shallowReactive(props);\n  } else {\n    if (!instance.type.props) {\n      // functional w/ optional props, props === attrs\n      instance.props = attrs;\n    } else {\n      // functional w/ declared props\n      instance.props = props;\n    }\n  }\n  instance.attrs = attrs;\n}\nfunction updateProps(instance, rawProps, rawPrevProps, optimized) {\n  var props = instance.props,\n    attrs = instance.attrs,\n    patchFlag = instance.vnode.patchFlag;\n  var rawCurrentProps = toRaw(props);\n  var _instance$propsOption4 = _slicedToArray(instance.propsOptions, 1),\n    options = _instance$propsOption4[0];\n  var hasAttrsChanged = false;\n  if (\n  // always force full diff in dev\n  // - #1942 if hmr is enabled with sfc component\n  // - vite#872 non-sfc component used by sfc component\n  !(process.env.NODE_ENV !== 'production' && (instance.type.__hmrId || instance.parent && instance.parent.type.__hmrId)) && (optimized || patchFlag > 0) && !(patchFlag & 16 /* FULL_PROPS */)) {\n    if (patchFlag & 8 /* PROPS */) {\n      // Compiler-generated props & no keys change, just set the updated\n      // the props.\n      var propsToUpdate = instance.vnode.dynamicProps;\n      for (var i = 0; i < propsToUpdate.length; i++) {\n        var key = propsToUpdate[i];\n        // PROPS flag guarantees rawProps to be non-null\n        var value = rawProps[key];\n        if (options) {\n          // attr / props separation was done on init and will be consistent\n          // in this code path, so just check if attrs have it.\n          if (hasOwn(attrs, key)) {\n            if (value !== attrs[key]) {\n              attrs[key] = value;\n              hasAttrsChanged = true;\n            }\n          } else {\n            var camelizedKey = camelize(key);\n            props[camelizedKey] = resolvePropValue(options, rawCurrentProps, camelizedKey, value, instance, false /* isAbsent */);\n          }\n        } else {\n          if (value !== attrs[key]) {\n            attrs[key] = value;\n            hasAttrsChanged = true;\n          }\n        }\n      }\n    }\n  } else {\n    // full props update.\n    if (setFullProps(instance, rawProps, props, attrs)) {\n      hasAttrsChanged = true;\n    }\n    // in case of dynamic props, check if we need to delete keys from\n    // the props object\n    var kebabKey;\n    for (var _key8 in rawCurrentProps) {\n      if (!rawProps ||\n      // for camelCase\n      !hasOwn(rawProps, _key8) && (\n      // it's possible the original props was passed in as kebab-case\n      // and converted to camelCase (#955)\n      (kebabKey = hyphenate(_key8)) === _key8 || !hasOwn(rawProps, kebabKey))) {\n        if (options) {\n          if (rawPrevProps && (\n          // for camelCase\n          rawPrevProps[_key8] !== undefined ||\n          // for kebab-case\n          rawPrevProps[kebabKey] !== undefined)) {\n            props[_key8] = resolvePropValue(options, rawCurrentProps, _key8, undefined, instance, true /* isAbsent */);\n          }\n        } else {\n          delete props[_key8];\n        }\n      }\n    }\n    // in the case of functional component w/o props declaration, props and\n    // attrs point to the same object so it should already have been updated.\n    if (attrs !== rawCurrentProps) {\n      for (var _key9 in attrs) {\n        if (!rawProps || !hasOwn(rawProps, _key9)) {\n          delete attrs[_key9];\n          hasAttrsChanged = true;\n        }\n      }\n    }\n  }\n  // trigger updates for $attrs in case it's used in component slots\n  if (hasAttrsChanged) {\n    trigger(instance, \"set\" /* SET */, '$attrs');\n  }\n  if (process.env.NODE_ENV !== 'production') {\n    validateProps(rawProps || {}, props, instance);\n  }\n}\nfunction setFullProps(instance, rawProps, props, attrs) {\n  var _instance$propsOption5 = _slicedToArray(instance.propsOptions, 2),\n    options = _instance$propsOption5[0],\n    needCastKeys = _instance$propsOption5[1];\n  var hasAttrsChanged = false;\n  var rawCastValues;\n  if (rawProps) {\n    for (var key in rawProps) {\n      // key, ref are reserved and never passed down\n      if (isReservedProp(key)) {\n        continue;\n      }\n      var value = rawProps[key];\n      // prop option names are camelized during normalization, so to support\n      // kebab -> camel conversion here we need to camelize the key.\n      var camelKey = void 0;\n      if (options && hasOwn(options, camelKey = camelize(key))) {\n        if (!needCastKeys || !needCastKeys.includes(camelKey)) {\n          props[camelKey] = value;\n        } else {\n          (rawCastValues || (rawCastValues = {}))[camelKey] = value;\n        }\n      } else if (!isEmitListener(instance.emitsOptions, key)) {\n        if (!(key in attrs) || value !== attrs[key]) {\n          attrs[key] = value;\n          hasAttrsChanged = true;\n        }\n      }\n    }\n  }\n  if (needCastKeys) {\n    var rawCurrentProps = toRaw(props);\n    var castValues = rawCastValues || EMPTY_OBJ;\n    for (var i = 0; i < needCastKeys.length; i++) {\n      var _key10 = needCastKeys[i];\n      props[_key10] = resolvePropValue(options, rawCurrentProps, _key10, castValues[_key10], instance, !hasOwn(castValues, _key10));\n    }\n  }\n  return hasAttrsChanged;\n}\nfunction resolvePropValue(options, props, key, value, instance, isAbsent) {\n  var opt = options[key];\n  if (opt != null) {\n    var hasDefault = hasOwn(opt, 'default');\n    // default values\n    if (hasDefault && value === undefined) {\n      var defaultValue = opt.default;\n      if (opt.type !== Function && isFunction(defaultValue)) {\n        var propsDefaults = instance.propsDefaults;\n        if (key in propsDefaults) {\n          value = propsDefaults[key];\n        } else {\n          setCurrentInstance(instance);\n          value = propsDefaults[key] = defaultValue.call(null, props);\n          unsetCurrentInstance();\n        }\n      } else {\n        value = defaultValue;\n      }\n    }\n    // boolean casting\n    if (opt[0 /* shouldCast */]) {\n      if (isAbsent && !hasDefault) {\n        value = false;\n      } else if (opt[1 /* shouldCastTrue */] && (value === '' || value === hyphenate(key))) {\n        value = true;\n      }\n    }\n  }\n  return value;\n}\nfunction normalizePropsOptions(comp, appContext) {\n  var asMixin = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n  var cache = appContext.propsCache;\n  var cached = cache.get(comp);\n  if (cached) {\n    return cached;\n  }\n  var raw = comp.props;\n  var normalized = {};\n  var needCastKeys = [];\n  // apply mixin/extends props\n  var hasExtends = false;\n  if (__VUE_OPTIONS_API__ && !isFunction(comp)) {\n    var extendProps = function extendProps(raw) {\n      hasExtends = true;\n      var _normalizePropsOption = normalizePropsOptions(raw, appContext, true),\n        _normalizePropsOption2 = _slicedToArray(_normalizePropsOption, 2),\n        props = _normalizePropsOption2[0],\n        keys = _normalizePropsOption2[1];\n      extend(normalized, props);\n      if (keys) needCastKeys.push.apply(needCastKeys, _toConsumableArray(keys));\n    };\n    if (!asMixin && appContext.mixins.length) {\n      appContext.mixins.forEach(extendProps);\n    }\n    if (comp.extends) {\n      extendProps(comp.extends);\n    }\n    if (comp.mixins) {\n      comp.mixins.forEach(extendProps);\n    }\n  }\n  if (!raw && !hasExtends) {\n    cache.set(comp, EMPTY_ARR);\n    return EMPTY_ARR;\n  }\n  if (isArray(raw)) {\n    for (var i = 0; i < raw.length; i++) {\n      if (process.env.NODE_ENV !== 'production' && !isString(raw[i])) {\n        warn(\"props must be strings when using array syntax.\", raw[i]);\n      }\n      var normalizedKey = camelize(raw[i]);\n      if (validatePropName(normalizedKey)) {\n        normalized[normalizedKey] = EMPTY_OBJ;\n      }\n    }\n  } else if (raw) {\n    if (process.env.NODE_ENV !== 'production' && !isObject(raw)) {\n      warn(\"invalid props options\", raw);\n    }\n    for (var key in raw) {\n      var _normalizedKey = camelize(key);\n      if (validatePropName(_normalizedKey)) {\n        var opt = raw[key];\n        var prop = normalized[_normalizedKey] = isArray(opt) || isFunction(opt) ? {\n          type: opt\n        } : opt;\n        if (prop) {\n          var booleanIndex = getTypeIndex(Boolean, prop.type);\n          var stringIndex = getTypeIndex(String, prop.type);\n          prop[0 /* shouldCast */] = booleanIndex > -1;\n          prop[1 /* shouldCastTrue */] = stringIndex < 0 || booleanIndex < stringIndex;\n          // if the prop needs boolean casting or default value\n          if (booleanIndex > -1 || hasOwn(prop, 'default')) {\n            needCastKeys.push(_normalizedKey);\n          }\n        }\n      }\n    }\n  }\n  var res = [normalized, needCastKeys];\n  cache.set(comp, res);\n  return res;\n}\nfunction validatePropName(key) {\n  if (key[0] !== '$') {\n    return true;\n  } else if (process.env.NODE_ENV !== 'production') {\n    warn(\"Invalid prop name: \\\"\".concat(key, \"\\\" is a reserved property.\"));\n  }\n  return false;\n}\n// use function string name to check type constructors\n// so that it works across vms / iframes.\nfunction getType(ctor) {\n  var match = ctor && ctor.toString().match(/^\\s*function (\\w+)/);\n  return match ? match[1] : ctor === null ? 'null' : '';\n}\nfunction isSameType(a, b) {\n  return getType(a) === getType(b);\n}\nfunction getTypeIndex(type, expectedTypes) {\n  if (isArray(expectedTypes)) {\n    return expectedTypes.findIndex(function (t) {\n      return isSameType(t, type);\n    });\n  } else if (isFunction(expectedTypes)) {\n    return isSameType(expectedTypes, type) ? 0 : -1;\n  }\n  return -1;\n}\n/**\r\n * dev only\r\n */\nfunction validateProps(rawProps, props, instance) {\n  var resolvedValues = toRaw(props);\n  var options = instance.propsOptions[0];\n  for (var key in options) {\n    var opt = options[key];\n    if (opt == null) continue;\n    validateProp(key, resolvedValues[key], opt, !hasOwn(rawProps, key) && !hasOwn(rawProps, hyphenate(key)));\n  }\n}\n/**\r\n * dev only\r\n */\nfunction validateProp(name, value, prop, isAbsent) {\n  var type = prop.type,\n    required = prop.required,\n    validator = prop.validator;\n  // required!\n  if (required && isAbsent) {\n    warn('Missing required prop: \"' + name + '\"');\n    return;\n  }\n  // missing but optional\n  if (value == null && !prop.required) {\n    return;\n  }\n  // type check\n  if (type != null && type !== true) {\n    var isValid = false;\n    var types = isArray(type) ? type : [type];\n    var expectedTypes = [];\n    // value is valid as long as one of the specified types match\n    for (var i = 0; i < types.length && !isValid; i++) {\n      var _assertType = assertType(value, types[i]),\n        valid = _assertType.valid,\n        expectedType = _assertType.expectedType;\n      expectedTypes.push(expectedType || '');\n      isValid = valid;\n    }\n    if (!isValid) {\n      warn(getInvalidTypeMessage(name, value, expectedTypes));\n      return;\n    }\n  }\n  // custom validator\n  if (validator && !validator(value)) {\n    warn('Invalid prop: custom validator check failed for prop \"' + name + '\".');\n  }\n}\nvar isSimpleType = /*#__PURE__*/makeMap('String,Number,Boolean,Function,Symbol,BigInt');\n/**\r\n * dev only\r\n */\nfunction assertType(value, type) {\n  var valid;\n  var expectedType = getType(type);\n  if (isSimpleType(expectedType)) {\n    var t = _typeof(value);\n    valid = t === expectedType.toLowerCase();\n    // for primitive wrapper objects\n    if (!valid && t === 'object') {\n      valid = value instanceof type;\n    }\n  } else if (expectedType === 'Object') {\n    valid = isObject(value);\n  } else if (expectedType === 'Array') {\n    valid = isArray(value);\n  } else if (expectedType === 'null') {\n    valid = value === null;\n  } else {\n    valid = value instanceof type;\n  }\n  return {\n    valid: valid,\n    expectedType: expectedType\n  };\n}\n/**\r\n * dev only\r\n */\nfunction getInvalidTypeMessage(name, value, expectedTypes) {\n  var message = \"Invalid prop: type check failed for prop \\\"\".concat(name, \"\\\".\") + \" Expected \".concat(expectedTypes.map(capitalize).join(' | '));\n  var expectedType = expectedTypes[0];\n  var receivedType = toRawType(value);\n  var expectedValue = styleValue(value, expectedType);\n  var receivedValue = styleValue(value, receivedType);\n  // check if we need to specify expected value\n  if (expectedTypes.length === 1 && isExplicable(expectedType) && !isBoolean(expectedType, receivedType)) {\n    message += \" with value \".concat(expectedValue);\n  }\n  message += \", got \".concat(receivedType, \" \");\n  // check if we need to specify received value\n  if (isExplicable(receivedType)) {\n    message += \"with value \".concat(receivedValue, \".\");\n  }\n  return message;\n}\n/**\r\n * dev only\r\n */\nfunction styleValue(value, type) {\n  if (type === 'String') {\n    return \"\\\"\".concat(value, \"\\\"\");\n  } else if (type === 'Number') {\n    return \"\".concat(Number(value));\n  } else {\n    return \"\".concat(value);\n  }\n}\n/**\r\n * dev only\r\n */\nfunction isExplicable(type) {\n  var explicitTypes = ['string', 'number', 'boolean'];\n  return explicitTypes.some(function (elem) {\n    return type.toLowerCase() === elem;\n  });\n}\n/**\r\n * dev only\r\n */\nfunction isBoolean() {\n  for (var _len4 = arguments.length, args = new Array(_len4), _key11 = 0; _key11 < _len4; _key11++) {\n    args[_key11] = arguments[_key11];\n  }\n  return args.some(function (elem) {\n    return elem.toLowerCase() === 'boolean';\n  });\n}\nvar isInternalKey = function isInternalKey(key) {\n  return key[0] === '_' || key === '$stable';\n};\nvar normalizeSlotValue = function normalizeSlotValue(value) {\n  return isArray(value) ? value.map(normalizeVNode) : [normalizeVNode(value)];\n};\nvar normalizeSlot = function normalizeSlot(key, rawSlot, ctx) {\n  var normalized = withCtx(function () {\n    if (process.env.NODE_ENV !== 'production' && currentInstance) {\n      warn(\"Slot \\\"\".concat(key, \"\\\" invoked outside of the render function: \") + \"this will not track dependencies used in the slot. \" + \"Invoke the slot function inside the render function instead.\");\n    }\n    return normalizeSlotValue(rawSlot.apply(void 0, arguments));\n  }, ctx);\n  normalized._c = false;\n  return normalized;\n};\nvar normalizeObjectSlots = function normalizeObjectSlots(rawSlots, slots, instance) {\n  var ctx = rawSlots._ctx;\n  var _loop4 = function _loop4() {\n    if (isInternalKey(key)) return \"continue\";\n    var value = rawSlots[key];\n    if (isFunction(value)) {\n      slots[key] = normalizeSlot(key, value, ctx);\n    } else if (value != null) {\n      if (process.env.NODE_ENV !== 'production' && !false) {\n        warn(\"Non-function value encountered for slot \\\"\".concat(key, \"\\\". \") + \"Prefer function slots for better performance.\");\n      }\n      var normalized = normalizeSlotValue(value);\n      slots[key] = function () {\n        return normalized;\n      };\n    }\n  };\n  for (var key in rawSlots) {\n    var _ret = _loop4();\n    if (_ret === \"continue\") continue;\n  }\n};\nvar normalizeVNodeSlots = function normalizeVNodeSlots(instance, children) {\n  if (process.env.NODE_ENV !== 'production' && !isKeepAlive(instance.vnode) && !false) {\n    warn(\"Non-function value encountered for default slot. \" + \"Prefer function slots for better performance.\");\n  }\n  var normalized = normalizeSlotValue(children);\n  instance.slots.default = function () {\n    return normalized;\n  };\n};\nvar initSlots = function initSlots(instance, children) {\n  if (instance.vnode.shapeFlag & 32 /* SLOTS_CHILDREN */) {\n    var type = children._;\n    if (type) {\n      // users can get the shallow readonly version of the slots object through `this.$slots`,\n      // we should avoid the proxy object polluting the slots of the internal instance\n      instance.slots = toRaw(children);\n      // make compiler marker non-enumerable\n      def(children, '_', type);\n    } else {\n      normalizeObjectSlots(children, instance.slots = {});\n    }\n  } else {\n    instance.slots = {};\n    if (children) {\n      normalizeVNodeSlots(instance, children);\n    }\n  }\n  def(instance.slots, InternalObjectKey, 1);\n};\nvar updateSlots = function updateSlots(instance, children, optimized) {\n  var vnode = instance.vnode,\n    slots = instance.slots;\n  var needDeletionCheck = true;\n  var deletionComparisonTarget = EMPTY_OBJ;\n  if (vnode.shapeFlag & 32 /* SLOTS_CHILDREN */) {\n    var type = children._;\n    if (type) {\n      // compiled slots.\n      if (process.env.NODE_ENV !== 'production' && isHmrUpdating) {\n        // Parent was HMR updated so slot content may have changed.\n        // force update slots and mark instance for hmr as well\n        extend(slots, children);\n      } else if (optimized && type === 1 /* STABLE */) {\n        // compiled AND stable.\n        // no need to update, and skip stale slots removal.\n        needDeletionCheck = false;\n      } else {\n        // compiled but dynamic (v-if/v-for on slots) - update slots, but skip\n        // normalization.\n        extend(slots, children);\n        // #2893\n        // when rendering the optimized slots by manually written render function,\n        // we need to delete the `slots._` flag if necessary to make subsequent updates reliable,\n        // i.e. let the `renderSlot` create the bailed Fragment\n        if (!optimized && type === 1 /* STABLE */) {\n          delete slots._;\n        }\n      }\n    } else {\n      needDeletionCheck = !children.$stable;\n      normalizeObjectSlots(children, slots);\n    }\n    deletionComparisonTarget = children;\n  } else if (children) {\n    // non slot object children (direct value) passed to a component\n    normalizeVNodeSlots(instance, children);\n    deletionComparisonTarget = {\n      default: 1\n    };\n  }\n  // delete stale slots\n  if (needDeletionCheck) {\n    for (var key in slots) {\n      if (!isInternalKey(key) && !(key in deletionComparisonTarget)) {\n        delete slots[key];\n      }\n    }\n  }\n};\n\n/**\r\nRuntime helper for applying directives to a vnode. Example usage:\r\n\nconst comp = resolveComponent('comp')\r\nconst foo = resolveDirective('foo')\r\nconst bar = resolveDirective('bar')\r\n\nreturn withDirectives(h(comp), [\r\n  [foo, this.x],\r\n  [bar, this.y]\r\n])\r\n*/\nvar isBuiltInDirective = /*#__PURE__*/makeMap('bind,cloak,else-if,else,for,html,if,model,on,once,pre,show,slot,text,memo');\nfunction validateDirectiveName(name) {\n  if (isBuiltInDirective(name)) {\n    warn('Do not use built-in directive ids as custom directive id: ' + name);\n  }\n}\n/**\r\n * Adds directives to a VNode.\r\n */\nfunction withDirectives(vnode, directives) {\n  var internalInstance = currentRenderingInstance;\n  if (internalInstance === null) {\n    process.env.NODE_ENV !== 'production' && warn(\"withDirectives can only be used inside render functions.\");\n    return vnode;\n  }\n  var instance = internalInstance.proxy;\n  var bindings = vnode.dirs || (vnode.dirs = []);\n  for (var i = 0; i < directives.length; i++) {\n    var _directives$i = _slicedToArray(directives[i], 4),\n      dir = _directives$i[0],\n      value = _directives$i[1],\n      arg = _directives$i[2],\n      _directives$i$ = _directives$i[3],\n      modifiers = _directives$i$ === void 0 ? EMPTY_OBJ : _directives$i$;\n    if (isFunction(dir)) {\n      dir = {\n        mounted: dir,\n        updated: dir\n      };\n    }\n    if (dir.deep) {\n      traverse(value);\n    }\n    bindings.push({\n      dir: dir,\n      instance: instance,\n      value: value,\n      oldValue: void 0,\n      arg: arg,\n      modifiers: modifiers\n    });\n  }\n  return vnode;\n}\nfunction invokeDirectiveHook(vnode, prevVNode, instance, name) {\n  var bindings = vnode.dirs;\n  var oldBindings = prevVNode && prevVNode.dirs;\n  for (var i = 0; i < bindings.length; i++) {\n    var binding = bindings[i];\n    if (oldBindings) {\n      binding.oldValue = oldBindings[i].value;\n    }\n    var hook = binding.dir[name];\n    if (hook) {\n      // disable tracking inside all lifecycle hooks\n      // since they can potentially be called inside effects.\n      pauseTracking();\n      callWithAsyncErrorHandling(hook, instance, 8 /* DIRECTIVE_HOOK */, [vnode.el, binding, vnode, prevVNode]);\n      resetTracking();\n    }\n  }\n}\nfunction createAppContext() {\n  return {\n    app: null,\n    config: {\n      isNativeTag: NO,\n      performance: false,\n      globalProperties: {},\n      optionMergeStrategies: {},\n      errorHandler: undefined,\n      warnHandler: undefined,\n      compilerOptions: {}\n    },\n    mixins: [],\n    components: {},\n    directives: {},\n    provides: Object.create(null),\n    optionsCache: new WeakMap(),\n    propsCache: new WeakMap(),\n    emitsCache: new WeakMap()\n  };\n}\nvar uid = 0;\nfunction createAppAPI(render, hydrate) {\n  return function createApp(rootComponent) {\n    var rootProps = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;\n    if (rootProps != null && !isObject(rootProps)) {\n      process.env.NODE_ENV !== 'production' && warn(\"root props passed to app.mount() must be an object.\");\n      rootProps = null;\n    }\n    var context = createAppContext();\n    var installedPlugins = new Set();\n    var isMounted = false;\n    var app = context.app = {\n      _uid: uid++,\n      _component: rootComponent,\n      _props: rootProps,\n      _container: null,\n      _context: context,\n      _instance: null,\n      version: version,\n      get config() {\n        return context.config;\n      },\n      set config(v) {\n        if (process.env.NODE_ENV !== 'production') {\n          warn(\"app.config cannot be replaced. Modify individual options instead.\");\n        }\n      },\n      use: function use(plugin) {\n        for (var _len5 = arguments.length, options = new Array(_len5 > 1 ? _len5 - 1 : 0), _key12 = 1; _key12 < _len5; _key12++) {\n          options[_key12 - 1] = arguments[_key12];\n        }\n        if (installedPlugins.has(plugin)) {\n          process.env.NODE_ENV !== 'production' && warn(\"Plugin has already been applied to target app.\");\n        } else if (plugin && isFunction(plugin.install)) {\n          installedPlugins.add(plugin);\n          plugin.install.apply(plugin, [app].concat(options));\n        } else if (isFunction(plugin)) {\n          installedPlugins.add(plugin);\n          plugin.apply(void 0, [app].concat(options));\n        } else if (process.env.NODE_ENV !== 'production') {\n          warn(\"A plugin must either be a function or an object with an \\\"install\\\" \" + \"function.\");\n        }\n        return app;\n      },\n      mixin: function mixin(_mixin) {\n        if (__VUE_OPTIONS_API__) {\n          if (!context.mixins.includes(_mixin)) {\n            context.mixins.push(_mixin);\n          } else if (process.env.NODE_ENV !== 'production') {\n            warn('Mixin has already been applied to target app' + (_mixin.name ? \": \".concat(_mixin.name) : ''));\n          }\n        } else if (process.env.NODE_ENV !== 'production') {\n          warn('Mixins are only available in builds supporting Options API');\n        }\n        return app;\n      },\n      component: function component(name, _component) {\n        if (process.env.NODE_ENV !== 'production') {\n          validateComponentName(name, context.config);\n        }\n        if (!_component) {\n          return context.components[name];\n        }\n        if (process.env.NODE_ENV !== 'production' && context.components[name]) {\n          warn(\"Component \\\"\".concat(name, \"\\\" has already been registered in target app.\"));\n        }\n        context.components[name] = _component;\n        return app;\n      },\n      directive: function directive(name, _directive) {\n        if (process.env.NODE_ENV !== 'production') {\n          validateDirectiveName(name);\n        }\n        if (!_directive) {\n          return context.directives[name];\n        }\n        if (process.env.NODE_ENV !== 'production' && context.directives[name]) {\n          warn(\"Directive \\\"\".concat(name, \"\\\" has already been registered in target app.\"));\n        }\n        context.directives[name] = _directive;\n        return app;\n      },\n      mount: function mount(rootContainer, isHydrate, isSVG) {\n        if (!isMounted) {\n          var vnode = createVNode(rootComponent, rootProps);\n          // store app context on the root VNode.\n          // this will be set on the root instance on initial mount.\n          vnode.appContext = context;\n          // HMR root reload\n          if (process.env.NODE_ENV !== 'production') {\n            context.reload = function () {\n              render(cloneVNode(vnode), rootContainer, isSVG);\n            };\n          }\n          if (isHydrate && hydrate) {\n            hydrate(vnode, rootContainer);\n          } else {\n            render(vnode, rootContainer, isSVG);\n          }\n          isMounted = true;\n          app._container = rootContainer;\n          rootContainer.__vue_app__ = app;\n          if (process.env.NODE_ENV !== 'production' || __VUE_PROD_DEVTOOLS__) {\n            app._instance = vnode.component;\n            devtoolsInitApp(app, version);\n          }\n          return getExposeProxy(vnode.component) || vnode.component.proxy;\n        } else if (process.env.NODE_ENV !== 'production') {\n          warn(\"App has already been mounted.\\n\" + \"If you want to remount the same app, move your app creation logic \" + \"into a factory function and create fresh app instances for each \" + \"mount - e.g. `const createMyApp = () => createApp(App)`\");\n        }\n      },\n      unmount: function unmount() {\n        if (isMounted) {\n          render(null, app._container);\n          if (process.env.NODE_ENV !== 'production' || __VUE_PROD_DEVTOOLS__) {\n            app._instance = null;\n            devtoolsUnmountApp(app);\n          }\n          delete app._container.__vue_app__;\n        } else if (process.env.NODE_ENV !== 'production') {\n          warn(\"Cannot unmount an app that is not mounted.\");\n        }\n      },\n      provide: function provide(key, value) {\n        if (process.env.NODE_ENV !== 'production' && key in context.provides) {\n          warn(\"App already provides property with key \\\"\".concat(String(key), \"\\\". \") + \"It will be overwritten with the new value.\");\n        }\n        // TypeScript doesn't allow symbols as index type\n        // https://github.com/Microsoft/TypeScript/issues/24587\n        context.provides[key] = value;\n        return app;\n      }\n    };\n    return app;\n  };\n}\n\n/**\r\n * Function for handling a template ref\r\n */\nfunction setRef(rawRef, oldRawRef, parentSuspense, vnode) {\n  var isUnmount = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : false;\n  if (isArray(rawRef)) {\n    rawRef.forEach(function (r, i) {\n      return setRef(r, oldRawRef && (isArray(oldRawRef) ? oldRawRef[i] : oldRawRef), parentSuspense, vnode, isUnmount);\n    });\n    return;\n  }\n  if (isAsyncWrapper(vnode) && !isUnmount) {\n    // when mounting async components, nothing needs to be done,\n    // because the template ref is forwarded to inner component\n    return;\n  }\n  var refValue = vnode.shapeFlag & 4 /* STATEFUL_COMPONENT */ ? getExposeProxy(vnode.component) || vnode.component.proxy : vnode.el;\n  var value = isUnmount ? null : refValue;\n  var owner = rawRef.i,\n    ref = rawRef.r;\n  if (process.env.NODE_ENV !== 'production' && !owner) {\n    warn(\"Missing ref owner context. ref cannot be used on hoisted vnodes. \" + \"A vnode with ref must be created inside the render function.\");\n    return;\n  }\n  var oldRef = oldRawRef && oldRawRef.r;\n  var refs = owner.refs === EMPTY_OBJ ? owner.refs = {} : owner.refs;\n  var setupState = owner.setupState;\n  // dynamic ref changed. unset old ref\n  if (oldRef != null && oldRef !== ref) {\n    if (isString(oldRef)) {\n      refs[oldRef] = null;\n      if (hasOwn(setupState, oldRef)) {\n        setupState[oldRef] = null;\n      }\n    } else if (isRef(oldRef)) {\n      oldRef.value = null;\n    }\n  }\n  if (isFunction(ref)) {\n    callWithErrorHandling(ref, owner, 12 /* FUNCTION_REF */, [value, refs]);\n  } else {\n    var _isString = isString(ref);\n    var _isRef = isRef(ref);\n    if (_isString || _isRef) {\n      var doSet = function doSet() {\n        if (rawRef.f) {\n          var existing = _isString ? refs[ref] : ref.value;\n          if (isUnmount) {\n            isArray(existing) && remove(existing, refValue);\n          } else {\n            if (!isArray(existing)) {\n              if (_isString) {\n                refs[ref] = [refValue];\n              } else {\n                ref.value = [refValue];\n                if (rawRef.k) refs[rawRef.k] = ref.value;\n              }\n            } else if (!existing.includes(refValue)) {\n              existing.push(refValue);\n            }\n          }\n        } else if (_isString) {\n          refs[ref] = value;\n          if (hasOwn(setupState, ref)) {\n            setupState[ref] = value;\n          }\n        } else if (isRef(ref)) {\n          ref.value = value;\n          if (rawRef.k) refs[rawRef.k] = value;\n        } else if (process.env.NODE_ENV !== 'production') {\n          warn('Invalid template ref type:', ref, \"(\".concat(_typeof(ref), \")\"));\n        }\n      };\n      if (value) {\n        doSet.id = -1;\n        queuePostRenderEffect(doSet, parentSuspense);\n      } else {\n        doSet();\n      }\n    } else if (process.env.NODE_ENV !== 'production') {\n      warn('Invalid template ref type:', ref, \"(\".concat(_typeof(ref), \")\"));\n    }\n  }\n}\nvar hasMismatch = false;\nvar isSVGContainer = function isSVGContainer(container) {\n  return /svg/.test(container.namespaceURI) && container.tagName !== 'foreignObject';\n};\nvar isComment = function isComment(node) {\n  return node.nodeType === 8;\n} /* COMMENT */;\n// Note: hydration is DOM-specific\n// But we have to place it in core due to tight coupling with core - splitting\n// it out creates a ton of unnecessary complexity.\n// Hydration also depends on some renderer internal logic which needs to be\n// passed in via arguments.\nfunction createHydrationFunctions(rendererInternals) {\n  var mountComponent = rendererInternals.mt,\n    patch = rendererInternals.p,\n    _rendererInternals$o2 = rendererInternals.o,\n    patchProp = _rendererInternals$o2.patchProp,\n    nextSibling = _rendererInternals$o2.nextSibling,\n    parentNode = _rendererInternals$o2.parentNode,\n    remove = _rendererInternals$o2.remove,\n    insert = _rendererInternals$o2.insert,\n    createComment = _rendererInternals$o2.createComment;\n  var hydrate = function hydrate(vnode, container) {\n    if (!container.hasChildNodes()) {\n      process.env.NODE_ENV !== 'production' && warn(\"Attempting to hydrate existing markup but container is empty. \" + \"Performing full mount instead.\");\n      patch(null, vnode, container);\n      flushPostFlushCbs();\n      return;\n    }\n    hasMismatch = false;\n    hydrateNode(container.firstChild, vnode, null, null, null);\n    flushPostFlushCbs();\n    if (hasMismatch && !false) {}\n  };\n  var hydrateNode = function hydrateNode(node, vnode, parentComponent, parentSuspense, slotScopeIds) {\n    var optimized = arguments.length > 5 && arguments[5] !== undefined ? arguments[5] : false;\n    var isFragmentStart = isComment(node) && node.data === '[';\n    var onMismatch = function onMismatch() {\n      return handleMismatch(node, vnode, parentComponent, parentSuspense, slotScopeIds, isFragmentStart);\n    };\n    var type = vnode.type,\n      ref = vnode.ref,\n      shapeFlag = vnode.shapeFlag;\n    var domType = node.nodeType;\n    vnode.el = node;\n    var nextNode = null;\n    switch (type) {\n      case Text:\n        if (domType !== 3 /* TEXT */) {\n          nextNode = onMismatch();\n        } else {\n          if (node.data !== vnode.children) {\n            hasMismatch = true;\n            process.env.NODE_ENV !== 'production' && warn(\"Hydration text mismatch:\" + \"\\n- Client: \".concat(JSON.stringify(node.data)) + \"\\n- Server: \".concat(JSON.stringify(vnode.children)));\n            node.data = vnode.children;\n          }\n          nextNode = nextSibling(node);\n        }\n        break;\n      case Comment:\n        if (domType !== 8 /* COMMENT */ || isFragmentStart) {\n          nextNode = onMismatch();\n        } else {\n          nextNode = nextSibling(node);\n        }\n        break;\n      case Static:\n        if (domType !== 1 /* ELEMENT */) {\n          nextNode = onMismatch();\n        } else {\n          // determine anchor, adopt content\n          nextNode = node;\n          // if the static vnode has its content stripped during build,\n          // adopt it from the server-rendered HTML.\n          var needToAdoptContent = !vnode.children.length;\n          for (var i = 0; i < vnode.staticCount; i++) {\n            if (needToAdoptContent) vnode.children += nextNode.outerHTML;\n            if (i === vnode.staticCount - 1) {\n              vnode.anchor = nextNode;\n            }\n            nextNode = nextSibling(nextNode);\n          }\n          return nextNode;\n        }\n        break;\n      case Fragment:\n        if (!isFragmentStart) {\n          nextNode = onMismatch();\n        } else {\n          nextNode = hydrateFragment(node, vnode, parentComponent, parentSuspense, slotScopeIds, optimized);\n        }\n        break;\n      default:\n        if (shapeFlag & 1 /* ELEMENT */) {\n          if (domType !== 1 /* ELEMENT */ || vnode.type.toLowerCase() !== node.tagName.toLowerCase()) {\n            nextNode = onMismatch();\n          } else {\n            nextNode = hydrateElement(node, vnode, parentComponent, parentSuspense, slotScopeIds, optimized);\n          }\n        } else if (shapeFlag & 6 /* COMPONENT */) {\n          // when setting up the render effect, if the initial vnode already\n          // has .el set, the component will perform hydration instead of mount\n          // on its sub-tree.\n          vnode.slotScopeIds = slotScopeIds;\n          var container = parentNode(node);\n          mountComponent(vnode, container, null, parentComponent, parentSuspense, isSVGContainer(container), optimized);\n          // component may be async, so in the case of fragments we cannot rely\n          // on component's rendered output to determine the end of the fragment\n          // instead, we do a lookahead to find the end anchor node.\n          nextNode = isFragmentStart ? locateClosingAsyncAnchor(node) : nextSibling(node);\n          // #3787\n          // if component is async, it may get moved / unmounted before its\n          // inner component is loaded, so we need to give it a placeholder\n          // vnode that matches its adopted DOM.\n          if (isAsyncWrapper(vnode)) {\n            var subTree;\n            if (isFragmentStart) {\n              subTree = createVNode(Fragment);\n              subTree.anchor = nextNode ? nextNode.previousSibling : container.lastChild;\n            } else {\n              subTree = node.nodeType === 3 ? createTextVNode('') : createVNode('div');\n            }\n            subTree.el = node;\n            vnode.component.subTree = subTree;\n          }\n        } else if (shapeFlag & 64 /* TELEPORT */) {\n          if (domType !== 8 /* COMMENT */) {\n            nextNode = onMismatch();\n          } else {\n            nextNode = vnode.type.hydrate(node, vnode, parentComponent, parentSuspense, slotScopeIds, optimized, rendererInternals, hydrateChildren);\n          }\n        } else if (shapeFlag & 128 /* SUSPENSE */) {\n          nextNode = vnode.type.hydrate(node, vnode, parentComponent, parentSuspense, isSVGContainer(parentNode(node)), slotScopeIds, optimized, rendererInternals, hydrateNode);\n        } else if (process.env.NODE_ENV !== 'production') {\n          warn('Invalid HostVNode type:', type, \"(\".concat(_typeof(type), \")\"));\n        }\n    }\n    if (ref != null) {\n      setRef(ref, null, parentSuspense, vnode);\n    }\n    return nextNode;\n  };\n  var hydrateElement = function hydrateElement(el, vnode, parentComponent, parentSuspense, slotScopeIds, optimized) {\n    optimized = optimized || !!vnode.dynamicChildren;\n    var type = vnode.type,\n      props = vnode.props,\n      patchFlag = vnode.patchFlag,\n      shapeFlag = vnode.shapeFlag,\n      dirs = vnode.dirs;\n    // #4006 for form elements with non-string v-model value bindings\n    // e.g. <option :value=\"obj\">, <input type=\"checkbox\" :true-value=\"1\">\n    var forcePatchValue = type === 'input' && dirs || type === 'option';\n    // skip props & children if this is hoisted static nodes\n    if (forcePatchValue || patchFlag !== -1 /* HOISTED */) {\n      if (dirs) {\n        invokeDirectiveHook(vnode, null, parentComponent, 'created');\n      }\n      // props\n      if (props) {\n        if (forcePatchValue || !optimized || patchFlag & (16 /* FULL_PROPS */ | 32 /* HYDRATE_EVENTS */)) {\n          for (var key in props) {\n            if (forcePatchValue && key.endsWith('value') || isOn(key) && !isReservedProp(key)) {\n              patchProp(el, key, null, props[key], false, undefined, parentComponent);\n            }\n          }\n        } else if (props.onClick) {\n          // Fast path for click listeners (which is most often) to avoid\n          // iterating through props.\n          patchProp(el, 'onClick', null, props.onClick, false, undefined, parentComponent);\n        }\n      }\n      // vnode / directive hooks\n      var vnodeHooks;\n      if (vnodeHooks = props && props.onVnodeBeforeMount) {\n        invokeVNodeHook(vnodeHooks, parentComponent, vnode);\n      }\n      if (dirs) {\n        invokeDirectiveHook(vnode, null, parentComponent, 'beforeMount');\n      }\n      if ((vnodeHooks = props && props.onVnodeMounted) || dirs) {\n        queueEffectWithSuspense(function () {\n          vnodeHooks && invokeVNodeHook(vnodeHooks, parentComponent, vnode);\n          dirs && invokeDirectiveHook(vnode, null, parentComponent, 'mounted');\n        }, parentSuspense);\n      }\n      // children\n      if (shapeFlag & 16 /* ARRAY_CHILDREN */ &&\n      // skip if element has innerHTML / textContent\n      !(props && (props.innerHTML || props.textContent))) {\n        var next = hydrateChildren(el.firstChild, vnode, el, parentComponent, parentSuspense, slotScopeIds, optimized);\n        var _hasWarned = false;\n        while (next) {\n          hasMismatch = true;\n          if (process.env.NODE_ENV !== 'production' && !_hasWarned) {\n            warn(\"Hydration children mismatch in <\".concat(vnode.type, \">: \") + \"server rendered element contains more child nodes than client vdom.\");\n            _hasWarned = true;\n          }\n          // The SSRed DOM contains more nodes than it should. Remove them.\n          var cur = next;\n          next = next.nextSibling;\n          remove(cur);\n        }\n      } else if (shapeFlag & 8 /* TEXT_CHILDREN */) {\n        if (el.textContent !== vnode.children) {\n          hasMismatch = true;\n          process.env.NODE_ENV !== 'production' && warn(\"Hydration text content mismatch in <\".concat(vnode.type, \">:\\n\") + \"- Client: \".concat(el.textContent, \"\\n\") + \"- Server: \".concat(vnode.children));\n          el.textContent = vnode.children;\n        }\n      }\n    }\n    return el.nextSibling;\n  };\n  var hydrateChildren = function hydrateChildren(node, parentVNode, container, parentComponent, parentSuspense, slotScopeIds, optimized) {\n    optimized = optimized || !!parentVNode.dynamicChildren;\n    var children = parentVNode.children;\n    var l = children.length;\n    var hasWarned = false;\n    for (var i = 0; i < l; i++) {\n      var vnode = optimized ? children[i] : children[i] = normalizeVNode(children[i]);\n      if (node) {\n        node = hydrateNode(node, vnode, parentComponent, parentSuspense, slotScopeIds, optimized);\n      } else if (vnode.type === Text && !vnode.children) {\n        continue;\n      } else {\n        hasMismatch = true;\n        if (process.env.NODE_ENV !== 'production' && !hasWarned) {\n          warn(\"Hydration children mismatch in <\".concat(container.tagName.toLowerCase(), \">: \") + \"server rendered element contains fewer child nodes than client vdom.\");\n          hasWarned = true;\n        }\n        // the SSRed DOM didn't contain enough nodes. Mount the missing ones.\n        patch(null, vnode, container, null, parentComponent, parentSuspense, isSVGContainer(container), slotScopeIds);\n      }\n    }\n    return node;\n  };\n  var hydrateFragment = function hydrateFragment(node, vnode, parentComponent, parentSuspense, slotScopeIds, optimized) {\n    var fragmentSlotScopeIds = vnode.slotScopeIds;\n    if (fragmentSlotScopeIds) {\n      slotScopeIds = slotScopeIds ? slotScopeIds.concat(fragmentSlotScopeIds) : fragmentSlotScopeIds;\n    }\n    var container = parentNode(node);\n    var next = hydrateChildren(nextSibling(node), vnode, container, parentComponent, parentSuspense, slotScopeIds, optimized);\n    if (next && isComment(next) && next.data === ']') {\n      return nextSibling(vnode.anchor = next);\n    } else {\n      // fragment didn't hydrate successfully, since we didn't get a end anchor\n      // back. This should have led to node/children mismatch warnings.\n      hasMismatch = true;\n      // since the anchor is missing, we need to create one and insert it\n      insert(vnode.anchor = createComment(\"]\"), container, next);\n      return next;\n    }\n  };\n  var handleMismatch = function handleMismatch(node, vnode, parentComponent, parentSuspense, slotScopeIds, isFragment) {\n    hasMismatch = true;\n    process.env.NODE_ENV !== 'production' && warn(\"Hydration node mismatch:\\n- Client vnode:\", vnode.type, \"\\n- Server rendered DOM:\", node, node.nodeType === 3 /* TEXT */ ? \"(text)\" : isComment(node) && node.data === '[' ? \"(start of fragment)\" : \"\");\n    vnode.el = null;\n    if (isFragment) {\n      // remove excessive fragment nodes\n      var end = locateClosingAsyncAnchor(node);\n      while (true) {\n        var _next2 = nextSibling(node);\n        if (_next2 && _next2 !== end) {\n          remove(_next2);\n        } else {\n          break;\n        }\n      }\n    }\n    var next = nextSibling(node);\n    var container = parentNode(node);\n    remove(node);\n    patch(null, vnode, container, next, parentComponent, parentSuspense, isSVGContainer(container), slotScopeIds);\n    return next;\n  };\n  var locateClosingAsyncAnchor = function locateClosingAsyncAnchor(node) {\n    var match = 0;\n    while (node) {\n      node = nextSibling(node);\n      if (node && isComment(node)) {\n        if (node.data === '[') match++;\n        if (node.data === ']') {\n          if (match === 0) {\n            return nextSibling(node);\n          } else {\n            match--;\n          }\n        }\n      }\n    }\n    return node;\n  };\n  return [hydrate, hydrateNode];\n}\nvar supported;\nvar perf;\nfunction startMeasure(instance, type) {\n  if (instance.appContext.config.performance && isSupported()) {\n    perf.mark(\"vue-\".concat(type, \"-\").concat(instance.uid));\n  }\n  if (process.env.NODE_ENV !== 'production' || __VUE_PROD_DEVTOOLS__) {\n    devtoolsPerfStart(instance, type, supported ? perf.now() : Date.now());\n  }\n}\nfunction endMeasure(instance, type) {\n  if (instance.appContext.config.performance && isSupported()) {\n    var startTag = \"vue-\".concat(type, \"-\").concat(instance.uid);\n    var endTag = startTag + \":end\";\n    perf.mark(endTag);\n    perf.measure(\"<\".concat(formatComponentName(instance, instance.type), \"> \").concat(type), startTag, endTag);\n    perf.clearMarks(startTag);\n    perf.clearMarks(endTag);\n  }\n  if (process.env.NODE_ENV !== 'production' || __VUE_PROD_DEVTOOLS__) {\n    devtoolsPerfEnd(instance, type, supported ? perf.now() : Date.now());\n  }\n}\nfunction isSupported() {\n  if (supported !== undefined) {\n    return supported;\n  }\n  /* eslint-disable no-restricted-globals */\n  if (typeof window !== 'undefined' && window.performance) {\n    supported = true;\n    perf = window.performance;\n  } else {\n    supported = false;\n  }\n  /* eslint-enable no-restricted-globals */\n  return supported;\n}\n\n/**\r\n * This is only called in esm-bundler builds.\r\n * It is called when a renderer is created, in `baseCreateRenderer` so that\r\n * importing runtime-core is side-effects free.\r\n *\r\n * istanbul-ignore-next\r\n */\nfunction initFeatureFlags() {\n  var needWarn = [];\n  if (typeof __VUE_OPTIONS_API__ !== 'boolean') {\n    process.env.NODE_ENV !== 'production' && needWarn.push(\"__VUE_OPTIONS_API__\");\n    getGlobalThis().__VUE_OPTIONS_API__ = true;\n  }\n  if (typeof __VUE_PROD_DEVTOOLS__ !== 'boolean') {\n    process.env.NODE_ENV !== 'production' && needWarn.push(\"__VUE_PROD_DEVTOOLS__\");\n    getGlobalThis().__VUE_PROD_DEVTOOLS__ = false;\n  }\n  if (process.env.NODE_ENV !== 'production' && needWarn.length) {\n    var multi = needWarn.length > 1;\n  }\n}\nvar queuePostRenderEffect = queueEffectWithSuspense;\n/**\r\n * The createRenderer function accepts two generic arguments:\r\n * HostNode and HostElement, corresponding to Node and Element types in the\r\n * host environment. For example, for runtime-dom, HostNode would be the DOM\r\n * `Node` interface and HostElement would be the DOM `Element` interface.\r\n *\r\n * Custom renderers can pass in the platform specific types like this:\r\n *\r\n * ``` js\r\n * const { render, createApp } = createRenderer<Node, Element>({\r\n *   patchProp,\r\n *   ...nodeOps\r\n * })\r\n * ```\r\n */\nfunction createRenderer(options) {\n  return baseCreateRenderer(options);\n}\n// Separate API for creating hydration-enabled renderer.\n// Hydration logic is only used when calling this function, making it\n// tree-shakable.\nfunction createHydrationRenderer(options) {\n  return baseCreateRenderer(options, createHydrationFunctions);\n}\n// implementation\nfunction baseCreateRenderer(options, createHydrationFns) {\n  // compile-time feature flags check\n  {\n    initFeatureFlags();\n  }\n  var target = getGlobalThis();\n  target.__VUE__ = true;\n  if (process.env.NODE_ENV !== 'production' || __VUE_PROD_DEVTOOLS__) {\n    setDevtoolsHook(target.__VUE_DEVTOOLS_GLOBAL_HOOK__, target);\n  }\n  var hostInsert = options.insert,\n    hostRemove = options.remove,\n    hostPatchProp = options.patchProp,\n    hostCreateElement = options.createElement,\n    hostCreateText = options.createText,\n    hostCreateComment = options.createComment,\n    hostSetText = options.setText,\n    hostSetElementText = options.setElementText,\n    hostParentNode = options.parentNode,\n    hostNextSibling = options.nextSibling,\n    _options$setScopeId = options.setScopeId,\n    hostSetScopeId = _options$setScopeId === void 0 ? NOOP : _options$setScopeId,\n    hostCloneNode = options.cloneNode,\n    hostInsertStaticContent = options.insertStaticContent;\n  // Note: functions inside this closure should use `const xxx = () => {}`\n  // style in order to prevent being inlined by minifiers.\n  var patch = function patch(n1, n2, container) {\n    var anchor = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : null;\n    var parentComponent = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : null;\n    var parentSuspense = arguments.length > 5 && arguments[5] !== undefined ? arguments[5] : null;\n    var isSVG = arguments.length > 6 && arguments[6] !== undefined ? arguments[6] : false;\n    var slotScopeIds = arguments.length > 7 && arguments[7] !== undefined ? arguments[7] : null;\n    var optimized = arguments.length > 8 && arguments[8] !== undefined ? arguments[8] : process.env.NODE_ENV !== 'production' && isHmrUpdating ? false : !!n2.dynamicChildren;\n    if (n1 === n2) {\n      return;\n    }\n    // patching & not same type, unmount old tree\n    if (n1 && !isSameVNodeType(n1, n2)) {\n      anchor = getNextHostNode(n1);\n      unmount(n1, parentComponent, parentSuspense, true);\n      n1 = null;\n    }\n    if (n2.patchFlag === -2 /* BAIL */) {\n      optimized = false;\n      n2.dynamicChildren = null;\n    }\n    var type = n2.type,\n      ref = n2.ref,\n      shapeFlag = n2.shapeFlag;\n    switch (type) {\n      case Text:\n        processText(n1, n2, container, anchor);\n        break;\n      case Comment:\n        processCommentNode(n1, n2, container, anchor);\n        break;\n      case Static:\n        if (n1 == null) {\n          mountStaticNode(n2, container, anchor, isSVG);\n        } else if (process.env.NODE_ENV !== 'production') {\n          patchStaticNode(n1, n2, container, isSVG);\n        }\n        break;\n      case Fragment:\n        processFragment(n1, n2, container, anchor, parentComponent, parentSuspense, isSVG, slotScopeIds, optimized);\n        break;\n      default:\n        if (shapeFlag & 1 /* ELEMENT */) {\n          processElement(n1, n2, container, anchor, parentComponent, parentSuspense, isSVG, slotScopeIds, optimized);\n        } else if (shapeFlag & 6 /* COMPONENT */) {\n          processComponent(n1, n2, container, anchor, parentComponent, parentSuspense, isSVG, slotScopeIds, optimized);\n        } else if (shapeFlag & 64 /* TELEPORT */) {\n          type.process(n1, n2, container, anchor, parentComponent, parentSuspense, isSVG, slotScopeIds, optimized, internals);\n        } else if (shapeFlag & 128 /* SUSPENSE */) {\n          type.process(n1, n2, container, anchor, parentComponent, parentSuspense, isSVG, slotScopeIds, optimized, internals);\n        } else if (process.env.NODE_ENV !== 'production') {\n          warn('Invalid VNode type:', type, \"(\".concat(_typeof(type), \")\"));\n        }\n    }\n    // set ref\n    if (ref != null && parentComponent) {\n      setRef(ref, n1 && n1.ref, parentSuspense, n2 || n1, !n2);\n    }\n  };\n  var processText = function processText(n1, n2, container, anchor) {\n    if (n1 == null) {\n      hostInsert(n2.el = hostCreateText(n2.children), container, anchor);\n    } else {\n      var el = n2.el = n1.el;\n      if (n2.children !== n1.children) {\n        hostSetText(el, n2.children);\n      }\n    }\n  };\n  var processCommentNode = function processCommentNode(n1, n2, container, anchor) {\n    if (n1 == null) {\n      hostInsert(n2.el = hostCreateComment(n2.children || ''), container, anchor);\n    } else {\n      // there's no support for dynamic comments\n      n2.el = n1.el;\n    }\n  };\n  var mountStaticNode = function mountStaticNode(n2, container, anchor, isSVG) {\n    var _hostInsertStaticCont = hostInsertStaticContent(n2.children, container, anchor, isSVG);\n    var _hostInsertStaticCont2 = _slicedToArray(_hostInsertStaticCont, 2);\n    n2.el = _hostInsertStaticCont2[0];\n    n2.anchor = _hostInsertStaticCont2[1];\n  };\n  /**\r\n   * Dev / HMR only\r\n   */\n  var patchStaticNode = function patchStaticNode(n1, n2, container, isSVG) {\n    // static nodes are only patched during dev for HMR\n    if (n2.children !== n1.children) {\n      var anchor = hostNextSibling(n1.anchor);\n      // remove existing\n      removeStaticNode(n1);\n      var _hostInsertStaticCont3 = hostInsertStaticContent(n2.children, container, anchor, isSVG);\n      var _hostInsertStaticCont4 = _slicedToArray(_hostInsertStaticCont3, 2);\n      n2.el = _hostInsertStaticCont4[0];\n      n2.anchor = _hostInsertStaticCont4[1];\n    } else {\n      n2.el = n1.el;\n      n2.anchor = n1.anchor;\n    }\n  };\n  var moveStaticNode = function moveStaticNode(_ref10, container, nextSibling) {\n    var el = _ref10.el,\n      anchor = _ref10.anchor;\n    var next;\n    while (el && el !== anchor) {\n      next = hostNextSibling(el);\n      hostInsert(el, container, nextSibling);\n      el = next;\n    }\n    hostInsert(anchor, container, nextSibling);\n  };\n  var removeStaticNode = function removeStaticNode(_ref11) {\n    var el = _ref11.el,\n      anchor = _ref11.anchor;\n    var next;\n    while (el && el !== anchor) {\n      next = hostNextSibling(el);\n      hostRemove(el);\n      el = next;\n    }\n    hostRemove(anchor);\n  };\n  var processElement = function processElement(n1, n2, container, anchor, parentComponent, parentSuspense, isSVG, slotScopeIds, optimized) {\n    isSVG = isSVG || n2.type === 'svg';\n    if (n1 == null) {\n      mountElement(n2, container, anchor, parentComponent, parentSuspense, isSVG, slotScopeIds, optimized);\n    } else {\n      patchElement(n1, n2, parentComponent, parentSuspense, isSVG, slotScopeIds, optimized);\n    }\n  };\n  var mountElement = function mountElement(vnode, container, anchor, parentComponent, parentSuspense, isSVG, slotScopeIds, optimized) {\n    var el;\n    var vnodeHook;\n    var type = vnode.type,\n      props = vnode.props,\n      shapeFlag = vnode.shapeFlag,\n      transition = vnode.transition,\n      patchFlag = vnode.patchFlag,\n      dirs = vnode.dirs;\n    if (!(process.env.NODE_ENV !== 'production') && vnode.el && hostCloneNode !== undefined && patchFlag === -1 /* HOISTED */) {\n      // If a vnode has non-null el, it means it's being reused.\n      // Only static vnodes can be reused, so its mounted DOM nodes should be\n      // exactly the same, and we can simply do a clone here.\n      // only do this in production since cloned trees cannot be HMR updated.\n      el = vnode.el = hostCloneNode(vnode.el);\n    } else {\n      el = vnode.el = hostCreateElement(vnode.type, isSVG, props && props.is, props);\n      // mount children first, since some props may rely on child content\n      // being already rendered, e.g. `<select value>`\n      if (shapeFlag & 8 /* TEXT_CHILDREN */) {\n        hostSetElementText(el, vnode.children);\n      } else if (shapeFlag & 16 /* ARRAY_CHILDREN */) {\n        mountChildren(vnode.children, el, null, parentComponent, parentSuspense, isSVG && type !== 'foreignObject', slotScopeIds, optimized);\n      }\n      if (dirs) {\n        invokeDirectiveHook(vnode, null, parentComponent, 'created');\n      }\n      // props\n      if (props) {\n        for (var key in props) {\n          if (key !== 'value' && !isReservedProp(key)) {\n            hostPatchProp(el, key, null, props[key], isSVG, vnode.children, parentComponent, parentSuspense, unmountChildren);\n          }\n        }\n        /**\r\n         * Special case for setting value on DOM elements:\r\n         * - it can be order-sensitive (e.g. should be set *after* min/max, #2325, #4024)\r\n         * - it needs to be forced (#1471)\r\n         * #2353 proposes adding another renderer option to configure this, but\r\n         * the properties affects are so finite it is worth special casing it\r\n         * here to reduce the complexity. (Special casing it also should not\r\n         * affect non-DOM renderers)\r\n         */\n        if ('value' in props) {\n          hostPatchProp(el, 'value', null, props.value);\n        }\n        if (vnodeHook = props.onVnodeBeforeMount) {\n          invokeVNodeHook(vnodeHook, parentComponent, vnode);\n        }\n      }\n      // scopeId\n      setScopeId(el, vnode, vnode.scopeId, slotScopeIds, parentComponent);\n    }\n    if (process.env.NODE_ENV !== 'production' || __VUE_PROD_DEVTOOLS__) {\n      Object.defineProperty(el, '__vnode', {\n        value: vnode,\n        enumerable: false\n      });\n      Object.defineProperty(el, '__vueParentComponent', {\n        value: parentComponent,\n        enumerable: false\n      });\n    }\n    if (dirs) {\n      invokeDirectiveHook(vnode, null, parentComponent, 'beforeMount');\n    }\n    // #1583 For inside suspense + suspense not resolved case, enter hook should call when suspense resolved\n    // #1689 For inside suspense + suspense resolved case, just call it\n    var needCallTransitionHooks = (!parentSuspense || parentSuspense && !parentSuspense.pendingBranch) && transition && !transition.persisted;\n    if (needCallTransitionHooks) {\n      transition.beforeEnter(el);\n    }\n    hostInsert(el, container, anchor);\n    if ((vnodeHook = props && props.onVnodeMounted) || needCallTransitionHooks || dirs) {\n      queuePostRenderEffect(function () {\n        vnodeHook && invokeVNodeHook(vnodeHook, parentComponent, vnode);\n        needCallTransitionHooks && transition.enter(el);\n        dirs && invokeDirectiveHook(vnode, null, parentComponent, 'mounted');\n      }, parentSuspense);\n    }\n  };\n  var setScopeId = function setScopeId(el, vnode, scopeId, slotScopeIds, parentComponent) {\n    if (scopeId) {\n      hostSetScopeId(el, scopeId);\n    }\n    if (slotScopeIds) {\n      for (var i = 0; i < slotScopeIds.length; i++) {\n        hostSetScopeId(el, slotScopeIds[i]);\n      }\n    }\n    if (parentComponent) {\n      var subTree = parentComponent.subTree;\n      if (process.env.NODE_ENV !== 'production' && subTree.patchFlag > 0 && subTree.patchFlag & 2048 /* DEV_ROOT_FRAGMENT */) {\n        subTree = filterSingleRoot(subTree.children) || subTree;\n      }\n      if (vnode === subTree) {\n        var parentVNode = parentComponent.vnode;\n        setScopeId(el, parentVNode, parentVNode.scopeId, parentVNode.slotScopeIds, parentComponent.parent);\n      }\n    }\n  };\n  var mountChildren = function mountChildren(children, container, anchor, parentComponent, parentSuspense, isSVG, slotScopeIds, optimized) {\n    var start = arguments.length > 8 && arguments[8] !== undefined ? arguments[8] : 0;\n    for (var i = start; i < children.length; i++) {\n      var child = children[i] = optimized ? cloneIfMounted(children[i]) : normalizeVNode(children[i]);\n      patch(null, child, container, anchor, parentComponent, parentSuspense, isSVG, slotScopeIds, optimized);\n    }\n  };\n  var patchElement = function patchElement(n1, n2, parentComponent, parentSuspense, isSVG, slotScopeIds, optimized) {\n    var el = n2.el = n1.el;\n    var patchFlag = n2.patchFlag,\n      dynamicChildren = n2.dynamicChildren,\n      dirs = n2.dirs;\n    // #1426 take the old vnode's patch flag into account since user may clone a\n    // compiler-generated vnode, which de-opts to FULL_PROPS\n    patchFlag |= n1.patchFlag & 16 /* FULL_PROPS */;\n    var oldProps = n1.props || EMPTY_OBJ;\n    var newProps = n2.props || EMPTY_OBJ;\n    var vnodeHook;\n    // disable recurse in beforeUpdate hooks\n    parentComponent && toggleRecurse(parentComponent, false);\n    if (vnodeHook = newProps.onVnodeBeforeUpdate) {\n      invokeVNodeHook(vnodeHook, parentComponent, n2, n1);\n    }\n    if (dirs) {\n      invokeDirectiveHook(n2, n1, parentComponent, 'beforeUpdate');\n    }\n    parentComponent && toggleRecurse(parentComponent, true);\n    if (process.env.NODE_ENV !== 'production' && isHmrUpdating) {\n      // HMR updated, force full diff\n      patchFlag = 0;\n      optimized = false;\n      dynamicChildren = null;\n    }\n    var areChildrenSVG = isSVG && n2.type !== 'foreignObject';\n    if (dynamicChildren) {\n      patchBlockChildren(n1.dynamicChildren, dynamicChildren, el, parentComponent, parentSuspense, areChildrenSVG, slotScopeIds);\n      if (process.env.NODE_ENV !== 'production' && parentComponent && parentComponent.type.__hmrId) {\n        traverseStaticChildren(n1, n2);\n      }\n    } else if (!optimized) {\n      // full diff\n      patchChildren(n1, n2, el, null, parentComponent, parentSuspense, areChildrenSVG, slotScopeIds, false);\n    }\n    if (patchFlag > 0) {\n      // the presence of a patchFlag means this element's render code was\n      // generated by the compiler and can take the fast path.\n      // in this path old node and new node are guaranteed to have the same shape\n      // (i.e. at the exact same position in the source template)\n      if (patchFlag & 16 /* FULL_PROPS */) {\n        // element props contain dynamic keys, full diff needed\n        patchProps(el, n2, oldProps, newProps, parentComponent, parentSuspense, isSVG);\n      } else {\n        // class\n        // this flag is matched when the element has dynamic class bindings.\n        if (patchFlag & 2 /* CLASS */) {\n          if (oldProps.class !== newProps.class) {\n            hostPatchProp(el, 'class', null, newProps.class, isSVG);\n          }\n        }\n        // style\n        // this flag is matched when the element has dynamic style bindings\n        if (patchFlag & 4 /* STYLE */) {\n          hostPatchProp(el, 'style', oldProps.style, newProps.style, isSVG);\n        }\n        // props\n        // This flag is matched when the element has dynamic prop/attr bindings\n        // other than class and style. The keys of dynamic prop/attrs are saved for\n        // faster iteration.\n        // Note dynamic keys like :[foo]=\"bar\" will cause this optimization to\n        // bail out and go through a full diff because we need to unset the old key\n        if (patchFlag & 8 /* PROPS */) {\n          // if the flag is present then dynamicProps must be non-null\n          var propsToUpdate = n2.dynamicProps;\n          for (var i = 0; i < propsToUpdate.length; i++) {\n            var key = propsToUpdate[i];\n            var prev = oldProps[key];\n            var next = newProps[key];\n            // #1471 force patch value\n            if (next !== prev || key === 'value') {\n              hostPatchProp(el, key, prev, next, isSVG, n1.children, parentComponent, parentSuspense, unmountChildren);\n            }\n          }\n        }\n      }\n      // text\n      // This flag is matched when the element has only dynamic text children.\n      if (patchFlag & 1 /* TEXT */) {\n        if (n1.children !== n2.children) {\n          hostSetElementText(el, n2.children);\n        }\n      }\n    } else if (!optimized && dynamicChildren == null) {\n      // unoptimized, full diff\n      patchProps(el, n2, oldProps, newProps, parentComponent, parentSuspense, isSVG);\n    }\n    if ((vnodeHook = newProps.onVnodeUpdated) || dirs) {\n      queuePostRenderEffect(function () {\n        vnodeHook && invokeVNodeHook(vnodeHook, parentComponent, n2, n1);\n        dirs && invokeDirectiveHook(n2, n1, parentComponent, 'updated');\n      }, parentSuspense);\n    }\n  };\n  // The fast path for blocks.\n  var patchBlockChildren = function patchBlockChildren(oldChildren, newChildren, fallbackContainer, parentComponent, parentSuspense, isSVG, slotScopeIds) {\n    for (var i = 0; i < newChildren.length; i++) {\n      var oldVNode = oldChildren[i];\n      var newVNode = newChildren[i];\n      // Determine the container (parent element) for the patch.\n      var container =\n      // oldVNode may be an errored async setup() component inside Suspense\n      // which will not have a mounted element\n      oldVNode.el && (\n      // - In the case of a Fragment, we need to provide the actual parent\n      // of the Fragment itself so it can move its children.\n      oldVNode.type === Fragment ||\n      // - In the case of different nodes, there is going to be a replacement\n      // which also requires the correct parent container\n      !isSameVNodeType(oldVNode, newVNode) ||\n      // - In the case of a component, it could contain anything.\n      oldVNode.shapeFlag & (6 /* COMPONENT */ | 64 /* TELEPORT */)) ? hostParentNode(oldVNode.el) :\n      // In other cases, the parent container is not actually used so we\n      // just pass the block element here to avoid a DOM parentNode call.\n      fallbackContainer;\n      patch(oldVNode, newVNode, container, null, parentComponent, parentSuspense, isSVG, slotScopeIds, true);\n    }\n  };\n  var patchProps = function patchProps(el, vnode, oldProps, newProps, parentComponent, parentSuspense, isSVG) {\n    if (oldProps !== newProps) {\n      for (var key in newProps) {\n        // empty string is not valid prop\n        if (isReservedProp(key)) continue;\n        var next = newProps[key];\n        var prev = oldProps[key];\n        // defer patching value\n        if (next !== prev && key !== 'value') {\n          hostPatchProp(el, key, prev, next, isSVG, vnode.children, parentComponent, parentSuspense, unmountChildren);\n        }\n      }\n      if (oldProps !== EMPTY_OBJ) {\n        for (var _key13 in oldProps) {\n          if (!isReservedProp(_key13) && !(_key13 in newProps)) {\n            hostPatchProp(el, _key13, oldProps[_key13], null, isSVG, vnode.children, parentComponent, parentSuspense, unmountChildren);\n          }\n        }\n      }\n      if ('value' in newProps) {\n        hostPatchProp(el, 'value', oldProps.value, newProps.value);\n      }\n    }\n  };\n  var processFragment = function processFragment(n1, n2, container, anchor, parentComponent, parentSuspense, isSVG, slotScopeIds, optimized) {\n    var fragmentStartAnchor = n2.el = n1 ? n1.el : hostCreateText('');\n    var fragmentEndAnchor = n2.anchor = n1 ? n1.anchor : hostCreateText('');\n    var patchFlag = n2.patchFlag,\n      dynamicChildren = n2.dynamicChildren,\n      fragmentSlotScopeIds = n2.slotScopeIds;\n    if (process.env.NODE_ENV !== 'production' && isHmrUpdating) {\n      // HMR updated, force full diff\n      patchFlag = 0;\n      optimized = false;\n      dynamicChildren = null;\n    }\n    // check if this is a slot fragment with :slotted scope ids\n    if (fragmentSlotScopeIds) {\n      slotScopeIds = slotScopeIds ? slotScopeIds.concat(fragmentSlotScopeIds) : fragmentSlotScopeIds;\n    }\n    if (n1 == null) {\n      hostInsert(fragmentStartAnchor, container, anchor);\n      hostInsert(fragmentEndAnchor, container, anchor);\n      // a fragment can only have array children\n      // since they are either generated by the compiler, or implicitly created\n      // from arrays.\n      mountChildren(n2.children, container, fragmentEndAnchor, parentComponent, parentSuspense, isSVG, slotScopeIds, optimized);\n    } else {\n      if (patchFlag > 0 && patchFlag & 64 /* STABLE_FRAGMENT */ && dynamicChildren &&\n      // #2715 the previous fragment could've been a BAILed one as a result\n      // of renderSlot() with no valid children\n      n1.dynamicChildren) {\n        // a stable fragment (template root or <template v-for>) doesn't need to\n        // patch children order, but it may contain dynamicChildren.\n        patchBlockChildren(n1.dynamicChildren, dynamicChildren, container, parentComponent, parentSuspense, isSVG, slotScopeIds);\n        if (process.env.NODE_ENV !== 'production' && parentComponent && parentComponent.type.__hmrId) {\n          traverseStaticChildren(n1, n2);\n        } else if (\n        // #2080 if the stable fragment has a key, it's a <template v-for> that may\n        //  get moved around. Make sure all root level vnodes inherit el.\n        // #2134 or if it's a component root, it may also get moved around\n        // as the component is being moved.\n        n2.key != null || parentComponent && n2 === parentComponent.subTree) {\n          traverseStaticChildren(n1, n2, true /* shallow */);\n        }\n      } else {\n        // keyed / unkeyed, or manual fragments.\n        // for keyed & unkeyed, since they are compiler generated from v-for,\n        // each child is guaranteed to be a block so the fragment will never\n        // have dynamicChildren.\n        patchChildren(n1, n2, container, fragmentEndAnchor, parentComponent, parentSuspense, isSVG, slotScopeIds, optimized);\n      }\n    }\n  };\n  var processComponent = function processComponent(n1, n2, container, anchor, parentComponent, parentSuspense, isSVG, slotScopeIds, optimized) {\n    n2.slotScopeIds = slotScopeIds;\n    if (n1 == null) {\n      if (n2.shapeFlag & 512 /* COMPONENT_KEPT_ALIVE */) {\n        parentComponent.ctx.activate(n2, container, anchor, isSVG, optimized);\n      } else {\n        mountComponent(n2, container, anchor, parentComponent, parentSuspense, isSVG, optimized);\n      }\n    } else {\n      updateComponent(n1, n2, optimized);\n    }\n  };\n  var mountComponent = function mountComponent(initialVNode, container, anchor, parentComponent, parentSuspense, isSVG, optimized) {\n    var instance = initialVNode.component = createComponentInstance(initialVNode, parentComponent, parentSuspense);\n    if (process.env.NODE_ENV !== 'production' && instance.type.__hmrId) {\n      registerHMR(instance);\n    }\n    if (process.env.NODE_ENV !== 'production') {\n      pushWarningContext(initialVNode);\n      startMeasure(instance, \"mount\");\n    }\n    // inject renderer internals for keepAlive\n    if (isKeepAlive(initialVNode)) {\n      instance.ctx.renderer = internals;\n    }\n    // resolve props and slots for setup context\n    {\n      if (process.env.NODE_ENV !== 'production') {\n        startMeasure(instance, \"init\");\n      }\n      setupComponent(instance);\n      if (process.env.NODE_ENV !== 'production') {\n        endMeasure(instance, \"init\");\n      }\n    }\n    // setup() is async. This component relies on async logic to be resolved\n    // before proceeding\n    if (instance.asyncDep) {\n      parentSuspense && parentSuspense.registerDep(instance, setupRenderEffect);\n      // Give it a placeholder if this is not hydration\n      // TODO handle self-defined fallback\n      if (!initialVNode.el) {\n        var placeholder = instance.subTree = createVNode(Comment);\n        processCommentNode(null, placeholder, container, anchor);\n      }\n      return;\n    }\n    setupRenderEffect(instance, initialVNode, container, anchor, parentSuspense, isSVG, optimized);\n    if (process.env.NODE_ENV !== 'production') {\n      popWarningContext();\n      endMeasure(instance, \"mount\");\n    }\n  };\n  var updateComponent = function updateComponent(n1, n2, optimized) {\n    var instance = n2.component = n1.component;\n    if (shouldUpdateComponent(n1, n2, optimized)) {\n      if (instance.asyncDep && !instance.asyncResolved) {\n        // async & still pending - just update props and slots\n        // since the component's reactive effect for render isn't set-up yet\n        if (process.env.NODE_ENV !== 'production') {\n          pushWarningContext(n2);\n        }\n        updateComponentPreRender(instance, n2, optimized);\n        if (process.env.NODE_ENV !== 'production') {\n          popWarningContext();\n        }\n        return;\n      } else {\n        // normal update\n        instance.next = n2;\n        // in case the child component is also queued, remove it to avoid\n        // double updating the same child component in the same flush.\n        invalidateJob(instance.update);\n        // instance.update is the reactive effect.\n        instance.update();\n      }\n    } else {\n      // no update needed. just copy over properties\n      n2.component = n1.component;\n      n2.el = n1.el;\n      instance.vnode = n2;\n    }\n  };\n  var setupRenderEffect = function setupRenderEffect(instance, initialVNode, container, anchor, parentSuspense, isSVG, optimized) {\n    var componentUpdateFn = function componentUpdateFn() {\n      if (!instance.isMounted) {\n        var vnodeHook;\n        var _initialVNode = initialVNode,\n          el = _initialVNode.el,\n          props = _initialVNode.props;\n        var bm = instance.bm,\n          m = instance.m,\n          parent = instance.parent;\n        var isAsyncWrapperVNode = isAsyncWrapper(initialVNode);\n        toggleRecurse(instance, false);\n        // beforeMount hook\n        if (bm) {\n          invokeArrayFns(bm);\n        }\n        // onVnodeBeforeMount\n        if (!isAsyncWrapperVNode && (vnodeHook = props && props.onVnodeBeforeMount)) {\n          invokeVNodeHook(vnodeHook, parent, initialVNode);\n        }\n        toggleRecurse(instance, true);\n        if (el && hydrateNode) {\n          // vnode has adopted host node - perform hydration instead of mount.\n          var hydrateSubTree = function hydrateSubTree() {\n            if (process.env.NODE_ENV !== 'production') {\n              startMeasure(instance, \"render\");\n            }\n            instance.subTree = renderComponentRoot(instance);\n            if (process.env.NODE_ENV !== 'production') {\n              endMeasure(instance, \"render\");\n            }\n            if (process.env.NODE_ENV !== 'production') {\n              startMeasure(instance, \"hydrate\");\n            }\n            hydrateNode(el, instance.subTree, instance, parentSuspense, null);\n            if (process.env.NODE_ENV !== 'production') {\n              endMeasure(instance, \"hydrate\");\n            }\n          };\n          if (isAsyncWrapperVNode) {\n            initialVNode.type.__asyncLoader().then(\n            // note: we are moving the render call into an async callback,\n            // which means it won't track dependencies - but it's ok because\n            // a server-rendered async wrapper is already in resolved state\n            // and it will never need to change.\n            function () {\n              return !instance.isUnmounted && hydrateSubTree();\n            });\n          } else {\n            hydrateSubTree();\n          }\n        } else {\n          if (process.env.NODE_ENV !== 'production') {\n            startMeasure(instance, \"render\");\n          }\n          var subTree = instance.subTree = renderComponentRoot(instance);\n          if (process.env.NODE_ENV !== 'production') {\n            endMeasure(instance, \"render\");\n          }\n          if (process.env.NODE_ENV !== 'production') {\n            startMeasure(instance, \"patch\");\n          }\n          patch(null, subTree, container, anchor, instance, parentSuspense, isSVG);\n          if (process.env.NODE_ENV !== 'production') {\n            endMeasure(instance, \"patch\");\n          }\n          initialVNode.el = subTree.el;\n        }\n        // mounted hook\n        if (m) {\n          queuePostRenderEffect(m, parentSuspense);\n        }\n        // onVnodeMounted\n        if (!isAsyncWrapperVNode && (vnodeHook = props && props.onVnodeMounted)) {\n          var scopedInitialVNode = initialVNode;\n          queuePostRenderEffect(function () {\n            return invokeVNodeHook(vnodeHook, parent, scopedInitialVNode);\n          }, parentSuspense);\n        }\n        // activated hook for keep-alive roots.\n        // #1742 activated hook must be accessed after first render\n        // since the hook may be injected by a child keep-alive\n        if (initialVNode.shapeFlag & 256 /* COMPONENT_SHOULD_KEEP_ALIVE */) {\n          instance.a && queuePostRenderEffect(instance.a, parentSuspense);\n        }\n        instance.isMounted = true;\n        if (process.env.NODE_ENV !== 'production' || __VUE_PROD_DEVTOOLS__) {\n          devtoolsComponentAdded(instance);\n        }\n        // #2458: deference mount-only object parameters to prevent memleaks\n        initialVNode = container = anchor = null;\n      } else {\n        // updateComponent\n        // This is triggered by mutation of component's own state (next: null)\n        // OR parent calling processComponent (next: VNode)\n        var next = instance.next,\n          bu = instance.bu,\n          u = instance.u,\n          _parent = instance.parent,\n          vnode = instance.vnode;\n        var originNext = next;\n        var _vnodeHook;\n        if (process.env.NODE_ENV !== 'production') {\n          pushWarningContext(next || instance.vnode);\n        }\n        // Disallow component effect recursion during pre-lifecycle hooks.\n        toggleRecurse(instance, false);\n        if (next) {\n          next.el = vnode.el;\n          updateComponentPreRender(instance, next, optimized);\n        } else {\n          next = vnode;\n        }\n        // beforeUpdate hook\n        if (bu) {\n          invokeArrayFns(bu);\n        }\n        // onVnodeBeforeUpdate\n        if (_vnodeHook = next.props && next.props.onVnodeBeforeUpdate) {\n          invokeVNodeHook(_vnodeHook, _parent, next, vnode);\n        }\n        toggleRecurse(instance, true);\n        // render\n        if (process.env.NODE_ENV !== 'production') {\n          startMeasure(instance, \"render\");\n        }\n        var nextTree = renderComponentRoot(instance);\n        if (process.env.NODE_ENV !== 'production') {\n          endMeasure(instance, \"render\");\n        }\n        var prevTree = instance.subTree;\n        instance.subTree = nextTree;\n        if (process.env.NODE_ENV !== 'production') {\n          startMeasure(instance, \"patch\");\n        }\n        patch(prevTree, nextTree,\n        // parent may have changed if it's in a teleport\n        hostParentNode(prevTree.el),\n        // anchor may have changed if it's in a fragment\n        getNextHostNode(prevTree), instance, parentSuspense, isSVG);\n        if (process.env.NODE_ENV !== 'production') {\n          endMeasure(instance, \"patch\");\n        }\n        next.el = nextTree.el;\n        if (originNext === null) {\n          // self-triggered update. In case of HOC, update parent component\n          // vnode el. HOC is indicated by parent instance's subTree pointing\n          // to child component's vnode\n          updateHOCHostEl(instance, nextTree.el);\n        }\n        // updated hook\n        if (u) {\n          queuePostRenderEffect(u, parentSuspense);\n        }\n        // onVnodeUpdated\n        if (_vnodeHook = next.props && next.props.onVnodeUpdated) {\n          queuePostRenderEffect(function () {\n            return invokeVNodeHook(_vnodeHook, _parent, next, vnode);\n          }, parentSuspense);\n        }\n        if (process.env.NODE_ENV !== 'production' || __VUE_PROD_DEVTOOLS__) {\n          devtoolsComponentUpdated(instance);\n        }\n        if (process.env.NODE_ENV !== 'production') {\n          popWarningContext();\n        }\n      }\n    };\n    // create reactive effect for rendering\n    var effect = instance.effect = new ReactiveEffect(componentUpdateFn, function () {\n      return queueJob(instance.update);\n    }, instance.scope // track it in component's effect scope\n    );\n\n    var update = instance.update = effect.run.bind(effect);\n    update.id = instance.uid;\n    // allowRecurse\n    // #1801, #2043 component render effects should allow recursive updates\n    toggleRecurse(instance, true);\n    if (process.env.NODE_ENV !== 'production') {\n      effect.onTrack = instance.rtc ? function (e) {\n        return invokeArrayFns(instance.rtc, e);\n      } : void 0;\n      effect.onTrigger = instance.rtg ? function (e) {\n        return invokeArrayFns(instance.rtg, e);\n      } : void 0;\n      // @ts-ignore (for scheduler)\n      update.ownerInstance = instance;\n    }\n    update();\n  };\n  var updateComponentPreRender = function updateComponentPreRender(instance, nextVNode, optimized) {\n    nextVNode.component = instance;\n    var prevProps = instance.vnode.props;\n    instance.vnode = nextVNode;\n    instance.next = null;\n    updateProps(instance, nextVNode.props, prevProps, optimized);\n    updateSlots(instance, nextVNode.children, optimized);\n    pauseTracking();\n    // props update may have triggered pre-flush watchers.\n    // flush them before the render update.\n    flushPreFlushCbs(undefined, instance.update);\n    resetTracking();\n  };\n  var patchChildren = function patchChildren(n1, n2, container, anchor, parentComponent, parentSuspense, isSVG, slotScopeIds) {\n    var optimized = arguments.length > 8 && arguments[8] !== undefined ? arguments[8] : false;\n    var c1 = n1 && n1.children;\n    var prevShapeFlag = n1 ? n1.shapeFlag : 0;\n    var c2 = n2.children;\n    var patchFlag = n2.patchFlag,\n      shapeFlag = n2.shapeFlag;\n    // fast path\n    if (patchFlag > 0) {\n      if (patchFlag & 128 /* KEYED_FRAGMENT */) {\n        // this could be either fully-keyed or mixed (some keyed some not)\n        // presence of patchFlag means children are guaranteed to be arrays\n        patchKeyedChildren(c1, c2, container, anchor, parentComponent, parentSuspense, isSVG, slotScopeIds, optimized);\n        return;\n      } else if (patchFlag & 256 /* UNKEYED_FRAGMENT */) {\n        // unkeyed\n        patchUnkeyedChildren(c1, c2, container, anchor, parentComponent, parentSuspense, isSVG, slotScopeIds, optimized);\n        return;\n      }\n    }\n    // children has 3 possibilities: text, array or no children.\n    if (shapeFlag & 8 /* TEXT_CHILDREN */) {\n      // text children fast path\n      if (prevShapeFlag & 16 /* ARRAY_CHILDREN */) {\n        unmountChildren(c1, parentComponent, parentSuspense);\n      }\n      if (c2 !== c1) {\n        hostSetElementText(container, c2);\n      }\n    } else {\n      if (prevShapeFlag & 16 /* ARRAY_CHILDREN */) {\n        // prev children was array\n        if (shapeFlag & 16 /* ARRAY_CHILDREN */) {\n          // two arrays, cannot assume anything, do full diff\n          patchKeyedChildren(c1, c2, container, anchor, parentComponent, parentSuspense, isSVG, slotScopeIds, optimized);\n        } else {\n          // no new children, just unmount old\n          unmountChildren(c1, parentComponent, parentSuspense, true);\n        }\n      } else {\n        // prev children was text OR null\n        // new children is array OR null\n        if (prevShapeFlag & 8 /* TEXT_CHILDREN */) {\n          hostSetElementText(container, '');\n        }\n        // mount new if array\n        if (shapeFlag & 16 /* ARRAY_CHILDREN */) {\n          mountChildren(c2, container, anchor, parentComponent, parentSuspense, isSVG, slotScopeIds, optimized);\n        }\n      }\n    }\n  };\n  var patchUnkeyedChildren = function patchUnkeyedChildren(c1, c2, container, anchor, parentComponent, parentSuspense, isSVG, slotScopeIds, optimized) {\n    c1 = c1 || EMPTY_ARR;\n    c2 = c2 || EMPTY_ARR;\n    var oldLength = c1.length;\n    var newLength = c2.length;\n    var commonLength = Math.min(oldLength, newLength);\n    var i;\n    for (i = 0; i < commonLength; i++) {\n      var nextChild = c2[i] = optimized ? cloneIfMounted(c2[i]) : normalizeVNode(c2[i]);\n      patch(c1[i], nextChild, container, null, parentComponent, parentSuspense, isSVG, slotScopeIds, optimized);\n    }\n    if (oldLength > newLength) {\n      // remove old\n      unmountChildren(c1, parentComponent, parentSuspense, true, false, commonLength);\n    } else {\n      // mount new\n      mountChildren(c2, container, anchor, parentComponent, parentSuspense, isSVG, slotScopeIds, optimized, commonLength);\n    }\n  };\n  // can be all-keyed or mixed\n  var patchKeyedChildren = function patchKeyedChildren(c1, c2, container, parentAnchor, parentComponent, parentSuspense, isSVG, slotScopeIds, optimized) {\n    var i = 0;\n    var l2 = c2.length;\n    var e1 = c1.length - 1; // prev ending index\n    var e2 = l2 - 1; // next ending index\n    // 1. sync from start\n    // (a b) c\n    // (a b) d e\n    while (i <= e1 && i <= e2) {\n      var n1 = c1[i];\n      var n2 = c2[i] = optimized ? cloneIfMounted(c2[i]) : normalizeVNode(c2[i]);\n      if (isSameVNodeType(n1, n2)) {\n        patch(n1, n2, container, null, parentComponent, parentSuspense, isSVG, slotScopeIds, optimized);\n      } else {\n        break;\n      }\n      i++;\n    }\n    // 2. sync from end\n    // a (b c)\n    // d e (b c)\n    while (i <= e1 && i <= e2) {\n      var _n2 = c1[e1];\n      var _n3 = c2[e2] = optimized ? cloneIfMounted(c2[e2]) : normalizeVNode(c2[e2]);\n      if (isSameVNodeType(_n2, _n3)) {\n        patch(_n2, _n3, container, null, parentComponent, parentSuspense, isSVG, slotScopeIds, optimized);\n      } else {\n        break;\n      }\n      e1--;\n      e2--;\n    }\n    // 3. common sequence + mount\n    // (a b)\n    // (a b) c\n    // i = 2, e1 = 1, e2 = 2\n    // (a b)\n    // c (a b)\n    // i = 0, e1 = -1, e2 = 0\n    if (i > e1) {\n      if (i <= e2) {\n        var nextPos = e2 + 1;\n        var anchor = nextPos < l2 ? c2[nextPos].el : parentAnchor;\n        while (i <= e2) {\n          patch(null, c2[i] = optimized ? cloneIfMounted(c2[i]) : normalizeVNode(c2[i]), container, anchor, parentComponent, parentSuspense, isSVG, slotScopeIds, optimized);\n          i++;\n        }\n      }\n    }\n    // 4. common sequence + unmount\n    // (a b) c\n    // (a b)\n    // i = 2, e1 = 2, e2 = 1\n    // a (b c)\n    // (b c)\n    // i = 0, e1 = 0, e2 = -1\n    else if (i > e2) {\n      while (i <= e1) {\n        unmount(c1[i], parentComponent, parentSuspense, true);\n        i++;\n      }\n    }\n    // 5. unknown sequence\n    // [i ... e1 + 1]: a b [c d e] f g\n    // [i ... e2 + 1]: a b [e d c h] f g\n    // i = 2, e1 = 4, e2 = 5\n    else {\n      var s1 = i; // prev starting index\n      var s2 = i; // next starting index\n      // 5.1 build key:index map for newChildren\n      var keyToNewIndexMap = new Map();\n      for (i = s2; i <= e2; i++) {\n        var nextChild = c2[i] = optimized ? cloneIfMounted(c2[i]) : normalizeVNode(c2[i]);\n        if (nextChild.key != null) {\n          if (process.env.NODE_ENV !== 'production' && keyToNewIndexMap.has(nextChild.key)) {\n            warn(\"Duplicate keys found during update:\", JSON.stringify(nextChild.key), \"Make sure keys are unique.\");\n          }\n          keyToNewIndexMap.set(nextChild.key, i);\n        }\n      }\n      // 5.2 loop through old children left to be patched and try to patch\n      // matching nodes & remove nodes that are no longer present\n      var j;\n      var patched = 0;\n      var toBePatched = e2 - s2 + 1;\n      var moved = false;\n      // used to track whether any node has moved\n      var maxNewIndexSoFar = 0;\n      // works as Map<newIndex, oldIndex>\n      // Note that oldIndex is offset by +1\n      // and oldIndex = 0 is a special value indicating the new node has\n      // no corresponding old node.\n      // used for determining longest stable subsequence\n      var newIndexToOldIndexMap = new Array(toBePatched);\n      for (i = 0; i < toBePatched; i++) newIndexToOldIndexMap[i] = 0;\n      for (i = s1; i <= e1; i++) {\n        var prevChild = c1[i];\n        if (patched >= toBePatched) {\n          // all new children have been patched so this can only be a removal\n          unmount(prevChild, parentComponent, parentSuspense, true);\n          continue;\n        }\n        var newIndex = void 0;\n        if (prevChild.key != null) {\n          newIndex = keyToNewIndexMap.get(prevChild.key);\n        } else {\n          // key-less node, try to locate a key-less node of the same type\n          for (j = s2; j <= e2; j++) {\n            if (newIndexToOldIndexMap[j - s2] === 0 && isSameVNodeType(prevChild, c2[j])) {\n              newIndex = j;\n              break;\n            }\n          }\n        }\n        if (newIndex === undefined) {\n          unmount(prevChild, parentComponent, parentSuspense, true);\n        } else {\n          newIndexToOldIndexMap[newIndex - s2] = i + 1;\n          if (newIndex >= maxNewIndexSoFar) {\n            maxNewIndexSoFar = newIndex;\n          } else {\n            moved = true;\n          }\n          patch(prevChild, c2[newIndex], container, null, parentComponent, parentSuspense, isSVG, slotScopeIds, optimized);\n          patched++;\n        }\n      }\n      // 5.3 move and mount\n      // generate longest stable subsequence only when nodes have moved\n      var increasingNewIndexSequence = moved ? getSequence(newIndexToOldIndexMap) : EMPTY_ARR;\n      j = increasingNewIndexSequence.length - 1;\n      // looping backwards so that we can use last patched node as anchor\n      for (i = toBePatched - 1; i >= 0; i--) {\n        var nextIndex = s2 + i;\n        var _nextChild = c2[nextIndex];\n        var _anchor2 = nextIndex + 1 < l2 ? c2[nextIndex + 1].el : parentAnchor;\n        if (newIndexToOldIndexMap[i] === 0) {\n          // mount new\n          patch(null, _nextChild, container, _anchor2, parentComponent, parentSuspense, isSVG, slotScopeIds, optimized);\n        } else if (moved) {\n          // move if:\n          // There is no stable subsequence (e.g. a reverse)\n          // OR current node is not among the stable sequence\n          if (j < 0 || i !== increasingNewIndexSequence[j]) {\n            move(_nextChild, container, _anchor2, 2 /* REORDER */);\n          } else {\n            j--;\n          }\n        }\n      }\n    }\n  };\n  var move = function move(vnode, container, anchor, moveType) {\n    var parentSuspense = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : null;\n    var el = vnode.el,\n      type = vnode.type,\n      transition = vnode.transition,\n      children = vnode.children,\n      shapeFlag = vnode.shapeFlag;\n    if (shapeFlag & 6 /* COMPONENT */) {\n      move(vnode.component.subTree, container, anchor, moveType);\n      return;\n    }\n    if (shapeFlag & 128 /* SUSPENSE */) {\n      vnode.suspense.move(container, anchor, moveType);\n      return;\n    }\n    if (shapeFlag & 64 /* TELEPORT */) {\n      type.move(vnode, container, anchor, internals);\n      return;\n    }\n    if (type === Fragment) {\n      hostInsert(el, container, anchor);\n      for (var i = 0; i < children.length; i++) {\n        move(children[i], container, anchor, moveType);\n      }\n      hostInsert(vnode.anchor, container, anchor);\n      return;\n    }\n    if (type === Static) {\n      moveStaticNode(vnode, container, anchor);\n      return;\n    }\n    // single nodes\n    var needTransition = moveType !== 2 /* REORDER */ && shapeFlag & 1 /* ELEMENT */ && transition;\n    if (needTransition) {\n      if (moveType === 0 /* ENTER */) {\n        transition.beforeEnter(el);\n        hostInsert(el, container, anchor);\n        queuePostRenderEffect(function () {\n          return transition.enter(el);\n        }, parentSuspense);\n      } else {\n        var leave = transition.leave,\n          delayLeave = transition.delayLeave,\n          afterLeave = transition.afterLeave;\n        var _remove = function _remove() {\n          return hostInsert(el, container, anchor);\n        };\n        var performLeave = function performLeave() {\n          leave(el, function () {\n            _remove();\n            afterLeave && afterLeave();\n          });\n        };\n        if (delayLeave) {\n          delayLeave(el, _remove, performLeave);\n        } else {\n          performLeave();\n        }\n      }\n    } else {\n      hostInsert(el, container, anchor);\n    }\n  };\n  var unmount = function unmount(vnode, parentComponent, parentSuspense) {\n    var doRemove = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : false;\n    var optimized = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : false;\n    var type = vnode.type,\n      props = vnode.props,\n      ref = vnode.ref,\n      children = vnode.children,\n      dynamicChildren = vnode.dynamicChildren,\n      shapeFlag = vnode.shapeFlag,\n      patchFlag = vnode.patchFlag,\n      dirs = vnode.dirs;\n    // unset ref\n    if (ref != null) {\n      setRef(ref, null, parentSuspense, vnode, true);\n    }\n    if (shapeFlag & 256 /* COMPONENT_SHOULD_KEEP_ALIVE */) {\n      parentComponent.ctx.deactivate(vnode);\n      return;\n    }\n    var shouldInvokeDirs = shapeFlag & 1 /* ELEMENT */ && dirs;\n    var shouldInvokeVnodeHook = !isAsyncWrapper(vnode);\n    var vnodeHook;\n    if (shouldInvokeVnodeHook && (vnodeHook = props && props.onVnodeBeforeUnmount)) {\n      invokeVNodeHook(vnodeHook, parentComponent, vnode);\n    }\n    if (shapeFlag & 6 /* COMPONENT */) {\n      unmountComponent(vnode.component, parentSuspense, doRemove);\n    } else {\n      if (shapeFlag & 128 /* SUSPENSE */) {\n        vnode.suspense.unmount(parentSuspense, doRemove);\n        return;\n      }\n      if (shouldInvokeDirs) {\n        invokeDirectiveHook(vnode, null, parentComponent, 'beforeUnmount');\n      }\n      if (shapeFlag & 64 /* TELEPORT */) {\n        vnode.type.remove(vnode, parentComponent, parentSuspense, optimized, internals, doRemove);\n      } else if (dynamicChildren && (\n      // #1153: fast path should not be taken for non-stable (v-for) fragments\n      type !== Fragment || patchFlag > 0 && patchFlag & 64 /* STABLE_FRAGMENT */)) {\n        // fast path for block nodes: only need to unmount dynamic children.\n        unmountChildren(dynamicChildren, parentComponent, parentSuspense, false, true);\n      } else if (type === Fragment && patchFlag & (128 /* KEYED_FRAGMENT */ | 256 /* UNKEYED_FRAGMENT */) || !optimized && shapeFlag & 16 /* ARRAY_CHILDREN */) {\n        unmountChildren(children, parentComponent, parentSuspense);\n      }\n      if (doRemove) {\n        remove(vnode);\n      }\n    }\n    if (shouldInvokeVnodeHook && (vnodeHook = props && props.onVnodeUnmounted) || shouldInvokeDirs) {\n      queuePostRenderEffect(function () {\n        vnodeHook && invokeVNodeHook(vnodeHook, parentComponent, vnode);\n        shouldInvokeDirs && invokeDirectiveHook(vnode, null, parentComponent, 'unmounted');\n      }, parentSuspense);\n    }\n  };\n  var remove = function remove(vnode) {\n    var type = vnode.type,\n      el = vnode.el,\n      anchor = vnode.anchor,\n      transition = vnode.transition;\n    if (type === Fragment) {\n      removeFragment(el, anchor);\n      return;\n    }\n    if (type === Static) {\n      removeStaticNode(vnode);\n      return;\n    }\n    var performRemove = function performRemove() {\n      hostRemove(el);\n      if (transition && !transition.persisted && transition.afterLeave) {\n        transition.afterLeave();\n      }\n    };\n    if (vnode.shapeFlag & 1 /* ELEMENT */ && transition && !transition.persisted) {\n      var leave = transition.leave,\n        delayLeave = transition.delayLeave;\n      var performLeave = function performLeave() {\n        return leave(el, performRemove);\n      };\n      if (delayLeave) {\n        delayLeave(vnode.el, performRemove, performLeave);\n      } else {\n        performLeave();\n      }\n    } else {\n      performRemove();\n    }\n  };\n  var removeFragment = function removeFragment(cur, end) {\n    // For fragments, directly remove all contained DOM nodes.\n    // (fragment child nodes cannot have transition)\n    var next;\n    while (cur !== end) {\n      next = hostNextSibling(cur);\n      hostRemove(cur);\n      cur = next;\n    }\n    hostRemove(end);\n  };\n  var unmountComponent = function unmountComponent(instance, parentSuspense, doRemove) {\n    if (process.env.NODE_ENV !== 'production' && instance.type.__hmrId) {\n      unregisterHMR(instance);\n    }\n    var bum = instance.bum,\n      scope = instance.scope,\n      update = instance.update,\n      subTree = instance.subTree,\n      um = instance.um;\n    // beforeUnmount hook\n    if (bum) {\n      invokeArrayFns(bum);\n    }\n    // stop effects in component scope\n    scope.stop();\n    // update may be null if a component is unmounted before its async\n    // setup has resolved.\n    if (update) {\n      // so that scheduler will no longer invoke it\n      update.active = false;\n      unmount(subTree, instance, parentSuspense, doRemove);\n    }\n    // unmounted hook\n    if (um) {\n      queuePostRenderEffect(um, parentSuspense);\n    }\n    queuePostRenderEffect(function () {\n      instance.isUnmounted = true;\n    }, parentSuspense);\n    // A component with async dep inside a pending suspense is unmounted before\n    // its async dep resolves. This should remove the dep from the suspense, and\n    // cause the suspense to resolve immediately if that was the last dep.\n    if (parentSuspense && parentSuspense.pendingBranch && !parentSuspense.isUnmounted && instance.asyncDep && !instance.asyncResolved && instance.suspenseId === parentSuspense.pendingId) {\n      parentSuspense.deps--;\n      if (parentSuspense.deps === 0) {\n        parentSuspense.resolve();\n      }\n    }\n    if (process.env.NODE_ENV !== 'production' || __VUE_PROD_DEVTOOLS__) {\n      devtoolsComponentRemoved(instance);\n    }\n  };\n  var unmountChildren = function unmountChildren(children, parentComponent, parentSuspense) {\n    var doRemove = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : false;\n    var optimized = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : false;\n    var start = arguments.length > 5 && arguments[5] !== undefined ? arguments[5] : 0;\n    for (var i = start; i < children.length; i++) {\n      unmount(children[i], parentComponent, parentSuspense, doRemove, optimized);\n    }\n  };\n  var getNextHostNode = function getNextHostNode(vnode) {\n    if (vnode.shapeFlag & 6 /* COMPONENT */) {\n      return getNextHostNode(vnode.component.subTree);\n    }\n    if (vnode.shapeFlag & 128 /* SUSPENSE */) {\n      return vnode.suspense.next();\n    }\n    return hostNextSibling(vnode.anchor || vnode.el);\n  };\n  var render = function render(vnode, container, isSVG) {\n    if (vnode == null) {\n      if (container._vnode) {\n        unmount(container._vnode, null, null, true);\n      }\n    } else {\n      patch(container._vnode || null, vnode, container, null, null, null, isSVG);\n    }\n    flushPostFlushCbs();\n    container._vnode = vnode;\n  };\n  var internals = {\n    p: patch,\n    um: unmount,\n    m: move,\n    r: remove,\n    mt: mountComponent,\n    mc: mountChildren,\n    pc: patchChildren,\n    pbc: patchBlockChildren,\n    n: getNextHostNode,\n    o: options\n  };\n  var hydrate;\n  var hydrateNode;\n  if (createHydrationFns) {\n    var _createHydrationFns = createHydrationFns(internals);\n    var _createHydrationFns2 = _slicedToArray(_createHydrationFns, 2);\n    hydrate = _createHydrationFns2[0];\n    hydrateNode = _createHydrationFns2[1];\n  }\n  return {\n    render: render,\n    hydrate: hydrate,\n    createApp: createAppAPI(render, hydrate)\n  };\n}\nfunction toggleRecurse(_ref12, allowed) {\n  var effect = _ref12.effect,\n    update = _ref12.update;\n  effect.allowRecurse = update.allowRecurse = allowed;\n}\n/**\r\n * #1156\r\n * When a component is HMR-enabled, we need to make sure that all static nodes\r\n * inside a block also inherit the DOM element from the previous tree so that\r\n * HMR updates (which are full updates) can retrieve the element for patching.\r\n *\r\n * #2080\r\n * Inside keyed `template` fragment static children, if a fragment is moved,\r\n * the children will always be moved. Therefore, in order to ensure correct move\r\n * position, el should be inherited from previous nodes.\r\n */\nfunction traverseStaticChildren(n1, n2) {\n  var shallow = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n  var ch1 = n1.children;\n  var ch2 = n2.children;\n  if (isArray(ch1) && isArray(ch2)) {\n    for (var i = 0; i < ch1.length; i++) {\n      // this is only called in the optimized path so array children are\n      // guaranteed to be vnodes\n      var c1 = ch1[i];\n      var c2 = ch2[i];\n      if (c2.shapeFlag & 1 /* ELEMENT */ && !c2.dynamicChildren) {\n        if (c2.patchFlag <= 0 || c2.patchFlag === 32 /* HYDRATE_EVENTS */) {\n          c2 = ch2[i] = cloneIfMounted(ch2[i]);\n          c2.el = c1.el;\n        }\n        if (!shallow) traverseStaticChildren(c1, c2);\n      }\n      // also inherit for comment nodes, but not placeholders (e.g. v-if which\n      // would have received .el during block patch)\n      if (process.env.NODE_ENV !== 'production' && c2.type === Comment && !c2.el) {\n        c2.el = c1.el;\n      }\n    }\n  }\n}\n// https://en.wikipedia.org/wiki/Longest_increasing_subsequence\nfunction getSequence(arr) {\n  var p = arr.slice();\n  var result = [0];\n  var i, j, u, v, c;\n  var len = arr.length;\n  for (i = 0; i < len; i++) {\n    var arrI = arr[i];\n    if (arrI !== 0) {\n      j = result[result.length - 1];\n      if (arr[j] < arrI) {\n        p[i] = j;\n        result.push(i);\n        continue;\n      }\n      u = 0;\n      v = result.length - 1;\n      while (u < v) {\n        c = u + v >> 1;\n        if (arr[result[c]] < arrI) {\n          u = c + 1;\n        } else {\n          v = c;\n        }\n      }\n      if (arrI < arr[result[u]]) {\n        if (u > 0) {\n          p[i] = result[u - 1];\n        }\n        result[u] = i;\n      }\n    }\n  }\n  u = result.length;\n  v = result[u - 1];\n  while (u-- > 0) {\n    result[u] = v;\n    v = p[v];\n  }\n  return result;\n}\nvar isTeleport = function isTeleport(type) {\n  return type.__isTeleport;\n};\nvar isTeleportDisabled = function isTeleportDisabled(props) {\n  return props && (props.disabled || props.disabled === '');\n};\nvar isTargetSVG = function isTargetSVG(target) {\n  return typeof SVGElement !== 'undefined' && target instanceof SVGElement;\n};\nvar resolveTarget = function resolveTarget(props, select) {\n  var targetSelector = props && props.to;\n  if (isString(targetSelector)) {\n    if (!select) {\n      process.env.NODE_ENV !== 'production' && warn(\"Current renderer does not support string target for Teleports. \" + \"(missing querySelector renderer option)\");\n      return null;\n    } else {\n      var target = select(targetSelector);\n      if (!target) {\n        process.env.NODE_ENV !== 'production' && warn(\"Failed to locate Teleport target with selector \\\"\".concat(targetSelector, \"\\\". \") + \"Note the target element must exist before the component is mounted - \" + \"i.e. the target cannot be rendered by the component itself, and \" + \"ideally should be outside of the entire Vue component tree.\");\n      }\n      return target;\n    }\n  } else {\n    if (process.env.NODE_ENV !== 'production' && !targetSelector && !isTeleportDisabled(props)) {\n      warn(\"Invalid Teleport target: \".concat(targetSelector));\n    }\n    return targetSelector;\n  }\n};\nvar TeleportImpl = {\n  __isTeleport: true,\n  process: function (_process) {\n    function process(_x2, _x3, _x4, _x5, _x6, _x7, _x8, _x9, _x10, _x11) {\n      return _process.apply(this, arguments);\n    }\n    process.toString = function () {\n      return _process.toString();\n    };\n    return process;\n  }(function (n1, n2, container, anchor, parentComponent, parentSuspense, isSVG, slotScopeIds, optimized, internals) {\n    var mountChildren = internals.mc,\n      patchChildren = internals.pc,\n      patchBlockChildren = internals.pbc,\n      _internals$o = internals.o,\n      insert = _internals$o.insert,\n      querySelector = _internals$o.querySelector,\n      createText = _internals$o.createText,\n      createComment = _internals$o.createComment;\n    var disabled = isTeleportDisabled(n2.props);\n    var shapeFlag = n2.shapeFlag,\n      children = n2.children,\n      dynamicChildren = n2.dynamicChildren;\n    // #3302\n    // HMR updated, force full diff\n    if (process.env.NODE_ENV !== 'production' && isHmrUpdating) {\n      optimized = false;\n      dynamicChildren = null;\n    }\n    if (n1 == null) {\n      // insert anchors in the main view\n      var placeholder = n2.el = process.env.NODE_ENV !== 'production' ? createComment('teleport start') : createText('');\n      var mainAnchor = n2.anchor = process.env.NODE_ENV !== 'production' ? createComment('teleport end') : createText('');\n      insert(placeholder, container, anchor);\n      insert(mainAnchor, container, anchor);\n      var target = n2.target = resolveTarget(n2.props, querySelector);\n      var targetAnchor = n2.targetAnchor = createText('');\n      if (target) {\n        insert(targetAnchor, target);\n        // #2652 we could be teleporting from a non-SVG tree into an SVG tree\n        isSVG = isSVG || isTargetSVG(target);\n      } else if (process.env.NODE_ENV !== 'production' && !disabled) {\n        warn('Invalid Teleport target on mount:', target, \"(\".concat(_typeof(target), \")\"));\n      }\n      var mount = function mount(container, anchor) {\n        // Teleport *always* has Array children. This is enforced in both the\n        // compiler and vnode children normalization.\n        if (shapeFlag & 16 /* ARRAY_CHILDREN */) {\n          mountChildren(children, container, anchor, parentComponent, parentSuspense, isSVG, slotScopeIds, optimized);\n        }\n      };\n      if (disabled) {\n        mount(container, mainAnchor);\n      } else if (target) {\n        mount(target, targetAnchor);\n      }\n    } else {\n      // update content\n      n2.el = n1.el;\n      var _mainAnchor = n2.anchor = n1.anchor;\n      var _target = n2.target = n1.target;\n      var _targetAnchor = n2.targetAnchor = n1.targetAnchor;\n      var wasDisabled = isTeleportDisabled(n1.props);\n      var currentContainer = wasDisabled ? container : _target;\n      var currentAnchor = wasDisabled ? _mainAnchor : _targetAnchor;\n      isSVG = isSVG || isTargetSVG(_target);\n      if (dynamicChildren) {\n        // fast path when the teleport happens to be a block root\n        patchBlockChildren(n1.dynamicChildren, dynamicChildren, currentContainer, parentComponent, parentSuspense, isSVG, slotScopeIds);\n        // even in block tree mode we need to make sure all root-level nodes\n        // in the teleport inherit previous DOM references so that they can\n        // be moved in future patches.\n        traverseStaticChildren(n1, n2, true);\n      } else if (!optimized) {\n        patchChildren(n1, n2, currentContainer, currentAnchor, parentComponent, parentSuspense, isSVG, slotScopeIds, false);\n      }\n      if (disabled) {\n        if (!wasDisabled) {\n          // enabled -> disabled\n          // move into main container\n          moveTeleport(n2, container, _mainAnchor, internals, 1 /* TOGGLE */);\n        }\n      } else {\n        // target changed\n        if ((n2.props && n2.props.to) !== (n1.props && n1.props.to)) {\n          var nextTarget = n2.target = resolveTarget(n2.props, querySelector);\n          if (nextTarget) {\n            moveTeleport(n2, nextTarget, null, internals, 0 /* TARGET_CHANGE */);\n          } else if (process.env.NODE_ENV !== 'production') {\n            warn('Invalid Teleport target on update:', _target, \"(\".concat(_typeof(_target), \")\"));\n          }\n        } else if (wasDisabled) {\n          // disabled -> enabled\n          // move into teleport target\n          moveTeleport(n2, _target, _targetAnchor, internals, 1 /* TOGGLE */);\n        }\n      }\n    }\n  }),\n  remove: function remove(vnode, parentComponent, parentSuspense, optimized, _ref13, doRemove) {\n    var unmount = _ref13.um,\n      hostRemove = _ref13.o.remove;\n    var shapeFlag = vnode.shapeFlag,\n      children = vnode.children,\n      anchor = vnode.anchor,\n      targetAnchor = vnode.targetAnchor,\n      target = vnode.target,\n      props = vnode.props;\n    if (target) {\n      hostRemove(targetAnchor);\n    }\n    // an unmounted teleport should always remove its children if not disabled\n    if (doRemove || !isTeleportDisabled(props)) {\n      hostRemove(anchor);\n      if (shapeFlag & 16 /* ARRAY_CHILDREN */) {\n        for (var i = 0; i < children.length; i++) {\n          var child = children[i];\n          unmount(child, parentComponent, parentSuspense, true, !!child.dynamicChildren);\n        }\n      }\n    }\n  },\n  move: moveTeleport,\n  hydrate: hydrateTeleport\n};\nfunction moveTeleport(vnode, container, parentAnchor, _ref14) {\n  var insert = _ref14.o.insert,\n    move = _ref14.m;\n  var moveType = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : 2;\n  // move target anchor if this is a target change.\n  if (moveType === 0 /* TARGET_CHANGE */) {\n    insert(vnode.targetAnchor, container, parentAnchor);\n  }\n  var el = vnode.el,\n    anchor = vnode.anchor,\n    shapeFlag = vnode.shapeFlag,\n    children = vnode.children,\n    props = vnode.props;\n  var isReorder = moveType === 2 /* REORDER */;\n  // move main view anchor if this is a re-order.\n  if (isReorder) {\n    insert(el, container, parentAnchor);\n  }\n  // if this is a re-order and teleport is enabled (content is in target)\n  // do not move children. So the opposite is: only move children if this\n  // is not a reorder, or the teleport is disabled\n  if (!isReorder || isTeleportDisabled(props)) {\n    // Teleport has either Array children or no children.\n    if (shapeFlag & 16 /* ARRAY_CHILDREN */) {\n      for (var i = 0; i < children.length; i++) {\n        move(children[i], container, parentAnchor, 2 /* REORDER */);\n      }\n    }\n  }\n  // move main view anchor if this is a re-order.\n  if (isReorder) {\n    insert(anchor, container, parentAnchor);\n  }\n}\nfunction hydrateTeleport(node, vnode, parentComponent, parentSuspense, slotScopeIds, optimized, _ref15, hydrateChildren) {\n  var _ref15$o = _ref15.o,\n    nextSibling = _ref15$o.nextSibling,\n    parentNode = _ref15$o.parentNode,\n    querySelector = _ref15$o.querySelector;\n  var target = vnode.target = resolveTarget(vnode.props, querySelector);\n  if (target) {\n    // if multiple teleports rendered to the same target element, we need to\n    // pick up from where the last teleport finished instead of the first node\n    var targetNode = target._lpa || target.firstChild;\n    if (vnode.shapeFlag & 16 /* ARRAY_CHILDREN */) {\n      if (isTeleportDisabled(vnode.props)) {\n        vnode.anchor = hydrateChildren(nextSibling(node), vnode, parentNode(node), parentComponent, parentSuspense, slotScopeIds, optimized);\n        vnode.targetAnchor = targetNode;\n      } else {\n        vnode.anchor = nextSibling(node);\n        vnode.targetAnchor = hydrateChildren(targetNode, vnode, target, parentComponent, parentSuspense, slotScopeIds, optimized);\n      }\n      target._lpa = vnode.targetAnchor && nextSibling(vnode.targetAnchor);\n    }\n  }\n  return vnode.anchor && nextSibling(vnode.anchor);\n}\n// Force-casted public typing for h and TSX props inference\nvar Teleport = TeleportImpl;\nvar COMPONENTS = 'components';\nvar DIRECTIVES = 'directives';\n/**\r\n * @private\r\n */\nfunction resolveComponent(name, maybeSelfReference) {\n  return resolveAsset(COMPONENTS, name, true, maybeSelfReference) || name;\n}\nvar NULL_DYNAMIC_COMPONENT = Symbol();\n/**\r\n * @private\r\n */\nfunction resolveDynamicComponent(component) {\n  if (isString(component)) {\n    return resolveAsset(COMPONENTS, component, false) || component;\n  } else {\n    // invalid types will fallthrough to createVNode and raise warning\n    return component || NULL_DYNAMIC_COMPONENT;\n  }\n}\n/**\r\n * @private\r\n */\nfunction resolveDirective(name) {\n  return resolveAsset(DIRECTIVES, name);\n}\n// implementation\nfunction resolveAsset(type, name) {\n  var warnMissing = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : true;\n  var maybeSelfReference = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : false;\n  var instance = currentRenderingInstance || currentInstance;\n  if (instance) {\n    var Component = instance.type;\n    // explicit self name has highest priority\n    if (type === COMPONENTS) {\n      var selfName = getComponentName(Component);\n      if (selfName && (selfName === name || selfName === camelize(name) || selfName === capitalize(camelize(name)))) {\n        return Component;\n      }\n    }\n    var res =\n    // local registration\n    // check instance[type] first which is resolved for options API\n    resolve(instance[type] || Component[type], name) ||\n    // global registration\n    resolve(instance.appContext[type], name);\n    if (!res && maybeSelfReference) {\n      // fallback to implicit self-reference\n      return Component;\n    }\n    if (process.env.NODE_ENV !== 'production' && warnMissing && !res) {\n      var extra = type === COMPONENTS ? \"\\nIf this is a native custom element, make sure to exclude it from \" + \"component resolution via compilerOptions.isCustomElement.\" : \"\";\n      warn(\"Failed to resolve \".concat(type.slice(0, -1), \": \").concat(name).concat(extra));\n    }\n    return res;\n  } else if (process.env.NODE_ENV !== 'production') {\n    warn(\"resolve\".concat(capitalize(type.slice(0, -1)), \" \") + \"can only be used in render() or setup().\");\n  }\n}\nfunction resolve(registry, name) {\n  return registry && (registry[name] || registry[camelize(name)] || registry[capitalize(camelize(name))]);\n}\nvar Fragment = Symbol(process.env.NODE_ENV !== 'production' ? 'Fragment' : undefined);\nvar Text = Symbol(process.env.NODE_ENV !== 'production' ? 'Text' : undefined);\nvar Comment = Symbol(process.env.NODE_ENV !== 'production' ? 'Comment' : undefined);\nvar Static = Symbol(process.env.NODE_ENV !== 'production' ? 'Static' : undefined);\n// Since v-if and v-for are the two possible ways node structure can dynamically\n// change, once we consider v-if branches and each v-for fragment a block, we\n// can divide a template into nested blocks, and within each block the node\n// structure would be stable. This allows us to skip most children diffing\n// and only worry about the dynamic nodes (indicated by patch flags).\nvar blockStack = [];\nvar currentBlock = null;\n/**\r\n * Open a block.\r\n * This must be called before `createBlock`. It cannot be part of `createBlock`\r\n * because the children of the block are evaluated before `createBlock` itself\r\n * is called. The generated code typically looks like this:\r\n *\r\n * ```js\r\n * function render() {\r\n *   return (openBlock(),createBlock('div', null, [...]))\r\n * }\r\n * ```\r\n * disableTracking is true when creating a v-for fragment block, since a v-for\r\n * fragment always diffs its children.\r\n *\r\n * @private\r\n */\nfunction openBlock() {\n  var disableTracking = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;\n  blockStack.push(currentBlock = disableTracking ? null : []);\n}\nfunction closeBlock() {\n  blockStack.pop();\n  currentBlock = blockStack[blockStack.length - 1] || null;\n}\n// Whether we should be tracking dynamic child nodes inside a block.\n// Only tracks when this value is > 0\n// We are not using a simple boolean because this value may need to be\n// incremented/decremented by nested usage of v-once (see below)\nvar isBlockTreeEnabled = 1;\n/**\r\n * Block tracking sometimes needs to be disabled, for example during the\r\n * creation of a tree that needs to be cached by v-once. The compiler generates\r\n * code like this:\r\n *\r\n * ``` js\r\n * _cache[1] || (\r\n *   setBlockTracking(-1),\r\n *   _cache[1] = createVNode(...),\r\n *   setBlockTracking(1),\r\n *   _cache[1]\r\n * )\r\n * ```\r\n *\r\n * @private\r\n */\nfunction setBlockTracking(value) {\n  isBlockTreeEnabled += value;\n}\nfunction setupBlock(vnode) {\n  // save current block children on the block vnode\n  vnode.dynamicChildren = isBlockTreeEnabled > 0 ? currentBlock || EMPTY_ARR : null;\n  // close block\n  closeBlock();\n  // a block is always going to be patched, so track it as a child of its\n  // parent block\n  if (isBlockTreeEnabled > 0 && currentBlock) {\n    currentBlock.push(vnode);\n  }\n  return vnode;\n}\n/**\r\n * @private\r\n */\nfunction createElementBlock(type, props, children, patchFlag, dynamicProps, shapeFlag) {\n  return setupBlock(createBaseVNode(type, props, children, patchFlag, dynamicProps, shapeFlag, true /* isBlock */));\n}\n/**\r\n * Create a block root vnode. Takes the same exact arguments as `createVNode`.\r\n * A block root keeps track of dynamic nodes within the block in the\r\n * `dynamicChildren` array.\r\n *\r\n * @private\r\n */\nfunction createBlock(type, props, children, patchFlag, dynamicProps) {\n  return setupBlock(createVNode(type, props, children, patchFlag, dynamicProps, true /* isBlock: prevent a block from tracking itself */));\n}\n\nfunction isVNode(value) {\n  return value ? value.__v_isVNode === true : false;\n}\nfunction isSameVNodeType(n1, n2) {\n  if (process.env.NODE_ENV !== 'production' && n2.shapeFlag & 6 /* COMPONENT */ && hmrDirtyComponents.has(n2.type)) {\n    // HMR only: if the component has been hot-updated, force a reload.\n    return false;\n  }\n  return n1.type === n2.type && n1.key === n2.key;\n}\nvar vnodeArgsTransformer;\n/**\r\n * Internal API for registering an arguments transform for createVNode\r\n * used for creating stubs in the test-utils\r\n * It is *internal* but needs to be exposed for test-utils to pick up proper\r\n * typings\r\n */\nfunction transformVNodeArgs(transformer) {\n  vnodeArgsTransformer = transformer;\n}\nvar createVNodeWithArgsTransform = function createVNodeWithArgsTransform() {\n  for (var _len6 = arguments.length, args = new Array(_len6), _key14 = 0; _key14 < _len6; _key14++) {\n    args[_key14] = arguments[_key14];\n  }\n  return _createVNode.apply(void 0, _toConsumableArray(vnodeArgsTransformer ? vnodeArgsTransformer(args, currentRenderingInstance) : args));\n};\nvar InternalObjectKey = \"__vInternal\";\nvar normalizeKey = function normalizeKey(_ref16) {\n  var key = _ref16.key;\n  return key != null ? key : null;\n};\nvar normalizeRef = function normalizeRef(_ref17) {\n  var ref = _ref17.ref,\n    ref_key = _ref17.ref_key,\n    ref_for = _ref17.ref_for;\n  return ref != null ? isString(ref) || isRef(ref) || isFunction(ref) ? {\n    i: currentRenderingInstance,\n    r: ref,\n    k: ref_key,\n    f: !!ref_for\n  } : ref : null;\n};\nfunction createBaseVNode(type) {\n  var props = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;\n  var children = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : null;\n  var patchFlag = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 0;\n  var dynamicProps = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : null;\n  var shapeFlag = arguments.length > 5 && arguments[5] !== undefined ? arguments[5] : type === Fragment ? 0 : 1;\n  var isBlockNode = arguments.length > 6 && arguments[6] !== undefined ? arguments[6] : false;\n  var needFullChildrenNormalization = arguments.length > 7 && arguments[7] !== undefined ? arguments[7] : false;\n  var vnode = {\n    __v_isVNode: true,\n    __v_skip: true,\n    type: type,\n    props: props,\n    key: props && normalizeKey(props),\n    ref: props && normalizeRef(props),\n    scopeId: currentScopeId,\n    slotScopeIds: null,\n    children: children,\n    component: null,\n    suspense: null,\n    ssContent: null,\n    ssFallback: null,\n    dirs: null,\n    transition: null,\n    el: null,\n    anchor: null,\n    target: null,\n    targetAnchor: null,\n    staticCount: 0,\n    shapeFlag: shapeFlag,\n    patchFlag: patchFlag,\n    dynamicProps: dynamicProps,\n    dynamicChildren: null,\n    appContext: null\n  };\n  if (needFullChildrenNormalization) {\n    normalizeChildren(vnode, children);\n    // normalize suspense children\n    if (shapeFlag & 128 /* SUSPENSE */) {\n      type.normalize(vnode);\n    }\n  } else if (children) {\n    // compiled element vnode - if children is passed, only possible types are\n    // string or Array.\n    vnode.shapeFlag |= isString(children) ? 8 /* TEXT_CHILDREN */ : 16 /* ARRAY_CHILDREN */;\n  }\n  // validate key\n  if (process.env.NODE_ENV !== 'production' && vnode.key !== vnode.key) {\n    warn(\"VNode created with invalid key (NaN). VNode type:\", vnode.type);\n  }\n  // track vnode for block tree\n  if (isBlockTreeEnabled > 0 &&\n  // avoid a block node from tracking itself\n  !isBlockNode &&\n  // has current parent block\n  currentBlock && (\n  // presence of a patch flag indicates this node needs patching on updates.\n  // component nodes also should always be patched, because even if the\n  // component doesn't need to update, it needs to persist the instance on to\n  // the next vnode so that it can be properly unmounted later.\n  vnode.patchFlag > 0 || shapeFlag & 6 /* COMPONENT */) &&\n  // the EVENTS flag is only for hydration and if it is the only flag, the\n  // vnode should not be considered dynamic due to handler caching.\n  vnode.patchFlag !== 32 /* HYDRATE_EVENTS */) {\n    currentBlock.push(vnode);\n  }\n  return vnode;\n}\nvar createVNode = process.env.NODE_ENV !== 'production' ? createVNodeWithArgsTransform : _createVNode;\nfunction _createVNode(type) {\n  var props = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;\n  var children = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : null;\n  var patchFlag = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 0;\n  var dynamicProps = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : null;\n  var isBlockNode = arguments.length > 5 && arguments[5] !== undefined ? arguments[5] : false;\n  if (!type || type === NULL_DYNAMIC_COMPONENT) {\n    if (process.env.NODE_ENV !== 'production' && !type) {\n      warn(\"Invalid vnode type when creating vnode: \".concat(type, \".\"));\n    }\n    type = Comment;\n  }\n  if (isVNode(type)) {\n    // createVNode receiving an existing vnode. This happens in cases like\n    // <component :is=\"vnode\"/>\n    // #2078 make sure to merge refs during the clone instead of overwriting it\n    var cloned = cloneVNode(type, props, true /* mergeRef: true */);\n    if (children) {\n      normalizeChildren(cloned, children);\n    }\n    return cloned;\n  }\n  // class component normalization.\n  if (isClassComponent(type)) {\n    type = type.__vccOpts;\n  }\n  // class & style normalization.\n  if (props) {\n    // for reactive or proxy objects, we need to clone it to enable mutation.\n    props = guardReactiveProps(props);\n    var _props = props,\n      klass = _props.class,\n      style = _props.style;\n    if (klass && !isString(klass)) {\n      props.class = normalizeClass(klass);\n    }\n    if (isObject(style)) {\n      // reactive state objects need to be cloned since they are likely to be\n      // mutated\n      if (isProxy(style) && !isArray(style)) {\n        style = extend({}, style);\n      }\n      props.style = normalizeStyle(style);\n    }\n  }\n  // encode the vnode type information into a bitmap\n  var shapeFlag = isString(type) ? 1 /* ELEMENT */ : isSuspense(type) ? 128 /* SUSPENSE */ : isTeleport(type) ? 64 /* TELEPORT */ : isObject(type) ? 4 /* STATEFUL_COMPONENT */ : isFunction(type) ? 2 /* FUNCTIONAL_COMPONENT */ : 0;\n  if (process.env.NODE_ENV !== 'production' && shapeFlag & 4 /* STATEFUL_COMPONENT */ && isProxy(type)) {\n    type = toRaw(type);\n    warn(\"Vue received a Component which was made a reactive object. This can \" + \"lead to unnecessary performance overhead, and should be avoided by \" + \"marking the component with `markRaw` or using `shallowRef` \" + \"instead of `ref`.\", \"\\nComponent that was made reactive: \", type);\n  }\n  return createBaseVNode(type, props, children, patchFlag, dynamicProps, shapeFlag, isBlockNode, true);\n}\nfunction guardReactiveProps(props) {\n  if (!props) return null;\n  return isProxy(props) || InternalObjectKey in props ? extend({}, props) : props;\n}\nfunction cloneVNode(vnode, extraProps) {\n  var mergeRef = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n  // This is intentionally NOT using spread or extend to avoid the runtime\n  // key enumeration cost.\n  var props = vnode.props,\n    ref = vnode.ref,\n    patchFlag = vnode.patchFlag,\n    children = vnode.children;\n  var mergedProps = extraProps ? mergeProps(props || {}, extraProps) : props;\n  var cloned = {\n    __v_isVNode: true,\n    __v_skip: true,\n    type: vnode.type,\n    props: mergedProps,\n    key: mergedProps && normalizeKey(mergedProps),\n    ref: extraProps && extraProps.ref ?\n    // #2078 in the case of <component :is=\"vnode\" ref=\"extra\"/>\n    // if the vnode itself already has a ref, cloneVNode will need to merge\n    // the refs so the single vnode can be set on multiple refs\n    mergeRef && ref ? isArray(ref) ? ref.concat(normalizeRef(extraProps)) : [ref, normalizeRef(extraProps)] : normalizeRef(extraProps) : ref,\n    scopeId: vnode.scopeId,\n    slotScopeIds: vnode.slotScopeIds,\n    children: process.env.NODE_ENV !== 'production' && patchFlag === -1 /* HOISTED */ && isArray(children) ? children.map(deepCloneVNode) : children,\n    target: vnode.target,\n    targetAnchor: vnode.targetAnchor,\n    staticCount: vnode.staticCount,\n    shapeFlag: vnode.shapeFlag,\n    // if the vnode is cloned with extra props, we can no longer assume its\n    // existing patch flag to be reliable and need to add the FULL_PROPS flag.\n    // note: perserve flag for fragments since they use the flag for children\n    // fast paths only.\n    patchFlag: extraProps && vnode.type !== Fragment ? patchFlag === -1 // hoisted node\n    ? 16 /* FULL_PROPS */ : patchFlag | 16 /* FULL_PROPS */ : patchFlag,\n    dynamicProps: vnode.dynamicProps,\n    dynamicChildren: vnode.dynamicChildren,\n    appContext: vnode.appContext,\n    dirs: vnode.dirs,\n    transition: vnode.transition,\n    // These should technically only be non-null on mounted VNodes. However,\n    // they *should* be copied for kept-alive vnodes. So we just always copy\n    // them since them being non-null during a mount doesn't affect the logic as\n    // they will simply be overwritten.\n    component: vnode.component,\n    suspense: vnode.suspense,\n    ssContent: vnode.ssContent && cloneVNode(vnode.ssContent),\n    ssFallback: vnode.ssFallback && cloneVNode(vnode.ssFallback),\n    el: vnode.el,\n    anchor: vnode.anchor\n  };\n  return cloned;\n}\n/**\r\n * Dev only, for HMR of hoisted vnodes reused in v-for\r\n * https://github.com/vitejs/vite/issues/2022\r\n */\nfunction deepCloneVNode(vnode) {\n  var cloned = cloneVNode(vnode);\n  if (isArray(vnode.children)) {\n    cloned.children = vnode.children.map(deepCloneVNode);\n  }\n  return cloned;\n}\n/**\r\n * @private\r\n */\nfunction createTextVNode() {\n  var text = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : ' ';\n  var flag = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n  return createVNode(Text, null, text, flag);\n}\n/**\r\n * @private\r\n */\nfunction createStaticVNode(content, numberOfNodes) {\n  // A static vnode can contain multiple stringified elements, and the number\n  // of elements is necessary for hydration.\n  var vnode = createVNode(Static, null, content);\n  vnode.staticCount = numberOfNodes;\n  return vnode;\n}\n/**\r\n * @private\r\n */\nfunction createCommentVNode() {\n  var text = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : '';\n  var asBlock = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n  return asBlock ? (openBlock(), createBlock(Comment, null, text)) : createVNode(Comment, null, text);\n}\nfunction normalizeVNode(child) {\n  if (child == null || typeof child === 'boolean') {\n    // empty placeholder\n    return createVNode(Comment);\n  } else if (isArray(child)) {\n    // fragment\n    return createVNode(Fragment, null,\n    // #3666, avoid reference pollution when reusing vnode\n    child.slice());\n  } else if (_typeof(child) === 'object') {\n    // already vnode, this should be the most common since compiled templates\n    // always produce all-vnode children arrays\n    return cloneIfMounted(child);\n  } else {\n    // strings and numbers\n    return createVNode(Text, null, String(child));\n  }\n}\n// optimized normalization for template-compiled render fns\nfunction cloneIfMounted(child) {\n  return child.el === null || child.memo ? child : cloneVNode(child);\n}\nfunction normalizeChildren(vnode, children) {\n  var type = 0;\n  var shapeFlag = vnode.shapeFlag;\n  if (children == null) {\n    children = null;\n  } else if (isArray(children)) {\n    type = 16 /* ARRAY_CHILDREN */;\n  } else if (_typeof(children) === 'object') {\n    if (shapeFlag & (1 /* ELEMENT */ | 64 /* TELEPORT */)) {\n      // Normalize slot to plain children for plain element and Teleport\n      var slot = children.default;\n      if (slot) {\n        // _c marker is added by withCtx() indicating this is a compiled slot\n        slot._c && (slot._d = false);\n        normalizeChildren(vnode, slot());\n        slot._c && (slot._d = true);\n      }\n      return;\n    } else {\n      type = 32 /* SLOTS_CHILDREN */;\n      var slotFlag = children._;\n      if (!slotFlag && !(InternalObjectKey in children)) {\n        children._ctx = currentRenderingInstance;\n      } else if (slotFlag === 3 /* FORWARDED */ && currentRenderingInstance) {\n        // a child component receives forwarded slots from the parent.\n        // its slot type is determined by its parent's slot type.\n        if (currentRenderingInstance.slots._ === 1 /* STABLE */) {\n          children._ = 1 /* STABLE */;\n        } else {\n          children._ = 2 /* DYNAMIC */;\n          vnode.patchFlag |= 1024 /* DYNAMIC_SLOTS */;\n        }\n      }\n    }\n  } else if (isFunction(children)) {\n    children = {\n      default: children,\n      _ctx: currentRenderingInstance\n    };\n    type = 32 /* SLOTS_CHILDREN */;\n  } else {\n    children = String(children);\n    // force teleport children to array so it can be moved around\n    if (shapeFlag & 64 /* TELEPORT */) {\n      type = 16 /* ARRAY_CHILDREN */;\n      children = [createTextVNode(children)];\n    } else {\n      type = 8 /* TEXT_CHILDREN */;\n    }\n  }\n\n  vnode.children = children;\n  vnode.shapeFlag |= type;\n}\nfunction mergeProps() {\n  var ret = {};\n  for (var i = 0; i < arguments.length; i++) {\n    var toMerge = i < 0 || arguments.length <= i ? undefined : arguments[i];\n    for (var key in toMerge) {\n      if (key === 'class') {\n        if (ret.class !== toMerge.class) {\n          ret.class = normalizeClass([ret.class, toMerge.class]);\n        }\n      } else if (key === 'style') {\n        ret.style = normalizeStyle([ret.style, toMerge.style]);\n      } else if (isOn(key)) {\n        var existing = ret[key];\n        var incoming = toMerge[key];\n        if (existing !== incoming && !(isArray(existing) && existing.includes(incoming))) {\n          ret[key] = existing ? [].concat(existing, incoming) : incoming;\n        }\n      } else if (key !== '') {\n        ret[key] = toMerge[key];\n      }\n    }\n  }\n  return ret;\n}\nfunction invokeVNodeHook(hook, instance, vnode) {\n  var prevVNode = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : null;\n  callWithAsyncErrorHandling(hook, instance, 7 /* VNODE_HOOK */, [vnode, prevVNode]);\n}\n\n/**\r\n * Actual implementation\r\n */\nfunction renderList(source, renderItem, cache, index) {\n  var ret;\n  var cached = cache && cache[index];\n  if (isArray(source) || isString(source)) {\n    ret = new Array(source.length);\n    for (var i = 0, l = source.length; i < l; i++) {\n      ret[i] = renderItem(source[i], i, undefined, cached && cached[i]);\n    }\n  } else if (typeof source === 'number') {\n    if (process.env.NODE_ENV !== 'production' && !Number.isInteger(source)) {\n      warn(\"The v-for range expect an integer value but got \".concat(source, \".\"));\n      return [];\n    }\n    ret = new Array(source);\n    for (var _i3 = 0; _i3 < source; _i3++) {\n      ret[_i3] = renderItem(_i3 + 1, _i3, undefined, cached && cached[_i3]);\n    }\n  } else if (isObject(source)) {\n    if (source[Symbol.iterator]) {\n      ret = Array.from(source, function (item, i) {\n        return renderItem(item, i, undefined, cached && cached[i]);\n      });\n    } else {\n      var keys = Object.keys(source);\n      ret = new Array(keys.length);\n      for (var _i4 = 0, _l = keys.length; _i4 < _l; _i4++) {\n        var key = keys[_i4];\n        ret[_i4] = renderItem(source[key], key, _i4, cached && cached[_i4]);\n      }\n    }\n  } else {\n    ret = [];\n  }\n  if (cache) {\n    cache[index] = ret;\n  }\n  return ret;\n}\n\n/**\r\n * Compiler runtime helper for creating dynamic slots object\r\n * @private\r\n */\nfunction createSlots(slots, dynamicSlots) {\n  for (var i = 0; i < dynamicSlots.length; i++) {\n    var slot = dynamicSlots[i];\n    // array of dynamic slot generated by <template v-for=\"...\" #[...]>\n    if (isArray(slot)) {\n      for (var j = 0; j < slot.length; j++) {\n        slots[slot[j].name] = slot[j].fn;\n      }\n    } else if (slot) {\n      // conditional single slot generated by <template v-if=\"...\" #foo>\n      slots[slot.name] = slot.fn;\n    }\n  }\n  return slots;\n}\n\n/**\r\n * Compiler runtime helper for rendering `<slot/>`\r\n * @private\r\n */\nfunction renderSlot(slots, name) {\n  var props = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n  var\n  // this is not a user-facing function, so the fallback is always generated by\n  // the compiler and guaranteed to be a function returning an array\n  fallback = arguments.length > 3 ? arguments[3] : undefined;\n  var noSlotted = arguments.length > 4 ? arguments[4] : undefined;\n  if (currentRenderingInstance.isCE) {\n    return createVNode('slot', name === 'default' ? null : {\n      name: name\n    }, fallback && fallback());\n  }\n  var slot = slots[name];\n  if (process.env.NODE_ENV !== 'production' && slot && slot.length > 1) {\n    warn(\"SSR-optimized slot function detected in a non-SSR-optimized render \" + \"function. You need to mark this component with $dynamic-slots in the \" + \"parent template.\");\n    slot = function slot() {\n      return [];\n    };\n  }\n  // a compiled slot disables block tracking by default to avoid manual\n  // invocation interfering with template-based block tracking, but in\n  // `renderSlot` we can be sure that it's template-based so we can force\n  // enable it.\n  if (slot && slot._c) {\n    slot._d = false;\n  }\n  openBlock();\n  var validSlotContent = slot && ensureValidVNode(slot(props));\n  var rendered = createBlock(Fragment, {\n    key: props.key || \"_\".concat(name)\n  }, validSlotContent || (fallback ? fallback() : []), validSlotContent && slots._ === 1 /* STABLE */ ? 64 /* STABLE_FRAGMENT */ : -2 /* BAIL */);\n  if (!noSlotted && rendered.scopeId) {\n    rendered.slotScopeIds = [rendered.scopeId + '-s'];\n  }\n  if (slot && slot._c) {\n    slot._d = true;\n  }\n  return rendered;\n}\nfunction ensureValidVNode(vnodes) {\n  return vnodes.some(function (child) {\n    if (!isVNode(child)) return true;\n    if (child.type === Comment) return false;\n    if (child.type === Fragment && !ensureValidVNode(child.children)) return false;\n    return true;\n  }) ? vnodes : null;\n}\n\n/**\r\n * For prefixing keys in v-on=\"obj\" with \"on\"\r\n * @private\r\n */\nfunction toHandlers(obj) {\n  var ret = {};\n  if (process.env.NODE_ENV !== 'production' && !isObject(obj)) {\n    warn(\"v-on with no argument expects an object value.\");\n    return ret;\n  }\n  for (var key in obj) {\n    ret[toHandlerKey(key)] = obj[key];\n  }\n  return ret;\n}\n\n/**\r\n * #2437 In Vue 3, functional components do not have a public instance proxy but\r\n * they exist in the internal parent chain. For code that relies on traversing\r\n * public $parent chains, skip functional ones and go to the parent instead.\r\n */\nvar getPublicInstance = function getPublicInstance(i) {\n  if (!i) return null;\n  if (isStatefulComponent(i)) return getExposeProxy(i) || i.proxy;\n  return getPublicInstance(i.parent);\n};\nvar publicPropertiesMap = extend(Object.create(null), {\n  $: function $(i) {\n    return i;\n  },\n  $el: function $el(i) {\n    return i.vnode.el;\n  },\n  $data: function $data(i) {\n    return i.data;\n  },\n  $props: function $props(i) {\n    return process.env.NODE_ENV !== 'production' ? shallowReadonly(i.props) : i.props;\n  },\n  $attrs: function $attrs(i) {\n    return process.env.NODE_ENV !== 'production' ? shallowReadonly(i.attrs) : i.attrs;\n  },\n  $slots: function $slots(i) {\n    return process.env.NODE_ENV !== 'production' ? shallowReadonly(i.slots) : i.slots;\n  },\n  $refs: function $refs(i) {\n    return process.env.NODE_ENV !== 'production' ? shallowReadonly(i.refs) : i.refs;\n  },\n  $parent: function $parent(i) {\n    return getPublicInstance(i.parent);\n  },\n  $root: function $root(i) {\n    return getPublicInstance(i.root);\n  },\n  $emit: function $emit(i) {\n    return i.emit;\n  },\n  $options: function $options(i) {\n    return __VUE_OPTIONS_API__ ? resolveMergedOptions(i) : i.type;\n  },\n  $forceUpdate: function $forceUpdate(i) {\n    return function () {\n      return queueJob(i.update);\n    };\n  },\n  $nextTick: function $nextTick(i) {\n    return nextTick.bind(i.proxy);\n  },\n  $watch: function $watch(i) {\n    return __VUE_OPTIONS_API__ ? instanceWatch.bind(i) : NOOP;\n  }\n});\nvar PublicInstanceProxyHandlers = {\n  get: function get(_ref18, key) {\n    var instance = _ref18._;\n    var ctx = instance.ctx,\n      setupState = instance.setupState,\n      data = instance.data,\n      props = instance.props,\n      accessCache = instance.accessCache,\n      type = instance.type,\n      appContext = instance.appContext;\n    // for internal formatters to know that this is a Vue instance\n    if (process.env.NODE_ENV !== 'production' && key === '__isVue') {\n      return true;\n    }\n    // prioritize <script setup> bindings during dev.\n    // this allows even properties that start with _ or $ to be used - so that\n    // it aligns with the production behavior where the render fn is inlined and\n    // indeed has access to all declared variables.\n    if (process.env.NODE_ENV !== 'production' && setupState !== EMPTY_OBJ && setupState.__isScriptSetup && hasOwn(setupState, key)) {\n      return setupState[key];\n    }\n    // data / props / ctx\n    // This getter gets called for every property access on the render context\n    // during render and is a major hotspot. The most expensive part of this\n    // is the multiple hasOwn() calls. It's much faster to do a simple property\n    // access on a plain object, so we use an accessCache object (with null\n    // prototype) to memoize what access type a key corresponds to.\n    var normalizedProps;\n    if (key[0] !== '$') {\n      var n = accessCache[key];\n      if (n !== undefined) {\n        switch (n) {\n          case 1 /* SETUP */:\n            return setupState[key];\n          case 2 /* DATA */:\n            return data[key];\n          case 4 /* CONTEXT */:\n            return ctx[key];\n          case 3 /* PROPS */:\n            return props[key];\n          // default: just fallthrough\n        }\n      } else if (setupState !== EMPTY_OBJ && hasOwn(setupState, key)) {\n        accessCache[key] = 1 /* SETUP */;\n        return setupState[key];\n      } else if (data !== EMPTY_OBJ && hasOwn(data, key)) {\n        accessCache[key] = 2 /* DATA */;\n        return data[key];\n      } else if (\n      // only cache other properties when instance has declared (thus stable)\n      // props\n      (normalizedProps = instance.propsOptions[0]) && hasOwn(normalizedProps, key)) {\n        accessCache[key] = 3 /* PROPS */;\n        return props[key];\n      } else if (ctx !== EMPTY_OBJ && hasOwn(ctx, key)) {\n        accessCache[key] = 4 /* CONTEXT */;\n        return ctx[key];\n      } else if (!__VUE_OPTIONS_API__ || shouldCacheAccess) {\n        accessCache[key] = 0 /* OTHER */;\n      }\n    }\n\n    var publicGetter = publicPropertiesMap[key];\n    var cssModule, globalProperties;\n    // public $xxx properties\n    if (publicGetter) {\n      if (key === '$attrs') {\n        track(instance, \"get\" /* GET */, key);\n        process.env.NODE_ENV !== 'production' && markAttrsAccessed();\n      }\n      return publicGetter(instance);\n    } else if (\n    // css module (injected by vue-loader)\n    (cssModule = type.__cssModules) && (cssModule = cssModule[key])) {\n      return cssModule;\n    } else if (ctx !== EMPTY_OBJ && hasOwn(ctx, key)) {\n      // user may set custom properties to `this` that start with `$`\n      accessCache[key] = 4 /* CONTEXT */;\n      return ctx[key];\n    } else if (\n    // global properties\n    globalProperties = appContext.config.globalProperties, hasOwn(globalProperties, key)) {\n      {\n        return globalProperties[key];\n      }\n    } else if (process.env.NODE_ENV !== 'production' && currentRenderingInstance && (!isString(key) ||\n    // #1091 avoid internal isRef/isVNode checks on component instance leading\n    // to infinite warning loop\n    key.indexOf('__v') !== 0)) {\n      if (data !== EMPTY_OBJ && (key[0] === '$' || key[0] === '_') && hasOwn(data, key)) {\n        warn(\"Property \".concat(JSON.stringify(key), \" must be accessed via $data because it starts with a reserved \") + \"character (\\\"$\\\" or \\\"_\\\") and is not proxied on the render context.\");\n      } else if (instance === currentRenderingInstance) {\n        warn(\"Property \".concat(JSON.stringify(key), \" was accessed during render \") + \"but is not defined on instance.\");\n      }\n    }\n  },\n  set: function set(_ref19, key, value) {\n    var instance = _ref19._;\n    var data = instance.data,\n      setupState = instance.setupState,\n      ctx = instance.ctx;\n    if (setupState !== EMPTY_OBJ && hasOwn(setupState, key)) {\n      setupState[key] = value;\n    } else if (data !== EMPTY_OBJ && hasOwn(data, key)) {\n      data[key] = value;\n    } else if (hasOwn(instance.props, key)) {\n      process.env.NODE_ENV !== 'production' && warn(\"Attempting to mutate prop \\\"\".concat(key, \"\\\". Props are readonly.\"), instance);\n      return false;\n    }\n    if (key[0] === '$' && key.slice(1) in instance) {\n      process.env.NODE_ENV !== 'production' && warn(\"Attempting to mutate public property \\\"\".concat(key, \"\\\". \") + \"Properties starting with $ are reserved and readonly.\", instance);\n      return false;\n    } else {\n      if (process.env.NODE_ENV !== 'production' && key in instance.appContext.config.globalProperties) {\n        Object.defineProperty(ctx, key, {\n          enumerable: true,\n          configurable: true,\n          value: value\n        });\n      } else {\n        ctx[key] = value;\n      }\n    }\n    return true;\n  },\n  has: function has(_ref20, key) {\n    var _ref20$_ = _ref20._,\n      data = _ref20$_.data,\n      setupState = _ref20$_.setupState,\n      accessCache = _ref20$_.accessCache,\n      ctx = _ref20$_.ctx,\n      appContext = _ref20$_.appContext,\n      propsOptions = _ref20$_.propsOptions;\n    var normalizedProps;\n    return !!accessCache[key] || data !== EMPTY_OBJ && hasOwn(data, key) || setupState !== EMPTY_OBJ && hasOwn(setupState, key) || (normalizedProps = propsOptions[0]) && hasOwn(normalizedProps, key) || hasOwn(ctx, key) || hasOwn(publicPropertiesMap, key) || hasOwn(appContext.config.globalProperties, key);\n  }\n};\nif (process.env.NODE_ENV !== 'production' && !false) {\n  PublicInstanceProxyHandlers.ownKeys = function (target) {\n    warn(\"Avoid app logic that relies on enumerating keys on a component instance. \" + \"The keys will be empty in production mode to avoid performance overhead.\");\n    return Reflect.ownKeys(target);\n  };\n}\nvar RuntimeCompiledPublicInstanceProxyHandlers = /*#__PURE__*/extend({}, PublicInstanceProxyHandlers, {\n  get: function get(target, key) {\n    // fast path for unscopables when using `with` block\n    if (key === Symbol.unscopables) {\n      return;\n    }\n    return PublicInstanceProxyHandlers.get(target, key, target);\n  },\n  has: function has(_, key) {\n    var has = key[0] !== '_' && !isGloballyWhitelisted(key);\n    if (process.env.NODE_ENV !== 'production' && !has && PublicInstanceProxyHandlers.has(_, key)) {\n      warn(\"Property \".concat(JSON.stringify(key), \" should not start with _ which is a reserved prefix for Vue internals.\"));\n    }\n    return has;\n  }\n});\n// dev only\n// In dev mode, the proxy target exposes the same properties as seen on `this`\n// for easier console inspection. In prod mode it will be an empty object so\n// these properties definitions can be skipped.\nfunction createDevRenderContext(instance) {\n  var target = {};\n  // expose internal instance for proxy handlers\n  Object.defineProperty(target, \"_\", {\n    configurable: true,\n    enumerable: false,\n    get: function get() {\n      return instance;\n    }\n  });\n  // expose public properties\n  Object.keys(publicPropertiesMap).forEach(function (key) {\n    Object.defineProperty(target, key, {\n      configurable: true,\n      enumerable: false,\n      get: function get() {\n        return publicPropertiesMap[key](instance);\n      },\n      // intercepted by the proxy so no need for implementation,\n      // but needed to prevent set errors\n      set: NOOP\n    });\n  });\n  return target;\n}\n// dev only\nfunction exposePropsOnRenderContext(instance) {\n  var ctx = instance.ctx,\n    _instance$propsOption6 = _slicedToArray(instance.propsOptions, 1),\n    propsOptions = _instance$propsOption6[0];\n  if (propsOptions) {\n    Object.keys(propsOptions).forEach(function (key) {\n      Object.defineProperty(ctx, key, {\n        enumerable: true,\n        configurable: true,\n        get: function get() {\n          return instance.props[key];\n        },\n        set: NOOP\n      });\n    });\n  }\n}\n// dev only\nfunction exposeSetupStateOnRenderContext(instance) {\n  var ctx = instance.ctx,\n    setupState = instance.setupState;\n  Object.keys(toRaw(setupState)).forEach(function (key) {\n    if (!setupState.__isScriptSetup) {\n      if (key[0] === '$' || key[0] === '_') {\n        warn(\"setup() return property \".concat(JSON.stringify(key), \" should not start with \\\"$\\\" or \\\"_\\\" \") + \"which are reserved prefixes for Vue internals.\");\n        return;\n      }\n      Object.defineProperty(ctx, key, {\n        enumerable: true,\n        configurable: true,\n        get: function get() {\n          return setupState[key];\n        },\n        set: NOOP\n      });\n    }\n  });\n}\nvar emptyAppContext = createAppContext();\nvar uid$1 = 0;\nfunction createComponentInstance(vnode, parent, suspense) {\n  var type = vnode.type;\n  // inherit parent app context - or - if root, adopt from root vnode\n  var appContext = (parent ? parent.appContext : vnode.appContext) || emptyAppContext;\n  var instance = {\n    uid: uid$1++,\n    vnode: vnode,\n    type: type,\n    parent: parent,\n    appContext: appContext,\n    root: null,\n    next: null,\n    subTree: null,\n    effect: null,\n    update: null,\n    scope: new EffectScope(true /* detached */),\n    render: null,\n    proxy: null,\n    exposed: null,\n    exposeProxy: null,\n    withProxy: null,\n    provides: parent ? parent.provides : Object.create(appContext.provides),\n    accessCache: null,\n    renderCache: [],\n    // local resovled assets\n    components: null,\n    directives: null,\n    // resolved props and emits options\n    propsOptions: normalizePropsOptions(type, appContext),\n    emitsOptions: normalizeEmitsOptions(type, appContext),\n    // emit\n    emit: null,\n    emitted: null,\n    // props default value\n    propsDefaults: EMPTY_OBJ,\n    // inheritAttrs\n    inheritAttrs: type.inheritAttrs,\n    // state\n    ctx: EMPTY_OBJ,\n    data: EMPTY_OBJ,\n    props: EMPTY_OBJ,\n    attrs: EMPTY_OBJ,\n    slots: EMPTY_OBJ,\n    refs: EMPTY_OBJ,\n    setupState: EMPTY_OBJ,\n    setupContext: null,\n    // suspense related\n    suspense: suspense,\n    suspenseId: suspense ? suspense.pendingId : 0,\n    asyncDep: null,\n    asyncResolved: false,\n    // lifecycle hooks\n    // not using enums here because it results in computed properties\n    isMounted: false,\n    isUnmounted: false,\n    isDeactivated: false,\n    bc: null,\n    c: null,\n    bm: null,\n    m: null,\n    bu: null,\n    u: null,\n    um: null,\n    bum: null,\n    da: null,\n    a: null,\n    rtg: null,\n    rtc: null,\n    ec: null,\n    sp: null\n  };\n  if (process.env.NODE_ENV !== 'production') {\n    instance.ctx = createDevRenderContext(instance);\n  } else {\n    instance.ctx = {\n      _: instance\n    };\n  }\n  instance.root = parent ? parent.root : instance;\n  instance.emit = emit$1.bind(null, instance);\n  // apply custom element special handling\n  if (vnode.ce) {\n    vnode.ce(instance);\n  }\n  return instance;\n}\nvar currentInstance = null;\nvar getCurrentInstance = function getCurrentInstance() {\n  return currentInstance || currentRenderingInstance;\n};\nvar setCurrentInstance = function setCurrentInstance(instance) {\n  currentInstance = instance;\n  instance.scope.on();\n};\nvar unsetCurrentInstance = function unsetCurrentInstance() {\n  currentInstance && currentInstance.scope.off();\n  currentInstance = null;\n};\nvar isBuiltInTag = /*#__PURE__*/makeMap('slot,component');\nfunction validateComponentName(name, config) {\n  var appIsNativeTag = config.isNativeTag || NO;\n  if (isBuiltInTag(name) || appIsNativeTag(name)) {\n    warn('Do not use built-in or reserved HTML elements as component id: ' + name);\n  }\n}\nfunction isStatefulComponent(instance) {\n  return instance.vnode.shapeFlag & 4 /* STATEFUL_COMPONENT */;\n}\n\nvar isInSSRComponentSetup = false;\nfunction setupComponent(instance) {\n  var isSSR = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n  isInSSRComponentSetup = isSSR;\n  var _instance$vnode = instance.vnode,\n    props = _instance$vnode.props,\n    children = _instance$vnode.children;\n  var isStateful = isStatefulComponent(instance);\n  initProps(instance, props, isStateful, isSSR);\n  initSlots(instance, children);\n  var setupResult = isStateful ? setupStatefulComponent(instance, isSSR) : undefined;\n  isInSSRComponentSetup = false;\n  return setupResult;\n}\nfunction setupStatefulComponent(instance, isSSR) {\n  var Component = instance.type;\n  if (process.env.NODE_ENV !== 'production') {\n    if (Component.name) {\n      validateComponentName(Component.name, instance.appContext.config);\n    }\n    if (Component.components) {\n      var names = Object.keys(Component.components);\n      for (var i = 0; i < names.length; i++) {\n        validateComponentName(names[i], instance.appContext.config);\n      }\n    }\n    if (Component.directives) {\n      var _names = Object.keys(Component.directives);\n      for (var _i5 = 0; _i5 < _names.length; _i5++) {\n        validateDirectiveName(_names[_i5]);\n      }\n    }\n    if (Component.compilerOptions && isRuntimeOnly()) {\n      warn(\"\\\"compilerOptions\\\" is only supported when using a build of Vue that \" + \"includes the runtime compiler. Since you are using a runtime-only \" + \"build, the options should be passed via your build tool config instead.\");\n    }\n  }\n  // 0. create render proxy property access cache\n  instance.accessCache = Object.create(null);\n  // 1. create public instance / render proxy\n  // also mark it raw so it's never observed\n  instance.proxy = markRaw(new Proxy(instance.ctx, PublicInstanceProxyHandlers));\n  if (process.env.NODE_ENV !== 'production') {\n    exposePropsOnRenderContext(instance);\n  }\n  // 2. call setup()\n  var setup = Component.setup;\n  if (setup) {\n    var setupContext = instance.setupContext = setup.length > 1 ? createSetupContext(instance) : null;\n    setCurrentInstance(instance);\n    pauseTracking();\n    var setupResult = callWithErrorHandling(setup, instance, 0 /* SETUP_FUNCTION */, [process.env.NODE_ENV !== 'production' ? shallowReadonly(instance.props) : instance.props, setupContext]);\n    resetTracking();\n    unsetCurrentInstance();\n    if (isPromise(setupResult)) {\n      setupResult.then(unsetCurrentInstance, unsetCurrentInstance);\n      if (isSSR) {\n        // return the promise so server-renderer can wait on it\n        return setupResult.then(function (resolvedResult) {\n          handleSetupResult(instance, resolvedResult, isSSR);\n        }).catch(function (e) {\n          handleError(e, instance, 0 /* SETUP_FUNCTION */);\n        });\n      } else {\n        // async setup returned Promise.\n        // bail here and wait for re-entry.\n        instance.asyncDep = setupResult;\n      }\n    } else {\n      handleSetupResult(instance, setupResult, isSSR);\n    }\n  } else {\n    finishComponentSetup(instance, isSSR);\n  }\n}\nfunction handleSetupResult(instance, setupResult, isSSR) {\n  if (isFunction(setupResult)) {\n    // setup returned an inline render function\n    if (instance.type.__ssrInlineRender) {\n      // when the function's name is `ssrRender` (compiled by SFC inline mode),\n      // set it as ssrRender instead.\n      instance.ssrRender = setupResult;\n    } else {\n      instance.render = setupResult;\n    }\n  } else if (isObject(setupResult)) {\n    if (process.env.NODE_ENV !== 'production' && isVNode(setupResult)) {\n      warn(\"setup() should not return VNodes directly - \" + \"return a render function instead.\");\n    }\n    // setup returned bindings.\n    // assuming a render function compiled from template is present.\n    if (process.env.NODE_ENV !== 'production' || __VUE_PROD_DEVTOOLS__) {\n      instance.devtoolsRawSetupState = setupResult;\n    }\n    instance.setupState = proxyRefs(setupResult);\n    if (process.env.NODE_ENV !== 'production') {\n      exposeSetupStateOnRenderContext(instance);\n    }\n  } else if (process.env.NODE_ENV !== 'production' && setupResult !== undefined) {\n    warn(\"setup() should return an object. Received: \".concat(setupResult === null ? 'null' : _typeof(setupResult)));\n  }\n  finishComponentSetup(instance, isSSR);\n}\nvar compile;\nvar installWithProxy;\n/**\r\n * For runtime-dom to register the compiler.\r\n * Note the exported method uses any to avoid d.ts relying on the compiler types.\r\n */\nfunction registerRuntimeCompiler(_compile) {\n  compile = _compile;\n  installWithProxy = function installWithProxy(i) {\n    if (i.render._rc) {\n      i.withProxy = new Proxy(i.ctx, RuntimeCompiledPublicInstanceProxyHandlers);\n    }\n  };\n}\n// dev only\nvar isRuntimeOnly = function isRuntimeOnly() {\n  return !compile;\n};\nfunction finishComponentSetup(instance, isSSR, skipOptions) {\n  var Component = instance.type;\n  // template / render function normalization\n  // could be already set when returned from setup()\n  if (!instance.render) {\n    // only do on-the-fly compile if not in SSR - SSR on-the-fly compliation\n    // is done by server-renderer\n    if (!isSSR && compile && !Component.render) {\n      var template = Component.template;\n      if (template) {\n        if (process.env.NODE_ENV !== 'production') {\n          startMeasure(instance, \"compile\");\n        }\n        var _instance$appContext$ = instance.appContext.config,\n          isCustomElement = _instance$appContext$.isCustomElement,\n          compilerOptions = _instance$appContext$.compilerOptions;\n        var delimiters = Component.delimiters,\n          componentCompilerOptions = Component.compilerOptions;\n        var finalCompilerOptions = extend(extend({\n          isCustomElement: isCustomElement,\n          delimiters: delimiters\n        }, compilerOptions), componentCompilerOptions);\n        Component.render = compile(template, finalCompilerOptions);\n        if (process.env.NODE_ENV !== 'production') {\n          endMeasure(instance, \"compile\");\n        }\n      }\n    }\n    instance.render = Component.render || NOOP;\n    // for runtime-compiled render functions using `with` blocks, the render\n    // proxy used needs a different `has` handler which is more performant and\n    // also only allows a whitelist of globals to fallthrough.\n    if (installWithProxy) {\n      installWithProxy(instance);\n    }\n  }\n  // support for 2.x options\n  if (__VUE_OPTIONS_API__ && !false) {\n    setCurrentInstance(instance);\n    pauseTracking();\n    applyOptions(instance);\n    resetTracking();\n    unsetCurrentInstance();\n  }\n  // warn missing template/render\n  // the runtime compilation of template in SSR is done by server-render\n  if (process.env.NODE_ENV !== 'production' && !Component.render && instance.render === NOOP && !isSSR) {\n    /* istanbul ignore if */\n    if (!compile && Component.template) {\n      warn(\"Component provided template option but \" + \"runtime compilation is not supported in this build of Vue.\" + \" Configure your bundler to alias \\\"vue\\\" to \\\"vue/dist/vue.esm-bundler.js\\\".\" /* should not happen */);\n    } else {\n      warn(\"Component is missing template or render function.\");\n    }\n  }\n}\nfunction createAttrsProxy(instance) {\n  return new Proxy(instance.attrs, process.env.NODE_ENV !== 'production' ? {\n    get: function get(target, key) {\n      markAttrsAccessed();\n      track(instance, \"get\" /* GET */, '$attrs');\n      return target[key];\n    },\n    set: function set() {\n      warn(\"setupContext.attrs is readonly.\");\n      return false;\n    },\n    deleteProperty: function deleteProperty() {\n      warn(\"setupContext.attrs is readonly.\");\n      return false;\n    }\n  } : {\n    get: function get(target, key) {\n      track(instance, \"get\" /* GET */, '$attrs');\n      return target[key];\n    }\n  });\n}\nfunction createSetupContext(instance) {\n  var expose = function expose(exposed) {\n    if (process.env.NODE_ENV !== 'production' && instance.exposed) {\n      warn(\"expose() should be called only once per setup().\");\n    }\n    instance.exposed = exposed || {};\n  };\n  var attrs;\n  if (process.env.NODE_ENV !== 'production') {\n    // We use getters in dev in case libs like test-utils overwrite instance\n    // properties (overwrites should not be done in prod)\n    return Object.freeze({\n      get attrs() {\n        return attrs || (attrs = createAttrsProxy(instance));\n      },\n      get slots() {\n        return shallowReadonly(instance.slots);\n      },\n      get emit() {\n        return function (event) {\n          for (var _len7 = arguments.length, args = new Array(_len7 > 1 ? _len7 - 1 : 0), _key15 = 1; _key15 < _len7; _key15++) {\n            args[_key15 - 1] = arguments[_key15];\n          }\n          return instance.emit.apply(instance, [event].concat(args));\n        };\n      },\n      expose: expose\n    });\n  } else {\n    return {\n      get attrs() {\n        return attrs || (attrs = createAttrsProxy(instance));\n      },\n      slots: instance.slots,\n      emit: instance.emit,\n      expose: expose\n    };\n  }\n}\nfunction getExposeProxy(instance) {\n  if (instance.exposed) {\n    return instance.exposeProxy || (instance.exposeProxy = new Proxy(proxyRefs(markRaw(instance.exposed)), {\n      get: function get(target, key) {\n        if (key in target) {\n          return target[key];\n        } else if (key in publicPropertiesMap) {\n          return publicPropertiesMap[key](instance);\n        }\n      }\n    }));\n  }\n}\nvar classifyRE = /(?:^|[-_])(\\w)/g;\nvar classify = function classify(str) {\n  return str.replace(classifyRE, function (c) {\n    return c.toUpperCase();\n  }).replace(/[-_]/g, '');\n};\nfunction getComponentName(Component) {\n  return isFunction(Component) ? Component.displayName || Component.name : Component.name;\n}\n/* istanbul ignore next */\nfunction formatComponentName(instance, Component) {\n  var isRoot = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n  var name = getComponentName(Component);\n  if (!name && Component.__file) {\n    var match = Component.__file.match(/([^/\\\\]+)\\.\\w+$/);\n    if (match) {\n      name = match[1];\n    }\n  }\n  if (!name && instance && instance.parent) {\n    // try to infer the name based on reverse resolution\n    var inferFromRegistry = function inferFromRegistry(registry) {\n      for (var key in registry) {\n        if (registry[key] === Component) {\n          return key;\n        }\n      }\n    };\n    name = inferFromRegistry(instance.components || instance.parent.type.components) || inferFromRegistry(instance.appContext.components);\n  }\n  return name ? classify(name) : isRoot ? \"App\" : \"Anonymous\";\n}\nfunction isClassComponent(value) {\n  return isFunction(value) && '__vccOpts' in value;\n}\nvar stack = [];\nfunction pushWarningContext(vnode) {\n  stack.push(vnode);\n}\nfunction popWarningContext() {\n  stack.pop();\n}\nfunction warn(msg) {\n  // avoid props formatting or warn handler tracking deps that might be mutated\n  // during patch, leading to infinite recursion.\n  pauseTracking();\n  var instance = stack.length ? stack[stack.length - 1].component : null;\n  var appWarnHandler = instance && instance.appContext.config.warnHandler;\n  var trace = getComponentTrace();\n  for (var _len8 = arguments.length, args = new Array(_len8 > 1 ? _len8 - 1 : 0), _key16 = 1; _key16 < _len8; _key16++) {\n    args[_key16 - 1] = arguments[_key16];\n  }\n  if (appWarnHandler) {\n    callWithErrorHandling(appWarnHandler, instance, 11 /* APP_WARN_HANDLER */, [msg + args.join(''), instance && instance.proxy, trace.map(function (_ref21) {\n      var vnode = _ref21.vnode;\n      return \"at <\".concat(formatComponentName(instance, vnode.type), \">\");\n    }).join('\\n'), trace]);\n  } else {\n    var warnArgs = [\"[Vue warn]: \".concat(msg)].concat(args);\n    /* istanbul ignore if */\n    if (trace.length &&\n    // avoid spamming console during tests\n    !false) {\n      warnArgs.push.apply(warnArgs, [\"\\n\"].concat(_toConsumableArray(formatTrace(trace))));\n    }\n  }\n  resetTracking();\n}\nfunction getComponentTrace() {\n  var currentVNode = stack[stack.length - 1];\n  if (!currentVNode) {\n    return [];\n  }\n  // we can't just use the stack because it will be incomplete during updates\n  // that did not start from the root. Re-construct the parent chain using\n  // instance parent pointers.\n  var normalizedStack = [];\n  while (currentVNode) {\n    var last = normalizedStack[0];\n    if (last && last.vnode === currentVNode) {\n      last.recurseCount++;\n    } else {\n      normalizedStack.push({\n        vnode: currentVNode,\n        recurseCount: 0\n      });\n    }\n    var parentInstance = currentVNode.component && currentVNode.component.parent;\n    currentVNode = parentInstance && parentInstance.vnode;\n  }\n  return normalizedStack;\n}\n/* istanbul ignore next */\nfunction formatTrace(trace) {\n  var logs = [];\n  trace.forEach(function (entry, i) {\n    logs.push.apply(logs, _toConsumableArray(i === 0 ? [] : [\"\\n\"]).concat(_toConsumableArray(formatTraceEntry(entry))));\n  });\n  return logs;\n}\nfunction formatTraceEntry(_ref22) {\n  var vnode = _ref22.vnode,\n    recurseCount = _ref22.recurseCount;\n  var postfix = recurseCount > 0 ? \"... (\".concat(recurseCount, \" recursive calls)\") : \"\";\n  var isRoot = vnode.component ? vnode.component.parent == null : false;\n  var open = \" at <\".concat(formatComponentName(vnode.component, vnode.type, isRoot));\n  var close = \">\" + postfix;\n  return vnode.props ? [open].concat(_toConsumableArray(formatProps(vnode.props)), [close]) : [open + close];\n}\n/* istanbul ignore next */\nfunction formatProps(props) {\n  var res = [];\n  var keys = Object.keys(props);\n  keys.slice(0, 3).forEach(function (key) {\n    res.push.apply(res, _toConsumableArray(formatProp(key, props[key])));\n  });\n  if (keys.length > 3) {\n    res.push(\" ...\");\n  }\n  return res;\n}\n/* istanbul ignore next */\nfunction formatProp(key, value, raw) {\n  if (isString(value)) {\n    value = JSON.stringify(value);\n    return raw ? value : [\"\".concat(key, \"=\").concat(value)];\n  } else if (typeof value === 'number' || typeof value === 'boolean' || value == null) {\n    return raw ? value : [\"\".concat(key, \"=\").concat(value)];\n  } else if (isRef(value)) {\n    value = formatProp(key, toRaw(value.value), true);\n    return raw ? value : [\"\".concat(key, \"=Ref<\"), value, \">\"];\n  } else if (isFunction(value)) {\n    return [\"\".concat(key, \"=fn\").concat(value.name ? \"<\".concat(value.name, \">\") : \"\")];\n  } else {\n    value = toRaw(value);\n    return raw ? value : [\"\".concat(key, \"=\"), value];\n  }\n}\nvar ErrorTypeStrings = (_ErrorTypeStrings = {}, _defineProperty(_ErrorTypeStrings, \"sp\" /* SERVER_PREFETCH */, 'serverPrefetch hook'), _defineProperty(_ErrorTypeStrings, \"bc\" /* BEFORE_CREATE */, 'beforeCreate hook'), _defineProperty(_ErrorTypeStrings, \"c\" /* CREATED */, 'created hook'), _defineProperty(_ErrorTypeStrings, \"bm\" /* BEFORE_MOUNT */, 'beforeMount hook'), _defineProperty(_ErrorTypeStrings, \"m\" /* MOUNTED */, 'mounted hook'), _defineProperty(_ErrorTypeStrings, \"bu\" /* BEFORE_UPDATE */, 'beforeUpdate hook'), _defineProperty(_ErrorTypeStrings, \"u\" /* UPDATED */, 'updated'), _defineProperty(_ErrorTypeStrings, \"bum\" /* BEFORE_UNMOUNT */, 'beforeUnmount hook'), _defineProperty(_ErrorTypeStrings, \"um\" /* UNMOUNTED */, 'unmounted hook'), _defineProperty(_ErrorTypeStrings, \"a\" /* ACTIVATED */, 'activated hook'), _defineProperty(_ErrorTypeStrings, \"da\" /* DEACTIVATED */, 'deactivated hook'), _defineProperty(_ErrorTypeStrings, \"ec\" /* ERROR_CAPTURED */, 'errorCaptured hook'), _defineProperty(_ErrorTypeStrings, \"rtc\" /* RENDER_TRACKED */, 'renderTracked hook'), _defineProperty(_ErrorTypeStrings, \"rtg\" /* RENDER_TRIGGERED */, 'renderTriggered hook'), _defineProperty(_ErrorTypeStrings, 0 /* SETUP_FUNCTION */, 'setup function'), _defineProperty(_ErrorTypeStrings, 1 /* RENDER_FUNCTION */, 'render function'), _defineProperty(_ErrorTypeStrings, 2 /* WATCH_GETTER */, 'watcher getter'), _defineProperty(_ErrorTypeStrings, 3 /* WATCH_CALLBACK */, 'watcher callback'), _defineProperty(_ErrorTypeStrings, 4 /* WATCH_CLEANUP */, 'watcher cleanup function'), _defineProperty(_ErrorTypeStrings, 5 /* NATIVE_EVENT_HANDLER */, 'native event handler'), _defineProperty(_ErrorTypeStrings, 6 /* COMPONENT_EVENT_HANDLER */, 'component event handler'), _defineProperty(_ErrorTypeStrings, 7 /* VNODE_HOOK */, 'vnode hook'), _defineProperty(_ErrorTypeStrings, 8 /* DIRECTIVE_HOOK */, 'directive hook'), _defineProperty(_ErrorTypeStrings, 9 /* TRANSITION_HOOK */, 'transition hook'), _defineProperty(_ErrorTypeStrings, 10 /* APP_ERROR_HANDLER */, 'app errorHandler'), _defineProperty(_ErrorTypeStrings, 11 /* APP_WARN_HANDLER */, 'app warnHandler'), _defineProperty(_ErrorTypeStrings, 12 /* FUNCTION_REF */, 'ref function'), _defineProperty(_ErrorTypeStrings, 13 /* ASYNC_COMPONENT_LOADER */, 'async component loader'), _defineProperty(_ErrorTypeStrings, 14 /* SCHEDULER */, 'scheduler flush. This is likely a Vue internals bug. ' + 'Please open an issue at https://new-issue.vuejs.org/?repo=vuejs/vue-next'), _ErrorTypeStrings);\nfunction callWithErrorHandling(fn, instance, type, args) {\n  var res;\n  try {\n    res = args ? fn.apply(void 0, _toConsumableArray(args)) : fn();\n  } catch (err) {\n    handleError(err, instance, type);\n  }\n  return res;\n}\nfunction callWithAsyncErrorHandling(fn, instance, type, args) {\n  if (isFunction(fn)) {\n    var res = callWithErrorHandling(fn, instance, type, args);\n    if (res && isPromise(res)) {\n      res.catch(function (err) {\n        handleError(err, instance, type);\n      });\n    }\n    return res;\n  }\n  var values = [];\n  for (var i = 0; i < fn.length; i++) {\n    values.push(callWithAsyncErrorHandling(fn[i], instance, type, args));\n  }\n  return values;\n}\nfunction handleError(err, instance, type) {\n  var throwInDev = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : true;\n  var contextVNode = instance ? instance.vnode : null;\n  if (instance) {\n    var cur = instance.parent;\n    // the exposed instance is the render proxy to keep it consistent with 2.x\n    var exposedInstance = instance.proxy;\n    // in production the hook receives only the error code\n    var errorInfo = process.env.NODE_ENV !== 'production' ? ErrorTypeStrings[type] : type;\n    while (cur) {\n      var errorCapturedHooks = cur.ec;\n      if (errorCapturedHooks) {\n        for (var i = 0; i < errorCapturedHooks.length; i++) {\n          if (errorCapturedHooks[i](err, exposedInstance, errorInfo) === false) {\n            return;\n          }\n        }\n      }\n      cur = cur.parent;\n    }\n    // app-level handling\n    var appErrorHandler = instance.appContext.config.errorHandler;\n    if (appErrorHandler) {\n      callWithErrorHandling(appErrorHandler, null, 10 /* APP_ERROR_HANDLER */, [err, exposedInstance, errorInfo]);\n      return;\n    }\n  }\n  logError(err, type, contextVNode, throwInDev);\n}\nfunction logError(err, type, contextVNode) {\n  var throwInDev = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : true;\n  if (process.env.NODE_ENV !== 'production') {\n    var info = ErrorTypeStrings[type];\n    if (contextVNode) {\n      pushWarningContext(contextVNode);\n    }\n    warn(\"Unhandled error\".concat(info ? \" during execution of \".concat(info) : \"\"));\n    if (contextVNode) {\n      popWarningContext();\n    }\n    // crash in dev by default so it's more noticeable\n    if (throwInDev) {\n      throw err;\n    } else {}\n  } else {}\n}\nvar isFlushing = false;\nvar isFlushPending = false;\nvar queue = [];\nvar flushIndex = 0;\nvar pendingPreFlushCbs = [];\nvar activePreFlushCbs = null;\nvar preFlushIndex = 0;\nvar pendingPostFlushCbs = [];\nvar activePostFlushCbs = null;\nvar postFlushIndex = 0;\nvar resolvedPromise = Promise.resolve();\nvar currentFlushPromise = null;\nvar currentPreFlushParentJob = null;\nvar RECURSION_LIMIT = 100;\nfunction nextTick(fn) {\n  var p = currentFlushPromise || resolvedPromise;\n  return fn ? p.then(this ? fn.bind(this) : fn) : p;\n}\n// #2768\n// Use binary-search to find a suitable position in the queue,\n// so that the queue maintains the increasing order of job's id,\n// which can prevent the job from being skipped and also can avoid repeated patching.\nfunction findInsertionIndex(id) {\n  // the start index should be `flushIndex + 1`\n  var start = flushIndex + 1;\n  var end = queue.length;\n  while (start < end) {\n    var middle = start + end >>> 1;\n    var middleJobId = getId(queue[middle]);\n    middleJobId < id ? start = middle + 1 : end = middle;\n  }\n  return start;\n}\nfunction queueJob(job) {\n  // the dedupe search uses the startIndex argument of Array.includes()\n  // by default the search index includes the current job that is being run\n  // so it cannot recursively trigger itself again.\n  // if the job is a watch() callback, the search will start with a +1 index to\n  // allow it recursively trigger itself - it is the user's responsibility to\n  // ensure it doesn't end up in an infinite loop.\n  if ((!queue.length || !queue.includes(job, isFlushing && job.allowRecurse ? flushIndex + 1 : flushIndex)) && job !== currentPreFlushParentJob) {\n    if (job.id == null) {\n      queue.push(job);\n    } else {\n      queue.splice(findInsertionIndex(job.id), 0, job);\n    }\n    queueFlush();\n  }\n}\nfunction queueFlush() {\n  if (!isFlushing && !isFlushPending) {\n    isFlushPending = true;\n    currentFlushPromise = resolvedPromise.then(flushJobs);\n  }\n}\nfunction invalidateJob(job) {\n  var i = queue.indexOf(job);\n  if (i > flushIndex) {\n    queue.splice(i, 1);\n  }\n}\nfunction queueCb(cb, activeQueue, pendingQueue, index) {\n  if (!isArray(cb)) {\n    if (!activeQueue || !activeQueue.includes(cb, cb.allowRecurse ? index + 1 : index)) {\n      pendingQueue.push(cb);\n    }\n  } else {\n    // if cb is an array, it is a component lifecycle hook which can only be\n    // triggered by a job, which is already deduped in the main queue, so\n    // we can skip duplicate check here to improve perf\n    pendingQueue.push.apply(pendingQueue, _toConsumableArray(cb));\n  }\n  queueFlush();\n}\nfunction queuePreFlushCb(cb) {\n  queueCb(cb, activePreFlushCbs, pendingPreFlushCbs, preFlushIndex);\n}\nfunction queuePostFlushCb(cb) {\n  queueCb(cb, activePostFlushCbs, pendingPostFlushCbs, postFlushIndex);\n}\nfunction flushPreFlushCbs(seen) {\n  var parentJob = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;\n  if (pendingPreFlushCbs.length) {\n    currentPreFlushParentJob = parentJob;\n    activePreFlushCbs = _toConsumableArray(new Set(pendingPreFlushCbs));\n    pendingPreFlushCbs.length = 0;\n    if (process.env.NODE_ENV !== 'production') {\n      seen = seen || new Map();\n    }\n    for (preFlushIndex = 0; preFlushIndex < activePreFlushCbs.length; preFlushIndex++) {\n      if (process.env.NODE_ENV !== 'production' && checkRecursiveUpdates(seen, activePreFlushCbs[preFlushIndex])) {\n        continue;\n      }\n      activePreFlushCbs[preFlushIndex]();\n    }\n    activePreFlushCbs = null;\n    preFlushIndex = 0;\n    currentPreFlushParentJob = null;\n    // recursively flush until it drains\n    flushPreFlushCbs(seen, parentJob);\n  }\n}\nfunction flushPostFlushCbs(seen) {\n  if (pendingPostFlushCbs.length) {\n    var deduped = _toConsumableArray(new Set(pendingPostFlushCbs));\n    pendingPostFlushCbs.length = 0;\n    // #1947 already has active queue, nested flushPostFlushCbs call\n    if (activePostFlushCbs) {\n      var _activePostFlushCbs;\n      (_activePostFlushCbs = activePostFlushCbs).push.apply(_activePostFlushCbs, _toConsumableArray(deduped));\n      return;\n    }\n    activePostFlushCbs = deduped;\n    if (process.env.NODE_ENV !== 'production') {\n      seen = seen || new Map();\n    }\n    activePostFlushCbs.sort(function (a, b) {\n      return getId(a) - getId(b);\n    });\n    for (postFlushIndex = 0; postFlushIndex < activePostFlushCbs.length; postFlushIndex++) {\n      if (process.env.NODE_ENV !== 'production' && checkRecursiveUpdates(seen, activePostFlushCbs[postFlushIndex])) {\n        continue;\n      }\n      activePostFlushCbs[postFlushIndex]();\n    }\n    activePostFlushCbs = null;\n    postFlushIndex = 0;\n  }\n}\nvar getId = function getId(job) {\n  return job.id == null ? Infinity : job.id;\n};\nfunction flushJobs(seen) {\n  isFlushPending = false;\n  isFlushing = true;\n  if (process.env.NODE_ENV !== 'production') {\n    seen = seen || new Map();\n  }\n  flushPreFlushCbs(seen);\n  // Sort queue before flush.\n  // This ensures that:\n  // 1. Components are updated from parent to child. (because parent is always\n  //    created before the child so its render effect will have smaller\n  //    priority number)\n  // 2. If a component is unmounted during a parent component's update,\n  //    its update can be skipped.\n  queue.sort(function (a, b) {\n    return getId(a) - getId(b);\n  });\n  // conditional usage of checkRecursiveUpdate must be determined out of\n  // try ... catch block since Rollup by default de-optimizes treeshaking\n  // inside try-catch. This can leave all warning code unshaked. Although\n  // they would get eventually shaken by a minifier like terser, some minifiers\n  // would fail to do that (e.g. https://github.com/evanw/esbuild/issues/1610)\n  var check = process.env.NODE_ENV !== 'production' ? function (job) {\n    return checkRecursiveUpdates(seen, job);\n  } : NOOP;\n  try {\n    for (flushIndex = 0; flushIndex < queue.length; flushIndex++) {\n      var job = queue[flushIndex];\n      if (job && job.active !== false) {\n        if (process.env.NODE_ENV !== 'production' && check(job)) {\n          continue;\n        }\n        // console.log(`running:`, job.id)\n        callWithErrorHandling(job, null, 14 /* SCHEDULER */);\n      }\n    }\n  } finally {\n    flushIndex = 0;\n    queue.length = 0;\n    flushPostFlushCbs(seen);\n    isFlushing = false;\n    currentFlushPromise = null;\n    // some postFlushCb queued jobs!\n    // keep flushing until it drains.\n    if (queue.length || pendingPreFlushCbs.length || pendingPostFlushCbs.length) {\n      flushJobs(seen);\n    }\n  }\n}\nfunction checkRecursiveUpdates(seen, fn) {\n  if (!seen.has(fn)) {\n    seen.set(fn, 1);\n  } else {\n    var count = seen.get(fn);\n    if (count > RECURSION_LIMIT) {\n      var instance = fn.ownerInstance;\n      var componentName = instance && getComponentName(instance.type);\n      warn(\"Maximum recursive updates exceeded\".concat(componentName ? \" in component <\".concat(componentName, \">\") : \"\", \". \") + \"This means you have a reactive effect that is mutating its own \" + \"dependencies and thus recursively triggering itself. Possible sources \" + \"include component template, render function, updated hook or \" + \"watcher source function.\");\n      return true;\n    } else {\n      seen.set(fn, count + 1);\n    }\n  }\n}\n\n// Simple effect.\nfunction watchEffect(effect, options) {\n  return doWatch(effect, null, options);\n}\nfunction watchPostEffect(effect, options) {\n  return doWatch(effect, null, process.env.NODE_ENV !== 'production' ? Object.assign(options || {}, {\n    flush: 'post'\n  }) : {\n    flush: 'post'\n  });\n}\nfunction watchSyncEffect(effect, options) {\n  return doWatch(effect, null, process.env.NODE_ENV !== 'production' ? Object.assign(options || {}, {\n    flush: 'sync'\n  }) : {\n    flush: 'sync'\n  });\n}\n// initial value for watchers to trigger on undefined initial values\nvar INITIAL_WATCHER_VALUE = {};\n// implementation\nfunction watch(source, cb, options) {\n  if (process.env.NODE_ENV !== 'production' && !isFunction(cb)) {\n    warn(\"`watch(fn, options?)` signature has been moved to a separate API. \" + \"Use `watchEffect(fn, options?)` instead. `watch` now only \" + \"supports `watch(source, cb, options?) signature.\");\n  }\n  return doWatch(source, cb, options);\n}\nfunction doWatch(source, cb) {\n  var _ref23 = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : EMPTY_OBJ,\n    immediate = _ref23.immediate,\n    deep = _ref23.deep,\n    flush = _ref23.flush,\n    onTrack = _ref23.onTrack,\n    onTrigger = _ref23.onTrigger;\n  if (process.env.NODE_ENV !== 'production' && !cb) {\n    if (immediate !== undefined) {\n      warn(\"watch() \\\"immediate\\\" option is only respected when using the \" + \"watch(source, callback, options?) signature.\");\n    }\n    if (deep !== undefined) {\n      warn(\"watch() \\\"deep\\\" option is only respected when using the \" + \"watch(source, callback, options?) signature.\");\n    }\n  }\n  var warnInvalidSource = function warnInvalidSource(s) {\n    warn(\"Invalid watch source: \", s, \"A watch source can only be a getter/effect function, a ref, \" + \"a reactive object, or an array of these types.\");\n  };\n  var instance = currentInstance;\n  var getter;\n  var forceTrigger = false;\n  var isMultiSource = false;\n  if (isRef(source)) {\n    getter = function getter() {\n      return source.value;\n    };\n    forceTrigger = !!source._shallow;\n  } else if (isReactive(source)) {\n    getter = function getter() {\n      return source;\n    };\n    deep = true;\n  } else if (isArray(source)) {\n    isMultiSource = true;\n    forceTrigger = source.some(isReactive);\n    getter = function getter() {\n      return source.map(function (s) {\n        if (isRef(s)) {\n          return s.value;\n        } else if (isReactive(s)) {\n          return traverse(s);\n        } else if (isFunction(s)) {\n          return callWithErrorHandling(s, instance, 2 /* WATCH_GETTER */);\n        } else {\n          process.env.NODE_ENV !== 'production' && warnInvalidSource(s);\n        }\n      });\n    };\n  } else if (isFunction(source)) {\n    if (cb) {\n      // getter with cb\n      getter = function getter() {\n        return callWithErrorHandling(source, instance, 2 /* WATCH_GETTER */);\n      };\n    } else {\n      // no cb -> simple effect\n      getter = function getter() {\n        if (instance && instance.isUnmounted) {\n          return;\n        }\n        if (cleanup) {\n          cleanup();\n        }\n        return callWithAsyncErrorHandling(source, instance, 3 /* WATCH_CALLBACK */, [onInvalidate]);\n      };\n    }\n  } else {\n    getter = NOOP;\n    process.env.NODE_ENV !== 'production' && warnInvalidSource(source);\n  }\n  if (cb && deep) {\n    var baseGetter = getter;\n    getter = function getter() {\n      return traverse(baseGetter());\n    };\n  }\n  var cleanup;\n  var onInvalidate = function onInvalidate(fn) {\n    cleanup = effect.onStop = function () {\n      callWithErrorHandling(fn, instance, 4 /* WATCH_CLEANUP */);\n    };\n  };\n  // in SSR there is no need to setup an actual effect, and it should be noop\n  // unless it's eager\n  if (isInSSRComponentSetup) {\n    // we will also not call the invalidate callback (+ runner is not set up)\n    onInvalidate = NOOP;\n    if (!cb) {\n      getter();\n    } else if (immediate) {\n      callWithAsyncErrorHandling(cb, instance, 3 /* WATCH_CALLBACK */, [getter(), isMultiSource ? [] : undefined, onInvalidate]);\n    }\n    return NOOP;\n  }\n  var oldValue = isMultiSource ? [] : INITIAL_WATCHER_VALUE;\n  var job = function job() {\n    if (!effect.active) {\n      return;\n    }\n    if (cb) {\n      // watch(source, cb)\n      var newValue = effect.run();\n      if (deep || forceTrigger || (isMultiSource ? newValue.some(function (v, i) {\n        return hasChanged(v, oldValue[i]);\n      }) : hasChanged(newValue, oldValue)) || false) {\n        // cleanup before running cb again\n        if (cleanup) {\n          cleanup();\n        }\n        callWithAsyncErrorHandling(cb, instance, 3 /* WATCH_CALLBACK */, [newValue,\n        // pass undefined as the old value when it's changed for the first time\n        oldValue === INITIAL_WATCHER_VALUE ? undefined : oldValue, onInvalidate]);\n        oldValue = newValue;\n      }\n    } else {\n      // watchEffect\n      effect.run();\n    }\n  };\n  // important: mark the job as a watcher callback so that scheduler knows\n  // it is allowed to self-trigger (#1727)\n  job.allowRecurse = !!cb;\n  var scheduler;\n  if (flush === 'sync') {\n    scheduler = job; // the scheduler function gets called directly\n  } else if (flush === 'post') {\n    scheduler = function scheduler() {\n      return queuePostRenderEffect(job, instance && instance.suspense);\n    };\n  } else {\n    // default: 'pre'\n    scheduler = function scheduler() {\n      if (!instance || instance.isMounted) {\n        queuePreFlushCb(job);\n      } else {\n        // with 'pre' option, the first call must happen before\n        // the component is mounted so it is called synchronously.\n        job();\n      }\n    };\n  }\n  var effect = new ReactiveEffect(getter, scheduler);\n  if (process.env.NODE_ENV !== 'production') {\n    effect.onTrack = onTrack;\n    effect.onTrigger = onTrigger;\n  }\n  // initial run\n  if (cb) {\n    if (immediate) {\n      job();\n    } else {\n      oldValue = effect.run();\n    }\n  } else if (flush === 'post') {\n    queuePostRenderEffect(effect.run.bind(effect), instance && instance.suspense);\n  } else {\n    effect.run();\n  }\n  return function () {\n    effect.stop();\n    if (instance && instance.scope) {\n      remove(instance.scope.effects, effect);\n    }\n  };\n}\n// this.$watch\nfunction instanceWatch(source, value, options) {\n  var publicThis = this.proxy;\n  var getter = isString(source) ? source.includes('.') ? createPathGetter(publicThis, source) : function () {\n    return publicThis[source];\n  } : source.bind(publicThis, publicThis);\n  var cb;\n  if (isFunction(value)) {\n    cb = value;\n  } else {\n    cb = value.handler;\n    options = value;\n  }\n  var cur = currentInstance;\n  setCurrentInstance(this);\n  var res = doWatch(getter, cb.bind(publicThis), options);\n  if (cur) {\n    setCurrentInstance(cur);\n  } else {\n    unsetCurrentInstance();\n  }\n  return res;\n}\nfunction createPathGetter(ctx, path) {\n  var segments = path.split('.');\n  return function () {\n    var cur = ctx;\n    for (var i = 0; i < segments.length && cur; i++) {\n      cur = cur[segments[i]];\n    }\n    return cur;\n  };\n}\nfunction traverse(value, seen) {\n  if (!isObject(value) || value[\"__v_skip\" /* SKIP */]) {\n    return value;\n  }\n  seen = seen || new Set();\n  if (seen.has(value)) {\n    return value;\n  }\n  seen.add(value);\n  if (isRef(value)) {\n    traverse(value.value, seen);\n  } else if (isArray(value)) {\n    for (var i = 0; i < value.length; i++) {\n      traverse(value[i], seen);\n    }\n  } else if (isSet(value) || isMap(value)) {\n    value.forEach(function (v) {\n      traverse(v, seen);\n    });\n  } else if (isPlainObject(value)) {\n    for (var key in value) {\n      traverse(value[key], seen);\n    }\n  }\n  return value;\n}\n\n// dev only\nvar warnRuntimeUsage = function warnRuntimeUsage(method) {\n  return warn(\"\".concat(method, \"() is a compiler-hint helper that is only usable inside \") + \"<script setup> of a single file component. Its arguments should be \" + \"compiled away and passing it at runtime has no effect.\");\n};\n// implementation\nfunction defineProps() {\n  if (process.env.NODE_ENV !== 'production') {\n    warnRuntimeUsage(\"defineProps\");\n  }\n  return null;\n}\n// implementation\nfunction defineEmits() {\n  if (process.env.NODE_ENV !== 'production') {\n    warnRuntimeUsage(\"defineEmits\");\n  }\n  return null;\n}\n/**\r\n * Vue `<script setup>` compiler macro for declaring a component's exposed\r\n * instance properties when it is accessed by a parent component via template\r\n * refs.\r\n *\r\n * `<script setup>` components are closed by default - i.e. varaibles inside\r\n * the `<script setup>` scope is not exposed to parent unless explicitly exposed\r\n * via `defineExpose`.\r\n *\r\n * This is only usable inside `<script setup>`, is compiled away in the\r\n * output and should **not** be actually called at runtime.\r\n */\nfunction defineExpose(exposed) {\n  if (process.env.NODE_ENV !== 'production') {\n    warnRuntimeUsage(\"defineExpose\");\n  }\n}\n/**\r\n * Vue `<script setup>` compiler macro for providing props default values when\r\n * using type-based `defineProps` declaration.\r\n *\r\n * Example usage:\r\n * ```ts\r\n * withDefaults(defineProps<{\r\n *   size?: number\r\n *   labels?: string[]\r\n * }>(), {\r\n *   size: 3,\r\n *   labels: () => ['default label']\r\n * })\r\n * ```\r\n *\r\n * This is only usable inside `<script setup>`, is compiled away in the output\r\n * and should **not** be actually called at runtime.\r\n */\nfunction withDefaults(props, defaults) {\n  if (process.env.NODE_ENV !== 'production') {\n    warnRuntimeUsage(\"withDefaults\");\n  }\n  return null;\n}\nfunction useSlots() {\n  return getContext().slots;\n}\nfunction useAttrs() {\n  return getContext().attrs;\n}\nfunction getContext() {\n  var i = getCurrentInstance();\n  if (process.env.NODE_ENV !== 'production' && !i) {\n    warn(\"useContext() called without active instance.\");\n  }\n  return i.setupContext || (i.setupContext = createSetupContext(i));\n}\n/**\r\n * Runtime helper for merging default declarations. Imported by compiled code\r\n * only.\r\n * @internal\r\n */\nfunction mergeDefaults(raw, defaults) {\n  var props = isArray(raw) ? raw.reduce(function (normalized, p) {\n    return normalized[p] = {}, normalized;\n  }, {}) : raw;\n  for (var key in defaults) {\n    var opt = props[key];\n    if (opt) {\n      if (isArray(opt) || isFunction(opt)) {\n        props[key] = {\n          type: opt,\n          default: defaults[key]\n        };\n      } else {\n        opt.default = defaults[key];\n      }\n    } else if (opt === null) {\n      props[key] = {\n        default: defaults[key]\n      };\n    } else if (process.env.NODE_ENV !== 'production') {\n      warn(\"props default key \\\"\".concat(key, \"\\\" has no corresponding declaration.\"));\n    }\n  }\n  return props;\n}\n/**\r\n * Used to create a proxy for the rest element when destructuring props with\r\n * defineProps().\r\n * @internal\r\n */\nfunction createPropsRestProxy(props, excludedKeys) {\n  var ret = {};\n  var _loop5 = function _loop5(key) {\n    if (!excludedKeys.includes(key)) {\n      Object.defineProperty(ret, key, {\n        enumerable: true,\n        get: function get() {\n          return props[key];\n        }\n      });\n    }\n  };\n  for (var key in props) {\n    _loop5(key);\n  }\n  return ret;\n}\n/**\r\n * `<script setup>` helper for persisting the current instance context over\r\n * async/await flows.\r\n *\r\n * `@vue/compiler-sfc` converts the following:\r\n *\r\n * ```ts\r\n * const x = await foo()\r\n * ```\r\n *\r\n * into:\r\n *\r\n * ```ts\r\n * let __temp, __restore\r\n * const x = (([__temp, __restore] = withAsyncContext(() => foo())),__temp=await __temp,__restore(),__temp)\r\n * ```\r\n * @internal\r\n */\nfunction withAsyncContext(getAwaitable) {\n  var ctx = getCurrentInstance();\n  if (process.env.NODE_ENV !== 'production' && !ctx) {\n    warn(\"withAsyncContext called without active current instance. \" + \"This is likely a bug.\");\n  }\n  var awaitable = getAwaitable();\n  unsetCurrentInstance();\n  if (isPromise(awaitable)) {\n    awaitable = awaitable.catch(function (e) {\n      setCurrentInstance(ctx);\n      throw e;\n    });\n  }\n  return [awaitable, function () {\n    return setCurrentInstance(ctx);\n  }];\n}\n\n// Actual implementation\nfunction h(type, propsOrChildren, children) {\n  var l = arguments.length;\n  if (l === 2) {\n    if (isObject(propsOrChildren) && !isArray(propsOrChildren)) {\n      // single vnode without props\n      if (isVNode(propsOrChildren)) {\n        return createVNode(type, null, [propsOrChildren]);\n      }\n      // props without children\n      return createVNode(type, propsOrChildren);\n    } else {\n      // omit props\n      return createVNode(type, null, propsOrChildren);\n    }\n  } else {\n    if (l > 3) {\n      children = Array.prototype.slice.call(arguments, 2);\n    } else if (l === 3 && isVNode(children)) {\n      children = [children];\n    }\n    return createVNode(type, propsOrChildren, children);\n  }\n}\nvar ssrContextKey = Symbol(process.env.NODE_ENV !== 'production' ? \"ssrContext\" : \"\");\nvar useSSRContext = function useSSRContext() {\n  {\n    var ctx = inject(ssrContextKey);\n    if (!ctx) {\n      warn(\"Server rendering context not provided. Make sure to only call \" + \"useSSRContext() conditionally in the server build.\");\n    }\n    return ctx;\n  }\n};\nfunction initCustomFormatter() {\n  /* eslint-disable no-restricted-globals */\n  if (!(process.env.NODE_ENV !== 'production') || typeof window === 'undefined') {\n    return;\n  }\n  var vueStyle = {\n    style: 'color:#3ba776'\n  };\n  var numberStyle = {\n    style: 'color:#0b1bc9'\n  };\n  var stringStyle = {\n    style: 'color:#b62e24'\n  };\n  var keywordStyle = {\n    style: 'color:#9d288c'\n  };\n  // custom formatter for Chrome\n  // https://www.mattzeunert.com/2016/02/19/custom-chrome-devtools-object-formatters.html\n  var formatter = {\n    header: function header(obj) {\n      // TODO also format ComponentPublicInstance & ctx.slots/attrs in setup\n      if (!isObject(obj)) {\n        return null;\n      }\n      if (obj.__isVue) {\n        return ['div', vueStyle, \"VueInstance\"];\n      } else if (isRef(obj)) {\n        return ['div', {}, ['span', vueStyle, genRefFlag(obj)], '<', formatValue(obj.value), \">\"];\n      } else if (isReactive(obj)) {\n        return ['div', {}, ['span', vueStyle, 'Reactive'], '<', formatValue(obj), \">\".concat(isReadonly(obj) ? \" (readonly)\" : \"\")];\n      } else if (isReadonly(obj)) {\n        return ['div', {}, ['span', vueStyle, 'Readonly'], '<', formatValue(obj), '>'];\n      }\n      return null;\n    },\n    hasBody: function hasBody(obj) {\n      return obj && obj.__isVue;\n    },\n    body: function body(obj) {\n      if (obj && obj.__isVue) {\n        return ['div', {}].concat(_toConsumableArray(formatInstance(obj.$)));\n      }\n    }\n  };\n  function formatInstance(instance) {\n    var blocks = [];\n    if (instance.type.props && instance.props) {\n      blocks.push(createInstanceBlock('props', toRaw(instance.props)));\n    }\n    if (instance.setupState !== EMPTY_OBJ) {\n      blocks.push(createInstanceBlock('setup', instance.setupState));\n    }\n    if (instance.data !== EMPTY_OBJ) {\n      blocks.push(createInstanceBlock('data', toRaw(instance.data)));\n    }\n    var computed = extractKeys(instance, 'computed');\n    if (computed) {\n      blocks.push(createInstanceBlock('computed', computed));\n    }\n    var injected = extractKeys(instance, 'inject');\n    if (injected) {\n      blocks.push(createInstanceBlock('injected', injected));\n    }\n    blocks.push(['div', {}, ['span', {\n      style: keywordStyle.style + ';opacity:0.66'\n    }, '$ (internal): '], ['object', {\n      object: instance\n    }]]);\n    return blocks;\n  }\n  function createInstanceBlock(type, target) {\n    target = extend({}, target);\n    if (!Object.keys(target).length) {\n      return ['span', {}];\n    }\n    return ['div', {\n      style: 'line-height:1.25em;margin-bottom:0.6em'\n    }, ['div', {\n      style: 'color:#476582'\n    }, type], ['div', {\n      style: 'padding-left:1.25em'\n    }].concat(_toConsumableArray(Object.keys(target).map(function (key) {\n      return ['div', {}, ['span', keywordStyle, key + ': '], formatValue(target[key], false)];\n    })))];\n  }\n  function formatValue(v) {\n    var asRaw = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;\n    if (typeof v === 'number') {\n      return ['span', numberStyle, v];\n    } else if (typeof v === 'string') {\n      return ['span', stringStyle, JSON.stringify(v)];\n    } else if (typeof v === 'boolean') {\n      return ['span', keywordStyle, v];\n    } else if (isObject(v)) {\n      return ['object', {\n        object: asRaw ? toRaw(v) : v\n      }];\n    } else {\n      return ['span', stringStyle, String(v)];\n    }\n  }\n  function extractKeys(instance, type) {\n    var Comp = instance.type;\n    if (isFunction(Comp)) {\n      return;\n    }\n    var extracted = {};\n    for (var key in instance.ctx) {\n      if (isKeyOfType(Comp, key, type)) {\n        extracted[key] = instance.ctx[key];\n      }\n    }\n    return extracted;\n  }\n  function isKeyOfType(Comp, key, type) {\n    var opts = Comp[type];\n    if (isArray(opts) && opts.includes(key) || isObject(opts) && key in opts) {\n      return true;\n    }\n    if (Comp.extends && isKeyOfType(Comp.extends, key, type)) {\n      return true;\n    }\n    if (Comp.mixins && Comp.mixins.some(function (m) {\n      return isKeyOfType(m, key, type);\n    })) {\n      return true;\n    }\n  }\n  function genRefFlag(v) {\n    if (v._shallow) {\n      return \"ShallowRef\";\n    }\n    if (v.effect) {\n      return \"ComputedRef\";\n    }\n    return \"Ref\";\n  }\n  if (window.devtoolsFormatters) {\n    window.devtoolsFormatters.push(formatter);\n  } else {\n    window.devtoolsFormatters = [formatter];\n  }\n}\nfunction withMemo(memo, render, cache, index) {\n  var cached = cache[index];\n  if (cached && isMemoSame(cached, memo)) {\n    return cached;\n  }\n  var ret = render();\n  // shallow clone\n  ret.memo = memo.slice();\n  return cache[index] = ret;\n}\nfunction isMemoSame(cached, memo) {\n  var prev = cached.memo;\n  if (prev.length != memo.length) {\n    return false;\n  }\n  for (var i = 0; i < prev.length; i++) {\n    if (prev[i] !== memo[i]) {\n      return false;\n    }\n  }\n  // make sure to let parent block track it when returning cached\n  if (isBlockTreeEnabled > 0 && currentBlock) {\n    currentBlock.push(cached);\n  }\n  return true;\n}\n\n// Core API ------------------------------------------------------------------\nvar version = \"3.2.26\";\nvar _ssrUtils = {\n  createComponentInstance: createComponentInstance,\n  setupComponent: setupComponent,\n  renderComponentRoot: renderComponentRoot,\n  setCurrentRenderingInstance: setCurrentRenderingInstance,\n  isVNode: isVNode,\n  normalizeVNode: normalizeVNode\n};\n/**\r\n * SSR utils for \\@vue/server-renderer. Only exposed in cjs builds.\r\n * @internal\r\n */\nvar ssrUtils = _ssrUtils;\n/**\r\n * @internal only exposed in compat builds\r\n */\nvar resolveFilter = null;\n/**\r\n * @internal only exposed in compat builds.\r\n */\nvar compatUtils = null;\nexport { BaseTransition, Comment, Fragment, KeepAlive, Static, Suspense, Teleport, Text, callWithAsyncErrorHandling, callWithErrorHandling, cloneVNode, compatUtils, createBlock, createCommentVNode, createElementBlock, createBaseVNode as createElementVNode, createHydrationRenderer, createPropsRestProxy, createRenderer, createSlots, createStaticVNode, createTextVNode, createVNode, defineAsyncComponent, defineComponent, defineEmits, defineExpose, defineProps, devtools, getCurrentInstance, getTransitionRawChildren, guardReactiveProps, h, handleError, initCustomFormatter, inject, isMemoSame, isRuntimeOnly, isVNode, mergeDefaults, mergeProps, nextTick, onActivated, onBeforeMount, onBeforeUnmount, onBeforeUpdate, onDeactivated, onErrorCaptured, onMounted, onRenderTracked, onRenderTriggered, onServerPrefetch, onUnmounted, onUpdated, openBlock, popScopeId, provide, pushScopeId, queuePostFlushCb, registerRuntimeCompiler, renderList, renderSlot, resolveComponent, resolveDirective, resolveDynamicComponent, resolveFilter, resolveTransitionHooks, setBlockTracking, setDevtoolsHook, setTransitionHooks, ssrContextKey, ssrUtils, toHandlers, transformVNodeArgs, useAttrs, useSSRContext, useSlots, useTransitionState, version, warn, watch, watchEffect, watchPostEffect, watchSyncEffect, withAsyncContext, withCtx, withDefaults, withDirectives, withMemo, withScopeId };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}