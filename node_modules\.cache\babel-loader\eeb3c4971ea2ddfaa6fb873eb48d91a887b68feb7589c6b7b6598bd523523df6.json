{"ast": null, "code": "function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nfunction _iterableToArrayLimit(arr, i) { var _i = null == arr ? null : \"undefined\" != typeof Symbol && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (null != _i) { var _s, _e, _x, _r, _arr = [], _n = !0, _d = !1; try { if (_x = (_i = _i.call(arr)).next, 0 === i) { if (Object(_i) !== _i) return; _n = !1; } else for (; !(_n = (_s = _x.call(_i)).done) && (_arr.push(_s.value), _arr.length !== i); _n = !0); } catch (err) { _d = !0, _e = err; } finally { try { if (!_n && null != _i.return && (_r = _i.return(), Object(_r) !== _r)) return; } finally { if (_d) throw _e; } } return _arr; } }\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\nimport { createVNode as _createVNode } from \"vue\";\nimport { computed, defineComponent } from \"vue\";\nimport { addUnit, truthProp, numericProp, createNamespace } from \"../utils/index.mjs\";\nvar _createNamespace = createNamespace(\"progress\"),\n  _createNamespace2 = _slicedToArray(_createNamespace, 2),\n  name = _createNamespace2[0],\n  bem = _createNamespace2[1];\nvar progressProps = {\n  color: String,\n  inactive: Boolean,\n  pivotText: String,\n  textColor: String,\n  showPivot: truthProp,\n  pivotColor: String,\n  trackColor: String,\n  strokeWidth: numericProp,\n  percentage: {\n    type: numericProp,\n    default: 0,\n    validator: function validator(value) {\n      return value >= 0 && value <= 100;\n    }\n  }\n};\nvar stdin_default = defineComponent({\n  name: name,\n  props: progressProps,\n  setup: function setup(props) {\n    var background = computed(function () {\n      return props.inactive ? void 0 : props.color;\n    });\n    var renderPivot = function renderPivot() {\n      var textColor = props.textColor,\n        pivotText = props.pivotText,\n        pivotColor = props.pivotColor,\n        percentage = props.percentage;\n      var text = pivotText != null ? pivotText : \"\".concat(percentage, \"%\");\n      if (props.showPivot && text) {\n        var style = {\n          color: textColor,\n          left: \"\".concat(+percentage, \"%\"),\n          transform: \"translate(-\".concat(+percentage, \"%,-50%)\"),\n          background: pivotColor || background.value\n        };\n        return _createVNode(\"span\", {\n          \"style\": style,\n          \"class\": bem(\"pivot\", {\n            inactive: props.inactive\n          })\n        }, [text]);\n      }\n    };\n    return function () {\n      var trackColor = props.trackColor,\n        percentage = props.percentage,\n        strokeWidth = props.strokeWidth;\n      var rootStyle = {\n        background: trackColor,\n        height: addUnit(strokeWidth)\n      };\n      var portionStyle = {\n        width: \"\".concat(percentage, \"%\"),\n        background: background.value\n      };\n      return _createVNode(\"div\", {\n        \"class\": bem(),\n        \"style\": rootStyle\n      }, [_createVNode(\"span\", {\n        \"class\": bem(\"portion\", {\n          inactive: props.inactive\n        }),\n        \"style\": portionStyle\n      }, null), renderPivot()]);\n    };\n  }\n});\nexport { stdin_default as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}