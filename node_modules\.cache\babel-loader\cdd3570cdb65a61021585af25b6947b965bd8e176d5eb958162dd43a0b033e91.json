{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, pushScopeId as _pushScopeId, popScopeId as _popScopeId } from \"vue\";\nvar _withScopeId = function _withScopeId(n) {\n  return _pushScopeId(\"data-v-4323ade8\"), n = n(), _popScopeId(), n;\n};\nvar _hoisted_1 = {\n  class: \"home_box\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_router_view = _resolveComponent(\"router-view\");\n  var _component_footer_demo = _resolveComponent(\"footer-demo\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_router_view, {\n    class: \"pore\",\n    onHideFooter: $setup.hideFooter\n  }, null, 8, [\"onHideFooter\"]), _createVNode(_component_footer_demo)]);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}