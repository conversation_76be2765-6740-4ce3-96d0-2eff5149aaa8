{"ast": null, "code": "function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nfunction _iterableToArrayLimit(arr, i) { var _i = null == arr ? null : \"undefined\" != typeof Symbol && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (null != _i) { var _s, _e, _x, _r, _arr = [], _n = !0, _d = !1; try { if (_x = (_i = _i.call(arr)).next, 0 === i) { if (Object(_i) !== _i) return; _n = !1; } else for (; !(_n = (_s = _x.call(_i)).done) && (_arr.push(_s.value), _arr.length !== i); _n = !0); } catch (err) { _d = !0, _e = err; } finally { try { if (!_n && null != _i.return && (_r = _i.return(), Object(_r) !== _r)) return; } finally { if (_d) throw _e; } } return _arr; } }\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\nimport { createVNode as _createVNode } from \"vue\";\nimport { computed, defineComponent } from \"vue\";\nimport { isDef, truthProp, numericProp, createNamespace } from \"../utils/index.mjs\";\nimport { Badge } from \"../badge/index.mjs\";\nvar _createNamespace = createNamespace(\"tab\"),\n  _createNamespace2 = _slicedToArray(_createNamespace, 2),\n  name = _createNamespace2[0],\n  bem = _createNamespace2[1];\nvar stdin_default = defineComponent({\n  name: name,\n  props: {\n    id: String,\n    dot: Boolean,\n    type: String,\n    color: String,\n    title: String,\n    badge: numericProp,\n    shrink: Boolean,\n    isActive: Boolean,\n    disabled: Boolean,\n    controls: String,\n    scrollable: Boolean,\n    activeColor: String,\n    inactiveColor: String,\n    showZeroBadge: truthProp\n  },\n  setup: function setup(props, _ref) {\n    var slots = _ref.slots;\n    var style = computed(function () {\n      var style2 = {};\n      var type = props.type,\n        color = props.color,\n        disabled = props.disabled,\n        isActive = props.isActive,\n        activeColor = props.activeColor,\n        inactiveColor = props.inactiveColor;\n      var isCard = type === \"card\";\n      if (color && isCard) {\n        style2.borderColor = color;\n        if (!disabled) {\n          if (isActive) {\n            style2.backgroundColor = color;\n          } else {\n            style2.color = color;\n          }\n        }\n      }\n      var titleColor = isActive ? activeColor : inactiveColor;\n      if (titleColor) {\n        style2.color = titleColor;\n      }\n      return style2;\n    });\n    var renderText = function renderText() {\n      var Text = _createVNode(\"span\", {\n        \"class\": bem(\"text\", {\n          ellipsis: !props.scrollable\n        })\n      }, [slots.title ? slots.title() : props.title]);\n      if (props.dot || isDef(props.badge) && props.badge !== \"\") {\n        return _createVNode(Badge, {\n          \"dot\": props.dot,\n          \"content\": props.badge,\n          \"showZero\": props.showZeroBadge\n        }, {\n          default: function _default() {\n            return [Text];\n          }\n        });\n      }\n      return Text;\n    };\n    return function () {\n      return _createVNode(\"div\", {\n        \"id\": props.id,\n        \"role\": \"tab\",\n        \"class\": [bem([props.type, {\n          grow: props.scrollable && !props.shrink,\n          shrink: props.shrink,\n          active: props.isActive,\n          disabled: props.disabled\n        }])],\n        \"style\": style.value,\n        \"tabindex\": props.disabled ? void 0 : props.isActive ? 0 : -1,\n        \"aria-selected\": props.isActive,\n        \"aria-disabled\": props.disabled || void 0,\n        \"aria-controls\": props.controls\n      }, [renderText()]);\n    };\n  }\n});\nexport { stdin_default as default };", "map": {"version": 3, "names": ["createVNode", "_createVNode", "computed", "defineComponent", "isDef", "truthProp", "numericProp", "createNamespace", "Badge", "_createNamespace", "_createNamespace2", "_slicedToArray", "name", "bem", "stdin_default", "props", "id", "String", "dot", "Boolean", "type", "color", "title", "badge", "shrink", "isActive", "disabled", "controls", "scrollable", "activeColor", "inactiveColor", "showZeroBadge", "setup", "_ref", "slots", "style", "style2", "isCard", "borderColor", "backgroundColor", "titleColor", "renderText", "Text", "ellipsis", "default", "_default", "grow", "active", "value"], "sources": ["C:/Users/<USER>/Downloads/Telegram Desktop/vue3_3.0/vue3_3.0/node_modules/vant/es/tabs/TabsTitle.mjs"], "sourcesContent": ["import { createVNode as _createVNode } from \"vue\";\nimport { computed, defineComponent } from \"vue\";\nimport { isDef, truthProp, numericProp, createNamespace } from \"../utils/index.mjs\";\nimport { Badge } from \"../badge/index.mjs\";\nconst [name, bem] = createNamespace(\"tab\");\nvar stdin_default = defineComponent({\n  name,\n  props: {\n    id: String,\n    dot: Boolean,\n    type: String,\n    color: String,\n    title: String,\n    badge: numericProp,\n    shrink: Boolean,\n    isActive: Boolean,\n    disabled: <PERSON>olean,\n    controls: String,\n    scrollable: Boolean,\n    activeColor: String,\n    inactiveColor: String,\n    showZeroBadge: truthProp\n  },\n  setup(props, {\n    slots\n  }) {\n    const style = computed(() => {\n      const style2 = {};\n      const {\n        type,\n        color,\n        disabled,\n        isActive,\n        activeColor,\n        inactiveColor\n      } = props;\n      const isCard = type === \"card\";\n      if (color && isCard) {\n        style2.borderColor = color;\n        if (!disabled) {\n          if (isActive) {\n            style2.backgroundColor = color;\n          } else {\n            style2.color = color;\n          }\n        }\n      }\n      const titleColor = isActive ? activeColor : inactiveColor;\n      if (titleColor) {\n        style2.color = titleColor;\n      }\n      return style2;\n    });\n    const renderText = () => {\n      const Text = _createVNode(\"span\", {\n        \"class\": bem(\"text\", {\n          ellipsis: !props.scrollable\n        })\n      }, [slots.title ? slots.title() : props.title]);\n      if (props.dot || isDef(props.badge) && props.badge !== \"\") {\n        return _createVNode(Badge, {\n          \"dot\": props.dot,\n          \"content\": props.badge,\n          \"showZero\": props.showZeroBadge\n        }, {\n          default: () => [Text]\n        });\n      }\n      return Text;\n    };\n    return () => _createVNode(\"div\", {\n      \"id\": props.id,\n      \"role\": \"tab\",\n      \"class\": [bem([props.type, {\n        grow: props.scrollable && !props.shrink,\n        shrink: props.shrink,\n        active: props.isActive,\n        disabled: props.disabled\n      }])],\n      \"style\": style.value,\n      \"tabindex\": props.disabled ? void 0 : props.isActive ? 0 : -1,\n      \"aria-selected\": props.isActive,\n      \"aria-disabled\": props.disabled || void 0,\n      \"aria-controls\": props.controls\n    }, [renderText()]);\n  }\n});\nexport {\n  stdin_default as default\n};\n"], "mappings": ";;;;;;AAAA,SAASA,WAAW,IAAIC,YAAY,QAAQ,KAAK;AACjD,SAASC,QAAQ,EAAEC,eAAe,QAAQ,KAAK;AAC/C,SAASC,KAAK,EAAEC,SAAS,EAAEC,WAAW,EAAEC,eAAe,QAAQ,oBAAoB;AACnF,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,IAAAC,gBAAA,GAAoBF,eAAe,CAAC,KAAK,CAAC;EAAAG,iBAAA,GAAAC,cAAA,CAAAF,gBAAA;EAAnCG,IAAI,GAAAF,iBAAA;EAAEG,GAAG,GAAAH,iBAAA;AAChB,IAAII,aAAa,GAAGX,eAAe,CAAC;EAClCS,IAAI,EAAJA,IAAI;EACJG,KAAK,EAAE;IACLC,EAAE,EAAEC,MAAM;IACVC,GAAG,EAAEC,OAAO;IACZC,IAAI,EAAEH,MAAM;IACZI,KAAK,EAAEJ,MAAM;IACbK,KAAK,EAAEL,MAAM;IACbM,KAAK,EAAEjB,WAAW;IAClBkB,MAAM,EAAEL,OAAO;IACfM,QAAQ,EAAEN,OAAO;IACjBO,QAAQ,EAAEP,OAAO;IACjBQ,QAAQ,EAAEV,MAAM;IAChBW,UAAU,EAAET,OAAO;IACnBU,WAAW,EAAEZ,MAAM;IACnBa,aAAa,EAAEb,MAAM;IACrBc,aAAa,EAAE1B;EACjB,CAAC;EACD2B,KAAK,WAAAA,MAACjB,KAAK,EAAAkB,IAAA,EAER;IAAA,IADDC,KAAK,GAAAD,IAAA,CAALC,KAAK;IAEL,IAAMC,KAAK,GAAGjC,QAAQ,CAAC,YAAM;MAC3B,IAAMkC,MAAM,GAAG,CAAC,CAAC;MACjB,IACEhB,IAAI,GAMFL,KAAK,CANPK,IAAI;QACJC,KAAK,GAKHN,KAAK,CALPM,KAAK;QACLK,QAAQ,GAINX,KAAK,CAJPW,QAAQ;QACRD,QAAQ,GAGNV,KAAK,CAHPU,QAAQ;QACRI,WAAW,GAETd,KAAK,CAFPc,WAAW;QACXC,aAAa,GACXf,KAAK,CADPe,aAAa;MAEf,IAAMO,MAAM,GAAGjB,IAAI,KAAK,MAAM;MAC9B,IAAIC,KAAK,IAAIgB,MAAM,EAAE;QACnBD,MAAM,CAACE,WAAW,GAAGjB,KAAK;QAC1B,IAAI,CAACK,QAAQ,EAAE;UACb,IAAID,QAAQ,EAAE;YACZW,MAAM,CAACG,eAAe,GAAGlB,KAAK;UAChC,CAAC,MAAM;YACLe,MAAM,CAACf,KAAK,GAAGA,KAAK;UACtB;QACF;MACF;MACA,IAAMmB,UAAU,GAAGf,QAAQ,GAAGI,WAAW,GAAGC,aAAa;MACzD,IAAIU,UAAU,EAAE;QACdJ,MAAM,CAACf,KAAK,GAAGmB,UAAU;MAC3B;MACA,OAAOJ,MAAM;IACf,CAAC,CAAC;IACF,IAAMK,UAAU,GAAG,SAAbA,UAAUA,CAAA,EAAS;MACvB,IAAMC,IAAI,GAAGzC,YAAY,CAAC,MAAM,EAAE;QAChC,OAAO,EAAEY,GAAG,CAAC,MAAM,EAAE;UACnB8B,QAAQ,EAAE,CAAC5B,KAAK,CAACa;QACnB,CAAC;MACH,CAAC,EAAE,CAACM,KAAK,CAACZ,KAAK,GAAGY,KAAK,CAACZ,KAAK,CAAC,CAAC,GAAGP,KAAK,CAACO,KAAK,CAAC,CAAC;MAC/C,IAAIP,KAAK,CAACG,GAAG,IAAId,KAAK,CAACW,KAAK,CAACQ,KAAK,CAAC,IAAIR,KAAK,CAACQ,KAAK,KAAK,EAAE,EAAE;QACzD,OAAOtB,YAAY,CAACO,KAAK,EAAE;UACzB,KAAK,EAAEO,KAAK,CAACG,GAAG;UAChB,SAAS,EAAEH,KAAK,CAACQ,KAAK;UACtB,UAAU,EAAER,KAAK,CAACgB;QACpB,CAAC,EAAE;UACDa,OAAO,EAAE,SAAAC,SAAA;YAAA,OAAM,CAACH,IAAI,CAAC;UAAA;QACvB,CAAC,CAAC;MACJ;MACA,OAAOA,IAAI;IACb,CAAC;IACD,OAAO;MAAA,OAAMzC,YAAY,CAAC,KAAK,EAAE;QAC/B,IAAI,EAAEc,KAAK,CAACC,EAAE;QACd,MAAM,EAAE,KAAK;QACb,OAAO,EAAE,CAACH,GAAG,CAAC,CAACE,KAAK,CAACK,IAAI,EAAE;UACzB0B,IAAI,EAAE/B,KAAK,CAACa,UAAU,IAAI,CAACb,KAAK,CAACS,MAAM;UACvCA,MAAM,EAAET,KAAK,CAACS,MAAM;UACpBuB,MAAM,EAAEhC,KAAK,CAACU,QAAQ;UACtBC,QAAQ,EAAEX,KAAK,CAACW;QAClB,CAAC,CAAC,CAAC,CAAC;QACJ,OAAO,EAAES,KAAK,CAACa,KAAK;QACpB,UAAU,EAAEjC,KAAK,CAACW,QAAQ,GAAG,KAAK,CAAC,GAAGX,KAAK,CAACU,QAAQ,GAAG,CAAC,GAAG,CAAC,CAAC;QAC7D,eAAe,EAAEV,KAAK,CAACU,QAAQ;QAC/B,eAAe,EAAEV,KAAK,CAACW,QAAQ,IAAI,KAAK,CAAC;QACzC,eAAe,EAAEX,KAAK,CAACY;MACzB,CAAC,EAAE,CAACc,UAAU,CAAC,CAAC,CAAC,CAAC;IAAA;EACpB;AACF,CAAC,CAAC;AACF,SACE3B,aAAa,IAAI8B,OAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}