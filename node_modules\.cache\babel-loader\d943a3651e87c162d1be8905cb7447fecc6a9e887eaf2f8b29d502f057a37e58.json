{"ast": null, "code": "import { ref, reactive, watch } from 'vue';\nimport store from '@/store/index';\nimport { useRouter } from 'vue-router';\nexport default {\n  setup: function setup() {\n    var _useRouter = useRouter(),\n      push = _useRouter.push;\n    var fooCheck = ref(store.state.fooCheck);\n    var list = reactive([{\n      img: require('@/assets/images/footer/home1.svg'),\n      img_check: require('@/assets/images/footer/home2.svg'),\n      key: 'home',\n      name: 'home'\n    }, {\n      img: require('@/assets/images/footer/pages1.svg'),\n      img_check: require('@/assets/images/footer/pages2.svg'),\n      key: 'order',\n      name: 'order'\n    }, {\n      img: require('@/assets/images/footer/test2.svg'),\n      img_check: require('@/assets/images/footer/test2.svg'),\n      key: 'obj',\n      name: 'jiaoyi'\n    }, {\n      img: require('@/assets/images/footer/team1.svg'),\n      img_check: require('@/assets/images/footer/team2.svg'),\n      key: 'team',\n      name: 'team'\n    }, {\n      img: require('@/assets/images/footer/user1.svg'),\n      img_check: require('@/assets/images/footer/user2.svg'),\n      key: 'self',\n      name: 'self'\n    }]);\n    var checkList = function checkList(key) {\n      fooCheck.value = key;\n      store.dispatch('changefooCheck', fooCheck.value);\n    };\n    watch(fooCheck, function (newValue) {\n      push('/' + newValue);\n    });\n    return {\n      list: list,\n      fooCheck: fooCheck,\n      checkList: checkList\n    };\n  }\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}