{"ast": null, "code": "import { ActionBar } from \"./action-bar/index.mjs\";\nimport { ActionBarButton } from \"./action-bar-button/index.mjs\";\nimport { ActionBarIcon } from \"./action-bar-icon/index.mjs\";\nimport { ActionSheet } from \"./action-sheet/index.mjs\";\nimport { AddressEdit } from \"./address-edit/index.mjs\";\nimport { AddressList } from \"./address-list/index.mjs\";\nimport { Area } from \"./area/index.mjs\";\nimport { Badge } from \"./badge/index.mjs\";\nimport { Button } from \"./button/index.mjs\";\nimport { Calendar } from \"./calendar/index.mjs\";\nimport { Card } from \"./card/index.mjs\";\nimport { Cascader } from \"./cascader/index.mjs\";\nimport { Cell } from \"./cell/index.mjs\";\nimport { CellGroup } from \"./cell-group/index.mjs\";\nimport { Checkbox } from \"./checkbox/index.mjs\";\nimport { CheckboxGroup } from \"./checkbox-group/index.mjs\";\nimport { Circle } from \"./circle/index.mjs\";\nimport { Col } from \"./col/index.mjs\";\nimport { Collapse } from \"./collapse/index.mjs\";\nimport { CollapseItem } from \"./collapse-item/index.mjs\";\nimport { ConfigProvider } from \"./config-provider/index.mjs\";\nimport { ContactCard } from \"./contact-card/index.mjs\";\nimport { ContactEdit } from \"./contact-edit/index.mjs\";\nimport { ContactList } from \"./contact-list/index.mjs\";\nimport { CountDown } from \"./count-down/index.mjs\";\nimport { Coupon } from \"./coupon/index.mjs\";\nimport { CouponCell } from \"./coupon-cell/index.mjs\";\nimport { CouponList } from \"./coupon-list/index.mjs\";\nimport { DatetimePicker } from \"./datetime-picker/index.mjs\";\nimport { Dialog } from \"./dialog/index.mjs\";\nimport { Divider } from \"./divider/index.mjs\";\nimport { DropdownItem } from \"./dropdown-item/index.mjs\";\nimport { DropdownMenu } from \"./dropdown-menu/index.mjs\";\nimport { Empty } from \"./empty/index.mjs\";\nimport { Field } from \"./field/index.mjs\";\nimport { Form } from \"./form/index.mjs\";\nimport { Grid } from \"./grid/index.mjs\";\nimport { GridItem } from \"./grid-item/index.mjs\";\nimport { Icon } from \"./icon/index.mjs\";\nimport { Image } from \"./image/index.mjs\";\nimport { ImagePreview } from \"./image-preview/index.mjs\";\nimport { IndexAnchor } from \"./index-anchor/index.mjs\";\nimport { IndexBar } from \"./index-bar/index.mjs\";\nimport { List } from \"./list/index.mjs\";\nimport { Loading } from \"./loading/index.mjs\";\nimport { Locale } from \"./locale/index.mjs\";\nimport { NavBar } from \"./nav-bar/index.mjs\";\nimport { NoticeBar } from \"./notice-bar/index.mjs\";\nimport { Notify } from \"./notify/index.mjs\";\nimport { NumberKeyboard } from \"./number-keyboard/index.mjs\";\nimport { Overlay } from \"./overlay/index.mjs\";\nimport { Pagination } from \"./pagination/index.mjs\";\nimport { PasswordInput } from \"./password-input/index.mjs\";\nimport { Picker } from \"./picker/index.mjs\";\nimport { Popover } from \"./popover/index.mjs\";\nimport { Popup } from \"./popup/index.mjs\";\nimport { Progress } from \"./progress/index.mjs\";\nimport { PullRefresh } from \"./pull-refresh/index.mjs\";\nimport { Radio } from \"./radio/index.mjs\";\nimport { RadioGroup } from \"./radio-group/index.mjs\";\nimport { Rate } from \"./rate/index.mjs\";\nimport { Row } from \"./row/index.mjs\";\nimport { Search } from \"./search/index.mjs\";\nimport { ShareSheet } from \"./share-sheet/index.mjs\";\nimport { Sidebar } from \"./sidebar/index.mjs\";\nimport { SidebarItem } from \"./sidebar-item/index.mjs\";\nimport { Skeleton } from \"./skeleton/index.mjs\";\nimport { Slider } from \"./slider/index.mjs\";\nimport { Space } from \"./space/index.mjs\";\nimport { Step } from \"./step/index.mjs\";\nimport { Stepper } from \"./stepper/index.mjs\";\nimport { Steps } from \"./steps/index.mjs\";\nimport { Sticky } from \"./sticky/index.mjs\";\nimport { SubmitBar } from \"./submit-bar/index.mjs\";\nimport { Swipe } from \"./swipe/index.mjs\";\nimport { SwipeCell } from \"./swipe-cell/index.mjs\";\nimport { SwipeItem } from \"./swipe-item/index.mjs\";\nimport { Switch } from \"./switch/index.mjs\";\nimport { Tab } from \"./tab/index.mjs\";\nimport { Tabbar } from \"./tabbar/index.mjs\";\nimport { TabbarItem } from \"./tabbar-item/index.mjs\";\nimport { Tabs } from \"./tabs/index.mjs\";\nimport { Tag } from \"./tag/index.mjs\";\nimport { Toast } from \"./toast/index.mjs\";\nimport { TreeSelect } from \"./tree-select/index.mjs\";\nimport { Uploader } from \"./uploader/index.mjs\";\nvar version = \"3.6.11\";\nfunction install(app) {\n  var components = [ActionBar, ActionBarButton, ActionBarIcon, ActionSheet, AddressEdit, AddressList, Area, Badge, Button, Calendar, Card, Cascader, Cell, CellGroup, Checkbox, CheckboxGroup, Circle, Col, Collapse, CollapseItem, ConfigProvider, ContactCard, ContactEdit, ContactList, CountDown, Coupon, CouponCell, CouponList, DatetimePicker, Dialog, Divider, DropdownItem, DropdownMenu, Empty, Field, Form, Grid, GridItem, Icon, Image, ImagePreview, IndexAnchor, IndexBar, List, Loading, Locale, NavBar, NoticeBar, Notify, NumberKeyboard, Overlay, Pagination, PasswordInput, Picker, Popover, Popup, Progress, PullRefresh, Radio, RadioGroup, Rate, Row, Search, ShareSheet, Sidebar, SidebarItem, Skeleton, Slider, Space, Step, Stepper, Steps, Sticky, SubmitBar, Swipe, SwipeCell, SwipeItem, Switch, Tab, Tabbar, TabbarItem, Tabs, Tag, Toast, TreeSelect, Uploader];\n  components.forEach(function (item) {\n    if (item.install) {\n      app.use(item);\n    } else if (item.name) {\n      app.component(item.name, item);\n    }\n  });\n}\nexport * from \"./action-bar/index.mjs\";\nexport * from \"./action-bar-button/index.mjs\";\nexport * from \"./action-bar-icon/index.mjs\";\nexport * from \"./action-sheet/index.mjs\";\nexport * from \"./address-edit/index.mjs\";\nexport * from \"./address-list/index.mjs\";\nexport * from \"./area/index.mjs\";\nexport * from \"./badge/index.mjs\";\nexport * from \"./button/index.mjs\";\nexport * from \"./calendar/index.mjs\";\nexport * from \"./card/index.mjs\";\nexport * from \"./cascader/index.mjs\";\nexport * from \"./cell/index.mjs\";\nexport * from \"./cell-group/index.mjs\";\nexport * from \"./checkbox/index.mjs\";\nexport * from \"./checkbox-group/index.mjs\";\nexport * from \"./circle/index.mjs\";\nexport * from \"./col/index.mjs\";\nexport * from \"./collapse/index.mjs\";\nexport * from \"./collapse-item/index.mjs\";\nexport * from \"./config-provider/index.mjs\";\nexport * from \"./contact-card/index.mjs\";\nexport * from \"./contact-edit/index.mjs\";\nexport * from \"./contact-list/index.mjs\";\nexport * from \"./count-down/index.mjs\";\nexport * from \"./coupon/index.mjs\";\nexport * from \"./coupon-cell/index.mjs\";\nexport * from \"./coupon-list/index.mjs\";\nexport * from \"./datetime-picker/index.mjs\";\nexport * from \"./dialog/index.mjs\";\nexport * from \"./divider/index.mjs\";\nexport * from \"./dropdown-item/index.mjs\";\nexport * from \"./dropdown-menu/index.mjs\";\nexport * from \"./empty/index.mjs\";\nexport * from \"./field/index.mjs\";\nexport * from \"./form/index.mjs\";\nexport * from \"./grid/index.mjs\";\nexport * from \"./grid-item/index.mjs\";\nexport * from \"./icon/index.mjs\";\nexport * from \"./image/index.mjs\";\nexport * from \"./image-preview/index.mjs\";\nexport * from \"./index-anchor/index.mjs\";\nexport * from \"./index-bar/index.mjs\";\nexport * from \"./lazyload/index.mjs\";\nexport * from \"./list/index.mjs\";\nexport * from \"./loading/index.mjs\";\nexport * from \"./locale/index.mjs\";\nexport * from \"./nav-bar/index.mjs\";\nexport * from \"./notice-bar/index.mjs\";\nexport * from \"./notify/index.mjs\";\nexport * from \"./number-keyboard/index.mjs\";\nexport * from \"./overlay/index.mjs\";\nexport * from \"./pagination/index.mjs\";\nexport * from \"./password-input/index.mjs\";\nexport * from \"./picker/index.mjs\";\nexport * from \"./popover/index.mjs\";\nexport * from \"./popup/index.mjs\";\nexport * from \"./progress/index.mjs\";\nexport * from \"./pull-refresh/index.mjs\";\nexport * from \"./radio/index.mjs\";\nexport * from \"./radio-group/index.mjs\";\nexport * from \"./rate/index.mjs\";\nexport * from \"./row/index.mjs\";\nexport * from \"./search/index.mjs\";\nexport * from \"./share-sheet/index.mjs\";\nexport * from \"./sidebar/index.mjs\";\nexport * from \"./sidebar-item/index.mjs\";\nexport * from \"./skeleton/index.mjs\";\nexport * from \"./slider/index.mjs\";\nexport * from \"./space/index.mjs\";\nexport * from \"./step/index.mjs\";\nexport * from \"./stepper/index.mjs\";\nexport * from \"./steps/index.mjs\";\nexport * from \"./sticky/index.mjs\";\nexport * from \"./submit-bar/index.mjs\";\nexport * from \"./swipe/index.mjs\";\nexport * from \"./swipe-cell/index.mjs\";\nexport * from \"./swipe-item/index.mjs\";\nexport * from \"./switch/index.mjs\";\nexport * from \"./tab/index.mjs\";\nexport * from \"./tabbar/index.mjs\";\nexport * from \"./tabbar-item/index.mjs\";\nexport * from \"./tabs/index.mjs\";\nexport * from \"./tag/index.mjs\";\nexport * from \"./toast/index.mjs\";\nexport * from \"./tree-select/index.mjs\";\nexport * from \"./uploader/index.mjs\";\nvar stdin_default = {\n  install: install,\n  version: version\n};\nexport { stdin_default as default, install, version };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}