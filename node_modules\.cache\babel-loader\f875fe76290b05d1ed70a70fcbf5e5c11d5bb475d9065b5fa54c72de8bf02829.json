{"ast": null, "code": "import { defineComponent, ref, computed, watch, nextTick, onBeforeMount, onMounted, createVNode, Fragment, getCurrentInstance } from 'vue';\n\n/* eslint-disable no-undefined,no-param-reassign,no-shadow */\n\n/**\n * Throttle execution of a function. Especially useful for rate limiting\n * execution of handlers on events like resize and scroll.\n *\n * @param  {number}    delay -          A zero-or-greater delay in milliseconds. For event callbacks, values around 100 or 250 (or even higher) are most useful.\n * @param  {boolean}   [noTrailing] -   Optional, defaults to false. If noTrailing is true, callback will only execute every `delay` milliseconds while the\n *                                    throttled-function is being called. If noTrailing is false or unspecified, callback will be executed one final time\n *                                    after the last throttled-function call. (After the throttled-function has not been called for `delay` milliseconds,\n *                                    the internal counter is reset).\n * @param  {Function}  callback -       A function to be executed after delay milliseconds. The `this` context and all arguments are passed through, as-is,\n *                                    to `callback` when the throttled-function is executed.\n * @param  {boolean}   [debounceMode] - If `debounceMode` is true (at begin), schedule `clear` to execute after `delay` ms. If `debounceMode` is false (at end),\n *                                    schedule `callback` to execute after `delay` ms.\n *\n * @returns {Function}  A new, throttled, function.\n */\nfunction throttle(delay, noTrailing, callback, debounceMode) {\n  /*\n   * After wrapper has stopped being called, this timeout ensures that\n   * `callback` is executed at the proper times in `throttle` and `end`\n   * debounce modes.\n   */\n  var timeoutID;\n  var cancelled = false; // Keep track of the last time `callback` was executed.\n\n  var lastExec = 0; // Function to clear existing timeout\n\n  function clearExistingTimeout() {\n    if (timeoutID) {\n      clearTimeout(timeoutID);\n    }\n  } // Function to cancel next exec\n\n  function cancel() {\n    clearExistingTimeout();\n    cancelled = true;\n  } // `noTrailing` defaults to falsy.\n\n  if (typeof noTrailing !== 'boolean') {\n    debounceMode = callback;\n    callback = noTrailing;\n    noTrailing = undefined;\n  }\n  /*\n   * The `wrapper` function encapsulates all of the throttling / debouncing\n   * functionality and when executed will limit the rate at which `callback`\n   * is executed.\n   */\n\n  function wrapper() {\n    for (var _len = arguments.length, arguments_ = new Array(_len), _key = 0; _key < _len; _key++) {\n      arguments_[_key] = arguments[_key];\n    }\n    var self = this;\n    var elapsed = Date.now() - lastExec;\n    if (cancelled) {\n      return;\n    } // Execute `callback` and update the `lastExec` timestamp.\n\n    function exec() {\n      lastExec = Date.now();\n      callback.apply(self, arguments_);\n    }\n    /*\n     * If `debounceMode` is true (at begin) this is used to clear the flag\n     * to allow future `callback` executions.\n     */\n\n    function clear() {\n      timeoutID = undefined;\n    }\n    if (debounceMode && !timeoutID) {\n      /*\n       * Since `wrapper` is being called for the first time and\n       * `debounceMode` is true (at begin), execute `callback`.\n       */\n      exec();\n    }\n    clearExistingTimeout();\n    if (debounceMode === undefined && elapsed > delay) {\n      /*\n       * In throttle mode, if `delay` time has been exceeded, execute\n       * `callback`.\n       */\n      exec();\n    } else if (noTrailing !== true) {\n      /*\n       * In trailing throttle mode, since `delay` time has not been\n       * exceeded, schedule `callback` to execute `delay` ms after most\n       * recent execution.\n       *\n       * If `debounceMode` is true (at begin), schedule `clear` to execute\n       * after `delay` ms.\n       *\n       * If `debounceMode` is false (at end), schedule `callback` to\n       * execute after `delay` ms.\n       */\n      timeoutID = setTimeout(debounceMode ? clear : exec, debounceMode === undefined ? delay - elapsed : delay);\n    }\n  }\n  wrapper.cancel = cancel; // Return the wrapper function.\n\n  return wrapper;\n}\nfunction useExpose(apis) {\n  var instance = getCurrentInstance();\n  if (instance) {\n    Object.assign(instance.proxy, apis);\n  }\n}\nvar Props = {\n  // 是否开启自动滚动\n  modelValue: {\n    type: Boolean,\n    default: true\n  },\n  // 原始数据列表\n  list: {\n    type: Array,\n    required: true,\n    default: []\n  },\n  // 步进速度，step 需是单步大小的约数\n  step: {\n    type: Number,\n    default: 1\n  },\n  // 开启滚动的数据量\n  limitScrollNum: {\n    type: Number,\n    default: 3\n  },\n  // 是否开启鼠标悬停\n  hover: {\n    type: Boolean,\n    default: false\n  },\n  // 控制滚动方向\n  direction: {\n    type: String,\n    default: \"up\"\n  },\n  // 单步运动停止的高度\n  singleHeight: {\n    type: Number,\n    default: 0\n  },\n  // 单步运动停止的宽度\n  singleWidth: {\n    type: Number,\n    default: 0\n  },\n  // 单步停止等待时间 (默认值 1000ms)\n  singleWaitTime: {\n    type: Number,\n    default: 1000\n  },\n  // 是否开启 rem 度量\n  isRemUnit: {\n    type: Boolean,\n    default: false\n  },\n  // 开启数据更新监听\n  isWatch: {\n    type: Boolean,\n    default: true\n  },\n  // 动画时间\n  delay: {\n    type: Number,\n    default: 0\n  },\n  // 动画方式\n  ease: {\n    type: [String, Object],\n    default: \"ease-in\"\n  },\n  // 动画循环次数，-1 表示一直动画\n  count: {\n    type: Number,\n    default: -1\n  },\n  // 拷贝几份滚动列表\n  copyNum: {\n    type: Number,\n    default: 1\n  },\n  // 开启鼠标悬停时支持滚轮滚动\n  wheel: {\n    type: Boolean,\n    default: false\n  },\n  // 启用单行滚动\n  singleLine: {\n    type: Boolean,\n    default: false\n  }\n};\nglobalThis.window.cancelAnimationFrame = function () {\n  return globalThis.window.cancelAnimationFrame ||\n  // @ts-ignore\n  globalThis.window.webkitCancelAnimationFrame ||\n  // @ts-ignore\n  globalThis.window.mozCancelAnimationFrame ||\n  // @ts-ignore\n  globalThis.window.oCancelAnimationFrame ||\n  // @ts-ignore\n  globalThis.window.msCancelAnimationFrame || function (id) {\n    return globalThis.window.clearTimeout(id);\n  };\n}();\nglobalThis.window.requestAnimationFrame = function () {\n  return globalThis.window.requestAnimationFrame ||\n  // @ts-ignore\n  globalThis.window.webkitRequestAnimationFrame ||\n  // @ts-ignore\n  globalThis.window.mozRequestAnimationFrame ||\n  // @ts-ignore\n  globalThis.window.oRequestAnimationFrame ||\n  // @ts-ignore\n  globalThis.window.msRequestAnimationFrame || function (callback) {\n    return globalThis.window.setTimeout(callback, 1000 / 60);\n  };\n}();\nfunction dataWarm(list) {\n  if (list && typeof list !== \"boolean\" && list.length > 100) {}\n}\nvar Vue3SeamlessScroll = defineComponent({\n  name: \"vue3-seamless-scroll\",\n  inheritAttrs: false,\n  props: Props,\n  emits: [\"stop\", \"count\", \"move\"],\n  setup: function setup(_props, _ref) {\n    var slots = _ref.slots,\n      emit = _ref.emit,\n      attrs = _ref.attrs;\n    var props = _props;\n    var scrollRef = ref(null);\n    var slotListRef = ref(null);\n    var realBoxRef = ref(null);\n    var reqFrame = ref(null);\n    var singleWaitTimeout = ref(null);\n    var realBoxWidth = ref(0);\n    var realBoxHeight = ref(0);\n    var xPos = ref(0);\n    var yPos = ref(0);\n    var isHover = ref(false);\n    var _count = ref(0);\n    var isScroll = computed(function () {\n      return props.list ? props.list.length >= props.limitScrollNum : false;\n    });\n    var realBoxStyle = computed(function () {\n      return {\n        width: realBoxWidth.value ? \"\".concat(realBoxWidth.value, \"px\") : \"auto\",\n        transform: \"translate(\".concat(xPos.value, \"px,\").concat(yPos.value, \"px)\"),\n        // @ts-ignore\n        transition: \"all \".concat(typeof props.ease === \"string\" ? props.ease : \"cubic-bezier(\" + props.ease.x1 + \",\" + props.ease.y1 + \",\" + props.ease.x2 + \",\" + props.ease.y2 + \")\", \" \").concat(props.delay, \"ms\"),\n        overflow: \"hidden\",\n        display: props.singleLine ? \"flex\" : \"block\"\n      };\n    });\n    var isHorizontal = computed(function () {\n      return props.direction == \"left\" || props.direction == \"right\";\n    });\n    var floatStyle = computed(function () {\n      return isHorizontal.value ? {\n        float: \"left\",\n        overflow: \"hidden\",\n        display: props.singleLine ? \"flex\" : \"block\",\n        flexShrink: props.singleLine ? 0 : 1\n      } : {\n        overflow: \"hidden\"\n      };\n    });\n    var baseFontSize = computed(function () {\n      return props.isRemUnit ? parseInt(globalThis.window.getComputedStyle(globalThis.document.documentElement, null).fontSize) : 1;\n    });\n    var realSingleStopWidth = computed(function () {\n      return props.singleWidth * baseFontSize.value;\n    });\n    var realSingleStopHeight = computed(function () {\n      return props.singleHeight * baseFontSize.value;\n    });\n    var step = computed(function () {\n      var singleStep;\n      var _step = props.step;\n      if (isHorizontal.value) {\n        singleStep = realSingleStopWidth.value;\n      } else {\n        singleStep = realSingleStopHeight.value;\n      }\n      if (singleStep > 0 && singleStep % _step > 0) {}\n      return _step;\n    });\n    var cancle = function cancle() {\n      cancelAnimationFrame(reqFrame.value);\n      reqFrame.value = null;\n    };\n    var animation = function animation(_direction, _step, isWheel) {\n      reqFrame.value = requestAnimationFrame(function () {\n        var h = realBoxHeight.value / 2;\n        var w = realBoxWidth.value / 2;\n        if (_direction === \"up\") {\n          if (Math.abs(yPos.value) >= h) {\n            yPos.value = 0;\n            _count.value += 1;\n            emit(\"count\", _count.value);\n          }\n          yPos.value -= _step;\n        } else if (_direction === \"down\") {\n          if (yPos.value >= 0) {\n            yPos.value = h * -1;\n            _count.value += 1;\n            emit(\"count\", _count.value);\n          }\n          yPos.value += _step;\n        } else if (_direction === \"left\") {\n          if (Math.abs(xPos.value) >= w) {\n            xPos.value = 0;\n            _count.value += 1;\n            emit(\"count\", _count.value);\n          }\n          xPos.value -= _step;\n        } else if (_direction === \"right\") {\n          if (xPos.value >= 0) {\n            xPos.value = w * -1;\n            _count.value += 1;\n            emit(\"count\", _count.value);\n          }\n          xPos.value += _step;\n        }\n        if (isWheel) {\n          return;\n        }\n        var singleWaitTime = props.singleWaitTime;\n        if (singleWaitTimeout.value) {\n          clearTimeout(singleWaitTimeout.value);\n        }\n        if (!!realSingleStopHeight.value) {\n          if (Math.abs(yPos.value) % realSingleStopHeight.value < _step) {\n            singleWaitTimeout.value = setTimeout(function () {\n              move();\n            }, singleWaitTime);\n          } else {\n            move();\n          }\n        } else if (!!realSingleStopWidth.value) {\n          if (Math.abs(xPos.value) % realSingleStopWidth.value < _step) {\n            singleWaitTimeout.value = setTimeout(function () {\n              move();\n            }, singleWaitTime);\n          } else {\n            move();\n          }\n        } else {\n          move();\n        }\n      });\n    };\n    var move = function move() {\n      cancle();\n      if (isHover.value || !isScroll.value || _count.value === props.count) {\n        emit(\"stop\", _count.value);\n        _count.value = 0;\n        return;\n      }\n      animation(props.direction, step.value, false);\n    };\n    var initMove = function initMove() {\n      dataWarm(props.list);\n      if (isHorizontal.value) {\n        var slotListWidth = slotListRef.value.offsetWidth;\n        slotListWidth = slotListWidth * 2 + 1;\n        realBoxWidth.value = slotListWidth;\n      }\n      if (isScroll.value) {\n        realBoxHeight.value = realBoxRef.value.offsetHeight;\n        if (props.modelValue) {\n          move();\n        }\n      } else {\n        cancle();\n        yPos.value = xPos.value = 0;\n      }\n    };\n    var startMove = function startMove() {\n      isHover.value = false;\n      move();\n    };\n    var stopMove = function stopMove() {\n      isHover.value = true;\n      if (singleWaitTimeout.value) {\n        clearTimeout(singleWaitTimeout.value);\n      }\n      cancle();\n    };\n    var hoverStop = computed(function () {\n      return props.hover && props.modelValue && isScroll.value;\n    });\n    var throttleFunc = throttle(30, function (e) {\n      cancle();\n      var singleHeight = !!realSingleStopHeight.value ? realSingleStopHeight.value : 15;\n      if (e.deltaY < 0) {\n        animation(\"down\", singleHeight, true);\n      }\n      if (e.deltaY > 0) {\n        animation(\"up\", singleHeight, true);\n      }\n    });\n    var _onWheel = function onWheel(e) {\n      throttleFunc(e);\n    };\n    var reset = function reset() {\n      cancle();\n      isHover.value = false;\n      initMove();\n    };\n    var Reset = function Reset() {\n      reset();\n    };\n    useExpose({\n      Reset: Reset\n    });\n    watch(function () {\n      return props.list;\n    }, function () {\n      if (props.isWatch) {\n        nextTick(function () {\n          reset();\n        });\n      }\n    }, {\n      deep: true\n    });\n    watch(function () {\n      return props.modelValue;\n    }, function (newValue) {\n      if (newValue) {\n        startMove();\n      } else {\n        stopMove();\n      }\n    });\n    watch(function () {\n      return props.count;\n    }, function (newValue) {\n      if (newValue !== 0) {\n        startMove();\n      }\n    });\n    onBeforeMount(function () {\n      cancle();\n      clearTimeout(singleWaitTimeout.value);\n    });\n    onMounted(function () {\n      if (isScroll.value) {\n        initMove();\n      }\n    });\n    var $default = slots.default,\n      html = slots.html;\n    var copyNum = new Array(props.copyNum).fill(null);\n    var getHtml = function getHtml() {\n      return createVNode(Fragment, null, [createVNode(\"div\", {\n        \"ref\": slotListRef,\n        \"style\": floatStyle.value\n      }, [$default && $default()]), isScroll.value ? copyNum.map(function () {\n        if (html && typeof html === \"function\") {\n          return createVNode(\"div\", {\n            \"style\": floatStyle.value\n          }, [html()]);\n        } else {\n          return createVNode(\"div\", {\n            \"style\": floatStyle.value\n          }, [$default && $default()]);\n        }\n      }) : null]);\n    };\n    return function () {\n      return createVNode(\"div\", {\n        \"ref\": scrollRef,\n        \"class\": attrs.class\n      }, [props.wheel && props.hover ? createVNode(\"div\", {\n        \"ref\": realBoxRef,\n        \"style\": realBoxStyle.value,\n        \"onMouseenter\": function onMouseenter() {\n          if (hoverStop.value) {\n            stopMove();\n          }\n        },\n        \"onMouseleave\": function onMouseleave() {\n          if (hoverStop.value) {\n            startMove();\n          }\n        },\n        \"onWheel\": function onWheel(e) {\n          if (hoverStop.value) {\n            _onWheel(e);\n          }\n        }\n      }, [getHtml()]) : createVNode(\"div\", {\n        \"ref\": realBoxRef,\n        \"style\": realBoxStyle.value,\n        \"onMouseenter\": function onMouseenter() {\n          if (hoverStop.value) {\n            stopMove();\n          }\n        },\n        \"onMouseleave\": function onMouseleave() {\n          if (hoverStop.value) {\n            startMove();\n          }\n        }\n      }, [getHtml()])]);\n    };\n  }\n});\nvar install = function install(app) {\n  var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  app.component(options.name || Vue3SeamlessScroll.name, Vue3SeamlessScroll);\n};\nfunction index(app) {\n  app.use(install);\n}\nexport { Vue3SeamlessScroll, index as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}