{"ast": null, "code": "function _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return _typeof(key) === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (_typeof(input) !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (_typeof(res) !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\nimport { createStore } from 'vuex';\nvar lang = localStorage.getItem('lang') || '';\nvar langImg = localStorage.getItem('langImg') || '';\nvar fooCheck = localStorage.getItem('fooCheck') || 'home';\n// 公用参数\nvar baseInfo = localStorage.getItem('baseInfo');\n// 个人信息\nvar userinfo = localStorage.getItem('userinfo');\n// token\nvar token = localStorage.getItem('token');\n// 记住账号\nvar user = localStorage.getItem('user');\n// 系统弹窗时间\nvar xtTime = localStorage.getItem('xtTime');\n// 抢单界面数据\nvar objInfo = localStorage.getItem('objInfo');\nexport default createStore({\n  state: {\n    lang: lang,\n    langImg: langImg,\n    langList: [{\n      label: 'lang.zh',\n      value: 'zh_cn',\n      img: require('@/assets/images/register/zh.png')\n    }, {\n      label: 'lang.en',\n      value: 'en_es',\n      img: require('@/assets/images/register/en.png')\n    }],\n    fooCheck: fooCheck,\n    token: token,\n    xtTime: xtTime,\n    objInfo: objInfo ? JSON.parse(objInfo) : {},\n    baseInfo: baseInfo ? JSON.parse(baseInfo) : {},\n    userinfo: userinfo ? JSON.parse(userinfo) : {},\n    user: user ? JSON.parse(user) : {}\n  },\n  getters: {},\n  mutations: {\n    settoken: function settoken(state, value) {\n      state.token = value;\n      localStorage.setItem('token', value);\n    },\n    setxtTime: function setxtTime(state, value) {\n      state.xtTime = value;\n      localStorage.setItem('xtTime', value);\n    },\n    setlang: function setlang(state, value) {\n      state.lang = value;\n      localStorage.setItem('lang', value);\n    },\n    setlangImg: function setlangImg(state, value) {\n      state.langImg = value;\n      localStorage.setItem('langImg', value);\n    },\n    setfooCheck: function setfooCheck(state, value) {\n      state.fooCheck = value;\n      localStorage.setItem('fooCheck', value);\n    },\n    setobjInfo: function setobjInfo(state, value) {\n      state.objInfo = _objectSpread({}, value);\n      localStorage.setItem('objInfo', JSON.stringify(value));\n    },\n    setbaseInfo: function setbaseInfo(state, value) {\n      state.baseInfo = _objectSpread({}, value);\n      localStorage.setItem('baseInfo', JSON.stringify(value));\n    },\n    setuserinfo: function setuserinfo(state, value) {\n      state.userinfo = _objectSpread({}, value);\n      localStorage.setItem('userinfo', JSON.stringify(value));\n    },\n    setuser: function setuser(state, value) {\n      state.user = _objectSpread({}, value);\n      localStorage.setItem('user', JSON.stringify(value));\n    },\n    deluser: function deluser(state, value) {\n      state.user = {};\n      localStorage.setItem('user', '');\n    }\n  },\n  actions: {\n    changetoken: function changetoken(context, params) {\n      //context是一个对象，从它里面把咱们需要的commit方法解构出来\n      var commit = context.commit;\n      commit('settoken', params);\n    },\n    changextTime: function changextTime(context, params) {\n      //context是一个对象，从它里面把咱们需要的commit方法解构出来\n      var commit = context.commit;\n      commit('setxtTime', params);\n    },\n    changeUser: function changeUser(context, params) {\n      //context是一个对象，从它里面把咱们需要的commit方法解构出来\n      var commit = context.commit;\n      commit('setuser', params);\n    },\n    clearUser: function clearUser(context, params) {\n      //context是一个对象，从它里面把咱们需要的commit方法解构出来\n      var commit = context.commit;\n      commit('deluser', params);\n    },\n    changelang: function changelang(context, params) {\n      //context是一个对象，从它里面把咱们需要的commit方法解构出来\n      var commit = context.commit;\n      commit('setlang', params);\n    },\n    changelangImg: function changelangImg(context, params) {\n      //context是一个对象，从它里面把咱们需要的commit方法解构出来\n      var commit = context.commit;\n      commit('setlangImg', params);\n    },\n    changefooCheck: function changefooCheck(context, params) {\n      //context是一个对象，从它里面把咱们需要的commit方法解构出来\n      var commit = context.commit;\n      commit('setfooCheck', params);\n    },\n    changeobjInfo: function changeobjInfo(context, params) {\n      //context是一个对象，从它里面把咱们需要的commit方法解构出来\n      var commit = context.commit;\n      commit('setobjInfo', params);\n    },\n    changebaseInfo: function changebaseInfo(context, params) {\n      //context是一个对象，从它里面把咱们需要的commit方法解构出来\n      var commit = context.commit;\n      commit('setbaseInfo', params);\n    },\n    changeuserinfo: function changeuserinfo(context, params) {\n      //context是一个对象，从它里面把咱们需要的commit方法解构出来\n      var commit = context.commit;\n      commit('setuserinfo', params);\n    }\n  },\n  modules: {}\n});\n// store.dispatch('changeworkStatus',list)", "map": {"version": 3, "names": ["createStore", "lang", "localStorage", "getItem", "langImg", "foo<PERSON>heck", "baseInfo", "userinfo", "token", "user", "xtTime", "objInfo", "state", "langList", "label", "value", "img", "require", "JSON", "parse", "getters", "mutations", "settoken", "setItem", "setxtTime", "setlang", "setlangImg", "setfoo<PERSON>heck", "setobjInfo", "_objectSpread", "stringify", "setbaseInfo", "setuserinfo", "setuser", "del<PERSON>", "actions", "changetoken", "context", "params", "commit", "changextTime", "changeUser", "clearUser", "changelang", "changelangImg", "changefooCheck", "changeobjInfo", "changebaseInfo", "changeuserinfo", "modules"], "sources": ["C:/Users/<USER>/Desktop/vue3_3.0/src/store/index.js"], "sourcesContent": ["import { createStore } from 'vuex'\r\nconst lang = localStorage.getItem('lang') || ''\r\nconst langImg = localStorage.getItem('langImg') || ''\r\nconst fooCheck = localStorage.getItem('fooCheck') || 'home'\r\n// 公用参数\r\nconst baseInfo = localStorage.getItem('baseInfo')\r\n// 个人信息\r\nconst userinfo = localStorage.getItem('userinfo')\r\n// token\r\nconst token = localStorage.getItem('token')\r\n// 记住账号\r\nconst user = localStorage.getItem('user')\r\n// 系统弹窗时间\r\nconst xtTime = localStorage.getItem('xtTime')\r\n// 抢单界面数据\r\nconst objInfo = localStorage.getItem('objInfo')\r\nexport default createStore({\r\n  state: {\r\n    lang,\r\n    langImg,\r\n    langList:[\r\n      {label: 'lang.zh', value: 'zh_cn', img: require('@/assets/images/register/zh.png')},\r\n      {label: 'lang.en', value: 'en_es', img: require('@/assets/images/register/en.png')},\r\n    ],\r\n    fooCheck,\r\n    token,\r\n    xtTime,\r\n    objInfo: objInfo ? JSON.parse(objInfo) : {},\r\n    baseInfo: baseInfo ? JSON.parse(baseInfo) : {},\r\n    userinfo: userinfo ? JSON.parse(userinfo) : {},\r\n    user: user ? JSON.parse(user) : {},\r\n  },\r\n  getters: {\r\n  },\r\n  mutations: {\r\n    settoken(state,value){\r\n      state.token = value\r\n      localStorage.setItem('token',value)\r\n    },\r\n    setxtTime(state,value){\r\n      state.xtTime = value\r\n      localStorage.setItem('xtTime',value)\r\n    },\r\n    setlang(state,value){\r\n      state.lang = value\r\n      localStorage.setItem('lang',value)\r\n    },\r\n    setlangImg(state,value){\r\n      state.langImg = value\r\n      localStorage.setItem('langImg',value)\r\n    },\r\n    setfooCheck(state,value){\r\n      state.fooCheck = value\r\n      localStorage.setItem('fooCheck',value)\r\n    },\r\n    setobjInfo(state,value){\r\n      state.objInfo = {...value}\r\n      localStorage.setItem('objInfo',JSON.stringify(value))\r\n    },\r\n    setbaseInfo(state,value){\r\n      state.baseInfo = {...value}\r\n      localStorage.setItem('baseInfo',JSON.stringify(value))\r\n    },\r\n    setuserinfo(state,value){\r\n      state.userinfo = {...value}\r\n      localStorage.setItem('userinfo',JSON.stringify(value))\r\n    },\r\n    setuser(state,value){\r\n      state.user = {...value}\r\n      localStorage.setItem('user',JSON.stringify(value))\r\n    },\r\n    deluser(state,value){\r\n      state.user = {}\r\n      localStorage.setItem('user', '')\r\n    },\r\n  },\r\n  actions: {\r\n    changetoken(context,params){  //context是一个对象，从它里面把咱们需要的commit方法解构出来\r\n        let {commit} = context\r\n        commit('settoken',params)\r\n    },\r\n    changextTime(context,params){  //context是一个对象，从它里面把咱们需要的commit方法解构出来\r\n        let {commit} = context\r\n        commit('setxtTime',params)\r\n    },\r\n    changeUser(context,params){  //context是一个对象，从它里面把咱们需要的commit方法解构出来\r\n        let {commit} = context\r\n        commit('setuser',params)\r\n    },\r\n    clearUser(context,params){  //context是一个对象，从它里面把咱们需要的commit方法解构出来\r\n        let {commit} = context\r\n        commit('deluser',params)\r\n    },\r\n    changelang(context,params){  //context是一个对象，从它里面把咱们需要的commit方法解构出来\r\n        let {commit} = context\r\n        commit('setlang',params)\r\n    },\r\n    changelangImg(context,params){  //context是一个对象，从它里面把咱们需要的commit方法解构出来\r\n        let {commit} = context\r\n        commit('setlangImg',params)\r\n    },\r\n    changefooCheck(context,params){  //context是一个对象，从它里面把咱们需要的commit方法解构出来\r\n        let {commit} = context\r\n        commit('setfooCheck',params)\r\n    },\r\n    changeobjInfo(context,params){  //context是一个对象，从它里面把咱们需要的commit方法解构出来\r\n        let {commit} = context\r\n        commit('setobjInfo',params)\r\n    },\r\n    changebaseInfo(context,params){  //context是一个对象，从它里面把咱们需要的commit方法解构出来\r\n        let {commit} = context\r\n        commit('setbaseInfo',params)\r\n    },\r\n    changeuserinfo(context,params){  //context是一个对象，从它里面把咱们需要的commit方法解构出来\r\n        let {commit} = context\r\n        commit('setuserinfo',params)\r\n    },\r\n  },\r\n  modules: {\r\n  }\r\n})\r\n// store.dispatch('changeworkStatus',list)"], "mappings": ";;;;;;AAAA,SAASA,WAAW,QAAQ,MAAM;AAClC,IAAMC,IAAI,GAAGC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,IAAI,EAAE;AAC/C,IAAMC,OAAO,GAAGF,YAAY,CAACC,OAAO,CAAC,SAAS,CAAC,IAAI,EAAE;AACrD,IAAME,QAAQ,GAAGH,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC,IAAI,MAAM;AAC3D;AACA,IAAMG,QAAQ,GAAGJ,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC;AACjD;AACA,IAAMI,QAAQ,GAAGL,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC;AACjD;AACA,IAAMK,KAAK,GAAGN,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;AAC3C;AACA,IAAMM,IAAI,GAAGP,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;AACzC;AACA,IAAMO,MAAM,GAAGR,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC;AAC7C;AACA,IAAMQ,OAAO,GAAGT,YAAY,CAACC,OAAO,CAAC,SAAS,CAAC;AAC/C,eAAeH,WAAW,CAAC;EACzBY,KAAK,EAAE;IACLX,IAAI,EAAJA,IAAI;IACJG,OAAO,EAAPA,OAAO;IACPS,QAAQ,EAAC,CACP;MAACC,KAAK,EAAE,SAAS;MAAEC,KAAK,EAAE,OAAO;MAAEC,GAAG,EAAEC,OAAO,CAAC,iCAAiC;IAAC,CAAC,EACnF;MAACH,KAAK,EAAE,SAAS;MAAEC,KAAK,EAAE,OAAO;MAAEC,GAAG,EAAEC,OAAO,CAAC,iCAAiC;IAAC,CAAC,CACpF;IACDZ,QAAQ,EAARA,QAAQ;IACRG,KAAK,EAALA,KAAK;IACLE,MAAM,EAANA,MAAM;IACNC,OAAO,EAAEA,OAAO,GAAGO,IAAI,CAACC,KAAK,CAACR,OAAO,CAAC,GAAG,CAAC,CAAC;IAC3CL,QAAQ,EAAEA,QAAQ,GAAGY,IAAI,CAACC,KAAK,CAACb,QAAQ,CAAC,GAAG,CAAC,CAAC;IAC9CC,QAAQ,EAAEA,QAAQ,GAAGW,IAAI,CAACC,KAAK,CAACZ,QAAQ,CAAC,GAAG,CAAC,CAAC;IAC9CE,IAAI,EAAEA,IAAI,GAAGS,IAAI,CAACC,KAAK,CAACV,IAAI,CAAC,GAAG,CAAC;EACnC,CAAC;EACDW,OAAO,EAAE,CACT,CAAC;EACDC,SAAS,EAAE;IACTC,QAAQ,WAAAA,SAACV,KAAK,EAACG,KAAK,EAAC;MACnBH,KAAK,CAACJ,KAAK,GAAGO,KAAK;MACnBb,YAAY,CAACqB,OAAO,CAAC,OAAO,EAACR,KAAK,CAAC;IACrC,CAAC;IACDS,SAAS,WAAAA,UAACZ,KAAK,EAACG,KAAK,EAAC;MACpBH,KAAK,CAACF,MAAM,GAAGK,KAAK;MACpBb,YAAY,CAACqB,OAAO,CAAC,QAAQ,EAACR,KAAK,CAAC;IACtC,CAAC;IACDU,OAAO,WAAAA,QAACb,KAAK,EAACG,KAAK,EAAC;MAClBH,KAAK,CAACX,IAAI,GAAGc,KAAK;MAClBb,YAAY,CAACqB,OAAO,CAAC,MAAM,EAACR,KAAK,CAAC;IACpC,CAAC;IACDW,UAAU,WAAAA,WAACd,KAAK,EAACG,KAAK,EAAC;MACrBH,KAAK,CAACR,OAAO,GAAGW,KAAK;MACrBb,YAAY,CAACqB,OAAO,CAAC,SAAS,EAACR,KAAK,CAAC;IACvC,CAAC;IACDY,WAAW,WAAAA,YAACf,KAAK,EAACG,KAAK,EAAC;MACtBH,KAAK,CAACP,QAAQ,GAAGU,KAAK;MACtBb,YAAY,CAACqB,OAAO,CAAC,UAAU,EAACR,KAAK,CAAC;IACxC,CAAC;IACDa,UAAU,WAAAA,WAAChB,KAAK,EAACG,KAAK,EAAC;MACrBH,KAAK,CAACD,OAAO,GAAAkB,aAAA,KAAOd,KAAK,CAAC;MAC1Bb,YAAY,CAACqB,OAAO,CAAC,SAAS,EAACL,IAAI,CAACY,SAAS,CAACf,KAAK,CAAC,CAAC;IACvD,CAAC;IACDgB,WAAW,WAAAA,YAACnB,KAAK,EAACG,KAAK,EAAC;MACtBH,KAAK,CAACN,QAAQ,GAAAuB,aAAA,KAAOd,KAAK,CAAC;MAC3Bb,YAAY,CAACqB,OAAO,CAAC,UAAU,EAACL,IAAI,CAACY,SAAS,CAACf,KAAK,CAAC,CAAC;IACxD,CAAC;IACDiB,WAAW,WAAAA,YAACpB,KAAK,EAACG,KAAK,EAAC;MACtBH,KAAK,CAACL,QAAQ,GAAAsB,aAAA,KAAOd,KAAK,CAAC;MAC3Bb,YAAY,CAACqB,OAAO,CAAC,UAAU,EAACL,IAAI,CAACY,SAAS,CAACf,KAAK,CAAC,CAAC;IACxD,CAAC;IACDkB,OAAO,WAAAA,QAACrB,KAAK,EAACG,KAAK,EAAC;MAClBH,KAAK,CAACH,IAAI,GAAAoB,aAAA,KAAOd,KAAK,CAAC;MACvBb,YAAY,CAACqB,OAAO,CAAC,MAAM,EAACL,IAAI,CAACY,SAAS,CAACf,KAAK,CAAC,CAAC;IACpD,CAAC;IACDmB,OAAO,WAAAA,QAACtB,KAAK,EAACG,KAAK,EAAC;MAClBH,KAAK,CAACH,IAAI,GAAG,CAAC,CAAC;MACfP,YAAY,CAACqB,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC;IAClC;EACF,CAAC;EACDY,OAAO,EAAE;IACPC,WAAW,WAAAA,YAACC,OAAO,EAACC,MAAM,EAAC;MAAG;MAC1B,IAAKC,MAAM,GAAIF,OAAO,CAAjBE,MAAM;MACXA,MAAM,CAAC,UAAU,EAACD,MAAM,CAAC;IAC7B,CAAC;IACDE,YAAY,WAAAA,aAACH,OAAO,EAACC,MAAM,EAAC;MAAG;MAC3B,IAAKC,MAAM,GAAIF,OAAO,CAAjBE,MAAM;MACXA,MAAM,CAAC,WAAW,EAACD,MAAM,CAAC;IAC9B,CAAC;IACDG,UAAU,WAAAA,WAACJ,OAAO,EAACC,MAAM,EAAC;MAAG;MACzB,IAAKC,MAAM,GAAIF,OAAO,CAAjBE,MAAM;MACXA,MAAM,CAAC,SAAS,EAACD,MAAM,CAAC;IAC5B,CAAC;IACDI,SAAS,WAAAA,UAACL,OAAO,EAACC,MAAM,EAAC;MAAG;MACxB,IAAKC,MAAM,GAAIF,OAAO,CAAjBE,MAAM;MACXA,MAAM,CAAC,SAAS,EAACD,MAAM,CAAC;IAC5B,CAAC;IACDK,UAAU,WAAAA,WAACN,OAAO,EAACC,MAAM,EAAC;MAAG;MACzB,IAAKC,MAAM,GAAIF,OAAO,CAAjBE,MAAM;MACXA,MAAM,CAAC,SAAS,EAACD,MAAM,CAAC;IAC5B,CAAC;IACDM,aAAa,WAAAA,cAACP,OAAO,EAACC,MAAM,EAAC;MAAG;MAC5B,IAAKC,MAAM,GAAIF,OAAO,CAAjBE,MAAM;MACXA,MAAM,CAAC,YAAY,EAACD,MAAM,CAAC;IAC/B,CAAC;IACDO,cAAc,WAAAA,eAACR,OAAO,EAACC,MAAM,EAAC;MAAG;MAC7B,IAAKC,MAAM,GAAIF,OAAO,CAAjBE,MAAM;MACXA,MAAM,CAAC,aAAa,EAACD,MAAM,CAAC;IAChC,CAAC;IACDQ,aAAa,WAAAA,cAACT,OAAO,EAACC,MAAM,EAAC;MAAG;MAC5B,IAAKC,MAAM,GAAIF,OAAO,CAAjBE,MAAM;MACXA,MAAM,CAAC,YAAY,EAACD,MAAM,CAAC;IAC/B,CAAC;IACDS,cAAc,WAAAA,eAACV,OAAO,EAACC,MAAM,EAAC;MAAG;MAC7B,IAAKC,MAAM,GAAIF,OAAO,CAAjBE,MAAM;MACXA,MAAM,CAAC,aAAa,EAACD,MAAM,CAAC;IAChC,CAAC;IACDU,cAAc,WAAAA,eAACX,OAAO,EAACC,MAAM,EAAC;MAAG;MAC7B,IAAKC,MAAM,GAAIF,OAAO,CAAjBE,MAAM;MACXA,MAAM,CAAC,aAAa,EAACD,MAAM,CAAC;IAChC;EACF,CAAC;EACDW,OAAO,EAAE,CACT;AACF,CAAC,CAAC;AACF"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}