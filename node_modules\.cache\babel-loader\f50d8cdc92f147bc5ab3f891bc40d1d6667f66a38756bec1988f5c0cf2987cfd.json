{"ast": null, "code": "function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nfunction _iterableToArrayLimit(arr, i) { var _i = null == arr ? null : \"undefined\" != typeof Symbol && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (null != _i) { var _s, _e, _x, _r, _arr = [], _n = !0, _d = !1; try { if (_x = (_i = _i.call(arr)).next, 0 === i) { if (Object(_i) !== _i) return; _n = !1; } else for (; !(_n = (_s = _x.call(_i)).done) && (_arr.push(_s.value), _arr.length !== i); _n = !0); } catch (err) { _d = !0, _e = err; } finally { try { if (!_n && null != _i.return && (_r = _i.return(), Object(_r) !== _r)) return; } finally { if (_d) throw _e; } } return _arr; } }\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\nimport { createVNode as _createVNode } from \"vue\";\nimport { ref, watch, reactive, nextTick, defineComponent } from \"vue\";\nimport { numericProp, getScrollTop, preventDefault, createNamespace, makeNumericProp } from \"../utils/index.mjs\";\nimport { useEventListener, useScrollParent } from \"@vant/use\";\nimport { useTouch } from \"../composables/use-touch.mjs\";\nimport { Loading } from \"../loading/index.mjs\";\nvar _createNamespace = createNamespace(\"pull-refresh\"),\n  _createNamespace2 = _slicedToArray(_createNamespace, 3),\n  name = _createNamespace2[0],\n  bem = _createNamespace2[1],\n  t = _createNamespace2[2];\nvar DEFAULT_HEAD_HEIGHT = 50;\nvar TEXT_STATUS = [\"pulling\", \"loosing\", \"success\"];\nvar pullRefreshProps = {\n  disabled: Boolean,\n  modelValue: Boolean,\n  headHeight: makeNumericProp(DEFAULT_HEAD_HEIGHT),\n  successText: String,\n  pullingText: String,\n  loosingText: String,\n  loadingText: String,\n  pullDistance: numericProp,\n  successDuration: makeNumericProp(500),\n  animationDuration: makeNumericProp(300)\n};\nvar stdin_default = defineComponent({\n  name: name,\n  props: pullRefreshProps,\n  emits: [\"change\", \"refresh\", \"update:modelValue\"],\n  setup: function setup(props, _ref) {\n    var emit = _ref.emit,\n      slots = _ref.slots;\n    var reachTop;\n    var root = ref();\n    var track = ref();\n    var scrollParent = useScrollParent(root);\n    var state = reactive({\n      status: \"normal\",\n      distance: 0,\n      duration: 0\n    });\n    var touch = useTouch();\n    var getHeadStyle = function getHeadStyle() {\n      if (props.headHeight !== DEFAULT_HEAD_HEIGHT) {\n        return {\n          height: \"\".concat(props.headHeight, \"px\")\n        };\n      }\n    };\n    var isTouchable = function isTouchable() {\n      return state.status !== \"loading\" && state.status !== \"success\" && !props.disabled;\n    };\n    var ease = function ease(distance) {\n      var pullDistance = +(props.pullDistance || props.headHeight);\n      if (distance > pullDistance) {\n        if (distance < pullDistance * 2) {\n          distance = pullDistance + (distance - pullDistance) / 2;\n        } else {\n          distance = pullDistance * 1.5 + (distance - pullDistance * 2) / 4;\n        }\n      }\n      return Math.round(distance);\n    };\n    var setStatus = function setStatus(distance, isLoading) {\n      var pullDistance = +(props.pullDistance || props.headHeight);\n      state.distance = distance;\n      if (isLoading) {\n        state.status = \"loading\";\n      } else if (distance === 0) {\n        state.status = \"normal\";\n      } else if (distance < pullDistance) {\n        state.status = \"pulling\";\n      } else {\n        state.status = \"loosing\";\n      }\n      emit(\"change\", {\n        status: state.status,\n        distance: distance\n      });\n    };\n    var getStatusText = function getStatusText() {\n      var status = state.status;\n      if (status === \"normal\") {\n        return \"\";\n      }\n      return props[\"\".concat(status, \"Text\")] || t(status);\n    };\n    var renderStatus = function renderStatus() {\n      var status = state.status,\n        distance = state.distance;\n      if (slots[status]) {\n        return slots[status]({\n          distance: distance\n        });\n      }\n      var nodes = [];\n      if (TEXT_STATUS.includes(status)) {\n        nodes.push(_createVNode(\"div\", {\n          \"class\": bem(\"text\")\n        }, [getStatusText()]));\n      }\n      if (status === \"loading\") {\n        nodes.push(_createVNode(Loading, {\n          \"class\": bem(\"loading\")\n        }, {\n          default: getStatusText\n        }));\n      }\n      return nodes;\n    };\n    var showSuccessTip = function showSuccessTip() {\n      state.status = \"success\";\n      setTimeout(function () {\n        setStatus(0);\n      }, +props.successDuration);\n    };\n    var checkPosition = function checkPosition(event) {\n      reachTop = getScrollTop(scrollParent.value) === 0;\n      if (reachTop) {\n        state.duration = 0;\n        touch.start(event);\n      }\n    };\n    var onTouchStart = function onTouchStart(event) {\n      if (isTouchable()) {\n        checkPosition(event);\n      }\n    };\n    var onTouchMove = function onTouchMove(event) {\n      if (isTouchable()) {\n        if (!reachTop) {\n          checkPosition(event);\n        }\n        var deltaY = touch.deltaY;\n        touch.move(event);\n        if (reachTop && deltaY.value >= 0 && touch.isVertical()) {\n          preventDefault(event);\n          setStatus(ease(deltaY.value));\n        }\n      }\n    };\n    var onTouchEnd = function onTouchEnd() {\n      if (reachTop && touch.deltaY.value && isTouchable()) {\n        state.duration = +props.animationDuration;\n        if (state.status === \"loosing\") {\n          setStatus(+props.headHeight, true);\n          emit(\"update:modelValue\", true);\n          nextTick(function () {\n            return emit(\"refresh\");\n          });\n        } else {\n          setStatus(0);\n        }\n      }\n    };\n    watch(function () {\n      return props.modelValue;\n    }, function (value) {\n      state.duration = +props.animationDuration;\n      if (value) {\n        setStatus(+props.headHeight, true);\n      } else if (slots.success || props.successText) {\n        showSuccessTip();\n      } else {\n        setStatus(0, false);\n      }\n    });\n    useEventListener(\"touchmove\", onTouchMove, {\n      target: track\n    });\n    return function () {\n      var _a;\n      var trackStyle = {\n        transitionDuration: \"\".concat(state.duration, \"ms\"),\n        transform: state.distance ? \"translate3d(0,\".concat(state.distance, \"px, 0)\") : \"\"\n      };\n      return _createVNode(\"div\", {\n        \"ref\": root,\n        \"class\": bem()\n      }, [_createVNode(\"div\", {\n        \"ref\": track,\n        \"class\": bem(\"track\"),\n        \"style\": trackStyle,\n        \"onTouchstartPassive\": onTouchStart,\n        \"onTouchend\": onTouchEnd,\n        \"onTouchcancel\": onTouchEnd\n      }, [_createVNode(\"div\", {\n        \"class\": bem(\"head\"),\n        \"style\": getHeadStyle()\n      }, [renderStatus()]), (_a = slots.default) == null ? void 0 : _a.call(slots)])]);\n    };\n  }\n});\nexport { stdin_default as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}