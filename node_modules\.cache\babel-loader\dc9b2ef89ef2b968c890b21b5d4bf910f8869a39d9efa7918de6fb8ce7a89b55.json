{"ast": null, "code": "import { createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, toDisplayString as _toDisplayString, openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, createTextVNode as _createTextVNode, createBlock as _createBlock, pushScopeId as _pushScopeId, popScopeId as _popScopeId } from \"vue\";\nvar _withScopeId = function _withScopeId(n) {\n  return _pushScopeId(\"data-v-7ea7349e\"), n = n(), _popScopeId(), n;\n};\nvar _hoisted_1 = {\n  class: \"home\"\n};\nvar _hoisted_2 = [\"src\"];\nvar _hoisted_3 = {\n  class: \"ktx\"\n};\nvar _hoisted_4 = {\n  class: \"b\"\n};\nvar _hoisted_5 = {\n  class: \"t\"\n};\nvar _hoisted_6 = {\n  class: \"check_money\"\n};\nvar _hoisted_7 = {\n  key: 0,\n  class: \"text\"\n};\nvar _hoisted_8 = {\n  class: \"withdraw_title\"\n};\nvar _hoisted_9 = {\n  key: 1,\n  class: \"text\"\n};\nvar _hoisted_10 = {\n  key: 2,\n  class: \"text\"\n};\nvar _hoisted_11 = {\n  key: 0,\n  class: \"account_info\"\n};\nvar _hoisted_12 = {\n  class: \"info_item\"\n};\nvar _hoisted_13 = {\n  class: \"label\"\n};\nvar _hoisted_14 = {\n  class: \"value\"\n};\nvar _hoisted_15 = {\n  class: \"info_item\"\n};\nvar _hoisted_16 = {\n  class: \"label\"\n};\nvar _hoisted_17 = {\n  class: \"value\"\n};\nvar _hoisted_18 = {\n  key: 1,\n  class: \"account_info\"\n};\nvar _hoisted_19 = {\n  class: \"info_item\"\n};\nvar _hoisted_20 = {\n  class: \"label\"\n};\nvar _hoisted_21 = {\n  class: \"value\"\n};\nvar _hoisted_22 = {\n  class: \"info_item\"\n};\nvar _hoisted_23 = {\n  class: \"label\"\n};\nvar _hoisted_24 = {\n  class: \"value\"\n};\nvar _hoisted_25 = {\n  key: 2,\n  class: \"tixian_money\"\n};\nvar _hoisted_26 = {\n  key: 0,\n  class: \"buttons\"\n};\nvar _hoisted_27 = [\"innerHTML\"];\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_van_nav_bar = _resolveComponent(\"van-nav-bar\");\n  var _component_van_radio = _resolveComponent(\"van-radio\");\n  var _component_van_radio_group = _resolveComponent(\"van-radio-group\");\n  var _component_van_empty = _resolveComponent(\"van-empty\");\n  var _component_van_button = _resolveComponent(\"van-button\");\n  var _component_van_field = _resolveComponent(\"van-field\");\n  var _component_van_cell_group = _resolveComponent(\"van-cell-group\");\n  var _component_van_form = _resolveComponent(\"van-form\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_van_nav_bar, {\n    title: _ctx.$t('msg.tikuan'),\n    \"left-arrow\": \"\",\n    onClickLeft: _cache[0] || (_cache[0] = function ($event) {\n      return _ctx.$router.go(-1);\n    }),\n    onClickRight: $setup.clickRight\n  }, {\n    right: _withCtx(function () {\n      return [_createElementVNode(\"img\", {\n        src: require('@/assets/images/self/hank/tel.png'),\n        class: \"img\",\n        alt: \"\"\n      }, null, 8 /* PROPS */, _hoisted_2)];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"title\", \"onClickRight\"]), _createVNode(_component_van_form, {\n    onSubmit: $setup.onSubmit\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_van_cell_group, {\n        inset: \"\"\n      }, {\n        default: _withCtx(function () {\n          return [_createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"div\", _hoisted_4, _toDisplayString($setup.currency) + \" \" + _toDisplayString($setup.money), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_5, _toDisplayString(_ctx.$t('msg.my_yu_e')), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_6, [$setup.bankInfoExists || $setup.usdtInfoExists ? (_openBlock(), _createElementBlock(\"div\", _hoisted_7, [_createElementVNode(\"span\", _hoisted_8, _toDisplayString(_ctx.$t('msg.select_withdraw_method')), 1 /* TEXT */)])) : _createCommentVNode(\"v-if\", true), $setup.bankInfoExists || $setup.usdtInfoExists ? (_openBlock(), _createElementBlock(\"div\", _hoisted_9, [_createVNode(_component_van_radio_group, {\n            modelValue: $setup.withdrawType,\n            \"onUpdate:modelValue\": _cache[1] || (_cache[1] = function ($event) {\n              return $setup.withdrawType = $event;\n            }),\n            direction: \"horizontal\"\n          }, {\n            default: _withCtx(function () {\n              return [$setup.bankInfoExists ? (_openBlock(), _createBlock(_component_van_radio, {\n                key: 0,\n                name: \"bank\"\n              }, {\n                default: _withCtx(function () {\n                  return [_createTextVNode(_toDisplayString(_ctx.$t('msg.bank_tx')), 1 /* TEXT */)];\n                }),\n\n                _: 1 /* STABLE */\n              })) : _createCommentVNode(\"v-if\", true), $setup.usdtInfoExists ? (_openBlock(), _createBlock(_component_van_radio, {\n                key: 1,\n                name: \"usdt\"\n              }, {\n                default: _withCtx(function () {\n                  return [_createTextVNode(_toDisplayString(_ctx.$t('msg.usdt_tx')), 1 /* TEXT */)];\n                }),\n\n                _: 1 /* STABLE */\n              })) : _createCommentVNode(\"v-if\", true)];\n            }),\n            _: 1 /* STABLE */\n          }, 8 /* PROPS */, [\"modelValue\"])])) : _createCommentVNode(\"v-if\", true), !$setup.bankInfoExists && !$setup.usdtInfoExists ? (_openBlock(), _createElementBlock(\"div\", _hoisted_10, [_createVNode(_component_van_empty, {\n            description: _ctx.$t('msg.no_withdraw_method')\n          }, null, 8 /* PROPS */, [\"description\"]), _createVNode(_component_van_button, {\n            round: \"\",\n            block: \"\",\n            type: \"primary\",\n            onClick: $setup.goToBingBank,\n            style: {\n              \"margin-top\": \"20px\"\n            }\n          }, {\n            default: _withCtx(function () {\n              return [_createTextVNode(_toDisplayString(_ctx.$t('msg.go_bind_account')), 1 /* TEXT */)];\n            }),\n\n            _: 1 /* STABLE */\n          }, 8 /* PROPS */, [\"onClick\"])])) : _createCommentVNode(\"v-if\", true)]), _createCommentVNode(\" 银行卡信息展示 \"), $setup.withdrawType === 'bank' && $setup.bankInfoExists ? (_openBlock(), _createElementBlock(\"div\", _hoisted_11, [_createElementVNode(\"div\", _hoisted_12, [_createElementVNode(\"span\", _hoisted_13, _toDisplayString(_ctx.$t('msg.bank_name')) + \":\", 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_14, _toDisplayString($setup.bankInfo.bankname), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_15, [_createElementVNode(\"span\", _hoisted_16, _toDisplayString(_ctx.$t('msg.yhkh')) + \":\", 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_17, _toDisplayString($setup.bankInfo.cardnum), 1 /* TEXT */)])])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" USDT信息展示 \"), $setup.withdrawType === 'usdt' && $setup.usdtInfoExists ? (_openBlock(), _createElementBlock(\"div\", _hoisted_18, [_createElementVNode(\"div\", _hoisted_19, [_createElementVNode(\"span\", _hoisted_20, _toDisplayString(_ctx.$t('msg.usdt_type')) + \":\", 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_21, _toDisplayString($setup.bankInfo.usdt_type), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_22, [_createElementVNode(\"span\", _hoisted_23, _toDisplayString(_ctx.$t('msg.usdt_address')) + \":\", 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_24, _toDisplayString($setup.bankInfo.usdt_diz), 1 /* TEXT */)])])) : _createCommentVNode(\"v-if\", true), $setup.bankInfoExists || $setup.usdtInfoExists ? (_openBlock(), _createElementBlock(\"div\", _hoisted_25, _toDisplayString(_ctx.$t('msg.tixian_money')), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true), $setup.bankInfoExists || $setup.usdtInfoExists ? (_openBlock(), _createBlock(_component_van_field, {\n            key: 3,\n            class: \"zdy\",\n            modelValue: $setup.money_check,\n            \"onUpdate:modelValue\": _cache[2] || (_cache[2] = function ($event) {\n              return $setup.money_check = $event;\n            }),\n            placeholder: _ctx.$t('msg.tixian_money')\n          }, null, 8 /* PROPS */, [\"modelValue\", \"placeholder\"])) : _createCommentVNode(\"v-if\", true), $setup.bankInfoExists || $setup.usdtInfoExists ? (_openBlock(), _createBlock(_component_van_field, {\n            key: 4,\n            class: \"zdy\",\n            modelValue: $setup.paypassword,\n            \"onUpdate:modelValue\": _cache[3] || (_cache[3] = function ($event) {\n              return $setup.paypassword = $event;\n            }),\n            type: \"password\",\n            name: \"paypassword\",\n            placeholder: _ctx.$t('msg.tx_pwd'),\n            rules: [{\n              required: true,\n              message: _ctx.$t('msg.input_tx_pwd')\n            }]\n          }, null, 8 /* PROPS */, [\"modelValue\", \"placeholder\", \"rules\"])) : _createCommentVNode(\"v-if\", true)];\n        }),\n        _: 1 /* STABLE */\n      }), $setup.bankInfoExists || $setup.usdtInfoExists ? (_openBlock(), _createElementBlock(\"div\", _hoisted_26, [_createVNode(_component_van_button, {\n        round: \"\",\n        block: \"\",\n        type: \"primary\",\n        \"native-type\": \"submit\"\n      }, {\n        default: _withCtx(function () {\n          return [_createTextVNode(_toDisplayString(_ctx.$t('msg.true_tx')), 1 /* TEXT */)];\n        }),\n\n        _: 1 /* STABLE */\n      })])) : _createCommentVNode(\"v-if\", true), $setup.bankInfoExists || $setup.usdtInfoExists ? (_openBlock(), _createElementBlock(\"div\", {\n        key: 1,\n        class: \"text_b\",\n        innerHTML: $setup.content\n      }, null, 8 /* PROPS */, _hoisted_27)) : _createCommentVNode(\"v-if\", true)];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"onSubmit\"])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_van_nav_bar", "title", "_ctx", "$t", "onClickLeft", "_cache", "$event", "$router", "go", "onClickRight", "$setup", "clickRight", "right", "_withCtx", "_createElementVNode", "src", "require", "alt", "_component_van_form", "onSubmit", "_component_van_cell_group", "inset", "_hoisted_3", "_hoisted_4", "_toDisplayString", "currency", "money", "_hoisted_5", "_hoisted_6", "bankInfoExists", "usdtInfoExists", "_hoisted_7", "_hoisted_8", "_hoisted_9", "_component_van_radio_group", "withdrawType", "direction", "_createBlock", "_component_van_radio", "name", "_hoisted_10", "_component_van_empty", "description", "_component_van_button", "round", "block", "type", "onClick", "goToBingBank", "style", "_createCommentVNode", "_hoisted_11", "_hoisted_12", "_hoisted_13", "_hoisted_14", "bankInfo", "bankname", "_hoisted_15", "_hoisted_16", "_hoisted_17", "cardnum", "_hoisted_18", "_hoisted_19", "_hoisted_20", "_hoisted_21", "usdt_type", "_hoisted_22", "_hoisted_23", "_hoisted_24", "usdt_diz", "_hoisted_25", "_component_van_field", "money_check", "placeholder", "paypassword", "rules", "required", "message", "_hoisted_26", "innerHTML", "content"], "sources": ["C:\\Users\\<USER>\\Downloads\\Telegram Desktop\\vue3_3.0\\vue3_3.0\\src\\views\\self\\components\\drawing.vue"], "sourcesContent": ["<template>\r\n  <div class=\"home\">\r\n    <van-nav-bar :title=\"$t('msg.tikuan')\" left-arrow @click-left=\"$router.go(-1)\" @click-right=\"clickRight\">\r\n        <template #right>\r\n            <img :src=\"require('@/assets/images/self/hank/tel.png')\" class=\"img\" alt=\"\">\r\n        </template>\r\n    </van-nav-bar>\r\n    <van-form @submit=\"onSubmit\">\r\n      <van-cell-group inset>\r\n          <div class=\"ktx\">\r\n              <div class=\"b\">{{currency}} {{money}}</div>\r\n              <div class=\"t\">{{$t('msg.my_yu_e')}}</div>\r\n          </div>\r\n          <div class=\"check_money\">\r\n              <div class=\"text\" v-if=\"bankInfoExists || usdtInfoExists\">\r\n                  <span class=\"withdraw_title\">{{ $t('msg.select_withdraw_method') }}</span>\r\n              </div>\r\n              <div class=\"text\" v-if=\"bankInfoExists || usdtInfoExists\">\r\n                  <van-radio-group v-model=\"withdrawType\" direction=\"horizontal\">\r\n                    <van-radio name=\"bank\" v-if=\"bankInfoExists\">{{ $t('msg.bank_tx') }}</van-radio>\r\n                    <van-radio name=\"usdt\" v-if=\"usdtInfoExists\">{{ $t('msg.usdt_tx') }}</van-radio>\r\n                  </van-radio-group>\r\n              </div>\r\n              <div class=\"text\" v-if=\"!bankInfoExists && !usdtInfoExists\">\r\n                  <van-empty :description=\"$t('msg.no_withdraw_method')\" />\r\n                  <van-button round block type=\"primary\" @click=\"goToBingBank\" style=\"margin-top: 20px;\">\r\n                    {{ $t('msg.go_bind_account') }}\r\n                  </van-button>\r\n              </div>\r\n          </div>\r\n          \r\n          <!-- 银行卡信息展示 -->\r\n          <div class=\"account_info\" v-if=\"withdrawType === 'bank' && bankInfoExists\">\r\n            <div class=\"info_item\">\r\n              <span class=\"label\">{{ $t('msg.bank_name') }}:</span>\r\n              <span class=\"value\">{{ bankInfo.bankname }}</span>\r\n            </div>\r\n            <div class=\"info_item\">\r\n              <span class=\"label\">{{ $t('msg.yhkh') }}:</span>\r\n              <span class=\"value\">{{ bankInfo.cardnum }}</span>\r\n            </div>\r\n          </div>\r\n          \r\n          <!-- USDT信息展示 -->\r\n          <div class=\"account_info\" v-if=\"withdrawType === 'usdt' && usdtInfoExists\">\r\n            <div class=\"info_item\">\r\n              <span class=\"label\">{{ $t('msg.usdt_type') }}:</span>\r\n              <span class=\"value\">{{ bankInfo.usdt_type }}</span>\r\n            </div>\r\n            <div class=\"info_item\">\r\n              <span class=\"label\">{{ $t('msg.usdt_address') }}:</span>\r\n              <span class=\"value\">{{ bankInfo.usdt_diz }}</span>\r\n            </div>\r\n          </div>\r\n          \r\n        <div class=\"tixian_money\" v-if=\"bankInfoExists || usdtInfoExists\">{{$t('msg.tixian_money')}}</div>\r\n        <van-field\r\n          class=\"zdy\"\r\n          v-model=\"money_check\"\r\n          :placeholder=\"$t('msg.tixian_money')\"\r\n          v-if=\"bankInfoExists || usdtInfoExists\"\r\n        />\r\n        <van-field\r\n          class=\"zdy\"\r\n          v-model=\"paypassword\"\r\n          type=\"password\"\r\n          name=\"paypassword\"\r\n          :placeholder=\"$t('msg.tx_pwd')\"\r\n          :rules=\"[{ required: true, message: $t('msg.input_tx_pwd') }]\"\r\n          v-if=\"bankInfoExists || usdtInfoExists\"\r\n        />\r\n      </van-cell-group>\r\n      <div class=\"buttons\" v-if=\"bankInfoExists || usdtInfoExists\">\r\n        <van-button round block type=\"primary\" native-type=\"submit\">\r\n          {{$t('msg.true_tx')}}\r\n        </van-button>\r\n      </div>\r\n      <div class=\"text_b\" v-html=\"content\" v-if=\"bankInfoExists || usdtInfoExists\">\r\n      </div>\r\n    </van-form>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { reactive, ref, getCurrentInstance } from 'vue';\r\nimport store from '@/store/index'\r\nimport { do_deposit, bind_bank } from '@/api/self/index.js'\r\nimport { useRouter, useRoute } from 'vue-router';\r\nimport { useI18n } from 'vue-i18n'\r\nimport { getdetailbyid } from '@/api/home/<USER>'\r\nimport { Dialog } from 'vant'\r\nexport default {\r\n  name: 'HomeView',\r\n  setup() {\r\n    const { t } = useI18n()\r\n    const { push } = useRouter();\r\n    const route = useRoute();\r\n    const { proxy } = getCurrentInstance()\r\n    const paypassword = ref('')\r\n    const info = ref({})\r\n    const bankInfo = ref({})\r\n    const currency = ref(store.state.baseInfo?.currency)\r\n    const tel = ref(store.state.userinfo?.tel)\r\n    const infoa = ref(store.state.objInfo)\r\n    const withdrawType = ref('bank') // 默认选择银行卡提现\r\n    const content = ref('')\r\n    const bankInfoExists = ref(false)\r\n    const usdtInfoExists = ref(false)\r\n    \r\n    // 获取用户绑定的银行卡和USDT信息\r\n    bind_bank().then(res => {\r\n        if(res.code === 0) {\r\n            bankInfo.value = res.data.info || {}\r\n            \r\n            // 检查用户是否绑定了银行卡\r\n            bankInfoExists.value = !!(bankInfo.value.bankname && bankInfo.value.cardnum)\r\n            \r\n            // 检查用户是否绑定了USDT钱包\r\n            usdtInfoExists.value = !!(bankInfo.value.usdt_type && bankInfo.value.usdt_diz)\r\n            \r\n            // 如果只有一种提现方式可用，则默认选择该方式\r\n            if (bankInfoExists.value && !usdtInfoExists.value) {\r\n                withdrawType.value = 'bank'\r\n            } else if (!bankInfoExists.value && usdtInfoExists.value) {\r\n                withdrawType.value = 'usdt'\r\n            }\r\n        }\r\n    })\r\n\r\n    getdetailbyid(14).then(res => {\r\n        content.value = res.data?.content\r\n    })\r\n\r\n    const money_check = ref()\r\n    const money = ref(store.state.userinfo?.balance)\r\n    const moneys = ref(store.state.baseInfo?.recharge_money_list)\r\n\r\n    const clickLeft = () => {\r\n        push('/self')\r\n    }\r\n    \r\n    const clickRight = () => {\r\n        push('/tel')\r\n    }\r\n    \r\n    const goToBingBank = () => {\r\n        push('/bingbank')\r\n    }\r\n\r\n    const onSubmit = (values) => {\r\n        if (!bankInfoExists.value && !usdtInfoExists.value) {\r\n            Dialog.confirm({\r\n                confirmButtonText: t('msg.queren'),\r\n                cancelButtonText: t('msg.quxiao'),\r\n                title: '',\r\n                message: t('msg.tjtkxx'),\r\n            })\r\n            .then(() => {\r\n                push('/bingbank')\r\n            })\r\n            .catch(() => {\r\n                // on cancel\r\n            });\r\n            return false\r\n        }\r\n        \r\n        // 验证提现金额\r\n        if (!money_check.value) {\r\n            proxy.$Message({ type: 'error', message: t('msg.input_money') });\r\n            return false;\r\n        }\r\n        \r\n        let json = {\r\n            num: money_check.value == 0 ? money.value : money_check.value,\r\n            type: withdrawType.value, // 使用选择的提现方式\r\n            paypassword: values.paypassword,\r\n        }\r\n        \r\n        do_deposit(json).then(res => {\r\n            if(res.code === 0) {\r\n                proxy.$Message({ type: 'success', message: res.info });\r\n                push('/self')\r\n            } else {\r\n                proxy.$Message({ type: 'error', message: res.info });\r\n            }\r\n        })\r\n    };\r\n\r\n    return {\r\n        paypassword,\r\n        withdrawType,\r\n        onSubmit,\r\n        clickLeft,\r\n        clickRight,\r\n        info,\r\n        bankInfo,\r\n        money,\r\n        currency,\r\n        money_check,\r\n        moneys,\r\n        content,\r\n        infoa,\r\n        tel,\r\n        bankInfoExists,\r\n        usdtInfoExists,\r\n        goToBingBank\r\n    };\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n@import '@/styles/theme.scss';\r\n.home{\r\n    // background-image: url('~@/assets/images/home/<USER>') !important;\r\n    background-color: #f5f5f5;\r\n    border-radius: 0;\r\n}\r\n.home{\r\n    :deep(.van-nav-bar){\r\n        background-color: #fff;\r\n        // background-color: $theme;\r\n        color: #000;\r\n        .van-nav-bar__left{\r\n            .van-icon{\r\n                color: #000;\r\n            }\r\n        }\r\n        .van-nav-bar__title{\r\n            color: #000;\r\n            \r\n        }\r\n        .van-nav-bar__right{\r\n            img{\r\n                height: 42px;\r\n            }\r\n        }\r\n    }\r\n    :deep(.van-form){\r\n        padding: 40px 0 0;\r\n\r\n        .van-cell.van-cell--clickable{\r\n            border-left: 5px solid $theme;\r\n            padding: 32px;\r\n            text-align: left;\r\n            margin: 20px 0;\r\n            border-bottom: none;\r\n            box-shadow: $shadow;\r\n            .van-cell__right-icon{\r\n                color: $theme;\r\n            }\r\n        }\r\n        .van-cell-group--inset{\r\n            padding: 0 30px;\r\n            background-color: initial;\r\n        }\r\n        .van-cell{\r\n            padding: 23px 10px;\r\n            border-bottom: 1px solid  var(--van-cell-border-color);\r\n            &.zdy {\r\n                margin-bottom: 20px;\r\n                border-radius: 40px;\r\n                padding-left: 30px;\r\n            }\r\n            .van-field__left-icon{\r\n                width:90px;\r\n                text-align: center;\r\n                .van-icon__image{\r\n                    height: 42px;\r\n                    width: auto;\r\n                }\r\n                .icon{\r\n                    height: 42px;\r\n                    width: auto;\r\n                    vertical-align:middle;\r\n                }\r\n                .van-dropdown-menu{\r\n                  .van-dropdown-menu__bar{\r\n                    height: auto;\r\n                    background: none;\r\n                    box-shadow: none;\r\n                  }\r\n                  .van-cell{\r\n                    padding: 30px 80px;\r\n                  }\r\n                }\r\n            }\r\n            .van-field__control{\r\n                font-size: 24px;\r\n            }\r\n            &::after {\r\n                display: none;\r\n            }\r\n        }\r\n        .van-checkbox{\r\n            margin: 30px 0 60px 0;\r\n            .van-checkbox__icon{\r\n                font-size: 50px;\r\n                margin-right: 80px;\r\n                &.van-checkbox__icon--checked .van-icon{\r\n                    background-color:$theme;\r\n                    border-color:$theme;\r\n                }\r\n            }\r\n            .van-checkbox__label{\r\n                font-size: 24px;\r\n            }\r\n        }\r\n        .text_b{\r\n            margin:70px 60px 40px;\r\n            font-size: 27px;\r\n            color: #333;\r\n            text-align: left;\r\n            line-height: 1.5;\r\n            .tex{\r\n                margin-top: 20px;\r\n            }\r\n        }\r\n        .buttons{\r\n            padding: 0 76px;\r\n            .van-button{\r\n                font-size: 28px;\r\n                padding: 20px 0;\r\n                height: auto;\r\n                background: #000;\r\n                border: none;\r\n                color: #fff;\r\n            }\r\n            .van-button--plain{\r\n                margin-top: 40px;\r\n            }\r\n        }\r\n        .tixian_money{\r\n            text-align: left;\r\n            font-size: 30px;\r\n            margin-bottom: 20px;\r\n            color: #333;\r\n        }\r\n        .ktx{\r\n            width: 100%;\r\n            height: 190px;\r\n            border-radius: 20px;\r\n            padding: 24px 50px;\r\n            text-align: left;\r\n            // margin-bottom: 35px;\r\n            background-color: #fe2c55;\r\n            text-align: center;\r\n            .t{\r\n                font-size: 20px;\r\n                color: #fff;\r\n                margin-bottom: 10px;\r\n                opacity: 0.7;\r\n            }\r\n            .b{\r\n                font-size: 50px;\r\n                color: #fefefe;\r\n                margin-bottom: 20px;\r\n            }\r\n        }\r\n        .check_money{\r\n            display: flex;\r\n            flex-wrap: wrap;\r\n            margin-bottom: 40px;\r\n            background-color: #fff;\r\n            padding: 24px;\r\n            border-radius: 20px;\r\n            color: #333;\r\n            .text{\r\n                display: flex;\r\n                width: 100%;\r\n                text-align: left;\r\n                font-size: 28px;\r\n                margin-bottom: 25px;\r\n                \r\n                .withdraw_title {\r\n                    font-weight: bold;\r\n                    margin-bottom: 15px;\r\n                    width: 100%;\r\n                }\r\n                \r\n                span{\r\n                    flex: 1;\r\n                    &.tel{\r\n                        color: #999;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n        \r\n        .account_info {\r\n            background-color: #fff;\r\n            padding: 24px;\r\n            border-radius: 20px;\r\n            margin-bottom: 20px;\r\n            \r\n            .info_item {\r\n                display: flex;\r\n                margin-bottom: 10px;\r\n                font-size: 26px;\r\n                \r\n                .label {\r\n                    color: #666;\r\n                    margin-right: 10px;\r\n                }\r\n                \r\n                .value {\r\n                    color: #333;\r\n                    font-weight: bold;\r\n                    word-break: break-all;\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    :deep(.van-){\r\n        .van-dialog__content{\r\n            padding: 50px;\r\n        }\r\n        .van-dialog__footer{\r\n            .van-dialog__confirm{\r\n                color: $theme;\r\n            }\r\n        }\r\n    }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;EACOA,KAAK,EAAC;AAAM;;;EAQJA,KAAK,EAAC;AAAK;;EACPA,KAAK,EAAC;AAAG;;EACTA,KAAK,EAAC;AAAG;;EAEbA,KAAK,EAAC;AAAa;;;EACfA,KAAK,EAAC;;;EACDA,KAAK,EAAC;AAAgB;;;EAE3BA,KAAK,EAAC;;;;EAMNA,KAAK,EAAC;;;;EASVA,KAAK,EAAC;;;EACJA,KAAK,EAAC;AAAW;;EACdA,KAAK,EAAC;AAAO;;EACbA,KAAK,EAAC;AAAO;;EAEhBA,KAAK,EAAC;AAAW;;EACdA,KAAK,EAAC;AAAO;;EACbA,KAAK,EAAC;AAAO;;;EAKlBA,KAAK,EAAC;;;EACJA,KAAK,EAAC;AAAW;;EACdA,KAAK,EAAC;AAAO;;EACbA,KAAK,EAAC;AAAO;;EAEhBA,KAAK,EAAC;AAAW;;EACdA,KAAK,EAAC;AAAO;;EACbA,KAAK,EAAC;AAAO;;;EAIpBA,KAAK,EAAC;;;;EAiBRA,KAAK,EAAC;;;;;;;;;;;;uBAvEfC,mBAAA,CA+EM,OA/ENC,UA+EM,GA9EJC,YAAA,CAIcC,sBAAA;IAJAC,KAAK,EAAEC,IAAA,CAAAC,EAAE;IAAgB,YAAU,EAAV,EAAU;IAAEC,WAAU,EAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAAEJ,IAAA,CAAAK,OAAO,CAACC,EAAE;IAAA;IAAOC,YAAW,EAAEC,MAAA,CAAAC;;IAC9EC,KAAK,EAAAC,QAAA,CACZ;MAAA,OAA4E,CAA5EC,mBAAA,CAA4E;QAAtEC,GAAG,EAAEC,OAAO;QAAuCpB,KAAK,EAAC,KAAK;QAACqB,GAAG,EAAC;;;;gDAGjFlB,YAAA,CAwEWmB,mBAAA;IAxEAC,QAAM,EAAET,MAAA,CAAAS;EAAQ;sBACzB;MAAA,OA+DiB,CA/DjBpB,YAAA,CA+DiBqB,yBAAA;QA/DDC,KAAK,EAAL;MAAK;0BACjB;UAAA,OAGM,CAHNP,mBAAA,CAGM,OAHNQ,UAGM,GAFFR,mBAAA,CAA2C,OAA3CS,UAA2C,EAAAC,gBAAA,CAA1Bd,MAAA,CAAAe,QAAQ,IAAE,GAAC,GAAAD,gBAAA,CAAEd,MAAA,CAAAgB,KAAK,kBACnCZ,mBAAA,CAA0C,OAA1Ca,UAA0C,EAAAH,gBAAA,CAAzBtB,IAAA,CAAAC,EAAE,gC,GAEvBW,mBAAA,CAgBM,OAhBNc,UAgBM,GAfsBlB,MAAA,CAAAmB,cAAc,IAAInB,MAAA,CAAAoB,cAAc,I,cAAxDjC,mBAAA,CAEM,OAFNkC,UAEM,GADFjB,mBAAA,CAA0E,QAA1EkB,UAA0E,EAAAR,gBAAA,CAA1CtB,IAAA,CAAAC,EAAE,+C,wCAEdO,MAAA,CAAAmB,cAAc,IAAInB,MAAA,CAAAoB,cAAc,I,cAAxDjC,mBAAA,CAKM,OALNoC,UAKM,GAJFlC,YAAA,CAGkBmC,0BAAA;wBAHQxB,MAAA,CAAAyB,YAAY;;qBAAZzB,MAAA,CAAAyB,YAAY,GAAA7B,MAAA;YAAA;YAAE8B,SAAS,EAAC;;8BAChD;cAAA,OAAgF,CAAnD1B,MAAA,CAAAmB,cAAc,I,cAA3CQ,YAAA,CAAgFC,oBAAA;;gBAArEC,IAAI,EAAC;;kCAA6B;kBAAA,OAAuB,C,kCAApBrC,IAAA,CAAAC,EAAE,gC;;;;uDACrBO,MAAA,CAAAoB,cAAc,I,cAA3CO,YAAA,CAAgFC,oBAAA;;gBAArEC,IAAI,EAAC;;kCAA6B;kBAAA,OAAuB,C,kCAApBrC,IAAA,CAAAC,EAAE,gC;;;;;;;qFAG/BO,MAAA,CAAAmB,cAAc,KAAKnB,MAAA,CAAAoB,cAAc,I,cAA1DjC,mBAAA,CAKM,OALN2C,WAKM,GAJFzC,YAAA,CAAyD0C,oBAAA;YAA7CC,WAAW,EAAExC,IAAA,CAAAC,EAAE;oDAC3BJ,YAAA,CAEa4C,qBAAA;YAFDC,KAAK,EAAL,EAAK;YAACC,KAAK,EAAL,EAAK;YAACC,IAAI,EAAC,SAAS;YAAEC,OAAK,EAAErC,MAAA,CAAAsC,YAAY;YAAEC,KAAyB,EAAzB;cAAA;YAAA;;8BAC3D;cAAA,OAA+B,C,kCAA5B/C,IAAA,CAAAC,EAAE,wC;;;;mFAKf+C,mBAAA,aAAgB,EACgBxC,MAAA,CAAAyB,YAAY,eAAezB,MAAA,CAAAmB,cAAc,I,cAAzEhC,mBAAA,CASM,OATNsD,WASM,GARJrC,mBAAA,CAGM,OAHNsC,WAGM,GAFJtC,mBAAA,CAAqD,QAArDuC,WAAqD,EAAA7B,gBAAA,CAA9BtB,IAAA,CAAAC,EAAE,qBAAoB,GAAC,iBAC9CW,mBAAA,CAAkD,QAAlDwC,WAAkD,EAAA9B,gBAAA,CAA3Bd,MAAA,CAAA6C,QAAQ,CAACC,QAAQ,iB,GAE1C1C,mBAAA,CAGM,OAHN2C,WAGM,GAFJ3C,mBAAA,CAAgD,QAAhD4C,WAAgD,EAAAlC,gBAAA,CAAzBtB,IAAA,CAAAC,EAAE,gBAAe,GAAC,iBACzCW,mBAAA,CAAiD,QAAjD6C,WAAiD,EAAAnC,gBAAA,CAA1Bd,MAAA,CAAA6C,QAAQ,CAACK,OAAO,iB,0CAI3CV,mBAAA,cAAiB,EACexC,MAAA,CAAAyB,YAAY,eAAezB,MAAA,CAAAoB,cAAc,I,cAAzEjC,mBAAA,CASM,OATNgE,WASM,GARJ/C,mBAAA,CAGM,OAHNgD,WAGM,GAFJhD,mBAAA,CAAqD,QAArDiD,WAAqD,EAAAvC,gBAAA,CAA9BtB,IAAA,CAAAC,EAAE,qBAAoB,GAAC,iBAC9CW,mBAAA,CAAmD,QAAnDkD,WAAmD,EAAAxC,gBAAA,CAA5Bd,MAAA,CAAA6C,QAAQ,CAACU,SAAS,iB,GAE3CnD,mBAAA,CAGM,OAHNoD,WAGM,GAFJpD,mBAAA,CAAwD,QAAxDqD,WAAwD,EAAA3C,gBAAA,CAAjCtB,IAAA,CAAAC,EAAE,wBAAuB,GAAC,iBACjDW,mBAAA,CAAkD,QAAlDsD,WAAkD,EAAA5C,gBAAA,CAA3Bd,MAAA,CAAA6C,QAAQ,CAACc,QAAQ,iB,0CAId3D,MAAA,CAAAmB,cAAc,IAAInB,MAAA,CAAAoB,cAAc,I,cAAhEjC,mBAAA,CAAkG,OAAlGyE,WAAkG,EAAA9C,gBAAA,CAA9BtB,IAAA,CAAAC,EAAE,wC,mCAK9DO,MAAA,CAAAmB,cAAc,IAAInB,MAAA,CAAAoB,cAAc,I,cAJxCO,YAAA,CAKEkC,oBAAA;;YAJA3E,KAAK,EAAC,KAAK;wBACFc,MAAA,CAAA8D,WAAW;;qBAAX9D,MAAA,CAAA8D,WAAW,GAAAlE,MAAA;YAAA;YACnBmE,WAAW,EAAEvE,IAAA,CAAAC,EAAE;uGAUVO,MAAA,CAAAmB,cAAc,IAAInB,MAAA,CAAAoB,cAAc,I,cAPxCO,YAAA,CAQEkC,oBAAA;;YAPA3E,KAAK,EAAC,KAAK;wBACFc,MAAA,CAAAgE,WAAW;;qBAAXhE,MAAA,CAAAgE,WAAW,GAAApE,MAAA;YAAA;YACpBwC,IAAI,EAAC,UAAU;YACfP,IAAI,EAAC,aAAa;YACjBkC,WAAW,EAAEvE,IAAA,CAAAC,EAAE;YACfwE,KAAK;cAAAC,QAAA;cAAAC,OAAA,EAA8B3E,IAAA,CAAAC,EAAE;YAAA;;;;UAIfO,MAAA,CAAAmB,cAAc,IAAInB,MAAA,CAAAoB,cAAc,I,cAA3DjC,mBAAA,CAIM,OAJNiF,WAIM,GAHJ/E,YAAA,CAEa4C,qBAAA;QAFDC,KAAK,EAAL,EAAK;QAACC,KAAK,EAAL,EAAK;QAACC,IAAI,EAAC,SAAS;QAAC,aAAW,EAAC;;0BACjD;UAAA,OAAqB,C,kCAAnB5C,IAAA,CAAAC,EAAE,gC;;;;iDAGmCO,MAAA,CAAAmB,cAAc,IAAInB,MAAA,CAAAoB,cAAc,I,cAA3EjC,mBAAA,CACM;;QADDD,KAAK,EAAC,QAAQ;QAACmF,SAAgB,EAARrE,MAAA,CAAAsE"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}