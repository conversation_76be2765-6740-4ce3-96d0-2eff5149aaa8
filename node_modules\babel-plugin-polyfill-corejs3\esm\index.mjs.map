{"version": 3, "file": "index.mjs", "sources": ["../src/shipped-proposals.ts", "../src/built-in-definitions.ts", "../src/usage-filters.ts", "../src/utils.ts", "../src/index.ts"], "sourcesContent": ["// This file is automatically generated by scripts/build-corejs3-shipped-proposals.mjs\n\nexport default new Set<string>([\n  \"esnext.array.from-async\",\n  \"esnext.array.group\",\n  \"esnext.array.group-to-map\",\n  \"esnext.array-buffer.detached\",\n  \"esnext.array-buffer.transfer\",\n  \"esnext.array-buffer.transfer-to-fixed-length\",\n  \"esnext.json.is-raw-json\",\n  \"esnext.json.parse\",\n  \"esnext.json.raw-json\",\n  \"esnext.set.difference.v2\",\n  \"esnext.set.intersection.v2\",\n  \"esnext.set.is-disjoint-from.v2\",\n  \"esnext.set.is-subset-of.v2\",\n  \"esnext.set.is-superset-of.v2\",\n  \"esnext.set.symmetric-difference.v2\",\n  \"esnext.set.union.v2\",\n  \"esnext.string.is-well-formed\",\n  \"esnext.string.to-well-formed\",\n]);\n", "import corejs3Polyfills from \"../core-js-compat/data.js\";\n\ntype ObjectMap<V> = { [name: string]: V };\ntype ObjectMap2<V> = ObjectMap<ObjectMap<V>>;\n\nexport type CoreJSPolyfillDescriptor = {\n  name: string;\n  pure: string | null;\n  global: string[];\n  exclude: string[] | null;\n};\n\nconst polyfillsOrder = {};\nObject.keys(corejs3Polyfills).forEach((name, index) => {\n  polyfillsOrder[name] = index;\n});\n\nconst define = (\n  pure,\n  global,\n  name = global[0],\n  exclude?,\n): CoreJSPolyfillDescriptor => {\n  return {\n    name,\n    pure,\n    global: global.sort((a, b) => polyfillsOrder[a] - polyfillsOrder[b]),\n    exclude,\n  };\n};\n\nconst typed = (name: string) => define(null, [name, ...TypedArrayDependencies]);\n\nconst ArrayNatureIterators = [\n  \"es.array.iterator\",\n  \"web.dom-collections.iterator\",\n];\n\nexport const CommonIterators = [\"es.string.iterator\", ...ArrayNatureIterators];\n\nconst ArrayNatureIteratorsWithTag = [\n  \"es.object.to-string\",\n  ...ArrayNatureIterators,\n];\n\nconst CommonIteratorsWithTag = [\"es.object.to-string\", ...CommonIterators];\n\nconst ErrorDependencies = [\"es.error.cause\", \"es.error.to-string\"];\n\nconst SuppressedErrorDependencies = [\n  \"esnext.suppressed-error.constructor\",\n  ...ErrorDependencies,\n];\n\nconst TypedArrayDependencies = [\n  \"es.typed-array.at\",\n  \"es.typed-array.copy-within\",\n  \"es.typed-array.every\",\n  \"es.typed-array.fill\",\n  \"es.typed-array.filter\",\n  \"es.typed-array.find\",\n  \"es.typed-array.find-index\",\n  \"es.typed-array.find-last\",\n  \"es.typed-array.find-last-index\",\n  \"es.typed-array.for-each\",\n  \"es.typed-array.includes\",\n  \"es.typed-array.index-of\",\n  \"es.typed-array.iterator\",\n  \"es.typed-array.join\",\n  \"es.typed-array.last-index-of\",\n  \"es.typed-array.map\",\n  \"es.typed-array.reduce\",\n  \"es.typed-array.reduce-right\",\n  \"es.typed-array.reverse\",\n  \"es.typed-array.set\",\n  \"es.typed-array.slice\",\n  \"es.typed-array.some\",\n  \"es.typed-array.sort\",\n  \"es.typed-array.subarray\",\n  \"es.typed-array.to-locale-string\",\n  \"es.typed-array.to-reversed\",\n  \"es.typed-array.to-sorted\",\n  \"es.typed-array.to-string\",\n  \"es.typed-array.with\",\n  \"es.object.to-string\",\n  \"es.array.iterator\",\n  \"es.array-buffer.slice\",\n  \"esnext.array-buffer.detached\",\n  \"esnext.array-buffer.transfer\",\n  \"esnext.array-buffer.transfer-to-fixed-length\",\n  \"esnext.typed-array.filter-reject\",\n  \"esnext.typed-array.group-by\",\n  \"esnext.typed-array.to-spliced\",\n  \"esnext.typed-array.unique-by\",\n];\n\nexport const PromiseDependencies = [\"es.promise\", \"es.object.to-string\"];\n\nexport const PromiseDependenciesWithIterators = [\n  ...PromiseDependencies,\n  ...CommonIterators,\n];\n\nconst SymbolDependencies = [\n  \"es.symbol\",\n  \"es.symbol.description\",\n  \"es.object.to-string\",\n];\n\nconst MapDependencies = [\n  \"es.map\",\n  \"esnext.map.delete-all\",\n  \"esnext.map.emplace\",\n  \"esnext.map.every\",\n  \"esnext.map.filter\",\n  \"esnext.map.find\",\n  \"esnext.map.find-key\",\n  \"esnext.map.includes\",\n  \"esnext.map.key-of\",\n  \"esnext.map.map-keys\",\n  \"esnext.map.map-values\",\n  \"esnext.map.merge\",\n  \"esnext.map.reduce\",\n  \"esnext.map.some\",\n  \"esnext.map.update\",\n  ...CommonIteratorsWithTag,\n];\n\nconst SetDependencies = [\n  \"es.set\",\n  \"esnext.set.add-all\",\n  \"esnext.set.delete-all\",\n  \"esnext.set.difference\",\n  \"esnext.set.difference.v2\",\n  \"esnext.set.every\",\n  \"esnext.set.filter\",\n  \"esnext.set.find\",\n  \"esnext.set.intersection\",\n  \"esnext.set.intersection.v2\",\n  \"esnext.set.is-disjoint-from\",\n  \"esnext.set.is-disjoint-from.v2\",\n  \"esnext.set.is-subset-of\",\n  \"esnext.set.is-subset-of.v2\",\n  \"esnext.set.is-superset-of\",\n  \"esnext.set.is-superset-of.v2\",\n  \"esnext.set.join\",\n  \"esnext.set.map\",\n  \"esnext.set.reduce\",\n  \"esnext.set.some\",\n  \"esnext.set.symmetric-difference\",\n  \"esnext.set.symmetric-difference.v2\",\n  \"esnext.set.union\",\n  \"esnext.set.union.v2\",\n  ...CommonIteratorsWithTag,\n];\n\nconst WeakMapDependencies = [\n  \"es.weak-map\",\n  \"esnext.weak-map.delete-all\",\n  \"esnext.weak-map.emplace\",\n  ...CommonIteratorsWithTag,\n];\n\nconst WeakSetDependencies = [\n  \"es.weak-set\",\n  \"esnext.weak-set.add-all\",\n  \"esnext.weak-set.delete-all\",\n  ...CommonIteratorsWithTag,\n];\n\nconst DOMExceptionDependencies = [\n  \"web.dom-exception.constructor\",\n  \"web.dom-exception.stack\",\n  \"web.dom-exception.to-string-tag\",\n  \"es.error.to-string\",\n];\n\nconst URLSearchParamsDependencies = [\n  \"web.url-search-params\",\n  \"web.url-search-params.size\",\n  ...CommonIteratorsWithTag,\n];\n\nconst AsyncIteratorDependencies = [\n  \"esnext.async-iterator.constructor\",\n  ...PromiseDependencies,\n];\n\nconst AsyncIteratorProblemMethods = [\n  \"esnext.async-iterator.every\",\n  \"esnext.async-iterator.filter\",\n  \"esnext.async-iterator.find\",\n  \"esnext.async-iterator.flat-map\",\n  \"esnext.async-iterator.for-each\",\n  \"esnext.async-iterator.map\",\n  \"esnext.async-iterator.reduce\",\n  \"esnext.async-iterator.some\",\n];\n\nconst IteratorDependencies = [\n  \"esnext.iterator.constructor\",\n  \"es.object.to-string\",\n];\n\nconst TypedArrayStaticMethods = {\n  from: define(null, [\"es.typed-array.from\"]),\n  fromAsync: define(null, [\n    \"esnext.typed-array.from-async\",\n    ...PromiseDependenciesWithIterators,\n  ]),\n  of: define(null, [\"es.typed-array.of\"]),\n};\n\nexport const BuiltIns: ObjectMap<CoreJSPolyfillDescriptor> = {\n  AsyncDisposableStack: define(\"async-disposable-stack\", [\n    \"esnext.async-disposable-stack.constructor\",\n    \"es.object.to-string\",\n    \"esnext.async-iterator.async-dispose\",\n    \"esnext.iterator.dispose\",\n    ...PromiseDependencies,\n    ...SuppressedErrorDependencies,\n  ]),\n  AsyncIterator: define(\"async-iterator/index\", AsyncIteratorDependencies),\n  AggregateError: define(\"aggregate-error\", [\n    \"es.aggregate-error\",\n    ...ErrorDependencies,\n    ...CommonIteratorsWithTag,\n    \"es.aggregate-error.cause\",\n  ]),\n  ArrayBuffer: define(null, [\n    \"es.array-buffer.constructor\",\n    \"es.array-buffer.slice\",\n    \"es.object.to-string\",\n  ]),\n  DataView: define(null, [\n    \"es.data-view\",\n    \"es.array-buffer.slice\",\n    \"es.object.to-string\",\n  ]),\n  Date: define(null, [\"es.date.to-string\"]),\n  DOMException: define(\"dom-exception\", DOMExceptionDependencies),\n  DisposableStack: define(\"disposable-stack\", [\n    \"esnext.disposable-stack.constructor\",\n    \"es.object.to-string\",\n    \"esnext.iterator.dispose\",\n    ...SuppressedErrorDependencies,\n  ]),\n  Error: define(null, ErrorDependencies),\n  EvalError: define(null, ErrorDependencies),\n  Float32Array: typed(\"es.typed-array.float32-array\"),\n  Float64Array: typed(\"es.typed-array.float64-array\"),\n  Int8Array: typed(\"es.typed-array.int8-array\"),\n  Int16Array: typed(\"es.typed-array.int16-array\"),\n  Int32Array: typed(\"es.typed-array.int32-array\"),\n  Iterator: define(\"iterator/index\", IteratorDependencies),\n  Uint8Array: typed(\"es.typed-array.uint8-array\"),\n  Uint8ClampedArray: typed(\"es.typed-array.uint8-clamped-array\"),\n  Uint16Array: typed(\"es.typed-array.uint16-array\"),\n  Uint32Array: typed(\"es.typed-array.uint32-array\"),\n  Map: define(\"map/index\", MapDependencies),\n  Number: define(null, [\"es.number.constructor\"]),\n  Observable: define(\"observable/index\", [\n    \"esnext.observable\",\n    \"esnext.symbol.observable\",\n    \"es.object.to-string\",\n    ...CommonIteratorsWithTag,\n  ]),\n  Promise: define(\"promise/index\", PromiseDependencies),\n  RangeError: define(null, ErrorDependencies),\n  ReferenceError: define(null, ErrorDependencies),\n  Reflect: define(null, [\"es.reflect.to-string-tag\", \"es.object.to-string\"]),\n  RegExp: define(null, [\n    \"es.regexp.constructor\",\n    \"es.regexp.dot-all\",\n    \"es.regexp.exec\",\n    \"es.regexp.sticky\",\n    \"es.regexp.to-string\",\n  ]),\n  Set: define(\"set/index\", SetDependencies),\n  SuppressedError: define(\"suppressed-error\", SuppressedErrorDependencies),\n  Symbol: define(\"symbol/index\", SymbolDependencies),\n  SyntaxError: define(null, ErrorDependencies),\n  TypeError: define(null, ErrorDependencies),\n  URIError: define(null, ErrorDependencies),\n  URL: define(\"url/index\", [\"web.url\", ...URLSearchParamsDependencies]),\n  URLSearchParams: define(\n    \"url-search-params/index\",\n    URLSearchParamsDependencies,\n  ),\n  WeakMap: define(\"weak-map/index\", WeakMapDependencies),\n  WeakSet: define(\"weak-set/index\", WeakSetDependencies),\n\n  atob: define(\"atob\", [\"web.atob\", ...DOMExceptionDependencies]),\n  btoa: define(\"btoa\", [\"web.btoa\", ...DOMExceptionDependencies]),\n  clearImmediate: define(\"clear-immediate\", [\"web.immediate\"]),\n  compositeKey: define(\"composite-key\", [\"esnext.composite-key\"]),\n  compositeSymbol: define(\"composite-symbol\", [\"esnext.composite-symbol\"]),\n  escape: define(\"escape\", [\"es.escape\"]),\n  fetch: define(null, PromiseDependencies),\n  globalThis: define(\"global-this\", [\"es.global-this\"]),\n  parseFloat: define(\"parse-float\", [\"es.parse-float\"]),\n  parseInt: define(\"parse-int\", [\"es.parse-int\"]),\n  queueMicrotask: define(\"queue-microtask\", [\"web.queue-microtask\"]),\n  self: define(\"self\", [\"web.self\"]),\n  setImmediate: define(\"set-immediate\", [\"web.immediate\"]),\n  setInterval: define(\"set-interval\", [\"web.timers\"]),\n  setTimeout: define(\"set-timeout\", [\"web.timers\"]),\n  structuredClone: define(\"structured-clone\", [\n    \"web.structured-clone\",\n    ...DOMExceptionDependencies,\n    \"es.array.iterator\",\n    \"es.object.keys\",\n    \"es.object.to-string\",\n    \"es.map\",\n    \"es.set\",\n  ]),\n  unescape: define(\"unescape\", [\"es.unescape\"]),\n};\n\nexport const StaticProperties: ObjectMap2<CoreJSPolyfillDescriptor> = {\n  AsyncIterator: {\n    from: define(\"async-iterator/from\", [\n      \"esnext.async-iterator.from\",\n      ...AsyncIteratorDependencies,\n      ...AsyncIteratorProblemMethods,\n      ...CommonIterators,\n    ]),\n  },\n  Array: {\n    from: define(\"array/from\", [\"es.array.from\", \"es.string.iterator\"]),\n    fromAsync: define(\"array/from-async\", [\n      \"esnext.array.from-async\",\n      ...PromiseDependenciesWithIterators,\n    ]),\n    isArray: define(\"array/is-array\", [\"es.array.is-array\"]),\n    isTemplateObject: define(\"array/is-template-object\", [\n      \"esnext.array.is-template-object\",\n    ]),\n    of: define(\"array/of\", [\"es.array.of\"]),\n  },\n\n  ArrayBuffer: {\n    isView: define(null, [\"es.array-buffer.is-view\"]),\n  },\n\n  BigInt: {\n    range: define(\"bigint/range\", [\n      \"esnext.bigint.range\",\n      \"es.object.to-string\",\n    ]),\n  },\n\n  Date: {\n    now: define(\"date/now\", [\"es.date.now\"]),\n  },\n\n  Function: {\n    isCallable: define(\"function/is-callable\", [\"esnext.function.is-callable\"]),\n    isConstructor: define(\"function/is-constructor\", [\n      \"esnext.function.is-constructor\",\n    ]),\n  },\n\n  Iterator: {\n    from: define(\"iterator/from\", [\n      \"esnext.iterator.from\",\n      ...IteratorDependencies,\n      ...CommonIterators,\n    ]),\n    range: define(\"iterator/range\", [\n      \"esnext.iterator.range\",\n      \"es.object.to-string\",\n    ]),\n  },\n\n  JSON: {\n    isRawJSON: define(\"json/is-raw-json\", [\"esnext.json.is-raw-json\"]),\n    parse: define(\"json/parse\", [\"esnext.json.parse\", \"es.object.keys\"]),\n    rawJSON: define(\"json/raw-json\", [\n      \"esnext.json.raw-json\",\n      \"es.object.create\",\n      \"es.object.freeze\",\n    ]),\n    stringify: define(\"json/stringify\", [\"es.json.stringify\"], \"es.symbol\"),\n  },\n\n  Math: {\n    DEG_PER_RAD: define(\"math/deg-per-rad\", [\"esnext.math.deg-per-rad\"]),\n    RAD_PER_DEG: define(\"math/rad-per-deg\", [\"esnext.math.rad-per-deg\"]),\n    acosh: define(\"math/acosh\", [\"es.math.acosh\"]),\n    asinh: define(\"math/asinh\", [\"es.math.asinh\"]),\n    atanh: define(\"math/atanh\", [\"es.math.atanh\"]),\n    cbrt: define(\"math/cbrt\", [\"es.math.cbrt\"]),\n    clamp: define(\"math/clamp\", [\"esnext.math.clamp\"]),\n    clz32: define(\"math/clz32\", [\"es.math.clz32\"]),\n    cosh: define(\"math/cosh\", [\"es.math.cosh\"]),\n    degrees: define(\"math/degrees\", [\"esnext.math.degrees\"]),\n    expm1: define(\"math/expm1\", [\"es.math.expm1\"]),\n    fround: define(\"math/fround\", [\"es.math.fround\"]),\n    fscale: define(\"math/fscale\", [\"esnext.math.fscale\"]),\n    hypot: define(\"math/hypot\", [\"es.math.hypot\"]),\n    iaddh: define(\"math/iaddh\", [\"esnext.math.iaddh\"]),\n    imul: define(\"math/imul\", [\"es.math.imul\"]),\n    imulh: define(\"math/imulh\", [\"esnext.math.imulh\"]),\n    isubh: define(\"math/isubh\", [\"esnext.math.isubh\"]),\n    log10: define(\"math/log10\", [\"es.math.log10\"]),\n    log1p: define(\"math/log1p\", [\"es.math.log1p\"]),\n    log2: define(\"math/log2\", [\"es.math.log2\"]),\n    radians: define(\"math/radians\", [\"esnext.math.radians\"]),\n    scale: define(\"math/scale\", [\"esnext.math.scale\"]),\n    seededPRNG: define(\"math/seeded-prng\", [\"esnext.math.seeded-prng\"]),\n    sign: define(\"math/sign\", [\"es.math.sign\"]),\n    signbit: define(\"math/signbit\", [\"esnext.math.signbit\"]),\n    sinh: define(\"math/sinh\", [\"es.math.sinh\"]),\n    tanh: define(\"math/tanh\", [\"es.math.tanh\"]),\n    trunc: define(\"math/trunc\", [\"es.math.trunc\"]),\n    umulh: define(\"math/umulh\", [\"esnext.math.umulh\"]),\n  },\n\n  Map: {\n    from: define(null, [\"esnext.map.from\", ...MapDependencies]),\n    groupBy: define(null, [\"esnext.map.group-by\", ...MapDependencies]),\n    keyBy: define(null, [\"esnext.map.key-by\", ...MapDependencies]),\n    of: define(null, [\"esnext.map.of\", ...MapDependencies]),\n  },\n\n  Number: {\n    EPSILON: define(\"number/epsilon\", [\"es.number.epsilon\"]),\n    MAX_SAFE_INTEGER: define(\"number/max-safe-integer\", [\n      \"es.number.max-safe-integer\",\n    ]),\n    MIN_SAFE_INTEGER: define(\"number/min-safe-integer\", [\n      \"es.number.min-safe-integer\",\n    ]),\n    fromString: define(\"number/from-string\", [\"esnext.number.from-string\"]),\n    isFinite: define(\"number/is-finite\", [\"es.number.is-finite\"]),\n    isInteger: define(\"number/is-integer\", [\"es.number.is-integer\"]),\n    isNaN: define(\"number/is-nan\", [\"es.number.is-nan\"]),\n    isSafeInteger: define(\"number/is-safe-integer\", [\n      \"es.number.is-safe-integer\",\n    ]),\n    parseFloat: define(\"number/parse-float\", [\"es.number.parse-float\"]),\n    parseInt: define(\"number/parse-int\", [\"es.number.parse-int\"]),\n    range: define(\"number/range\", [\n      \"esnext.number.range\",\n      \"es.object.to-string\",\n    ]),\n  },\n\n  Object: {\n    assign: define(\"object/assign\", [\"es.object.assign\"]),\n    create: define(\"object/create\", [\"es.object.create\"]),\n    defineProperties: define(\"object/define-properties\", [\n      \"es.object.define-properties\",\n    ]),\n    defineProperty: define(\"object/define-property\", [\n      \"es.object.define-property\",\n    ]),\n    entries: define(\"object/entries\", [\"es.object.entries\"]),\n    freeze: define(\"object/freeze\", [\"es.object.freeze\"]),\n    fromEntries: define(\"object/from-entries\", [\n      \"es.object.from-entries\",\n      \"es.array.iterator\",\n    ]),\n    getOwnPropertyDescriptor: define(\"object/get-own-property-descriptor\", [\n      \"es.object.get-own-property-descriptor\",\n    ]),\n    getOwnPropertyDescriptors: define(\"object/get-own-property-descriptors\", [\n      \"es.object.get-own-property-descriptors\",\n    ]),\n    getOwnPropertyNames: define(\"object/get-own-property-names\", [\n      \"es.object.get-own-property-names\",\n    ]),\n    getOwnPropertySymbols: define(\"object/get-own-property-symbols\", [\n      \"es.symbol\",\n    ]),\n    getPrototypeOf: define(\"object/get-prototype-of\", [\n      \"es.object.get-prototype-of\",\n    ]),\n    hasOwn: define(\"object/has-own\", [\"es.object.has-own\"]),\n    is: define(\"object/is\", [\"es.object.is\"]),\n    isExtensible: define(\"object/is-extensible\", [\"es.object.is-extensible\"]),\n    isFrozen: define(\"object/is-frozen\", [\"es.object.is-frozen\"]),\n    isSealed: define(\"object/is-sealed\", [\"es.object.is-sealed\"]),\n    keys: define(\"object/keys\", [\"es.object.keys\"]),\n    preventExtensions: define(\"object/prevent-extensions\", [\n      \"es.object.prevent-extensions\",\n    ]),\n    seal: define(\"object/seal\", [\"es.object.seal\"]),\n    setPrototypeOf: define(\"object/set-prototype-of\", [\n      \"es.object.set-prototype-of\",\n    ]),\n    values: define(\"object/values\", [\"es.object.values\"]),\n  },\n\n  Promise: {\n    all: define(null, PromiseDependenciesWithIterators),\n    allSettled: define(null, [\n      \"es.promise.all-settled\",\n      ...PromiseDependenciesWithIterators,\n    ]),\n    any: define(null, [\n      \"es.promise.any\",\n      \"es.aggregate-error\",\n      ...PromiseDependenciesWithIterators,\n    ]),\n    race: define(null, PromiseDependenciesWithIterators),\n    try: define(null, [\n      \"esnext.promise.try\",\n      ...PromiseDependenciesWithIterators,\n    ]),\n  },\n\n  Reflect: {\n    apply: define(\"reflect/apply\", [\"es.reflect.apply\"]),\n    construct: define(\"reflect/construct\", [\"es.reflect.construct\"]),\n    defineMetadata: define(\"reflect/define-metadata\", [\n      \"esnext.reflect.define-metadata\",\n    ]),\n    defineProperty: define(\"reflect/define-property\", [\n      \"es.reflect.define-property\",\n    ]),\n    deleteMetadata: define(\"reflect/delete-metadata\", [\n      \"esnext.reflect.delete-metadata\",\n    ]),\n    deleteProperty: define(\"reflect/delete-property\", [\n      \"es.reflect.delete-property\",\n    ]),\n    get: define(\"reflect/get\", [\"es.reflect.get\"]),\n    getMetadata: define(\"reflect/get-metadata\", [\n      \"esnext.reflect.get-metadata\",\n    ]),\n    getMetadataKeys: define(\"reflect/get-metadata-keys\", [\n      \"esnext.reflect.get-metadata-keys\",\n    ]),\n    getOwnMetadata: define(\"reflect/get-own-metadata\", [\n      \"esnext.reflect.get-own-metadata\",\n    ]),\n    getOwnMetadataKeys: define(\"reflect/get-own-metadata-keys\", [\n      \"esnext.reflect.get-own-metadata-keys\",\n    ]),\n    getOwnPropertyDescriptor: define(\"reflect/get-own-property-descriptor\", [\n      \"es.reflect.get-own-property-descriptor\",\n    ]),\n    getPrototypeOf: define(\"reflect/get-prototype-of\", [\n      \"es.reflect.get-prototype-of\",\n    ]),\n    has: define(\"reflect/has\", [\"es.reflect.has\"]),\n    hasMetadata: define(\"reflect/has-metadata\", [\n      \"esnext.reflect.has-metadata\",\n    ]),\n    hasOwnMetadata: define(\"reflect/has-own-metadata\", [\n      \"esnext.reflect.has-own-metadata\",\n    ]),\n    isExtensible: define(\"reflect/is-extensible\", [\"es.reflect.is-extensible\"]),\n    metadata: define(\"reflect/metadata\", [\"esnext.reflect.metadata\"]),\n    ownKeys: define(\"reflect/own-keys\", [\"es.reflect.own-keys\"]),\n    preventExtensions: define(\"reflect/prevent-extensions\", [\n      \"es.reflect.prevent-extensions\",\n    ]),\n    set: define(\"reflect/set\", [\"es.reflect.set\"]),\n    setPrototypeOf: define(\"reflect/set-prototype-of\", [\n      \"es.reflect.set-prototype-of\",\n    ]),\n  },\n\n  Set: {\n    from: define(null, [\"esnext.set.from\", ...SetDependencies]),\n    of: define(null, [\"esnext.set.of\", ...SetDependencies]),\n  },\n\n  String: {\n    cooked: define(\"string/cooked\", [\"esnext.string.cooked\"]),\n    dedent: define(\"string/dedent\", [\n      \"esnext.string.dedent\",\n      \"es.string.from-code-point\",\n      \"es.weak-map\",\n    ]),\n    fromCodePoint: define(\"string/from-code-point\", [\n      \"es.string.from-code-point\",\n    ]),\n    raw: define(\"string/raw\", [\"es.string.raw\"]),\n  },\n\n  Symbol: {\n    asyncDispose: define(\"symbol/async-dispose\", [\n      \"esnext.symbol.async-dispose\",\n      \"esnext.async-iterator.async-dispose\",\n    ]),\n    asyncIterator: define(\"symbol/async-iterator\", [\n      \"es.symbol.async-iterator\",\n    ]),\n    dispose: define(\"symbol/dispose\", [\n      \"esnext.symbol.dispose\",\n      \"esnext.iterator.dispose\",\n    ]),\n    for: define(\"symbol/for\", [], \"es.symbol\"),\n    hasInstance: define(\"symbol/has-instance\", [\n      \"es.symbol.has-instance\",\n      \"es.function.has-instance\",\n    ]),\n    isConcatSpreadable: define(\"symbol/is-concat-spreadable\", [\n      \"es.symbol.is-concat-spreadable\",\n      \"es.array.concat\",\n    ]),\n    isRegistered: define(\"symbol/is-registered\", [\n      \"esnext.symbol.is-registered\",\n      \"es.symbol\",\n    ]),\n    isWellKnown: define(\"symbol/is-well-known\", [\n      \"esnext.symbol.is-well-known\",\n      \"es.symbol\",\n    ]),\n    iterator: define(\"symbol/iterator\", [\n      \"es.symbol.iterator\",\n      ...CommonIteratorsWithTag,\n    ]),\n    keyFor: define(\"symbol/key-for\", [], \"es.symbol\"),\n    match: define(\"symbol/match\", [\"es.symbol.match\", \"es.string.match\"]),\n    matcher: define(\"symbol/matcher\", [\"esnext.symbol.matcher\"]),\n    matchAll: define(\"symbol/match-all\", [\n      \"es.symbol.match-all\",\n      \"es.string.match-all\",\n    ]),\n    metadata: define(\"symbol/metadata\", [\"esnext.symbol.metadata\"]),\n    metadataKey: define(\"symbol/metadata-key\", [\"esnext.symbol.metadata-key\"]),\n    observable: define(\"symbol/observable\", [\"esnext.symbol.observable\"]),\n    patternMatch: define(\"symbol/pattern-match\", [\n      \"esnext.symbol.pattern-match\",\n    ]),\n    replace: define(\"symbol/replace\", [\n      \"es.symbol.replace\",\n      \"es.string.replace\",\n    ]),\n    search: define(\"symbol/search\", [\"es.symbol.search\", \"es.string.search\"]),\n    species: define(\"symbol/species\", [\n      \"es.symbol.species\",\n      \"es.array.species\",\n    ]),\n    split: define(\"symbol/split\", [\"es.symbol.split\", \"es.string.split\"]),\n    toPrimitive: define(\"symbol/to-primitive\", [\n      \"es.symbol.to-primitive\",\n      \"es.date.to-primitive\",\n    ]),\n    toStringTag: define(\"symbol/to-string-tag\", [\n      \"es.symbol.to-string-tag\",\n      \"es.object.to-string\",\n      \"es.math.to-string-tag\",\n      \"es.json.to-string-tag\",\n    ]),\n    unscopables: define(\"symbol/unscopables\", [\"es.symbol.unscopables\"]),\n  },\n\n  URL: {\n    canParse: define(\"url/can-parse\", [\"web.url.can-parse\", \"web.url\"]),\n  },\n\n  WeakMap: {\n    from: define(null, [\"esnext.weak-map.from\", ...WeakMapDependencies]),\n    of: define(null, [\"esnext.weak-map.of\", ...WeakMapDependencies]),\n  },\n\n  WeakSet: {\n    from: define(null, [\"esnext.weak-set.from\", ...WeakSetDependencies]),\n    of: define(null, [\"esnext.weak-set.of\", ...WeakSetDependencies]),\n  },\n\n  Int8Array: TypedArrayStaticMethods,\n  Uint8Array: TypedArrayStaticMethods,\n  Uint8ClampedArray: TypedArrayStaticMethods,\n  Int16Array: TypedArrayStaticMethods,\n  Uint16Array: TypedArrayStaticMethods,\n  Int32Array: TypedArrayStaticMethods,\n  Uint32Array: TypedArrayStaticMethods,\n  Float32Array: TypedArrayStaticMethods,\n  Float64Array: TypedArrayStaticMethods,\n\n  WebAssembly: {\n    CompileError: define(null, ErrorDependencies),\n    LinkError: define(null, ErrorDependencies),\n    RuntimeError: define(null, ErrorDependencies),\n  },\n};\n\nexport const InstanceProperties = {\n  asIndexedPairs: define(\"instance/asIndexedPairs\", [\n    \"esnext.async-iterator.as-indexed-pairs\",\n    ...AsyncIteratorDependencies,\n    \"esnext.iterator.as-indexed-pairs\",\n    ...IteratorDependencies,\n  ]),\n  at: define(\"instance/at\", [\n    // TODO: We should introduce overloaded instance methods definition\n    // Before that is implemented, the `esnext.string.at` must be the first\n    // In pure mode, the provider resolves the descriptor as a \"pure\" `esnext.string.at`\n    // and treats the compat-data of `esnext.string.at` as the compat-data of\n    // pure import `instance/at`. The first polyfill here should have the lowest corejs\n    // supported versions.\n    \"esnext.string.at\",\n    \"es.string.at-alternative\",\n    \"es.array.at\",\n  ]),\n  anchor: define(null, [\"es.string.anchor\"]),\n  big: define(null, [\"es.string.big\"]),\n  bind: define(\"instance/bind\", [\"es.function.bind\"]),\n  blink: define(null, [\"es.string.blink\"]),\n  bold: define(null, [\"es.string.bold\"]),\n  codePointAt: define(\"instance/code-point-at\", [\"es.string.code-point-at\"]),\n  codePoints: define(\"instance/code-points\", [\"esnext.string.code-points\"]),\n  concat: define(\"instance/concat\", [\"es.array.concat\"], undefined, [\"String\"]),\n  copyWithin: define(\"instance/copy-within\", [\"es.array.copy-within\"]),\n  demethodize: define(\"instance/demethodize\", [\"esnext.function.demethodize\"]),\n  description: define(null, [\"es.symbol\", \"es.symbol.description\"]),\n  dotAll: define(null, [\"es.regexp.dot-all\"]),\n  drop: define(\"instance/drop\", [\n    \"esnext.async-iterator.drop\",\n    ...AsyncIteratorDependencies,\n    \"esnext.iterator.drop\",\n    ...IteratorDependencies,\n  ]),\n  emplace: define(\"instance/emplace\", [\n    \"esnext.map.emplace\",\n    \"esnext.weak-map.emplace\",\n  ]),\n  endsWith: define(\"instance/ends-with\", [\"es.string.ends-with\"]),\n  entries: define(\"instance/entries\", ArrayNatureIteratorsWithTag),\n  every: define(\"instance/every\", [\n    \"es.array.every\",\n    \"esnext.async-iterator.every\",\n    // TODO: add async iterator dependencies when we support sub-dependencies\n    // esnext.async-iterator.every depends on es.promise\n    // but we don't want to pull es.promise when esnext.async-iterator is disabled\n    //\n    // ...AsyncIteratorDependencies\n    \"esnext.iterator.every\",\n    ...IteratorDependencies,\n  ]),\n  exec: define(null, [\"es.regexp.exec\"]),\n  fill: define(\"instance/fill\", [\"es.array.fill\"]),\n  filter: define(\"instance/filter\", [\n    \"es.array.filter\",\n    \"esnext.async-iterator.filter\",\n    \"esnext.iterator.filter\",\n    ...IteratorDependencies,\n  ]),\n  filterReject: define(\"instance/filterReject\", [\"esnext.array.filter-reject\"]),\n  finally: define(null, [\"es.promise.finally\", ...PromiseDependencies]),\n  find: define(\"instance/find\", [\n    \"es.array.find\",\n    \"esnext.async-iterator.find\",\n    \"esnext.iterator.find\",\n    ...IteratorDependencies,\n  ]),\n  findIndex: define(\"instance/find-index\", [\"es.array.find-index\"]),\n  findLast: define(\"instance/find-last\", [\"es.array.find-last\"]),\n  findLastIndex: define(\"instance/find-last-index\", [\n    \"es.array.find-last-index\",\n  ]),\n  fixed: define(null, [\"es.string.fixed\"]),\n  flags: define(\"instance/flags\", [\"es.regexp.flags\"]),\n  flatMap: define(\"instance/flat-map\", [\n    \"es.array.flat-map\",\n    \"es.array.unscopables.flat-map\",\n    \"esnext.async-iterator.flat-map\",\n    \"esnext.iterator.flat-map\",\n    ...IteratorDependencies,\n  ]),\n  flat: define(\"instance/flat\", [\"es.array.flat\", \"es.array.unscopables.flat\"]),\n  getYear: define(null, [\"es.date.get-year\"]),\n  group: define(\"instance/group\", [\"esnext.array.group\"]),\n  groupBy: define(\"instance/group-by\", [\"esnext.array.group-by\"]),\n  groupByToMap: define(\"instance/group-by-to-map\", [\n    \"esnext.array.group-by-to-map\",\n    \"es.map\",\n    \"es.object.to-string\",\n  ]),\n  groupToMap: define(\"instance/group-to-map\", [\n    \"esnext.array.group-to-map\",\n    \"es.map\",\n    \"es.object.to-string\",\n  ]),\n  fontcolor: define(null, [\"es.string.fontcolor\"]),\n  fontsize: define(null, [\"es.string.fontsize\"]),\n  forEach: define(\"instance/for-each\", [\n    \"es.array.for-each\",\n    \"esnext.async-iterator.for-each\",\n    \"esnext.iterator.for-each\",\n    ...IteratorDependencies,\n    \"web.dom-collections.for-each\",\n  ]),\n  includes: define(\"instance/includes\", [\n    \"es.array.includes\",\n    \"es.string.includes\",\n  ]),\n  indexed: define(\"instance/indexed\", [\n    \"esnext.async-iterator.indexed\",\n    ...AsyncIteratorDependencies,\n    \"esnext.iterator.indexed\",\n    ...IteratorDependencies,\n  ]),\n  indexOf: define(\"instance/index-of\", [\"es.array.index-of\"]),\n  isWellFormed: define(\"instance/is-well-formed\", [\n    \"esnext.string.is-well-formed\",\n  ]),\n  italic: define(null, [\"es.string.italics\"]),\n  join: define(null, [\"es.array.join\"]),\n  keys: define(\"instance/keys\", ArrayNatureIteratorsWithTag),\n  lastIndex: define(null, [\"esnext.array.last-index\"]),\n  lastIndexOf: define(\"instance/last-index-of\", [\"es.array.last-index-of\"]),\n  lastItem: define(null, [\"esnext.array.last-item\"]),\n  link: define(null, [\"es.string.link\"]),\n  map: define(\"instance/map\", [\n    \"es.array.map\",\n    \"esnext.async-iterator.map\",\n    \"esnext.iterator.map\",\n  ]),\n  match: define(null, [\"es.string.match\", \"es.regexp.exec\"]),\n  matchAll: define(\"instance/match-all\", [\n    \"es.string.match-all\",\n    \"es.regexp.exec\",\n  ]),\n  name: define(null, [\"es.function.name\"]),\n  padEnd: define(\"instance/pad-end\", [\"es.string.pad-end\"]),\n  padStart: define(\"instance/pad-start\", [\"es.string.pad-start\"]),\n  push: define(\"instance/push\", [\"es.array.push\"]),\n  reduce: define(\"instance/reduce\", [\n    \"es.array.reduce\",\n    \"esnext.async-iterator.reduce\",\n    \"esnext.iterator.reduce\",\n    ...IteratorDependencies,\n  ]),\n  reduceRight: define(\"instance/reduce-right\", [\"es.array.reduce-right\"]),\n  repeat: define(\"instance/repeat\", [\"es.string.repeat\"]),\n  replace: define(null, [\"es.string.replace\", \"es.regexp.exec\"]),\n  replaceAll: define(\"instance/replace-all\", [\n    \"es.string.replace-all\",\n    \"es.string.replace\",\n    \"es.regexp.exec\",\n  ]),\n  reverse: define(\"instance/reverse\", [\"es.array.reverse\"]),\n  search: define(null, [\"es.string.search\", \"es.regexp.exec\"]),\n  setYear: define(null, [\"es.date.set-year\"]),\n  slice: define(\"instance/slice\", [\"es.array.slice\"]),\n  small: define(null, [\"es.string.small\"]),\n  some: define(\"instance/some\", [\n    \"es.array.some\",\n    \"esnext.async-iterator.some\",\n    \"esnext.iterator.some\",\n    ...IteratorDependencies,\n  ]),\n  sort: define(\"instance/sort\", [\"es.array.sort\"]),\n  splice: define(\"instance/splice\", [\"es.array.splice\"]),\n  split: define(null, [\"es.string.split\", \"es.regexp.exec\"]),\n  startsWith: define(\"instance/starts-with\", [\"es.string.starts-with\"]),\n  sticky: define(null, [\"es.regexp.sticky\"]),\n  strike: define(null, [\"es.string.strike\"]),\n  sub: define(null, [\"es.string.sub\"]),\n  substr: define(null, [\"es.string.substr\"]),\n  sup: define(null, [\"es.string.sup\"]),\n  take: define(\"instance/take\", [\n    \"esnext.async-iterator.take\",\n    ...AsyncIteratorDependencies,\n    \"esnext.iterator.take\",\n    ...IteratorDependencies,\n  ]),\n  test: define(null, [\"es.regexp.test\", \"es.regexp.exec\"]),\n  toArray: define(\"instance/to-array\", [\n    \"esnext.async-iterator.to-array\",\n    ...AsyncIteratorDependencies,\n    \"esnext.iterator.to-array\",\n    ...IteratorDependencies,\n  ]),\n  toAsync: define(null, [\n    \"esnext.iterator.to-async\",\n    ...IteratorDependencies,\n    ...AsyncIteratorDependencies,\n    ...AsyncIteratorProblemMethods,\n  ]),\n  toExponential: define(null, [\"es.number.to-exponential\"]),\n  toFixed: define(null, [\"es.number.to-fixed\"]),\n  toGMTString: define(null, [\"es.date.to-gmt-string\"]),\n  toISOString: define(null, [\"es.date.to-iso-string\"]),\n  toJSON: define(null, [\"es.date.to-json\", \"web.url.to-json\"]),\n  toPrecision: define(null, [\"es.number.to-precision\"]),\n  toReversed: define(\"instance/to-reversed\", [\"es.array.to-reversed\"]),\n  toSorted: define(\"instance/to-sorted\", [\n    \"es.array.to-sorted\",\n    \"es.array.sort\",\n  ]),\n  toSpliced: define(\"instance/to-spliced\", [\"es.array.to-spliced\"]),\n  toString: define(null, [\n    \"es.object.to-string\",\n    \"es.error.to-string\",\n    \"es.date.to-string\",\n    \"es.regexp.to-string\",\n  ]),\n  toWellFormed: define(\"instance/to-well-formed\", [\n    \"esnext.string.to-well-formed\",\n  ]),\n  trim: define(\"instance/trim\", [\"es.string.trim\"]),\n  trimEnd: define(\"instance/trim-end\", [\"es.string.trim-end\"]),\n  trimLeft: define(\"instance/trim-left\", [\"es.string.trim-start\"]),\n  trimRight: define(\"instance/trim-right\", [\"es.string.trim-end\"]),\n  trimStart: define(\"instance/trim-start\", [\"es.string.trim-start\"]),\n  uniqueBy: define(\"instance/unique-by\", [\"esnext.array.unique-by\", \"es.map\"]),\n  unshift: define(\"instance/unshift\", [\"es.array.unshift\"]),\n  unThis: define(\"instance/un-this\", [\"esnext.function.un-this\"]),\n  values: define(\"instance/values\", ArrayNatureIteratorsWithTag),\n  with: define(\"instance/with\", [\"es.array.with\"]),\n  __defineGetter__: define(null, [\"es.object.define-getter\"]),\n  __defineSetter__: define(null, [\"es.object.define-setter\"]),\n  __lookupGetter__: define(null, [\"es.object.lookup-getter\"]),\n  __lookupSetter__: define(null, [\"es.object.lookup-setter\"]),\n  [\"__proto__\"]: define(null, [\"es.object.proto\"]),\n};\n", "import type { CoreJSPolyfillDescriptor } from \"./built-in-definitions\";\nimport { types as t, type NodePath } from \"@babel/core\";\n\nexport default function canSkipPolyfill(\n  desc: CoreJSPolyfillDescriptor,\n  path: NodePath,\n) {\n  const { node, parent } = path;\n  switch (desc.name) {\n    case \"es.string.split\": {\n      if (!t.isCallExpression(parent, { callee: node })) return false;\n      if (parent.arguments.length < 1) return true;\n      const splitter = parent.arguments[0];\n      return t.isStringLiteral(splitter) || t.isTemplateLiteral(splitter);\n    }\n  }\n}\n", "import { types as t } from \"@babel/core\";\nimport corejsEntries from \"../core-js-compat/entries.js\";\n\nexport const BABEL_RUNTIME = \"@babel/runtime-corejs3\";\n\nexport function callMethod(path: any, id: t.Identifier) {\n  const { object } = path.node;\n\n  let context1, context2;\n  if (t.isIdentifier(object)) {\n    context1 = object;\n    context2 = t.cloneNode(object);\n  } else {\n    context1 = path.scope.generateDeclaredUidIdentifier(\"context\");\n    context2 = t.assignmentExpression(\"=\", t.cloneNode(context1), object);\n  }\n\n  path.replaceWith(\n    t.memberExpression(t.callExpression(id, [context2]), t.identifier(\"call\")),\n  );\n\n  path.parentPath.unshiftContainer(\"arguments\", context1);\n}\n\nexport function isCoreJSSource(source: string) {\n  if (typeof source === \"string\") {\n    source = source\n      .replace(/\\\\/g, \"/\")\n      .replace(/(\\/(index)?)?(\\.js)?$/i, \"\")\n      .toLowerCase();\n  }\n\n  return (\n    Object.prototype.hasOwnProperty.call(corejsEntries, source) &&\n    corejsEntries[source]\n  );\n}\n\nexport function coreJSModule(name: string) {\n  return `core-js/modules/${name}.js`;\n}\n\nexport function coreJSPureHelper(\n  name: string,\n  useBabelRuntime: boolean,\n  ext: string,\n) {\n  return useBabelRuntime\n    ? `${BABEL_RUNTIME}/core-js/${name}${ext}`\n    : `core-js-pure/features/${name}.js`;\n}\n", "import corejs3Polyfills from \"../core-js-compat/data.js\";\nimport corejs3ShippedProposalsList from \"./shipped-proposals\";\nimport getModulesListForTargetVersion from \"../core-js-compat/get-modules-list-for-target-version.js\";\nimport {\n  BuiltIns,\n  CommonIterators,\n  PromiseDependencies,\n  PromiseDependenciesWithIterators,\n  StaticProperties,\n  InstanceProperties,\n  type CoreJSPolyfillDescriptor,\n} from \"./built-in-definitions\";\nimport canSkipPolyfill from \"./usage-filters\";\n\nimport type { NodePath } from \"@babel/traverse\";\nimport { types as t } from \"@babel/core\";\nimport {\n  callMethod,\n  coreJSModule,\n  isCoreJSSource,\n  coreJSPureHelper,\n  BABEL_RUNTIME,\n} from \"./utils\";\n\nimport defineProvider from \"@babel/helper-define-polyfill-provider\";\n\nconst presetEnvCompat = \"#__secret_key__@babel/preset-env__compatibility\";\nconst runtimeCompat = \"#__secret_key__@babel/runtime__compatibility\";\n\ntype Options = {\n  version?: number | string;\n  proposals?: boolean;\n  shippedProposals?: boolean;\n  [presetEnvCompat]?: { noRuntimeName: boolean };\n  [runtimeCompat]: {\n    useBabelRuntime: boolean;\n    babelRuntimePath: string;\n    ext: string;\n  };\n};\n\nconst uniqueObjects = [\n  \"array\",\n  \"string\",\n\n  \"iterator\",\n  \"async-iterator\",\n  \"dom-collections\",\n].map(v => new RegExp(`[a-z]*\\\\.${v}\\\\..*`));\n\nconst esnextFallback = (\n  name: string,\n  cb: (name: string) => boolean,\n): boolean => {\n  if (cb(name)) return true;\n  if (!name.startsWith(\"es.\")) return false;\n  const fallback = `esnext.${name.slice(3)}`;\n  if (!corejs3Polyfills[fallback]) return false;\n  return cb(fallback);\n};\n\nexport default defineProvider<Options>(function (\n  { getUtils, method, shouldInjectPolyfill, createMetaResolver, debug, babel },\n  {\n    version = 3,\n    proposals,\n    shippedProposals,\n    [presetEnvCompat]: { noRuntimeName = false } = {},\n    [runtimeCompat]: { useBabelRuntime = false, ext = \".js\" } = {},\n  },\n) {\n  const isWebpack = babel.caller(caller => caller?.name === \"babel-loader\");\n\n  const resolve = createMetaResolver({\n    global: BuiltIns,\n    static: StaticProperties,\n    instance: InstanceProperties,\n  });\n\n  const available = new Set(getModulesListForTargetVersion(version));\n\n  function getCoreJSPureBase(useProposalBase) {\n    return useBabelRuntime\n      ? useProposalBase\n        ? `${BABEL_RUNTIME}/core-js`\n        : `${BABEL_RUNTIME}/core-js-stable`\n      : useProposalBase\n      ? \"core-js-pure/features\"\n      : \"core-js-pure/stable\";\n  }\n\n  function maybeInjectGlobalImpl(name: string, utils) {\n    if (shouldInjectPolyfill(name)) {\n      debug(name);\n      utils.injectGlobalImport(coreJSModule(name));\n      return true;\n    }\n    return false;\n  }\n\n  function maybeInjectGlobal(names: string[], utils, fallback = true) {\n    for (const name of names) {\n      if (fallback) {\n        esnextFallback(name, name => maybeInjectGlobalImpl(name, utils));\n      } else {\n        maybeInjectGlobalImpl(name, utils);\n      }\n    }\n  }\n\n  function maybeInjectPure(\n    desc: CoreJSPolyfillDescriptor,\n    hint,\n    utils,\n    object?,\n  ) {\n    if (\n      desc.pure &&\n      !(object && desc.exclude && desc.exclude.includes(object)) &&\n      esnextFallback(desc.name, shouldInjectPolyfill)\n    ) {\n      const { name } = desc;\n      let useProposalBase = false;\n      if (proposals || (shippedProposals && name.startsWith(\"esnext.\"))) {\n        useProposalBase = true;\n      } else if (name.startsWith(\"es.\") && !available.has(name)) {\n        useProposalBase = true;\n      }\n      const coreJSPureBase = getCoreJSPureBase(useProposalBase);\n      return utils.injectDefaultImport(\n        `${coreJSPureBase}/${desc.pure}${ext}`,\n        hint,\n      );\n    }\n  }\n\n  function isFeatureStable(name) {\n    if (name.startsWith(\"esnext.\")) {\n      const esName = `es.${name.slice(7)}`;\n      // If its imaginative esName is not in latest compat data, it means\n      // the proposal is not stage 4\n      return esName in corejs3Polyfills;\n    }\n    return true;\n  }\n\n  return {\n    name: \"corejs3\",\n\n    runtimeName: noRuntimeName ? null : BABEL_RUNTIME,\n\n    polyfills: corejs3Polyfills,\n\n    filterPolyfills(name) {\n      if (!available.has(name)) return false;\n      if (proposals || method === \"entry-global\") return true;\n      if (shippedProposals && corejs3ShippedProposalsList.has(name)) {\n        return true;\n      }\n      return isFeatureStable(name);\n    },\n\n    entryGlobal(meta, utils, path) {\n      if (meta.kind !== \"import\") return;\n\n      const modules = isCoreJSSource(meta.source);\n      if (!modules) return;\n\n      if (\n        modules.length === 1 &&\n        meta.source === coreJSModule(modules[0]) &&\n        shouldInjectPolyfill(modules[0])\n      ) {\n        // Avoid infinite loop: do not replace imports with a new copy of\n        // themselves.\n        debug(null);\n        return;\n      }\n\n      const modulesSet = new Set(modules);\n      const filteredModules = modules.filter(module => {\n        if (!module.startsWith(\"esnext.\")) return true;\n        const stable = module.replace(\"esnext.\", \"es.\");\n        if (modulesSet.has(stable) && shouldInjectPolyfill(stable)) {\n          return false;\n        }\n        return true;\n      });\n\n      maybeInjectGlobal(filteredModules, utils, false);\n      path.remove();\n    },\n\n    usageGlobal(meta, utils, path) {\n      const resolved = resolve(meta);\n      if (!resolved) return;\n\n      if (canSkipPolyfill(resolved.desc, path)) return;\n\n      let deps = resolved.desc.global;\n\n      if (\n        resolved.kind !== \"global\" &&\n        \"object\" in meta &&\n        meta.object &&\n        meta.placement === \"prototype\"\n      ) {\n        const low = meta.object.toLowerCase();\n        deps = deps.filter(m =>\n          uniqueObjects.some(v => v.test(m)) ? m.includes(low) : true,\n        );\n      }\n\n      maybeInjectGlobal(deps, utils);\n    },\n\n    usagePure(meta, utils, path) {\n      if (meta.kind === \"in\") {\n        if (meta.key === \"Symbol.iterator\") {\n          path.replaceWith(\n            t.callExpression(\n              utils.injectDefaultImport(\n                coreJSPureHelper(\"is-iterable\", useBabelRuntime, ext),\n                \"isIterable\",\n              ),\n              [(path.node as t.BinaryExpression).right], // meta.kind === \"in\" narrows this\n            ),\n          );\n        }\n        return;\n      }\n\n      if (path.parentPath.isUnaryExpression({ operator: \"delete\" })) return;\n\n      if (meta.kind === \"property\") {\n        // We can't compile destructuring and updateExpression.\n        if (!path.isMemberExpression()) return;\n        if (!path.isReferenced()) return;\n        if (path.parentPath.isUpdateExpression()) return;\n        if (t.isSuper(path.node.object)) {\n          return;\n        }\n\n        if (meta.key === \"Symbol.iterator\") {\n          if (!shouldInjectPolyfill(\"es.symbol.iterator\")) return;\n\n          const { parent, node } = path;\n          if (t.isCallExpression(parent, { callee: node })) {\n            if (parent.arguments.length === 0) {\n              path.parentPath.replaceWith(\n                t.callExpression(\n                  utils.injectDefaultImport(\n                    coreJSPureHelper(\"get-iterator\", useBabelRuntime, ext),\n                    \"getIterator\",\n                  ),\n                  [node.object],\n                ),\n              );\n              path.skip();\n            } else {\n              callMethod(\n                path,\n                utils.injectDefaultImport(\n                  coreJSPureHelper(\"get-iterator-method\", useBabelRuntime, ext),\n                  \"getIteratorMethod\",\n                ),\n              );\n            }\n          } else {\n            path.replaceWith(\n              t.callExpression(\n                utils.injectDefaultImport(\n                  coreJSPureHelper(\"get-iterator-method\", useBabelRuntime, ext),\n                  \"getIteratorMethod\",\n                ),\n                [path.node.object],\n              ),\n            );\n          }\n\n          return;\n        }\n      }\n\n      let resolved = resolve(meta);\n      if (!resolved) return;\n\n      if (canSkipPolyfill(resolved.desc, path)) return;\n\n      if (\n        useBabelRuntime &&\n        resolved.desc.pure &&\n        resolved.desc.pure.slice(-6) === \"/index\"\n      ) {\n        // Remove /index, since it doesn't exist in @babel/runtime-corejs3s\n        resolved = {\n          ...resolved,\n          desc: {\n            ...resolved.desc,\n            pure: resolved.desc.pure.slice(0, -6),\n          },\n        };\n      }\n\n      if (resolved.kind === \"global\") {\n        const id = maybeInjectPure(resolved.desc, resolved.name, utils);\n        if (id) path.replaceWith(id);\n      } else if (resolved.kind === \"static\") {\n        const id = maybeInjectPure(\n          resolved.desc,\n          resolved.name,\n          utils,\n          // @ts-expect-error\n          meta.object,\n        );\n        if (id) path.replaceWith(id);\n      } else if (resolved.kind === \"instance\") {\n        const id = maybeInjectPure(\n          resolved.desc,\n          `${resolved.name}InstanceProperty`,\n          utils,\n          // @ts-expect-error\n          meta.object,\n        );\n        if (!id) return;\n\n        const { node } = path as NodePath<t.MemberExpression>;\n        if (t.isCallExpression(path.parent, { callee: node })) {\n          callMethod(path, id);\n        } else {\n          path.replaceWith(t.callExpression(id, [node.object]));\n        }\n      }\n    },\n\n    visitor: method === \"usage-global\" && {\n      // import(\"foo\")\n      CallExpression(path: NodePath<t.CallExpression>) {\n        if (path.get(\"callee\").isImport()) {\n          const utils = getUtils(path);\n\n          if (isWebpack) {\n            // Webpack uses Promise.all to handle dynamic import.\n            maybeInjectGlobal(PromiseDependenciesWithIterators, utils);\n          } else {\n            maybeInjectGlobal(PromiseDependencies, utils);\n          }\n        }\n      },\n\n      // (async function () { }).finally(...)\n      Function(path: NodePath<t.Function>) {\n        if (path.node.async) {\n          maybeInjectGlobal(PromiseDependencies, getUtils(path));\n        }\n      },\n\n      // for-of, [a, b] = c\n      \"ForOfStatement|ArrayPattern\"(\n        path: NodePath<t.ForOfStatement | t.ArrayPattern>,\n      ) {\n        maybeInjectGlobal(CommonIterators, getUtils(path));\n      },\n\n      // [...spread]\n      SpreadElement(path: NodePath<t.SpreadElement>) {\n        if (!path.parentPath.isObjectExpression()) {\n          maybeInjectGlobal(CommonIterators, getUtils(path));\n        }\n      },\n\n      // yield*\n      YieldExpression(path: NodePath<t.YieldExpression>) {\n        if (path.node.delegate) {\n          maybeInjectGlobal(CommonIterators, getUtils(path));\n        }\n      },\n    },\n  };\n});\n"], "names": ["Set", "polyfillsOrder", "Object", "keys", "corejs3Polyfills", "for<PERSON>ach", "name", "index", "define", "pure", "global", "exclude", "sort", "a", "b", "typed", "TypedArrayDependencies", "ArrayNatureIterators", "CommonIterators", "ArrayNatureIteratorsWithTag", "CommonIteratorsWithTag", "ErrorDependencies", "SuppressedErrorDependencies", "PromiseDependencies", "PromiseDependenciesWithIterators", "SymbolDependencies", "MapDependencies", "SetDependencies", "WeakMapDependencies", "WeakSetDependencies", "DOMExceptionDependencies", "URLSearchParamsDependencies", "AsyncIteratorDependencies", "AsyncIteratorProblemMethods", "IteratorDependencies", "TypedArrayStaticMethods", "from", "fromAsync", "of", "BuiltIns", "AsyncDisposableStack", "AsyncIterator", "AggregateError", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DataView", "Date", "DOMException", "DisposableStack", "Error", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Float32Array", "Float64Array", "Int8Array", "Int16Array", "Int32Array", "Iterator", "Uint8Array", "Uint8ClampedArray", "Uint16Array", "Uint32Array", "Map", "Number", "Observable", "Promise", "RangeError", "ReferenceError", "Reflect", "RegExp", "SuppressedError", "Symbol", "SyntaxError", "TypeError", "URIError", "URL", "URLSearchParams", "WeakMap", "WeakSet", "atob", "btoa", "clearImmediate", "compositeKey", "compositeSymbol", "escape", "fetch", "globalThis", "parseFloat", "parseInt", "queueMicrotask", "self", "setImmediate", "setInterval", "setTimeout", "structuredClone", "unescape", "StaticProperties", "Array", "isArray", "isTemplateObject", "<PERSON><PERSON><PERSON><PERSON>", "BigInt", "range", "now", "Function", "isCallable", "isConstructor", "JSON", "isRawJSON", "parse", "rawJSON", "stringify", "Math", "DEG_PER_RAD", "RAD_PER_DEG", "acosh", "asinh", "atanh", "cbrt", "clamp", "clz32", "cosh", "degrees", "expm1", "fround", "fscale", "hypot", "iaddh", "imul", "imulh", "<PERSON><PERSON><PERSON>", "log10", "log1p", "log2", "radians", "scale", "seededPRNG", "sign", "signbit", "sinh", "tanh", "trunc", "umulh", "groupBy", "keyBy", "EPSILON", "MAX_SAFE_INTEGER", "MIN_SAFE_INTEGER", "fromString", "isFinite", "isInteger", "isNaN", "isSafeInteger", "assign", "create", "defineProperties", "defineProperty", "entries", "freeze", "fromEntries", "getOwnPropertyDescriptor", "getOwnPropertyDescriptors", "getOwnPropertyNames", "getOwnPropertySymbols", "getPrototypeOf", "hasOwn", "is", "isExtensible", "isFrozen", "isSealed", "preventExtensions", "seal", "setPrototypeOf", "values", "all", "allSettled", "any", "race", "try", "apply", "construct", "defineMetadata", "deleteMetadata", "deleteProperty", "get", "getMetadata", "getMetadataKeys", "getOwnMetadata", "getOwnMetadataKeys", "has", "hasMetadata", "hasOwnMetadata", "metadata", "ownKeys", "set", "String", "cooked", "dedent", "fromCodePoint", "raw", "asyncDispose", "asyncIterator", "dispose", "for", "hasInstance", "isConcatSpreadable", "isRegistered", "isWellKnown", "iterator", "keyFor", "match", "matcher", "matchAll", "metadataKey", "observable", "patternMatch", "replace", "search", "species", "split", "toPrimitive", "toStringTag", "unscopables", "canParse", "WebAssembly", "CompileError", "LinkError", "RuntimeError", "InstanceProperties", "asIndexedPairs", "at", "anchor", "big", "bind", "blink", "bold", "codePointAt", "codePoints", "concat", "undefined", "copyWithin", "demethodize", "description", "dotAll", "drop", "emplace", "endsWith", "every", "exec", "fill", "filter", "filterReject", "finally", "find", "findIndex", "findLast", "findLastIndex", "fixed", "flags", "flatMap", "flat", "getYear", "group", "groupByToMap", "groupToMap", "fontcolor", "fontsize", "includes", "indexed", "indexOf", "isWellFormed", "italic", "join", "lastIndex", "lastIndexOf", "lastItem", "link", "map", "padEnd", "padStart", "push", "reduce", "reduceRight", "repeat", "replaceAll", "reverse", "setYear", "slice", "small", "some", "splice", "startsWith", "sticky", "strike", "sub", "substr", "sup", "take", "test", "toArray", "to<PERSON><PERSON>", "toExponential", "toFixed", "toGMTString", "toISOString", "toJSON", "toPrecision", "toReversed", "toSorted", "toSpliced", "toString", "to<PERSON><PERSON><PERSON><PERSON><PERSON>", "trim", "trimEnd", "trimLeft", "trimRight", "trimStart", "uniqueBy", "unshift", "unThis", "with", "__defineGetter__", "__defineSetter__", "__lookupGetter__", "__lookupSetter__", "types", "t", "canSkipPolyfill", "desc", "path", "node", "parent", "isCallExpression", "callee", "arguments", "length", "splitter", "isStringLiteral", "isTemplateLiteral", "BABEL_RUNTIME", "callMethod", "id", "object", "context1", "context2", "isIdentifier", "cloneNode", "scope", "generateDeclaredUidIdentifier", "assignmentExpression", "replaceWith", "memberExpression", "callExpression", "identifier", "parentPath", "unshiftContainer", "isCoreJSSource", "source", "toLowerCase", "prototype", "hasOwnProperty", "call", "corejsEntries", "coreJSModule", "coreJSPureHelper", "useBabelRuntime", "ext", "presetEnvCompat", "runtimeCompat", "uniqueObjects", "v", "esnextFallback", "cb", "fallback", "define<PERSON>rovider", "getUtils", "method", "shouldInjectPolyfill", "createMetaResolver", "debug", "babel", "version", "proposals", "shippedProposals", "noRuntimeName", "isWebpack", "caller", "resolve", "static", "instance", "available", "getModulesListForTargetVersion", "getCoreJSPureBase", "useProposalBase", "maybeInjectGlobalImpl", "utils", "injectGlobalImport", "maybeInjectGlobal", "names", "maybeInjectPure", "hint", "coreJSPureBase", "injectDefaultImport", "isFeatureStable", "esName", "runtimeName", "polyfills", "filterPolyfills", "corejs3ShippedProposalsList", "entryGlobal", "meta", "kind", "modules", "modulesSet", "filteredModules", "module", "stable", "remove", "usageGlobal", "resolved", "deps", "placement", "low", "m", "usagePure", "key", "right", "isUnaryExpression", "operator", "isMemberExpression", "isReferenced", "isUpdateExpression", "is<PERSON><PERSON><PERSON>", "skip", "visitor", "CallExpression", "isImport", "async", "SpreadElement", "isObjectExpression", "YieldExpression", "delegate"], "mappings": ";;;;;;AAAA;AAEA,kCAAe,IAAIA,GAAJ,CAAgB,CAC7B,yBAD6B,EAE7B,oBAF6B,EAG7B,2BAH6B,EAI7B,8BAJ6B,EAK7B,8BAL6B,EAM7B,8CAN6B,EAO7B,yBAP6B,EAQ7B,mBAR6B,EAS7B,sBAT6B,EAU7B,0BAV6B,EAW7B,4BAX6B,EAY7B,gCAZ6B,EAa7B,4BAb6B,EAc7B,8BAd6B,EAe7B,oCAf6B,EAgB7B,qBAhB6B,EAiB7B,8BAjB6B,EAkB7B,8BAlB6B,CAAhB,CAAf;;ACUA,MAAMC,cAAc,GAAG,EAAvB;AACAC,MAAM,CAACC,IAAP,CAAYC,gBAAZ,EAA8BC,OAA9B,CAAsC,CAACC,IAAD,EAAOC,KAAP,KAAiB;EACrDN,cAAc,CAACK,IAAD,CAAd,GAAuBC,KAAvB;AACD,CAFD;;AAIA,MAAMC,MAAM,GAAG,CACbC,IADa,EAEbC,MAFa,EAGbJ,IAAI,GAAGI,MAAM,CAAC,CAAD,CAHA,EAIbC,OAJa,KAKgB;EAC7B,OAAO;IACLL,IADK;IAELG,IAFK;IAGLC,MAAM,EAAEA,MAAM,CAACE,IAAP,CAAY,CAACC,CAAD,EAAIC,CAAJ,KAAUb,cAAc,CAACY,CAAD,CAAd,GAAoBZ,cAAc,CAACa,CAAD,CAAxD,CAHH;IAILH;GAJF;AAMD,CAZD;;AAcA,MAAMI,KAAK,GAAIT,IAAD,IAAkBE,MAAM,CAAC,IAAD,EAAO,CAACF,IAAD,EAAO,GAAGU,sBAAV,CAAP,CAAtC;;AAEA,MAAMC,oBAAoB,GAAG,CAC3B,mBAD2B,EAE3B,8BAF2B,CAA7B;AAKO,MAAMC,eAAe,GAAG,CAAC,oBAAD,EAAuB,GAAGD,oBAA1B,CAAxB;AAEP,MAAME,2BAA2B,GAAG,CAClC,qBADkC,EAElC,GAAGF,oBAF+B,CAApC;AAKA,MAAMG,sBAAsB,GAAG,CAAC,qBAAD,EAAwB,GAAGF,eAA3B,CAA/B;AAEA,MAAMG,iBAAiB,GAAG,CAAC,gBAAD,EAAmB,oBAAnB,CAA1B;AAEA,MAAMC,2BAA2B,GAAG,CAClC,qCADkC,EAElC,GAAGD,iBAF+B,CAApC;AAKA,MAAML,sBAAsB,GAAG,CAC7B,mBAD6B,EAE7B,4BAF6B,EAG7B,sBAH6B,EAI7B,qBAJ6B,EAK7B,uBAL6B,EAM7B,qBAN6B,EAO7B,2BAP6B,EAQ7B,0BAR6B,EAS7B,gCAT6B,EAU7B,yBAV6B,EAW7B,yBAX6B,EAY7B,yBAZ6B,EAa7B,yBAb6B,EAc7B,qBAd6B,EAe7B,8BAf6B,EAgB7B,oBAhB6B,EAiB7B,uBAjB6B,EAkB7B,6BAlB6B,EAmB7B,wBAnB6B,EAoB7B,oBApB6B,EAqB7B,sBArB6B,EAsB7B,qBAtB6B,EAuB7B,qBAvB6B,EAwB7B,yBAxB6B,EAyB7B,iCAzB6B,EA0B7B,4BA1B6B,EA2B7B,0BA3B6B,EA4B7B,0BA5B6B,EA6B7B,qBA7B6B,EA8B7B,qBA9B6B,EA+B7B,mBA/B6B,EAgC7B,uBAhC6B,EAiC7B,8BAjC6B,EAkC7B,8BAlC6B,EAmC7B,8CAnC6B,EAoC7B,kCApC6B,EAqC7B,6BArC6B,EAsC7B,+BAtC6B,EAuC7B,8BAvC6B,CAA/B;AA0CO,MAAMO,mBAAmB,GAAG,CAAC,YAAD,EAAe,qBAAf,CAA5B;AAEA,MAAMC,gCAAgC,GAAG,CAC9C,GAAGD,mBAD2C,EAE9C,GAAGL,eAF2C,CAAzC;AAKP,MAAMO,kBAAkB,GAAG,CACzB,WADyB,EAEzB,uBAFyB,EAGzB,qBAHyB,CAA3B;AAMA,MAAMC,eAAe,GAAG,CACtB,QADsB,EAEtB,uBAFsB,EAGtB,oBAHsB,EAItB,kBAJsB,EAKtB,mBALsB,EAMtB,iBANsB,EAOtB,qBAPsB,EAQtB,qBARsB,EAStB,mBATsB,EAUtB,qBAVsB,EAWtB,uBAXsB,EAYtB,kBAZsB,EAatB,mBAbsB,EActB,iBAdsB,EAetB,mBAfsB,EAgBtB,GAAGN,sBAhBmB,CAAxB;AAmBA,MAAMO,eAAe,GAAG,CACtB,QADsB,EAEtB,oBAFsB,EAGtB,uBAHsB,EAItB,uBAJsB,EAKtB,0BALsB,EAMtB,kBANsB,EAOtB,mBAPsB,EAQtB,iBARsB,EAStB,yBATsB,EAUtB,4BAVsB,EAWtB,6BAXsB,EAYtB,gCAZsB,EAatB,yBAbsB,EActB,4BAdsB,EAetB,2BAfsB,EAgBtB,8BAhBsB,EAiBtB,iBAjBsB,EAkBtB,gBAlBsB,EAmBtB,mBAnBsB,EAoBtB,iBApBsB,EAqBtB,iCArBsB,EAsBtB,oCAtBsB,EAuBtB,kBAvBsB,EAwBtB,qBAxBsB,EAyBtB,GAAGP,sBAzBmB,CAAxB;AA4BA,MAAMQ,mBAAmB,GAAG,CAC1B,aAD0B,EAE1B,4BAF0B,EAG1B,yBAH0B,EAI1B,GAAGR,sBAJuB,CAA5B;AAOA,MAAMS,mBAAmB,GAAG,CAC1B,aAD0B,EAE1B,yBAF0B,EAG1B,4BAH0B,EAI1B,GAAGT,sBAJuB,CAA5B;AAOA,MAAMU,wBAAwB,GAAG,CAC/B,+BAD+B,EAE/B,yBAF+B,EAG/B,iCAH+B,EAI/B,oBAJ+B,CAAjC;AAOA,MAAMC,2BAA2B,GAAG,CAClC,uBADkC,EAElC,4BAFkC,EAGlC,GAAGX,sBAH+B,CAApC;AAMA,MAAMY,yBAAyB,GAAG,CAChC,mCADgC,EAEhC,GAAGT,mBAF6B,CAAlC;AAKA,MAAMU,2BAA2B,GAAG,CAClC,6BADkC,EAElC,8BAFkC,EAGlC,4BAHkC,EAIlC,gCAJkC,EAKlC,gCALkC,EAMlC,2BANkC,EAOlC,8BAPkC,EAQlC,4BARkC,CAApC;AAWA,MAAMC,oBAAoB,GAAG,CAC3B,6BAD2B,EAE3B,qBAF2B,CAA7B;AAKA,MAAMC,uBAAuB,GAAG;EAC9BC,IAAI,EAAE5B,MAAM,CAAC,IAAD,EAAO,CAAC,qBAAD,CAAP,CADkB;EAE9B6B,SAAS,EAAE7B,MAAM,CAAC,IAAD,EAAO,CACtB,+BADsB,EAEtB,GAAGgB,gCAFmB,CAAP,CAFa;EAM9Bc,EAAE,EAAE9B,MAAM,CAAC,IAAD,EAAO,CAAC,mBAAD,CAAP;AANoB,CAAhC;AASO,MAAM+B,QAA6C,GAAG;EAC3DC,oBAAoB,EAAEhC,MAAM,CAAC,wBAAD,EAA2B,CACrD,2CADqD,EAErD,qBAFqD,EAGrD,qCAHqD,EAIrD,yBAJqD,EAKrD,GAAGe,mBALkD,EAMrD,GAAGD,2BANkD,CAA3B,CAD+B;EAS3DmB,aAAa,EAAEjC,MAAM,CAAC,sBAAD,EAAyBwB,yBAAzB,CATsC;EAU3DU,cAAc,EAAElC,MAAM,CAAC,iBAAD,EAAoB,CACxC,oBADwC,EAExC,GAAGa,iBAFqC,EAGxC,GAAGD,sBAHqC,EAIxC,0BAJwC,CAApB,CAVqC;EAgB3DuB,WAAW,EAAEnC,MAAM,CAAC,IAAD,EAAO,CACxB,6BADwB,EAExB,uBAFwB,EAGxB,qBAHwB,CAAP,CAhBwC;EAqB3DoC,QAAQ,EAAEpC,MAAM,CAAC,IAAD,EAAO,CACrB,cADqB,EAErB,uBAFqB,EAGrB,qBAHqB,CAAP,CArB2C;EA0B3DqC,IAAI,EAAErC,MAAM,CAAC,IAAD,EAAO,CAAC,mBAAD,CAAP,CA1B+C;EA2B3DsC,YAAY,EAAEtC,MAAM,CAAC,eAAD,EAAkBsB,wBAAlB,CA3BuC;EA4B3DiB,eAAe,EAAEvC,MAAM,CAAC,kBAAD,EAAqB,CAC1C,qCAD0C,EAE1C,qBAF0C,EAG1C,yBAH0C,EAI1C,GAAGc,2BAJuC,CAArB,CA5BoC;EAkC3D0B,KAAK,EAAExC,MAAM,CAAC,IAAD,EAAOa,iBAAP,CAlC8C;EAmC3D4B,SAAS,EAAEzC,MAAM,CAAC,IAAD,EAAOa,iBAAP,CAnC0C;EAoC3D6B,YAAY,EAAEnC,KAAK,CAAC,8BAAD,CApCwC;EAqC3DoC,YAAY,EAAEpC,KAAK,CAAC,8BAAD,CArCwC;EAsC3DqC,SAAS,EAAErC,KAAK,CAAC,2BAAD,CAtC2C;EAuC3DsC,UAAU,EAAEtC,KAAK,CAAC,4BAAD,CAvC0C;EAwC3DuC,UAAU,EAAEvC,KAAK,CAAC,4BAAD,CAxC0C;EAyC3DwC,QAAQ,EAAE/C,MAAM,CAAC,gBAAD,EAAmB0B,oBAAnB,CAzC2C;EA0C3DsB,UAAU,EAAEzC,KAAK,CAAC,4BAAD,CA1C0C;EA2C3D0C,iBAAiB,EAAE1C,KAAK,CAAC,oCAAD,CA3CmC;EA4C3D2C,WAAW,EAAE3C,KAAK,CAAC,6BAAD,CA5CyC;EA6C3D4C,WAAW,EAAE5C,KAAK,CAAC,6BAAD,CA7CyC;EA8C3D6C,GAAG,EAAEpD,MAAM,CAAC,WAAD,EAAckB,eAAd,CA9CgD;EA+C3DmC,MAAM,EAAErD,MAAM,CAAC,IAAD,EAAO,CAAC,uBAAD,CAAP,CA/C6C;EAgD3DsD,UAAU,EAAEtD,MAAM,CAAC,kBAAD,EAAqB,CACrC,mBADqC,EAErC,0BAFqC,EAGrC,qBAHqC,EAIrC,GAAGY,sBAJkC,CAArB,CAhDyC;EAsD3D2C,OAAO,EAAEvD,MAAM,CAAC,eAAD,EAAkBe,mBAAlB,CAtD4C;EAuD3DyC,UAAU,EAAExD,MAAM,CAAC,IAAD,EAAOa,iBAAP,CAvDyC;EAwD3D4C,cAAc,EAAEzD,MAAM,CAAC,IAAD,EAAOa,iBAAP,CAxDqC;EAyD3D6C,OAAO,EAAE1D,MAAM,CAAC,IAAD,EAAO,CAAC,0BAAD,EAA6B,qBAA7B,CAAP,CAzD4C;EA0D3D2D,MAAM,EAAE3D,MAAM,CAAC,IAAD,EAAO,CACnB,uBADmB,EAEnB,mBAFmB,EAGnB,gBAHmB,EAInB,kBAJmB,EAKnB,qBALmB,CAAP,CA1D6C;EAiE3DR,GAAG,EAAEQ,MAAM,CAAC,WAAD,EAAcmB,eAAd,CAjEgD;EAkE3DyC,eAAe,EAAE5D,MAAM,CAAC,kBAAD,EAAqBc,2BAArB,CAlEoC;EAmE3D+C,MAAM,EAAE7D,MAAM,CAAC,cAAD,EAAiBiB,kBAAjB,CAnE6C;EAoE3D6C,WAAW,EAAE9D,MAAM,CAAC,IAAD,EAAOa,iBAAP,CApEwC;EAqE3DkD,SAAS,EAAE/D,MAAM,CAAC,IAAD,EAAOa,iBAAP,CArE0C;EAsE3DmD,QAAQ,EAAEhE,MAAM,CAAC,IAAD,EAAOa,iBAAP,CAtE2C;EAuE3DoD,GAAG,EAAEjE,MAAM,CAAC,WAAD,EAAc,CAAC,SAAD,EAAY,GAAGuB,2BAAf,CAAd,CAvEgD;EAwE3D2C,eAAe,EAAElE,MAAM,CACrB,yBADqB,EAErBuB,2BAFqB,CAxEoC;EA4E3D4C,OAAO,EAAEnE,MAAM,CAAC,gBAAD,EAAmBoB,mBAAnB,CA5E4C;EA6E3DgD,OAAO,EAAEpE,MAAM,CAAC,gBAAD,EAAmBqB,mBAAnB,CA7E4C;EA+E3DgD,IAAI,EAAErE,MAAM,CAAC,MAAD,EAAS,CAAC,UAAD,EAAa,GAAGsB,wBAAhB,CAAT,CA/E+C;EAgF3DgD,IAAI,EAAEtE,MAAM,CAAC,MAAD,EAAS,CAAC,UAAD,EAAa,GAAGsB,wBAAhB,CAAT,CAhF+C;EAiF3DiD,cAAc,EAAEvE,MAAM,CAAC,iBAAD,EAAoB,CAAC,eAAD,CAApB,CAjFqC;EAkF3DwE,YAAY,EAAExE,MAAM,CAAC,eAAD,EAAkB,CAAC,sBAAD,CAAlB,CAlFuC;EAmF3DyE,eAAe,EAAEzE,MAAM,CAAC,kBAAD,EAAqB,CAAC,yBAAD,CAArB,CAnFoC;EAoF3D0E,MAAM,EAAE1E,MAAM,CAAC,QAAD,EAAW,CAAC,WAAD,CAAX,CApF6C;EAqF3D2E,KAAK,EAAE3E,MAAM,CAAC,IAAD,EAAOe,mBAAP,CArF8C;EAsF3D6D,UAAU,EAAE5E,MAAM,CAAC,aAAD,EAAgB,CAAC,gBAAD,CAAhB,CAtFyC;EAuF3D6E,UAAU,EAAE7E,MAAM,CAAC,aAAD,EAAgB,CAAC,gBAAD,CAAhB,CAvFyC;EAwF3D8E,QAAQ,EAAE9E,MAAM,CAAC,WAAD,EAAc,CAAC,cAAD,CAAd,CAxF2C;EAyF3D+E,cAAc,EAAE/E,MAAM,CAAC,iBAAD,EAAoB,CAAC,qBAAD,CAApB,CAzFqC;EA0F3DgF,IAAI,EAAEhF,MAAM,CAAC,MAAD,EAAS,CAAC,UAAD,CAAT,CA1F+C;EA2F3DiF,YAAY,EAAEjF,MAAM,CAAC,eAAD,EAAkB,CAAC,eAAD,CAAlB,CA3FuC;EA4F3DkF,WAAW,EAAElF,MAAM,CAAC,cAAD,EAAiB,CAAC,YAAD,CAAjB,CA5FwC;EA6F3DmF,UAAU,EAAEnF,MAAM,CAAC,aAAD,EAAgB,CAAC,YAAD,CAAhB,CA7FyC;EA8F3DoF,eAAe,EAAEpF,MAAM,CAAC,kBAAD,EAAqB,CAC1C,sBAD0C,EAE1C,GAAGsB,wBAFuC,EAG1C,mBAH0C,EAI1C,gBAJ0C,EAK1C,qBAL0C,EAM1C,QAN0C,EAO1C,QAP0C,CAArB,CA9FoC;EAuG3D+D,QAAQ,EAAErF,MAAM,CAAC,UAAD,EAAa,CAAC,aAAD,CAAb;AAvG2C,CAAtD;AA0GA,MAAMsF,gBAAsD,GAAG;EACpErD,aAAa,EAAE;IACbL,IAAI,EAAE5B,MAAM,CAAC,qBAAD,EAAwB,CAClC,4BADkC,EAElC,GAAGwB,yBAF+B,EAGlC,GAAGC,2BAH+B,EAIlC,GAAGf,eAJ+B,CAAxB;GAFsD;EASpE6E,KAAK,EAAE;IACL3D,IAAI,EAAE5B,MAAM,CAAC,YAAD,EAAe,CAAC,eAAD,EAAkB,oBAAlB,CAAf,CADP;IAEL6B,SAAS,EAAE7B,MAAM,CAAC,kBAAD,EAAqB,CACpC,yBADoC,EAEpC,GAAGgB,gCAFiC,CAArB,CAFZ;IAMLwE,OAAO,EAAExF,MAAM,CAAC,gBAAD,EAAmB,CAAC,mBAAD,CAAnB,CANV;IAOLyF,gBAAgB,EAAEzF,MAAM,CAAC,0BAAD,EAA6B,CACnD,iCADmD,CAA7B,CAPnB;IAUL8B,EAAE,EAAE9B,MAAM,CAAC,UAAD,EAAa,CAAC,aAAD,CAAb;GAnBwD;EAsBpEmC,WAAW,EAAE;IACXuD,MAAM,EAAE1F,MAAM,CAAC,IAAD,EAAO,CAAC,yBAAD,CAAP;GAvBoD;EA0BpE2F,MAAM,EAAE;IACNC,KAAK,EAAE5F,MAAM,CAAC,cAAD,EAAiB,CAC5B,qBAD4B,EAE5B,qBAF4B,CAAjB;GA3BqD;EAiCpEqC,IAAI,EAAE;IACJwD,GAAG,EAAE7F,MAAM,CAAC,UAAD,EAAa,CAAC,aAAD,CAAb;GAlCuD;EAqCpE8F,QAAQ,EAAE;IACRC,UAAU,EAAE/F,MAAM,CAAC,sBAAD,EAAyB,CAAC,6BAAD,CAAzB,CADV;IAERgG,aAAa,EAAEhG,MAAM,CAAC,yBAAD,EAA4B,CAC/C,gCAD+C,CAA5B;GAvC6C;EA4CpE+C,QAAQ,EAAE;IACRnB,IAAI,EAAE5B,MAAM,CAAC,eAAD,EAAkB,CAC5B,sBAD4B,EAE5B,GAAG0B,oBAFyB,EAG5B,GAAGhB,eAHyB,CAAlB,CADJ;IAMRkF,KAAK,EAAE5F,MAAM,CAAC,gBAAD,EAAmB,CAC9B,uBAD8B,EAE9B,qBAF8B,CAAnB;GAlDqD;EAwDpEiG,IAAI,EAAE;IACJC,SAAS,EAAElG,MAAM,CAAC,kBAAD,EAAqB,CAAC,yBAAD,CAArB,CADb;IAEJmG,KAAK,EAAEnG,MAAM,CAAC,YAAD,EAAe,CAAC,mBAAD,EAAsB,gBAAtB,CAAf,CAFT;IAGJoG,OAAO,EAAEpG,MAAM,CAAC,eAAD,EAAkB,CAC/B,sBAD+B,EAE/B,kBAF+B,EAG/B,kBAH+B,CAAlB,CAHX;IAQJqG,SAAS,EAAErG,MAAM,CAAC,gBAAD,EAAmB,CAAC,mBAAD,CAAnB,EAA0C,WAA1C;GAhEiD;EAmEpEsG,IAAI,EAAE;IACJC,WAAW,EAAEvG,MAAM,CAAC,kBAAD,EAAqB,CAAC,yBAAD,CAArB,CADf;IAEJwG,WAAW,EAAExG,MAAM,CAAC,kBAAD,EAAqB,CAAC,yBAAD,CAArB,CAFf;IAGJyG,KAAK,EAAEzG,MAAM,CAAC,YAAD,EAAe,CAAC,eAAD,CAAf,CAHT;IAIJ0G,KAAK,EAAE1G,MAAM,CAAC,YAAD,EAAe,CAAC,eAAD,CAAf,CAJT;IAKJ2G,KAAK,EAAE3G,MAAM,CAAC,YAAD,EAAe,CAAC,eAAD,CAAf,CALT;IAMJ4G,IAAI,EAAE5G,MAAM,CAAC,WAAD,EAAc,CAAC,cAAD,CAAd,CANR;IAOJ6G,KAAK,EAAE7G,MAAM,CAAC,YAAD,EAAe,CAAC,mBAAD,CAAf,CAPT;IAQJ8G,KAAK,EAAE9G,MAAM,CAAC,YAAD,EAAe,CAAC,eAAD,CAAf,CART;IASJ+G,IAAI,EAAE/G,MAAM,CAAC,WAAD,EAAc,CAAC,cAAD,CAAd,CATR;IAUJgH,OAAO,EAAEhH,MAAM,CAAC,cAAD,EAAiB,CAAC,qBAAD,CAAjB,CAVX;IAWJiH,KAAK,EAAEjH,MAAM,CAAC,YAAD,EAAe,CAAC,eAAD,CAAf,CAXT;IAYJkH,MAAM,EAAElH,MAAM,CAAC,aAAD,EAAgB,CAAC,gBAAD,CAAhB,CAZV;IAaJmH,MAAM,EAAEnH,MAAM,CAAC,aAAD,EAAgB,CAAC,oBAAD,CAAhB,CAbV;IAcJoH,KAAK,EAAEpH,MAAM,CAAC,YAAD,EAAe,CAAC,eAAD,CAAf,CAdT;IAeJqH,KAAK,EAAErH,MAAM,CAAC,YAAD,EAAe,CAAC,mBAAD,CAAf,CAfT;IAgBJsH,IAAI,EAAEtH,MAAM,CAAC,WAAD,EAAc,CAAC,cAAD,CAAd,CAhBR;IAiBJuH,KAAK,EAAEvH,MAAM,CAAC,YAAD,EAAe,CAAC,mBAAD,CAAf,CAjBT;IAkBJwH,KAAK,EAAExH,MAAM,CAAC,YAAD,EAAe,CAAC,mBAAD,CAAf,CAlBT;IAmBJyH,KAAK,EAAEzH,MAAM,CAAC,YAAD,EAAe,CAAC,eAAD,CAAf,CAnBT;IAoBJ0H,KAAK,EAAE1H,MAAM,CAAC,YAAD,EAAe,CAAC,eAAD,CAAf,CApBT;IAqBJ2H,IAAI,EAAE3H,MAAM,CAAC,WAAD,EAAc,CAAC,cAAD,CAAd,CArBR;IAsBJ4H,OAAO,EAAE5H,MAAM,CAAC,cAAD,EAAiB,CAAC,qBAAD,CAAjB,CAtBX;IAuBJ6H,KAAK,EAAE7H,MAAM,CAAC,YAAD,EAAe,CAAC,mBAAD,CAAf,CAvBT;IAwBJ8H,UAAU,EAAE9H,MAAM,CAAC,kBAAD,EAAqB,CAAC,yBAAD,CAArB,CAxBd;IAyBJ+H,IAAI,EAAE/H,MAAM,CAAC,WAAD,EAAc,CAAC,cAAD,CAAd,CAzBR;IA0BJgI,OAAO,EAAEhI,MAAM,CAAC,cAAD,EAAiB,CAAC,qBAAD,CAAjB,CA1BX;IA2BJiI,IAAI,EAAEjI,MAAM,CAAC,WAAD,EAAc,CAAC,cAAD,CAAd,CA3BR;IA4BJkI,IAAI,EAAElI,MAAM,CAAC,WAAD,EAAc,CAAC,cAAD,CAAd,CA5BR;IA6BJmI,KAAK,EAAEnI,MAAM,CAAC,YAAD,EAAe,CAAC,eAAD,CAAf,CA7BT;IA8BJoI,KAAK,EAAEpI,MAAM,CAAC,YAAD,EAAe,CAAC,mBAAD,CAAf;GAjGqD;EAoGpEoD,GAAG,EAAE;IACHxB,IAAI,EAAE5B,MAAM,CAAC,IAAD,EAAO,CAAC,iBAAD,EAAoB,GAAGkB,eAAvB,CAAP,CADT;IAEHmH,OAAO,EAAErI,MAAM,CAAC,IAAD,EAAO,CAAC,qBAAD,EAAwB,GAAGkB,eAA3B,CAAP,CAFZ;IAGHoH,KAAK,EAAEtI,MAAM,CAAC,IAAD,EAAO,CAAC,mBAAD,EAAsB,GAAGkB,eAAzB,CAAP,CAHV;IAIHY,EAAE,EAAE9B,MAAM,CAAC,IAAD,EAAO,CAAC,eAAD,EAAkB,GAAGkB,eAArB,CAAP;GAxGwD;EA2GpEmC,MAAM,EAAE;IACNkF,OAAO,EAAEvI,MAAM,CAAC,gBAAD,EAAmB,CAAC,mBAAD,CAAnB,CADT;IAENwI,gBAAgB,EAAExI,MAAM,CAAC,yBAAD,EAA4B,CAClD,4BADkD,CAA5B,CAFlB;IAKNyI,gBAAgB,EAAEzI,MAAM,CAAC,yBAAD,EAA4B,CAClD,4BADkD,CAA5B,CALlB;IAQN0I,UAAU,EAAE1I,MAAM,CAAC,oBAAD,EAAuB,CAAC,2BAAD,CAAvB,CARZ;IASN2I,QAAQ,EAAE3I,MAAM,CAAC,kBAAD,EAAqB,CAAC,qBAAD,CAArB,CATV;IAUN4I,SAAS,EAAE5I,MAAM,CAAC,mBAAD,EAAsB,CAAC,sBAAD,CAAtB,CAVX;IAWN6I,KAAK,EAAE7I,MAAM,CAAC,eAAD,EAAkB,CAAC,kBAAD,CAAlB,CAXP;IAYN8I,aAAa,EAAE9I,MAAM,CAAC,wBAAD,EAA2B,CAC9C,2BAD8C,CAA3B,CAZf;IAeN6E,UAAU,EAAE7E,MAAM,CAAC,oBAAD,EAAuB,CAAC,uBAAD,CAAvB,CAfZ;IAgBN8E,QAAQ,EAAE9E,MAAM,CAAC,kBAAD,EAAqB,CAAC,qBAAD,CAArB,CAhBV;IAiBN4F,KAAK,EAAE5F,MAAM,CAAC,cAAD,EAAiB,CAC5B,qBAD4B,EAE5B,qBAF4B,CAAjB;GA5HqD;EAkIpEN,MAAM,EAAE;IACNqJ,MAAM,EAAE/I,MAAM,CAAC,eAAD,EAAkB,CAAC,kBAAD,CAAlB,CADR;IAENgJ,MAAM,EAAEhJ,MAAM,CAAC,eAAD,EAAkB,CAAC,kBAAD,CAAlB,CAFR;IAGNiJ,gBAAgB,EAAEjJ,MAAM,CAAC,0BAAD,EAA6B,CACnD,6BADmD,CAA7B,CAHlB;IAMNkJ,cAAc,EAAElJ,MAAM,CAAC,wBAAD,EAA2B,CAC/C,2BAD+C,CAA3B,CANhB;IASNmJ,OAAO,EAAEnJ,MAAM,CAAC,gBAAD,EAAmB,CAAC,mBAAD,CAAnB,CATT;IAUNoJ,MAAM,EAAEpJ,MAAM,CAAC,eAAD,EAAkB,CAAC,kBAAD,CAAlB,CAVR;IAWNqJ,WAAW,EAAErJ,MAAM,CAAC,qBAAD,EAAwB,CACzC,wBADyC,EAEzC,mBAFyC,CAAxB,CAXb;IAeNsJ,wBAAwB,EAAEtJ,MAAM,CAAC,oCAAD,EAAuC,CACrE,uCADqE,CAAvC,CAf1B;IAkBNuJ,yBAAyB,EAAEvJ,MAAM,CAAC,qCAAD,EAAwC,CACvE,wCADuE,CAAxC,CAlB3B;IAqBNwJ,mBAAmB,EAAExJ,MAAM,CAAC,+BAAD,EAAkC,CAC3D,kCAD2D,CAAlC,CArBrB;IAwBNyJ,qBAAqB,EAAEzJ,MAAM,CAAC,iCAAD,EAAoC,CAC/D,WAD+D,CAApC,CAxBvB;IA2BN0J,cAAc,EAAE1J,MAAM,CAAC,yBAAD,EAA4B,CAChD,4BADgD,CAA5B,CA3BhB;IA8BN2J,MAAM,EAAE3J,MAAM,CAAC,gBAAD,EAAmB,CAAC,mBAAD,CAAnB,CA9BR;IA+BN4J,EAAE,EAAE5J,MAAM,CAAC,WAAD,EAAc,CAAC,cAAD,CAAd,CA/BJ;IAgCN6J,YAAY,EAAE7J,MAAM,CAAC,sBAAD,EAAyB,CAAC,yBAAD,CAAzB,CAhCd;IAiCN8J,QAAQ,EAAE9J,MAAM,CAAC,kBAAD,EAAqB,CAAC,qBAAD,CAArB,CAjCV;IAkCN+J,QAAQ,EAAE/J,MAAM,CAAC,kBAAD,EAAqB,CAAC,qBAAD,CAArB,CAlCV;IAmCNL,IAAI,EAAEK,MAAM,CAAC,aAAD,EAAgB,CAAC,gBAAD,CAAhB,CAnCN;IAoCNgK,iBAAiB,EAAEhK,MAAM,CAAC,2BAAD,EAA8B,CACrD,8BADqD,CAA9B,CApCnB;IAuCNiK,IAAI,EAAEjK,MAAM,CAAC,aAAD,EAAgB,CAAC,gBAAD,CAAhB,CAvCN;IAwCNkK,cAAc,EAAElK,MAAM,CAAC,yBAAD,EAA4B,CAChD,4BADgD,CAA5B,CAxChB;IA2CNmK,MAAM,EAAEnK,MAAM,CAAC,eAAD,EAAkB,CAAC,kBAAD,CAAlB;GA7KoD;EAgLpEuD,OAAO,EAAE;IACP6G,GAAG,EAAEpK,MAAM,CAAC,IAAD,EAAOgB,gCAAP,CADJ;IAEPqJ,UAAU,EAAErK,MAAM,CAAC,IAAD,EAAO,CACvB,wBADuB,EAEvB,GAAGgB,gCAFoB,CAAP,CAFX;IAMPsJ,GAAG,EAAEtK,MAAM,CAAC,IAAD,EAAO,CAChB,gBADgB,EAEhB,oBAFgB,EAGhB,GAAGgB,gCAHa,CAAP,CANJ;IAWPuJ,IAAI,EAAEvK,MAAM,CAAC,IAAD,EAAOgB,gCAAP,CAXL;IAYPwJ,GAAG,EAAExK,MAAM,CAAC,IAAD,EAAO,CAChB,oBADgB,EAEhB,GAAGgB,gCAFa,CAAP;GA5LuD;EAkMpE0C,OAAO,EAAE;IACP+G,KAAK,EAAEzK,MAAM,CAAC,eAAD,EAAkB,CAAC,kBAAD,CAAlB,CADN;IAEP0K,SAAS,EAAE1K,MAAM,CAAC,mBAAD,EAAsB,CAAC,sBAAD,CAAtB,CAFV;IAGP2K,cAAc,EAAE3K,MAAM,CAAC,yBAAD,EAA4B,CAChD,gCADgD,CAA5B,CAHf;IAMPkJ,cAAc,EAAElJ,MAAM,CAAC,yBAAD,EAA4B,CAChD,4BADgD,CAA5B,CANf;IASP4K,cAAc,EAAE5K,MAAM,CAAC,yBAAD,EAA4B,CAChD,gCADgD,CAA5B,CATf;IAYP6K,cAAc,EAAE7K,MAAM,CAAC,yBAAD,EAA4B,CAChD,4BADgD,CAA5B,CAZf;IAeP8K,GAAG,EAAE9K,MAAM,CAAC,aAAD,EAAgB,CAAC,gBAAD,CAAhB,CAfJ;IAgBP+K,WAAW,EAAE/K,MAAM,CAAC,sBAAD,EAAyB,CAC1C,6BAD0C,CAAzB,CAhBZ;IAmBPgL,eAAe,EAAEhL,MAAM,CAAC,2BAAD,EAA8B,CACnD,kCADmD,CAA9B,CAnBhB;IAsBPiL,cAAc,EAAEjL,MAAM,CAAC,0BAAD,EAA6B,CACjD,iCADiD,CAA7B,CAtBf;IAyBPkL,kBAAkB,EAAElL,MAAM,CAAC,+BAAD,EAAkC,CAC1D,sCAD0D,CAAlC,CAzBnB;IA4BPsJ,wBAAwB,EAAEtJ,MAAM,CAAC,qCAAD,EAAwC,CACtE,wCADsE,CAAxC,CA5BzB;IA+BP0J,cAAc,EAAE1J,MAAM,CAAC,0BAAD,EAA6B,CACjD,6BADiD,CAA7B,CA/Bf;IAkCPmL,GAAG,EAAEnL,MAAM,CAAC,aAAD,EAAgB,CAAC,gBAAD,CAAhB,CAlCJ;IAmCPoL,WAAW,EAAEpL,MAAM,CAAC,sBAAD,EAAyB,CAC1C,6BAD0C,CAAzB,CAnCZ;IAsCPqL,cAAc,EAAErL,MAAM,CAAC,0BAAD,EAA6B,CACjD,iCADiD,CAA7B,CAtCf;IAyCP6J,YAAY,EAAE7J,MAAM,CAAC,uBAAD,EAA0B,CAAC,0BAAD,CAA1B,CAzCb;IA0CPsL,QAAQ,EAAEtL,MAAM,CAAC,kBAAD,EAAqB,CAAC,yBAAD,CAArB,CA1CT;IA2CPuL,OAAO,EAAEvL,MAAM,CAAC,kBAAD,EAAqB,CAAC,qBAAD,CAArB,CA3CR;IA4CPgK,iBAAiB,EAAEhK,MAAM,CAAC,4BAAD,EAA+B,CACtD,+BADsD,CAA/B,CA5ClB;IA+CPwL,GAAG,EAAExL,MAAM,CAAC,aAAD,EAAgB,CAAC,gBAAD,CAAhB,CA/CJ;IAgDPkK,cAAc,EAAElK,MAAM,CAAC,0BAAD,EAA6B,CACjD,6BADiD,CAA7B;GAlP4C;EAuPpER,GAAG,EAAE;IACHoC,IAAI,EAAE5B,MAAM,CAAC,IAAD,EAAO,CAAC,iBAAD,EAAoB,GAAGmB,eAAvB,CAAP,CADT;IAEHW,EAAE,EAAE9B,MAAM,CAAC,IAAD,EAAO,CAAC,eAAD,EAAkB,GAAGmB,eAArB,CAAP;GAzPwD;EA4PpEsK,MAAM,EAAE;IACNC,MAAM,EAAE1L,MAAM,CAAC,eAAD,EAAkB,CAAC,sBAAD,CAAlB,CADR;IAEN2L,MAAM,EAAE3L,MAAM,CAAC,eAAD,EAAkB,CAC9B,sBAD8B,EAE9B,2BAF8B,EAG9B,aAH8B,CAAlB,CAFR;IAON4L,aAAa,EAAE5L,MAAM,CAAC,wBAAD,EAA2B,CAC9C,2BAD8C,CAA3B,CAPf;IAUN6L,GAAG,EAAE7L,MAAM,CAAC,YAAD,EAAe,CAAC,eAAD,CAAf;GAtQuD;EAyQpE6D,MAAM,EAAE;IACNiI,YAAY,EAAE9L,MAAM,CAAC,sBAAD,EAAyB,CAC3C,6BAD2C,EAE3C,qCAF2C,CAAzB,CADd;IAKN+L,aAAa,EAAE/L,MAAM,CAAC,uBAAD,EAA0B,CAC7C,0BAD6C,CAA1B,CALf;IAQNgM,OAAO,EAAEhM,MAAM,CAAC,gBAAD,EAAmB,CAChC,uBADgC,EAEhC,yBAFgC,CAAnB,CART;IAYNiM,GAAG,EAAEjM,MAAM,CAAC,YAAD,EAAe,EAAf,EAAmB,WAAnB,CAZL;IAaNkM,WAAW,EAAElM,MAAM,CAAC,qBAAD,EAAwB,CACzC,wBADyC,EAEzC,0BAFyC,CAAxB,CAbb;IAiBNmM,kBAAkB,EAAEnM,MAAM,CAAC,6BAAD,EAAgC,CACxD,gCADwD,EAExD,iBAFwD,CAAhC,CAjBpB;IAqBNoM,YAAY,EAAEpM,MAAM,CAAC,sBAAD,EAAyB,CAC3C,6BAD2C,EAE3C,WAF2C,CAAzB,CArBd;IAyBNqM,WAAW,EAAErM,MAAM,CAAC,sBAAD,EAAyB,CAC1C,6BAD0C,EAE1C,WAF0C,CAAzB,CAzBb;IA6BNsM,QAAQ,EAAEtM,MAAM,CAAC,iBAAD,EAAoB,CAClC,oBADkC,EAElC,GAAGY,sBAF+B,CAApB,CA7BV;IAiCN2L,MAAM,EAAEvM,MAAM,CAAC,gBAAD,EAAmB,EAAnB,EAAuB,WAAvB,CAjCR;IAkCNwM,KAAK,EAAExM,MAAM,CAAC,cAAD,EAAiB,CAAC,iBAAD,EAAoB,iBAApB,CAAjB,CAlCP;IAmCNyM,OAAO,EAAEzM,MAAM,CAAC,gBAAD,EAAmB,CAAC,uBAAD,CAAnB,CAnCT;IAoCN0M,QAAQ,EAAE1M,MAAM,CAAC,kBAAD,EAAqB,CACnC,qBADmC,EAEnC,qBAFmC,CAArB,CApCV;IAwCNsL,QAAQ,EAAEtL,MAAM,CAAC,iBAAD,EAAoB,CAAC,wBAAD,CAApB,CAxCV;IAyCN2M,WAAW,EAAE3M,MAAM,CAAC,qBAAD,EAAwB,CAAC,4BAAD,CAAxB,CAzCb;IA0CN4M,UAAU,EAAE5M,MAAM,CAAC,mBAAD,EAAsB,CAAC,0BAAD,CAAtB,CA1CZ;IA2CN6M,YAAY,EAAE7M,MAAM,CAAC,sBAAD,EAAyB,CAC3C,6BAD2C,CAAzB,CA3Cd;IA8CN8M,OAAO,EAAE9M,MAAM,CAAC,gBAAD,EAAmB,CAChC,mBADgC,EAEhC,mBAFgC,CAAnB,CA9CT;IAkDN+M,MAAM,EAAE/M,MAAM,CAAC,eAAD,EAAkB,CAAC,kBAAD,EAAqB,kBAArB,CAAlB,CAlDR;IAmDNgN,OAAO,EAAEhN,MAAM,CAAC,gBAAD,EAAmB,CAChC,mBADgC,EAEhC,kBAFgC,CAAnB,CAnDT;IAuDNiN,KAAK,EAAEjN,MAAM,CAAC,cAAD,EAAiB,CAAC,iBAAD,EAAoB,iBAApB,CAAjB,CAvDP;IAwDNkN,WAAW,EAAElN,MAAM,CAAC,qBAAD,EAAwB,CACzC,wBADyC,EAEzC,sBAFyC,CAAxB,CAxDb;IA4DNmN,WAAW,EAAEnN,MAAM,CAAC,sBAAD,EAAyB,CAC1C,yBAD0C,EAE1C,qBAF0C,EAG1C,uBAH0C,EAI1C,uBAJ0C,CAAzB,CA5Db;IAkENoN,WAAW,EAAEpN,MAAM,CAAC,oBAAD,EAAuB,CAAC,uBAAD,CAAvB;GA3U+C;EA8UpEiE,GAAG,EAAE;IACHoJ,QAAQ,EAAErN,MAAM,CAAC,eAAD,EAAkB,CAAC,mBAAD,EAAsB,SAAtB,CAAlB;GA/UkD;EAkVpEmE,OAAO,EAAE;IACPvC,IAAI,EAAE5B,MAAM,CAAC,IAAD,EAAO,CAAC,sBAAD,EAAyB,GAAGoB,mBAA5B,CAAP,CADL;IAEPU,EAAE,EAAE9B,MAAM,CAAC,IAAD,EAAO,CAAC,oBAAD,EAAuB,GAAGoB,mBAA1B,CAAP;GApVwD;EAuVpEgD,OAAO,EAAE;IACPxC,IAAI,EAAE5B,MAAM,CAAC,IAAD,EAAO,CAAC,sBAAD,EAAyB,GAAGqB,mBAA5B,CAAP,CADL;IAEPS,EAAE,EAAE9B,MAAM,CAAC,IAAD,EAAO,CAAC,oBAAD,EAAuB,GAAGqB,mBAA1B,CAAP;GAzVwD;EA4VpEuB,SAAS,EAAEjB,uBA5VyD;EA6VpEqB,UAAU,EAAErB,uBA7VwD;EA8VpEsB,iBAAiB,EAAEtB,uBA9ViD;EA+VpEkB,UAAU,EAAElB,uBA/VwD;EAgWpEuB,WAAW,EAAEvB,uBAhWuD;EAiWpEmB,UAAU,EAAEnB,uBAjWwD;EAkWpEwB,WAAW,EAAExB,uBAlWuD;EAmWpEe,YAAY,EAAEf,uBAnWsD;EAoWpEgB,YAAY,EAAEhB,uBApWsD;EAsWpE2L,WAAW,EAAE;IACXC,YAAY,EAAEvN,MAAM,CAAC,IAAD,EAAOa,iBAAP,CADT;IAEX2M,SAAS,EAAExN,MAAM,CAAC,IAAD,EAAOa,iBAAP,CAFN;IAGX4M,YAAY,EAAEzN,MAAM,CAAC,IAAD,EAAOa,iBAAP;;AAzW8C,CAA/D;AA6WA,MAAM6M,kBAAkB,GAAG;EAChCC,cAAc,EAAE3N,MAAM,CAAC,yBAAD,EAA4B,CAChD,wCADgD,EAEhD,GAAGwB,yBAF6C,EAGhD,kCAHgD,EAIhD,GAAGE,oBAJ6C,CAA5B,CADU;EAOhCkM,EAAE,EAAE5N,MAAM,CAAC,aAAD,EAAgB;;;;;;EAOxB,kBAPwB,EAQxB,0BARwB,EASxB,aATwB,CAAhB,CAPsB;EAkBhC6N,MAAM,EAAE7N,MAAM,CAAC,IAAD,EAAO,CAAC,kBAAD,CAAP,CAlBkB;EAmBhC8N,GAAG,EAAE9N,MAAM,CAAC,IAAD,EAAO,CAAC,eAAD,CAAP,CAnBqB;EAoBhC+N,IAAI,EAAE/N,MAAM,CAAC,eAAD,EAAkB,CAAC,kBAAD,CAAlB,CApBoB;EAqBhCgO,KAAK,EAAEhO,MAAM,CAAC,IAAD,EAAO,CAAC,iBAAD,CAAP,CArBmB;EAsBhCiO,IAAI,EAAEjO,MAAM,CAAC,IAAD,EAAO,CAAC,gBAAD,CAAP,CAtBoB;EAuBhCkO,WAAW,EAAElO,MAAM,CAAC,wBAAD,EAA2B,CAAC,yBAAD,CAA3B,CAvBa;EAwBhCmO,UAAU,EAAEnO,MAAM,CAAC,sBAAD,EAAyB,CAAC,2BAAD,CAAzB,CAxBc;EAyBhCoO,MAAM,EAAEpO,MAAM,CAAC,iBAAD,EAAoB,CAAC,iBAAD,CAApB,EAAyCqO,SAAzC,EAAoD,CAAC,QAAD,CAApD,CAzBkB;EA0BhCC,UAAU,EAAEtO,MAAM,CAAC,sBAAD,EAAyB,CAAC,sBAAD,CAAzB,CA1Bc;EA2BhCuO,WAAW,EAAEvO,MAAM,CAAC,sBAAD,EAAyB,CAAC,6BAAD,CAAzB,CA3Ba;EA4BhCwO,WAAW,EAAExO,MAAM,CAAC,IAAD,EAAO,CAAC,WAAD,EAAc,uBAAd,CAAP,CA5Ba;EA6BhCyO,MAAM,EAAEzO,MAAM,CAAC,IAAD,EAAO,CAAC,mBAAD,CAAP,CA7BkB;EA8BhC0O,IAAI,EAAE1O,MAAM,CAAC,eAAD,EAAkB,CAC5B,4BAD4B,EAE5B,GAAGwB,yBAFyB,EAG5B,sBAH4B,EAI5B,GAAGE,oBAJyB,CAAlB,CA9BoB;EAoChCiN,OAAO,EAAE3O,MAAM,CAAC,kBAAD,EAAqB,CAClC,oBADkC,EAElC,yBAFkC,CAArB,CApCiB;EAwChC4O,QAAQ,EAAE5O,MAAM,CAAC,oBAAD,EAAuB,CAAC,qBAAD,CAAvB,CAxCgB;EAyChCmJ,OAAO,EAAEnJ,MAAM,CAAC,kBAAD,EAAqBW,2BAArB,CAzCiB;EA0ChCkO,KAAK,EAAE7O,MAAM,CAAC,gBAAD,EAAmB,CAC9B,gBAD8B,EAE9B,6BAF8B;;;;;EAQ9B,uBAR8B,EAS9B,GAAG0B,oBAT2B,CAAnB,CA1CmB;EAqDhCoN,IAAI,EAAE9O,MAAM,CAAC,IAAD,EAAO,CAAC,gBAAD,CAAP,CArDoB;EAsDhC+O,IAAI,EAAE/O,MAAM,CAAC,eAAD,EAAkB,CAAC,eAAD,CAAlB,CAtDoB;EAuDhCgP,MAAM,EAAEhP,MAAM,CAAC,iBAAD,EAAoB,CAChC,iBADgC,EAEhC,8BAFgC,EAGhC,wBAHgC,EAIhC,GAAG0B,oBAJ6B,CAApB,CAvDkB;EA6DhCuN,YAAY,EAAEjP,MAAM,CAAC,uBAAD,EAA0B,CAAC,4BAAD,CAA1B,CA7DY;EA8DhCkP,OAAO,EAAElP,MAAM,CAAC,IAAD,EAAO,CAAC,oBAAD,EAAuB,GAAGe,mBAA1B,CAAP,CA9DiB;EA+DhCoO,IAAI,EAAEnP,MAAM,CAAC,eAAD,EAAkB,CAC5B,eAD4B,EAE5B,4BAF4B,EAG5B,sBAH4B,EAI5B,GAAG0B,oBAJyB,CAAlB,CA/DoB;EAqEhC0N,SAAS,EAAEpP,MAAM,CAAC,qBAAD,EAAwB,CAAC,qBAAD,CAAxB,CArEe;EAsEhCqP,QAAQ,EAAErP,MAAM,CAAC,oBAAD,EAAuB,CAAC,oBAAD,CAAvB,CAtEgB;EAuEhCsP,aAAa,EAAEtP,MAAM,CAAC,0BAAD,EAA6B,CAChD,0BADgD,CAA7B,CAvEW;EA0EhCuP,KAAK,EAAEvP,MAAM,CAAC,IAAD,EAAO,CAAC,iBAAD,CAAP,CA1EmB;EA2EhCwP,KAAK,EAAExP,MAAM,CAAC,gBAAD,EAAmB,CAAC,iBAAD,CAAnB,CA3EmB;EA4EhCyP,OAAO,EAAEzP,MAAM,CAAC,mBAAD,EAAsB,CACnC,mBADmC,EAEnC,+BAFmC,EAGnC,gCAHmC,EAInC,0BAJmC,EAKnC,GAAG0B,oBALgC,CAAtB,CA5EiB;EAmFhCgO,IAAI,EAAE1P,MAAM,CAAC,eAAD,EAAkB,CAAC,eAAD,EAAkB,2BAAlB,CAAlB,CAnFoB;EAoFhC2P,OAAO,EAAE3P,MAAM,CAAC,IAAD,EAAO,CAAC,kBAAD,CAAP,CApFiB;EAqFhC4P,KAAK,EAAE5P,MAAM,CAAC,gBAAD,EAAmB,CAAC,oBAAD,CAAnB,CArFmB;EAsFhCqI,OAAO,EAAErI,MAAM,CAAC,mBAAD,EAAsB,CAAC,uBAAD,CAAtB,CAtFiB;EAuFhC6P,YAAY,EAAE7P,MAAM,CAAC,0BAAD,EAA6B,CAC/C,8BAD+C,EAE/C,QAF+C,EAG/C,qBAH+C,CAA7B,CAvFY;EA4FhC8P,UAAU,EAAE9P,MAAM,CAAC,uBAAD,EAA0B,CAC1C,2BAD0C,EAE1C,QAF0C,EAG1C,qBAH0C,CAA1B,CA5Fc;EAiGhC+P,SAAS,EAAE/P,MAAM,CAAC,IAAD,EAAO,CAAC,qBAAD,CAAP,CAjGe;EAkGhCgQ,QAAQ,EAAEhQ,MAAM,CAAC,IAAD,EAAO,CAAC,oBAAD,CAAP,CAlGgB;EAmGhCH,OAAO,EAAEG,MAAM,CAAC,mBAAD,EAAsB,CACnC,mBADmC,EAEnC,gCAFmC,EAGnC,0BAHmC,EAInC,GAAG0B,oBAJgC,EAKnC,8BALmC,CAAtB,CAnGiB;EA0GhCuO,QAAQ,EAAEjQ,MAAM,CAAC,mBAAD,EAAsB,CACpC,mBADoC,EAEpC,oBAFoC,CAAtB,CA1GgB;EA8GhCkQ,OAAO,EAAElQ,MAAM,CAAC,kBAAD,EAAqB,CAClC,+BADkC,EAElC,GAAGwB,yBAF+B,EAGlC,yBAHkC,EAIlC,GAAGE,oBAJ+B,CAArB,CA9GiB;EAoHhCyO,OAAO,EAAEnQ,MAAM,CAAC,mBAAD,EAAsB,CAAC,mBAAD,CAAtB,CApHiB;EAqHhCoQ,YAAY,EAAEpQ,MAAM,CAAC,yBAAD,EAA4B,CAC9C,8BAD8C,CAA5B,CArHY;EAwHhCqQ,MAAM,EAAErQ,MAAM,CAAC,IAAD,EAAO,CAAC,mBAAD,CAAP,CAxHkB;EAyHhCsQ,IAAI,EAAEtQ,MAAM,CAAC,IAAD,EAAO,CAAC,eAAD,CAAP,CAzHoB;EA0HhCL,IAAI,EAAEK,MAAM,CAAC,eAAD,EAAkBW,2BAAlB,CA1HoB;EA2HhC4P,SAAS,EAAEvQ,MAAM,CAAC,IAAD,EAAO,CAAC,yBAAD,CAAP,CA3He;EA4HhCwQ,WAAW,EAAExQ,MAAM,CAAC,wBAAD,EAA2B,CAAC,wBAAD,CAA3B,CA5Ha;EA6HhCyQ,QAAQ,EAAEzQ,MAAM,CAAC,IAAD,EAAO,CAAC,wBAAD,CAAP,CA7HgB;EA8HhC0Q,IAAI,EAAE1Q,MAAM,CAAC,IAAD,EAAO,CAAC,gBAAD,CAAP,CA9HoB;EA+HhC2Q,GAAG,EAAE3Q,MAAM,CAAC,cAAD,EAAiB,CAC1B,cAD0B,EAE1B,2BAF0B,EAG1B,qBAH0B,CAAjB,CA/HqB;EAoIhCwM,KAAK,EAAExM,MAAM,CAAC,IAAD,EAAO,CAAC,iBAAD,EAAoB,gBAApB,CAAP,CApImB;EAqIhC0M,QAAQ,EAAE1M,MAAM,CAAC,oBAAD,EAAuB,CACrC,qBADqC,EAErC,gBAFqC,CAAvB,CArIgB;EAyIhCF,IAAI,EAAEE,MAAM,CAAC,IAAD,EAAO,CAAC,kBAAD,CAAP,CAzIoB;EA0IhC4Q,MAAM,EAAE5Q,MAAM,CAAC,kBAAD,EAAqB,CAAC,mBAAD,CAArB,CA1IkB;EA2IhC6Q,QAAQ,EAAE7Q,MAAM,CAAC,oBAAD,EAAuB,CAAC,qBAAD,CAAvB,CA3IgB;EA4IhC8Q,IAAI,EAAE9Q,MAAM,CAAC,eAAD,EAAkB,CAAC,eAAD,CAAlB,CA5IoB;EA6IhC+Q,MAAM,EAAE/Q,MAAM,CAAC,iBAAD,EAAoB,CAChC,iBADgC,EAEhC,8BAFgC,EAGhC,wBAHgC,EAIhC,GAAG0B,oBAJ6B,CAApB,CA7IkB;EAmJhCsP,WAAW,EAAEhR,MAAM,CAAC,uBAAD,EAA0B,CAAC,uBAAD,CAA1B,CAnJa;EAoJhCiR,MAAM,EAAEjR,MAAM,CAAC,iBAAD,EAAoB,CAAC,kBAAD,CAApB,CApJkB;EAqJhC8M,OAAO,EAAE9M,MAAM,CAAC,IAAD,EAAO,CAAC,mBAAD,EAAsB,gBAAtB,CAAP,CArJiB;EAsJhCkR,UAAU,EAAElR,MAAM,CAAC,sBAAD,EAAyB,CACzC,uBADyC,EAEzC,mBAFyC,EAGzC,gBAHyC,CAAzB,CAtJc;EA2JhCmR,OAAO,EAAEnR,MAAM,CAAC,kBAAD,EAAqB,CAAC,kBAAD,CAArB,CA3JiB;EA4JhC+M,MAAM,EAAE/M,MAAM,CAAC,IAAD,EAAO,CAAC,kBAAD,EAAqB,gBAArB,CAAP,CA5JkB;EA6JhCoR,OAAO,EAAEpR,MAAM,CAAC,IAAD,EAAO,CAAC,kBAAD,CAAP,CA7JiB;EA8JhCqR,KAAK,EAAErR,MAAM,CAAC,gBAAD,EAAmB,CAAC,gBAAD,CAAnB,CA9JmB;EA+JhCsR,KAAK,EAAEtR,MAAM,CAAC,IAAD,EAAO,CAAC,iBAAD,CAAP,CA/JmB;EAgKhCuR,IAAI,EAAEvR,MAAM,CAAC,eAAD,EAAkB,CAC5B,eAD4B,EAE5B,4BAF4B,EAG5B,sBAH4B,EAI5B,GAAG0B,oBAJyB,CAAlB,CAhKoB;EAsKhCtB,IAAI,EAAEJ,MAAM,CAAC,eAAD,EAAkB,CAAC,eAAD,CAAlB,CAtKoB;EAuKhCwR,MAAM,EAAExR,MAAM,CAAC,iBAAD,EAAoB,CAAC,iBAAD,CAApB,CAvKkB;EAwKhCiN,KAAK,EAAEjN,MAAM,CAAC,IAAD,EAAO,CAAC,iBAAD,EAAoB,gBAApB,CAAP,CAxKmB;EAyKhCyR,UAAU,EAAEzR,MAAM,CAAC,sBAAD,EAAyB,CAAC,uBAAD,CAAzB,CAzKc;EA0KhC0R,MAAM,EAAE1R,MAAM,CAAC,IAAD,EAAO,CAAC,kBAAD,CAAP,CA1KkB;EA2KhC2R,MAAM,EAAE3R,MAAM,CAAC,IAAD,EAAO,CAAC,kBAAD,CAAP,CA3KkB;EA4KhC4R,GAAG,EAAE5R,MAAM,CAAC,IAAD,EAAO,CAAC,eAAD,CAAP,CA5KqB;EA6KhC6R,MAAM,EAAE7R,MAAM,CAAC,IAAD,EAAO,CAAC,kBAAD,CAAP,CA7KkB;EA8KhC8R,GAAG,EAAE9R,MAAM,CAAC,IAAD,EAAO,CAAC,eAAD,CAAP,CA9KqB;EA+KhC+R,IAAI,EAAE/R,MAAM,CAAC,eAAD,EAAkB,CAC5B,4BAD4B,EAE5B,GAAGwB,yBAFyB,EAG5B,sBAH4B,EAI5B,GAAGE,oBAJyB,CAAlB,CA/KoB;EAqLhCsQ,IAAI,EAAEhS,MAAM,CAAC,IAAD,EAAO,CAAC,gBAAD,EAAmB,gBAAnB,CAAP,CArLoB;EAsLhCiS,OAAO,EAAEjS,MAAM,CAAC,mBAAD,EAAsB,CACnC,gCADmC,EAEnC,GAAGwB,yBAFgC,EAGnC,0BAHmC,EAInC,GAAGE,oBAJgC,CAAtB,CAtLiB;EA4LhCwQ,OAAO,EAAElS,MAAM,CAAC,IAAD,EAAO,CACpB,0BADoB,EAEpB,GAAG0B,oBAFiB,EAGpB,GAAGF,yBAHiB,EAIpB,GAAGC,2BAJiB,CAAP,CA5LiB;EAkMhC0Q,aAAa,EAAEnS,MAAM,CAAC,IAAD,EAAO,CAAC,0BAAD,CAAP,CAlMW;EAmMhCoS,OAAO,EAAEpS,MAAM,CAAC,IAAD,EAAO,CAAC,oBAAD,CAAP,CAnMiB;EAoMhCqS,WAAW,EAAErS,MAAM,CAAC,IAAD,EAAO,CAAC,uBAAD,CAAP,CApMa;EAqMhCsS,WAAW,EAAEtS,MAAM,CAAC,IAAD,EAAO,CAAC,uBAAD,CAAP,CArMa;EAsMhCuS,MAAM,EAAEvS,MAAM,CAAC,IAAD,EAAO,CAAC,iBAAD,EAAoB,iBAApB,CAAP,CAtMkB;EAuMhCwS,WAAW,EAAExS,MAAM,CAAC,IAAD,EAAO,CAAC,wBAAD,CAAP,CAvMa;EAwMhCyS,UAAU,EAAEzS,MAAM,CAAC,sBAAD,EAAyB,CAAC,sBAAD,CAAzB,CAxMc;EAyMhC0S,QAAQ,EAAE1S,MAAM,CAAC,oBAAD,EAAuB,CACrC,oBADqC,EAErC,eAFqC,CAAvB,CAzMgB;EA6MhC2S,SAAS,EAAE3S,MAAM,CAAC,qBAAD,EAAwB,CAAC,qBAAD,CAAxB,CA7Me;EA8MhC4S,QAAQ,EAAE5S,MAAM,CAAC,IAAD,EAAO,CACrB,qBADqB,EAErB,oBAFqB,EAGrB,mBAHqB,EAIrB,qBAJqB,CAAP,CA9MgB;EAoNhC6S,YAAY,EAAE7S,MAAM,CAAC,yBAAD,EAA4B,CAC9C,8BAD8C,CAA5B,CApNY;EAuNhC8S,IAAI,EAAE9S,MAAM,CAAC,eAAD,EAAkB,CAAC,gBAAD,CAAlB,CAvNoB;EAwNhC+S,OAAO,EAAE/S,MAAM,CAAC,mBAAD,EAAsB,CAAC,oBAAD,CAAtB,CAxNiB;EAyNhCgT,QAAQ,EAAEhT,MAAM,CAAC,oBAAD,EAAuB,CAAC,sBAAD,CAAvB,CAzNgB;EA0NhCiT,SAAS,EAAEjT,MAAM,CAAC,qBAAD,EAAwB,CAAC,oBAAD,CAAxB,CA1Ne;EA2NhCkT,SAAS,EAAElT,MAAM,CAAC,qBAAD,EAAwB,CAAC,sBAAD,CAAxB,CA3Ne;EA4NhCmT,QAAQ,EAAEnT,MAAM,CAAC,oBAAD,EAAuB,CAAC,wBAAD,EAA2B,QAA3B,CAAvB,CA5NgB;EA6NhCoT,OAAO,EAAEpT,MAAM,CAAC,kBAAD,EAAqB,CAAC,kBAAD,CAArB,CA7NiB;EA8NhCqT,MAAM,EAAErT,MAAM,CAAC,kBAAD,EAAqB,CAAC,yBAAD,CAArB,CA9NkB;EA+NhCmK,MAAM,EAAEnK,MAAM,CAAC,iBAAD,EAAoBW,2BAApB,CA/NkB;EAgOhC2S,IAAI,EAAEtT,MAAM,CAAC,eAAD,EAAkB,CAAC,eAAD,CAAlB,CAhOoB;EAiOhCuT,gBAAgB,EAAEvT,MAAM,CAAC,IAAD,EAAO,CAAC,yBAAD,CAAP,CAjOQ;EAkOhCwT,gBAAgB,EAAExT,MAAM,CAAC,IAAD,EAAO,CAAC,yBAAD,CAAP,CAlOQ;EAmOhCyT,gBAAgB,EAAEzT,MAAM,CAAC,IAAD,EAAO,CAAC,yBAAD,CAAP,CAnOQ;EAoOhC0T,gBAAgB,EAAE1T,MAAM,CAAC,IAAD,EAAO,CAAC,yBAAD,CAAP,CApOQ;EAqOhC,CAAC,WAAD,GAAeA,MAAM,CAAC,IAAD,EAAO,CAAC,iBAAD,CAAP;AArOW,CAA3B;;;EC3qBE2T,OAASC;;AAEH,SAASC,eAAT,CACbC,IADa,EAEbC,IAFa,EAGb;EACA,MAAM;IAAEC,IAAF;IAAQC;MAAWF,IAAzB;;EACA,QAAQD,IAAI,CAAChU,IAAb;IACE,KAAK,iBAAL;MAAwB;QACtB,IAAI,CAAC8T,GAAC,CAACM,gBAAF,CAAmBD,MAAnB,EAA2B;UAAEE,MAAM,EAAEH;SAArC,CAAL,EAAmD,OAAO,KAAP;QACnD,IAAIC,MAAM,CAACG,SAAP,CAAiBC,MAAjB,GAA0B,CAA9B,EAAiC,OAAO,IAAP;QACjC,MAAMC,QAAQ,GAAGL,MAAM,CAACG,SAAP,CAAiB,CAAjB,CAAjB;QACA,OAAOR,GAAC,CAACW,eAAF,CAAkBD,QAAlB,KAA+BV,GAAC,CAACY,iBAAF,CAAoBF,QAApB,CAAtC;;;AAGL;;;EChBQX,OAASC;;AAGX,MAAMa,aAAa,GAAG,wBAAtB;AAEA,SAASC,UAAT,CAAoBX,IAApB,EAA+BY,EAA/B,EAAiD;EACtD,MAAM;IAAEC;MAAWb,IAAI,CAACC,IAAxB;EAEA,IAAIa,QAAJ,EAAcC,QAAd;;EACA,IAAIlB,GAAC,CAACmB,YAAF,CAAeH,MAAf,CAAJ,EAA4B;IAC1BC,QAAQ,GAAGD,MAAX;IACAE,QAAQ,GAAGlB,GAAC,CAACoB,SAAF,CAAYJ,MAAZ,CAAX;GAFF,MAGO;IACLC,QAAQ,GAAGd,IAAI,CAACkB,KAAL,CAAWC,6BAAX,CAAyC,SAAzC,CAAX;IACAJ,QAAQ,GAAGlB,GAAC,CAACuB,oBAAF,CAAuB,GAAvB,EAA4BvB,GAAC,CAACoB,SAAF,CAAYH,QAAZ,CAA5B,EAAmDD,MAAnD,CAAX;;;EAGFb,IAAI,CAACqB,WAAL,CACExB,GAAC,CAACyB,gBAAF,CAAmBzB,GAAC,CAAC0B,cAAF,CAAiBX,EAAjB,EAAqB,CAACG,QAAD,CAArB,CAAnB,EAAqDlB,GAAC,CAAC2B,UAAF,CAAa,MAAb,CAArD,CADF;EAIAxB,IAAI,CAACyB,UAAL,CAAgBC,gBAAhB,CAAiC,WAAjC,EAA8CZ,QAA9C;AACD;AAEM,SAASa,cAAT,CAAwBC,MAAxB,EAAwC;EAC7C,IAAI,OAAOA,MAAP,KAAkB,QAAtB,EAAgC;IAC9BA,MAAM,GAAGA,MAAM,CACZ7I,OADM,CACE,KADF,EACS,GADT,EAENA,OAFM,CAEE,wBAFF,EAE4B,EAF5B,EAGN8I,WAHM,EAAT;;;EAMF,OACElW,MAAM,CAACmW,SAAP,CAAiBC,cAAjB,CAAgCC,IAAhC,CAAqCC,aAArC,EAAoDL,MAApD,KACAK,aAAa,CAACL,MAAD,CAFf;AAID;AAEM,SAASM,YAAT,CAAsBnW,IAAtB,EAAoC;EACzC,OAAQ,mBAAkBA,IAAK,KAA/B;AACD;AAEM,SAASoW,gBAAT,CACLpW,IADK,EAELqW,eAFK,EAGLC,GAHK,EAIL;EACA,OAAOD,eAAe,GACjB,GAAE1B,aAAc,YAAW3U,IAAK,GAAEsW,GAAI,EADrB,GAEjB,yBAAwBtW,IAAK,KAFlC;AAGD;;;ECnCQ6T,OAASC;;AAWlB,MAAMyC,eAAe,GAAG,iDAAxB;AACA,MAAMC,aAAa,GAAG,8CAAtB;AAcA,MAAMC,aAAa,GAAG,CACpB,OADoB,EAEpB,QAFoB,EAIpB,UAJoB,EAKpB,gBALoB,EAMpB,iBANoB,EAOpB5F,GAPoB,CAOhB6F,CAAC,IAAI,IAAI7S,MAAJ,CAAY,YAAW6S,CAAE,OAAzB,CAPW,CAAtB;;AASA,MAAMC,cAAc,GAAG,CACrB3W,IADqB,EAErB4W,EAFqB,KAGT;EACZ,IAAIA,EAAE,CAAC5W,IAAD,CAAN,EAAc,OAAO,IAAP;EACd,IAAI,CAACA,IAAI,CAAC2R,UAAL,CAAgB,KAAhB,CAAL,EAA6B,OAAO,KAAP;EAC7B,MAAMkF,QAAQ,GAAI,UAAS7W,IAAI,CAACuR,KAAL,CAAW,CAAX,CAAc,EAAzC;EACA,IAAI,CAACzR,gBAAgB,CAAC+W,QAAD,CAArB,EAAiC,OAAO,KAAP;EACjC,OAAOD,EAAE,CAACC,QAAD,CAAT;AACD,CATD;;AAWA,YAAeC,cAAc,CAAU,UACrC;EAAEC,QAAF;EAAYC,MAAZ;EAAoBC,oBAApB;EAA0CC,kBAA1C;EAA8DC,KAA9D;EAAqEC;AAArE,CADqC,EAErC;EACEC,OAAO,GAAG,CADZ;EAEEC,SAFF;EAGEC,gBAHF;EAIE,CAAChB,eAAD,GAAmB;IAAEiB,aAAa,GAAG;MAAU,EAJjD;EAKE,CAAChB,aAAD,GAAiB;IAAEH,eAAe,GAAG,KAApB;IAA2BC,GAAG,GAAG;MAAU;AAL9D,CAFqC,EASrC;EACA,MAAMmB,SAAS,GAAGL,KAAK,CAACM,MAAN,CAAaA,MAAM,IAAI,CAAAA,MAAM,QAAN,YAAAA,MAAM,CAAE1X,IAAR,MAAiB,cAAxC,CAAlB;EAEA,MAAM2X,OAAO,GAAGT,kBAAkB,CAAC;IACjC9W,MAAM,EAAE6B,QADyB;IAEjC2V,MAAM,EAAEpS,gBAFyB;IAGjCqS,QAAQ,EAAEjK;GAHsB,CAAlC;EAMA,MAAMkK,SAAS,GAAG,IAAIpY,GAAJ,CAAQqY,8BAA8B,CAACV,OAAD,CAAtC,CAAlB;;EAEA,SAASW,iBAAT,CAA2BC,eAA3B,EAA4C;IAC1C,OAAO5B,eAAe,GAClB4B,eAAe,GACZ,GAAEtD,aAAc,UADJ,GAEZ,GAAEA,aAAc,iBAHD,GAIlBsD,eAAe,GACf,uBADe,GAEf,qBANJ;;;EASF,SAASC,qBAAT,CAA+BlY,IAA/B,EAA6CmY,KAA7C,EAAoD;IAClD,IAAIlB,oBAAoB,CAACjX,IAAD,CAAxB,EAAgC;MAC9BmX,KAAK,CAACnX,IAAD,CAAL;MACAmY,KAAK,CAACC,kBAAN,CAAyBjC,YAAY,CAACnW,IAAD,CAArC;MACA,OAAO,IAAP;;;IAEF,OAAO,KAAP;;;EAGF,SAASqY,iBAAT,CAA2BC,KAA3B,EAA4CH,KAA5C,EAAmDtB,QAAQ,GAAG,IAA9D,EAAoE;IAClE,KAAK,MAAM7W,IAAX,IAAmBsY,KAAnB,EAA0B;MACxB,IAAIzB,QAAJ,EAAc;QACZF,cAAc,CAAC3W,IAAD,EAAOA,IAAI,IAAIkY,qBAAqB,CAAClY,IAAD,EAAOmY,KAAP,CAApC,CAAd;OADF,MAEO;QACLD,qBAAqB,CAAClY,IAAD,EAAOmY,KAAP,CAArB;;;;;EAKN,SAASI,eAAT,CACEvE,IADF,EAEEwE,IAFF,EAGEL,KAHF,EAIErD,MAJF,EAKE;IACA,IACEd,IAAI,CAAC7T,IAAL,IACA,EAAE2U,MAAM,IAAId,IAAI,CAAC3T,OAAf,IAA0B2T,IAAI,CAAC3T,OAAL,CAAa8P,QAAb,CAAsB2E,MAAtB,CAA5B,CADA,IAEA6B,cAAc,CAAC3C,IAAI,CAAChU,IAAN,EAAYiX,oBAAZ,CAHhB,EAIE;MACA,MAAM;QAAEjX;UAASgU,IAAjB;MACA,IAAIiE,eAAe,GAAG,KAAtB;;MACA,IAAIX,SAAS,IAAKC,gBAAgB,IAAIvX,IAAI,CAAC2R,UAAL,CAAgB,SAAhB,CAAtC,EAAmE;QACjEsG,eAAe,GAAG,IAAlB;OADF,MAEO,IAAIjY,IAAI,CAAC2R,UAAL,CAAgB,KAAhB,KAA0B,CAACmG,SAAS,CAACzM,GAAV,CAAcrL,IAAd,CAA/B,EAAoD;QACzDiY,eAAe,GAAG,IAAlB;;;MAEF,MAAMQ,cAAc,GAAGT,iBAAiB,CAACC,eAAD,CAAxC;MACA,OAAOE,KAAK,CAACO,mBAAN,CACJ,GAAED,cAAe,IAAGzE,IAAI,CAAC7T,IAAK,GAAEmW,GAAI,EADhC,EAELkC,IAFK,CAAP;;;;EAOJ,SAASG,eAAT,CAAyB3Y,IAAzB,EAA+B;IAC7B,IAAIA,IAAI,CAAC2R,UAAL,CAAgB,SAAhB,CAAJ,EAAgC;MAC9B,MAAMiH,MAAM,GAAI,MAAK5Y,IAAI,CAACuR,KAAL,CAAW,CAAX,CAAc,EAAnC,CAD8B;;;MAI9B,OAAOqH,MAAM,IAAI9Y,gBAAjB;;;IAEF,OAAO,IAAP;;;EAGF,OAAO;IACLE,IAAI,EAAE,SADD;IAGL6Y,WAAW,EAAErB,aAAa,GAAG,IAAH,GAAU7C,aAH/B;IAKLmE,SAAS,EAAEhZ,gBALN;;IAOLiZ,eAAe,CAAC/Y,IAAD,EAAO;MACpB,IAAI,CAAC8X,SAAS,CAACzM,GAAV,CAAcrL,IAAd,CAAL,EAA0B,OAAO,KAAP;MAC1B,IAAIsX,SAAS,IAAIN,MAAM,KAAK,cAA5B,EAA4C,OAAO,IAAP;;MAC5C,IAAIO,gBAAgB,IAAIyB,2BAA2B,CAAC3N,GAA5B,CAAgCrL,IAAhC,CAAxB,EAA+D;QAC7D,OAAO,IAAP;;;MAEF,OAAO2Y,eAAe,CAAC3Y,IAAD,CAAtB;KAbG;;IAgBLiZ,WAAW,CAACC,IAAD,EAAOf,KAAP,EAAclE,IAAd,EAAoB;MAC7B,IAAIiF,IAAI,CAACC,IAAL,KAAc,QAAlB,EAA4B;MAE5B,MAAMC,OAAO,GAAGxD,cAAc,CAACsD,IAAI,CAACrD,MAAN,CAA9B;MACA,IAAI,CAACuD,OAAL,EAAc;;MAEd,IACEA,OAAO,CAAC7E,MAAR,KAAmB,CAAnB,IACA2E,IAAI,CAACrD,MAAL,KAAgBM,YAAY,CAACiD,OAAO,CAAC,CAAD,CAAR,CAD5B,IAEAnC,oBAAoB,CAACmC,OAAO,CAAC,CAAD,CAAR,CAHtB,EAIE;;;QAGAjC,KAAK,CAAC,IAAD,CAAL;QACA;;;MAGF,MAAMkC,UAAU,GAAG,IAAI3Z,GAAJ,CAAQ0Z,OAAR,CAAnB;MACA,MAAME,eAAe,GAAGF,OAAO,CAAClK,MAAR,CAAeqK,MAAM,IAAI;QAC/C,IAAI,CAACA,MAAM,CAAC5H,UAAP,CAAkB,SAAlB,CAAL,EAAmC,OAAO,IAAP;QACnC,MAAM6H,MAAM,GAAGD,MAAM,CAACvM,OAAP,CAAe,SAAf,EAA0B,KAA1B,CAAf;;QACA,IAAIqM,UAAU,CAAChO,GAAX,CAAemO,MAAf,KAA0BvC,oBAAoB,CAACuC,MAAD,CAAlD,EAA4D;UAC1D,OAAO,KAAP;;;QAEF,OAAO,IAAP;OANsB,CAAxB;MASAnB,iBAAiB,CAACiB,eAAD,EAAkBnB,KAAlB,EAAyB,KAAzB,CAAjB;MACAlE,IAAI,CAACwF,MAAL;KA5CG;;IA+CLC,WAAW,CAACR,IAAD,EAAOf,KAAP,EAAclE,IAAd,EAAoB;MAC7B,MAAM0F,QAAQ,GAAGhC,OAAO,CAACuB,IAAD,CAAxB;MACA,IAAI,CAACS,QAAL,EAAe;MAEf,IAAI5F,eAAe,CAAC4F,QAAQ,CAAC3F,IAAV,EAAgBC,IAAhB,CAAnB,EAA0C;MAE1C,IAAI2F,IAAI,GAAGD,QAAQ,CAAC3F,IAAT,CAAc5T,MAAzB;;MAEA,IACEuZ,QAAQ,CAACR,IAAT,KAAkB,QAAlB,IACA,YAAYD,IADZ,IAEAA,IAAI,CAACpE,MAFL,IAGAoE,IAAI,CAACW,SAAL,KAAmB,WAJrB,EAKE;QACA,MAAMC,GAAG,GAAGZ,IAAI,CAACpE,MAAL,CAAYgB,WAAZ,EAAZ;QACA8D,IAAI,GAAGA,IAAI,CAAC1K,MAAL,CAAY6K,CAAC,IAClBtD,aAAa,CAAChF,IAAd,CAAmBiF,CAAC,IAAIA,CAAC,CAACxE,IAAF,CAAO6H,CAAP,CAAxB,IAAqCA,CAAC,CAAC5J,QAAF,CAAW2J,GAAX,CAArC,GAAuD,IADlD,CAAP;;;MAKFzB,iBAAiB,CAACuB,IAAD,EAAOzB,KAAP,CAAjB;KAnEG;;IAsEL6B,SAAS,CAACd,IAAD,EAAOf,KAAP,EAAclE,IAAd,EAAoB;MAC3B,IAAIiF,IAAI,CAACC,IAAL,KAAc,IAAlB,EAAwB;QACtB,IAAID,IAAI,CAACe,GAAL,KAAa,iBAAjB,EAAoC;UAClChG,IAAI,CAACqB,WAAL,CACExB,CAAC,CAAC0B,cAAF,CACE2C,KAAK,CAACO,mBAAN,CACEtC,gBAAgB,CAAC,aAAD,EAAgBC,eAAhB,EAAiCC,GAAjC,CADlB,EAEE,YAFF,CADF,EAKE,CAAErC,IAAI,CAACC,IAAN,CAAkCgG,KAAnC,CALF;WADF;;;QAUF;;;MAGF,IAAIjG,IAAI,CAACyB,UAAL,CAAgByE,iBAAhB,CAAkC;QAAEC,QAAQ,EAAE;OAA9C,CAAJ,EAA+D;;MAE/D,IAAIlB,IAAI,CAACC,IAAL,KAAc,UAAlB,EAA8B;;QAE5B,IAAI,CAAClF,IAAI,CAACoG,kBAAL,EAAL,EAAgC;QAChC,IAAI,CAACpG,IAAI,CAACqG,YAAL,EAAL,EAA0B;QAC1B,IAAIrG,IAAI,CAACyB,UAAL,CAAgB6E,kBAAhB,EAAJ,EAA0C;;QAC1C,IAAIzG,CAAC,CAAC0G,OAAF,CAAUvG,IAAI,CAACC,IAAL,CAAUY,MAApB,CAAJ,EAAiC;UAC/B;;;QAGF,IAAIoE,IAAI,CAACe,GAAL,KAAa,iBAAjB,EAAoC;UAClC,IAAI,CAAChD,oBAAoB,CAAC,oBAAD,CAAzB,EAAiD;UAEjD,MAAM;YAAE9C,MAAF;YAAUD;cAASD,IAAzB;;UACA,IAAIH,CAAC,CAACM,gBAAF,CAAmBD,MAAnB,EAA2B;YAAEE,MAAM,EAAEH;WAArC,CAAJ,EAAkD;YAChD,IAAIC,MAAM,CAACG,SAAP,CAAiBC,MAAjB,KAA4B,CAAhC,EAAmC;cACjCN,IAAI,CAACyB,UAAL,CAAgBJ,WAAhB,CACExB,CAAC,CAAC0B,cAAF,CACE2C,KAAK,CAACO,mBAAN,CACEtC,gBAAgB,CAAC,cAAD,EAAiBC,eAAjB,EAAkCC,GAAlC,CADlB,EAEE,aAFF,CADF,EAKE,CAACpC,IAAI,CAACY,MAAN,CALF,CADF;cASAb,IAAI,CAACwG,IAAL;aAVF,MAWO;cACL7F,UAAU,CACRX,IADQ,EAERkE,KAAK,CAACO,mBAAN,CACEtC,gBAAgB,CAAC,qBAAD,EAAwBC,eAAxB,EAAyCC,GAAzC,CADlB,EAEE,mBAFF,CAFQ,CAAV;;WAbJ,MAqBO;YACLrC,IAAI,CAACqB,WAAL,CACExB,CAAC,CAAC0B,cAAF,CACE2C,KAAK,CAACO,mBAAN,CACEtC,gBAAgB,CAAC,qBAAD,EAAwBC,eAAxB,EAAyCC,GAAzC,CADlB,EAEE,mBAFF,CADF,EAKE,CAACrC,IAAI,CAACC,IAAL,CAAUY,MAAX,CALF,CADF;;;UAWF;;;;MAIJ,IAAI6E,QAAQ,GAAGhC,OAAO,CAACuB,IAAD,CAAtB;MACA,IAAI,CAACS,QAAL,EAAe;MAEf,IAAI5F,eAAe,CAAC4F,QAAQ,CAAC3F,IAAV,EAAgBC,IAAhB,CAAnB,EAA0C;;MAE1C,IACEoC,eAAe,IACfsD,QAAQ,CAAC3F,IAAT,CAAc7T,IADd,IAEAwZ,QAAQ,CAAC3F,IAAT,CAAc7T,IAAd,CAAmBoR,KAAnB,CAAyB,CAAC,CAA1B,MAAiC,QAHnC,EAIE;;QAEAoI,QAAQ,GAAG,EACT,GAAGA,QADM;UAET3F,IAAI,EAAE,EACJ,GAAG2F,QAAQ,CAAC3F,IADR;YAEJ7T,IAAI,EAAEwZ,QAAQ,CAAC3F,IAAT,CAAc7T,IAAd,CAAmBoR,KAAnB,CAAyB,CAAzB,EAA4B,CAAC,CAA7B;;SAJV;;;MASF,IAAIoI,QAAQ,CAACR,IAAT,KAAkB,QAAtB,EAAgC;QAC9B,MAAMtE,EAAE,GAAG0D,eAAe,CAACoB,QAAQ,CAAC3F,IAAV,EAAgB2F,QAAQ,CAAC3Z,IAAzB,EAA+BmY,KAA/B,CAA1B;QACA,IAAItD,EAAJ,EAAQZ,IAAI,CAACqB,WAAL,CAAiBT,EAAjB;OAFV,MAGO,IAAI8E,QAAQ,CAACR,IAAT,KAAkB,QAAtB,EAAgC;QACrC,MAAMtE,EAAE,GAAG0D,eAAe,CACxBoB,QAAQ,CAAC3F,IADe,EAExB2F,QAAQ,CAAC3Z,IAFe,EAGxBmY,KAHwB;QAKxBe,IAAI,CAACpE,MALmB,CAA1B;QAOA,IAAID,EAAJ,EAAQZ,IAAI,CAACqB,WAAL,CAAiBT,EAAjB;OARH,MASA,IAAI8E,QAAQ,CAACR,IAAT,KAAkB,UAAtB,EAAkC;QACvC,MAAMtE,EAAE,GAAG0D,eAAe,CACxBoB,QAAQ,CAAC3F,IADe,EAEvB,GAAE2F,QAAQ,CAAC3Z,IAAK,kBAFO,EAGxBmY,KAHwB;QAKxBe,IAAI,CAACpE,MALmB,CAA1B;QAOA,IAAI,CAACD,EAAL,EAAS;QAET,MAAM;UAAEX;YAASD,IAAjB;;QACA,IAAIH,CAAC,CAACM,gBAAF,CAAmBH,IAAI,CAACE,MAAxB,EAAgC;UAAEE,MAAM,EAAEH;SAA1C,CAAJ,EAAuD;UACrDU,UAAU,CAACX,IAAD,EAAOY,EAAP,CAAV;SADF,MAEO;UACLZ,IAAI,CAACqB,WAAL,CAAiBxB,CAAC,CAAC0B,cAAF,CAAiBX,EAAjB,EAAqB,CAACX,IAAI,CAACY,MAAN,CAArB,CAAjB;;;KAxLD;;IA6LL4F,OAAO,EAAE1D,MAAM,KAAK,cAAX,IAA6B;;MAEpC2D,cAAc,CAAC1G,IAAD,EAAmC;QAC/C,IAAIA,IAAI,CAACjJ,GAAL,CAAS,QAAT,EAAmB4P,QAAnB,EAAJ,EAAmC;UACjC,MAAMzC,KAAK,GAAGpB,QAAQ,CAAC9C,IAAD,CAAtB;;UAEA,IAAIwD,SAAJ,EAAe;;YAEbY,iBAAiB,CAACnX,gCAAD,EAAmCiX,KAAnC,CAAjB;WAFF,MAGO;YACLE,iBAAiB,CAACpX,mBAAD,EAAsBkX,KAAtB,CAAjB;;;OAV8B;;;MAgBpCnS,QAAQ,CAACiO,IAAD,EAA6B;QACnC,IAAIA,IAAI,CAACC,IAAL,CAAU2G,KAAd,EAAqB;UACnBxC,iBAAiB,CAACpX,mBAAD,EAAsB8V,QAAQ,CAAC9C,IAAD,CAA9B,CAAjB;;OAlBgC;;;MAuBpC,8BACEA,IADF,EAEE;QACAoE,iBAAiB,CAACzX,eAAD,EAAkBmW,QAAQ,CAAC9C,IAAD,CAA1B,CAAjB;OA1BkC;;;MA8BpC6G,aAAa,CAAC7G,IAAD,EAAkC;QAC7C,IAAI,CAACA,IAAI,CAACyB,UAAL,CAAgBqF,kBAAhB,EAAL,EAA2C;UACzC1C,iBAAiB,CAACzX,eAAD,EAAkBmW,QAAQ,CAAC9C,IAAD,CAA1B,CAAjB;;OAhCgC;;;MAqCpC+G,eAAe,CAAC/G,IAAD,EAAoC;QACjD,IAAIA,IAAI,CAACC,IAAL,CAAU+G,QAAd,EAAwB;UACtB5C,iBAAiB,CAACzX,eAAD,EAAkBmW,QAAQ,CAAC9C,IAAD,CAA1B,CAAjB;;;;;GApOR;AAyOD,CA9T4B,CAA7B;;;;"}