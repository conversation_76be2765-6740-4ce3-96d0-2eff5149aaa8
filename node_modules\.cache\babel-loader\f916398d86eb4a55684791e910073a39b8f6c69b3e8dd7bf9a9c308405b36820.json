{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, createElementVNode as _createElementVNode, vModelText as _vModelText, withDirectives as _withDirectives, toDisplayString as _toDisplayString, createCommentVNode as _createCommentVNode, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, createTextVNode as _createTextVNode, pushScopeId as _pushScopeId, popScopeId as _popScopeId } from \"vue\";\nvar _withScopeId = function _withScopeId(n) {\n  return _pushScopeId(\"data-v-29a9df8d\"), n = n(), _popScopeId(), n;\n};\nvar _hoisted_1 = {\n  class: \"tel home\"\n};\nvar _hoisted_2 = {\n  style: {\n    \"background\": \"#f5f5f5\",\n    \"height\": \"2.5rem\",\n    \"width\": \"100%\",\n    \"display\": \"flex\",\n    \"justify-content\": \"center\",\n    \"align-items\": \"center\",\n    \"color\": \"#000\",\n    \"margin-top\": \"-0.7rem\",\n    \"position\": \"fixed\",\n    \"top\": \"3.5rem\"\n  }\n};\nvar _hoisted_3 = /*#__PURE__*/_withScopeId(function () {\n  return /*#__PURE__*/_createElementVNode(\"div\", {\n    style: {\n      \"width\": \"10%\"\n    }\n  }, \"+\", -1 /* HOISTED */);\n});\nvar _hoisted_4 = {\n  style: {\n    \"width\": \"70%\"\n  }\n};\nvar _hoisted_5 = [\"placeholder\"];\nvar _hoisted_6 = {\n  style: {\n    \"width\": \"20%\",\n    \"font-weight\": \"bold\"\n  }\n};\nvar _hoisted_7 = {\n  style: {\n    \"margin-top\": \"1.7rem\",\n    \"border-top\": \"1px solid #ccc\",\n    \"background\": \"#fff\",\n    \"display\": \"flex\",\n    \"justify-content\": \"center\",\n    \"align-items\": \"center\",\n    \"flex-direction\": \"column\",\n    \"color\": \"#000\"\n  }\n};\nvar _hoisted_8 = [\"onClick\"];\nvar _hoisted_9 = {\n  style: {\n    \"display\": \"flex\",\n    \"align-items\": \"center\"\n  }\n};\nvar _hoisted_10 = [\"src\"];\nvar _hoisted_11 = [\"onClick\"];\nvar _hoisted_12 = {\n  style: {\n    \"display\": \"flex\",\n    \"align-items\": \"center\"\n  }\n};\nvar _hoisted_13 = [\"src\"];\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_van_nav_bar = _resolveComponent(\"van-nav-bar\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_van_nav_bar, {\n    title: 'Area code',\n    background: \"rgb(254, 44, 85)\",\n    \"title-style\": \"color:black; font-size: 16px;\",\n    \"left-arrow\": \"\",\n    onClickLeft: _cache[0] || (_cache[0] = function ($event) {\n      return _ctx.$router.go(-1);\n    })\n  }), _createElementVNode(\"div\", _hoisted_2, [_hoisted_3, _createElementVNode(\"div\", _hoisted_4, [_withDirectives(_createElementVNode(\"input\", {\n    \"onUpdate:modelValue\": _cache[1] || (_cache[1] = function ($event) {\n      return $setup.search = $event;\n    }),\n    style: {\n      \"width\": \"100%\",\n      \"height\": \"2.1rem\",\n      \"border-radius\": \"0.5rem\",\n      \"border\": \"none\",\n      \"background\": \"#e6e6e6\"\n    },\n    placeholder: _ctx.$t('msg.plz_areacode')\n  }, null, 8 /* PROPS */, _hoisted_5), [[_vModelText, $setup.search]])]), _createElementVNode(\"div\", _hoisted_6, _toDisplayString(_ctx.$t('msg.yes')), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_7, [_createCommentVNode(\" :src=\\\"'~@/assets/images/areacode/'+ v[1] +'.png'\\\" \"), !$setup.search ? (_openBlock(true), _createElementBlock(_Fragment, {\n    key: 0\n  }, _renderList($setup.list, function (v) {\n    return _openBlock(), _createElementBlock(\"div\", {\n      class: \"areaclass\",\n      onClick: function onClick($event) {\n        return $setup.tourl(v[0]);\n      }\n    }, [_createElementVNode(\"div\", _hoisted_9, [_createElementVNode(\"img\", {\n      src: require('@/assets/images/areacode/' + v[1] + '.png'),\n      style: {\n        \"width\": \"2.2rem\",\n        \"height\": \"1.5rem\"\n      }\n    }, null, 8 /* PROPS */, _hoisted_10), _createTextVNode(\" +\" + _toDisplayString(v[0]), 1 /* TEXT */)]), _createElementVNode(\"div\", null, _toDisplayString(v[1]), 1 /* TEXT */)], 8 /* PROPS */, _hoisted_8);\n  }), 256 /* UNKEYED_FRAGMENT */)) : (_openBlock(true), _createElementBlock(_Fragment, {\n    key: 1\n  }, _renderList($setup.list2, function (v) {\n    return _openBlock(), _createElementBlock(\"div\", {\n      class: \"areaclass\",\n      onClick: function onClick($event) {\n        return $setup.tourl(v[0]);\n      }\n    }, [_createElementVNode(\"div\", _hoisted_12, [_createElementVNode(\"img\", {\n      src: require('@/assets/images/areacode/' + v[1] + '.png'),\n      style: {\n        \"width\": \"2.2rem\",\n        \"height\": \"1.5rem\"\n      }\n    }, null, 8 /* PROPS */, _hoisted_13), _createTextVNode(\" +\" + _toDisplayString(v[0]), 1 /* TEXT */)]), _createElementVNode(\"div\", null, _toDisplayString(v[1]), 1 /* TEXT */)], 8 /* PROPS */, _hoisted_11);\n  }), 256 /* UNKEYED_FRAGMENT */))])]);\n}", "map": {"version": 3, "names": ["class", "style", "_createElementVNode", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_van_nav_bar", "title", "background", "onClickLeft", "_cache", "$event", "_ctx", "$router", "go", "_hoisted_2", "_hoisted_3", "_hoisted_4", "$setup", "search", "placeholder", "$t", "_hoisted_6", "_toDisplayString", "_hoisted_7", "_createCommentVNode", "_Fragment", "key", "_renderList", "list", "v", "onClick", "tourl", "_hoisted_9", "src", "require", "list2", "_hoisted_12"], "sources": ["C:\\Users\\<USER>\\Desktop\\vue3_3.0\\src\\views\\login\\area.vue"], "sourcesContent": ["<template>\r\n    <div class=\"tel home\">\r\n        <van-nav-bar :title=\"'Area code'\" background=\"rgb(254, 44, 85)\" title-style=\"color:black; font-size: 16px;\"\r\n            left-arrow @click-left=\"$router.go(-1)\">\r\n        </van-nav-bar>\r\n        <div\r\n            style=\"background: #f5f5f5;height: 2.5rem;width: 100%;display: flex;justify-content: center;align-items: center;color:#000;margin-top: -0.7rem;position: fixed;top: 3.5rem;\">\r\n            <div style=\"width: 10%;\">+</div>\r\n            <div style=\"width: 70%;\">\r\n                <input v-model=\"search\"\r\n                    style=\"width: 100%;height: 2.1rem;border-radius: 0.5rem;border: none;background: #e6e6e6;\"\r\n                    :placeholder=\"$t('msg.plz_areacode')\">\r\n            </div>\r\n            <div style=\"width: 20%;font-weight: bold;\">{{ $t('msg.yes') }}</div>\r\n        </div>\r\n\r\n        <div\r\n            style=\"margin-top: 1.7rem;border-top: 1px solid #ccc;background: #fff;display: flex;justify-content: center;align-items: center;flex-direction: column;color: #000;\">\r\n            <!-- :src=\"'~@/assets/images/areacode/'+ v[1] +'.png'\" -->\r\n\r\n            <div class=\"areaclass\" v-for=\"v in list\" v-if=\"!search\" @click=\"tourl(v[0])\">\r\n                <div style=\"display: flex;align-items: center;\">\r\n                    <img :src=\"require('@/assets/images/areacode/' + v[1] + '.png')\" style=\"width: 2.2rem;height: 1.5rem;\">\r\n                    +{{ v[0] }}\r\n                </div>\r\n                <div>{{ v[1] }}</div>\r\n            </div>\r\n\r\n            <div class=\"areaclass\" v-for=\"v in list2\" v-else @click=\"tourl(v[0])\">\r\n                <div style=\"display: flex;align-items: center;\">\r\n                    <img :src=\"require('@/assets/images/areacode/' + v[1] + '.png')\" style=\"width: 2.2rem;height: 1.5rem;\">\r\n                    +{{ v[0] }}\r\n                </div>\r\n                <div>{{ v[1] }}</div>\r\n            </div>\r\n\r\n            \r\n\r\n        </div>\r\n\r\n\r\n\r\n\r\n    </div>\r\n</template>\r\n<script>\r\nimport { ref, getCurrentInstance } from 'vue';\r\nimport { get_invite } from '@/api/self/index'\r\n\r\nimport { useI18n } from 'vue-i18n'\r\nimport store from '@/store/index'\r\nimport { formatTime } from '@/api/format.js'\r\nimport { watch } from 'vue';\r\nimport { useRoute } from \"vue-router\"\r\nimport { useRouter } from 'vue-router';\r\nexport default {\r\n    setup() {\r\n\r\n        const route = useRoute()\r\n        const { push } = useRouter();\r\n      \r\n        const url = route.query?.data || 1;\r\n     \r\n      \r\n        \r\n\r\n        const list = [\r\n            [1, 'US'],\r\n            [86, 'CN'],\r\n            [44, 'GB'],\r\n            [33, 'FR'],\r\n            [81, 'JP'],\r\n            [34, 'ES'],\r\n            [20, 'EG'],\r\n            [84, 'VN'],\r\n            [7, 'RU'],\r\n            [51, 'PE'],\r\n            [54, 'AR'],\r\n            [93, 'AF'],\r\n            [376, 'AD'],\r\n            [61, 'AU'],\r\n            [43, 'AT'],\r\n            [880, 'BD'],\r\n            [375, 'BY'],\r\n            [32, 'BE'],\r\n            [975, 'BT'],\r\n            [55, 'BR'],\r\n            [673, 'BN'],\r\n            [359, 'BG'],\r\n            [855, 'KH'],\r\n            [237, 'CM'],\r\n            ['001', 'CA'],\r\n            [236, 'CF'],\r\n            [56, 'CL'],\r\n            [57, 'CO'],\r\n            [242, 'CG'],\r\n            [53, 'CU'],\r\n            [420, 'CZ'],\r\n            [45, 'DK'],\r\n            [251, 'ET'],\r\n            [697, 'FJ'],\r\n            [358, 'FI'],\r\n            [30, 'GR'],\r\n            [299, 'GL'],\r\n            [852, 'HK'],\r\n            [36, 'HU'],\r\n            [354, 'IS'],\r\n            [91, 'IN'],\r\n            [62, 'ID'],\r\n            [972, 'IL'],\r\n            [39, 'IT'],\r\n            [49, 'DE'],\r\n            [98, 'IR'],\r\n            [1876, 'JM'],\r\n            [353, 'IE'],\r\n            [85, 'KP'],\r\n            [82, 'KR'],\r\n            [856, 'LA'],\r\n            [52, 'MX'],\r\n            [60, 'MY'],\r\n            [377, 'MC'],\r\n            [47, 'NO'],\r\n            [212, 'MA'],\r\n            [95, 'MM'],\r\n            [31, 'NL'],\r\n            [64, 'NZ'],\r\n            [63, 'PH'],\r\n            [48, 'PL'],\r\n            [351, 'PT'],\r\n            [92, 'PK'],\r\n            [974, 'QA'],\r\n            [40, 'RO'],\r\n            [250, 'RW'],\r\n            [966, 'SA'],\r\n            [65, 'SG'],\r\n            [421, 'SK'],\r\n            [27, 'ZA'],\r\n            [94, 'LK'],\r\n            [46, 'SE'],\r\n            [41, 'CH'],\r\n            [886, 'TW'],\r\n            [66, 'TH'],\r\n            [670, 'TL'],\r\n            [971, 'AE'],\r\n            [90, 'TR'],\r\n            [380, 'UA'],\r\n            [598, 'UY'],\r\n            [965, 'KW'],\r\n            [968, 'OM'],\r\n        ];\r\n\r\n        \r\n        const tourl = (v) => {\r\n            \r\n            if(url == 1){\r\n                push({ path: '/login',query:{data:v} })\r\n            }else{\r\n                push({ path: '/register',query:{data:v} })\r\n            }\r\n            \r\n\r\n        }\r\n\r\n\r\n        const search = ref('');\r\n        const list2 = [];\r\n        watch(() => search, (newVal) => {\r\n            list2.length = 0\r\n      \r\n            list.forEach(function(v){\r\n                let text = v[0];\r\n                text = text.toString()\r\n                let news = { ...newVal }._value\r\n\r\n                let ret = text.search(news);\r\n              \r\n                \r\n                if(ret != -1){\r\n                    list2.push(v)\r\n                }\r\n            })\r\n            console.log(list2)\r\n  \r\n        }, { deep: true })\r\n\r\n        return {\r\n            list,\r\n            list2,\r\n            search,\r\n            tourl,\r\n        }\r\n    },\r\n\r\n\r\n\r\n\r\n    mounted() {\r\n\r\n\r\n\r\n    }\r\n\r\n}\r\n\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.qr img {\r\n    width: 100px !important;\r\n    height: 100px !important;\r\n}\r\n\r\n.box_tlt {\r\n    height: 110px !important;\r\n    padding-left: 30px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: left;\r\n    color: #ffffff;\r\n\r\n    img {\r\n        width: 40px;\r\n        height: 40px;\r\n        margin-left: 30px;\r\n    }\r\n}\r\n\r\n.qr {\r\n    margin: 25px 0;\r\n    width: 400px;\r\n    height: 400px;\r\n    background-image: url(\"~@/assets/images/home/<USER>\");\r\n    background-size: cover;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n}\r\n\r\n.box_text {\r\n    display: block;\r\n    font-size: 25px;\r\n    font-weight: 800;\r\n    padding: 50px 20px;\r\n}\r\n\r\n.cop {\r\n    margin: 0 0 15px;\r\n    width: 70%;\r\n    height: 70px;\r\n    background-color: #000;\r\n    color: #fff;\r\n    border-radius: 20px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    margin: 50px auto;\r\n}\r\n\r\n.areaclass {\r\n    height: 3rem;\r\n    border-bottom: #ccc solid 1px;\r\n    width: 84%;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n}\r\n\r\n.box {\r\n    display: block;\r\n    box-sizing: border-box;\r\n    width: 100%;\r\n    padding: 15px;\r\n\r\n    .box_t {\r\n        width: 88%;\r\n        height: auto;\r\n        background-color: #fff;\r\n        border-radius: 25px;\r\n        overflow: hidden;\r\n        box-shadow: rgba(41, 5, 5, .15) 0 0 20px;\r\n        overflow: hidden;\r\n        margin: 0 auto;\r\n\r\n        .box_tlt {\r\n            width: 100%;\r\n            height: 60px;\r\n            background-color: #000;\r\n            display: flex;\r\n            align-items: center;\r\n            color: #fff;\r\n        }\r\n\r\n        .box_fot {\r\n            padding: 0 20px;\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: space-around;\r\n            flex-direction: column;\r\n        }\r\n    }\r\n}\r\n\r\n.tel {\r\n    overflow: hidden;\r\n\r\n    // background-image: url(\"~@/assets/images/home/<USER>\");\r\n    // background-size: 100% 100%;\r\n    position: relative;\r\n    background-color: #f5f5f5;\r\n\r\n\r\n    .yqhy {\r\n        height: 400px;\r\n        width: 100%;\r\n        background-color: $theme;\r\n        display: flex;\r\n        flex-direction: column;\r\n        justify-content: center;\r\n        color: #fff;\r\n        font-size: 50px;\r\n        padding-top: 60px;\r\n        font-weight: bold;\r\n    }\r\n\r\n    :deep(.van-nav-bar) {\r\n        padding: 0;\r\n        background-color: #ffffff;\r\n        position: absolute !important;\r\n        width: 100%;\r\n        background-color: inherit;\r\n        color: #fff;\r\n        z-index: 0;\r\n\r\n        .van-nav-bar__content {\r\n            background-color: rgb(254, 44, 85);\r\n            position: fixed;\r\n            top: 0;\r\n            width: 100%;\r\n        }\r\n\r\n        .van-nav-bar__left {\r\n\r\n            .van-icon {\r\n                color: #fff !important;\r\n            }\r\n        }\r\n\r\n        .van-nav-bar__left {\r\n            .van-icon {\r\n                color: #fff;\r\n            }\r\n        }\r\n\r\n        .van-nav-bar__title {\r\n            color: #fff;\r\n        }\r\n\r\n        .van-nav-bar__right {\r\n            .van-icon {\r\n                color: #fff;\r\n            }\r\n        }\r\n    }\r\n\r\n    .content {\r\n        width: 100%;\r\n        padding: 0 25px;\r\n        border-radius: 30px;\r\n        flex: 1;\r\n        overflow: auto;\r\n\r\n        // background-color: #fff;\r\n        .bottom {\r\n            width: 100%;\r\n            height: 74px;\r\n            line-height: 74px;\r\n            font-size: 32px;\r\n            background-color: $theme;\r\n            color: #fff;\r\n            border-radius: 6px;\r\n            margin-top: 20px;\r\n        }\r\n\r\n        .top {\r\n            padding: 20px;\r\n            background-color: #fff;\r\n            border-radius: 20px;\r\n\r\n            .title {\r\n                margin-bottom: 20px;\r\n            }\r\n\r\n            .c {\r\n                font-size: 22px;\r\n                line-height: 2;\r\n                text-indent: 2em;\r\n                color: #666;\r\n                margin-bottom: 20px;\r\n            }\r\n\r\n            .b {\r\n                width: 290px;\r\n                margin: 0 auto;\r\n                font-size: 26px;\r\n                color: #333;\r\n                position: relative;\r\n\r\n                .span {\r\n                    margin-left: 5px;\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n</style>"], "mappings": ";;;;;EACSA,KAAK,EAAC;AAAU;;EAKbC,KAA4K,EAA5K;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;EAAA;AAA4K;;sBAC5KC,mBAAA,CAAgC;IAA3BD,KAAmB,EAAnB;MAAA;IAAA;EAAmB,GAAC,GAAC;AAAA;;EACrBA,KAAmB,EAAnB;IAAA;EAAA;AAAmB;;;EAKnBA,KAAqC,EAArC;IAAA;IAAA;EAAA;AAAqC;;EAI1CA,KAAoK,EAApK;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;EAAA;AAAoK;;;EAI3JA,KAA0C,EAA1C;IAAA;IAAA;EAAA;AAA0C;;;;EAQ1CA,KAA0C,EAA1C;IAAA;IAAA;EAAA;AAA0C;;;;uBA5B3DE,mBAAA,CA0CM,OA1CNC,UA0CM,GAzCFC,YAAA,CAEcC,sBAAA;IAFAC,KAAK,EAAE,WAAW;IAAEC,UAAU,EAAC,kBAAkB;IAAC,aAAW,EAAC,+BAA+B;IACvG,YAAU,EAAV,EAAU;IAAEC,WAAU,EAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAAEC,IAAA,CAAAC,OAAO,CAACC,EAAE;IAAA;MAEtCZ,mBAAA,CASM,OATNa,UASM,GAPFC,UAAgC,EAChCd,mBAAA,CAIM,OAJNe,UAIM,G,gBAHFf,mBAAA,CAE0C;;aAF1BgB,MAAA,CAAAC,MAAM,GAAAR,MAAA;IAAA;IAClBV,KAA0F,EAA1F;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA,CAA0F;IACzFmB,WAAW,EAAER,IAAA,CAAAS,EAAE;sDAFJH,MAAA,CAAAC,MAAM,E,KAI1BjB,mBAAA,CAAoE,OAApEoB,UAAoE,EAAAC,gBAAA,CAAtBX,IAAA,CAAAS,EAAE,4B,GAGpDnB,mBAAA,CAsBM,OAtBNsB,UAsBM,GApBFC,mBAAA,yDAA0D,E,CAEVP,MAAA,CAAAC,MAAM,I,kBAAtDhB,mBAAA,CAMMuB,SAAA;IAAAC,GAAA;EAAA,GAAAC,WAAA,CAN6BV,MAAA,CAAAW,IAAI,YAATC,CAAC;yBAA/B3B,mBAAA,CAMM;MANDH,KAAK,EAAC,WAAW;MAAmC+B,OAAK,WAAAA,QAAApB,MAAA;QAAA,OAAEO,MAAA,CAAAc,KAAK,CAACF,CAAC;MAAA;QACnE5B,mBAAA,CAGM,OAHN+B,UAGM,GAFF/B,mBAAA,CAAuG;MAAjGgC,GAAG,EAAEC,OAAO,+BAA+BL,CAAC;MAAe7B,KAAqC,EAArC;QAAA;QAAA;MAAA;2DAAsC,IACtG,GAAAsB,gBAAA,CAAGO,CAAC,oB,GAET5B,mBAAA,CAAqB,aAAAqB,gBAAA,CAAbO,CAAC,oB;wDAGb3B,mBAAA,CAMMuB,SAAA;IAAAC,GAAA;EAAA,GAAAC,WAAA,CAN6BV,MAAA,CAAAkB,KAAK,YAAVN,CAAC;yBAA/B3B,mBAAA,CAMM;MANDH,KAAK,EAAC,WAAW;MAA4B+B,OAAK,WAAAA,QAAApB,MAAA;QAAA,OAAEO,MAAA,CAAAc,KAAK,CAACF,CAAC;MAAA;QAC5D5B,mBAAA,CAGM,OAHNmC,WAGM,GAFFnC,mBAAA,CAAuG;MAAjGgC,GAAG,EAAEC,OAAO,+BAA+BL,CAAC;MAAe7B,KAAqC,EAArC;QAAA;QAAA;MAAA;2DAAsC,IACtG,GAAAsB,gBAAA,CAAGO,CAAC,oB,GAET5B,mBAAA,CAAqB,aAAAqB,gBAAA,CAAbO,CAAC,oB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}