{"ast": null, "code": "function _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return _typeof(key) === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (_typeof(input) !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (_typeof(res) !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\nfunction _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nfunction _iterableToArrayLimit(arr, i) { var _i = null == arr ? null : \"undefined\" != typeof Symbol && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (null != _i) { var _s, _e, _x, _r, _arr = [], _n = !0, _d = !1; try { if (_x = (_i = _i.call(arr)).next, 0 === i) { if (Object(_i) !== _i) return; _n = !1; } else for (; !(_n = (_s = _x.call(_i)).done) && (_arr.push(_s.value), _arr.length !== i); _n = !0); } catch (err) { _d = !0, _e = err; } finally { try { if (!_n && null != _i.return && (_r = _i.return(), Object(_r) !== _r)) return; } finally { if (_d) throw _e; } } return _arr; } }\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\nimport { createVNode as _createVNode } from \"vue\";\nimport { ref, watch, reactive, defineComponent } from \"vue\";\nimport { deepClone } from \"../utils/deep-clone.mjs\";\nimport { clamp, isObject, unknownProp, numericProp, makeArrayProp, makeNumberProp, preventDefault, createNamespace, makeRequiredProp } from \"../utils/index.mjs\";\nimport { useEventListener, useParent } from \"@vant/use\";\nimport { useTouch } from \"../composables/use-touch.mjs\";\nimport { useExpose } from \"../composables/use-expose.mjs\";\nvar DEFAULT_DURATION = 200;\nvar MOMENTUM_LIMIT_TIME = 300;\nvar MOMENTUM_LIMIT_DISTANCE = 15;\nvar _createNamespace = createNamespace(\"picker-column\"),\n  _createNamespace2 = _slicedToArray(_createNamespace, 2),\n  name = _createNamespace2[0],\n  bem = _createNamespace2[1];\nfunction getElementTranslateY(element) {\n  var _window$getComputedSt = window.getComputedStyle(element),\n    transform = _window$getComputedSt.transform;\n  var translateY = transform.slice(7, transform.length - 1).split(\", \")[5];\n  return Number(translateY);\n}\nvar PICKER_KEY = Symbol(name);\nvar isOptionDisabled = function isOptionDisabled(option) {\n  return isObject(option) && option.disabled;\n};\nvar stdin_default = defineComponent({\n  name: name,\n  props: {\n    textKey: makeRequiredProp(String),\n    readonly: Boolean,\n    allowHtml: Boolean,\n    className: unknownProp,\n    itemHeight: makeRequiredProp(Number),\n    defaultIndex: makeNumberProp(0),\n    swipeDuration: makeRequiredProp(numericProp),\n    initialOptions: makeArrayProp(),\n    visibleItemCount: makeRequiredProp(numericProp)\n  },\n  emits: [\"change\"],\n  setup: function setup(props, _ref) {\n    var emit = _ref.emit,\n      slots = _ref.slots;\n    var moving;\n    var startOffset;\n    var touchStartTime;\n    var momentumOffset;\n    var transitionEndTrigger;\n    var root = ref();\n    var wrapper = ref();\n    var state = reactive({\n      index: props.defaultIndex,\n      offset: 0,\n      duration: 0,\n      options: deepClone(props.initialOptions)\n    });\n    var touch = useTouch();\n    var count = function count() {\n      return state.options.length;\n    };\n    var baseOffset = function baseOffset() {\n      return props.itemHeight * (+props.visibleItemCount - 1) / 2;\n    };\n    var adjustIndex = function adjustIndex(index) {\n      index = clamp(index, 0, count());\n      for (var i = index; i < count(); i++) {\n        if (!isOptionDisabled(state.options[i])) return i;\n      }\n      for (var _i2 = index - 1; _i2 >= 0; _i2--) {\n        if (!isOptionDisabled(state.options[_i2])) return _i2;\n      }\n    };\n    var setIndex = function setIndex(index, emitChange) {\n      index = adjustIndex(index) || 0;\n      var offset = -index * props.itemHeight;\n      var trigger = function trigger() {\n        if (index !== state.index) {\n          state.index = index;\n          if (emitChange) {\n            emit(\"change\", index);\n          }\n        }\n      };\n      if (moving && offset !== state.offset) {\n        transitionEndTrigger = trigger;\n      } else {\n        trigger();\n      }\n      state.offset = offset;\n    };\n    var setOptions = function setOptions(options) {\n      if (JSON.stringify(options) !== JSON.stringify(state.options)) {\n        state.options = deepClone(options);\n        setIndex(props.defaultIndex);\n      }\n    };\n    var onClickItem = function onClickItem(index) {\n      if (moving || props.readonly) {\n        return;\n      }\n      transitionEndTrigger = null;\n      state.duration = DEFAULT_DURATION;\n      setIndex(index, true);\n    };\n    var getOptionText = function getOptionText(option) {\n      if (isObject(option) && props.textKey in option) {\n        return option[props.textKey];\n      }\n      return option;\n    };\n    var getIndexByOffset = function getIndexByOffset(offset) {\n      return clamp(Math.round(-offset / props.itemHeight), 0, count() - 1);\n    };\n    var momentum = function momentum(distance, duration) {\n      var speed = Math.abs(distance / duration);\n      distance = state.offset + speed / 3e-3 * (distance < 0 ? -1 : 1);\n      var index = getIndexByOffset(distance);\n      state.duration = +props.swipeDuration;\n      setIndex(index, true);\n    };\n    var stopMomentum = function stopMomentum() {\n      moving = false;\n      state.duration = 0;\n      if (transitionEndTrigger) {\n        transitionEndTrigger();\n        transitionEndTrigger = null;\n      }\n    };\n    var onTouchStart = function onTouchStart(event) {\n      if (props.readonly) {\n        return;\n      }\n      touch.start(event);\n      if (moving) {\n        var translateY = getElementTranslateY(wrapper.value);\n        state.offset = Math.min(0, translateY - baseOffset());\n        startOffset = state.offset;\n      } else {\n        startOffset = state.offset;\n      }\n      state.duration = 0;\n      touchStartTime = Date.now();\n      momentumOffset = startOffset;\n      transitionEndTrigger = null;\n    };\n    var onTouchMove = function onTouchMove(event) {\n      if (props.readonly) {\n        return;\n      }\n      touch.move(event);\n      if (touch.isVertical()) {\n        moving = true;\n        preventDefault(event, true);\n      }\n      state.offset = clamp(startOffset + touch.deltaY.value, -(count() * props.itemHeight), props.itemHeight);\n      var now = Date.now();\n      if (now - touchStartTime > MOMENTUM_LIMIT_TIME) {\n        touchStartTime = now;\n        momentumOffset = state.offset;\n      }\n    };\n    var onTouchEnd = function onTouchEnd() {\n      if (props.readonly) {\n        return;\n      }\n      var distance = state.offset - momentumOffset;\n      var duration = Date.now() - touchStartTime;\n      var allowMomentum = duration < MOMENTUM_LIMIT_TIME && Math.abs(distance) > MOMENTUM_LIMIT_DISTANCE;\n      if (allowMomentum) {\n        momentum(distance, duration);\n        return;\n      }\n      var index = getIndexByOffset(state.offset);\n      state.duration = DEFAULT_DURATION;\n      setIndex(index, true);\n      setTimeout(function () {\n        moving = false;\n      }, 0);\n    };\n    var renderOptions = function renderOptions() {\n      var optionStyle = {\n        height: \"\".concat(props.itemHeight, \"px\")\n      };\n      return state.options.map(function (option, index) {\n        var text = getOptionText(option);\n        var disabled = isOptionDisabled(option);\n        var data = {\n          role: \"button\",\n          style: optionStyle,\n          tabindex: disabled ? -1 : 0,\n          class: bem(\"item\", {\n            disabled: disabled,\n            selected: index === state.index\n          }),\n          onClick: function onClick() {\n            return onClickItem(index);\n          }\n        };\n        var childData = _defineProperty({\n          class: \"van-ellipsis\"\n        }, props.allowHtml ? \"innerHTML\" : \"textContent\", text);\n        return _createVNode(\"li\", data, [slots.option ? slots.option(option) : _createVNode(\"div\", childData, null)]);\n      });\n    };\n    var setValue = function setValue(value) {\n      var options = state.options;\n      for (var i = 0; i < options.length; i++) {\n        if (getOptionText(options[i]) === value) {\n          return setIndex(i);\n        }\n      }\n    };\n    var getValue = function getValue() {\n      return state.options[state.index];\n    };\n    var hasOptions = function hasOptions() {\n      return state.options.length;\n    };\n    setIndex(state.index);\n    useParent(PICKER_KEY);\n    useExpose({\n      state: state,\n      setIndex: setIndex,\n      getValue: getValue,\n      setValue: setValue,\n      setOptions: setOptions,\n      hasOptions: hasOptions,\n      stopMomentum: stopMomentum\n    });\n    watch(function () {\n      return props.initialOptions;\n    }, setOptions);\n    watch(function () {\n      return props.defaultIndex;\n    }, function (value) {\n      return setIndex(value);\n    });\n    useEventListener(\"touchmove\", onTouchMove, {\n      target: root\n    });\n    return function () {\n      return _createVNode(\"div\", {\n        \"ref\": root,\n        \"class\": [bem(), props.className],\n        \"onTouchstartPassive\": onTouchStart,\n        \"onTouchend\": onTouchEnd,\n        \"onTouchcancel\": onTouchEnd\n      }, [_createVNode(\"ul\", {\n        \"ref\": wrapper,\n        \"style\": {\n          transform: \"translate3d(0, \".concat(state.offset + baseOffset(), \"px, 0)\"),\n          transitionDuration: \"\".concat(state.duration, \"ms\"),\n          transitionProperty: state.duration ? \"all\" : \"none\"\n        },\n        \"class\": bem(\"wrapper\"),\n        \"onTransitionend\": stopMomentum\n      }, [renderOptions()])]);\n    };\n  }\n});\nexport { PICKER_KEY, stdin_default as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}