{"ast": null, "code": "function _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread(); }\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _iterableToArray(iter) { if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter); }\nfunction _arrayWithoutHoles(arr) { if (Array.isArray(arr)) return _arrayLikeToArray(arr); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nimport { isObject, isPromise, isFunction, getRootScrollTop, setRootScrollTop } from \"../utils/index.mjs\";\nfunction isEmptyValue(value) {\n  if (Array.isArray(value)) {\n    return !value.length;\n  }\n  if (value === 0) {\n    return false;\n  }\n  return !value;\n}\nfunction runSyncRule(value, rule) {\n  if (isEmptyValue(value)) {\n    if (rule.required) {\n      return false;\n    }\n    if (rule.validateEmpty === false) {\n      return true;\n    }\n  }\n  if (rule.pattern && !rule.pattern.test(String(value))) {\n    return false;\n  }\n  return true;\n}\nfunction runRuleValidator(value, rule) {\n  return new Promise(function (resolve) {\n    var returnVal = rule.validator(value, rule);\n    if (isPromise(returnVal)) {\n      returnVal.then(resolve);\n      return;\n    }\n    resolve(returnVal);\n  });\n}\nfunction getRuleMessage(value, rule) {\n  var message = rule.message;\n  if (isFunction(message)) {\n    return message(value, rule);\n  }\n  return message || \"\";\n}\nfunction startComposing(_ref) {\n  var target = _ref.target;\n  target.composing = true;\n}\nfunction endComposing(_ref2) {\n  var target = _ref2.target;\n  if (target.composing) {\n    target.composing = false;\n    target.dispatchEvent(new Event(\"input\"));\n  }\n}\nfunction resizeTextarea(input, autosize) {\n  var scrollTop = getRootScrollTop();\n  input.style.height = \"auto\";\n  var height = input.scrollHeight;\n  if (isObject(autosize)) {\n    var maxHeight = autosize.maxHeight,\n      minHeight = autosize.minHeight;\n    if (maxHeight !== void 0) {\n      height = Math.min(height, maxHeight);\n    }\n    if (minHeight !== void 0) {\n      height = Math.max(height, minHeight);\n    }\n  }\n  if (height) {\n    input.style.height = \"\".concat(height, \"px\");\n    setRootScrollTop(scrollTop);\n  }\n}\nfunction mapInputType(type) {\n  if (type === \"number\") {\n    return {\n      type: \"text\",\n      inputmode: \"decimal\"\n    };\n  }\n  if (type === \"digit\") {\n    return {\n      type: \"tel\",\n      inputmode: \"numeric\"\n    };\n  }\n  return {\n    type: type\n  };\n}\nfunction getStringLength(str) {\n  return _toConsumableArray(str).length;\n}\nfunction cutString(str, maxlength) {\n  return _toConsumableArray(str).slice(0, maxlength).join(\"\");\n}\nexport { cutString, endComposing, getRuleMessage, getStringLength, isEmptyValue, mapInputType, resizeTextarea, runRuleValidator, runSyncRule, startComposing };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}