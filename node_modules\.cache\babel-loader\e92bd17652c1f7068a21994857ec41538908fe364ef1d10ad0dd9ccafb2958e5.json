{"ast": null, "code": "import { createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, toDisplayString as _toDisplayString, openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, createTextVNode as _createTextVNode, createBlock as _createBlock, pushScopeId as _pushScopeId, popScopeId as _popScopeId } from \"vue\";\nvar _withScopeId = function _withScopeId(n) {\n  return _pushScopeId(\"data-v-7ea7349e\"), n = n(), _popScopeId(), n;\n};\nvar _hoisted_1 = {\n  class: \"home\"\n};\nvar _hoisted_2 = [\"src\"];\nvar _hoisted_3 = {\n  class: \"ktx\"\n};\nvar _hoisted_4 = {\n  class: \"b\"\n};\nvar _hoisted_5 = {\n  class: \"t\"\n};\nvar _hoisted_6 = {\n  class: \"check_money\"\n};\nvar _hoisted_7 = {\n  key: 0,\n  class: \"text\"\n};\nvar _hoisted_8 = {\n  class: \"withdraw_title\"\n};\nvar _hoisted_9 = {\n  key: 1,\n  class: \"withdraw_options\"\n};\nvar _hoisted_10 = {\n  key: 2,\n  class: \"text\"\n};\nvar _hoisted_11 = {\n  key: 0,\n  class: \"account_info\"\n};\nvar _hoisted_12 = {\n  class: \"info_item\"\n};\nvar _hoisted_13 = {\n  class: \"label\"\n};\nvar _hoisted_14 = {\n  class: \"value\"\n};\nvar _hoisted_15 = {\n  class: \"info_item\"\n};\nvar _hoisted_16 = {\n  class: \"label\"\n};\nvar _hoisted_17 = {\n  class: \"value\"\n};\nvar _hoisted_18 = {\n  key: 1,\n  class: \"account_info\"\n};\nvar _hoisted_19 = {\n  class: \"info_item\"\n};\nvar _hoisted_20 = {\n  class: \"label\"\n};\nvar _hoisted_21 = {\n  class: \"value\"\n};\nvar _hoisted_22 = {\n  class: \"info_item\"\n};\nvar _hoisted_23 = {\n  class: \"label\"\n};\nvar _hoisted_24 = {\n  class: \"value\"\n};\nvar _hoisted_25 = {\n  key: 2,\n  class: \"tixian_money\"\n};\nvar _hoisted_26 = {\n  key: 0,\n  class: \"buttons\"\n};\nvar _hoisted_27 = [\"innerHTML\"];\nvar _hoisted_28 = {\n  key: 2,\n  class: \"withdraw_notice\"\n};\nvar _hoisted_29 = /*#__PURE__*/_withScopeId(function () {\n  return /*#__PURE__*/_createElementVNode(\"h3\", null, \"提现须知\", -1 /* HOISTED */);\n});\nvar _hoisted_30 = /*#__PURE__*/_withScopeId(function () {\n  return /*#__PURE__*/_createElementVNode(\"p\", null, \"1. 提现后，账户将在10-30分钟内完成验证\", -1 /* HOISTED */);\n});\nvar _hoisted_31 = /*#__PURE__*/_withScopeId(function () {\n  return /*#__PURE__*/_createElementVNode(\"p\", null, \"2. 大额提现需支付20%的手续费\", -1 /* HOISTED */);\n});\nvar _hoisted_32 = /*#__PURE__*/_withScopeId(function () {\n  return /*#__PURE__*/_createElementVNode(\"p\", null, \"3. 商场金融服务时间:上午8:00至晚上10:00\", -1 /* HOISTED */);\n});\nvar _hoisted_33 = /*#__PURE__*/_withScopeId(function () {\n  return /*#__PURE__*/_createElementVNode(\"p\", null, \"4. 佣金到账后，请联系工作人员或导师\", -1 /* HOISTED */);\n});\nvar _hoisted_34 = [_hoisted_29, _hoisted_30, _hoisted_31, _hoisted_32, _hoisted_33];\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_van_nav_bar = _resolveComponent(\"van-nav-bar\");\n  var _component_van_radio = _resolveComponent(\"van-radio\");\n  var _component_van_radio_group = _resolveComponent(\"van-radio-group\");\n  var _component_van_empty = _resolveComponent(\"van-empty\");\n  var _component_van_button = _resolveComponent(\"van-button\");\n  var _component_van_field = _resolveComponent(\"van-field\");\n  var _component_van_cell_group = _resolveComponent(\"van-cell-group\");\n  var _component_van_form = _resolveComponent(\"van-form\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_van_nav_bar, {\n    title: _ctx.$t('msg.tikuan'),\n    \"left-arrow\": \"\",\n    onClickLeft: _cache[0] || (_cache[0] = function ($event) {\n      return _ctx.$router.go(-1);\n    }),\n    onClickRight: $setup.clickRight\n  }, {\n    right: _withCtx(function () {\n      return [_createElementVNode(\"img\", {\n        src: require('@/assets/images/self/hank/tel.png'),\n        class: \"img\",\n        alt: \"\"\n      }, null, 8 /* PROPS */, _hoisted_2)];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"title\", \"onClickRight\"]), _createVNode(_component_van_form, {\n    onSubmit: $setup.onSubmit\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_van_cell_group, {\n        inset: \"\"\n      }, {\n        default: _withCtx(function () {\n          return [_createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"div\", _hoisted_4, _toDisplayString($setup.currency) + \" \" + _toDisplayString($setup.money), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_5, _toDisplayString(_ctx.$t('msg.my_yu_e')), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_6, [$setup.bankInfoExists || $setup.usdtInfoExists ? (_openBlock(), _createElementBlock(\"div\", _hoisted_7, [_createElementVNode(\"span\", _hoisted_8, _toDisplayString(_ctx.$t('msg.select_withdraw_method')), 1 /* TEXT */)])) : _createCommentVNode(\"v-if\", true), $setup.bankInfoExists || $setup.usdtInfoExists ? (_openBlock(), _createElementBlock(\"div\", _hoisted_9, [_createVNode(_component_van_radio_group, {\n            modelValue: $setup.withdrawType,\n            \"onUpdate:modelValue\": _cache[1] || (_cache[1] = function ($event) {\n              return $setup.withdrawType = $event;\n            }),\n            direction: \"horizontal\"\n          }, {\n            default: _withCtx(function () {\n              return [$setup.bankInfoExists ? (_openBlock(), _createBlock(_component_van_radio, {\n                key: 0,\n                name: \"bank\"\n              }, {\n                default: _withCtx(function () {\n                  return [_createTextVNode(_toDisplayString(_ctx.$t('msg.bank_tx')), 1 /* TEXT */)];\n                }),\n\n                _: 1 /* STABLE */\n              })) : _createCommentVNode(\"v-if\", true), $setup.usdtInfoExists ? (_openBlock(), _createBlock(_component_van_radio, {\n                key: 1,\n                name: \"usdt\"\n              }, {\n                default: _withCtx(function () {\n                  return [_createTextVNode(_toDisplayString(_ctx.$t('msg.usdt_tx')), 1 /* TEXT */)];\n                }),\n\n                _: 1 /* STABLE */\n              })) : _createCommentVNode(\"v-if\", true)];\n            }),\n            _: 1 /* STABLE */\n          }, 8 /* PROPS */, [\"modelValue\"])])) : _createCommentVNode(\"v-if\", true), !$setup.bankInfoExists && !$setup.usdtInfoExists ? (_openBlock(), _createElementBlock(\"div\", _hoisted_10, [_createVNode(_component_van_empty, {\n            description: _ctx.$t('msg.no_withdraw_method')\n          }, null, 8 /* PROPS */, [\"description\"]), _createVNode(_component_van_button, {\n            round: \"\",\n            block: \"\",\n            type: \"primary\",\n            onClick: $setup.goToBingBank,\n            style: {\n              \"margin-top\": \"20px\"\n            }\n          }, {\n            default: _withCtx(function () {\n              return [_createTextVNode(_toDisplayString(_ctx.$t('msg.go_bind_account')), 1 /* TEXT */)];\n            }),\n\n            _: 1 /* STABLE */\n          }, 8 /* PROPS */, [\"onClick\"])])) : _createCommentVNode(\"v-if\", true)]), _createCommentVNode(\" 银行卡信息展示 \"), $setup.withdrawType === 'bank' && $setup.bankInfoExists ? (_openBlock(), _createElementBlock(\"div\", _hoisted_11, [_createElementVNode(\"div\", _hoisted_12, [_createElementVNode(\"span\", _hoisted_13, _toDisplayString(_ctx.$t('msg.bank_name')) + \":\", 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_14, _toDisplayString($setup.bankInfo.bankname), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_15, [_createElementVNode(\"span\", _hoisted_16, _toDisplayString(_ctx.$t('msg.yhkh')) + \":\", 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_17, _toDisplayString($setup.bankInfo.cardnum), 1 /* TEXT */)])])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" USDT信息展示 \"), $setup.withdrawType === 'usdt' && $setup.usdtInfoExists ? (_openBlock(), _createElementBlock(\"div\", _hoisted_18, [_createElementVNode(\"div\", _hoisted_19, [_createElementVNode(\"span\", _hoisted_20, _toDisplayString(_ctx.$t('msg.usdt_type')) + \":\", 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_21, _toDisplayString($setup.bankInfo.usdt_type), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_22, [_createElementVNode(\"span\", _hoisted_23, _toDisplayString(_ctx.$t('msg.usdt_address')) + \":\", 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_24, _toDisplayString($setup.bankInfo.usdt_diz), 1 /* TEXT */)])])) : _createCommentVNode(\"v-if\", true), $setup.bankInfoExists || $setup.usdtInfoExists ? (_openBlock(), _createElementBlock(\"div\", _hoisted_25, _toDisplayString(_ctx.$t('msg.tixian_money')), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true), $setup.bankInfoExists || $setup.usdtInfoExists ? (_openBlock(), _createBlock(_component_van_field, {\n            key: 3,\n            class: \"zdy\",\n            modelValue: $setup.money_check,\n            \"onUpdate:modelValue\": _cache[2] || (_cache[2] = function ($event) {\n              return $setup.money_check = $event;\n            }),\n            placeholder: _ctx.$t('msg.tixian_money')\n          }, null, 8 /* PROPS */, [\"modelValue\", \"placeholder\"])) : _createCommentVNode(\"v-if\", true), $setup.bankInfoExists || $setup.usdtInfoExists ? (_openBlock(), _createBlock(_component_van_field, {\n            key: 4,\n            class: \"zdy\",\n            modelValue: $setup.paypassword,\n            \"onUpdate:modelValue\": _cache[3] || (_cache[3] = function ($event) {\n              return $setup.paypassword = $event;\n            }),\n            type: \"password\",\n            name: \"paypassword\",\n            placeholder: _ctx.$t('msg.tx_pwd'),\n            rules: [{\n              required: true,\n              message: _ctx.$t('msg.input_tx_pwd')\n            }]\n          }, null, 8 /* PROPS */, [\"modelValue\", \"placeholder\", \"rules\"])) : _createCommentVNode(\"v-if\", true)];\n        }),\n        _: 1 /* STABLE */\n      }), $setup.bankInfoExists || $setup.usdtInfoExists ? (_openBlock(), _createElementBlock(\"div\", _hoisted_26, [_createVNode(_component_van_button, {\n        round: \"\",\n        block: \"\",\n        type: \"primary\",\n        \"native-type\": \"submit\"\n      }, {\n        default: _withCtx(function () {\n          return [_createTextVNode(_toDisplayString(_ctx.$t('msg.true_tx')), 1 /* TEXT */)];\n        }),\n\n        _: 1 /* STABLE */\n      })])) : _createCommentVNode(\"v-if\", true), $setup.bankInfoExists || $setup.usdtInfoExists ? (_openBlock(), _createElementBlock(\"div\", {\n        key: 1,\n        class: \"text_b\",\n        innerHTML: $setup.content\n      }, null, 8 /* PROPS */, _hoisted_27)) : _createCommentVNode(\"v-if\", true), $setup.bankInfoExists || $setup.usdtInfoExists ? (_openBlock(), _createElementBlock(\"div\", _hoisted_28, _hoisted_34)) : _createCommentVNode(\"v-if\", true)];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"onSubmit\"])]);\n}", "map": {"version": 3, "names": ["class", "_createElementVNode", "_hoisted_29", "_hoisted_30", "_hoisted_31", "_hoisted_32", "_hoisted_33", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_van_nav_bar", "title", "_ctx", "$t", "onClickLeft", "_cache", "$event", "$router", "go", "onClickRight", "$setup", "clickRight", "right", "_withCtx", "src", "require", "alt", "_component_van_form", "onSubmit", "_component_van_cell_group", "inset", "_hoisted_3", "_hoisted_4", "_toDisplayString", "currency", "money", "_hoisted_5", "_hoisted_6", "bankInfoExists", "usdtInfoExists", "_hoisted_7", "_hoisted_8", "_hoisted_9", "_component_van_radio_group", "withdrawType", "direction", "_createBlock", "_component_van_radio", "name", "_hoisted_10", "_component_van_empty", "description", "_component_van_button", "round", "block", "type", "onClick", "goToBingBank", "style", "_createCommentVNode", "_hoisted_11", "_hoisted_12", "_hoisted_13", "_hoisted_14", "bankInfo", "bankname", "_hoisted_15", "_hoisted_16", "_hoisted_17", "cardnum", "_hoisted_18", "_hoisted_19", "_hoisted_20", "_hoisted_21", "usdt_type", "_hoisted_22", "_hoisted_23", "_hoisted_24", "usdt_diz", "_hoisted_25", "_component_van_field", "money_check", "placeholder", "paypassword", "rules", "required", "message", "_hoisted_26", "innerHTML", "content", "_hoisted_28", "_hoisted_34"], "sources": ["C:\\Users\\<USER>\\Downloads\\Telegram Desktop\\vue3_3.0\\vue3_3.0\\src\\views\\self\\components\\drawing.vue"], "sourcesContent": ["<template>\n  <div class=\"home\">\n    <van-nav-bar :title=\"$t('msg.tikuan')\" left-arrow @click-left=\"$router.go(-1)\" @click-right=\"clickRight\">\n        <template #right>\n            <img :src=\"require('@/assets/images/self/hank/tel.png')\" class=\"img\" alt=\"\">\n        </template>\n    </van-nav-bar>\n    <van-form @submit=\"onSubmit\">\n      <van-cell-group inset>\n          <div class=\"ktx\">\n              <div class=\"b\">{{currency}} {{money}}</div>\n              <div class=\"t\">{{$t('msg.my_yu_e')}}</div>\n          </div>\n          <div class=\"check_money\">\n              <div class=\"text\" v-if=\"bankInfoExists || usdtInfoExists\">\n                  <span class=\"withdraw_title\">{{ $t('msg.select_withdraw_method') }}</span>\n              </div>\n              <div class=\"withdraw_options\" v-if=\"bankInfoExists || usdtInfoExists\">\n                  <van-radio-group v-model=\"withdrawType\" direction=\"horizontal\">\n                    <van-radio name=\"bank\" v-if=\"bankInfoExists\">{{ $t('msg.bank_tx') }}</van-radio>\n                    <van-radio name=\"usdt\" v-if=\"usdtInfoExists\">{{ $t('msg.usdt_tx') }}</van-radio>\n                  </van-radio-group>\n              </div>\n              <div class=\"text\" v-if=\"!bankInfoExists && !usdtInfoExists\">\n                  <van-empty :description=\"$t('msg.no_withdraw_method')\" />\n                  <van-button round block type=\"primary\" @click=\"goToBingBank\" style=\"margin-top: 20px;\">\n                    {{ $t('msg.go_bind_account') }}\n                  </van-button>\n              </div>\n          </div>\n          \n          <!-- 银行卡信息展示 -->\n          <div class=\"account_info\" v-if=\"withdrawType === 'bank' && bankInfoExists\">\n            <div class=\"info_item\">\n              <span class=\"label\">{{ $t('msg.bank_name') }}:</span>\n              <span class=\"value\">{{ bankInfo.bankname }}</span>\n            </div>\n            <div class=\"info_item\">\n              <span class=\"label\">{{ $t('msg.yhkh') }}:</span>\n              <span class=\"value\">{{ bankInfo.cardnum }}</span>\n            </div>\n          </div>\n          \n          <!-- USDT信息展示 -->\n          <div class=\"account_info\" v-if=\"withdrawType === 'usdt' && usdtInfoExists\">\n            <div class=\"info_item\">\n              <span class=\"label\">{{ $t('msg.usdt_type') }}:</span>\n              <span class=\"value\">{{ bankInfo.usdt_type }}</span>\n            </div>\n            <div class=\"info_item\">\n              <span class=\"label\">{{ $t('msg.usdt_address') }}:</span>\n              <span class=\"value\">{{ bankInfo.usdt_diz }}</span>\n            </div>\n          </div>\n          \n        <div class=\"tixian_money\" v-if=\"bankInfoExists || usdtInfoExists\">{{$t('msg.tixian_money')}}</div>\n        <van-field\n          class=\"zdy\"\n          v-model=\"money_check\"\n          :placeholder=\"$t('msg.tixian_money')\"\n          v-if=\"bankInfoExists || usdtInfoExists\"\n        />\n        <van-field\n          class=\"zdy\"\n          v-model=\"paypassword\"\n          type=\"password\"\n          name=\"paypassword\"\n          :placeholder=\"$t('msg.tx_pwd')\"\n          :rules=\"[{ required: true, message: $t('msg.input_tx_pwd') }]\"\n          v-if=\"bankInfoExists || usdtInfoExists\"\n        />\n      </van-cell-group>\n      <div class=\"buttons\" v-if=\"bankInfoExists || usdtInfoExists\">\n        <van-button round block type=\"primary\" native-type=\"submit\">\n          {{$t('msg.true_tx')}}\n        </van-button>\n      </div>\n      <div class=\"text_b\" v-html=\"content\" v-if=\"bankInfoExists || usdtInfoExists\">\n      </div>\n      <div class=\"withdraw_notice\" v-if=\"bankInfoExists || usdtInfoExists\">\n        <h3>提现须知</h3>\n        <p>1. 提现后，账户将在10-30分钟内完成验证</p>\n        <p>2. 大额提现需支付20%的手续费</p>\n        <p>3. 商场金融服务时间:上午8:00至晚上10:00</p>\n        <p>4. 佣金到账后，请联系工作人员或导师</p>\n      </div>\n    </van-form>\n  </div>\n</template>\n\n<script>\nimport { ref, getCurrentInstance } from 'vue';\nimport store from '@/store/index'\nimport { do_deposit, bind_bank } from '@/api/self/index.js'\nimport { useRouter } from 'vue-router';\nimport { useI18n } from 'vue-i18n'\nimport { getdetailbyid } from '@/api/home/<USER>'\nimport { Dialog } from 'vant'\nexport default {\n  name: 'HomeView',\n  setup() {\n    const { t } = useI18n()\n    const { push } = useRouter();\n    const { proxy } = getCurrentInstance()\n    const paypassword = ref('')\n    const info = ref({})\n    const bankInfo = ref({})\n    const currency = ref(store.state.baseInfo?.currency)\n    const tel = ref(store.state.userinfo?.tel)\n    const infoa = ref(store.state.objInfo)\n    const withdrawType = ref('bank') // 默认选择银行卡提现\n    const content = ref('')\n    const bankInfoExists = ref(false)\n    const usdtInfoExists = ref(false)\n    \n    // 获取用户绑定的银行卡和USDT信息\n    bind_bank().then(res => {\n        if(res.code === 0) {\n            bankInfo.value = res.data.info || {}\n            \n            // 检查用户是否绑定了银行卡\n            bankInfoExists.value = !!(bankInfo.value.bankname && bankInfo.value.cardnum)\n            \n            // 检查用户是否绑定了USDT钱包\n            usdtInfoExists.value = !!(bankInfo.value.usdt_type && bankInfo.value.usdt_diz)\n            \n            // 如果只有一种提现方式可用，则默认选择该方式\n            if (bankInfoExists.value && !usdtInfoExists.value) {\n                withdrawType.value = 'bank'\n            } else if (!bankInfoExists.value && usdtInfoExists.value) {\n                withdrawType.value = 'usdt'\n            } else if (bankInfoExists.value && usdtInfoExists.value) {\n                // 如果两种方式都可用，默认选择银行卡\n                withdrawType.value = 'bank'\n            }\n        }\n    })\n\n    getdetailbyid(14).then(res => {\n        content.value = res.data?.content\n    })\n\n    const money_check = ref()\n    const money = ref(store.state.userinfo?.balance)\n    const moneys = ref(store.state.baseInfo?.recharge_money_list)\n\n    const clickLeft = () => {\n        push('/self')\n    }\n    \n    const clickRight = () => {\n        push('/tel')\n    }\n    \n    const goToBingBank = () => {\n        push('/bingbank')\n    }\n\n    const onSubmit = (values) => {\n        if (!bankInfoExists.value && !usdtInfoExists.value) {\n            Dialog.confirm({\n                confirmButtonText: t('msg.queren'),\n                cancelButtonText: t('msg.quxiao'),\n                title: '',\n                message: t('msg.tjtkxx'),\n            })\n            .then(() => {\n                push('/bingbank')\n            })\n            .catch(() => {\n                // on cancel\n            });\n            return false\n        }\n        \n        // 验证提现金额\n        if (!money_check.value) {\n            proxy.$Message({ type: 'error', message: t('msg.input_money') });\n            return false;\n        }\n        \n        // 检查选择的提现方式是否有效\n        if (withdrawType.value === 'bank' && !bankInfoExists.value) {\n            proxy.$Message({ type: 'error', message: t('msg.not_put_bank') });\n            return false;\n        }\n        \n        if (withdrawType.value === 'usdt' && !usdtInfoExists.value) {\n            proxy.$Message({ type: 'error', message: t('msg.not_put_usdt') });\n            return false;\n        }\n        \n        let json = {\n            num: money_check.value == 0 ? money.value : money_check.value,\n            type: withdrawType.value, // 使用选择的提现方式\n            paypassword: values.paypassword,\n        }\n        \n        // 如果是USDT提现，添加USDT地址\n        if (withdrawType.value === 'usdt') {\n            json.address = bankInfo.value.usdt_diz;\n            json.USDT_code = bankInfo.value.usdt_type;\n        }\n        \n        do_deposit(json).then(res => {\n            if(res.code === 0) {\n                proxy.$Message({ type: 'success', message: res.info });\n                push('/self')\n            } else {\n                proxy.$Message({ type: 'error', message: res.info });\n            }\n        })\n    };\n\n    return {\n        paypassword,\n        withdrawType,\n        onSubmit,\n        clickLeft,\n        clickRight,\n        info,\n        bankInfo,\n        money,\n        currency,\n        money_check,\n        moneys,\n        content,\n        infoa,\n        tel,\n        bankInfoExists,\n        usdtInfoExists,\n        goToBingBank\n    };\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n@import '@/styles/theme.scss';\n.home{\n    // background-image: url('~@/assets/images/home/<USER>') !important;\n    background-color: #f5f5f5;\n    border-radius: 0;\n}\n.home{\n    :deep(.van-nav-bar){\n        background-color: #fff;\n        // background-color: $theme;\n        color: #000;\n        .van-nav-bar__left{\n            .van-icon{\n                color: #000;\n            }\n        }\n        .van-nav-bar__title{\n            color: #000;\n            \n        }\n        .van-nav-bar__right{\n            img{\n                height: 42px;\n            }\n        }\n    }\n    :deep(.van-form){\n        padding: 40px 0 0;\n\n        .van-cell.van-cell--clickable{\n            border-left: 5px solid $theme;\n            padding: 32px;\n            text-align: left;\n            margin: 20px 0;\n            border-bottom: none;\n            box-shadow: $shadow;\n            .van-cell__right-icon{\n                color: $theme;\n            }\n        }\n        .van-cell-group--inset{\n            padding: 0 30px;\n            background-color: initial;\n        }\n        .van-cell{\n            padding: 23px 10px;\n            border-bottom: 1px solid  var(--van-cell-border-color);\n            &.zdy {\n                margin-bottom: 20px;\n                border-radius: 40px;\n                padding-left: 30px;\n            }\n            .van-field__left-icon{\n                width:90px;\n                text-align: center;\n                .van-icon__image{\n                    height: 42px;\n                    width: auto;\n                }\n                .icon{\n                    height: 42px;\n                    width: auto;\n                    vertical-align:middle;\n                }\n                .van-dropdown-menu{\n                  .van-dropdown-menu__bar{\n                    height: auto;\n                    background: none;\n                    box-shadow: none;\n                  }\n                  .van-cell{\n                    padding: 30px 80px;\n                  }\n                }\n            }\n            .van-field__control{\n                font-size: 24px;\n            }\n            &::after {\n                display: none;\n            }\n        }\n        .van-checkbox{\n            margin: 30px 0 60px 0;\n            .van-checkbox__icon{\n                font-size: 50px;\n                margin-right: 80px;\n                &.van-checkbox__icon--checked .van-icon{\n                    background-color:$theme;\n                    border-color:$theme;\n                }\n            }\n            .van-checkbox__label{\n                font-size: 24px;\n            }\n        }\n        .text_b{\n            margin:70px 60px 40px;\n            font-size: 27px;\n            color: #333;\n            text-align: left;\n            line-height: 1.5;\n            .tex{\n                margin-top: 20px;\n            }\n        }\n        .buttons{\n            padding: 0 76px;\n            .van-button{\n                font-size: 28px;\n                padding: 20px 0;\n                height: auto;\n                background: #000;\n                border: none;\n                color: #fff;\n            }\n            .van-button--plain{\n                margin-top: 40px;\n            }\n        }\n        .tixian_money{\n            text-align: left;\n            font-size: 30px;\n            margin-bottom: 20px;\n            color: #333;\n        }\n        .ktx{\n            width: 100%;\n            height: 190px;\n            border-radius: 20px;\n            padding: 24px 50px;\n            text-align: left;\n            // margin-bottom: 35px;\n            background-color: #fe2c55;\n            text-align: center;\n            .t{\n                font-size: 20px;\n                color: #fff;\n                margin-bottom: 10px;\n                opacity: 0.7;\n            }\n            .b{\n                font-size: 50px;\n                color: #fefefe;\n                margin-bottom: 20px;\n            }\n        }\n        .check_money{\n            display: flex;\n            flex-wrap: wrap;\n            margin-bottom: 40px;\n            background-color: #fff;\n            padding: 24px;\n            border-radius: 20px;\n            color: #333;\n            .text{\n                display: flex;\n                width: 100%;\n                text-align: left;\n                font-size: 28px;\n                margin-bottom: 25px;\n                \n                .withdraw_title {\n                    font-weight: bold;\n                    margin-bottom: 15px;\n                    width: 100%;\n                }\n                \n                span{\n                    flex: 1;\n                    &.tel{\n                        color: #999;\n                    }\n                }\n            }\n        }\n        \n        .account_info {\n            background-color: #fff;\n            padding: 24px;\n            border-radius: 20px;\n            margin-bottom: 20px;\n            \n            .info_item {\n                display: flex;\n                margin-bottom: 10px;\n                font-size: 26px;\n                \n                .label {\n                    color: #666;\n                    margin-right: 10px;\n                }\n                \n                .value {\n                    color: #333;\n                    font-weight: bold;\n                    word-break: break-all;\n                }\n            }\n        }\n        \n        .withdraw_options {\n            margin: 10px 0 20px;\n            \n            :deep(.van-radio-group--horizontal) {\n                display: flex;\n                justify-content: space-around;\n                width: 100%;\n                \n                .van-radio {\n                    margin-right: 0;\n                    font-size: 28px;\n                    font-weight: bold;\n                    padding: 10px 20px;\n                }\n            }\n        }\n        \n        .withdraw_notice {\n            margin: 30px 20px;\n            padding: 20px;\n            background-color: #f9f9f9;\n            border-radius: 10px;\n            \n            h3 {\n                font-size: 28px;\n                color: #333;\n                margin-bottom: 15px;\n                font-weight: bold;\n                text-align: center;\n            }\n            \n            p {\n                font-size: 24px;\n                color: #666;\n                margin-bottom: 10px;\n                line-height: 1.5;\n            }\n        }\n    }\n\n    :deep(.van-){\n        .van-dialog__content{\n            padding: 50px;\n        }\n        .van-dialog__footer{\n            .van-dialog__confirm{\n                color: $theme;\n            }\n        }\n    }\n}\n</style>\n"], "mappings": ";;;;;EACOA,KAAK,EAAC;AAAM;;;EAQJA,KAAK,EAAC;AAAK;;EACPA,KAAK,EAAC;AAAG;;EACTA,KAAK,EAAC;AAAG;;EAEbA,KAAK,EAAC;AAAa;;;EACfA,KAAK,EAAC;;;EACDA,KAAK,EAAC;AAAgB;;;EAE3BA,KAAK,EAAC;;;;EAMNA,KAAK,EAAC;;;;EASVA,KAAK,EAAC;;;EACJA,KAAK,EAAC;AAAW;;EACdA,KAAK,EAAC;AAAO;;EACbA,KAAK,EAAC;AAAO;;EAEhBA,KAAK,EAAC;AAAW;;EACdA,KAAK,EAAC;AAAO;;EACbA,KAAK,EAAC;AAAO;;;EAKlBA,KAAK,EAAC;;;EACJA,KAAK,EAAC;AAAW;;EACdA,KAAK,EAAC;AAAO;;EACbA,KAAK,EAAC;AAAO;;EAEhBA,KAAK,EAAC;AAAW;;EACdA,KAAK,EAAC;AAAO;;EACbA,KAAK,EAAC;AAAO;;;EAIpBA,KAAK,EAAC;;;;EAiBRA,KAAK,EAAC;;;;;EAONA,KAAK,EAAC;;;sBACTC,mBAAA,CAAa,YAAT,MAAI;AAAA;;sBACRA,mBAAA,CAA8B,WAA3B,yBAAuB;AAAA;;sBAC1BA,mBAAA,CAAwB,WAArB,mBAAiB;AAAA;;sBACpBA,mBAAA,CAAiC,WAA9B,4BAA0B;AAAA;;sBAC7BA,mBAAA,CAA0B,WAAvB,qBAAmB;AAAA;mBAJtBC,WAAa,EACbC,WAA8B,EAC9BC,WAAwB,EACxBC,WAAiC,EACjCC,WAA0B,C;;;;;;;;;;uBAnFhCC,mBAAA,CAsFM,OAtFNC,UAsFM,GArFJC,YAAA,CAIcC,sBAAA;IAJAC,KAAK,EAAEC,IAAA,CAAAC,EAAE;IAAgB,YAAU,EAAV,EAAU;IAAEC,WAAU,EAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAAEJ,IAAA,CAAAK,OAAO,CAACC,EAAE;IAAA;IAAOC,YAAW,EAAEC,MAAA,CAAAC;;IAC9EC,KAAK,EAAAC,QAAA,CACZ;MAAA,OAA4E,CAA5EtB,mBAAA,CAA4E;QAAtEuB,GAAG,EAAEC,OAAO;QAAuCzB,KAAK,EAAC,KAAK;QAAC0B,GAAG,EAAC;;;;gDAGjFjB,YAAA,CA+EWkB,mBAAA;IA/EAC,QAAM,EAAER,MAAA,CAAAQ;EAAQ;sBACzB;MAAA,OA+DiB,CA/DjBnB,YAAA,CA+DiBoB,yBAAA;QA/DDC,KAAK,EAAL;MAAK;0BACjB;UAAA,OAGM,CAHN7B,mBAAA,CAGM,OAHN8B,UAGM,GAFF9B,mBAAA,CAA2C,OAA3C+B,UAA2C,EAAAC,gBAAA,CAA1Bb,MAAA,CAAAc,QAAQ,IAAE,GAAC,GAAAD,gBAAA,CAAEb,MAAA,CAAAe,KAAK,kBACnClC,mBAAA,CAA0C,OAA1CmC,UAA0C,EAAAH,gBAAA,CAAzBrB,IAAA,CAAAC,EAAE,gC,GAEvBZ,mBAAA,CAgBM,OAhBNoC,UAgBM,GAfsBjB,MAAA,CAAAkB,cAAc,IAAIlB,MAAA,CAAAmB,cAAc,I,cAAxDhC,mBAAA,CAEM,OAFNiC,UAEM,GADFvC,mBAAA,CAA0E,QAA1EwC,UAA0E,EAAAR,gBAAA,CAA1CrB,IAAA,CAAAC,EAAE,+C,wCAEFO,MAAA,CAAAkB,cAAc,IAAIlB,MAAA,CAAAmB,cAAc,I,cAApEhC,mBAAA,CAKM,OALNmC,UAKM,GAJFjC,YAAA,CAGkBkC,0BAAA;wBAHQvB,MAAA,CAAAwB,YAAY;;qBAAZxB,MAAA,CAAAwB,YAAY,GAAA5B,MAAA;YAAA;YAAE6B,SAAS,EAAC;;8BAChD;cAAA,OAAgF,CAAnDzB,MAAA,CAAAkB,cAAc,I,cAA3CQ,YAAA,CAAgFC,oBAAA;;gBAArEC,IAAI,EAAC;;kCAA6B;kBAAA,OAAuB,C,kCAApBpC,IAAA,CAAAC,EAAE,gC;;;;uDACrBO,MAAA,CAAAmB,cAAc,I,cAA3CO,YAAA,CAAgFC,oBAAA;;gBAArEC,IAAI,EAAC;;kCAA6B;kBAAA,OAAuB,C,kCAApBpC,IAAA,CAAAC,EAAE,gC;;;;;;;qFAG/BO,MAAA,CAAAkB,cAAc,KAAKlB,MAAA,CAAAmB,cAAc,I,cAA1DhC,mBAAA,CAKM,OALN0C,WAKM,GAJFxC,YAAA,CAAyDyC,oBAAA;YAA7CC,WAAW,EAAEvC,IAAA,CAAAC,EAAE;oDAC3BJ,YAAA,CAEa2C,qBAAA;YAFDC,KAAK,EAAL,EAAK;YAACC,KAAK,EAAL,EAAK;YAACC,IAAI,EAAC,SAAS;YAAEC,OAAK,EAAEpC,MAAA,CAAAqC,YAAY;YAAEC,KAAyB,EAAzB;cAAA;YAAA;;8BAC3D;cAAA,OAA+B,C,kCAA5B9C,IAAA,CAAAC,EAAE,wC;;;;mFAKf8C,mBAAA,aAAgB,EACgBvC,MAAA,CAAAwB,YAAY,eAAexB,MAAA,CAAAkB,cAAc,I,cAAzE/B,mBAAA,CASM,OATNqD,WASM,GARJ3D,mBAAA,CAGM,OAHN4D,WAGM,GAFJ5D,mBAAA,CAAqD,QAArD6D,WAAqD,EAAA7B,gBAAA,CAA9BrB,IAAA,CAAAC,EAAE,qBAAoB,GAAC,iBAC9CZ,mBAAA,CAAkD,QAAlD8D,WAAkD,EAAA9B,gBAAA,CAA3Bb,MAAA,CAAA4C,QAAQ,CAACC,QAAQ,iB,GAE1ChE,mBAAA,CAGM,OAHNiE,WAGM,GAFJjE,mBAAA,CAAgD,QAAhDkE,WAAgD,EAAAlC,gBAAA,CAAzBrB,IAAA,CAAAC,EAAE,gBAAe,GAAC,iBACzCZ,mBAAA,CAAiD,QAAjDmE,WAAiD,EAAAnC,gBAAA,CAA1Bb,MAAA,CAAA4C,QAAQ,CAACK,OAAO,iB,0CAI3CV,mBAAA,cAAiB,EACevC,MAAA,CAAAwB,YAAY,eAAexB,MAAA,CAAAmB,cAAc,I,cAAzEhC,mBAAA,CASM,OATN+D,WASM,GARJrE,mBAAA,CAGM,OAHNsE,WAGM,GAFJtE,mBAAA,CAAqD,QAArDuE,WAAqD,EAAAvC,gBAAA,CAA9BrB,IAAA,CAAAC,EAAE,qBAAoB,GAAC,iBAC9CZ,mBAAA,CAAmD,QAAnDwE,WAAmD,EAAAxC,gBAAA,CAA5Bb,MAAA,CAAA4C,QAAQ,CAACU,SAAS,iB,GAE3CzE,mBAAA,CAGM,OAHN0E,WAGM,GAFJ1E,mBAAA,CAAwD,QAAxD2E,WAAwD,EAAA3C,gBAAA,CAAjCrB,IAAA,CAAAC,EAAE,wBAAuB,GAAC,iBACjDZ,mBAAA,CAAkD,QAAlD4E,WAAkD,EAAA5C,gBAAA,CAA3Bb,MAAA,CAAA4C,QAAQ,CAACc,QAAQ,iB,0CAId1D,MAAA,CAAAkB,cAAc,IAAIlB,MAAA,CAAAmB,cAAc,I,cAAhEhC,mBAAA,CAAkG,OAAlGwE,WAAkG,EAAA9C,gBAAA,CAA9BrB,IAAA,CAAAC,EAAE,wC,mCAK9DO,MAAA,CAAAkB,cAAc,IAAIlB,MAAA,CAAAmB,cAAc,I,cAJxCO,YAAA,CAKEkC,oBAAA;;YAJAhF,KAAK,EAAC,KAAK;wBACFoB,MAAA,CAAA6D,WAAW;;qBAAX7D,MAAA,CAAA6D,WAAW,GAAAjE,MAAA;YAAA;YACnBkE,WAAW,EAAEtE,IAAA,CAAAC,EAAE;uGAUVO,MAAA,CAAAkB,cAAc,IAAIlB,MAAA,CAAAmB,cAAc,I,cAPxCO,YAAA,CAQEkC,oBAAA;;YAPAhF,KAAK,EAAC,KAAK;wBACFoB,MAAA,CAAA+D,WAAW;;qBAAX/D,MAAA,CAAA+D,WAAW,GAAAnE,MAAA;YAAA;YACpBuC,IAAI,EAAC,UAAU;YACfP,IAAI,EAAC,aAAa;YACjBkC,WAAW,EAAEtE,IAAA,CAAAC,EAAE;YACfuE,KAAK;cAAAC,QAAA;cAAAC,OAAA,EAA8B1E,IAAA,CAAAC,EAAE;YAAA;;;;UAIfO,MAAA,CAAAkB,cAAc,IAAIlB,MAAA,CAAAmB,cAAc,I,cAA3DhC,mBAAA,CAIM,OAJNgF,WAIM,GAHJ9E,YAAA,CAEa2C,qBAAA;QAFDC,KAAK,EAAL,EAAK;QAACC,KAAK,EAAL,EAAK;QAACC,IAAI,EAAC,SAAS;QAAC,aAAW,EAAC;;0BACjD;UAAA,OAAqB,C,kCAAnB3C,IAAA,CAAAC,EAAE,gC;;;;iDAGmCO,MAAA,CAAAkB,cAAc,IAAIlB,MAAA,CAAAmB,cAAc,I,cAA3EhC,mBAAA,CACM;;QADDP,KAAK,EAAC,QAAQ;QAACwF,SAAgB,EAARpE,MAAA,CAAAqE;iFAEOrE,MAAA,CAAAkB,cAAc,IAAIlB,MAAA,CAAAmB,cAAc,I,cAAnEhC,mBAAA,CAMM,OANNmF,WAMM,EAAAC,WAAA,K"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}