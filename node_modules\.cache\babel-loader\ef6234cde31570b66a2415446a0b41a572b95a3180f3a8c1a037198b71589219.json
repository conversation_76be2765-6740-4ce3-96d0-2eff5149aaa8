{"ast": null, "code": "function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nfunction _iterableToArrayLimit(arr, i) { var _i = null == arr ? null : \"undefined\" != typeof Symbol && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (null != _i) { var _s, _e, _x, _r, _arr = [], _n = !0, _d = !1; try { if (_x = (_i = _i.call(arr)).next, 0 === i) { if (Object(_i) !== _i) return; _n = !1; } else for (; !(_n = (_s = _x.call(_i)).done) && (_arr.push(_s.value), _arr.length !== i); _n = !0); } catch (err) { _d = !0, _e = err; } finally { try { if (!_n && null != _i.return && (_r = _i.return(), Object(_r) !== _r)) return; } finally { if (_d) throw _e; } } return _arr; } }\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\nimport { createVNode as _createVNode } from \"vue\";\nimport { defineComponent } from \"vue\";\nimport { extend, createNamespace, makeRequiredProp } from \"../utils/index.mjs\";\nimport { Tag } from \"../tag/index.mjs\";\nimport { Icon } from \"../icon/index.mjs\";\nimport { Cell } from \"../cell/index.mjs\";\nimport { Radio } from \"../radio/index.mjs\";\nvar _createNamespace = createNamespace(\"address-item\"),\n  _createNamespace2 = _slicedToArray(_createNamespace, 2),\n  name = _createNamespace2[0],\n  bem = _createNamespace2[1];\nvar stdin_default = defineComponent({\n  name: name,\n  props: {\n    address: makeRequiredProp(Object),\n    disabled: Boolean,\n    switchable: Boolean,\n    defaultTagText: String\n  },\n  emits: [\"edit\", \"click\", \"select\"],\n  setup: function setup(props, _ref) {\n    var slots = _ref.slots,\n      emit = _ref.emit;\n    var onClick = function onClick() {\n      if (props.switchable) {\n        emit(\"select\");\n      }\n      emit(\"click\");\n    };\n    var renderRightIcon = function renderRightIcon() {\n      return _createVNode(Icon, {\n        \"name\": \"edit\",\n        \"class\": bem(\"edit\"),\n        \"onClick\": function onClick(event) {\n          event.stopPropagation();\n          emit(\"edit\");\n          emit(\"click\");\n        }\n      }, null);\n    };\n    var renderTag = function renderTag() {\n      if (slots.tag) {\n        return slots.tag(props.address);\n      }\n      if (props.address.isDefault && props.defaultTagText) {\n        return _createVNode(Tag, {\n          \"type\": \"danger\",\n          \"round\": true,\n          \"class\": bem(\"tag\")\n        }, {\n          default: function _default() {\n            return [props.defaultTagText];\n          }\n        });\n      }\n    };\n    var renderContent = function renderContent() {\n      var address = props.address,\n        disabled = props.disabled,\n        switchable = props.switchable;\n      var Info = [_createVNode(\"div\", {\n        \"class\": bem(\"name\")\n      }, [\"\".concat(address.name, \" \").concat(address.tel), renderTag()]), _createVNode(\"div\", {\n        \"class\": bem(\"address\")\n      }, [address.address])];\n      if (switchable && !disabled) {\n        return _createVNode(Radio, {\n          \"name\": address.id,\n          \"iconSize\": 18\n        }, {\n          default: function _default() {\n            return [Info];\n          }\n        });\n      }\n      return Info;\n    };\n    return function () {\n      var _a;\n      var disabled = props.disabled;\n      return _createVNode(\"div\", {\n        \"class\": bem({\n          disabled: disabled\n        }),\n        \"onClick\": onClick\n      }, [_createVNode(Cell, {\n        \"border\": false,\n        \"valueClass\": bem(\"value\")\n      }, {\n        value: renderContent,\n        \"right-icon\": renderRightIcon\n      }), (_a = slots.bottom) == null ? void 0 : _a.call(slots, extend({}, props.address, {\n        disabled: disabled\n      }))]);\n    };\n  }\n});\nexport { stdin_default as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}