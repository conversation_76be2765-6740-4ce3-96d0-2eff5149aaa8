{"ast": null, "code": "function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nfunction _iterableToArrayLimit(arr, i) { var _i = null == arr ? null : \"undefined\" != typeof Symbol && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (null != _i) { var _s, _e, _x, _r, _arr = [], _n = !0, _d = !1; try { if (_x = (_i = _i.call(arr)).next, 0 === i) { if (Object(_i) !== _i) return; _n = !1; } else for (; !(_n = (_s = _x.call(_i)).done) && (_arr.push(_s.value), _arr.length !== i); _n = !0); } catch (err) { _d = !0, _e = err; } finally { try { if (!_n && null != _i.return && (_r = _i.return(), Object(_r) !== _r)) return; } finally { if (_d) throw _e; } } return _arr; } }\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\nimport { Fragment as _Fragment, createVNode as _createVNode } from \"vue\";\nimport { ref, defineComponent } from \"vue\";\nimport { createNamespace, numericProp } from \"../utils/index.mjs\";\nimport { Cell } from \"../cell/index.mjs\";\nimport { Field } from \"../field/index.mjs\";\nvar _createNamespace = createNamespace(\"address-edit-detail\"),\n  _createNamespace2 = _slicedToArray(_createNamespace, 2),\n  name = _createNamespace2[0],\n  bem = _createNamespace2[1];\nvar t = createNamespace(\"address-edit\")[2];\nvar stdin_default = defineComponent({\n  name: name,\n  props: {\n    show: Boolean,\n    rows: numericProp,\n    value: String,\n    rules: Array,\n    focused: Boolean,\n    maxlength: numericProp,\n    searchResult: Array,\n    showSearchResult: Boolean\n  },\n  emits: [\"blur\", \"focus\", \"input\", \"select-search\"],\n  setup: function setup(props, _ref) {\n    var emit = _ref.emit;\n    var field = ref();\n    var showSearchResult = function showSearchResult() {\n      return props.focused && props.searchResult && props.showSearchResult;\n    };\n    var onSelect = function onSelect(express) {\n      emit(\"select-search\", express);\n      emit(\"input\", \"\".concat(express.address || \"\", \" \").concat(express.name || \"\").trim());\n    };\n    var renderSearchTitle = function renderSearchTitle(express) {\n      if (express.name) {\n        var text = express.name.replace(props.value, \"<span class=\".concat(bem(\"keyword\"), \">\").concat(props.value, \"</span>\"));\n        return _createVNode(\"div\", {\n          \"innerHTML\": text\n        }, null);\n      }\n    };\n    var renderSearchResult = function renderSearchResult() {\n      if (!showSearchResult()) {\n        return;\n      }\n      var searchResult = props.searchResult;\n      return searchResult.map(function (express) {\n        return _createVNode(Cell, {\n          \"clickable\": true,\n          \"key\": express.name + express.address,\n          \"icon\": \"location-o\",\n          \"label\": express.address,\n          \"class\": bem(\"search-item\"),\n          \"border\": false,\n          \"onClick\": function onClick() {\n            return onSelect(express);\n          }\n        }, {\n          title: function title() {\n            return renderSearchTitle(express);\n          }\n        });\n      });\n    };\n    var onBlur = function onBlur(event) {\n      return emit(\"blur\", event);\n    };\n    var onFocus = function onFocus(event) {\n      return emit(\"focus\", event);\n    };\n    var onInput = function onInput(value) {\n      return emit(\"input\", value);\n    };\n    return function () {\n      if (props.show) {\n        return _createVNode(_Fragment, null, [_createVNode(Field, {\n          \"autosize\": true,\n          \"clearable\": true,\n          \"ref\": field,\n          \"class\": bem(),\n          \"rows\": props.rows,\n          \"type\": \"textarea\",\n          \"rules\": props.rules,\n          \"label\": t(\"addressDetail\"),\n          \"border\": !showSearchResult(),\n          \"maxlength\": props.maxlength,\n          \"modelValue\": props.value,\n          \"placeholder\": t(\"addressDetail\"),\n          \"onBlur\": onBlur,\n          \"onFocus\": onFocus,\n          \"onUpdate:modelValue\": onInput\n        }, null), renderSearchResult()]);\n      }\n    };\n  }\n});\nexport { stdin_default as default };", "map": {"version": 3, "names": ["Fragment", "_Fragment", "createVNode", "_createVNode", "ref", "defineComponent", "createNamespace", "numericProp", "Cell", "Field", "_createNamespace", "_createNamespace2", "_slicedToArray", "name", "bem", "t", "stdin_default", "props", "show", "Boolean", "rows", "value", "String", "rules", "Array", "focused", "maxlength", "searchResult", "showSearchResult", "emits", "setup", "_ref", "emit", "field", "onSelect", "express", "concat", "address", "trim", "renderSearchTitle", "text", "replace", "renderSearchResult", "map", "onClick", "title", "onBlur", "event", "onFocus", "onInput", "default"], "sources": ["C:/Users/<USER>/Downloads/Telegram Desktop/vue3_3.0/vue3_3.0/node_modules/vant/es/address-edit/AddressEditDetail.mjs"], "sourcesContent": ["import { Fragment as _Fragment, createVNode as _createVNode } from \"vue\";\nimport { ref, defineComponent } from \"vue\";\nimport { createNamespace, numericProp } from \"../utils/index.mjs\";\nimport { Cell } from \"../cell/index.mjs\";\nimport { Field } from \"../field/index.mjs\";\nconst [name, bem] = createNamespace(\"address-edit-detail\");\nconst t = createNamespace(\"address-edit\")[2];\nvar stdin_default = defineComponent({\n  name,\n  props: {\n    show: Boolean,\n    rows: numericProp,\n    value: String,\n    rules: Array,\n    focused: Boolean,\n    maxlength: numericProp,\n    searchResult: Array,\n    showSearchResult: Boolean\n  },\n  emits: [\"blur\", \"focus\", \"input\", \"select-search\"],\n  setup(props, {\n    emit\n  }) {\n    const field = ref();\n    const showSearchResult = () => props.focused && props.searchResult && props.showSearchResult;\n    const onSelect = (express) => {\n      emit(\"select-search\", express);\n      emit(\"input\", `${express.address || \"\"} ${express.name || \"\"}`.trim());\n    };\n    const renderSearchTitle = (express) => {\n      if (express.name) {\n        const text = express.name.replace(props.value, `<span class=${bem(\"keyword\")}>${props.value}</span>`);\n        return _createVNode(\"div\", {\n          \"innerHTML\": text\n        }, null);\n      }\n    };\n    const renderSearchResult = () => {\n      if (!showSearchResult()) {\n        return;\n      }\n      const {\n        searchResult\n      } = props;\n      return searchResult.map((express) => _createVNode(Cell, {\n        \"clickable\": true,\n        \"key\": express.name + express.address,\n        \"icon\": \"location-o\",\n        \"label\": express.address,\n        \"class\": bem(\"search-item\"),\n        \"border\": false,\n        \"onClick\": () => onSelect(express)\n      }, {\n        title: () => renderSearchTitle(express)\n      }));\n    };\n    const onBlur = (event) => emit(\"blur\", event);\n    const onFocus = (event) => emit(\"focus\", event);\n    const onInput = (value) => emit(\"input\", value);\n    return () => {\n      if (props.show) {\n        return _createVNode(_Fragment, null, [_createVNode(Field, {\n          \"autosize\": true,\n          \"clearable\": true,\n          \"ref\": field,\n          \"class\": bem(),\n          \"rows\": props.rows,\n          \"type\": \"textarea\",\n          \"rules\": props.rules,\n          \"label\": t(\"addressDetail\"),\n          \"border\": !showSearchResult(),\n          \"maxlength\": props.maxlength,\n          \"modelValue\": props.value,\n          \"placeholder\": t(\"addressDetail\"),\n          \"onBlur\": onBlur,\n          \"onFocus\": onFocus,\n          \"onUpdate:modelValue\": onInput\n        }, null), renderSearchResult()]);\n      }\n    };\n  }\n});\nexport {\n  stdin_default as default\n};\n"], "mappings": ";;;;;;AAAA,SAASA,QAAQ,IAAIC,SAAS,EAAEC,WAAW,IAAIC,YAAY,QAAQ,KAAK;AACxE,SAASC,GAAG,EAAEC,eAAe,QAAQ,KAAK;AAC1C,SAASC,eAAe,EAAEC,WAAW,QAAQ,oBAAoB;AACjE,SAASC,IAAI,QAAQ,mBAAmB;AACxC,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,IAAAC,gBAAA,GAAoBJ,eAAe,CAAC,qBAAqB,CAAC;EAAAK,iBAAA,GAAAC,cAAA,CAAAF,gBAAA;EAAnDG,IAAI,GAAAF,iBAAA;EAAEG,GAAG,GAAAH,iBAAA;AAChB,IAAMI,CAAC,GAAGT,eAAe,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;AAC5C,IAAIU,aAAa,GAAGX,eAAe,CAAC;EAClCQ,IAAI,EAAJA,IAAI;EACJI,KAAK,EAAE;IACLC,IAAI,EAAEC,OAAO;IACbC,IAAI,EAAEb,WAAW;IACjBc,KAAK,EAAEC,MAAM;IACbC,KAAK,EAAEC,KAAK;IACZC,OAAO,EAAEN,OAAO;IAChBO,SAAS,EAAEnB,WAAW;IACtBoB,YAAY,EAAEH,KAAK;IACnBI,gBAAgB,EAAET;EACpB,CAAC;EACDU,KAAK,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,eAAe,CAAC;EAClDC,KAAK,WAAAA,MAACb,KAAK,EAAAc,IAAA,EAER;IAAA,IADDC,IAAI,GAAAD,IAAA,CAAJC,IAAI;IAEJ,IAAMC,KAAK,GAAG7B,GAAG,CAAC,CAAC;IACnB,IAAMwB,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAA;MAAA,OAASX,KAAK,CAACQ,OAAO,IAAIR,KAAK,CAACU,YAAY,IAAIV,KAAK,CAACW,gBAAgB;IAAA;IAC5F,IAAMM,QAAQ,GAAG,SAAXA,QAAQA,CAAIC,OAAO,EAAK;MAC5BH,IAAI,CAAC,eAAe,EAAEG,OAAO,CAAC;MAC9BH,IAAI,CAAC,OAAO,EAAE,GAAAI,MAAA,CAAGD,OAAO,CAACE,OAAO,IAAI,EAAE,OAAAD,MAAA,CAAID,OAAO,CAACtB,IAAI,IAAI,EAAE,EAAGyB,IAAI,CAAC,CAAC,CAAC;IACxE,CAAC;IACD,IAAMC,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAIJ,OAAO,EAAK;MACrC,IAAIA,OAAO,CAACtB,IAAI,EAAE;QAChB,IAAM2B,IAAI,GAAGL,OAAO,CAACtB,IAAI,CAAC4B,OAAO,CAACxB,KAAK,CAACI,KAAK,iBAAAe,MAAA,CAAiBtB,GAAG,CAAC,SAAS,CAAC,OAAAsB,MAAA,CAAInB,KAAK,CAACI,KAAK,YAAS,CAAC;QACrG,OAAOlB,YAAY,CAAC,KAAK,EAAE;UACzB,WAAW,EAAEqC;QACf,CAAC,EAAE,IAAI,CAAC;MACV;IACF,CAAC;IACD,IAAME,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAA,EAAS;MAC/B,IAAI,CAACd,gBAAgB,CAAC,CAAC,EAAE;QACvB;MACF;MACA,IACED,YAAY,GACVV,KAAK,CADPU,YAAY;MAEd,OAAOA,YAAY,CAACgB,GAAG,CAAC,UAACR,OAAO;QAAA,OAAKhC,YAAY,CAACK,IAAI,EAAE;UACtD,WAAW,EAAE,IAAI;UACjB,KAAK,EAAE2B,OAAO,CAACtB,IAAI,GAAGsB,OAAO,CAACE,OAAO;UACrC,MAAM,EAAE,YAAY;UACpB,OAAO,EAAEF,OAAO,CAACE,OAAO;UACxB,OAAO,EAAEvB,GAAG,CAAC,aAAa,CAAC;UAC3B,QAAQ,EAAE,KAAK;UACf,SAAS,EAAE,SAAA8B,QAAA;YAAA,OAAMV,QAAQ,CAACC,OAAO,CAAC;UAAA;QACpC,CAAC,EAAE;UACDU,KAAK,EAAE,SAAAA,MAAA;YAAA,OAAMN,iBAAiB,CAACJ,OAAO,CAAC;UAAA;QACzC,CAAC,CAAC;MAAA,EAAC;IACL,CAAC;IACD,IAAMW,MAAM,GAAG,SAATA,MAAMA,CAAIC,KAAK;MAAA,OAAKf,IAAI,CAAC,MAAM,EAAEe,KAAK,CAAC;IAAA;IAC7C,IAAMC,OAAO,GAAG,SAAVA,OAAOA,CAAID,KAAK;MAAA,OAAKf,IAAI,CAAC,OAAO,EAAEe,KAAK,CAAC;IAAA;IAC/C,IAAME,OAAO,GAAG,SAAVA,OAAOA,CAAI5B,KAAK;MAAA,OAAKW,IAAI,CAAC,OAAO,EAAEX,KAAK,CAAC;IAAA;IAC/C,OAAO,YAAM;MACX,IAAIJ,KAAK,CAACC,IAAI,EAAE;QACd,OAAOf,YAAY,CAACF,SAAS,EAAE,IAAI,EAAE,CAACE,YAAY,CAACM,KAAK,EAAE;UACxD,UAAU,EAAE,IAAI;UAChB,WAAW,EAAE,IAAI;UACjB,KAAK,EAAEwB,KAAK;UACZ,OAAO,EAAEnB,GAAG,CAAC,CAAC;UACd,MAAM,EAAEG,KAAK,CAACG,IAAI;UAClB,MAAM,EAAE,UAAU;UAClB,OAAO,EAAEH,KAAK,CAACM,KAAK;UACpB,OAAO,EAAER,CAAC,CAAC,eAAe,CAAC;UAC3B,QAAQ,EAAE,CAACa,gBAAgB,CAAC,CAAC;UAC7B,WAAW,EAAEX,KAAK,CAACS,SAAS;UAC5B,YAAY,EAAET,KAAK,CAACI,KAAK;UACzB,aAAa,EAAEN,CAAC,CAAC,eAAe,CAAC;UACjC,QAAQ,EAAE+B,MAAM;UAChB,SAAS,EAAEE,OAAO;UAClB,qBAAqB,EAAEC;QACzB,CAAC,EAAE,IAAI,CAAC,EAAEP,kBAAkB,CAAC,CAAC,CAAC,CAAC;MAClC;IACF,CAAC;EACH;AACF,CAAC,CAAC;AACF,SACE1B,aAAa,IAAIkC,OAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}