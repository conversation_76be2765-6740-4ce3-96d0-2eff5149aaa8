{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, pushScopeId as _pushScopeId, popScopeId as _popScopeId } from \"vue\";\nvar _withScopeId = function _withScopeId(n) {\n  return _pushScopeId(\"data-v-74af8ea3\"), n = n(), _popScopeId(), n;\n};\nvar _hoisted_1 = {\n  class: \"homes\"\n};\nvar _hoisted_2 = {\n  class: \"buttons\"\n};\nvar _hoisted_3 = {\n  class: \"gore\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_login_top = _resolveComponent(\"login-top\");\n  var _component_van_field = _resolveComponent(\"van-field\");\n  var _component_van_cell_group = _resolveComponent(\"van-cell-group\");\n  var _component_van_button = _resolveComponent(\"van-button\");\n  var _component_van_form = _resolveComponent(\"van-form\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_login_top, {\n    \"hide-lang\": \"\",\n    title: _ctx.$t('msg.register'),\n    \"left-arrow\": \"\"\n  }, null, 8 /* PROPS */, [\"title\"]), _createVNode(_component_van_form, {\n    onSubmit: $setup.onSubmit,\n    style: {\n      \"z-index\": \"11\"\n    }\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_van_cell_group, {\n        inset: \"\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_van_field, {\n            \"label-width\": \"100\",\n            class: \"zdy\",\n            modelValue: $setup.userName,\n            \"onUpdate:modelValue\": _cache[0] || (_cache[0] = function ($event) {\n              return $setup.userName = $event;\n            }),\n            name: \"userName\",\n            label: _ctx.$t('msg.username'),\n            placeholder: _ctx.$t('msg.input_username'),\n            rules: [{\n              required: true,\n              message: _ctx.$t('msg.input_username')\n            }]\n          }, null, 8 /* PROPS */, [\"modelValue\", \"label\", \"placeholder\", \"rules\"]), _createVNode(_component_van_field, {\n            \"label-width\": \"100\",\n            modelValue: $setup.invite_code,\n            \"onUpdate:modelValue\": _cache[1] || (_cache[1] = function ($event) {\n              return $setup.invite_code = $event;\n            }),\n            name: \"invite_code\",\n            label: _ctx.$t('msg.code'),\n            placeholder: _ctx.$t('msg.code'),\n            rules: [{\n              required: true,\n              message: _ctx.$t('msg.input_code')\n            }]\n          }, null, 8 /* PROPS */, [\"modelValue\", \"label\", \"placeholder\", \"rules\"]), _createVNode(_component_van_field, {\n            \"label-width\": \"100\",\n            modelValue: $setup.pwd,\n            \"onUpdate:modelValue\": _cache[2] || (_cache[2] = function ($event) {\n              return $setup.pwd = $event;\n            }),\n            type: \"password\",\n            name: \"pwd\",\n            label: _ctx.$t('msg.pwd'),\n            placeholder: _ctx.$t('msg.pwd'),\n            rules: [{\n              required: true,\n              message: _ctx.$t('msg.input_pwd')\n            }]\n          }, null, 8 /* PROPS */, [\"modelValue\", \"label\", \"placeholder\", \"rules\"]), _createVNode(_component_van_field, {\n            \"label-width\": \"100\",\n            modelValue: $setup.pwd2,\n            \"onUpdate:modelValue\": _cache[3] || (_cache[3] = function ($event) {\n              return $setup.pwd2 = $event;\n            }),\n            type: \"password\",\n            name: \"pwd2\",\n            label: _ctx.$t('msg.true_pwd'),\n            placeholder: _ctx.$t('msg.true_pwd'),\n            rules: [{\n              required: true,\n              message: _ctx.$t('msg.input_true_pwd')\n            }]\n          }, null, 8 /* PROPS */, [\"modelValue\", \"label\", \"placeholder\", \"rules\"]), _createVNode(_component_van_field, {\n            \"label-width\": \"100\",\n            modelValue: $setup.depositPwd,\n            \"onUpdate:modelValue\": _cache[4] || (_cache[4] = function ($event) {\n              return $setup.depositPwd = $event;\n            }),\n            name: \"depositPwd\",\n            label: _ctx.$t('msg.tx_pwd'),\n            placeholder: _ctx.$t('msg.tx_pwd'),\n            rules: [{\n              required: true,\n              message: _ctx.$t('msg.input_t_pwd')\n            }]\n          }, null, 8 /* PROPS */, [\"modelValue\", \"label\", \"placeholder\", \"rules\"])];\n        }),\n        _: 1 /* STABLE */\n      }), _createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_van_button, {\n        round: \"5\",\n        block: \"\",\n        plain: \"\",\n        type: \"primary\",\n        \"native-type\": \"submit\",\n        style: {\n          \"border-radius\": \"10px\"\n        }\n      }, {\n        default: _withCtx(function () {\n          return [_createTextVNode(_toDisplayString(_ctx.$t('msg.register1')), 1 /* TEXT */)];\n        }),\n\n        _: 1 /* STABLE */\n      }), _createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"span\", {\n        class: \"gores\",\n        onClick: _cache[5] || (_cache[5] = function ($event) {\n          return _ctx.$router.push({\n            path: '/login'\n          });\n        })\n      }, _toDisplayString(_ctx.$t('msg.login')), 1 /* TEXT */)])])];\n    }),\n\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"onSubmit\"])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_login_top", "title", "_ctx", "$t", "_component_van_form", "onSubmit", "$setup", "style", "_component_van_cell_group", "inset", "_component_van_field", "userName", "$event", "name", "label", "placeholder", "rules", "required", "message", "invite_code", "pwd", "type", "pwd2", "depositPwd", "_createElementVNode", "_hoisted_2", "_component_van_button", "round", "block", "plain", "_hoisted_3", "onClick", "_cache", "$router", "push", "path"], "sources": ["C:\\Users\\<USER>\\Downloads\\Telegram Desktop\\vue3_3.0\\vue3_3.0\\src\\views\\login\\register.vue"], "sourcesContent": ["<template>\r\n  <div class=\"homes\">\r\n    <login-top  hide-lang :title=\"$t('msg.register')\" left-arrow></login-top>\r\n    <van-form @submit=\"onSubmit\" style=\"z-index: 11;\">\r\n\r\n      <van-cell-group inset >\r\n        <van-field label-width=\"100\" class=\"zdy\" v-model=\"userName\" name=\"userName\" :label=\"$t('msg.username')\"\r\n          :placeholder=\"$t('msg.input_username')\" :rules=\"[{ required: true, message: $t('msg.input_username') }]\" />\r\n        <van-field label-width=\"100\" v-model=\"invite_code\" name=\"invite_code\" :label=\"$t('msg.code')\"\r\n          :placeholder=\"$t('msg.code')\" :rules=\"[{ required: true, message: $t('msg.input_code') }]\" />\r\n        <van-field label-width=\"100\" v-model=\"pwd\" type=\"password\" name=\"pwd\" :label=\"$t('msg.pwd')\"\r\n          :placeholder=\"$t('msg.pwd')\" :rules=\"[{ required: true, message: $t('msg.input_pwd') }]\" />\r\n        <van-field label-width=\"100\" v-model=\"pwd2\" type=\"password\" name=\"pwd2\" :label=\"$t('msg.true_pwd')\"\r\n          :placeholder=\"$t('msg.true_pwd')\" :rules=\"[{ required: true, message: $t('msg.input_true_pwd') }]\" />\r\n        <van-field label-width=\"100\" v-model=\"depositPwd\" name=\"depositPwd\" :label=\"$t('msg.tx_pwd')\"\r\n          :placeholder=\"$t('msg.tx_pwd')\" :rules=\"[{ required: true, message: $t('msg.input_t_pwd') }]\" />\r\n      </van-cell-group>\r\n      <div class=\"buttons\">\r\n        <van-button round=\"5\" block plain type=\"primary\" native-type=\"submit\" style=\"border-radius: 10px;\">\r\n          {{ $t('msg.register1') }}\r\n        </van-button>\r\n        <div class=\"gore\">\r\n          <span class=\"gores\" @click=\"$router.push({ path: '/login' })\"> {{ $t('msg.login') }} </span>\r\n        </div>\r\n\r\n      </div>\r\n    </van-form>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport loginTop from './index.vue'\r\nimport { ref, getCurrentInstance } from 'vue';\r\nimport store from '@/store/index'\r\nimport { useRouter, useRoute } from 'vue-router';\r\nimport { do_register, login } from '@/api/login/index'\r\nimport { Toast } from 'vant';\r\nexport default {\r\n  name: 'HomeView',\r\n  components: { loginTop },\r\n  setup() {\r\n\r\n    const route = useRoute()\r\n    const { push } = useRouter();\r\n    const { proxy } = getCurrentInstance()\r\n    const baseInfo = ref(store.state.baseInfo)\r\n\r\n    const invite_code = ref('');\r\n    if (route.query?.invite_code) {\r\n      invite_code.value = route.query?.invite_code\r\n    }\r\n    const userName = ref('');\r\n    const pwd = ref('');\r\n    const pwd2 = ref('');\r\n    const depositPwd = ref('');\r\n\r\n    const onSubmit = (values) => {\r\n      if (values.pwd != values.pwd2) {\r\n        Toast.fail('两次输入的密码不正确')\r\n        return false\r\n      }\r\n      const json = JSON.parse(JSON.stringify(values))\r\n      delete json.pwd2\r\n      \r\n      do_register(json).then(res => {\r\n        if (res.code === 0) {\r\n          proxy.$Message({ type: 'success', message: res.info });\r\n          let info = {\r\n            userName: userName.value,\r\n            pwd: pwd.value\r\n          }\r\n          login(info).then(red => {\r\n            if (red.code === 0) {\r\n              store.dispatch('changetoken', red.token)\r\n              store.dispatch('changeuserinfo', red.userinfo || {})\r\n              proxy.$Message({ type: 'success', message: red.info });\r\n              // 记住密码\r\n              const useri = { ...json, ...{ checked: true } }\r\n              store.dispatch('changeUser', useri)\r\n              push('/')\r\n            } else {\r\n              proxy.$Message({ type: 'error', message: red.info });\r\n            }\r\n\r\n          })\r\n        } else {\r\n          proxy.$Message({ type: 'error', message: res.info });\r\n        }\r\n      })\r\n    };\r\n\r\n    return {\r\n      userName,\r\n      pwd,\r\n      pwd2,\r\n      depositPwd,\r\n      invite_code,\r\n      onSubmit\r\n    };\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n@import '@/styles/theme.scss';\r\n\r\n:deep .van-form {\r\n  background-color: #fff;\r\n  width: 94%;\r\n  margin: 0 auto;\r\n  color: black;\r\n  border-radius: 20px;\r\n  padding: 10px 0 0;\r\n  margin-top: 1rem;\r\n}\r\n\r\n.gore {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  color: black;\r\n  margin-top: 10px;\r\n}\r\n\r\n.gores {\r\n  color: #009995 !important;\r\n}\r\n\r\n:deep .van-button--plain {\r\n  background-color: #009995 !important;\r\n  color: #fff !important;\r\n  border-radius: 25 px !important;\r\n  border: none !important;\r\n}\r\n\r\n:deep .van-cell {\r\n  border: 1px solid #ccc;\r\n  border-radius: 12px;\r\n  margin-top: 20px;\r\n  padding: 15px 35px !important;\r\n}\r\n\r\n.homes {\r\n  height: 100vh;\r\n  overflow: auto;\r\n  background-image: url('~@/assets/images/bj.png');\r\n  background-size: 100% 100%;\r\n\r\n  :deep(.van-form) {\r\n    position: relative;\r\n    padding-bottom: 40px;\r\n    z-index: 9;\r\n    .van-cell-group--inset {\r\n      padding: 0 50px;\r\n      background-color: initial;\r\n    }\r\n\r\n    .van-ellipsis {\r\n      color: #fff;\r\n    }\r\n\r\n    .van-cell {\r\n      padding: 34px 10px;\r\n      border-bottom: 1px solid var(--van-cell-border-color);\r\n      background-color: initial;\r\n\r\n      &.zdy {\r\n        .van-field__left-icon {\r\n          margin-right: 30px;\r\n        }\r\n      }\r\n\r\n      .van-field__left-icon {\r\n        margin-right: 90px;\r\n\r\n        .van-icon__image {\r\n          height: 42px;\r\n          width: auto;\r\n        }\r\n\r\n        .icon {\r\n          height: 42px;\r\n          width: auto;\r\n          vertical-align: middle;\r\n        }\r\n\r\n        display: flex;\r\n\r\n        .van-dropdown-menu {\r\n          .van-dropdown-menu__bar {\r\n            height: auto;\r\n            background: none;\r\n            box-shadow: none;\r\n          }\r\n\r\n          .van-cell {\r\n            padding: 30px 80px;\r\n          }\r\n        }\r\n      }\r\n\r\n      .van-field__control {\r\n        font-size: 24px;\r\n      }\r\n\r\n      .van-field__label {\r\n        color: #fff;\r\n        display: none;\r\n      }\r\n\r\n      zdy .van-field__label {\r\n        color: #fff;\r\n        display: flex;\r\n      }\r\n\r\n      &::after {\r\n        display: none;\r\n      }\r\n    }\r\n\r\n    .van-checkbox {\r\n      margin: 30px 0 60px 0;\r\n\r\n      .van-checkbox__icon {\r\n        font-size: 50px;\r\n        margin-right: 80px;\r\n\r\n        &.van-checkbox__icon--checked .van-icon {\r\n          background-color: $theme;\r\n          border-color: $theme;\r\n        }\r\n      }\r\n\r\n      .van-checkbox__label {\r\n        font-size: 24px;\r\n      }\r\n    }\r\n\r\n    .buttons {\r\n      padding: 0 50px;\r\n      border-radius: 3px;\r\n\r\n      .van-button {\r\n        font-size: 26px;\r\n        padding: 26px 0;\r\n        height: auto;\r\n        margin-top: 40px;\r\n\r\n        &+.van-button {\r\n          background-color: rgba(255, 255, 255, 0.2);\r\n          border: none;\r\n          color: #fff;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;EACOA,KAAK,EAAC;AAAO;;EAgBTA,KAAK,EAAC;AAAS;;EAIbA,KAAK,EAAC;AAAM;;;;;;;uBApBvBC,mBAAA,CA0BM,OA1BNC,UA0BM,GAzBJC,YAAA,CAAyEC,oBAAA;IAA7D,WAAS,EAAT,EAAS;IAAEC,KAAK,EAAEC,IAAA,CAAAC,EAAE;IAAkB,YAAU,EAAV;sCAClDJ,YAAA,CAuBWK,mBAAA;IAvBAC,QAAM,EAAEC,MAAA,CAAAD,QAAQ;IAAEE,KAAoB,EAApB;MAAA;IAAA;;sBAE3B;MAAA,OAWiB,CAXjBR,YAAA,CAWiBS,yBAAA;QAXDC,KAAK,EAAL;MAAK;0BACnB;UAAA,OAC6G,CAD7GV,YAAA,CAC6GW,oBAAA;YADlG,aAAW,EAAC,KAAK;YAACd,KAAK,EAAC,KAAK;wBAAUU,MAAA,CAAAK,QAAQ;;qBAARL,MAAA,CAAAK,QAAQ,GAAAC,MAAA;YAAA;YAAEC,IAAI,EAAC,UAAU;YAAEC,KAAK,EAAEZ,IAAA,CAAAC,EAAE;YACnFY,WAAW,EAAEb,IAAA,CAAAC,EAAE;YAAyBa,KAAK;cAAAC,QAAA;cAAAC,OAAA,EAA8BhB,IAAA,CAAAC,EAAE;YAAA;oFAChFJ,YAAA,CAC+FW,oBAAA;YADpF,aAAW,EAAC,KAAK;wBAAUJ,MAAA,CAAAa,WAAW;;qBAAXb,MAAA,CAAAa,WAAW,GAAAP,MAAA;YAAA;YAAEC,IAAI,EAAC,aAAa;YAAEC,KAAK,EAAEZ,IAAA,CAAAC,EAAE;YAC7EY,WAAW,EAAEb,IAAA,CAAAC,EAAE;YAAea,KAAK;cAAAC,QAAA;cAAAC,OAAA,EAA8BhB,IAAA,CAAAC,EAAE;YAAA;oFACtEJ,YAAA,CAC6FW,oBAAA;YADlF,aAAW,EAAC,KAAK;wBAAUJ,MAAA,CAAAc,GAAG;;qBAAHd,MAAA,CAAAc,GAAG,GAAAR,MAAA;YAAA;YAAES,IAAI,EAAC,UAAU;YAACR,IAAI,EAAC,KAAK;YAAEC,KAAK,EAAEZ,IAAA,CAAAC,EAAE;YAC7EY,WAAW,EAAEb,IAAA,CAAAC,EAAE;YAAca,KAAK;cAAAC,QAAA;cAAAC,OAAA,EAA8BhB,IAAA,CAAAC,EAAE;YAAA;oFACrEJ,YAAA,CACuGW,oBAAA;YAD5F,aAAW,EAAC,KAAK;wBAAUJ,MAAA,CAAAgB,IAAI;;qBAAJhB,MAAA,CAAAgB,IAAI,GAAAV,MAAA;YAAA;YAAES,IAAI,EAAC,UAAU;YAACR,IAAI,EAAC,MAAM;YAAEC,KAAK,EAAEZ,IAAA,CAAAC,EAAE;YAC/EY,WAAW,EAAEb,IAAA,CAAAC,EAAE;YAAmBa,KAAK;cAAAC,QAAA;cAAAC,OAAA,EAA8BhB,IAAA,CAAAC,EAAE;YAAA;oFAC1EJ,YAAA,CACkGW,oBAAA;YADvF,aAAW,EAAC,KAAK;wBAAUJ,MAAA,CAAAiB,UAAU;;qBAAVjB,MAAA,CAAAiB,UAAU,GAAAX,MAAA;YAAA;YAAEC,IAAI,EAAC,YAAY;YAAEC,KAAK,EAAEZ,IAAA,CAAAC,EAAE;YAC3EY,WAAW,EAAEb,IAAA,CAAAC,EAAE;YAAiBa,KAAK;cAAAC,QAAA;cAAAC,OAAA,EAA8BhB,IAAA,CAAAC,EAAE;YAAA;;;;UAE1EqB,mBAAA,CAQM,OARNC,UAQM,GAPJ1B,YAAA,CAEa2B,qBAAA;QAFDC,KAAK,EAAC,GAAG;QAACC,KAAK,EAAL,EAAK;QAACC,KAAK,EAAL,EAAK;QAACR,IAAI,EAAC,SAAS;QAAC,aAAW,EAAC,QAAQ;QAACd,KAA4B,EAA5B;UAAA;QAAA;;0BACpE;UAAA,OAAyB,C,kCAAtBL,IAAA,CAAAC,EAAE,kC;;;;UAEPqB,mBAAA,CAEM,OAFNM,UAEM,GADJN,mBAAA,CAA4F;QAAtF5B,KAAK,EAAC,OAAO;QAAEmC,OAAK,EAAAC,MAAA,QAAAA,MAAA,gBAAApB,MAAA;UAAA,OAAEV,IAAA,CAAA+B,OAAO,CAACC,IAAI;YAAAC,IAAA;UAAA;QAAA;0BAA0BjC,IAAA,CAAAC,EAAE,8B"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}