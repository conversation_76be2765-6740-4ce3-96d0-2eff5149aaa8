{"ast": null, "code": "function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nfunction _iterableToArrayLimit(arr, i) { var _i = null == arr ? null : \"undefined\" != typeof Symbol && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (null != _i) { var _s, _e, _x, _r, _arr = [], _n = !0, _d = !1; try { if (_x = (_i = _i.call(arr)).next, 0 === i) { if (Object(_i) !== _i) return; _n = !1; } else for (; !(_n = (_s = _x.call(_i)).done) && (_arr.push(_s.value), _arr.length !== i); _n = !0); } catch (err) { _d = !0, _e = err; } finally { try { if (!_n && null != _i.return && (_r = _i.return(), Object(_r) !== _r)) return; } finally { if (_d) throw _e; } } return _arr; } }\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\nimport { createVNode as _createVNode } from \"vue\";\nimport { ref, watch, reactive, computed, onMounted, onActivated, onDeactivated, onBeforeUnmount, defineComponent, nextTick } from \"vue\";\nimport { clamp, isHidden, truthProp, numericProp, windowWidth, windowHeight, preventDefault, createNamespace, makeNumericProp } from \"../utils/index.mjs\";\nimport { doubleRaf, useChildren, useEventListener, usePageVisibility } from \"@vant/use\";\nimport { useTouch } from \"../composables/use-touch.mjs\";\nimport { useExpose } from \"../composables/use-expose.mjs\";\nimport { onPopupReopen } from \"../composables/on-popup-reopen.mjs\";\nvar _createNamespace = createNamespace(\"swipe\"),\n  _createNamespace2 = _slicedToArray(_createNamespace, 2),\n  name = _createNamespace2[0],\n  bem = _createNamespace2[1];\nvar swipeProps = {\n  loop: truthProp,\n  width: numericProp,\n  height: numericProp,\n  vertical: Boolean,\n  autoplay: makeNumericProp(0),\n  duration: makeNumericProp(500),\n  touchable: truthProp,\n  lazyRender: Boolean,\n  initialSwipe: makeNumericProp(0),\n  indicatorColor: String,\n  showIndicators: truthProp,\n  stopPropagation: truthProp\n};\nvar SWIPE_KEY = Symbol(name);\nvar stdin_default = defineComponent({\n  name: name,\n  props: swipeProps,\n  emits: [\"change\"],\n  setup: function setup(props, _ref) {\n    var emit = _ref.emit,\n      slots = _ref.slots;\n    var root = ref();\n    var track = ref();\n    var state = reactive({\n      rect: null,\n      width: 0,\n      height: 0,\n      offset: 0,\n      active: 0,\n      swiping: false\n    });\n    var touch = useTouch();\n    var _useChildren = useChildren(SWIPE_KEY),\n      children = _useChildren.children,\n      linkChildren = _useChildren.linkChildren;\n    var count = computed(function () {\n      return children.length;\n    });\n    var size = computed(function () {\n      return state[props.vertical ? \"height\" : \"width\"];\n    });\n    var delta = computed(function () {\n      return props.vertical ? touch.deltaY.value : touch.deltaX.value;\n    });\n    var minOffset = computed(function () {\n      if (state.rect) {\n        var base = props.vertical ? state.rect.height : state.rect.width;\n        return base - size.value * count.value;\n      }\n      return 0;\n    });\n    var maxCount = computed(function () {\n      return Math.ceil(Math.abs(minOffset.value) / size.value);\n    });\n    var trackSize = computed(function () {\n      return count.value * size.value;\n    });\n    var activeIndicator = computed(function () {\n      return (state.active + count.value) % count.value;\n    });\n    var isCorrectDirection = computed(function () {\n      var expect = props.vertical ? \"vertical\" : \"horizontal\";\n      return touch.direction.value === expect;\n    });\n    var trackStyle = computed(function () {\n      var style = {\n        transitionDuration: \"\".concat(state.swiping ? 0 : props.duration, \"ms\"),\n        transform: \"translate\".concat(props.vertical ? \"Y\" : \"X\", \"(\").concat(state.offset, \"px)\")\n      };\n      if (size.value) {\n        var mainAxis = props.vertical ? \"height\" : \"width\";\n        var crossAxis = props.vertical ? \"width\" : \"height\";\n        style[mainAxis] = \"\".concat(trackSize.value, \"px\");\n        style[crossAxis] = props[crossAxis] ? \"\".concat(props[crossAxis], \"px\") : \"\";\n      }\n      return style;\n    });\n    var getTargetActive = function getTargetActive(pace) {\n      var active = state.active;\n      if (pace) {\n        if (props.loop) {\n          return clamp(active + pace, -1, count.value);\n        }\n        return clamp(active + pace, 0, maxCount.value);\n      }\n      return active;\n    };\n    var getTargetOffset = function getTargetOffset(targetActive) {\n      var offset = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n      var currentPosition = targetActive * size.value;\n      if (!props.loop) {\n        currentPosition = Math.min(currentPosition, -minOffset.value);\n      }\n      var targetOffset = offset - currentPosition;\n      if (!props.loop) {\n        targetOffset = clamp(targetOffset, minOffset.value, 0);\n      }\n      return targetOffset;\n    };\n    var move = function move(_ref2) {\n      var _ref2$pace = _ref2.pace,\n        pace = _ref2$pace === void 0 ? 0 : _ref2$pace,\n        _ref2$offset = _ref2.offset,\n        offset = _ref2$offset === void 0 ? 0 : _ref2$offset,\n        emitChange = _ref2.emitChange;\n      if (count.value <= 1) {\n        return;\n      }\n      var active = state.active;\n      var targetActive = getTargetActive(pace);\n      var targetOffset = getTargetOffset(targetActive, offset);\n      if (props.loop) {\n        if (children[0] && targetOffset !== minOffset.value) {\n          var outRightBound = targetOffset < minOffset.value;\n          children[0].setOffset(outRightBound ? trackSize.value : 0);\n        }\n        if (children[count.value - 1] && targetOffset !== 0) {\n          var outLeftBound = targetOffset > 0;\n          children[count.value - 1].setOffset(outLeftBound ? -trackSize.value : 0);\n        }\n      }\n      state.active = targetActive;\n      state.offset = targetOffset;\n      if (emitChange && targetActive !== active) {\n        emit(\"change\", activeIndicator.value);\n      }\n    };\n    var correctPosition = function correctPosition() {\n      state.swiping = true;\n      if (state.active <= -1) {\n        move({\n          pace: count.value\n        });\n      } else if (state.active >= count.value) {\n        move({\n          pace: -count.value\n        });\n      }\n    };\n    var prev = function prev() {\n      correctPosition();\n      touch.reset();\n      doubleRaf(function () {\n        state.swiping = false;\n        move({\n          pace: -1,\n          emitChange: true\n        });\n      });\n    };\n    var next = function next() {\n      correctPosition();\n      touch.reset();\n      doubleRaf(function () {\n        state.swiping = false;\n        move({\n          pace: 1,\n          emitChange: true\n        });\n      });\n    };\n    var autoplayTimer;\n    var stopAutoplay = function stopAutoplay() {\n      return clearTimeout(autoplayTimer);\n    };\n    var autoplay = function autoplay() {\n      stopAutoplay();\n      if (props.autoplay > 0 && count.value > 1) {\n        autoplayTimer = setTimeout(function () {\n          next();\n          autoplay();\n        }, +props.autoplay);\n      }\n    };\n    var initialize = function initialize() {\n      var active = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : +props.initialSwipe;\n      if (!root.value) {\n        return;\n      }\n      var cb = function cb() {\n        var _a, _b;\n        if (!isHidden(root)) {\n          var rect = {\n            width: root.value.offsetWidth,\n            height: root.value.offsetHeight\n          };\n          state.rect = rect;\n          state.width = +((_a = props.width) != null ? _a : rect.width);\n          state.height = +((_b = props.height) != null ? _b : rect.height);\n        }\n        if (count.value) {\n          active = Math.min(count.value - 1, active);\n        }\n        state.active = active;\n        state.swiping = true;\n        state.offset = getTargetOffset(active);\n        children.forEach(function (swipe) {\n          swipe.setOffset(0);\n        });\n        autoplay();\n      };\n      if (isHidden(root)) {\n        nextTick().then(cb);\n      } else {\n        cb();\n      }\n    };\n    var resize = function resize() {\n      return initialize(state.active);\n    };\n    var touchStartTime;\n    var onTouchStart = function onTouchStart(event) {\n      if (!props.touchable) return;\n      touch.start(event);\n      touchStartTime = Date.now();\n      stopAutoplay();\n      correctPosition();\n    };\n    var onTouchMove = function onTouchMove(event) {\n      if (props.touchable && state.swiping) {\n        touch.move(event);\n        if (isCorrectDirection.value) {\n          var isEdgeTouch = !props.loop && (state.active === 0 && delta.value > 0 || state.active === count.value - 1 && delta.value < 0);\n          if (!isEdgeTouch) {\n            preventDefault(event, props.stopPropagation);\n            move({\n              offset: delta.value\n            });\n          }\n        }\n      }\n    };\n    var onTouchEnd = function onTouchEnd() {\n      if (!props.touchable || !state.swiping) {\n        return;\n      }\n      var duration = Date.now() - touchStartTime;\n      var speed = delta.value / duration;\n      var shouldSwipe = Math.abs(speed) > 0.25 || Math.abs(delta.value) > size.value / 2;\n      if (shouldSwipe && isCorrectDirection.value) {\n        var offset = props.vertical ? touch.offsetY.value : touch.offsetX.value;\n        var pace = 0;\n        if (props.loop) {\n          pace = offset > 0 ? delta.value > 0 ? -1 : 1 : 0;\n        } else {\n          pace = -Math[delta.value > 0 ? \"ceil\" : \"floor\"](delta.value / size.value);\n        }\n        move({\n          pace: pace,\n          emitChange: true\n        });\n      } else if (delta.value) {\n        move({\n          pace: 0\n        });\n      }\n      state.swiping = false;\n      autoplay();\n    };\n    var swipeTo = function swipeTo(index) {\n      var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n      correctPosition();\n      touch.reset();\n      doubleRaf(function () {\n        var targetIndex;\n        if (props.loop && index === count.value) {\n          targetIndex = state.active === 0 ? 0 : index;\n        } else {\n          targetIndex = index % count.value;\n        }\n        if (options.immediate) {\n          doubleRaf(function () {\n            state.swiping = false;\n          });\n        } else {\n          state.swiping = false;\n        }\n        move({\n          pace: targetIndex - state.active,\n          emitChange: true\n        });\n      });\n    };\n    var renderDot = function renderDot(_, index) {\n      var active = index === activeIndicator.value;\n      var style = active ? {\n        backgroundColor: props.indicatorColor\n      } : void 0;\n      return _createVNode(\"i\", {\n        \"style\": style,\n        \"class\": bem(\"indicator\", {\n          active: active\n        })\n      }, null);\n    };\n    var renderIndicator = function renderIndicator() {\n      if (slots.indicator) {\n        return slots.indicator({\n          active: activeIndicator.value,\n          total: count.value\n        });\n      }\n      if (props.showIndicators && count.value > 1) {\n        return _createVNode(\"div\", {\n          \"class\": bem(\"indicators\", {\n            vertical: props.vertical\n          })\n        }, [Array(count.value).fill(\"\").map(renderDot)]);\n      }\n    };\n    useExpose({\n      prev: prev,\n      next: next,\n      state: state,\n      resize: resize,\n      swipeTo: swipeTo\n    });\n    linkChildren({\n      size: size,\n      props: props,\n      count: count,\n      activeIndicator: activeIndicator\n    });\n    watch(function () {\n      return props.initialSwipe;\n    }, function (value) {\n      return initialize(+value);\n    });\n    watch(count, function () {\n      return initialize(state.active);\n    });\n    watch(function () {\n      return props.autoplay;\n    }, autoplay);\n    watch([windowWidth, windowHeight], resize);\n    watch(usePageVisibility(), function (visible) {\n      if (visible === \"visible\") {\n        autoplay();\n      } else {\n        stopAutoplay();\n      }\n    });\n    onMounted(initialize);\n    onActivated(function () {\n      return initialize(state.active);\n    });\n    onPopupReopen(function () {\n      return initialize(state.active);\n    });\n    onDeactivated(stopAutoplay);\n    onBeforeUnmount(stopAutoplay);\n    useEventListener(\"touchmove\", onTouchMove, {\n      target: track\n    });\n    return function () {\n      var _a;\n      return _createVNode(\"div\", {\n        \"ref\": root,\n        \"class\": bem()\n      }, [_createVNode(\"div\", {\n        \"ref\": track,\n        \"style\": trackStyle.value,\n        \"class\": bem(\"track\", {\n          vertical: props.vertical\n        }),\n        \"onTouchstartPassive\": onTouchStart,\n        \"onTouchend\": onTouchEnd,\n        \"onTouchcancel\": onTouchEnd\n      }, [(_a = slots.default) == null ? void 0 : _a.call(slots)]), renderIndicator()]);\n    };\n  }\n});\nexport { SWIPE_KEY, stdin_default as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}