{"ast": null, "code": "var stdin_default = {\n  name: \"Name\",\n  tel: \"Phone\",\n  save: \"Save\",\n  confirm: \"Confirm\",\n  cancel: \"Cancel\",\n  delete: \"Delete\",\n  loading: \"Loading...\",\n  noCoupon: \"No coupons\",\n  nameEmpty: \"Please fill in the name\",\n  addContact: \"Add contact\",\n  telInvalid: \"Malformed phone number\",\n  vanCalendar: {\n    end: \"End\",\n    start: \"Start\",\n    title: \"Calendar\",\n    weekdays: [\"Sun\", \"Mon\", \"Tue\", \"Wed\", \"Thu\", \"Fri\", \"Sat\"],\n    monthTitle: function monthTitle(year, month) {\n      return \"\".concat(year, \"/\").concat(month);\n    },\n    rangePrompt: function rangePrompt(maxRange) {\n      return \"Choose no more than \".concat(maxRange, \" days\");\n    }\n  },\n  vanCascader: {\n    select: \"Select\"\n  },\n  vanPagination: {\n    prev: \"Previous\",\n    next: \"Next\"\n  },\n  vanPullRefresh: {\n    pulling: \"Pull to refresh...\",\n    loosing: \"Loose to refresh...\"\n  },\n  vanSubmitBar: {\n    label: \"Total:\"\n  },\n  vanCoupon: {\n    unlimited: \"Unlimited\",\n    discount: function discount(_discount) {\n      return \"\".concat(_discount * 10, \"% off\");\n    },\n    condition: function condition(_condition) {\n      return \"At least \".concat(_condition);\n    }\n  },\n  vanCouponCell: {\n    title: \"Coupon\",\n    count: function count(_count) {\n      return \"You have \".concat(_count, \" coupons\");\n    }\n  },\n  vanCouponList: {\n    exchange: \"Exchange\",\n    close: \"Close\",\n    enable: \"Available\",\n    disabled: \"Unavailable\",\n    placeholder: \"Coupon code\"\n  },\n  vanAddressEdit: {\n    area: \"Area\",\n    postal: \"Postal\",\n    areaEmpty: \"Please select a receiving area\",\n    addressEmpty: \"Address can not be empty\",\n    postalEmpty: \"Wrong postal code\",\n    addressDetail: \"Address\",\n    defaultAddress: \"Set as the default address\"\n  },\n  vanAddressList: {\n    add: \"Add new address\"\n  }\n};\nexport { stdin_default as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}