{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _Cell from \"./Cell.mjs\";\nvar Cell = withInstall(_Cell);\nvar stdin_default = Cell;\nexport { Cell, stdin_default as default };", "map": {"version": 3, "names": ["withInstall", "_Cell", "Cell", "stdin_default", "default"], "sources": ["C:/Users/<USER>/Desktop/vue3_3.0/node_modules/vant/es/cell/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _Cell from \"./Cell.mjs\";\nconst Cell = withInstall(_Cell);\nvar stdin_default = Cell;\nexport {\n  Cell,\n  stdin_default as default\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,KAAK,MAAM,YAAY;AAC9B,IAAMC,IAAI,GAAGF,WAAW,CAACC,KAAK,CAAC;AAC/B,IAAIE,aAAa,GAAGD,IAAI;AACxB,SACEA,IAAI,EACJC,aAAa,IAAIC,OAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}