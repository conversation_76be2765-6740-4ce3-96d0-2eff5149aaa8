{"ast": null, "code": "function _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return _typeof(key) === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (_typeof(input) !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (_typeof(res) !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\nfunction _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nfunction _iterableToArrayLimit(arr, i) { var _i = null == arr ? null : \"undefined\" != typeof Symbol && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (null != _i) { var _s, _e, _x, _r, _arr = [], _n = !0, _d = !1; try { if (_x = (_i = _i.call(arr)).next, 0 === i) { if (Object(_i) !== _i) return; _n = !1; } else for (; !(_n = (_s = _x.call(_i)).done) && (_arr.push(_s.value), _arr.length !== i); _n = !0); } catch (err) { _d = !0, _e = err; } finally { try { if (!_n && null != _i.return && (_r = _i.return(), Object(_r) !== _r)) return; } finally { if (_d) throw _e; } } return _arr; } }\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\nimport { createVNode as _createVNode } from \"vue\";\nimport { ref, reactive, computed, defineComponent } from \"vue\";\nimport { extend, numericProp, BORDER_BOTTOM, getZIndexStyle, createNamespace } from \"../utils/index.mjs\";\nimport { INDEX_BAR_KEY } from \"../index-bar/IndexBar.mjs\";\nimport { getScrollTop, getRootScrollTop } from \"../utils/dom.mjs\";\nimport { useRect, useParent } from \"@vant/use\";\nimport { useExpose } from \"../composables/use-expose.mjs\";\nvar _createNamespace = createNamespace(\"index-anchor\"),\n  _createNamespace2 = _slicedToArray(_createNamespace, 2),\n  name = _createNamespace2[0],\n  bem = _createNamespace2[1];\nvar indexAnchorProps = {\n  index: numericProp\n};\nvar stdin_default = defineComponent({\n  name: name,\n  props: indexAnchorProps,\n  setup: function setup(props, _ref) {\n    var slots = _ref.slots;\n    var state = reactive({\n      top: 0,\n      left: null,\n      rect: {\n        top: 0,\n        height: 0\n      },\n      width: null,\n      active: false\n    });\n    var root = ref();\n    var _useParent = useParent(INDEX_BAR_KEY),\n      parent = _useParent.parent;\n    if (!parent) {\n      if (process.env.NODE_ENV !== \"production\") {}\n      return;\n    }\n    var isSticky = function isSticky() {\n      return state.active && parent.props.sticky;\n    };\n    var anchorStyle = computed(function () {\n      var _parent$props = parent.props,\n        zIndex = _parent$props.zIndex,\n        highlightColor = _parent$props.highlightColor;\n      if (isSticky()) {\n        return extend(getZIndexStyle(zIndex), {\n          left: state.left ? \"\".concat(state.left, \"px\") : void 0,\n          width: state.width ? \"\".concat(state.width, \"px\") : void 0,\n          transform: state.top ? \"translate3d(0, \".concat(state.top, \"px, 0)\") : void 0,\n          color: highlightColor\n        });\n      }\n    });\n    var getRect = function getRect(scrollParent, scrollParentRect) {\n      var rootRect = useRect(root);\n      state.rect.height = rootRect.height;\n      if (scrollParent === window || scrollParent === document.body) {\n        state.rect.top = rootRect.top + getRootScrollTop();\n      } else {\n        state.rect.top = rootRect.top + getScrollTop(scrollParent) - scrollParentRect.top;\n      }\n      return state.rect;\n    };\n    useExpose({\n      state: state,\n      getRect: getRect\n    });\n    return function () {\n      var sticky = isSticky();\n      return _createVNode(\"div\", {\n        \"ref\": root,\n        \"style\": {\n          height: sticky ? \"\".concat(state.rect.height, \"px\") : void 0\n        }\n      }, [_createVNode(\"div\", {\n        \"style\": anchorStyle.value,\n        \"class\": [bem({\n          sticky: sticky\n        }), _defineProperty({}, BORDER_BOTTOM, sticky)]\n      }, [slots.default ? slots.default() : props.index])]);\n    };\n  }\n});\nexport { stdin_default as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}