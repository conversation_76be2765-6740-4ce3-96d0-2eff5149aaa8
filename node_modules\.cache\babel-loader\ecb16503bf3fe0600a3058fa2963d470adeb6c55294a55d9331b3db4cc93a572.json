{"ast": null, "code": "function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nfunction _iterableToArrayLimit(arr, i) { var _i = null == arr ? null : \"undefined\" != typeof Symbol && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (null != _i) { var _s, _e, _x, _r, _arr = [], _n = !0, _d = !1; try { if (_x = (_i = _i.call(arr)).next, 0 === i) { if (Object(_i) !== _i) return; _n = !1; } else for (; !(_n = (_s = _x.call(_i)).done) && (_arr.push(_s.value), _arr.length !== i); _n = !0); } catch (err) { _d = !0, _e = err; } finally { try { if (!_n && null != _i.return && (_r = _i.return(), Object(_r) !== _r)) return; } finally { if (_d) throw _e; } } return _arr; } }\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\nimport { createNamespace } from \"../utils/index.mjs\";\nvar _createNamespace = createNamespace(\"calendar\"),\n  _createNamespace2 = _slicedToArray(_createNamespace, 3),\n  name = _createNamespace2[0],\n  bem = _createNamespace2[1],\n  t = _createNamespace2[2];\nvar formatMonthTitle = function formatMonthTitle(date) {\n  return t(\"monthTitle\", date.getFullYear(), date.getMonth() + 1);\n};\nfunction compareMonth(date1, date2) {\n  var year1 = date1.getFullYear();\n  var year2 = date2.getFullYear();\n  if (year1 === year2) {\n    var month1 = date1.getMonth();\n    var month2 = date2.getMonth();\n    return month1 === month2 ? 0 : month1 > month2 ? 1 : -1;\n  }\n  return year1 > year2 ? 1 : -1;\n}\nfunction compareDay(day1, day2) {\n  var compareMonthResult = compareMonth(day1, day2);\n  if (compareMonthResult === 0) {\n    var date1 = day1.getDate();\n    var date2 = day2.getDate();\n    return date1 === date2 ? 0 : date1 > date2 ? 1 : -1;\n  }\n  return compareMonthResult;\n}\nvar cloneDate = function cloneDate(date) {\n  return new Date(date);\n};\nvar cloneDates = function cloneDates(dates) {\n  return Array.isArray(dates) ? dates.map(cloneDate) : cloneDate(dates);\n};\nfunction getDayByOffset(date, offset) {\n  var cloned = cloneDate(date);\n  cloned.setDate(cloned.getDate() + offset);\n  return cloned;\n}\nvar getPrevDay = function getPrevDay(date) {\n  return getDayByOffset(date, -1);\n};\nvar getNextDay = function getNextDay(date) {\n  return getDayByOffset(date, 1);\n};\nvar getToday = function getToday() {\n  var today = new Date();\n  today.setHours(0, 0, 0, 0);\n  return today;\n};\nfunction calcDateNum(date) {\n  var day1 = date[0].getTime();\n  var day2 = date[1].getTime();\n  return (day2 - day1) / (1e3 * 60 * 60 * 24) + 1;\n}\nexport { bem, calcDateNum, cloneDate, cloneDates, compareDay, compareMonth, formatMonthTitle, getDayByOffset, getNextDay, getPrevDay, getToday, name, t };", "map": {"version": 3, "names": ["createNamespace", "_createNamespace", "_createNamespace2", "_slicedToArray", "name", "bem", "t", "formatMonthTitle", "date", "getFullYear", "getMonth", "compareMonth", "date1", "date2", "year1", "year2", "month1", "month2", "compareDay", "day1", "day2", "compareMonthResult", "getDate", "cloneDate", "Date", "cloneDates", "dates", "Array", "isArray", "map", "getDayByOffset", "offset", "cloned", "setDate", "getPrevDay", "getNextDay", "get<PERSON><PERSON>y", "today", "setHours", "calcDateNum", "getTime"], "sources": ["C:/Users/<USER>/Downloads/Telegram Desktop/vue3_3.0/vue3_3.0/node_modules/vant/es/calendar/utils.mjs"], "sourcesContent": ["import { createNamespace } from \"../utils/index.mjs\";\nconst [name, bem, t] = createNamespace(\"calendar\");\nconst formatMonthTitle = (date) => t(\"monthTitle\", date.getFullYear(), date.getMonth() + 1);\nfunction compareMonth(date1, date2) {\n  const year1 = date1.getFullYear();\n  const year2 = date2.getFullYear();\n  if (year1 === year2) {\n    const month1 = date1.getMonth();\n    const month2 = date2.getMonth();\n    return month1 === month2 ? 0 : month1 > month2 ? 1 : -1;\n  }\n  return year1 > year2 ? 1 : -1;\n}\nfunction compareDay(day1, day2) {\n  const compareMonthResult = compareMonth(day1, day2);\n  if (compareMonthResult === 0) {\n    const date1 = day1.getDate();\n    const date2 = day2.getDate();\n    return date1 === date2 ? 0 : date1 > date2 ? 1 : -1;\n  }\n  return compareMonthResult;\n}\nconst cloneDate = (date) => new Date(date);\nconst cloneDates = (dates) => Array.isArray(dates) ? dates.map(cloneDate) : cloneDate(dates);\nfunction getDayByOffset(date, offset) {\n  const cloned = cloneDate(date);\n  cloned.setDate(cloned.getDate() + offset);\n  return cloned;\n}\nconst getPrevDay = (date) => getDayByOffset(date, -1);\nconst getNextDay = (date) => getDayByOffset(date, 1);\nconst getToday = () => {\n  const today = new Date();\n  today.setHours(0, 0, 0, 0);\n  return today;\n};\nfunction calcDateNum(date) {\n  const day1 = date[0].getTime();\n  const day2 = date[1].getTime();\n  return (day2 - day1) / (1e3 * 60 * 60 * 24) + 1;\n}\nexport {\n  bem,\n  calcDateNum,\n  cloneDate,\n  cloneDates,\n  compareDay,\n  compareMonth,\n  formatMonthTitle,\n  getDayByOffset,\n  getNextDay,\n  getPrevDay,\n  getToday,\n  name,\n  t\n};\n"], "mappings": ";;;;;;AAAA,SAASA,eAAe,QAAQ,oBAAoB;AACpD,IAAAC,gBAAA,GAAuBD,eAAe,CAAC,UAAU,CAAC;EAAAE,iBAAA,GAAAC,cAAA,CAAAF,gBAAA;EAA3CG,IAAI,GAAAF,iBAAA;EAAEG,GAAG,GAAAH,iBAAA;EAAEI,CAAC,GAAAJ,iBAAA;AACnB,IAAMK,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAIC,IAAI;EAAA,OAAKF,CAAC,CAAC,YAAY,EAAEE,IAAI,CAACC,WAAW,CAAC,CAAC,EAAED,IAAI,CAACE,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;AAAA;AAC3F,SAASC,YAAYA,CAACC,KAAK,EAAEC,KAAK,EAAE;EAClC,IAAMC,KAAK,GAAGF,KAAK,CAACH,WAAW,CAAC,CAAC;EACjC,IAAMM,KAAK,GAAGF,KAAK,CAACJ,WAAW,CAAC,CAAC;EACjC,IAAIK,KAAK,KAAKC,KAAK,EAAE;IACnB,IAAMC,MAAM,GAAGJ,KAAK,CAACF,QAAQ,CAAC,CAAC;IAC/B,IAAMO,MAAM,GAAGJ,KAAK,CAACH,QAAQ,CAAC,CAAC;IAC/B,OAAOM,MAAM,KAAKC,MAAM,GAAG,CAAC,GAAGD,MAAM,GAAGC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;EACzD;EACA,OAAOH,KAAK,GAAGC,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC;AAC/B;AACA,SAASG,UAAUA,CAACC,IAAI,EAAEC,IAAI,EAAE;EAC9B,IAAMC,kBAAkB,GAAGV,YAAY,CAACQ,IAAI,EAAEC,IAAI,CAAC;EACnD,IAAIC,kBAAkB,KAAK,CAAC,EAAE;IAC5B,IAAMT,KAAK,GAAGO,IAAI,CAACG,OAAO,CAAC,CAAC;IAC5B,IAAMT,KAAK,GAAGO,IAAI,CAACE,OAAO,CAAC,CAAC;IAC5B,OAAOV,KAAK,KAAKC,KAAK,GAAG,CAAC,GAAGD,KAAK,GAAGC,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC;EACrD;EACA,OAAOQ,kBAAkB;AAC3B;AACA,IAAME,SAAS,GAAG,SAAZA,SAASA,CAAIf,IAAI;EAAA,OAAK,IAAIgB,IAAI,CAAChB,IAAI,CAAC;AAAA;AAC1C,IAAMiB,UAAU,GAAG,SAAbA,UAAUA,CAAIC,KAAK;EAAA,OAAKC,KAAK,CAACC,OAAO,CAACF,KAAK,CAAC,GAAGA,KAAK,CAACG,GAAG,CAACN,SAAS,CAAC,GAAGA,SAAS,CAACG,KAAK,CAAC;AAAA;AAC5F,SAASI,cAAcA,CAACtB,IAAI,EAAEuB,MAAM,EAAE;EACpC,IAAMC,MAAM,GAAGT,SAAS,CAACf,IAAI,CAAC;EAC9BwB,MAAM,CAACC,OAAO,CAACD,MAAM,CAACV,OAAO,CAAC,CAAC,GAAGS,MAAM,CAAC;EACzC,OAAOC,MAAM;AACf;AACA,IAAME,UAAU,GAAG,SAAbA,UAAUA,CAAI1B,IAAI;EAAA,OAAKsB,cAAc,CAACtB,IAAI,EAAE,CAAC,CAAC,CAAC;AAAA;AACrD,IAAM2B,UAAU,GAAG,SAAbA,UAAUA,CAAI3B,IAAI;EAAA,OAAKsB,cAAc,CAACtB,IAAI,EAAE,CAAC,CAAC;AAAA;AACpD,IAAM4B,QAAQ,GAAG,SAAXA,QAAQA,CAAA,EAAS;EACrB,IAAMC,KAAK,GAAG,IAAIb,IAAI,CAAC,CAAC;EACxBa,KAAK,CAACC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAC1B,OAAOD,KAAK;AACd,CAAC;AACD,SAASE,WAAWA,CAAC/B,IAAI,EAAE;EACzB,IAAMW,IAAI,GAAGX,IAAI,CAAC,CAAC,CAAC,CAACgC,OAAO,CAAC,CAAC;EAC9B,IAAMpB,IAAI,GAAGZ,IAAI,CAAC,CAAC,CAAC,CAACgC,OAAO,CAAC,CAAC;EAC9B,OAAO,CAACpB,IAAI,GAAGD,IAAI,KAAK,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC;AACjD;AACA,SACEd,GAAG,EACHkC,WAAW,EACXhB,SAAS,EACTE,UAAU,EACVP,UAAU,EACVP,YAAY,EACZJ,gBAAgB,EAChBuB,cAAc,EACdK,UAAU,EACVD,UAAU,EACVE,QAAQ,EACRhC,IAAI,EACJE,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}