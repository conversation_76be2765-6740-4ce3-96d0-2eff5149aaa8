{"ast": null, "code": "function _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor); } }\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, \"prototype\", { writable: false }); return Constructor; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return _typeof(key) === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (_typeof(input) !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (_typeof(res) !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\nimport { nextTick } from \"vue\";\nimport { inBrowser, getScrollParent } from \"@vant/use\";\nimport { remove as _remove, on, off, throttle, supportWebp, getDPR, getBestSelectionFromSrcset, hasIntersectionObserver, modeType, ImageCache } from \"./util.mjs\";\nimport { isObject } from \"../../utils/index.mjs\";\nimport ReactiveListener from \"./listener.mjs\";\nvar DEFAULT_URL = \"data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7\";\nvar DEFAULT_EVENTS = [\"scroll\", \"wheel\", \"mousewheel\", \"resize\", \"animationend\", \"transitionend\", \"touchmove\"];\nvar DEFAULT_OBSERVER_OPTIONS = {\n  rootMargin: \"0px\",\n  threshold: 0\n};\nfunction stdin_default() {\n  return /*#__PURE__*/function () {\n    function Lazy(_ref) {\n      var preLoad = _ref.preLoad,\n        error = _ref.error,\n        throttleWait = _ref.throttleWait,\n        preLoadTop = _ref.preLoadTop,\n        dispatchEvent = _ref.dispatchEvent,\n        loading = _ref.loading,\n        attempt = _ref.attempt,\n        _ref$silent = _ref.silent,\n        silent = _ref$silent === void 0 ? true : _ref$silent,\n        scale = _ref.scale,\n        listenEvents = _ref.listenEvents,\n        filter = _ref.filter,\n        adapter = _ref.adapter,\n        observer = _ref.observer,\n        observerOptions = _ref.observerOptions;\n      _classCallCheck(this, Lazy);\n      this.mode = modeType.event;\n      this.listeners = [];\n      this.targetIndex = 0;\n      this.targets = [];\n      this.options = {\n        silent: silent,\n        dispatchEvent: !!dispatchEvent,\n        throttleWait: throttleWait || 200,\n        preLoad: preLoad || 1.3,\n        preLoadTop: preLoadTop || 0,\n        error: error || DEFAULT_URL,\n        loading: loading || DEFAULT_URL,\n        attempt: attempt || 3,\n        scale: scale || getDPR(scale),\n        ListenEvents: listenEvents || DEFAULT_EVENTS,\n        supportWebp: supportWebp(),\n        filter: filter || {},\n        adapter: adapter || {},\n        observer: !!observer,\n        observerOptions: observerOptions || DEFAULT_OBSERVER_OPTIONS\n      };\n      this.initEvent();\n      this.imageCache = new ImageCache({\n        max: 200\n      });\n      this.lazyLoadHandler = throttle(this.lazyLoadHandler.bind(this), this.options.throttleWait);\n      this.setMode(this.options.observer ? modeType.observer : modeType.event);\n    }\n    _createClass(Lazy, [{\n      key: \"config\",\n      value: function config() {\n        var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n        Object.assign(this.options, options);\n      }\n    }, {\n      key: \"performance\",\n      value: function performance() {\n        return this.listeners.map(function (item) {\n          return item.performance();\n        });\n      }\n    }, {\n      key: \"addLazyBox\",\n      value: function addLazyBox(vm) {\n        this.listeners.push(vm);\n        if (inBrowser) {\n          this.addListenerTarget(window);\n          this.observer && this.observer.observe(vm.el);\n          if (vm.$el && vm.$el.parentNode) {\n            this.addListenerTarget(vm.$el.parentNode);\n          }\n        }\n      }\n    }, {\n      key: \"add\",\n      value: function add(el, binding, vnode) {\n        var _this = this;\n        if (this.listeners.some(function (item) {\n          return item.el === el;\n        })) {\n          this.update(el, binding);\n          return nextTick(this.lazyLoadHandler);\n        }\n        var value = this.valueFormatter(binding.value);\n        var src = value.src;\n        nextTick(function () {\n          src = getBestSelectionFromSrcset(el, _this.options.scale) || src;\n          _this.observer && _this.observer.observe(el);\n          var container = Object.keys(binding.modifiers)[0];\n          var $parent;\n          if (container) {\n            $parent = vnode.context.$refs[container];\n            $parent = $parent ? $parent.$el || $parent : document.getElementById(container);\n          }\n          if (!$parent) {\n            $parent = getScrollParent(el);\n          }\n          var newListener = new ReactiveListener({\n            bindType: binding.arg,\n            $parent: $parent,\n            el: el,\n            src: src,\n            loading: value.loading,\n            error: value.error,\n            cors: value.cors,\n            elRenderer: _this.elRenderer.bind(_this),\n            options: _this.options,\n            imageCache: _this.imageCache\n          });\n          _this.listeners.push(newListener);\n          if (inBrowser) {\n            _this.addListenerTarget(window);\n            _this.addListenerTarget($parent);\n          }\n          _this.lazyLoadHandler();\n          nextTick(function () {\n            return _this.lazyLoadHandler();\n          });\n        });\n      }\n    }, {\n      key: \"update\",\n      value: function update(el, binding, vnode) {\n        var _this2 = this;\n        var value = this.valueFormatter(binding.value);\n        var src = value.src;\n        src = getBestSelectionFromSrcset(el, this.options.scale) || src;\n        var exist = this.listeners.find(function (item) {\n          return item.el === el;\n        });\n        if (!exist) {\n          this.add(el, binding, vnode);\n        } else {\n          exist.update({\n            src: src,\n            error: value.error,\n            loading: value.loading\n          });\n        }\n        if (this.observer) {\n          this.observer.unobserve(el);\n          this.observer.observe(el);\n        }\n        this.lazyLoadHandler();\n        nextTick(function () {\n          return _this2.lazyLoadHandler();\n        });\n      }\n    }, {\n      key: \"remove\",\n      value: function remove(el) {\n        if (!el) return;\n        this.observer && this.observer.unobserve(el);\n        var existItem = this.listeners.find(function (item) {\n          return item.el === el;\n        });\n        if (existItem) {\n          this.removeListenerTarget(existItem.$parent);\n          this.removeListenerTarget(window);\n          _remove(this.listeners, existItem);\n          existItem.$destroy();\n        }\n      }\n    }, {\n      key: \"removeComponent\",\n      value: function removeComponent(vm) {\n        if (!vm) return;\n        _remove(this.listeners, vm);\n        this.observer && this.observer.unobserve(vm.el);\n        if (vm.$parent && vm.$el.parentNode) {\n          this.removeListenerTarget(vm.$el.parentNode);\n        }\n        this.removeListenerTarget(window);\n      }\n    }, {\n      key: \"setMode\",\n      value: function setMode(mode) {\n        var _this3 = this;\n        if (!hasIntersectionObserver && mode === modeType.observer) {\n          mode = modeType.event;\n        }\n        this.mode = mode;\n        if (mode === modeType.event) {\n          if (this.observer) {\n            this.listeners.forEach(function (listener) {\n              _this3.observer.unobserve(listener.el);\n            });\n            this.observer = null;\n          }\n          this.targets.forEach(function (target) {\n            _this3.initListen(target.el, true);\n          });\n        } else {\n          this.targets.forEach(function (target) {\n            _this3.initListen(target.el, false);\n          });\n          this.initIntersectionObserver();\n        }\n      }\n    }, {\n      key: \"addListenerTarget\",\n      value: function addListenerTarget(el) {\n        if (!el) return;\n        var target = this.targets.find(function (target2) {\n          return target2.el === el;\n        });\n        if (!target) {\n          target = {\n            el: el,\n            id: ++this.targetIndex,\n            childrenCount: 1,\n            listened: true\n          };\n          this.mode === modeType.event && this.initListen(target.el, true);\n          this.targets.push(target);\n        } else {\n          target.childrenCount++;\n        }\n        return this.targetIndex;\n      }\n    }, {\n      key: \"removeListenerTarget\",\n      value: function removeListenerTarget(el) {\n        var _this4 = this;\n        this.targets.forEach(function (target, index) {\n          if (target.el === el) {\n            target.childrenCount--;\n            if (!target.childrenCount) {\n              _this4.initListen(target.el, false);\n              _this4.targets.splice(index, 1);\n              target = null;\n            }\n          }\n        });\n      }\n    }, {\n      key: \"initListen\",\n      value: function initListen(el, start) {\n        var _this5 = this;\n        this.options.ListenEvents.forEach(function (evt) {\n          return (start ? on : off)(el, evt, _this5.lazyLoadHandler);\n        });\n      }\n    }, {\n      key: \"initEvent\",\n      value: function initEvent() {\n        var _this6 = this;\n        this.Event = {\n          listeners: {\n            loading: [],\n            loaded: [],\n            error: []\n          }\n        };\n        this.$on = function (event, func) {\n          if (!_this6.Event.listeners[event]) _this6.Event.listeners[event] = [];\n          _this6.Event.listeners[event].push(func);\n        };\n        this.$once = function (event, func) {\n          var on2 = function on2() {\n            _this6.$off(event, on2);\n            for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n              args[_key] = arguments[_key];\n            }\n            func.apply(_this6, args);\n          };\n          _this6.$on(event, on2);\n        };\n        this.$off = function (event, func) {\n          if (!func) {\n            if (!_this6.Event.listeners[event]) return;\n            _this6.Event.listeners[event].length = 0;\n            return;\n          }\n          _remove(_this6.Event.listeners[event], func);\n        };\n        this.$emit = function (event, context, inCache) {\n          if (!_this6.Event.listeners[event]) return;\n          _this6.Event.listeners[event].forEach(function (func) {\n            return func(context, inCache);\n          });\n        };\n      }\n    }, {\n      key: \"lazyLoadHandler\",\n      value: function lazyLoadHandler() {\n        var _this7 = this;\n        var freeList = [];\n        this.listeners.forEach(function (listener) {\n          if (!listener.el || !listener.el.parentNode) {\n            freeList.push(listener);\n          }\n          var catIn = listener.checkInView();\n          if (!catIn) return;\n          listener.load();\n        });\n        freeList.forEach(function (item) {\n          _remove(_this7.listeners, item);\n          item.$destroy();\n        });\n      }\n    }, {\n      key: \"initIntersectionObserver\",\n      value: function initIntersectionObserver() {\n        var _this8 = this;\n        if (!hasIntersectionObserver) {\n          return;\n        }\n        this.observer = new IntersectionObserver(this.observerHandler.bind(this), this.options.observerOptions);\n        if (this.listeners.length) {\n          this.listeners.forEach(function (listener) {\n            _this8.observer.observe(listener.el);\n          });\n        }\n      }\n    }, {\n      key: \"observerHandler\",\n      value: function observerHandler(entries) {\n        var _this9 = this;\n        entries.forEach(function (entry) {\n          if (entry.isIntersecting) {\n            _this9.listeners.forEach(function (listener) {\n              if (listener.el === entry.target) {\n                if (listener.state.loaded) return _this9.observer.unobserve(listener.el);\n                listener.load();\n              }\n            });\n          }\n        });\n      }\n    }, {\n      key: \"elRenderer\",\n      value: function elRenderer(listener, state, cache) {\n        if (!listener.el) return;\n        var el = listener.el,\n          bindType = listener.bindType;\n        var src;\n        switch (state) {\n          case \"loading\":\n            src = listener.loading;\n            break;\n          case \"error\":\n            src = listener.error;\n            break;\n          default:\n            src = listener.src;\n            break;\n        }\n        if (bindType) {\n          el.style[bindType] = 'url(\"' + src + '\")';\n        } else if (el.getAttribute(\"src\") !== src) {\n          el.setAttribute(\"src\", src);\n        }\n        el.setAttribute(\"lazy\", state);\n        this.$emit(state, listener, cache);\n        this.options.adapter[state] && this.options.adapter[state](listener, this.options);\n        if (this.options.dispatchEvent) {\n          var event = new CustomEvent(state, {\n            detail: listener\n          });\n          el.dispatchEvent(event);\n        }\n      }\n    }, {\n      key: \"valueFormatter\",\n      value: function valueFormatter(value) {\n        var src = value;\n        var _this$options = this.options,\n          loading = _this$options.loading,\n          error = _this$options.error;\n        if (isObject(value)) {\n          if (process.env.NODE_ENV !== \"production\" && !value.src && !this.options.silent) {\n            console.error(\"[@vant/lazyload] miss src with \" + value);\n          }\n          src = value.src;\n          loading = value.loading || this.options.loading;\n          error = value.error || this.options.error;\n        }\n        return {\n          src: src,\n          loading: loading,\n          error: error\n        };\n      }\n    }]);\n    return Lazy;\n  }();\n}\nexport { stdin_default as default };", "map": {"version": 3, "names": ["nextTick", "inBrowser", "getScrollParent", "remove", "on", "off", "throttle", "supportWebp", "getDPR", "getBestSelectionFromSrcset", "hasIntersectionObserver", "modeType", "ImageCache", "isObject", "ReactiveListener", "DEFAULT_URL", "DEFAULT_EVENTS", "DEFAULT_OBSERVER_OPTIONS", "rootMargin", "threshold", "stdin_default", "Lazy", "_ref", "preLoad", "error", "throttleWait", "preLoadTop", "dispatchEvent", "loading", "attempt", "_ref$silent", "silent", "scale", "listenEvents", "filter", "adapter", "observer", "observerOptions", "_classCallCheck", "mode", "event", "listeners", "targetIndex", "targets", "options", "ListenEvents", "initEvent", "imageCache", "max", "lazy<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bind", "setMode", "_createClass", "key", "value", "config", "arguments", "length", "undefined", "Object", "assign", "performance", "map", "item", "addLazyBox", "vm", "push", "addListenerTarget", "window", "observe", "el", "$el", "parentNode", "add", "binding", "vnode", "_this", "some", "update", "valueFormatter", "src", "container", "keys", "modifiers", "$parent", "context", "$refs", "document", "getElementById", "newListener", "bindType", "arg", "cors", "<PERSON><PERSON><PERSON><PERSON>", "_this2", "exist", "find", "unobserve", "existItem", "removeListenerTarget", "$destroy", "removeComponent", "_this3", "for<PERSON>ach", "listener", "target", "initListen", "initIntersectionObserver", "target2", "id", "childrenCount", "listened", "_this4", "index", "splice", "start", "_this5", "evt", "_this6", "Event", "loaded", "$on", "func", "$once", "on2", "$off", "_len", "args", "Array", "_key", "apply", "$emit", "inCache", "_this7", "freeList", "catIn", "checkInView", "load", "_this8", "IntersectionObserver", "observer<PERSON><PERSON><PERSON>", "entries", "_this9", "entry", "isIntersecting", "state", "cache", "style", "getAttribute", "setAttribute", "CustomEvent", "detail", "_this$options", "process", "env", "NODE_ENV", "console", "default"], "sources": ["C:/Users/<USER>/Desktop/vue3_3.0/node_modules/vant/es/lazyload/vue-lazyload/lazy.mjs"], "sourcesContent": ["import { nextTick } from \"vue\";\nimport { inBrowser, getScrollParent } from \"@vant/use\";\nimport {\n  remove,\n  on,\n  off,\n  throttle,\n  supportWebp,\n  getDPR,\n  getBestSelectionFromSrcset,\n  hasIntersectionObserver,\n  modeType,\n  ImageCache\n} from \"./util.mjs\";\nimport { isObject } from \"../../utils/index.mjs\";\nimport ReactiveListener from \"./listener.mjs\";\nconst DEFAULT_URL = \"data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7\";\nconst DEFAULT_EVENTS = [\n  \"scroll\",\n  \"wheel\",\n  \"mousewheel\",\n  \"resize\",\n  \"animationend\",\n  \"transitionend\",\n  \"touchmove\"\n];\nconst DEFAULT_OBSERVER_OPTIONS = {\n  rootMargin: \"0px\",\n  threshold: 0\n};\nfunction stdin_default() {\n  return class Lazy {\n    constructor({\n      preLoad,\n      error,\n      throttleWait,\n      preLoadTop,\n      dispatchEvent,\n      loading,\n      attempt,\n      silent = true,\n      scale,\n      listenEvents,\n      filter,\n      adapter,\n      observer,\n      observerOptions\n    }) {\n      this.mode = modeType.event;\n      this.listeners = [];\n      this.targetIndex = 0;\n      this.targets = [];\n      this.options = {\n        silent,\n        dispatchEvent: !!dispatchEvent,\n        throttleWait: throttleWait || 200,\n        preLoad: preLoad || 1.3,\n        preLoadTop: preLoadTop || 0,\n        error: error || DEFAULT_URL,\n        loading: loading || DEFAULT_URL,\n        attempt: attempt || 3,\n        scale: scale || getDPR(scale),\n        ListenEvents: listenEvents || DEFAULT_EVENTS,\n        supportWebp: supportWebp(),\n        filter: filter || {},\n        adapter: adapter || {},\n        observer: !!observer,\n        observerOptions: observerOptions || DEFAULT_OBSERVER_OPTIONS\n      };\n      this.initEvent();\n      this.imageCache = new ImageCache({ max: 200 });\n      this.lazyLoadHandler = throttle(\n        this.lazyLoadHandler.bind(this),\n        this.options.throttleWait\n      );\n      this.setMode(this.options.observer ? modeType.observer : modeType.event);\n    }\n    config(options = {}) {\n      Object.assign(this.options, options);\n    }\n    performance() {\n      return this.listeners.map((item) => item.performance());\n    }\n    addLazyBox(vm) {\n      this.listeners.push(vm);\n      if (inBrowser) {\n        this.addListenerTarget(window);\n        this.observer && this.observer.observe(vm.el);\n        if (vm.$el && vm.$el.parentNode) {\n          this.addListenerTarget(vm.$el.parentNode);\n        }\n      }\n    }\n    add(el, binding, vnode) {\n      if (this.listeners.some((item) => item.el === el)) {\n        this.update(el, binding);\n        return nextTick(this.lazyLoadHandler);\n      }\n      const value = this.valueFormatter(binding.value);\n      let { src } = value;\n      nextTick(() => {\n        src = getBestSelectionFromSrcset(el, this.options.scale) || src;\n        this.observer && this.observer.observe(el);\n        const container = Object.keys(binding.modifiers)[0];\n        let $parent;\n        if (container) {\n          $parent = vnode.context.$refs[container];\n          $parent = $parent ? $parent.$el || $parent : document.getElementById(container);\n        }\n        if (!$parent) {\n          $parent = getScrollParent(el);\n        }\n        const newListener = new ReactiveListener({\n          bindType: binding.arg,\n          $parent,\n          el,\n          src,\n          loading: value.loading,\n          error: value.error,\n          cors: value.cors,\n          elRenderer: this.elRenderer.bind(this),\n          options: this.options,\n          imageCache: this.imageCache\n        });\n        this.listeners.push(newListener);\n        if (inBrowser) {\n          this.addListenerTarget(window);\n          this.addListenerTarget($parent);\n        }\n        this.lazyLoadHandler();\n        nextTick(() => this.lazyLoadHandler());\n      });\n    }\n    update(el, binding, vnode) {\n      const value = this.valueFormatter(binding.value);\n      let { src } = value;\n      src = getBestSelectionFromSrcset(el, this.options.scale) || src;\n      const exist = this.listeners.find((item) => item.el === el);\n      if (!exist) {\n        this.add(el, binding, vnode);\n      } else {\n        exist.update({\n          src,\n          error: value.error,\n          loading: value.loading\n        });\n      }\n      if (this.observer) {\n        this.observer.unobserve(el);\n        this.observer.observe(el);\n      }\n      this.lazyLoadHandler();\n      nextTick(() => this.lazyLoadHandler());\n    }\n    remove(el) {\n      if (!el)\n        return;\n      this.observer && this.observer.unobserve(el);\n      const existItem = this.listeners.find((item) => item.el === el);\n      if (existItem) {\n        this.removeListenerTarget(existItem.$parent);\n        this.removeListenerTarget(window);\n        remove(this.listeners, existItem);\n        existItem.$destroy();\n      }\n    }\n    removeComponent(vm) {\n      if (!vm)\n        return;\n      remove(this.listeners, vm);\n      this.observer && this.observer.unobserve(vm.el);\n      if (vm.$parent && vm.$el.parentNode) {\n        this.removeListenerTarget(vm.$el.parentNode);\n      }\n      this.removeListenerTarget(window);\n    }\n    setMode(mode) {\n      if (!hasIntersectionObserver && mode === modeType.observer) {\n        mode = modeType.event;\n      }\n      this.mode = mode;\n      if (mode === modeType.event) {\n        if (this.observer) {\n          this.listeners.forEach((listener) => {\n            this.observer.unobserve(listener.el);\n          });\n          this.observer = null;\n        }\n        this.targets.forEach((target) => {\n          this.initListen(target.el, true);\n        });\n      } else {\n        this.targets.forEach((target) => {\n          this.initListen(target.el, false);\n        });\n        this.initIntersectionObserver();\n      }\n    }\n    addListenerTarget(el) {\n      if (!el)\n        return;\n      let target = this.targets.find((target2) => target2.el === el);\n      if (!target) {\n        target = {\n          el,\n          id: ++this.targetIndex,\n          childrenCount: 1,\n          listened: true\n        };\n        this.mode === modeType.event && this.initListen(target.el, true);\n        this.targets.push(target);\n      } else {\n        target.childrenCount++;\n      }\n      return this.targetIndex;\n    }\n    removeListenerTarget(el) {\n      this.targets.forEach((target, index) => {\n        if (target.el === el) {\n          target.childrenCount--;\n          if (!target.childrenCount) {\n            this.initListen(target.el, false);\n            this.targets.splice(index, 1);\n            target = null;\n          }\n        }\n      });\n    }\n    initListen(el, start) {\n      this.options.ListenEvents.forEach(\n        (evt) => (start ? on : off)(el, evt, this.lazyLoadHandler)\n      );\n    }\n    initEvent() {\n      this.Event = {\n        listeners: {\n          loading: [],\n          loaded: [],\n          error: []\n        }\n      };\n      this.$on = (event, func) => {\n        if (!this.Event.listeners[event])\n          this.Event.listeners[event] = [];\n        this.Event.listeners[event].push(func);\n      };\n      this.$once = (event, func) => {\n        const on2 = (...args) => {\n          this.$off(event, on2);\n          func.apply(this, args);\n        };\n        this.$on(event, on2);\n      };\n      this.$off = (event, func) => {\n        if (!func) {\n          if (!this.Event.listeners[event])\n            return;\n          this.Event.listeners[event].length = 0;\n          return;\n        }\n        remove(this.Event.listeners[event], func);\n      };\n      this.$emit = (event, context, inCache) => {\n        if (!this.Event.listeners[event])\n          return;\n        this.Event.listeners[event].forEach((func) => func(context, inCache));\n      };\n    }\n    lazyLoadHandler() {\n      const freeList = [];\n      this.listeners.forEach((listener) => {\n        if (!listener.el || !listener.el.parentNode) {\n          freeList.push(listener);\n        }\n        const catIn = listener.checkInView();\n        if (!catIn)\n          return;\n        listener.load();\n      });\n      freeList.forEach((item) => {\n        remove(this.listeners, item);\n        item.$destroy();\n      });\n    }\n    initIntersectionObserver() {\n      if (!hasIntersectionObserver) {\n        return;\n      }\n      this.observer = new IntersectionObserver(\n        this.observerHandler.bind(this),\n        this.options.observerOptions\n      );\n      if (this.listeners.length) {\n        this.listeners.forEach((listener) => {\n          this.observer.observe(listener.el);\n        });\n      }\n    }\n    observerHandler(entries) {\n      entries.forEach((entry) => {\n        if (entry.isIntersecting) {\n          this.listeners.forEach((listener) => {\n            if (listener.el === entry.target) {\n              if (listener.state.loaded)\n                return this.observer.unobserve(listener.el);\n              listener.load();\n            }\n          });\n        }\n      });\n    }\n    elRenderer(listener, state, cache) {\n      if (!listener.el)\n        return;\n      const { el, bindType } = listener;\n      let src;\n      switch (state) {\n        case \"loading\":\n          src = listener.loading;\n          break;\n        case \"error\":\n          src = listener.error;\n          break;\n        default:\n          ({ src } = listener);\n          break;\n      }\n      if (bindType) {\n        el.style[bindType] = 'url(\"' + src + '\")';\n      } else if (el.getAttribute(\"src\") !== src) {\n        el.setAttribute(\"src\", src);\n      }\n      el.setAttribute(\"lazy\", state);\n      this.$emit(state, listener, cache);\n      this.options.adapter[state] && this.options.adapter[state](listener, this.options);\n      if (this.options.dispatchEvent) {\n        const event = new CustomEvent(state, {\n          detail: listener\n        });\n        el.dispatchEvent(event);\n      }\n    }\n    valueFormatter(value) {\n      let src = value;\n      let { loading, error } = this.options;\n      if (isObject(value)) {\n        if (process.env.NODE_ENV !== \"production\" && !value.src && !this.options.silent) {\n          console.error(\"[@vant/lazyload] miss src with \" + value);\n        }\n        ({ src } = value);\n        loading = value.loading || this.options.loading;\n        error = value.error || this.options.error;\n      }\n      return {\n        src,\n        loading,\n        error\n      };\n    }\n  };\n}\nexport {\n  stdin_default as default\n};\n"], "mappings": ";;;;;;AAAA,SAASA,QAAQ,QAAQ,KAAK;AAC9B,SAASC,SAAS,EAAEC,eAAe,QAAQ,WAAW;AACtD,SACEC,MAAM,IAANA,OAAM,EACNC,EAAE,EACFC,GAAG,EACHC,QAAQ,EACRC,WAAW,EACXC,MAAM,EACNC,0BAA0B,EAC1BC,uBAAuB,EACvBC,QAAQ,EACRC,UAAU,QACL,YAAY;AACnB,SAASC,QAAQ,QAAQ,uBAAuB;AAChD,OAAOC,gBAAgB,MAAM,gBAAgB;AAC7C,IAAMC,WAAW,GAAG,gFAAgF;AACpG,IAAMC,cAAc,GAAG,CACrB,QAAQ,EACR,OAAO,EACP,YAAY,EACZ,QAAQ,EACR,cAAc,EACd,eAAe,EACf,WAAW,CACZ;AACD,IAAMC,wBAAwB,GAAG;EAC/BC,UAAU,EAAE,KAAK;EACjBC,SAAS,EAAE;AACb,CAAC;AACD,SAASC,aAAaA,CAAA,EAAG;EACvB;IACE,SAAAC,KAAAC,IAAA,EAeG;MAAA,IAdDC,OAAO,GAAAD,IAAA,CAAPC,OAAO;QACPC,KAAK,GAAAF,IAAA,CAALE,KAAK;QACLC,YAAY,GAAAH,IAAA,CAAZG,YAAY;QACZC,UAAU,GAAAJ,IAAA,CAAVI,UAAU;QACVC,aAAa,GAAAL,IAAA,CAAbK,aAAa;QACbC,OAAO,GAAAN,IAAA,CAAPM,OAAO;QACPC,OAAO,GAAAP,IAAA,CAAPO,OAAO;QAAAC,WAAA,GAAAR,IAAA,CACPS,MAAM;QAANA,MAAM,GAAAD,WAAA,cAAG,IAAI,GAAAA,WAAA;QACbE,KAAK,GAAAV,IAAA,CAALU,KAAK;QACLC,YAAY,GAAAX,IAAA,CAAZW,YAAY;QACZC,MAAM,GAAAZ,IAAA,CAANY,MAAM;QACNC,OAAO,GAAAb,IAAA,CAAPa,OAAO;QACPC,QAAQ,GAAAd,IAAA,CAARc,QAAQ;QACRC,eAAe,GAAAf,IAAA,CAAfe,eAAe;MAAAC,eAAA,OAAAjB,IAAA;MAEf,IAAI,CAACkB,IAAI,GAAG5B,QAAQ,CAAC6B,KAAK;MAC1B,IAAI,CAACC,SAAS,GAAG,EAAE;MACnB,IAAI,CAACC,WAAW,GAAG,CAAC;MACpB,IAAI,CAACC,OAAO,GAAG,EAAE;MACjB,IAAI,CAACC,OAAO,GAAG;QACbb,MAAM,EAANA,MAAM;QACNJ,aAAa,EAAE,CAAC,CAACA,aAAa;QAC9BF,YAAY,EAAEA,YAAY,IAAI,GAAG;QACjCF,OAAO,EAAEA,OAAO,IAAI,GAAG;QACvBG,UAAU,EAAEA,UAAU,IAAI,CAAC;QAC3BF,KAAK,EAAEA,KAAK,IAAIT,WAAW;QAC3Ba,OAAO,EAAEA,OAAO,IAAIb,WAAW;QAC/Bc,OAAO,EAAEA,OAAO,IAAI,CAAC;QACrBG,KAAK,EAAEA,KAAK,IAAIxB,MAAM,CAACwB,KAAK,CAAC;QAC7Ba,YAAY,EAAEZ,YAAY,IAAIjB,cAAc;QAC5CT,WAAW,EAAEA,WAAW,CAAC,CAAC;QAC1B2B,MAAM,EAAEA,MAAM,IAAI,CAAC,CAAC;QACpBC,OAAO,EAAEA,OAAO,IAAI,CAAC,CAAC;QACtBC,QAAQ,EAAE,CAAC,CAACA,QAAQ;QACpBC,eAAe,EAAEA,eAAe,IAAIpB;MACtC,CAAC;MACD,IAAI,CAAC6B,SAAS,CAAC,CAAC;MAChB,IAAI,CAACC,UAAU,GAAG,IAAInC,UAAU,CAAC;QAAEoC,GAAG,EAAE;MAAI,CAAC,CAAC;MAC9C,IAAI,CAACC,eAAe,GAAG3C,QAAQ,CAC7B,IAAI,CAAC2C,eAAe,CAACC,IAAI,CAAC,IAAI,CAAC,EAC/B,IAAI,CAACN,OAAO,CAACnB,YACf,CAAC;MACD,IAAI,CAAC0B,OAAO,CAAC,IAAI,CAACP,OAAO,CAACR,QAAQ,GAAGzB,QAAQ,CAACyB,QAAQ,GAAGzB,QAAQ,CAAC6B,KAAK,CAAC;IAC1E;IAACY,YAAA,CAAA/B,IAAA;MAAAgC,GAAA;MAAAC,KAAA,EACD,SAAAC,OAAA,EAAqB;QAAA,IAAdX,OAAO,GAAAY,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;QACjBG,MAAM,CAACC,MAAM,CAAC,IAAI,CAAChB,OAAO,EAAEA,OAAO,CAAC;MACtC;IAAC;MAAAS,GAAA;MAAAC,KAAA,EACD,SAAAO,YAAA,EAAc;QACZ,OAAO,IAAI,CAACpB,SAAS,CAACqB,GAAG,CAAC,UAACC,IAAI;UAAA,OAAKA,IAAI,CAACF,WAAW,CAAC,CAAC;QAAA,EAAC;MACzD;IAAC;MAAAR,GAAA;MAAAC,KAAA,EACD,SAAAU,WAAWC,EAAE,EAAE;QACb,IAAI,CAACxB,SAAS,CAACyB,IAAI,CAACD,EAAE,CAAC;QACvB,IAAIhE,SAAS,EAAE;UACb,IAAI,CAACkE,iBAAiB,CAACC,MAAM,CAAC;UAC9B,IAAI,CAAChC,QAAQ,IAAI,IAAI,CAACA,QAAQ,CAACiC,OAAO,CAACJ,EAAE,CAACK,EAAE,CAAC;UAC7C,IAAIL,EAAE,CAACM,GAAG,IAAIN,EAAE,CAACM,GAAG,CAACC,UAAU,EAAE;YAC/B,IAAI,CAACL,iBAAiB,CAACF,EAAE,CAACM,GAAG,CAACC,UAAU,CAAC;UAC3C;QACF;MACF;IAAC;MAAAnB,GAAA;MAAAC,KAAA,EACD,SAAAmB,IAAIH,EAAE,EAAEI,OAAO,EAAEC,KAAK,EAAE;QAAA,IAAAC,KAAA;QACtB,IAAI,IAAI,CAACnC,SAAS,CAACoC,IAAI,CAAC,UAACd,IAAI;UAAA,OAAKA,IAAI,CAACO,EAAE,KAAKA,EAAE;QAAA,EAAC,EAAE;UACjD,IAAI,CAACQ,MAAM,CAACR,EAAE,EAAEI,OAAO,CAAC;UACxB,OAAO1E,QAAQ,CAAC,IAAI,CAACiD,eAAe,CAAC;QACvC;QACA,IAAMK,KAAK,GAAG,IAAI,CAACyB,cAAc,CAACL,OAAO,CAACpB,KAAK,CAAC;QAChD,IAAM0B,GAAG,GAAK1B,KAAK,CAAb0B,GAAG;QACThF,QAAQ,CAAC,YAAM;UACbgF,GAAG,GAAGvE,0BAA0B,CAAC6D,EAAE,EAAEM,KAAI,CAAChC,OAAO,CAACZ,KAAK,CAAC,IAAIgD,GAAG;UAC/DJ,KAAI,CAACxC,QAAQ,IAAIwC,KAAI,CAACxC,QAAQ,CAACiC,OAAO,CAACC,EAAE,CAAC;UAC1C,IAAMW,SAAS,GAAGtB,MAAM,CAACuB,IAAI,CAACR,OAAO,CAACS,SAAS,CAAC,CAAC,CAAC,CAAC;UACnD,IAAIC,OAAO;UACX,IAAIH,SAAS,EAAE;YACbG,OAAO,GAAGT,KAAK,CAACU,OAAO,CAACC,KAAK,CAACL,SAAS,CAAC;YACxCG,OAAO,GAAGA,OAAO,GAAGA,OAAO,CAACb,GAAG,IAAIa,OAAO,GAAGG,QAAQ,CAACC,cAAc,CAACP,SAAS,CAAC;UACjF;UACA,IAAI,CAACG,OAAO,EAAE;YACZA,OAAO,GAAGlF,eAAe,CAACoE,EAAE,CAAC;UAC/B;UACA,IAAMmB,WAAW,GAAG,IAAI3E,gBAAgB,CAAC;YACvC4E,QAAQ,EAAEhB,OAAO,CAACiB,GAAG;YACrBP,OAAO,EAAPA,OAAO;YACPd,EAAE,EAAFA,EAAE;YACFU,GAAG,EAAHA,GAAG;YACHpD,OAAO,EAAE0B,KAAK,CAAC1B,OAAO;YACtBJ,KAAK,EAAE8B,KAAK,CAAC9B,KAAK;YAClBoE,IAAI,EAAEtC,KAAK,CAACsC,IAAI;YAChBC,UAAU,EAAEjB,KAAI,CAACiB,UAAU,CAAC3C,IAAI,CAAC0B,KAAI,CAAC;YACtChC,OAAO,EAAEgC,KAAI,CAAChC,OAAO;YACrBG,UAAU,EAAE6B,KAAI,CAAC7B;UACnB,CAAC,CAAC;UACF6B,KAAI,CAACnC,SAAS,CAACyB,IAAI,CAACuB,WAAW,CAAC;UAChC,IAAIxF,SAAS,EAAE;YACb2E,KAAI,CAACT,iBAAiB,CAACC,MAAM,CAAC;YAC9BQ,KAAI,CAACT,iBAAiB,CAACiB,OAAO,CAAC;UACjC;UACAR,KAAI,CAAC3B,eAAe,CAAC,CAAC;UACtBjD,QAAQ,CAAC;YAAA,OAAM4E,KAAI,CAAC3B,eAAe,CAAC,CAAC;UAAA,EAAC;QACxC,CAAC,CAAC;MACJ;IAAC;MAAAI,GAAA;MAAAC,KAAA,EACD,SAAAwB,OAAOR,EAAE,EAAEI,OAAO,EAAEC,KAAK,EAAE;QAAA,IAAAmB,MAAA;QACzB,IAAMxC,KAAK,GAAG,IAAI,CAACyB,cAAc,CAACL,OAAO,CAACpB,KAAK,CAAC;QAChD,IAAM0B,GAAG,GAAK1B,KAAK,CAAb0B,GAAG;QACTA,GAAG,GAAGvE,0BAA0B,CAAC6D,EAAE,EAAE,IAAI,CAAC1B,OAAO,CAACZ,KAAK,CAAC,IAAIgD,GAAG;QAC/D,IAAMe,KAAK,GAAG,IAAI,CAACtD,SAAS,CAACuD,IAAI,CAAC,UAACjC,IAAI;UAAA,OAAKA,IAAI,CAACO,EAAE,KAAKA,EAAE;QAAA,EAAC;QAC3D,IAAI,CAACyB,KAAK,EAAE;UACV,IAAI,CAACtB,GAAG,CAACH,EAAE,EAAEI,OAAO,EAAEC,KAAK,CAAC;QAC9B,CAAC,MAAM;UACLoB,KAAK,CAACjB,MAAM,CAAC;YACXE,GAAG,EAAHA,GAAG;YACHxD,KAAK,EAAE8B,KAAK,CAAC9B,KAAK;YAClBI,OAAO,EAAE0B,KAAK,CAAC1B;UACjB,CAAC,CAAC;QACJ;QACA,IAAI,IAAI,CAACQ,QAAQ,EAAE;UACjB,IAAI,CAACA,QAAQ,CAAC6D,SAAS,CAAC3B,EAAE,CAAC;UAC3B,IAAI,CAAClC,QAAQ,CAACiC,OAAO,CAACC,EAAE,CAAC;QAC3B;QACA,IAAI,CAACrB,eAAe,CAAC,CAAC;QACtBjD,QAAQ,CAAC;UAAA,OAAM8F,MAAI,CAAC7C,eAAe,CAAC,CAAC;QAAA,EAAC;MACxC;IAAC;MAAAI,GAAA;MAAAC,KAAA,EACD,SAAAnD,OAAOmE,EAAE,EAAE;QACT,IAAI,CAACA,EAAE,EACL;QACF,IAAI,CAAClC,QAAQ,IAAI,IAAI,CAACA,QAAQ,CAAC6D,SAAS,CAAC3B,EAAE,CAAC;QAC5C,IAAM4B,SAAS,GAAG,IAAI,CAACzD,SAAS,CAACuD,IAAI,CAAC,UAACjC,IAAI;UAAA,OAAKA,IAAI,CAACO,EAAE,KAAKA,EAAE;QAAA,EAAC;QAC/D,IAAI4B,SAAS,EAAE;UACb,IAAI,CAACC,oBAAoB,CAACD,SAAS,CAACd,OAAO,CAAC;UAC5C,IAAI,CAACe,oBAAoB,CAAC/B,MAAM,CAAC;UACjCjE,OAAM,CAAC,IAAI,CAACsC,SAAS,EAAEyD,SAAS,CAAC;UACjCA,SAAS,CAACE,QAAQ,CAAC,CAAC;QACtB;MACF;IAAC;MAAA/C,GAAA;MAAAC,KAAA,EACD,SAAA+C,gBAAgBpC,EAAE,EAAE;QAClB,IAAI,CAACA,EAAE,EACL;QACF9D,OAAM,CAAC,IAAI,CAACsC,SAAS,EAAEwB,EAAE,CAAC;QAC1B,IAAI,CAAC7B,QAAQ,IAAI,IAAI,CAACA,QAAQ,CAAC6D,SAAS,CAAChC,EAAE,CAACK,EAAE,CAAC;QAC/C,IAAIL,EAAE,CAACmB,OAAO,IAAInB,EAAE,CAACM,GAAG,CAACC,UAAU,EAAE;UACnC,IAAI,CAAC2B,oBAAoB,CAAClC,EAAE,CAACM,GAAG,CAACC,UAAU,CAAC;QAC9C;QACA,IAAI,CAAC2B,oBAAoB,CAAC/B,MAAM,CAAC;MACnC;IAAC;MAAAf,GAAA;MAAAC,KAAA,EACD,SAAAH,QAAQZ,IAAI,EAAE;QAAA,IAAA+D,MAAA;QACZ,IAAI,CAAC5F,uBAAuB,IAAI6B,IAAI,KAAK5B,QAAQ,CAACyB,QAAQ,EAAE;UAC1DG,IAAI,GAAG5B,QAAQ,CAAC6B,KAAK;QACvB;QACA,IAAI,CAACD,IAAI,GAAGA,IAAI;QAChB,IAAIA,IAAI,KAAK5B,QAAQ,CAAC6B,KAAK,EAAE;UAC3B,IAAI,IAAI,CAACJ,QAAQ,EAAE;YACjB,IAAI,CAACK,SAAS,CAAC8D,OAAO,CAAC,UAACC,QAAQ,EAAK;cACnCF,MAAI,CAAClE,QAAQ,CAAC6D,SAAS,CAACO,QAAQ,CAAClC,EAAE,CAAC;YACtC,CAAC,CAAC;YACF,IAAI,CAAClC,QAAQ,GAAG,IAAI;UACtB;UACA,IAAI,CAACO,OAAO,CAAC4D,OAAO,CAAC,UAACE,MAAM,EAAK;YAC/BH,MAAI,CAACI,UAAU,CAACD,MAAM,CAACnC,EAAE,EAAE,IAAI,CAAC;UAClC,CAAC,CAAC;QACJ,CAAC,MAAM;UACL,IAAI,CAAC3B,OAAO,CAAC4D,OAAO,CAAC,UAACE,MAAM,EAAK;YAC/BH,MAAI,CAACI,UAAU,CAACD,MAAM,CAACnC,EAAE,EAAE,KAAK,CAAC;UACnC,CAAC,CAAC;UACF,IAAI,CAACqC,wBAAwB,CAAC,CAAC;QACjC;MACF;IAAC;MAAAtD,GAAA;MAAAC,KAAA,EACD,SAAAa,kBAAkBG,EAAE,EAAE;QACpB,IAAI,CAACA,EAAE,EACL;QACF,IAAImC,MAAM,GAAG,IAAI,CAAC9D,OAAO,CAACqD,IAAI,CAAC,UAACY,OAAO;UAAA,OAAKA,OAAO,CAACtC,EAAE,KAAKA,EAAE;QAAA,EAAC;QAC9D,IAAI,CAACmC,MAAM,EAAE;UACXA,MAAM,GAAG;YACPnC,EAAE,EAAFA,EAAE;YACFuC,EAAE,EAAE,EAAE,IAAI,CAACnE,WAAW;YACtBoE,aAAa,EAAE,CAAC;YAChBC,QAAQ,EAAE;UACZ,CAAC;UACD,IAAI,CAACxE,IAAI,KAAK5B,QAAQ,CAAC6B,KAAK,IAAI,IAAI,CAACkE,UAAU,CAACD,MAAM,CAACnC,EAAE,EAAE,IAAI,CAAC;UAChE,IAAI,CAAC3B,OAAO,CAACuB,IAAI,CAACuC,MAAM,CAAC;QAC3B,CAAC,MAAM;UACLA,MAAM,CAACK,aAAa,EAAE;QACxB;QACA,OAAO,IAAI,CAACpE,WAAW;MACzB;IAAC;MAAAW,GAAA;MAAAC,KAAA,EACD,SAAA6C,qBAAqB7B,EAAE,EAAE;QAAA,IAAA0C,MAAA;QACvB,IAAI,CAACrE,OAAO,CAAC4D,OAAO,CAAC,UAACE,MAAM,EAAEQ,KAAK,EAAK;UACtC,IAAIR,MAAM,CAACnC,EAAE,KAAKA,EAAE,EAAE;YACpBmC,MAAM,CAACK,aAAa,EAAE;YACtB,IAAI,CAACL,MAAM,CAACK,aAAa,EAAE;cACzBE,MAAI,CAACN,UAAU,CAACD,MAAM,CAACnC,EAAE,EAAE,KAAK,CAAC;cACjC0C,MAAI,CAACrE,OAAO,CAACuE,MAAM,CAACD,KAAK,EAAE,CAAC,CAAC;cAC7BR,MAAM,GAAG,IAAI;YACf;UACF;QACF,CAAC,CAAC;MACJ;IAAC;MAAApD,GAAA;MAAAC,KAAA,EACD,SAAAoD,WAAWpC,EAAE,EAAE6C,KAAK,EAAE;QAAA,IAAAC,MAAA;QACpB,IAAI,CAACxE,OAAO,CAACC,YAAY,CAAC0D,OAAO,CAC/B,UAACc,GAAG;UAAA,OAAK,CAACF,KAAK,GAAG/G,EAAE,GAAGC,GAAG,EAAEiE,EAAE,EAAE+C,GAAG,EAAED,MAAI,CAACnE,eAAe,CAAC;QAAA,CAC5D,CAAC;MACH;IAAC;MAAAI,GAAA;MAAAC,KAAA,EACD,SAAAR,UAAA,EAAY;QAAA,IAAAwE,MAAA;QACV,IAAI,CAACC,KAAK,GAAG;UACX9E,SAAS,EAAE;YACTb,OAAO,EAAE,EAAE;YACX4F,MAAM,EAAE,EAAE;YACVhG,KAAK,EAAE;UACT;QACF,CAAC;QACD,IAAI,CAACiG,GAAG,GAAG,UAACjF,KAAK,EAAEkF,IAAI,EAAK;UAC1B,IAAI,CAACJ,MAAI,CAACC,KAAK,CAAC9E,SAAS,CAACD,KAAK,CAAC,EAC9B8E,MAAI,CAACC,KAAK,CAAC9E,SAAS,CAACD,KAAK,CAAC,GAAG,EAAE;UAClC8E,MAAI,CAACC,KAAK,CAAC9E,SAAS,CAACD,KAAK,CAAC,CAAC0B,IAAI,CAACwD,IAAI,CAAC;QACxC,CAAC;QACD,IAAI,CAACC,KAAK,GAAG,UAACnF,KAAK,EAAEkF,IAAI,EAAK;UAC5B,IAAME,GAAG,GAAG,SAANA,GAAGA,CAAA,EAAgB;YACvBN,MAAI,CAACO,IAAI,CAACrF,KAAK,EAAEoF,GAAG,CAAC;YAAC,SAAAE,IAAA,GAAAtE,SAAA,CAAAC,MAAA,EADRsE,IAAI,OAAAC,KAAA,CAAAF,IAAA,GAAAG,IAAA,MAAAA,IAAA,GAAAH,IAAA,EAAAG,IAAA;cAAJF,IAAI,CAAAE,IAAA,IAAAzE,SAAA,CAAAyE,IAAA;YAAA;YAElBP,IAAI,CAACQ,KAAK,CAACZ,MAAI,EAAES,IAAI,CAAC;UACxB,CAAC;UACDT,MAAI,CAACG,GAAG,CAACjF,KAAK,EAAEoF,GAAG,CAAC;QACtB,CAAC;QACD,IAAI,CAACC,IAAI,GAAG,UAACrF,KAAK,EAAEkF,IAAI,EAAK;UAC3B,IAAI,CAACA,IAAI,EAAE;YACT,IAAI,CAACJ,MAAI,CAACC,KAAK,CAAC9E,SAAS,CAACD,KAAK,CAAC,EAC9B;YACF8E,MAAI,CAACC,KAAK,CAAC9E,SAAS,CAACD,KAAK,CAAC,CAACiB,MAAM,GAAG,CAAC;YACtC;UACF;UACAtD,OAAM,CAACmH,MAAI,CAACC,KAAK,CAAC9E,SAAS,CAACD,KAAK,CAAC,EAAEkF,IAAI,CAAC;QAC3C,CAAC;QACD,IAAI,CAACS,KAAK,GAAG,UAAC3F,KAAK,EAAE6C,OAAO,EAAE+C,OAAO,EAAK;UACxC,IAAI,CAACd,MAAI,CAACC,KAAK,CAAC9E,SAAS,CAACD,KAAK,CAAC,EAC9B;UACF8E,MAAI,CAACC,KAAK,CAAC9E,SAAS,CAACD,KAAK,CAAC,CAAC+D,OAAO,CAAC,UAACmB,IAAI;YAAA,OAAKA,IAAI,CAACrC,OAAO,EAAE+C,OAAO,CAAC;UAAA,EAAC;QACvE,CAAC;MACH;IAAC;MAAA/E,GAAA;MAAAC,KAAA,EACD,SAAAL,gBAAA,EAAkB;QAAA,IAAAoF,MAAA;QAChB,IAAMC,QAAQ,GAAG,EAAE;QACnB,IAAI,CAAC7F,SAAS,CAAC8D,OAAO,CAAC,UAACC,QAAQ,EAAK;UACnC,IAAI,CAACA,QAAQ,CAAClC,EAAE,IAAI,CAACkC,QAAQ,CAAClC,EAAE,CAACE,UAAU,EAAE;YAC3C8D,QAAQ,CAACpE,IAAI,CAACsC,QAAQ,CAAC;UACzB;UACA,IAAM+B,KAAK,GAAG/B,QAAQ,CAACgC,WAAW,CAAC,CAAC;UACpC,IAAI,CAACD,KAAK,EACR;UACF/B,QAAQ,CAACiC,IAAI,CAAC,CAAC;QACjB,CAAC,CAAC;QACFH,QAAQ,CAAC/B,OAAO,CAAC,UAACxC,IAAI,EAAK;UACzB5D,OAAM,CAACkI,MAAI,CAAC5F,SAAS,EAAEsB,IAAI,CAAC;UAC5BA,IAAI,CAACqC,QAAQ,CAAC,CAAC;QACjB,CAAC,CAAC;MACJ;IAAC;MAAA/C,GAAA;MAAAC,KAAA,EACD,SAAAqD,yBAAA,EAA2B;QAAA,IAAA+B,MAAA;QACzB,IAAI,CAAChI,uBAAuB,EAAE;UAC5B;QACF;QACA,IAAI,CAAC0B,QAAQ,GAAG,IAAIuG,oBAAoB,CACtC,IAAI,CAACC,eAAe,CAAC1F,IAAI,CAAC,IAAI,CAAC,EAC/B,IAAI,CAACN,OAAO,CAACP,eACf,CAAC;QACD,IAAI,IAAI,CAACI,SAAS,CAACgB,MAAM,EAAE;UACzB,IAAI,CAAChB,SAAS,CAAC8D,OAAO,CAAC,UAACC,QAAQ,EAAK;YACnCkC,MAAI,CAACtG,QAAQ,CAACiC,OAAO,CAACmC,QAAQ,CAAClC,EAAE,CAAC;UACpC,CAAC,CAAC;QACJ;MACF;IAAC;MAAAjB,GAAA;MAAAC,KAAA,EACD,SAAAsF,gBAAgBC,OAAO,EAAE;QAAA,IAAAC,MAAA;QACvBD,OAAO,CAACtC,OAAO,CAAC,UAACwC,KAAK,EAAK;UACzB,IAAIA,KAAK,CAACC,cAAc,EAAE;YACxBF,MAAI,CAACrG,SAAS,CAAC8D,OAAO,CAAC,UAACC,QAAQ,EAAK;cACnC,IAAIA,QAAQ,CAAClC,EAAE,KAAKyE,KAAK,CAACtC,MAAM,EAAE;gBAChC,IAAID,QAAQ,CAACyC,KAAK,CAACzB,MAAM,EACvB,OAAOsB,MAAI,CAAC1G,QAAQ,CAAC6D,SAAS,CAACO,QAAQ,CAAClC,EAAE,CAAC;gBAC7CkC,QAAQ,CAACiC,IAAI,CAAC,CAAC;cACjB;YACF,CAAC,CAAC;UACJ;QACF,CAAC,CAAC;MACJ;IAAC;MAAApF,GAAA;MAAAC,KAAA,EACD,SAAAuC,WAAWW,QAAQ,EAAEyC,KAAK,EAAEC,KAAK,EAAE;QACjC,IAAI,CAAC1C,QAAQ,CAAClC,EAAE,EACd;QACF,IAAQA,EAAE,GAAekC,QAAQ,CAAzBlC,EAAE;UAAEoB,QAAQ,GAAKc,QAAQ,CAArBd,QAAQ;QACpB,IAAIV,GAAG;QACP,QAAQiE,KAAK;UACX,KAAK,SAAS;YACZjE,GAAG,GAAGwB,QAAQ,CAAC5E,OAAO;YACtB;UACF,KAAK,OAAO;YACVoD,GAAG,GAAGwB,QAAQ,CAAChF,KAAK;YACpB;UACF;YACKwD,GAAG,GAAKwB,QAAQ,CAAhBxB,GAAG;YACN;QACJ;QACA,IAAIU,QAAQ,EAAE;UACZpB,EAAE,CAAC6E,KAAK,CAACzD,QAAQ,CAAC,GAAG,OAAO,GAAGV,GAAG,GAAG,IAAI;QAC3C,CAAC,MAAM,IAAIV,EAAE,CAAC8E,YAAY,CAAC,KAAK,CAAC,KAAKpE,GAAG,EAAE;UACzCV,EAAE,CAAC+E,YAAY,CAAC,KAAK,EAAErE,GAAG,CAAC;QAC7B;QACAV,EAAE,CAAC+E,YAAY,CAAC,MAAM,EAAEJ,KAAK,CAAC;QAC9B,IAAI,CAACd,KAAK,CAACc,KAAK,EAAEzC,QAAQ,EAAE0C,KAAK,CAAC;QAClC,IAAI,CAACtG,OAAO,CAACT,OAAO,CAAC8G,KAAK,CAAC,IAAI,IAAI,CAACrG,OAAO,CAACT,OAAO,CAAC8G,KAAK,CAAC,CAACzC,QAAQ,EAAE,IAAI,CAAC5D,OAAO,CAAC;QAClF,IAAI,IAAI,CAACA,OAAO,CAACjB,aAAa,EAAE;UAC9B,IAAMa,KAAK,GAAG,IAAI8G,WAAW,CAACL,KAAK,EAAE;YACnCM,MAAM,EAAE/C;UACV,CAAC,CAAC;UACFlC,EAAE,CAAC3C,aAAa,CAACa,KAAK,CAAC;QACzB;MACF;IAAC;MAAAa,GAAA;MAAAC,KAAA,EACD,SAAAyB,eAAezB,KAAK,EAAE;QACpB,IAAI0B,GAAG,GAAG1B,KAAK;QACf,IAAAkG,aAAA,GAAyB,IAAI,CAAC5G,OAAO;UAA/BhB,OAAO,GAAA4H,aAAA,CAAP5H,OAAO;UAAEJ,KAAK,GAAAgI,aAAA,CAALhI,KAAK;QACpB,IAAIX,QAAQ,CAACyC,KAAK,CAAC,EAAE;UACnB,IAAImG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAI,CAACrG,KAAK,CAAC0B,GAAG,IAAI,CAAC,IAAI,CAACpC,OAAO,CAACb,MAAM,EAAE;YAC/E6H,OAAO,CAACpI,KAAK,CAAC,iCAAiC,GAAG8B,KAAK,CAAC;UAC1D;UACG0B,GAAG,GAAK1B,KAAK,CAAb0B,GAAG;UACNpD,OAAO,GAAG0B,KAAK,CAAC1B,OAAO,IAAI,IAAI,CAACgB,OAAO,CAAChB,OAAO;UAC/CJ,KAAK,GAAG8B,KAAK,CAAC9B,KAAK,IAAI,IAAI,CAACoB,OAAO,CAACpB,KAAK;QAC3C;QACA,OAAO;UACLwD,GAAG,EAAHA,GAAG;UACHpD,OAAO,EAAPA,OAAO;UACPJ,KAAK,EAALA;QACF,CAAC;MACH;IAAC;IAAA,OAAAH,IAAA;EAAA;AAEL;AACA,SACED,aAAa,IAAIyI,OAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}