{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _Checkbox from \"./Checkbox.mjs\";\nvar Checkbox = withInstall(_Checkbox);\nvar stdin_default = Checkbox;\nexport { Checkbox, stdin_default as default };", "map": {"version": 3, "names": ["withInstall", "_Checkbox", "Checkbox", "stdin_default", "default"], "sources": ["C:/Users/<USER>/Desktop/vue3_3.0/node_modules/vant/es/checkbox/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _Checkbox from \"./Checkbox.mjs\";\nconst Checkbox = withInstall(_Checkbox);\nvar stdin_default = Checkbox;\nexport {\n  Checkbox,\n  stdin_default as default\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,SAAS,MAAM,gBAAgB;AACtC,IAAMC,QAAQ,GAAGF,WAAW,CAACC,SAAS,CAAC;AACvC,IAAIE,aAAa,GAAGD,QAAQ;AAC5B,SACEA,QAAQ,EACRC,aAAa,IAAIC,OAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}