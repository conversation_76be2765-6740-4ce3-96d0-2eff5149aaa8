{"ast": null, "code": "function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nfunction _iterableToArrayLimit(arr, i) { var _i = null == arr ? null : \"undefined\" != typeof Symbol && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (null != _i) { var _s, _e, _x, _r, _arr = [], _n = !0, _d = !1; try { if (_x = (_i = _i.call(arr)).next, 0 === i) { if (Object(_i) !== _i) return; _n = !1; } else for (; !(_n = (_s = _x.call(_i)).done) && (_arr.push(_s.value), _arr.length !== i); _n = !0); } catch (err) { _d = !0, _e = err; } finally { try { if (!_n && null != _i.return && (_r = _i.return(), Object(_r) !== _r)) return; } finally { if (_d) throw _e; } } return _arr; } }\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\nimport { createVNode as _createVNode } from \"vue\";\nimport { watch, reactive, defineComponent } from \"vue\";\nimport { isMobile, createNamespace, extend } from \"../utils/index.mjs\";\nimport { Cell } from \"../cell/index.mjs\";\nimport { Form } from \"../form/index.mjs\";\nimport { Field } from \"../field/index.mjs\";\nimport { Button } from \"../button/index.mjs\";\nimport { Switch } from \"../switch/index.mjs\";\nvar _createNamespace = createNamespace(\"contact-edit\"),\n  _createNamespace2 = _slicedToArray(_createNamespace, 3),\n  name = _createNamespace2[0],\n  bem = _createNamespace2[1],\n  t = _createNamespace2[2];\nvar DEFAULT_CONTACT = {\n  tel: \"\",\n  name: \"\"\n};\nvar contactEditProps = {\n  isEdit: Boolean,\n  isSaving: Boolean,\n  isDeleting: Boolean,\n  showSetDefault: Boolean,\n  setDefaultLabel: String,\n  contactInfo: {\n    type: Object,\n    default: function _default() {\n      return extend({}, DEFAULT_CONTACT);\n    }\n  },\n  telValidator: {\n    type: Function,\n    default: isMobile\n  }\n};\nvar stdin_default = defineComponent({\n  name: name,\n  props: contactEditProps,\n  emits: [\"save\", \"delete\", \"change-default\"],\n  setup: function setup(props, _ref) {\n    var emit = _ref.emit;\n    var contact = reactive(extend({}, DEFAULT_CONTACT, props.contactInfo));\n    var onSave = function onSave() {\n      if (!props.isSaving) {\n        emit(\"save\", contact);\n      }\n    };\n    var onDelete = function onDelete() {\n      return emit(\"delete\", contact);\n    };\n    var renderButtons = function renderButtons() {\n      return _createVNode(\"div\", {\n        \"class\": bem(\"buttons\")\n      }, [_createVNode(Button, {\n        \"block\": true,\n        \"round\": true,\n        \"type\": \"danger\",\n        \"text\": t(\"save\"),\n        \"class\": bem(\"button\"),\n        \"loading\": props.isSaving,\n        \"nativeType\": \"submit\"\n      }, null), props.isEdit && _createVNode(Button, {\n        \"block\": true,\n        \"round\": true,\n        \"text\": t(\"delete\"),\n        \"class\": bem(\"button\"),\n        \"loading\": props.isDeleting,\n        \"onClick\": onDelete\n      }, null)]);\n    };\n    var renderSwitch = function renderSwitch() {\n      return _createVNode(Switch, {\n        \"modelValue\": contact.isDefault,\n        \"onUpdate:modelValue\": function onUpdateModelValue($event) {\n          return contact.isDefault = $event;\n        },\n        \"size\": 24,\n        \"onChange\": function onChange(checked) {\n          return emit(\"change-default\", checked);\n        }\n      }, null);\n    };\n    var renderSetDefault = function renderSetDefault() {\n      if (props.showSetDefault) {\n        return _createVNode(Cell, {\n          \"title\": props.setDefaultLabel,\n          \"class\": bem(\"switch-cell\"),\n          \"border\": false\n        }, {\n          \"right-icon\": renderSwitch\n        });\n      }\n    };\n    watch(function () {\n      return props.contactInfo;\n    }, function (value) {\n      return extend(contact, DEFAULT_CONTACT, value);\n    });\n    return function () {\n      return _createVNode(Form, {\n        \"class\": bem(),\n        \"onSubmit\": onSave\n      }, {\n        default: function _default() {\n          return [_createVNode(\"div\", {\n            \"class\": bem(\"fields\")\n          }, [_createVNode(Field, {\n            \"modelValue\": contact.name,\n            \"onUpdate:modelValue\": function onUpdateModelValue($event) {\n              return contact.name = $event;\n            },\n            \"clearable\": true,\n            \"label\": t(\"name\"),\n            \"rules\": [{\n              required: true,\n              message: t(\"nameEmpty\")\n            }],\n            \"maxlength\": \"30\",\n            \"placeholder\": t(\"name\")\n          }, null), _createVNode(Field, {\n            \"modelValue\": contact.tel,\n            \"onUpdate:modelValue\": function onUpdateModelValue($event) {\n              return contact.tel = $event;\n            },\n            \"clearable\": true,\n            \"type\": \"tel\",\n            \"label\": t(\"tel\"),\n            \"rules\": [{\n              validator: props.telValidator,\n              message: t(\"telInvalid\")\n            }],\n            \"placeholder\": t(\"tel\")\n          }, null)]), renderSetDefault(), renderButtons()];\n        }\n      });\n    };\n  }\n});\nexport { stdin_default as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}