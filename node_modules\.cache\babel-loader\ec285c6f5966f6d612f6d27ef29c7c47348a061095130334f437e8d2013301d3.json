{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _Form from \"./Form.mjs\";\nvar Form = withInstall(_Form);\nvar stdin_default = Form;\nexport { Form, stdin_default as default };", "map": {"version": 3, "names": ["withInstall", "_Form", "Form", "stdin_default", "default"], "sources": ["C:/Users/<USER>/Desktop/vue3_3.0/node_modules/vant/es/form/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _Form from \"./Form.mjs\";\nconst Form = withInstall(_Form);\nvar stdin_default = Form;\nexport {\n  Form,\n  stdin_default as default\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,KAAK,MAAM,YAAY;AAC9B,IAAMC,IAAI,GAAGF,WAAW,CAACC,KAAK,CAAC;AAC/B,IAAIE,aAAa,GAAGD,IAAI;AACxB,SACEA,IAAI,EACJC,aAAa,IAAIC,OAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}