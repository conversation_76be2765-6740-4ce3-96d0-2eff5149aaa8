{"ast": null, "code": "import { useRect, useWindowSize } from \"@vant/use\";\nimport { unref } from \"vue\";\nimport { isIOS as checkIsIOS } from \"./validate.mjs\";\nfunction getScrollTop(el) {\n  var top = \"scrollTop\" in el ? el.scrollTop : el.pageYOffset;\n  return Math.max(top, 0);\n}\nfunction setScrollTop(el, value) {\n  if (\"scrollTop\" in el) {\n    el.scrollTop = value;\n  } else {\n    el.scrollTo(el.scrollX, value);\n  }\n}\nfunction getRootScrollTop() {\n  return window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop || 0;\n}\nfunction setRootScrollTop(value) {\n  setScrollTop(window, value);\n  setScrollTop(document.body, value);\n}\nfunction getElementTop(el, scroller) {\n  if (el === window) {\n    return 0;\n  }\n  var scrollTop = scroller ? getScrollTop(scroller) : getRootScrollTop();\n  return useRect(el).top + scrollTop;\n}\nvar isIOS = checkIsIOS();\nfunction resetScroll() {\n  if (isIOS) {\n    setRootScrollTop(getRootScrollTop());\n  }\n}\nvar stopPropagation = function stopPropagation(event) {\n  return event.stopPropagation();\n};\nfunction preventDefault(event, isStopPropagation) {\n  if (typeof event.cancelable !== \"boolean\" || event.cancelable) {\n    event.preventDefault();\n  }\n  if (isStopPropagation) {\n    stopPropagation(event);\n  }\n}\nfunction isHidden(elementRef) {\n  var el = unref(elementRef);\n  if (!el) {\n    return false;\n  }\n  var style = window.getComputedStyle(el);\n  var hidden = style.display === \"none\";\n  var parentHidden = el.offsetParent === null && style.position !== \"fixed\";\n  return hidden || parentHidden;\n}\nvar _useWindowSize = useWindowSize(),\n  windowWidth = _useWindowSize.width,\n  windowHeight = _useWindowSize.height;\nexport { getElementTop, getRootScrollTop, getScrollTop, isHidden, preventDefault, resetScroll, setRootScrollTop, setScrollTop, stopPropagation, windowHeight, windowWidth };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}