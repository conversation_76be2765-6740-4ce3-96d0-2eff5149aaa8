{"ast": null, "code": "import { renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, resolveComponent as _resolveComponent, normalizeClass as _normalizeClass, createVNode as _createVNode } from \"vue\";\nvar _hoisted_1 = {\n  class: \"footer\",\n  style: {\n    \"font-size\": \"2rem\",\n    \"display\": \"flex\"\n  }\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_van_icon = _resolveComponent(\"van-icon\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [(_openBlock(), _createElementBlock(_Fragment, null, _renderList(5, function (v) {\n    return _createVNode(_component_van_icon, {\n      style: {\n        \"color\": \"red\"\n      },\n      class: _normalizeClass('a' + v),\n      name: $props.num >= v ? 'star' : 'star-o',\n      onClick: function onClick($event) {\n        return $options.qh(v);\n      }\n    }, null, 8, [\"class\", \"name\", \"onClick\"]);\n  }), 64))]);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}