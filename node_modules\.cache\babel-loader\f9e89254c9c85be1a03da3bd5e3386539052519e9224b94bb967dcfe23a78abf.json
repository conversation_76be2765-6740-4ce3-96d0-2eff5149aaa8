{"ast": null, "code": "function _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return _typeof(key) === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (_typeof(input) !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (_typeof(res) !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\nimport { ref, getCurrentInstance } from 'vue';\nimport { get_invite } from '@/api/self/index';\nimport { useRouter } from 'vue-router';\nimport { useI18n } from 'vue-i18n';\nimport store from '@/store/index';\nimport { formatTime } from '@/api/format.js';\nimport QRCode from 'qrcodejs2';\n// import {getdetailbyid} from '@/api/home/<USER>'\n// 复制函数\nimport useClipboard from 'vue-clipboard3';\nvar url = '';\nexport default {\n  name: 'ShareInvite',\n  setup: function setup(props, ctx) {\n    var _getCurrentInstance = getCurrentInstance(),\n      proxy = _getCurrentInstance.proxy;\n    var _useI18n = useI18n(),\n      t = _useI18n.t;\n    var _useClipboard = useClipboard(),\n      toClipboard = _useClipboard.toClipboard;\n    var _useRouter = useRouter(),\n      push = _useRouter.push;\n    var a_content = ref('');\n    var info = ref({});\n    ctx.emit('hideFooter', true);\n    var clickLeft = function clickLeft() {\n      push('/self');\n    };\n    get_invite().then(function (res) {\n      var _res$data;\n      info.value = _objectSpread({}, res.data);\n      console.log(info, 111111);\n      a_content.value = (_res$data = res.data) === null || _res$data === void 0 ? void 0 : _res$data.invite_msg;\n\n      // 确保URL包含完整网址\n      var baseUrl = \"https://admin.tk-mall.cc\"; // 使用固定域名\n      if (info.value.url && !info.value.url.startsWith('http')) {\n        info.value.url = baseUrl + '/register?invite_code=' + info.value.invite_code;\n      }\n      url = info.value.url;\n    });\n    // getdetailbyid(1).then(res => {\n    // })\n    var copy = function copy(value) {\n      try {\n        // 确保复制的链接包含完整网址\n        var baseUrl = \"https://admin.tk-mall.cc\"; // 使用固定域名\n        var copyValue = value;\n        if (value && !value.startsWith('http')) {\n          copyValue = baseUrl + '/register?invite_code=' + info.value.invite_code;\n        }\n        toClipboard(copyValue);\n        proxy.$Message({\n          type: 'success',\n          message: t('msg.copy_s')\n        });\n      } catch (e) {\n        proxy.$Message({\n          type: 'error',\n          message: t('msg.copy_b')\n        });\n      }\n    };\n\n    // new QRCode(this.$refs.qrCodeUrl, {\n    //     text: '#qrcode', // 需要转换为二维码的内容\n    //     width: 100,\n    //     height: 100,\n    //     colorDark: '#000000',\n    //     colorLight: '#ffffff',\n    //     correctLevel: QRCode.CorrectLevel.H\n    // })\n\n    return {\n      clickLeft: clickLeft,\n      a_content: a_content,\n      copy: copy,\n      info: info\n    };\n  },\n  mounted: function mounted() {\n    var taht = this;\n    var dsq = setInterval(function () {\n      if (url != '') {\n        // 确保二维码链接包含完整网址\n        var qrUrl = url;\n        if (!qrUrl.startsWith('http')) {\n          var baseUrl = \"https://admin.tk-mall.cc\"; // 使用固定域名\n          qrUrl = baseUrl + '/register?invite_code=' + taht.info.invite_code;\n        }\n        new QRCode(taht.$refs.qrCodeUrl, {\n          text: qrUrl,\n          // 二维码的内容\n          width: 150,\n          height: 150,\n          colorDark: '#000',\n          colorLight: '#fff',\n          correctLevel: QRCode.CorrectLevel.H\n        });\n        clearInterval(dsq);\n      }\n    }, 500);\n  }\n};", "map": {"version": 3, "names": ["ref", "getCurrentInstance", "get_invite", "useRouter", "useI18n", "store", "formatTime", "QRCode", "useClipboard", "url", "name", "setup", "props", "ctx", "_getCurrentInstance", "proxy", "_useI18n", "t", "_useClipboard", "toClipboard", "_useRouter", "push", "a_content", "info", "emit", "clickLeft", "then", "res", "_res$data", "value", "_objectSpread", "data", "console", "log", "invite_msg", "baseUrl", "startsWith", "invite_code", "copy", "copyValue", "$Message", "type", "message", "e", "mounted", "taht", "dsq", "setInterval", "qrUrl", "$refs", "qrCodeUrl", "text", "width", "height", "colorDark", "colorLight", "correctLevel", "CorrectLevel", "H", "clearInterval"], "sources": ["C:\\Users\\<USER>\\Downloads\\Telegram Desktop\\vue3_3.0\\vue3_3.0\\src\\views\\self\\components\\share.vue"], "sourcesContent": ["<template>\r\n    <div class=\"tel home\">\r\n\t\t<van-nav-bar\r\n\t\t     :title=\"$t('msg.code')\"\r\n\t\t\t background=\"#ffffff\"\r\n\t\t\t title-style=\"color:black; font-size: 16px;\"\r\n\t\t\t left-arrow \r\n\t\t\t @click-left=\"$router.go(-1)\"\r\n\t\t>\r\n\t\t</van-nav-bar>\r\n\t\t<div class=\"box\">\r\n\t\t\t<div class=\"box_t\">\r\n\t\t\t\t<div class=\"box_tlt\">\r\n\t\t\t\t\t{{$t('msg.code')}} :&nbsp;<span>{{info.invite_code}}</span><img  :src=\"require('@/assets/images/copy.svg')\" alt=\"\" @click=\"copy(info?.url)\">\r\n                    \r\n\t\t\t\t</div>\r\n\t\t\t\t<div class=\"box_fot\">\r\n\t\t\t\t\t<div class=\"box_text\">{{$t('msg.yqhylqyj')}}</div>\r\n\t\t\t\t\t<div class=\"qr-code qr\" ref=\"qrCodeUrl\"></div>\r\n\t\t\t\t</div>\r\n\r\n \r\n\r\n\t\t\t\t<div class=\"cop\" @click=\"copy(info?.url)\">\r\n\t\t\t\t\t{{$t('msg.fzyqlj')}}\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\t\t\r\n\t\t\r\n\t\t\r\n\t\t\r\n\t\t\r\n\t\t\r\n\t\t<!--  \r\n        <div class=\"yqhy\">{{$t('msg.yqhylqyj')}}</div>\r\n        <div class=\"content\">\r\n            <div class=\"top\">\r\n                <div class=\"title\">\r\n                    <img :src=\"require('@/assets/images/news/money.png')\" alt=\"\" class=\"img\" width=\"20\">\r\n                    <img :src=\"require('@/assets/images/news/money.png')\" alt=\"\" class=\"img\" width=\"20\">\r\n                    <img :src=\"require('@/assets/images/news/money.png')\" alt=\"\" class=\"img\" width=\"20\">\r\n                </div>\r\n                <div class=\"c\" v-html=\"a_content\"></div>\r\n                <div class=\"b\">\r\n                    {{$t('msg.tgm')}} <span class=\"span\">{{info.invite_code}}</span>\r\n                </div>\r\n                <div class=\"bottom\" @click=\"copy(info?.url)\">{{$t('msg.fzyqlj')}}</div>\r\n            </div>\r\n        </div>\r\n\t\t-->\r\n    </div> \r\n</template>\r\n<script>\r\nimport { ref, getCurrentInstance } from 'vue';\r\nimport { get_invite } from '@/api/self/index'\r\nimport { useRouter } from 'vue-router';\r\nimport { useI18n } from 'vue-i18n'\r\nimport store from '@/store/index'\r\nimport {formatTime} from '@/api/format.js'\r\n\r\nimport QRCode from 'qrcodejs2';\r\n// import {getdetailbyid} from '@/api/home/<USER>'\r\n// 复制函数\r\nimport useClipboard from 'vue-clipboard3';\r\nvar url = '';\r\nexport default {\r\n    name: 'ShareInvite',\r\n    setup(props, ctx){\r\n        const {proxy} = getCurrentInstance()\r\n        const { t } = useI18n()\r\n        const { toClipboard } = useClipboard();\r\n        const { push } = useRouter();\r\n        const a_content = ref('')\r\n        const info = ref({})\r\n        ctx.emit('hideFooter',true)\r\n        const clickLeft = () => {\r\n            push('/self')\r\n        }\r\n        get_invite().then(res => {\r\n           \r\n            info.value = {...res.data}\r\n            console.log(info,111111)\r\n            a_content.value = res.data?.invite_msg\r\n            \r\n            // 确保URL包含完整网址\r\n            const baseUrl = \"https://admin.tk-mall.cc\"; // 使用固定域名\r\n            if (info.value.url && !info.value.url.startsWith('http')) {\r\n                info.value.url = baseUrl + '/register?invite_code=' + info.value.invite_code;\r\n            }\r\n            url = info.value.url;\r\n        })\r\n        // getdetailbyid(1).then(res => {\r\n        // })\r\n        const copy = (value) => {\r\n            try {\r\n                // 确保复制的链接包含完整网址\r\n                const baseUrl = \"https://admin.tk-mall.cc\"; // 使用固定域名\r\n                let copyValue = value;\r\n                if (value && !value.startsWith('http')) {\r\n                    copyValue = baseUrl + '/register?invite_code=' + info.value.invite_code;\r\n                }\r\n                toClipboard(copyValue);\r\n                proxy.$Message({ type: 'success', message:t('msg.copy_s')});\r\n            } catch (e) {\r\n                proxy.$Message({ type: 'error', message:t('msg.copy_b')});\r\n            }\r\n        }\r\n\r\n        // new QRCode(this.$refs.qrCodeUrl, {\r\n        //     text: '#qrcode', // 需要转换为二维码的内容\r\n        //     width: 100,\r\n        //     height: 100,\r\n        //     colorDark: '#000000',\r\n        //     colorLight: '#ffffff',\r\n        //     correctLevel: QRCode.CorrectLevel.H\r\n        // })\r\n\r\n        \r\n\r\n        return {clickLeft,a_content,copy,info}\r\n    },\r\n\r\n    mounted() {\r\n        let taht = this\r\n        let dsq = setInterval(function(){\r\n            if(url !=''){\r\n                // 确保二维码链接包含完整网址\r\n                let qrUrl = url;\r\n                if (!qrUrl.startsWith('http')) {\r\n                    const baseUrl = \"https://admin.tk-mall.cc\"; // 使用固定域名\r\n                    qrUrl = baseUrl + '/register?invite_code=' + taht.info.invite_code;\r\n                }\r\n                \r\n                new QRCode(taht.$refs.qrCodeUrl, {\r\n                    text: qrUrl, // 二维码的内容\r\n                    width: 150,\r\n                    height: 150,\r\n                    colorDark: '#000',\r\n                    colorLight: '#fff',\r\n                    correctLevel: QRCode.CorrectLevel.H\r\n                });\r\n                clearInterval(dsq)\r\n            }\r\n        },500)\r\n        \r\n        \r\n    }\r\n\r\n}\r\n\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\r\n.qr img{\r\n    width: 100px !important;\r\n    height: 100px !important;\r\n}\r\n\t.box_tlt{\r\n\t\theight: 110px !important;\r\n\t\tpadding-left: 30px;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: left;\r\n\t\tcolor: #ffffff;\r\n\t\timg{\r\n\t\t\twidth: 40px;\r\n\t\t\theight: 40px;\r\n\t\t\tmargin-left: 30px;\r\n\t\t}\r\n\t}\r\n.qr{\r\n\tmargin: 25px 0;\r\n\twidth: 400px;\r\n\theight: 400px;\r\n\tbackground-image: url(\"~@/assets/images/home/<USER>\");\r\n\tbackground-size: cover;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n}\r\n.box_text{\r\n\tdisplay: block;\r\n\tfont-size: 25px;\r\n\tfont-weight: 800;\r\n\tpadding: 50px 20px;\r\n}\r\n.cop{\r\n\tmargin: 0 0 15px;\r\n\t    width: 70%;\r\n\t    height: 70px;\r\n\t    background-color: #000;\r\n\t    color: #fff;\r\n\t    border-radius: 20px;\r\n\t    display: flex;\r\n\t    align-items: center;\r\n\t    justify-content: center;\r\n\t\tmargin: 50px auto;\r\n}\r\n.box{\r\n\tdisplay: block;\r\n\tbox-sizing: border-box;\r\n\twidth: 100%;\r\n\tpadding: 15px;\r\n\t.box_t{\r\n\t\twidth: 88%;\r\n\t\theight: auto;\r\n\t\tbackground-color: #fff;\r\n\t\tborder-radius: 25px;\r\n\t\toverflow: hidden;\r\n\t\tbox-shadow: rgba(41,5,5,.15) 0 0 20px;\r\n\t\toverflow: hidden;\r\n\t\tmargin:  0 auto;\r\n\t\t.box_tlt{\r\n\t\t\twidth: 100%;\r\n\t\t\theight: 60px;\r\n\t\t\tbackground-color: #000;\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tcolor: #fff;\r\n\t\t}\r\n\t\t.box_fot{\r\n\t\t\tpadding: 0 20px;\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: space-around;\r\n\t\t\tflex-direction: column;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.tel{\r\n    overflow: hidden;\r\n    // background-image: url(\"~@/assets/images/home/<USER>\");\r\n    // background-size: 100% 100%;\r\n    position: relative;\r\n\tbackground-color: #f5f5f5;\r\n    .yqhy {\r\n        height: 400px;\r\n        width: 100%;\r\n        background-color: $theme;\r\n        display: flex;\r\n        flex-direction: column;\r\n        justify-content: center;\r\n        color: #fff;\r\n        font-size: 50px;\r\n        padding-top: 60px;\r\n    }\r\n    :deep(.van-nav-bar){\r\n\t\tpadding: 0;\r\n\t\tbackground-color: #ffffff;\r\n        position: absolute !important;\r\n        width: 100%;\r\n        background-color: inherit;\r\n        color: #000000;\r\n        z-index: 0;\r\n\t\t.van-nav-bar__content{\r\n\t\t\tbackground-color: #ffffff;\r\n\t\t}\r\n\t\t.van-nav-bar__left{\r\n\t\t\t\r\n\t\t\t.van-icon{\r\n\t\t\t\tcolor: #000000 !important;\r\n\t\t\t}\r\n\t\t}\r\n        .van-nav-bar__left{\r\n            .van-icon{\r\n                color: #fff;\r\n            }\r\n        }\r\n        .van-nav-bar__title{\r\n            color: #060606;\r\n        }\r\n        .van-nav-bar__right{\r\n            .van-icon{\r\n                color: #fff;\r\n            }\r\n        }\r\n    }\r\n    .content{\r\n        width: 100%;\r\n        padding: 0 25px;\r\n        border-radius: 30px;\r\n        flex: 1;\r\n        overflow: auto;\r\n        // background-color: #fff;\r\n        .bottom{\r\n            width: 100%;\r\n            height: 74px;\r\n            line-height: 74px;\r\n            font-size: 32px;\r\n            background-color: $theme;\r\n            color: #fff;\r\n            border-radius: 6px;\r\n            margin-top: 20px;\r\n        }\r\n        .top{\r\n            padding:20px;\r\n            background-color: #fff;\r\n            border-radius: 20px;\r\n            .title{\r\n                margin-bottom: 20px;\r\n            }\r\n            .c{\r\n                font-size: 22px;\r\n                line-height: 2;\r\n                text-indent: 2em;\r\n                color: #666;\r\n                margin-bottom: 20px;\r\n            }\r\n            .b{\r\n                width: 290px;\r\n                margin: 0 auto;\r\n                font-size: 26px;\r\n                color: #333;\r\n                position: relative;\r\n                .span{\r\n                    margin-left: 5px;\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n</style>"], "mappings": ";;;;;;AAsDA,SAASA,GAAG,EAAEC,kBAAiB,QAAS,KAAK;AAC7C,SAASC,UAAS,QAAS,kBAAiB;AAC5C,SAASC,SAAQ,QAAS,YAAY;AACtC,SAASC,OAAM,QAAS,UAAS;AACjC,OAAOC,KAAI,MAAO,eAAc;AAChC,SAAQC,UAAU,QAAO,iBAAgB;AAEzC,OAAOC,MAAK,MAAO,WAAW;AAC9B;AACA;AACA,OAAOC,YAAW,MAAO,gBAAgB;AACzC,IAAIC,GAAE,GAAI,EAAE;AACZ,eAAe;EACXC,IAAI,EAAE,aAAa;EACnBC,KAAK,WAAAA,MAACC,KAAK,EAAEC,GAAG,EAAC;IACb,IAAAC,mBAAA,GAAgBb,kBAAkB,CAAC;MAA5Bc,KAAK,GAAAD,mBAAA,CAALC,KAAK;IACZ,IAAAC,QAAA,GAAcZ,OAAO,CAAC;MAAda,CAAA,GAAAD,QAAA,CAAAC,CAAA;IACR,IAAAC,aAAA,GAAwBV,YAAY,CAAC,CAAC;MAA9BW,WAAU,GAAAD,aAAA,CAAVC,WAAU;IAClB,IAAAC,UAAA,GAAiBjB,SAAS,CAAC,CAAC;MAApBkB,IAAG,GAAAD,UAAA,CAAHC,IAAG;IACX,IAAMC,SAAQ,GAAItB,GAAG,CAAC,EAAE;IACxB,IAAMuB,IAAG,GAAIvB,GAAG,CAAC,CAAC,CAAC;IACnBa,GAAG,CAACW,IAAI,CAAC,YAAY,EAAC,IAAI;IAC1B,IAAMC,SAAQ,GAAI,SAAZA,SAAQA,CAAA,EAAU;MACpBJ,IAAI,CAAC,OAAO;IAChB;IACAnB,UAAU,CAAC,CAAC,CAACwB,IAAI,CAAC,UAAAC,GAAE,EAAK;MAAA,IAAAC,SAAA;MAErBL,IAAI,CAACM,KAAI,GAAAC,aAAA,KAAQH,GAAG,CAACI,IAAI;MACzBC,OAAO,CAACC,GAAG,CAACV,IAAI,EAAC,MAAM;MACvBD,SAAS,CAACO,KAAI,IAAAD,SAAA,GAAID,GAAG,CAACI,IAAI,cAAAH,SAAA,uBAARA,SAAA,CAAUM,UAAS;;MAErC;MACA,IAAMC,OAAM,GAAI,0BAA0B,EAAE;MAC5C,IAAIZ,IAAI,CAACM,KAAK,CAACpB,GAAE,IAAK,CAACc,IAAI,CAACM,KAAK,CAACpB,GAAG,CAAC2B,UAAU,CAAC,MAAM,CAAC,EAAE;QACtDb,IAAI,CAACM,KAAK,CAACpB,GAAE,GAAI0B,OAAM,GAAI,wBAAuB,GAAIZ,IAAI,CAACM,KAAK,CAACQ,WAAW;MAChF;MACA5B,GAAE,GAAIc,IAAI,CAACM,KAAK,CAACpB,GAAG;IACxB,CAAC;IACD;IACA;IACA,IAAM6B,IAAG,GAAI,SAAPA,IAAGA,CAAKT,KAAK,EAAK;MACpB,IAAI;QACA;QACA,IAAMM,OAAM,GAAI,0BAA0B,EAAE;QAC5C,IAAII,SAAQ,GAAIV,KAAK;QACrB,IAAIA,KAAI,IAAK,CAACA,KAAK,CAACO,UAAU,CAAC,MAAM,CAAC,EAAE;UACpCG,SAAQ,GAAIJ,OAAM,GAAI,wBAAuB,GAAIZ,IAAI,CAACM,KAAK,CAACQ,WAAW;QAC3E;QACAlB,WAAW,CAACoB,SAAS,CAAC;QACtBxB,KAAK,CAACyB,QAAQ,CAAC;UAAEC,IAAI,EAAE,SAAS;UAAEC,OAAO,EAACzB,CAAC,CAAC,YAAY;QAAC,CAAC,CAAC;MAC/D,EAAE,OAAO0B,CAAC,EAAE;QACR5B,KAAK,CAACyB,QAAQ,CAAC;UAAEC,IAAI,EAAE,OAAO;UAAEC,OAAO,EAACzB,CAAC,CAAC,YAAY;QAAC,CAAC,CAAC;MAC7D;IACJ;;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IAIA,OAAO;MAACQ,SAAS,EAATA,SAAS;MAACH,SAAS,EAATA,SAAS;MAACgB,IAAI,EAAJA,IAAI;MAACf,IAAI,EAAJA;IAAI;EACzC,CAAC;EAEDqB,OAAO,WAAAA,QAAA,EAAG;IACN,IAAIC,IAAG,GAAI,IAAG;IACd,IAAIC,GAAE,GAAIC,WAAW,CAAC,YAAU;MAC5B,IAAGtC,GAAE,IAAI,EAAE,EAAC;QACR;QACA,IAAIuC,KAAI,GAAIvC,GAAG;QACf,IAAI,CAACuC,KAAK,CAACZ,UAAU,CAAC,MAAM,CAAC,EAAE;UAC3B,IAAMD,OAAM,GAAI,0BAA0B,EAAE;UAC5Ca,KAAI,GAAIb,OAAM,GAAI,wBAAuB,GAAIU,IAAI,CAACtB,IAAI,CAACc,WAAW;QACtE;QAEA,IAAI9B,MAAM,CAACsC,IAAI,CAACI,KAAK,CAACC,SAAS,EAAE;UAC7BC,IAAI,EAAEH,KAAK;UAAE;UACbI,KAAK,EAAE,GAAG;UACVC,MAAM,EAAE,GAAG;UACXC,SAAS,EAAE,MAAM;UACjBC,UAAU,EAAE,MAAM;UAClBC,YAAY,EAAEjD,MAAM,CAACkD,YAAY,CAACC;QACtC,CAAC,CAAC;QACFC,aAAa,CAACb,GAAG;MACrB;IACJ,CAAC,EAAC,GAAG;EAGT;AAEJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}