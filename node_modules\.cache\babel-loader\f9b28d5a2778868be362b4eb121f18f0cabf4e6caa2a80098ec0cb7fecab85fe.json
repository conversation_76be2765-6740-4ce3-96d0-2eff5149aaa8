{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _Card from \"./Card.mjs\";\nvar Card = withInstall(_Card);\nvar stdin_default = Card;\nexport { Card, stdin_default as default };", "map": {"version": 3, "names": ["withInstall", "_Card", "Card", "stdin_default", "default"], "sources": ["C:/Users/<USER>/Desktop/vue3_3.0/node_modules/vant/es/card/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _Card from \"./Card.mjs\";\nconst Card = withInstall(_Card);\nvar stdin_default = Card;\nexport {\n  Card,\n  stdin_default as default\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,KAAK,MAAM,YAAY;AAC9B,IAAMC,IAAI,GAAGF,WAAW,CAACC,KAAK,CAAC;AAC/B,IAAIE,aAAa,GAAGD,IAAI;AACxB,SACEA,IAAI,EACJC,aAAa,IAAIC,OAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}