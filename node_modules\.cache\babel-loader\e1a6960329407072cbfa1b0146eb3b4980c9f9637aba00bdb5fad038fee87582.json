{"ast": null, "code": "import http from '@/request/index';\nimport qs from 'qs';\n// 公共参数 -- 字典值\nexport var common_parameters = function common_parameters() {\n  return http.get('/user/common_parameters').then(function (result) {\n    return result.data;\n  });\n};\n\n// 定义接口的传参\n// interface UserInfoParam {\n//     tel: string,\n//     pwd: string\n//     qv: string\n// }\n//登录\nexport var login = function login(params) {\n  return http.post('/user/do_login', qs.stringify(params)).then(function (result) {\n    return result.data;\n  });\n};\n//退出登录\nexport var logout = function logout(params) {\n  return http.post('/user/logout', qs.stringify(params)).then(function (result) {\n    return result.data;\n  });\n};\n//注册\nexport var do_register = function do_register(params) {\n  return http.post('/user/do_register', qs.stringify(params)).then(function (result) {\n    return result.data;\n  });\n};", "map": {"version": 3, "names": ["http", "qs", "common_parameters", "get", "then", "result", "data", "login", "params", "post", "stringify", "logout", "do_register"], "sources": ["C:/Users/<USER>/Desktop/vue3_3.0/src/api/login/index.js"], "sourcesContent": ["import http from '@/request/index'\r\n\r\nimport qs from 'qs';\r\n// 公共参数 -- 字典值\r\nexport const common_parameters = () => {\r\n    return http.get('/user/common_parameters')\r\n      .then((result) => {\r\n        return result.data\r\n      })\r\n  }\r\n\r\n// 定义接口的传参\r\n// interface UserInfoParam {\r\n//     tel: string,\r\n//     pwd: string\r\n//     qv: string\r\n// }\r\n//登录\r\nexport const login = (params) => {\r\n    return http.post('/user/do_login',qs.stringify(params))\r\n      .then((result) => {\r\n        return result.data\r\n      })\r\n  }\r\n//退出登录\r\nexport const logout = (params) => {\r\n    return http.post('/user/logout',qs.stringify(params))\r\n      .then((result) => {\r\n        return result.data\r\n      })\r\n  }\r\n//注册\r\nexport const do_register = (params) => {\r\n    return http.post('/user/do_register',qs.stringify(params))\r\n      .then((result) => {\r\n        return result.data\r\n      })\r\n  }\r\n"], "mappings": "AAAA,OAAOA,IAAI,MAAM,iBAAiB;AAElC,OAAOC,EAAE,MAAM,IAAI;AACnB;AACA,OAAO,IAAMC,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAA,EAAS;EACnC,OAAOF,IAAI,CAACG,GAAG,CAAC,yBAAyB,CAAC,CACvCC,IAAI,CAAC,UAACC,MAAM,EAAK;IAChB,OAAOA,MAAM,CAACC,IAAI;EACpB,CAAC,CAAC;AACN,CAAC;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAMC,KAAK,GAAG,SAARA,KAAKA,CAAIC,MAAM,EAAK;EAC7B,OAAOR,IAAI,CAACS,IAAI,CAAC,gBAAgB,EAACR,EAAE,CAACS,SAAS,CAACF,MAAM,CAAC,CAAC,CACpDJ,IAAI,CAAC,UAACC,MAAM,EAAK;IAChB,OAAOA,MAAM,CAACC,IAAI;EACpB,CAAC,CAAC;AACN,CAAC;AACH;AACA,OAAO,IAAMK,MAAM,GAAG,SAATA,MAAMA,CAAIH,MAAM,EAAK;EAC9B,OAAOR,IAAI,CAACS,IAAI,CAAC,cAAc,EAACR,EAAE,CAACS,SAAS,CAACF,MAAM,CAAC,CAAC,CAClDJ,IAAI,CAAC,UAACC,MAAM,EAAK;IAChB,OAAOA,MAAM,CAACC,IAAI;EACpB,CAAC,CAAC;AACN,CAAC;AACH;AACA,OAAO,IAAMM,WAAW,GAAG,SAAdA,WAAWA,CAAIJ,MAAM,EAAK;EACnC,OAAOR,IAAI,CAACS,IAAI,CAAC,mBAAmB,EAACR,EAAE,CAACS,SAAS,CAACF,MAAM,CAAC,CAAC,CACvDJ,IAAI,CAAC,UAACC,MAAM,EAAK;IAChB,OAAOA,MAAM,CAACC,IAAI;EACpB,CAAC,CAAC;AACN,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}