{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _Empty from \"./Empty.mjs\";\nvar Empty = withInstall(_Empty);\nvar stdin_default = Empty;\nexport { Empty, stdin_default as default };", "map": {"version": 3, "names": ["withInstall", "_Empty", "Empty", "stdin_default", "default"], "sources": ["C:/Users/<USER>/Downloads/Telegram Desktop/vue3_3.0/vue3_3.0/node_modules/vant/es/empty/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _Empty from \"./Empty.mjs\";\nconst Empty = withInstall(_Empty);\nvar stdin_default = Empty;\nexport {\n  Empty,\n  stdin_default as default\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,MAAM,MAAM,aAAa;AAChC,IAAMC,KAAK,GAAGF,WAAW,CAACC,MAAM,CAAC;AACjC,IAAIE,aAAa,GAAGD,KAAK;AACzB,SACEA,KAAK,EACLC,aAAa,IAAIC,OAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}