{"ast": null, "code": "'use strict';\n\n/* eslint complexity: [2, 18], max-statements: [2, 33] */\nfunction _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\nmodule.exports = function hasSymbols() {\n  if (typeof Symbol !== 'function' || typeof Object.getOwnPropertySymbols !== 'function') {\n    return false;\n  }\n  if (_typeof(Symbol.iterator) === 'symbol') {\n    return true;\n  }\n  var obj = {};\n  var sym = Symbol('test');\n  var symObj = Object(sym);\n  if (typeof sym === 'string') {\n    return false;\n  }\n  if (Object.prototype.toString.call(sym) !== '[object Symbol]') {\n    return false;\n  }\n  if (Object.prototype.toString.call(symObj) !== '[object Symbol]') {\n    return false;\n  }\n\n  // temp disabled per https://github.com/ljharb/object.assign/issues/17\n  // if (sym instanceof Symbol) { return false; }\n  // temp disabled per https://github.com/WebReflection/get-own-property-symbols/issues/4\n  // if (!(symObj instanceof Symbol)) { return false; }\n\n  // if (typeof Symbol.prototype.toString !== 'function') { return false; }\n  // if (String(sym) !== Symbol.prototype.toString.call(sym)) { return false; }\n\n  var symVal = 42;\n  obj[sym] = symVal;\n  for (sym in obj) {\n    return false;\n  } // eslint-disable-line no-restricted-syntax, no-unreachable-loop\n  if (typeof Object.keys === 'function' && Object.keys(obj).length !== 0) {\n    return false;\n  }\n  if (typeof Object.getOwnPropertyNames === 'function' && Object.getOwnPropertyNames(obj).length !== 0) {\n    return false;\n  }\n  var syms = Object.getOwnPropertySymbols(obj);\n  if (syms.length !== 1 || syms[0] !== sym) {\n    return false;\n  }\n  if (!Object.prototype.propertyIsEnumerable.call(obj, sym)) {\n    return false;\n  }\n  if (typeof Object.getOwnPropertyDescriptor === 'function') {\n    var descriptor = Object.getOwnPropertyDescriptor(obj, sym);\n    if (descriptor.value !== symVal || descriptor.enumerable !== true) {\n      return false;\n    }\n  }\n  return true;\n};", "map": {"version": 3, "names": ["_typeof", "obj", "Symbol", "iterator", "constructor", "prototype", "module", "exports", "hasSymbols", "Object", "getOwnPropertySymbols", "sym", "symObj", "toString", "call", "symVal", "keys", "length", "getOwnPropertyNames", "syms", "propertyIsEnumerable", "getOwnPropertyDescriptor", "descriptor", "value", "enumerable"], "sources": ["C:/Users/<USER>/Downloads/Telegram Desktop/vue3_3.0/vue3_3.0/node_modules/has-symbols/shams.js"], "sourcesContent": ["'use strict';\n\n/* eslint complexity: [2, 18], max-statements: [2, 33] */\nmodule.exports = function hasSymbols() {\n\tif (typeof Symbol !== 'function' || typeof Object.getOwnPropertySymbols !== 'function') { return false; }\n\tif (typeof Symbol.iterator === 'symbol') { return true; }\n\n\tvar obj = {};\n\tvar sym = Symbol('test');\n\tvar symObj = Object(sym);\n\tif (typeof sym === 'string') { return false; }\n\n\tif (Object.prototype.toString.call(sym) !== '[object Symbol]') { return false; }\n\tif (Object.prototype.toString.call(symObj) !== '[object Symbol]') { return false; }\n\n\t// temp disabled per https://github.com/ljharb/object.assign/issues/17\n\t// if (sym instanceof Symbol) { return false; }\n\t// temp disabled per https://github.com/WebReflection/get-own-property-symbols/issues/4\n\t// if (!(symObj instanceof Symbol)) { return false; }\n\n\t// if (typeof Symbol.prototype.toString !== 'function') { return false; }\n\t// if (String(sym) !== Symbol.prototype.toString.call(sym)) { return false; }\n\n\tvar symVal = 42;\n\tobj[sym] = symVal;\n\tfor (sym in obj) { return false; } // eslint-disable-line no-restricted-syntax, no-unreachable-loop\n\tif (typeof Object.keys === 'function' && Object.keys(obj).length !== 0) { return false; }\n\n\tif (typeof Object.getOwnPropertyNames === 'function' && Object.getOwnPropertyNames(obj).length !== 0) { return false; }\n\n\tvar syms = Object.getOwnPropertySymbols(obj);\n\tif (syms.length !== 1 || syms[0] !== sym) { return false; }\n\n\tif (!Object.prototype.propertyIsEnumerable.call(obj, sym)) { return false; }\n\n\tif (typeof Object.getOwnPropertyDescriptor === 'function') {\n\t\tvar descriptor = Object.getOwnPropertyDescriptor(obj, sym);\n\t\tif (descriptor.value !== symVal || descriptor.enumerable !== true) { return false; }\n\t}\n\n\treturn true;\n};\n"], "mappings": "AAAA,YAAY;;AAEZ;AAAA,SAAAA,QAAAC,GAAA,sCAAAD,OAAA,wBAAAE,MAAA,uBAAAA,MAAA,CAAAC,QAAA,aAAAF,GAAA,kBAAAA,GAAA,gBAAAA,GAAA,WAAAA,GAAA,yBAAAC,MAAA,IAAAD,GAAA,CAAAG,WAAA,KAAAF,MAAA,IAAAD,GAAA,KAAAC,MAAA,CAAAG,SAAA,qBAAAJ,GAAA,KAAAD,OAAA,CAAAC,GAAA;AACAK,MAAM,CAACC,OAAO,GAAG,SAASC,UAAUA,CAAA,EAAG;EACtC,IAAI,OAAON,MAAM,KAAK,UAAU,IAAI,OAAOO,MAAM,CAACC,qBAAqB,KAAK,UAAU,EAAE;IAAE,OAAO,KAAK;EAAE;EACxG,IAAIV,OAAA,CAAOE,MAAM,CAACC,QAAQ,MAAK,QAAQ,EAAE;IAAE,OAAO,IAAI;EAAE;EAExD,IAAIF,GAAG,GAAG,CAAC,CAAC;EACZ,IAAIU,GAAG,GAAGT,MAAM,CAAC,MAAM,CAAC;EACxB,IAAIU,MAAM,GAAGH,MAAM,CAACE,GAAG,CAAC;EACxB,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;IAAE,OAAO,KAAK;EAAE;EAE7C,IAAIF,MAAM,CAACJ,SAAS,CAACQ,QAAQ,CAACC,IAAI,CAACH,GAAG,CAAC,KAAK,iBAAiB,EAAE;IAAE,OAAO,KAAK;EAAE;EAC/E,IAAIF,MAAM,CAACJ,SAAS,CAACQ,QAAQ,CAACC,IAAI,CAACF,MAAM,CAAC,KAAK,iBAAiB,EAAE;IAAE,OAAO,KAAK;EAAE;;EAElF;EACA;EACA;EACA;;EAEA;EACA;;EAEA,IAAIG,MAAM,GAAG,EAAE;EACfd,GAAG,CAACU,GAAG,CAAC,GAAGI,MAAM;EACjB,KAAKJ,GAAG,IAAIV,GAAG,EAAE;IAAE,OAAO,KAAK;EAAE,CAAC,CAAC;EACnC,IAAI,OAAOQ,MAAM,CAACO,IAAI,KAAK,UAAU,IAAIP,MAAM,CAACO,IAAI,CAACf,GAAG,CAAC,CAACgB,MAAM,KAAK,CAAC,EAAE;IAAE,OAAO,KAAK;EAAE;EAExF,IAAI,OAAOR,MAAM,CAACS,mBAAmB,KAAK,UAAU,IAAIT,MAAM,CAACS,mBAAmB,CAACjB,GAAG,CAAC,CAACgB,MAAM,KAAK,CAAC,EAAE;IAAE,OAAO,KAAK;EAAE;EAEtH,IAAIE,IAAI,GAAGV,MAAM,CAACC,qBAAqB,CAACT,GAAG,CAAC;EAC5C,IAAIkB,IAAI,CAACF,MAAM,KAAK,CAAC,IAAIE,IAAI,CAAC,CAAC,CAAC,KAAKR,GAAG,EAAE;IAAE,OAAO,KAAK;EAAE;EAE1D,IAAI,CAACF,MAAM,CAACJ,SAAS,CAACe,oBAAoB,CAACN,IAAI,CAACb,GAAG,EAAEU,GAAG,CAAC,EAAE;IAAE,OAAO,KAAK;EAAE;EAE3E,IAAI,OAAOF,MAAM,CAACY,wBAAwB,KAAK,UAAU,EAAE;IAC1D,IAAIC,UAAU,GAAGb,MAAM,CAACY,wBAAwB,CAACpB,GAAG,EAAEU,GAAG,CAAC;IAC1D,IAAIW,UAAU,CAACC,KAAK,KAAKR,MAAM,IAAIO,UAAU,CAACE,UAAU,KAAK,IAAI,EAAE;MAAE,OAAO,KAAK;IAAE;EACpF;EAEA,OAAO,IAAI;AACZ,CAAC"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}