{"ast": null, "code": "function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nfunction _iterableToArrayLimit(arr, i) { var _i = null == arr ? null : \"undefined\" != typeof Symbol && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (null != _i) { var _s, _e, _x, _r, _arr = [], _n = !0, _d = !1; try { if (_x = (_i = _i.call(arr)).next, 0 === i) { if (Object(_i) !== _i) return; _n = !1; } else for (; !(_n = (_s = _x.call(_i)).done) && (_arr.push(_s.value), _arr.length !== i); _n = !0); } catch (err) { _d = !0, _e = err; } finally { try { if (!_n && null != _i.return && (_r = _i.return(), Object(_r) !== _r)) return; } finally { if (_d) throw _e; } } return _arr; } }\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\nimport { mergeProps as _mergeProps, createVNode as _createVNode } from \"vue\";\nimport { computed, defineComponent, getCurrentInstance } from \"vue\";\nimport { createNamespace, extend, isObject, numericProp } from \"../utils/index.mjs\";\nimport { TABBAR_KEY } from \"../tabbar/Tabbar.mjs\";\nimport { useParent } from \"@vant/use\";\nimport { routeProps, useRoute } from \"../composables/use-route.mjs\";\nimport { Icon } from \"../icon/index.mjs\";\nimport { Badge } from \"../badge/index.mjs\";\nvar _createNamespace = createNamespace(\"tabbar-item\"),\n  _createNamespace2 = _slicedToArray(_createNamespace, 2),\n  name = _createNamespace2[0],\n  bem = _createNamespace2[1];\nvar tabbarItemProps = extend({}, routeProps, {\n  dot: Boolean,\n  icon: String,\n  name: numericProp,\n  badge: numericProp,\n  badgeProps: Object,\n  iconPrefix: String\n});\nvar stdin_default = defineComponent({\n  name: name,\n  props: tabbarItemProps,\n  emits: [\"click\"],\n  setup: function setup(props, _ref) {\n    var emit = _ref.emit,\n      slots = _ref.slots;\n    var route = useRoute();\n    var vm = getCurrentInstance().proxy;\n    var _useParent = useParent(TABBAR_KEY),\n      parent = _useParent.parent,\n      index = _useParent.index;\n    if (!parent) {\n      if (process.env.NODE_ENV !== \"production\") {\n        console.error(\"[Vant] <TabbarItem> must be a child component of <Tabbar>.\");\n      }\n      return;\n    }\n    var active = computed(function () {\n      var _a;\n      var _parent$props = parent.props,\n        route2 = _parent$props.route,\n        modelValue = _parent$props.modelValue;\n      if (route2 && \"$route\" in vm) {\n        var $route = vm.$route;\n        var to = props.to;\n        var config = isObject(to) ? to : {\n          path: to\n        };\n        return !!$route.matched.find(function (val) {\n          var pathMatched = \"path\" in config && config.path === val.path;\n          var nameMatched = \"name\" in config && config.name === val.name;\n          return pathMatched || nameMatched;\n        });\n      }\n      return ((_a = props.name) != null ? _a : index.value) === modelValue;\n    });\n    var onClick = function onClick(event) {\n      var _a;\n      if (!active.value) {\n        parent.setActive((_a = props.name) != null ? _a : index.value, route);\n      }\n      emit(\"click\", event);\n    };\n    var renderIcon = function renderIcon() {\n      if (slots.icon) {\n        return slots.icon({\n          active: active.value\n        });\n      }\n      if (props.icon) {\n        return _createVNode(Icon, {\n          \"name\": props.icon,\n          \"classPrefix\": props.iconPrefix\n        }, null);\n      }\n    };\n    return function () {\n      var _a;\n      var dot = props.dot,\n        badge = props.badge;\n      var _parent$props2 = parent.props,\n        activeColor = _parent$props2.activeColor,\n        inactiveColor = _parent$props2.inactiveColor;\n      var color = active.value ? activeColor : inactiveColor;\n      return _createVNode(\"div\", {\n        \"role\": \"tab\",\n        \"class\": bem({\n          active: active.value\n        }),\n        \"style\": {\n          color: color\n        },\n        \"tabindex\": 0,\n        \"aria-selected\": active.value,\n        \"onClick\": onClick\n      }, [_createVNode(Badge, _mergeProps({\n        \"dot\": dot,\n        \"class\": bem(\"icon\"),\n        \"content\": badge\n      }, props.badgeProps), {\n        default: renderIcon\n      }), _createVNode(\"div\", {\n        \"class\": bem(\"text\")\n      }, [(_a = slots.default) == null ? void 0 : _a.call(slots, {\n        active: active.value\n      })])]);\n    };\n  }\n});\nexport { stdin_default as default };", "map": {"version": 3, "names": ["mergeProps", "_mergeProps", "createVNode", "_createVNode", "computed", "defineComponent", "getCurrentInstance", "createNamespace", "extend", "isObject", "numericProp", "TABBAR_KEY", "useParent", "routeProps", "useRoute", "Icon", "Badge", "_createNamespace", "_createNamespace2", "_slicedToArray", "name", "bem", "tabbarItemProps", "dot", "Boolean", "icon", "String", "badge", "badgeProps", "Object", "iconPrefix", "stdin_default", "props", "emits", "setup", "_ref", "emit", "slots", "route", "vm", "proxy", "_useParent", "parent", "index", "process", "env", "NODE_ENV", "console", "error", "active", "_a", "_parent$props", "route2", "modelValue", "$route", "to", "config", "path", "matched", "find", "val", "pathMatched", "nameMatched", "value", "onClick", "event", "setActive", "renderIcon", "_parent$props2", "activeColor", "inactiveColor", "color", "default", "call"], "sources": ["C:/Users/<USER>/Downloads/Telegram Desktop/vue3_3.0/vue3_3.0/node_modules/vant/es/tabbar-item/TabbarItem.mjs"], "sourcesContent": ["import { mergeProps as _mergeProps, createVNode as _createVNode } from \"vue\";\nimport { computed, defineComponent, getCurrentInstance } from \"vue\";\nimport { createNamespace, extend, isObject, numericProp } from \"../utils/index.mjs\";\nimport { TABBAR_KEY } from \"../tabbar/Tabbar.mjs\";\nimport { useParent } from \"@vant/use\";\nimport { routeProps, useRoute } from \"../composables/use-route.mjs\";\nimport { Icon } from \"../icon/index.mjs\";\nimport { Badge } from \"../badge/index.mjs\";\nconst [name, bem] = createNamespace(\"tabbar-item\");\nconst tabbarItemProps = extend({}, routeProps, {\n  dot: Boolean,\n  icon: String,\n  name: numericProp,\n  badge: numericProp,\n  badgeProps: Object,\n  iconPrefix: String\n});\nvar stdin_default = defineComponent({\n  name,\n  props: tabbarItemProps,\n  emits: [\"click\"],\n  setup(props, {\n    emit,\n    slots\n  }) {\n    const route = useRoute();\n    const vm = getCurrentInstance().proxy;\n    const {\n      parent,\n      index\n    } = useParent(TABBAR_KEY);\n    if (!parent) {\n      if (process.env.NODE_ENV !== \"production\") {\n        console.error(\"[Vant] <TabbarItem> must be a child component of <Tabbar>.\");\n      }\n      return;\n    }\n    const active = computed(() => {\n      var _a;\n      const {\n        route: route2,\n        modelValue\n      } = parent.props;\n      if (route2 && \"$route\" in vm) {\n        const {\n          $route\n        } = vm;\n        const {\n          to\n        } = props;\n        const config = isObject(to) ? to : {\n          path: to\n        };\n        return !!$route.matched.find((val) => {\n          const pathMatched = \"path\" in config && config.path === val.path;\n          const nameMatched = \"name\" in config && config.name === val.name;\n          return pathMatched || nameMatched;\n        });\n      }\n      return ((_a = props.name) != null ? _a : index.value) === modelValue;\n    });\n    const onClick = (event) => {\n      var _a;\n      if (!active.value) {\n        parent.setActive((_a = props.name) != null ? _a : index.value, route);\n      }\n      emit(\"click\", event);\n    };\n    const renderIcon = () => {\n      if (slots.icon) {\n        return slots.icon({\n          active: active.value\n        });\n      }\n      if (props.icon) {\n        return _createVNode(Icon, {\n          \"name\": props.icon,\n          \"classPrefix\": props.iconPrefix\n        }, null);\n      }\n    };\n    return () => {\n      var _a;\n      const {\n        dot,\n        badge\n      } = props;\n      const {\n        activeColor,\n        inactiveColor\n      } = parent.props;\n      const color = active.value ? activeColor : inactiveColor;\n      return _createVNode(\"div\", {\n        \"role\": \"tab\",\n        \"class\": bem({\n          active: active.value\n        }),\n        \"style\": {\n          color\n        },\n        \"tabindex\": 0,\n        \"aria-selected\": active.value,\n        \"onClick\": onClick\n      }, [_createVNode(Badge, _mergeProps({\n        \"dot\": dot,\n        \"class\": bem(\"icon\"),\n        \"content\": badge\n      }, props.badgeProps), {\n        default: renderIcon\n      }), _createVNode(\"div\", {\n        \"class\": bem(\"text\")\n      }, [(_a = slots.default) == null ? void 0 : _a.call(slots, {\n        active: active.value\n      })])]);\n    };\n  }\n});\nexport {\n  stdin_default as default\n};\n"], "mappings": ";;;;;;AAAA,SAASA,UAAU,IAAIC,WAAW,EAAEC,WAAW,IAAIC,YAAY,QAAQ,KAAK;AAC5E,SAASC,QAAQ,EAAEC,eAAe,EAAEC,kBAAkB,QAAQ,KAAK;AACnE,SAASC,eAAe,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,WAAW,QAAQ,oBAAoB;AACnF,SAASC,UAAU,QAAQ,sBAAsB;AACjD,SAASC,SAAS,QAAQ,WAAW;AACrC,SAASC,UAAU,EAAEC,QAAQ,QAAQ,8BAA8B;AACnE,SAASC,IAAI,QAAQ,mBAAmB;AACxC,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,IAAAC,gBAAA,GAAoBV,eAAe,CAAC,aAAa,CAAC;EAAAW,iBAAA,GAAAC,cAAA,CAAAF,gBAAA;EAA3CG,IAAI,GAAAF,iBAAA;EAAEG,GAAG,GAAAH,iBAAA;AAChB,IAAMI,eAAe,GAAGd,MAAM,CAAC,CAAC,CAAC,EAAEK,UAAU,EAAE;EAC7CU,GAAG,EAAEC,OAAO;EACZC,IAAI,EAAEC,MAAM;EACZN,IAAI,EAAEV,WAAW;EACjBiB,KAAK,EAAEjB,WAAW;EAClBkB,UAAU,EAAEC,MAAM;EAClBC,UAAU,EAAEJ;AACd,CAAC,CAAC;AACF,IAAIK,aAAa,GAAG1B,eAAe,CAAC;EAClCe,IAAI,EAAJA,IAAI;EACJY,KAAK,EAAEV,eAAe;EACtBW,KAAK,EAAE,CAAC,OAAO,CAAC;EAChBC,KAAK,WAAAA,MAACF,KAAK,EAAAG,IAAA,EAGR;IAAA,IAFDC,IAAI,GAAAD,IAAA,CAAJC,IAAI;MACJC,KAAK,GAAAF,IAAA,CAALE,KAAK;IAEL,IAAMC,KAAK,GAAGxB,QAAQ,CAAC,CAAC;IACxB,IAAMyB,EAAE,GAAGjC,kBAAkB,CAAC,CAAC,CAACkC,KAAK;IACrC,IAAAC,UAAA,GAGI7B,SAAS,CAACD,UAAU,CAAC;MAFvB+B,MAAM,GAAAD,UAAA,CAANC,MAAM;MACNC,KAAK,GAAAF,UAAA,CAALE,KAAK;IAEP,IAAI,CAACD,MAAM,EAAE;MACX,IAAIE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzCC,OAAO,CAACC,KAAK,CAAC,4DAA4D,CAAC;MAC7E;MACA;IACF;IACA,IAAMC,MAAM,GAAG7C,QAAQ,CAAC,YAAM;MAC5B,IAAI8C,EAAE;MACN,IAAAC,aAAA,GAGIT,MAAM,CAACV,KAAK;QAFPoB,MAAM,GAAAD,aAAA,CAAbb,KAAK;QACLe,UAAU,GAAAF,aAAA,CAAVE,UAAU;MAEZ,IAAID,MAAM,IAAI,QAAQ,IAAIb,EAAE,EAAE;QAC5B,IACEe,MAAM,GACJf,EAAE,CADJe,MAAM;QAER,IACEC,EAAE,GACAvB,KAAK,CADPuB,EAAE;QAEJ,IAAMC,MAAM,GAAG/C,QAAQ,CAAC8C,EAAE,CAAC,GAAGA,EAAE,GAAG;UACjCE,IAAI,EAAEF;QACR,CAAC;QACD,OAAO,CAAC,CAACD,MAAM,CAACI,OAAO,CAACC,IAAI,CAAC,UAACC,GAAG,EAAK;UACpC,IAAMC,WAAW,GAAG,MAAM,IAAIL,MAAM,IAAIA,MAAM,CAACC,IAAI,KAAKG,GAAG,CAACH,IAAI;UAChE,IAAMK,WAAW,GAAG,MAAM,IAAIN,MAAM,IAAIA,MAAM,CAACpC,IAAI,KAAKwC,GAAG,CAACxC,IAAI;UAChE,OAAOyC,WAAW,IAAIC,WAAW;QACnC,CAAC,CAAC;MACJ;MACA,OAAO,CAAC,CAACZ,EAAE,GAAGlB,KAAK,CAACZ,IAAI,KAAK,IAAI,GAAG8B,EAAE,GAAGP,KAAK,CAACoB,KAAK,MAAMV,UAAU;IACtE,CAAC,CAAC;IACF,IAAMW,OAAO,GAAG,SAAVA,OAAOA,CAAIC,KAAK,EAAK;MACzB,IAAIf,EAAE;MACN,IAAI,CAACD,MAAM,CAACc,KAAK,EAAE;QACjBrB,MAAM,CAACwB,SAAS,CAAC,CAAChB,EAAE,GAAGlB,KAAK,CAACZ,IAAI,KAAK,IAAI,GAAG8B,EAAE,GAAGP,KAAK,CAACoB,KAAK,EAAEzB,KAAK,CAAC;MACvE;MACAF,IAAI,CAAC,OAAO,EAAE6B,KAAK,CAAC;IACtB,CAAC;IACD,IAAME,UAAU,GAAG,SAAbA,UAAUA,CAAA,EAAS;MACvB,IAAI9B,KAAK,CAACZ,IAAI,EAAE;QACd,OAAOY,KAAK,CAACZ,IAAI,CAAC;UAChBwB,MAAM,EAAEA,MAAM,CAACc;QACjB,CAAC,CAAC;MACJ;MACA,IAAI/B,KAAK,CAACP,IAAI,EAAE;QACd,OAAOtB,YAAY,CAACY,IAAI,EAAE;UACxB,MAAM,EAAEiB,KAAK,CAACP,IAAI;UAClB,aAAa,EAAEO,KAAK,CAACF;QACvB,CAAC,EAAE,IAAI,CAAC;MACV;IACF,CAAC;IACD,OAAO,YAAM;MACX,IAAIoB,EAAE;MACN,IACE3B,GAAG,GAEDS,KAAK,CAFPT,GAAG;QACHI,KAAK,GACHK,KAAK,CADPL,KAAK;MAEP,IAAAyC,cAAA,GAGI1B,MAAM,CAACV,KAAK;QAFdqC,WAAW,GAAAD,cAAA,CAAXC,WAAW;QACXC,aAAa,GAAAF,cAAA,CAAbE,aAAa;MAEf,IAAMC,KAAK,GAAGtB,MAAM,CAACc,KAAK,GAAGM,WAAW,GAAGC,aAAa;MACxD,OAAOnE,YAAY,CAAC,KAAK,EAAE;QACzB,MAAM,EAAE,KAAK;QACb,OAAO,EAAEkB,GAAG,CAAC;UACX4B,MAAM,EAAEA,MAAM,CAACc;QACjB,CAAC,CAAC;QACF,OAAO,EAAE;UACPQ,KAAK,EAALA;QACF,CAAC;QACD,UAAU,EAAE,CAAC;QACb,eAAe,EAAEtB,MAAM,CAACc,KAAK;QAC7B,SAAS,EAAEC;MACb,CAAC,EAAE,CAAC7D,YAAY,CAACa,KAAK,EAAEf,WAAW,CAAC;QAClC,KAAK,EAAEsB,GAAG;QACV,OAAO,EAAEF,GAAG,CAAC,MAAM,CAAC;QACpB,SAAS,EAAEM;MACb,CAAC,EAAEK,KAAK,CAACJ,UAAU,CAAC,EAAE;QACpB4C,OAAO,EAAEL;MACX,CAAC,CAAC,EAAEhE,YAAY,CAAC,KAAK,EAAE;QACtB,OAAO,EAAEkB,GAAG,CAAC,MAAM;MACrB,CAAC,EAAE,CAAC,CAAC6B,EAAE,GAAGb,KAAK,CAACmC,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGtB,EAAE,CAACuB,IAAI,CAACpC,KAAK,EAAE;QACzDY,MAAM,EAAEA,MAAM,CAACc;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACR,CAAC;EACH;AACF,CAAC,CAAC;AACF,SACEhC,aAAa,IAAIyC,OAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}