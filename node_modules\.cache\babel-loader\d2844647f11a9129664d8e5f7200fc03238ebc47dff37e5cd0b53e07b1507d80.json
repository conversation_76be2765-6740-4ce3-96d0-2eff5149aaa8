{"ast": null, "code": "import { createVNode as _createVNode } from \"vue\";\nimport { useHeight } from \"./use-height.mjs\";\nfunction usePlaceholder(contentRef, bem) {\n  var height = useHeight(contentRef, true);\n  return function (renderContent) {\n    return _createVNode(\"div\", {\n      \"class\": bem(\"placeholder\"),\n      \"style\": {\n        height: height.value ? \"\".concat(height.value, \"px\") : void 0\n      }\n    }, [renderContent()]);\n  };\n}\nexport { usePlaceholder };", "map": {"version": 3, "names": ["createVNode", "_createVNode", "useHeight", "usePlaceholder", "contentRef", "bem", "height", "renderContent", "value", "concat"], "sources": ["C:/Users/<USER>/Downloads/Telegram Desktop/vue3_3.0/vue3_3.0/node_modules/vant/es/composables/use-placeholder.mjs"], "sourcesContent": ["import { createVNode as _createVNode } from \"vue\";\nimport { useHeight } from \"./use-height.mjs\";\nfunction usePlaceholder(contentRef, bem) {\n  const height = useHeight(contentRef, true);\n  return (renderContent) => _createVNode(\"div\", {\n    \"class\": bem(\"placeholder\"),\n    \"style\": {\n      height: height.value ? `${height.value}px` : void 0\n    }\n  }, [renderContent()]);\n}\nexport {\n  usePlaceholder\n};\n"], "mappings": "AAAA,SAASA,WAAW,IAAIC,YAAY,QAAQ,KAAK;AACjD,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,SAASC,cAAcA,CAACC,UAAU,EAAEC,GAAG,EAAE;EACvC,IAAMC,MAAM,GAAGJ,SAAS,CAACE,UAAU,EAAE,IAAI,CAAC;EAC1C,OAAO,UAACG,aAAa;IAAA,OAAKN,YAAY,CAAC,KAAK,EAAE;MAC5C,OAAO,EAAEI,GAAG,CAAC,aAAa,CAAC;MAC3B,OAAO,EAAE;QACPC,MAAM,EAAEA,MAAM,CAACE,KAAK,MAAAC,MAAA,CAAMH,MAAM,CAACE,KAAK,UAAO,KAAK;MACpD;IACF,CAAC,EAAE,CAACD,aAAa,CAAC,CAAC,CAAC,CAAC;EAAA;AACvB;AACA,SACEJ,cAAc"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}