{"ast": null, "code": "function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nfunction _iterableToArrayLimit(arr, i) { var _i = null == arr ? null : \"undefined\" != typeof Symbol && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (null != _i) { var _s, _e, _x, _r, _arr = [], _n = !0, _d = !1; try { if (_x = (_i = _i.call(arr)).next, 0 === i) { if (Object(_i) !== _i) return; _n = !1; } else for (; !(_n = (_s = _x.call(_i)).done) && (_arr.push(_s.value), _arr.length !== i); _n = !0); } catch (err) { _d = !0, _e = err; } finally { try { if (!_n && null != _i.return && (_r = _i.return(), Object(_r) !== _r)) return; } finally { if (_d) throw _e; } } return _arr; } }\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\nimport { createVNode as _createVNode } from \"vue\";\nimport { defineComponent } from \"vue\";\nimport { addUnit, makeArrayProp, makeStringProp, makeNumericProp, createNamespace } from \"../utils/index.mjs\";\nimport { Icon } from \"../icon/index.mjs\";\nimport { Sidebar } from \"../sidebar/index.mjs\";\nimport { SidebarItem } from \"../sidebar-item/index.mjs\";\nvar _createNamespace = createNamespace(\"tree-select\"),\n  _createNamespace2 = _slicedToArray(_createNamespace, 2),\n  name = _createNamespace2[0],\n  bem = _createNamespace2[1];\nvar treeSelectProps = {\n  max: makeNumericProp(Infinity),\n  items: makeArrayProp(),\n  height: makeNumericProp(300),\n  selectedIcon: makeStringProp(\"success\"),\n  mainActiveIndex: makeNumericProp(0),\n  activeId: {\n    type: [Number, String, Array],\n    default: 0\n  }\n};\nvar stdin_default = defineComponent({\n  name: name,\n  props: treeSelectProps,\n  emits: [\"click-nav\", \"click-item\", \"update:activeId\", \"update:mainActiveIndex\"],\n  setup: function setup(props, _ref) {\n    var emit = _ref.emit,\n      slots = _ref.slots;\n    var isActiveItem = function isActiveItem(id) {\n      return Array.isArray(props.activeId) ? props.activeId.includes(id) : props.activeId === id;\n    };\n    var renderSubItem = function renderSubItem(item) {\n      var onClick = function onClick() {\n        if (item.disabled) {\n          return;\n        }\n        var activeId;\n        if (Array.isArray(props.activeId)) {\n          activeId = props.activeId.slice();\n          var index = activeId.indexOf(item.id);\n          if (index !== -1) {\n            activeId.splice(index, 1);\n          } else if (activeId.length < props.max) {\n            activeId.push(item.id);\n          }\n        } else {\n          activeId = item.id;\n        }\n        emit(\"update:activeId\", activeId);\n        emit(\"click-item\", item);\n      };\n      return _createVNode(\"div\", {\n        \"key\": item.id,\n        \"class\": [\"van-ellipsis\", bem(\"item\", {\n          active: isActiveItem(item.id),\n          disabled: item.disabled\n        })],\n        \"onClick\": onClick\n      }, [item.text, isActiveItem(item.id) && _createVNode(Icon, {\n        \"name\": props.selectedIcon,\n        \"class\": bem(\"selected\")\n      }, null)]);\n    };\n    var onSidebarChange = function onSidebarChange(index) {\n      emit(\"update:mainActiveIndex\", index);\n    };\n    var onClickSidebarItem = function onClickSidebarItem(index) {\n      return emit(\"click-nav\", index);\n    };\n    var renderSidebar = function renderSidebar() {\n      var Items = props.items.map(function (item) {\n        return _createVNode(SidebarItem, {\n          \"dot\": item.dot,\n          \"title\": item.text,\n          \"badge\": item.badge,\n          \"class\": [bem(\"nav-item\"), item.className],\n          \"disabled\": item.disabled,\n          \"onClick\": onClickSidebarItem\n        }, null);\n      });\n      return _createVNode(Sidebar, {\n        \"class\": bem(\"nav\"),\n        \"modelValue\": props.mainActiveIndex,\n        \"onChange\": onSidebarChange\n      }, {\n        default: function _default() {\n          return [Items];\n        }\n      });\n    };\n    var renderContent = function renderContent() {\n      if (slots.content) {\n        return slots.content();\n      }\n      var selected = props.items[+props.mainActiveIndex] || {};\n      if (selected.children) {\n        return selected.children.map(renderSubItem);\n      }\n    };\n    return function () {\n      return _createVNode(\"div\", {\n        \"class\": bem(),\n        \"style\": {\n          height: addUnit(props.height)\n        }\n      }, [renderSidebar(), _createVNode(\"div\", {\n        \"class\": bem(\"content\")\n      }, [renderContent()])]);\n    };\n  }\n});\nexport { stdin_default as default };", "map": {"version": 3, "names": ["createVNode", "_createVNode", "defineComponent", "addUnit", "makeArrayProp", "makeStringProp", "makeNumericProp", "createNamespace", "Icon", "Sidebar", "SidebarItem", "_createNamespace", "_createNamespace2", "_slicedToArray", "name", "bem", "treeSelectProps", "max", "Infinity", "items", "height", "selectedIcon", "mainActiveIndex", "activeId", "type", "Number", "String", "Array", "default", "stdin_default", "props", "emits", "setup", "_ref", "emit", "slots", "isActiveItem", "id", "isArray", "includes", "renderSubItem", "item", "onClick", "disabled", "slice", "index", "indexOf", "splice", "length", "push", "active", "text", "onSidebarChange", "onClickSidebarItem", "renderSidebar", "Items", "map", "dot", "badge", "className", "_default", "renderContent", "content", "selected", "children"], "sources": ["C:/Users/<USER>/Desktop/vue3_3.0/node_modules/vant/es/tree-select/TreeSelect.mjs"], "sourcesContent": ["import { createVNode as _createVNode } from \"vue\";\nimport { defineComponent } from \"vue\";\nimport { addUnit, makeArrayProp, makeStringProp, makeNumericProp, createNamespace } from \"../utils/index.mjs\";\nimport { Icon } from \"../icon/index.mjs\";\nimport { Sidebar } from \"../sidebar/index.mjs\";\nimport { SidebarItem } from \"../sidebar-item/index.mjs\";\nconst [name, bem] = createNamespace(\"tree-select\");\nconst treeSelectProps = {\n  max: makeNumericProp(Infinity),\n  items: makeArrayProp(),\n  height: makeNumericProp(300),\n  selectedIcon: makeStringProp(\"success\"),\n  mainActiveIndex: makeNumericProp(0),\n  activeId: {\n    type: [Number, String, Array],\n    default: 0\n  }\n};\nvar stdin_default = defineComponent({\n  name,\n  props: treeSelectProps,\n  emits: [\"click-nav\", \"click-item\", \"update:activeId\", \"update:mainActiveIndex\"],\n  setup(props, {\n    emit,\n    slots\n  }) {\n    const isActiveItem = (id) => Array.isArray(props.activeId) ? props.activeId.includes(id) : props.activeId === id;\n    const renderSubItem = (item) => {\n      const onClick = () => {\n        if (item.disabled) {\n          return;\n        }\n        let activeId;\n        if (Array.isArray(props.activeId)) {\n          activeId = props.activeId.slice();\n          const index = activeId.indexOf(item.id);\n          if (index !== -1) {\n            activeId.splice(index, 1);\n          } else if (activeId.length < props.max) {\n            activeId.push(item.id);\n          }\n        } else {\n          activeId = item.id;\n        }\n        emit(\"update:activeId\", activeId);\n        emit(\"click-item\", item);\n      };\n      return _createVNode(\"div\", {\n        \"key\": item.id,\n        \"class\": [\"van-ellipsis\", bem(\"item\", {\n          active: isActiveItem(item.id),\n          disabled: item.disabled\n        })],\n        \"onClick\": onClick\n      }, [item.text, isActiveItem(item.id) && _createVNode(Icon, {\n        \"name\": props.selectedIcon,\n        \"class\": bem(\"selected\")\n      }, null)]);\n    };\n    const onSidebarChange = (index) => {\n      emit(\"update:mainActiveIndex\", index);\n    };\n    const onClickSidebarItem = (index) => emit(\"click-nav\", index);\n    const renderSidebar = () => {\n      const Items = props.items.map((item) => _createVNode(SidebarItem, {\n        \"dot\": item.dot,\n        \"title\": item.text,\n        \"badge\": item.badge,\n        \"class\": [bem(\"nav-item\"), item.className],\n        \"disabled\": item.disabled,\n        \"onClick\": onClickSidebarItem\n      }, null));\n      return _createVNode(Sidebar, {\n        \"class\": bem(\"nav\"),\n        \"modelValue\": props.mainActiveIndex,\n        \"onChange\": onSidebarChange\n      }, {\n        default: () => [Items]\n      });\n    };\n    const renderContent = () => {\n      if (slots.content) {\n        return slots.content();\n      }\n      const selected = props.items[+props.mainActiveIndex] || {};\n      if (selected.children) {\n        return selected.children.map(renderSubItem);\n      }\n    };\n    return () => _createVNode(\"div\", {\n      \"class\": bem(),\n      \"style\": {\n        height: addUnit(props.height)\n      }\n    }, [renderSidebar(), _createVNode(\"div\", {\n      \"class\": bem(\"content\")\n    }, [renderContent()])]);\n  }\n});\nexport {\n  stdin_default as default\n};\n"], "mappings": ";;;;;;AAAA,SAASA,WAAW,IAAIC,YAAY,QAAQ,KAAK;AACjD,SAASC,eAAe,QAAQ,KAAK;AACrC,SAASC,OAAO,EAAEC,aAAa,EAAEC,cAAc,EAAEC,eAAe,EAAEC,eAAe,QAAQ,oBAAoB;AAC7G,SAASC,IAAI,QAAQ,mBAAmB;AACxC,SAASC,OAAO,QAAQ,sBAAsB;AAC9C,SAASC,WAAW,QAAQ,2BAA2B;AACvD,IAAAC,gBAAA,GAAoBJ,eAAe,CAAC,aAAa,CAAC;EAAAK,iBAAA,GAAAC,cAAA,CAAAF,gBAAA;EAA3CG,IAAI,GAAAF,iBAAA;EAAEG,GAAG,GAAAH,iBAAA;AAChB,IAAMI,eAAe,GAAG;EACtBC,GAAG,EAAEX,eAAe,CAACY,QAAQ,CAAC;EAC9BC,KAAK,EAAEf,aAAa,CAAC,CAAC;EACtBgB,MAAM,EAAEd,eAAe,CAAC,GAAG,CAAC;EAC5Be,YAAY,EAAEhB,cAAc,CAAC,SAAS,CAAC;EACvCiB,eAAe,EAAEhB,eAAe,CAAC,CAAC,CAAC;EACnCiB,QAAQ,EAAE;IACRC,IAAI,EAAE,CAACC,MAAM,EAAEC,MAAM,EAAEC,KAAK,CAAC;IAC7BC,OAAO,EAAE;EACX;AACF,CAAC;AACD,IAAIC,aAAa,GAAG3B,eAAe,CAAC;EAClCY,IAAI,EAAJA,IAAI;EACJgB,KAAK,EAAEd,eAAe;EACtBe,KAAK,EAAE,CAAC,WAAW,EAAE,YAAY,EAAE,iBAAiB,EAAE,wBAAwB,CAAC;EAC/EC,KAAK,WAAAA,MAACF,KAAK,EAAAG,IAAA,EAGR;IAAA,IAFDC,IAAI,GAAAD,IAAA,CAAJC,IAAI;MACJC,KAAK,GAAAF,IAAA,CAALE,KAAK;IAEL,IAAMC,YAAY,GAAG,SAAfA,YAAYA,CAAIC,EAAE;MAAA,OAAKV,KAAK,CAACW,OAAO,CAACR,KAAK,CAACP,QAAQ,CAAC,GAAGO,KAAK,CAACP,QAAQ,CAACgB,QAAQ,CAACF,EAAE,CAAC,GAAGP,KAAK,CAACP,QAAQ,KAAKc,EAAE;IAAA;IAChH,IAAMG,aAAa,GAAG,SAAhBA,aAAaA,CAAIC,IAAI,EAAK;MAC9B,IAAMC,OAAO,GAAG,SAAVA,OAAOA,CAAA,EAAS;QACpB,IAAID,IAAI,CAACE,QAAQ,EAAE;UACjB;QACF;QACA,IAAIpB,QAAQ;QACZ,IAAII,KAAK,CAACW,OAAO,CAACR,KAAK,CAACP,QAAQ,CAAC,EAAE;UACjCA,QAAQ,GAAGO,KAAK,CAACP,QAAQ,CAACqB,KAAK,CAAC,CAAC;UACjC,IAAMC,KAAK,GAAGtB,QAAQ,CAACuB,OAAO,CAACL,IAAI,CAACJ,EAAE,CAAC;UACvC,IAAIQ,KAAK,KAAK,CAAC,CAAC,EAAE;YAChBtB,QAAQ,CAACwB,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;UAC3B,CAAC,MAAM,IAAItB,QAAQ,CAACyB,MAAM,GAAGlB,KAAK,CAACb,GAAG,EAAE;YACtCM,QAAQ,CAAC0B,IAAI,CAACR,IAAI,CAACJ,EAAE,CAAC;UACxB;QACF,CAAC,MAAM;UACLd,QAAQ,GAAGkB,IAAI,CAACJ,EAAE;QACpB;QACAH,IAAI,CAAC,iBAAiB,EAAEX,QAAQ,CAAC;QACjCW,IAAI,CAAC,YAAY,EAAEO,IAAI,CAAC;MAC1B,CAAC;MACD,OAAOxC,YAAY,CAAC,KAAK,EAAE;QACzB,KAAK,EAAEwC,IAAI,CAACJ,EAAE;QACd,OAAO,EAAE,CAAC,cAAc,EAAEtB,GAAG,CAAC,MAAM,EAAE;UACpCmC,MAAM,EAAEd,YAAY,CAACK,IAAI,CAACJ,EAAE,CAAC;UAC7BM,QAAQ,EAAEF,IAAI,CAACE;QACjB,CAAC,CAAC,CAAC;QACH,SAAS,EAAED;MACb,CAAC,EAAE,CAACD,IAAI,CAACU,IAAI,EAAEf,YAAY,CAACK,IAAI,CAACJ,EAAE,CAAC,IAAIpC,YAAY,CAACO,IAAI,EAAE;QACzD,MAAM,EAAEsB,KAAK,CAACT,YAAY;QAC1B,OAAO,EAAEN,GAAG,CAAC,UAAU;MACzB,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;IACZ,CAAC;IACD,IAAMqC,eAAe,GAAG,SAAlBA,eAAeA,CAAIP,KAAK,EAAK;MACjCX,IAAI,CAAC,wBAAwB,EAAEW,KAAK,CAAC;IACvC,CAAC;IACD,IAAMQ,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAIR,KAAK;MAAA,OAAKX,IAAI,CAAC,WAAW,EAAEW,KAAK,CAAC;IAAA;IAC9D,IAAMS,aAAa,GAAG,SAAhBA,aAAaA,CAAA,EAAS;MAC1B,IAAMC,KAAK,GAAGzB,KAAK,CAACX,KAAK,CAACqC,GAAG,CAAC,UAACf,IAAI;QAAA,OAAKxC,YAAY,CAACS,WAAW,EAAE;UAChE,KAAK,EAAE+B,IAAI,CAACgB,GAAG;UACf,OAAO,EAAEhB,IAAI,CAACU,IAAI;UAClB,OAAO,EAAEV,IAAI,CAACiB,KAAK;UACnB,OAAO,EAAE,CAAC3C,GAAG,CAAC,UAAU,CAAC,EAAE0B,IAAI,CAACkB,SAAS,CAAC;UAC1C,UAAU,EAAElB,IAAI,CAACE,QAAQ;UACzB,SAAS,EAAEU;QACb,CAAC,EAAE,IAAI,CAAC;MAAA,EAAC;MACT,OAAOpD,YAAY,CAACQ,OAAO,EAAE;QAC3B,OAAO,EAAEM,GAAG,CAAC,KAAK,CAAC;QACnB,YAAY,EAAEe,KAAK,CAACR,eAAe;QACnC,UAAU,EAAE8B;MACd,CAAC,EAAE;QACDxB,OAAO,EAAE,SAAAgC,SAAA;UAAA,OAAM,CAACL,KAAK,CAAC;QAAA;MACxB,CAAC,CAAC;IACJ,CAAC;IACD,IAAMM,aAAa,GAAG,SAAhBA,aAAaA,CAAA,EAAS;MAC1B,IAAI1B,KAAK,CAAC2B,OAAO,EAAE;QACjB,OAAO3B,KAAK,CAAC2B,OAAO,CAAC,CAAC;MACxB;MACA,IAAMC,QAAQ,GAAGjC,KAAK,CAACX,KAAK,CAAC,CAACW,KAAK,CAACR,eAAe,CAAC,IAAI,CAAC,CAAC;MAC1D,IAAIyC,QAAQ,CAACC,QAAQ,EAAE;QACrB,OAAOD,QAAQ,CAACC,QAAQ,CAACR,GAAG,CAAChB,aAAa,CAAC;MAC7C;IACF,CAAC;IACD,OAAO;MAAA,OAAMvC,YAAY,CAAC,KAAK,EAAE;QAC/B,OAAO,EAAEc,GAAG,CAAC,CAAC;QACd,OAAO,EAAE;UACPK,MAAM,EAAEjB,OAAO,CAAC2B,KAAK,CAACV,MAAM;QAC9B;MACF,CAAC,EAAE,CAACkC,aAAa,CAAC,CAAC,EAAErD,YAAY,CAAC,KAAK,EAAE;QACvC,OAAO,EAAEc,GAAG,CAAC,SAAS;MACxB,CAAC,EAAE,CAAC8C,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAAA;EACzB;AACF,CAAC,CAAC;AACF,SACEhC,aAAa,IAAID,OAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}