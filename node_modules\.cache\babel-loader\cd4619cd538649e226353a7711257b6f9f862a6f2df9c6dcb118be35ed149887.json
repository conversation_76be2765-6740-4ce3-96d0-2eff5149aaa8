{"ast": null, "code": "var lockCount = 0;\nfunction lockClick(lock) {\n  if (lock) {\n    if (!lockCount) {\n      document.body.classList.add(\"van-toast--unclickable\");\n    }\n    lockCount++;\n  } else if (lockCount) {\n    lockCount--;\n    if (!lockCount) {\n      document.body.classList.remove(\"van-toast--unclickable\");\n    }\n  }\n}\nexport { lockClick };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}