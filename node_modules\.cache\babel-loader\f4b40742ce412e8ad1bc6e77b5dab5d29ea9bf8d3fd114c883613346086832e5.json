{"ast": null, "code": "function _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return _typeof(key) === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (_typeof(input) !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (_typeof(res) !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\nimport { ref, getCurrentInstance } from 'vue';\nimport { get_invite } from '@/api/self/index';\nimport { useRouter } from 'vue-router';\nimport { useI18n } from 'vue-i18n';\nimport QRCode from 'qrcodejs2';\n// import {getdetailbyid} from '@/api/home/<USER>'\n// 复制函数\nimport useClipboard from 'vue-clipboard3';\nvar url = '';\nexport default {\n  name: 'ShareInvite',\n  setup: function setup(props, ctx) {\n    var _getCurrentInstance = getCurrentInstance(),\n      proxy = _getCurrentInstance.proxy;\n    var _useI18n = useI18n(),\n      t = _useI18n.t;\n    var _useClipboard = useClipboard(),\n      toClipboard = _useClipboard.toClipboard;\n    var _useRouter = useRouter(),\n      push = _useRouter.push;\n    var a_content = ref('');\n    var info = ref({});\n    ctx.emit('hideFooter', true);\n    var clickLeft = function clickLeft() {\n      push('/self');\n    };\n\n    // 获取当前域名\n    var getCurrentDomain = function getCurrentDomain() {\n      // 尝试获取当前完整域名\n      var domain = '';\n      try {\n        var _window$document;\n        console.log('window对象:', typeof window !== 'undefined');\n        console.log('document对象:', window.document ? '存在' : '不存在');\n        console.log('location对象:', (_window$document = window.document) !== null && _window$document !== void 0 && _window$document.location ? '存在' : '不存在');\n\n        // 优先使用document.location.origin获取完整域名\n        if (typeof window !== 'undefined' && window.document && window.document.location) {\n          domain = window.document.location.origin;\n          console.log('通过window.document.location.origin获取的域名:', domain);\n        }\n\n        // 如果获取不到，使用配置中的API域名\n        if (!domain && window.config && window.config.api) {\n          // 从API URL中提取域名\n          var apiUrl = window.config.api;\n          console.log('配置中的API URL:', apiUrl);\n          var urlParts = apiUrl.split('/');\n          if (urlParts.length >= 3) {\n            domain = urlParts[0] + '//' + urlParts[2].split('/')[0];\n            console.log('从API URL提取的域名:', domain);\n          }\n        }\n\n        // 如果仍然获取不到，使用备用域名\n        if (!domain) {\n          domain = 'https://admin.tk-mall.cc';\n          console.log('使用备用域名:', domain);\n        }\n      } catch (e) {\n        console.error('获取域名失败', e);\n        domain = 'https://admin.tk-mall.cc'; // 备用域名\n      }\n\n      console.log('最终使用的域名:', domain);\n      return domain;\n    };\n\n    // 修复URL格式\n    var fixUrl = function fixUrl(inputUrl) {\n      if (!inputUrl) return '';\n\n      // 处理 https:///register?invite_code=xxx 这种三斜杠的情况\n      if (inputUrl.startsWith('https:///') || inputUrl.startsWith('http:///')) {\n        var baseUrl = getCurrentDomain();\n        var path = inputUrl.split('///')[1];\n        return baseUrl + '/' + path;\n      }\n\n      // 如果不是以http开头，添加域名\n      if (!inputUrl.startsWith('http')) {\n        var _baseUrl = getCurrentDomain();\n        return _baseUrl + (inputUrl.startsWith('/') ? '' : '/') + inputUrl;\n      }\n      return inputUrl;\n    };\n    get_invite().then(function (res) {\n      var _res$data;\n      info.value = _objectSpread({}, res.data);\n      console.log('获取到的邀请信息:', info.value);\n      a_content.value = (_res$data = res.data) === null || _res$data === void 0 ? void 0 : _res$data.invite_msg;\n\n      // 确保URL包含完整网址\n      if (info.value.url) {\n        console.log('原始URL:', info.value.url);\n        info.value.url = fixUrl(info.value.url);\n        console.log('修复后的URL:', info.value.url);\n      } else {\n        console.log('邀请信息中没有url字段');\n        var baseUrl = getCurrentDomain();\n        info.value.url = baseUrl + '/register?invite_code=' + info.value.invite_code;\n        console.log('新创建的URL:', info.value.url);\n      }\n      url = info.value.url;\n      console.log('设置全局url变量:', url);\n    });\n    // getdetailbyid(1).then(res => {\n    // })\n    var copy = function copy(value) {\n      try {\n        console.log('复制函数被调用，原始值:', value);\n        var copyValue = value;\n        if (!value) {\n          console.log('复制的值为空，使用邀请码创建链接');\n          var baseUrl = getCurrentDomain();\n          copyValue = baseUrl + '/register?invite_code=' + info.value.invite_code;\n        } else {\n          // 修复URL格式\n          copyValue = fixUrl(value);\n        }\n        console.log('最终复制的值:', copyValue);\n        toClipboard(copyValue);\n        proxy.$Message({\n          type: 'success',\n          message: t('msg.copy_s')\n        });\n      } catch (e) {\n        console.error('复制失败:', e);\n        proxy.$Message({\n          type: 'error',\n          message: t('msg.copy_b')\n        });\n      }\n    };\n\n    // new QRCode(this.$refs.qrCodeUrl, {\n    //     text: '#qrcode', // 需要转换为二维码的内容\n    //     width: 100,\n    //     height: 100,\n    //     colorDark: '#000000',\n    //     colorLight: '#ffffff',\n    //     correctLevel: QRCode.CorrectLevel.H\n    // })\n\n    return {\n      clickLeft: clickLeft,\n      a_content: a_content,\n      copy: copy,\n      info: info,\n      getCurrentDomain: getCurrentDomain,\n      fixUrl: fixUrl\n    };\n  },\n  mounted: function mounted() {\n    var taht = this;\n    console.log('mounted钩子执行');\n    var dsq = setInterval(function () {\n      console.log('检查url变量:', url);\n      if (url != '') {\n        console.log('url不为空，开始生成二维码');\n        // 确保二维码链接包含完整网址\n        var qrUrl = url;\n        console.log('原始二维码URL:', qrUrl);\n\n        // 修复URL格式\n        qrUrl = taht.fixUrl(qrUrl);\n        console.log('修复后的二维码URL:', qrUrl);\n        console.log('生成二维码，内容:', qrUrl);\n        new QRCode(taht.$refs.qrCodeUrl, {\n          text: qrUrl,\n          // 二维码的内容\n          width: 150,\n          height: 150,\n          colorDark: '#000',\n          colorLight: '#fff',\n          correctLevel: QRCode.CorrectLevel.H\n        });\n        console.log('二维码生成完成');\n        clearInterval(dsq);\n      }\n    }, 500);\n  }\n};", "map": {"version": 3, "names": ["ref", "getCurrentInstance", "get_invite", "useRouter", "useI18n", "QRCode", "useClipboard", "url", "name", "setup", "props", "ctx", "_getCurrentInstance", "proxy", "_useI18n", "t", "_useClipboard", "toClipboard", "_useRouter", "push", "a_content", "info", "emit", "clickLeft", "getCurrentDomain", "domain", "_window$document", "console", "log", "window", "document", "location", "origin", "config", "api", "apiUrl", "urlParts", "split", "length", "e", "error", "fixUrl", "inputUrl", "startsWith", "baseUrl", "path", "then", "res", "_res$data", "value", "_objectSpread", "data", "invite_msg", "invite_code", "copy", "copyValue", "$Message", "type", "message", "mounted", "taht", "dsq", "setInterval", "qrUrl", "$refs", "qrCodeUrl", "text", "width", "height", "colorDark", "colorLight", "correctLevel", "CorrectLevel", "H", "clearInterval"], "sources": ["C:\\Users\\<USER>\\Downloads\\Telegram Desktop\\vue3_3.0\\vue3_3.0\\src\\views\\self\\components\\share.vue"], "sourcesContent": ["<template>\n    <div class=\"tel home\">\n\t\t<van-nav-bar\n\t\t     :title=\"$t('msg.code')\"\n\t\t\t background=\"#ffffff\"\n\t\t\t title-style=\"color:black; font-size: 16px;\"\n\t\t\t left-arrow \n\t\t\t @click-left=\"$router.go(-1)\"\n\t\t>\n\t\t</van-nav-bar>\n\t\t<div class=\"box\">\n\t\t\t<div class=\"box_t\">\n\t\t\t\t<div class=\"box_tlt\">\n\t\t\t\t\t{{$t('msg.code')}} :&nbsp;<span>{{info.invite_code}}</span><img  :src=\"require('@/assets/images/copy.svg')\" alt=\"\" @click=\"copy(info?.url)\">\n                    \n\t\t\t\t</div>\n\t\t\t\t<div class=\"box_fot\">\n\t\t\t\t\t<div class=\"box_text\">{{$t('msg.yqhylqyj')}}</div>\n\t\t\t\t\t<div class=\"qr-code qr\" ref=\"qrCodeUrl\"></div>\n\t\t\t\t</div>\n\n \n\n\t\t\t\t<div class=\"cop\" @click=\"copy(info?.url)\">\n\t\t\t\t\t{{$t('msg.fzyqlj')}}\n\t\t\t\t</div>\n\t\t\t</div>\n\t\t</div>\n\t\t\n\t\t\n\t\t\n\t\t\n\t\t\n\t\t\n\t\t<!--  \n        <div class=\"yqhy\">{{$t('msg.yqhylqyj')}}</div>\n        <div class=\"content\">\n            <div class=\"top\">\n                <div class=\"title\">\n                    <img :src=\"require('@/assets/images/news/money.png')\" alt=\"\" class=\"img\" width=\"20\">\n                    <img :src=\"require('@/assets/images/news/money.png')\" alt=\"\" class=\"img\" width=\"20\">\n                    <img :src=\"require('@/assets/images/news/money.png')\" alt=\"\" class=\"img\" width=\"20\">\n                </div>\n                <div class=\"c\" v-html=\"a_content\"></div>\n                <div class=\"b\">\n                    {{$t('msg.tgm')}} <span class=\"span\">{{info.invite_code}}</span>\n                </div>\n                <div class=\"bottom\" @click=\"copy(info?.url)\">{{$t('msg.fzyqlj')}}</div>\n            </div>\n        </div>\n\t\t-->\n    </div> \n</template>\n<script>\nimport { ref, getCurrentInstance } from 'vue';\nimport { get_invite } from '@/api/self/index'\nimport { useRouter } from 'vue-router';\nimport { useI18n } from 'vue-i18n'\n\nimport QRCode from 'qrcodejs2';\n// import {getdetailbyid} from '@/api/home/<USER>'\n// 复制函数\nimport useClipboard from 'vue-clipboard3';\nvar url = '';\nexport default {\n    name: 'ShareInvite',\n    setup(props, ctx){\n        const {proxy} = getCurrentInstance()\n        const { t } = useI18n()\n        const { toClipboard } = useClipboard();\n        const { push } = useRouter();\n        const a_content = ref('')\n        const info = ref({})\n        ctx.emit('hideFooter',true)\n        const clickLeft = () => {\n            push('/self')\n        }\n        \n        // 获取当前域名\n        const getCurrentDomain = () => {\n            // 尝试获取当前完整域名\n            let domain = '';\n            try {\n                console.log('window对象:', typeof window !== 'undefined');\n                console.log('document对象:', window.document ? '存在' : '不存在');\n                console.log('location对象:', window.document?.location ? '存在' : '不存在');\n                \n                // 优先使用document.location.origin获取完整域名\n                if (typeof window !== 'undefined' && window.document && window.document.location) {\n                    domain = window.document.location.origin;\n                    console.log('通过window.document.location.origin获取的域名:', domain);\n                }\n                \n                // 如果获取不到，使用配置中的API域名\n                if (!domain && window.config && window.config.api) {\n                    // 从API URL中提取域名\n                    const apiUrl = window.config.api;\n                    console.log('配置中的API URL:', apiUrl);\n                    const urlParts = apiUrl.split('/');\n                    if (urlParts.length >= 3) {\n                        domain = urlParts[0] + '//' + urlParts[2].split('/')[0];\n                        console.log('从API URL提取的域名:', domain);\n                    }\n                }\n                \n                // 如果仍然获取不到，使用备用域名\n                if (!domain) {\n                    domain = 'https://admin.tk-mall.cc';\n                    console.log('使用备用域名:', domain);\n                }\n            } catch (e) {\n                console.error('获取域名失败', e);\n                domain = 'https://admin.tk-mall.cc'; // 备用域名\n            }\n            \n            console.log('最终使用的域名:', domain);\n            return domain;\n        };\n        \n        // 修复URL格式\n        const fixUrl = (inputUrl) => {\n            if (!inputUrl) return '';\n            \n            // 处理 https:///register?invite_code=xxx 这种三斜杠的情况\n            if (inputUrl.startsWith('https:///') || inputUrl.startsWith('http:///')) {\n                const baseUrl = getCurrentDomain();\n                const path = inputUrl.split('///')[1];\n                return baseUrl + '/' + path;\n            }\n            \n            // 如果不是以http开头，添加域名\n            if (!inputUrl.startsWith('http')) {\n                const baseUrl = getCurrentDomain();\n                return baseUrl + (inputUrl.startsWith('/') ? '' : '/') + inputUrl;\n            }\n            \n            return inputUrl;\n        };\n        \n        get_invite().then(res => {\n           \n            info.value = {...res.data}\n            console.log('获取到的邀请信息:', info.value);\n            a_content.value = res.data?.invite_msg\n            \n            // 确保URL包含完整网址\n            if (info.value.url) {\n                console.log('原始URL:', info.value.url);\n                info.value.url = fixUrl(info.value.url);\n                console.log('修复后的URL:', info.value.url);\n            } else {\n                console.log('邀请信息中没有url字段');\n                const baseUrl = getCurrentDomain();\n                info.value.url = baseUrl + '/register?invite_code=' + info.value.invite_code;\n                console.log('新创建的URL:', info.value.url);\n            }\n            \n            url = info.value.url;\n            console.log('设置全局url变量:', url);\n        })\n        // getdetailbyid(1).then(res => {\n        // })\n        const copy = (value) => {\n            try {\n                console.log('复制函数被调用，原始值:', value);\n                \n                let copyValue = value;\n                if (!value) {\n                    console.log('复制的值为空，使用邀请码创建链接');\n                    const baseUrl = getCurrentDomain();\n                    copyValue = baseUrl + '/register?invite_code=' + info.value.invite_code;\n                } else {\n                    // 修复URL格式\n                    copyValue = fixUrl(value);\n                }\n                \n                console.log('最终复制的值:', copyValue);\n                toClipboard(copyValue);\n                proxy.$Message({ type: 'success', message:t('msg.copy_s')});\n            } catch (e) {\n                console.error('复制失败:', e);\n                proxy.$Message({ type: 'error', message:t('msg.copy_b')});\n            }\n        }\n\n        // new QRCode(this.$refs.qrCodeUrl, {\n        //     text: '#qrcode', // 需要转换为二维码的内容\n        //     width: 100,\n        //     height: 100,\n        //     colorDark: '#000000',\n        //     colorLight: '#ffffff',\n        //     correctLevel: QRCode.CorrectLevel.H\n        // })\n\n        \n\n        return {clickLeft,a_content,copy,info,getCurrentDomain,fixUrl}\n    },\n\n    mounted() {\n        let taht = this\n        console.log('mounted钩子执行');\n        \n        let dsq = setInterval(function(){\n            console.log('检查url变量:', url);\n            if(url !=''){\n                console.log('url不为空，开始生成二维码');\n                // 确保二维码链接包含完整网址\n                let qrUrl = url;\n                console.log('原始二维码URL:', qrUrl);\n                \n                // 修复URL格式\n                qrUrl = taht.fixUrl(qrUrl);\n                console.log('修复后的二维码URL:', qrUrl);\n                \n                console.log('生成二维码，内容:', qrUrl);\n                new QRCode(taht.$refs.qrCodeUrl, {\n                    text: qrUrl, // 二维码的内容\n                    width: 150,\n                    height: 150,\n                    colorDark: '#000',\n                    colorLight: '#fff',\n                    correctLevel: QRCode.CorrectLevel.H\n                });\n                console.log('二维码生成完成');\n                clearInterval(dsq)\n            }\n        },500)\n        \n        \n    }\n\n}\n\n</script>\n\n<style lang=\"scss\" scoped>\n\n.qr img{\n    width: 100px !important;\n    height: 100px !important;\n}\n\t.box_tlt{\n\t\theight: 110px !important;\n\t\tpadding-left: 30px;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: left;\n\t\tcolor: #ffffff;\n\t\timg{\n\t\t\twidth: 40px;\n\t\t\theight: 40px;\n\t\t\tmargin-left: 30px;\n\t\t}\n\t}\n.qr{\n\tmargin: 25px 0;\n\twidth: 400px;\n\theight: 400px;\n\tbackground-image: url(\"~@/assets/images/home/<USER>\");\n\tbackground-size: cover;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n}\n.box_text{\n\tdisplay: block;\n\tfont-size: 25px;\n\tfont-weight: 800;\n\tpadding: 50px 20px;\n}\n.cop{\n\tmargin: 0 0 15px;\n    width: 70%;\n    height: 70px;\n    background-color: #000;\n    color: #fff;\n    border-radius: 20px;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    margin: 50px auto;\n}\n.box{\n\tdisplay: block;\n\tbox-sizing: border-box;\n\twidth: 100%;\n\tpadding: 15px;\n\t.box_t{\n\t\twidth: 88%;\n\t\theight: auto;\n\t\tbackground-color: #fff;\n\t\tborder-radius: 25px;\n\t\toverflow: hidden;\n\t\tbox-shadow: rgba(41,5,5,.15) 0 0 20px;\n\t\toverflow: hidden;\n\t\tmargin:  0 auto;\n\t\t.box_tlt{\n\t\t\twidth: 100%;\n\t\t\theight: 60px;\n\t\t\tbackground-color: #000;\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tcolor: #fff;\n\t\t}\n\t\t.box_fot{\n\t\t\tpadding: 0 20px;\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tjustify-content: space-around;\n\t\t\tflex-direction: column;\n\t\t}\n\t}\n}\n\n.tel{\n    overflow: hidden;\n    // background-image: url(\"~@/assets/images/home/<USER>\");\n    // background-size: 100% 100%;\n    position: relative;\n\tbackground-color: #f5f5f5;\n    .yqhy {\n        height: 400px;\n        width: 100%;\n        background-color: $theme;\n        display: flex;\n        flex-direction: column;\n        justify-content: center;\n        color: #fff;\n        font-size: 50px;\n        padding-top: 60px;\n    }\n    :deep(.van-nav-bar){\n\t\tpadding: 0;\n\t\tbackground-color: #ffffff;\n        position: absolute !important;\n        width: 100%;\n        background-color: inherit;\n        color: #000000;\n        z-index: 0;\n\t\t.van-nav-bar__content{\n\t\t\tbackground-color: #ffffff;\n\t\t}\n\t\t.van-nav-bar__left{\n\t\t\t\n\t\t\t.van-icon{\n\t\t\t\tcolor: #000000 !important;\n\t\t\t}\n\t\t}\n        .van-nav-bar__left{\n            .van-icon{\n                color: #fff;\n            }\n        }\n        .van-nav-bar__title{\n            color: #060606;\n        }\n        .van-nav-bar__right{\n            .van-icon{\n                color: #fff;\n            }\n        }\n    }\n    .content{\n        width: 100%;\n        padding: 0 25px;\n        border-radius: 30px;\n        flex: 1;\n        overflow: auto;\n        // background-color: #fff;\n        .bottom{\n            width: 100%;\n            height: 74px;\n            line-height: 74px;\n            font-size: 32px;\n            background-color: $theme;\n            color: #fff;\n            border-radius: 6px;\n            margin-top: 20px;\n        }\n        .top{\n            padding:20px;\n            background-color: #fff;\n            border-radius: 20px;\n            .title{\n                margin-bottom: 20px;\n            }\n            .c{\n                font-size: 22px;\n                line-height: 2;\n                text-indent: 2em;\n                color: #666;\n                margin-bottom: 20px;\n            }\n            .b{\n                width: 290px;\n                margin: 0 auto;\n                font-size: 26px;\n                color: #333;\n                position: relative;\n                .span{\n                    margin-left: 5px;\n                }\n            }\n        }\n    }\n}\n</style>"], "mappings": ";;;;;;AAsDA,SAASA,GAAG,EAAEC,kBAAiB,QAAS,KAAK;AAC7C,SAASC,UAAS,QAAS,kBAAiB;AAC5C,SAASC,SAAQ,QAAS,YAAY;AACtC,SAASC,OAAM,QAAS,UAAS;AAEjC,OAAOC,MAAK,MAAO,WAAW;AAC9B;AACA;AACA,OAAOC,YAAW,MAAO,gBAAgB;AACzC,IAAIC,GAAE,GAAI,EAAE;AACZ,eAAe;EACXC,IAAI,EAAE,aAAa;EACnBC,KAAK,WAAAA,MAACC,KAAK,EAAEC,GAAG,EAAC;IACb,IAAAC,mBAAA,GAAgBX,kBAAkB,CAAC;MAA5BY,KAAK,GAAAD,mBAAA,CAALC,KAAK;IACZ,IAAAC,QAAA,GAAcV,OAAO,CAAC;MAAdW,CAAA,GAAAD,QAAA,CAAAC,CAAA;IACR,IAAAC,aAAA,GAAwBV,YAAY,CAAC,CAAC;MAA9BW,WAAU,GAAAD,aAAA,CAAVC,WAAU;IAClB,IAAAC,UAAA,GAAiBf,SAAS,CAAC,CAAC;MAApBgB,IAAG,GAAAD,UAAA,CAAHC,IAAG;IACX,IAAMC,SAAQ,GAAIpB,GAAG,CAAC,EAAE;IACxB,IAAMqB,IAAG,GAAIrB,GAAG,CAAC,CAAC,CAAC;IACnBW,GAAG,CAACW,IAAI,CAAC,YAAY,EAAC,IAAI;IAC1B,IAAMC,SAAQ,GAAI,SAAZA,SAAQA,CAAA,EAAU;MACpBJ,IAAI,CAAC,OAAO;IAChB;;IAEA;IACA,IAAMK,gBAAe,GAAI,SAAnBA,gBAAeA,CAAA,EAAU;MAC3B;MACA,IAAIC,MAAK,GAAI,EAAE;MACf,IAAI;QAAA,IAAAC,gBAAA;QACAC,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE,OAAOC,MAAK,KAAM,WAAW,CAAC;QACvDF,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEC,MAAM,CAACC,QAAO,GAAI,IAAG,GAAI,KAAK,CAAC;QAC1DH,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE,CAAAF,gBAAA,GAAAG,MAAM,CAACC,QAAQ,cAAAJ,gBAAA,eAAfA,gBAAA,CAAiBK,QAAO,GAAI,IAAG,GAAI,KAAK,CAAC;;QAEpE;QACA,IAAI,OAAOF,MAAK,KAAM,WAAU,IAAKA,MAAM,CAACC,QAAO,IAAKD,MAAM,CAACC,QAAQ,CAACC,QAAQ,EAAE;UAC9EN,MAAK,GAAII,MAAM,CAACC,QAAQ,CAACC,QAAQ,CAACC,MAAM;UACxCL,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAEH,MAAM,CAAC;QAClE;;QAEA;QACA,IAAI,CAACA,MAAK,IAAKI,MAAM,CAACI,MAAK,IAAKJ,MAAM,CAACI,MAAM,CAACC,GAAG,EAAE;UAC/C;UACA,IAAMC,MAAK,GAAIN,MAAM,CAACI,MAAM,CAACC,GAAG;UAChCP,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEO,MAAM,CAAC;UACnC,IAAMC,QAAO,GAAID,MAAM,CAACE,KAAK,CAAC,GAAG,CAAC;UAClC,IAAID,QAAQ,CAACE,MAAK,IAAK,CAAC,EAAE;YACtBb,MAAK,GAAIW,QAAQ,CAAC,CAAC,IAAI,IAAG,GAAIA,QAAQ,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YACvDV,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEH,MAAM,CAAC;UACzC;QACJ;;QAEA;QACA,IAAI,CAACA,MAAM,EAAE;UACTA,MAAK,GAAI,0BAA0B;UACnCE,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEH,MAAM,CAAC;QAClC;MACJ,EAAE,OAAOc,CAAC,EAAE;QACRZ,OAAO,CAACa,KAAK,CAAC,QAAQ,EAAED,CAAC,CAAC;QAC1Bd,MAAK,GAAI,0BAA0B,EAAE;MACzC;;MAEAE,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEH,MAAM,CAAC;MAC/B,OAAOA,MAAM;IACjB,CAAC;;IAED;IACA,IAAMgB,MAAK,GAAI,SAATA,MAAKA,CAAKC,QAAQ,EAAK;MACzB,IAAI,CAACA,QAAQ,EAAE,OAAO,EAAE;;MAExB;MACA,IAAIA,QAAQ,CAACC,UAAU,CAAC,WAAW,KAAKD,QAAQ,CAACC,UAAU,CAAC,UAAU,CAAC,EAAE;QACrE,IAAMC,OAAM,GAAIpB,gBAAgB,CAAC,CAAC;QAClC,IAAMqB,IAAG,GAAIH,QAAQ,CAACL,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACrC,OAAOO,OAAM,GAAI,GAAE,GAAIC,IAAI;MAC/B;;MAEA;MACA,IAAI,CAACH,QAAQ,CAACC,UAAU,CAAC,MAAM,CAAC,EAAE;QAC9B,IAAMC,QAAM,GAAIpB,gBAAgB,CAAC,CAAC;QAClC,OAAOoB,QAAM,IAAKF,QAAQ,CAACC,UAAU,CAAC,GAAG,IAAI,EAAC,GAAI,GAAG,IAAID,QAAQ;MACrE;MAEA,OAAOA,QAAQ;IACnB,CAAC;IAEDxC,UAAU,CAAC,CAAC,CAAC4C,IAAI,CAAC,UAAAC,GAAE,EAAK;MAAA,IAAAC,SAAA;MAErB3B,IAAI,CAAC4B,KAAI,GAAAC,aAAA,KAAQH,GAAG,CAACI,IAAI;MACzBxB,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEP,IAAI,CAAC4B,KAAK,CAAC;MACpC7B,SAAS,CAAC6B,KAAI,IAAAD,SAAA,GAAID,GAAG,CAACI,IAAI,cAAAH,SAAA,uBAARA,SAAA,CAAUI,UAAS;;MAErC;MACA,IAAI/B,IAAI,CAAC4B,KAAK,CAAC1C,GAAG,EAAE;QAChBoB,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAEP,IAAI,CAAC4B,KAAK,CAAC1C,GAAG,CAAC;QACrCc,IAAI,CAAC4B,KAAK,CAAC1C,GAAE,GAAIkC,MAAM,CAACpB,IAAI,CAAC4B,KAAK,CAAC1C,GAAG,CAAC;QACvCoB,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEP,IAAI,CAAC4B,KAAK,CAAC1C,GAAG,CAAC;MAC3C,OAAO;QACHoB,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;QAC3B,IAAMgB,OAAM,GAAIpB,gBAAgB,CAAC,CAAC;QAClCH,IAAI,CAAC4B,KAAK,CAAC1C,GAAE,GAAIqC,OAAM,GAAI,wBAAuB,GAAIvB,IAAI,CAAC4B,KAAK,CAACI,WAAW;QAC5E1B,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEP,IAAI,CAAC4B,KAAK,CAAC1C,GAAG,CAAC;MAC3C;MAEAA,GAAE,GAAIc,IAAI,CAAC4B,KAAK,CAAC1C,GAAG;MACpBoB,OAAO,CAACC,GAAG,CAAC,YAAY,EAAErB,GAAG,CAAC;IAClC,CAAC;IACD;IACA;IACA,IAAM+C,IAAG,GAAI,SAAPA,IAAGA,CAAKL,KAAK,EAAK;MACpB,IAAI;QACAtB,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEqB,KAAK,CAAC;QAElC,IAAIM,SAAQ,GAAIN,KAAK;QACrB,IAAI,CAACA,KAAK,EAAE;UACRtB,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;UAC/B,IAAMgB,OAAM,GAAIpB,gBAAgB,CAAC,CAAC;UAClC+B,SAAQ,GAAIX,OAAM,GAAI,wBAAuB,GAAIvB,IAAI,CAAC4B,KAAK,CAACI,WAAW;QAC3E,OAAO;UACH;UACAE,SAAQ,GAAId,MAAM,CAACQ,KAAK,CAAC;QAC7B;QAEAtB,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE2B,SAAS,CAAC;QACjCtC,WAAW,CAACsC,SAAS,CAAC;QACtB1C,KAAK,CAAC2C,QAAQ,CAAC;UAAEC,IAAI,EAAE,SAAS;UAAEC,OAAO,EAAC3C,CAAC,CAAC,YAAY;QAAC,CAAC,CAAC;MAC/D,EAAE,OAAOwB,CAAC,EAAE;QACRZ,OAAO,CAACa,KAAK,CAAC,OAAO,EAAED,CAAC,CAAC;QACzB1B,KAAK,CAAC2C,QAAQ,CAAC;UAAEC,IAAI,EAAE,OAAO;UAAEC,OAAO,EAAC3C,CAAC,CAAC,YAAY;QAAC,CAAC,CAAC;MAC7D;IACJ;;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IAIA,OAAO;MAACQ,SAAS,EAATA,SAAS;MAACH,SAAS,EAATA,SAAS;MAACkC,IAAI,EAAJA,IAAI;MAACjC,IAAI,EAAJA,IAAI;MAACG,gBAAgB,EAAhBA,gBAAgB;MAACiB,MAAM,EAANA;IAAM;EACjE,CAAC;EAEDkB,OAAO,WAAAA,QAAA,EAAG;IACN,IAAIC,IAAG,GAAI,IAAG;IACdjC,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;IAE1B,IAAIiC,GAAE,GAAIC,WAAW,CAAC,YAAU;MAC5BnC,OAAO,CAACC,GAAG,CAAC,UAAU,EAAErB,GAAG,CAAC;MAC5B,IAAGA,GAAE,IAAI,EAAE,EAAC;QACRoB,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;QAC7B;QACA,IAAImC,KAAI,GAAIxD,GAAG;QACfoB,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEmC,KAAK,CAAC;;QAE/B;QACAA,KAAI,GAAIH,IAAI,CAACnB,MAAM,CAACsB,KAAK,CAAC;QAC1BpC,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEmC,KAAK,CAAC;QAEjCpC,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEmC,KAAK,CAAC;QAC/B,IAAI1D,MAAM,CAACuD,IAAI,CAACI,KAAK,CAACC,SAAS,EAAE;UAC7BC,IAAI,EAAEH,KAAK;UAAE;UACbI,KAAK,EAAE,GAAG;UACVC,MAAM,EAAE,GAAG;UACXC,SAAS,EAAE,MAAM;UACjBC,UAAU,EAAE,MAAM;UAClBC,YAAY,EAAElE,MAAM,CAACmE,YAAY,CAACC;QACtC,CAAC,CAAC;QACF9C,OAAO,CAACC,GAAG,CAAC,SAAS,CAAC;QACtB8C,aAAa,CAACb,GAAG;MACrB;IACJ,CAAC,EAAC,GAAG;EAGT;AAEJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}