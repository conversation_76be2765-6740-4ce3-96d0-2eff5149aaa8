{"ast": null, "code": "function _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return _typeof(key) === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (_typeof(input) !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (_typeof(res) !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\nfunction _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nfunction _iterableToArrayLimit(arr, i) { var _i = null == arr ? null : \"undefined\" != typeof Symbol && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (null != _i) { var _s, _e, _x, _r, _arr = [], _n = !0, _d = !1; try { if (_x = (_i = _i.call(arr)).next, 0 === i) { if (Object(_i) !== _i) return; _n = !1; } else for (; !(_n = (_s = _x.call(_i)).done) && (_arr.push(_s.value), _arr.length !== i); _n = !0); } catch (err) { _d = !0, _e = err; } finally { try { if (!_n && null != _i.return && (_r = _i.return(), Object(_r) !== _r)) return; } finally { if (_d) throw _e; } } return _arr; } }\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\nimport { createVNode as _createVNode } from \"vue\";\nimport { ref, watch, computed, reactive, defineComponent } from \"vue\";\nimport { extend, isHidden, unitToPx, numericProp, getScrollTop, getZIndexStyle, makeStringProp, makeNumericProp, createNamespace } from \"../utils/index.mjs\";\nimport { useRect, useEventListener, useScrollParent } from \"@vant/use\";\nimport { useVisibilityChange } from \"../composables/use-visibility-change.mjs\";\nvar _createNamespace = createNamespace(\"sticky\"),\n  _createNamespace2 = _slicedToArray(_createNamespace, 2),\n  name = _createNamespace2[0],\n  bem = _createNamespace2[1];\nvar stickyProps = {\n  zIndex: numericProp,\n  position: makeStringProp(\"top\"),\n  container: Object,\n  offsetTop: makeNumericProp(0),\n  offsetBottom: makeNumericProp(0)\n};\nvar stdin_default = defineComponent({\n  name: name,\n  props: stickyProps,\n  emits: [\"scroll\", \"change\"],\n  setup: function setup(props, _ref) {\n    var emit = _ref.emit,\n      slots = _ref.slots;\n    var root = ref();\n    var scrollParent = useScrollParent(root);\n    var state = reactive({\n      fixed: false,\n      width: 0,\n      height: 0,\n      transform: 0\n    });\n    var offset = computed(function () {\n      return unitToPx(props.position === \"top\" ? props.offsetTop : props.offsetBottom);\n    });\n    var rootStyle = computed(function () {\n      var fixed = state.fixed,\n        height = state.height,\n        width = state.width;\n      if (fixed) {\n        return {\n          width: \"\".concat(width, \"px\"),\n          height: \"\".concat(height, \"px\")\n        };\n      }\n    });\n    var stickyStyle = computed(function () {\n      if (!state.fixed) {\n        return;\n      }\n      var style = extend(getZIndexStyle(props.zIndex), _defineProperty({\n        width: \"\".concat(state.width, \"px\"),\n        height: \"\".concat(state.height, \"px\")\n      }, props.position, \"\".concat(offset.value, \"px\")));\n      if (state.transform) {\n        style.transform = \"translate3d(0, \".concat(state.transform, \"px, 0)\");\n      }\n      return style;\n    });\n    var emitScroll = function emitScroll(scrollTop) {\n      return emit(\"scroll\", {\n        scrollTop: scrollTop,\n        isFixed: state.fixed\n      });\n    };\n    var onScroll = function onScroll() {\n      if (!root.value || isHidden(root)) {\n        return;\n      }\n      var container = props.container,\n        position = props.position;\n      var rootRect = useRect(root);\n      var scrollTop = getScrollTop(window);\n      state.width = rootRect.width;\n      state.height = rootRect.height;\n      if (position === \"top\") {\n        if (container) {\n          var containerRect = useRect(container);\n          var difference = containerRect.bottom - offset.value - state.height;\n          state.fixed = offset.value > rootRect.top && containerRect.bottom > 0;\n          state.transform = difference < 0 ? difference : 0;\n        } else {\n          state.fixed = offset.value > rootRect.top;\n        }\n      } else {\n        var clientHeight = document.documentElement.clientHeight;\n        if (container) {\n          var _containerRect = useRect(container);\n          var _difference = clientHeight - _containerRect.top - offset.value - state.height;\n          state.fixed = clientHeight - offset.value < rootRect.bottom && clientHeight > _containerRect.top;\n          state.transform = _difference < 0 ? -_difference : 0;\n        } else {\n          state.fixed = clientHeight - offset.value < rootRect.bottom;\n        }\n      }\n      emitScroll(scrollTop);\n    };\n    watch(function () {\n      return state.fixed;\n    }, function (value) {\n      return emit(\"change\", value);\n    });\n    useEventListener(\"scroll\", onScroll, {\n      target: scrollParent,\n      passive: true\n    });\n    useVisibilityChange(root, onScroll);\n    return function () {\n      var _a;\n      return _createVNode(\"div\", {\n        \"ref\": root,\n        \"style\": rootStyle.value\n      }, [_createVNode(\"div\", {\n        \"class\": bem({\n          fixed: state.fixed\n        }),\n        \"style\": stickyStyle.value\n      }, [(_a = slots.default) == null ? void 0 : _a.call(slots)])]);\n    };\n  }\n});\nexport { stdin_default as default };", "map": {"version": 3, "names": ["createVNode", "_createVNode", "ref", "watch", "computed", "reactive", "defineComponent", "extend", "isHidden", "unitToPx", "numericProp", "getScrollTop", "getZIndexStyle", "makeStringProp", "makeNumericProp", "createNamespace", "useRect", "useEventListener", "useScrollParent", "useVisibilityChange", "_createNamespace", "_createNamespace2", "_slicedToArray", "name", "bem", "stickyProps", "zIndex", "position", "container", "Object", "offsetTop", "offsetBottom", "stdin_default", "props", "emits", "setup", "_ref", "emit", "slots", "root", "scrollParent", "state", "fixed", "width", "height", "transform", "offset", "rootStyle", "concat", "stickyStyle", "style", "_defineProperty", "value", "emitScroll", "scrollTop", "isFixed", "onScroll", "rootRect", "window", "containerRect", "difference", "bottom", "top", "clientHeight", "document", "documentElement", "target", "passive", "_a", "default", "call"], "sources": ["C:/Users/<USER>/Desktop/vue3_3.0/node_modules/vant/es/sticky/Sticky.mjs"], "sourcesContent": ["import { createVNode as _createVNode } from \"vue\";\nimport { ref, watch, computed, reactive, defineComponent } from \"vue\";\nimport { extend, isHidden, unitToPx, numericProp, getScrollTop, getZIndexStyle, makeStringProp, makeNumericProp, createNamespace } from \"../utils/index.mjs\";\nimport { useRect, useEventListener, useScrollParent } from \"@vant/use\";\nimport { useVisibilityChange } from \"../composables/use-visibility-change.mjs\";\nconst [name, bem] = createNamespace(\"sticky\");\nconst stickyProps = {\n  zIndex: numericProp,\n  position: makeStringProp(\"top\"),\n  container: Object,\n  offsetTop: makeNumericProp(0),\n  offsetBottom: makeNumericProp(0)\n};\nvar stdin_default = defineComponent({\n  name,\n  props: stickyProps,\n  emits: [\"scroll\", \"change\"],\n  setup(props, {\n    emit,\n    slots\n  }) {\n    const root = ref();\n    const scrollParent = useScrollParent(root);\n    const state = reactive({\n      fixed: false,\n      width: 0,\n      height: 0,\n      transform: 0\n    });\n    const offset = computed(() => unitToPx(props.position === \"top\" ? props.offsetTop : props.offsetBottom));\n    const rootStyle = computed(() => {\n      const {\n        fixed,\n        height,\n        width\n      } = state;\n      if (fixed) {\n        return {\n          width: `${width}px`,\n          height: `${height}px`\n        };\n      }\n    });\n    const stickyStyle = computed(() => {\n      if (!state.fixed) {\n        return;\n      }\n      const style = extend(getZIndexStyle(props.zIndex), {\n        width: `${state.width}px`,\n        height: `${state.height}px`,\n        [props.position]: `${offset.value}px`\n      });\n      if (state.transform) {\n        style.transform = `translate3d(0, ${state.transform}px, 0)`;\n      }\n      return style;\n    });\n    const emitScroll = (scrollTop) => emit(\"scroll\", {\n      scrollTop,\n      isFixed: state.fixed\n    });\n    const onScroll = () => {\n      if (!root.value || isHidden(root)) {\n        return;\n      }\n      const {\n        container,\n        position\n      } = props;\n      const rootRect = useRect(root);\n      const scrollTop = getScrollTop(window);\n      state.width = rootRect.width;\n      state.height = rootRect.height;\n      if (position === \"top\") {\n        if (container) {\n          const containerRect = useRect(container);\n          const difference = containerRect.bottom - offset.value - state.height;\n          state.fixed = offset.value > rootRect.top && containerRect.bottom > 0;\n          state.transform = difference < 0 ? difference : 0;\n        } else {\n          state.fixed = offset.value > rootRect.top;\n        }\n      } else {\n        const {\n          clientHeight\n        } = document.documentElement;\n        if (container) {\n          const containerRect = useRect(container);\n          const difference = clientHeight - containerRect.top - offset.value - state.height;\n          state.fixed = clientHeight - offset.value < rootRect.bottom && clientHeight > containerRect.top;\n          state.transform = difference < 0 ? -difference : 0;\n        } else {\n          state.fixed = clientHeight - offset.value < rootRect.bottom;\n        }\n      }\n      emitScroll(scrollTop);\n    };\n    watch(() => state.fixed, (value) => emit(\"change\", value));\n    useEventListener(\"scroll\", onScroll, {\n      target: scrollParent,\n      passive: true\n    });\n    useVisibilityChange(root, onScroll);\n    return () => {\n      var _a;\n      return _createVNode(\"div\", {\n        \"ref\": root,\n        \"style\": rootStyle.value\n      }, [_createVNode(\"div\", {\n        \"class\": bem({\n          fixed: state.fixed\n        }),\n        \"style\": stickyStyle.value\n      }, [(_a = slots.default) == null ? void 0 : _a.call(slots)])]);\n    };\n  }\n});\nexport {\n  stdin_default as default\n};\n"], "mappings": ";;;;;;;;;;AAAA,SAASA,WAAW,IAAIC,YAAY,QAAQ,KAAK;AACjD,SAASC,GAAG,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,eAAe,QAAQ,KAAK;AACrE,SAASC,MAAM,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,WAAW,EAAEC,YAAY,EAAEC,cAAc,EAAEC,cAAc,EAAEC,eAAe,EAAEC,eAAe,QAAQ,oBAAoB;AAC5J,SAASC,OAAO,EAAEC,gBAAgB,EAAEC,eAAe,QAAQ,WAAW;AACtE,SAASC,mBAAmB,QAAQ,0CAA0C;AAC9E,IAAAC,gBAAA,GAAoBL,eAAe,CAAC,QAAQ,CAAC;EAAAM,iBAAA,GAAAC,cAAA,CAAAF,gBAAA;EAAtCG,IAAI,GAAAF,iBAAA;EAAEG,GAAG,GAAAH,iBAAA;AAChB,IAAMI,WAAW,GAAG;EAClBC,MAAM,EAAEhB,WAAW;EACnBiB,QAAQ,EAAEd,cAAc,CAAC,KAAK,CAAC;EAC/Be,SAAS,EAAEC,MAAM;EACjBC,SAAS,EAAEhB,eAAe,CAAC,CAAC,CAAC;EAC7BiB,YAAY,EAAEjB,eAAe,CAAC,CAAC;AACjC,CAAC;AACD,IAAIkB,aAAa,GAAG1B,eAAe,CAAC;EAClCiB,IAAI,EAAJA,IAAI;EACJU,KAAK,EAAER,WAAW;EAClBS,KAAK,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC;EAC3BC,KAAK,WAAAA,MAACF,KAAK,EAAAG,IAAA,EAGR;IAAA,IAFDC,IAAI,GAAAD,IAAA,CAAJC,IAAI;MACJC,KAAK,GAAAF,IAAA,CAALE,KAAK;IAEL,IAAMC,IAAI,GAAGrC,GAAG,CAAC,CAAC;IAClB,IAAMsC,YAAY,GAAGtB,eAAe,CAACqB,IAAI,CAAC;IAC1C,IAAME,KAAK,GAAGpC,QAAQ,CAAC;MACrBqC,KAAK,EAAE,KAAK;MACZC,KAAK,EAAE,CAAC;MACRC,MAAM,EAAE,CAAC;MACTC,SAAS,EAAE;IACb,CAAC,CAAC;IACF,IAAMC,MAAM,GAAG1C,QAAQ,CAAC;MAAA,OAAMK,QAAQ,CAACwB,KAAK,CAACN,QAAQ,KAAK,KAAK,GAAGM,KAAK,CAACH,SAAS,GAAGG,KAAK,CAACF,YAAY,CAAC;IAAA,EAAC;IACxG,IAAMgB,SAAS,GAAG3C,QAAQ,CAAC,YAAM;MAC/B,IACEsC,KAAK,GAGHD,KAAK,CAHPC,KAAK;QACLE,MAAM,GAEJH,KAAK,CAFPG,MAAM;QACND,KAAK,GACHF,KAAK,CADPE,KAAK;MAEP,IAAID,KAAK,EAAE;QACT,OAAO;UACLC,KAAK,KAAAK,MAAA,CAAKL,KAAK,OAAI;UACnBC,MAAM,KAAAI,MAAA,CAAKJ,MAAM;QACnB,CAAC;MACH;IACF,CAAC,CAAC;IACF,IAAMK,WAAW,GAAG7C,QAAQ,CAAC,YAAM;MACjC,IAAI,CAACqC,KAAK,CAACC,KAAK,EAAE;QAChB;MACF;MACA,IAAMQ,KAAK,GAAG3C,MAAM,CAACK,cAAc,CAACqB,KAAK,CAACP,MAAM,CAAC,EAAAyB,eAAA;QAC/CR,KAAK,KAAAK,MAAA,CAAKP,KAAK,CAACE,KAAK,OAAI;QACzBC,MAAM,KAAAI,MAAA,CAAKP,KAAK,CAACG,MAAM;MAAI,GAC1BX,KAAK,CAACN,QAAQ,KAAAqB,MAAA,CAAMF,MAAM,CAACM,KAAK,QAClC,CAAC;MACF,IAAIX,KAAK,CAACI,SAAS,EAAE;QACnBK,KAAK,CAACL,SAAS,qBAAAG,MAAA,CAAqBP,KAAK,CAACI,SAAS,WAAQ;MAC7D;MACA,OAAOK,KAAK;IACd,CAAC,CAAC;IACF,IAAMG,UAAU,GAAG,SAAbA,UAAUA,CAAIC,SAAS;MAAA,OAAKjB,IAAI,CAAC,QAAQ,EAAE;QAC/CiB,SAAS,EAATA,SAAS;QACTC,OAAO,EAAEd,KAAK,CAACC;MACjB,CAAC,CAAC;IAAA;IACF,IAAMc,QAAQ,GAAG,SAAXA,QAAQA,CAAA,EAAS;MACrB,IAAI,CAACjB,IAAI,CAACa,KAAK,IAAI5C,QAAQ,CAAC+B,IAAI,CAAC,EAAE;QACjC;MACF;MACA,IACEX,SAAS,GAEPK,KAAK,CAFPL,SAAS;QACTD,QAAQ,GACNM,KAAK,CADPN,QAAQ;MAEV,IAAM8B,QAAQ,GAAGzC,OAAO,CAACuB,IAAI,CAAC;MAC9B,IAAMe,SAAS,GAAG3C,YAAY,CAAC+C,MAAM,CAAC;MACtCjB,KAAK,CAACE,KAAK,GAAGc,QAAQ,CAACd,KAAK;MAC5BF,KAAK,CAACG,MAAM,GAAGa,QAAQ,CAACb,MAAM;MAC9B,IAAIjB,QAAQ,KAAK,KAAK,EAAE;QACtB,IAAIC,SAAS,EAAE;UACb,IAAM+B,aAAa,GAAG3C,OAAO,CAACY,SAAS,CAAC;UACxC,IAAMgC,UAAU,GAAGD,aAAa,CAACE,MAAM,GAAGf,MAAM,CAACM,KAAK,GAAGX,KAAK,CAACG,MAAM;UACrEH,KAAK,CAACC,KAAK,GAAGI,MAAM,CAACM,KAAK,GAAGK,QAAQ,CAACK,GAAG,IAAIH,aAAa,CAACE,MAAM,GAAG,CAAC;UACrEpB,KAAK,CAACI,SAAS,GAAGe,UAAU,GAAG,CAAC,GAAGA,UAAU,GAAG,CAAC;QACnD,CAAC,MAAM;UACLnB,KAAK,CAACC,KAAK,GAAGI,MAAM,CAACM,KAAK,GAAGK,QAAQ,CAACK,GAAG;QAC3C;MACF,CAAC,MAAM;QACL,IACEC,YAAY,GACVC,QAAQ,CAACC,eAAe,CAD1BF,YAAY;QAEd,IAAInC,SAAS,EAAE;UACb,IAAM+B,cAAa,GAAG3C,OAAO,CAACY,SAAS,CAAC;UACxC,IAAMgC,WAAU,GAAGG,YAAY,GAAGJ,cAAa,CAACG,GAAG,GAAGhB,MAAM,CAACM,KAAK,GAAGX,KAAK,CAACG,MAAM;UACjFH,KAAK,CAACC,KAAK,GAAGqB,YAAY,GAAGjB,MAAM,CAACM,KAAK,GAAGK,QAAQ,CAACI,MAAM,IAAIE,YAAY,GAAGJ,cAAa,CAACG,GAAG;UAC/FrB,KAAK,CAACI,SAAS,GAAGe,WAAU,GAAG,CAAC,GAAG,CAACA,WAAU,GAAG,CAAC;QACpD,CAAC,MAAM;UACLnB,KAAK,CAACC,KAAK,GAAGqB,YAAY,GAAGjB,MAAM,CAACM,KAAK,GAAGK,QAAQ,CAACI,MAAM;QAC7D;MACF;MACAR,UAAU,CAACC,SAAS,CAAC;IACvB,CAAC;IACDnD,KAAK,CAAC;MAAA,OAAMsC,KAAK,CAACC,KAAK;IAAA,GAAE,UAACU,KAAK;MAAA,OAAKf,IAAI,CAAC,QAAQ,EAAEe,KAAK,CAAC;IAAA,EAAC;IAC1DnC,gBAAgB,CAAC,QAAQ,EAAEuC,QAAQ,EAAE;MACnCU,MAAM,EAAE1B,YAAY;MACpB2B,OAAO,EAAE;IACX,CAAC,CAAC;IACFhD,mBAAmB,CAACoB,IAAI,EAAEiB,QAAQ,CAAC;IACnC,OAAO,YAAM;MACX,IAAIY,EAAE;MACN,OAAOnE,YAAY,CAAC,KAAK,EAAE;QACzB,KAAK,EAAEsC,IAAI;QACX,OAAO,EAAEQ,SAAS,CAACK;MACrB,CAAC,EAAE,CAACnD,YAAY,CAAC,KAAK,EAAE;QACtB,OAAO,EAAEuB,GAAG,CAAC;UACXkB,KAAK,EAAED,KAAK,CAACC;QACf,CAAC,CAAC;QACF,OAAO,EAAEO,WAAW,CAACG;MACvB,CAAC,EAAE,CAAC,CAACgB,EAAE,GAAG9B,KAAK,CAAC+B,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGD,EAAE,CAACE,IAAI,CAAChC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IAChE,CAAC;EACH;AACF,CAAC,CAAC;AACF,SACEN,aAAa,IAAIqC,OAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}