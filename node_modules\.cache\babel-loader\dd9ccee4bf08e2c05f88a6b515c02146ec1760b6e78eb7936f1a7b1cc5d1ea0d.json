{"ast": null, "code": "//图标\n// 文本\nimport { createVNode, render } from 'vue';\nimport myMessage from './message.vue';\n// 动态创建一个DOM容器\nvar div = document.createElement('div');\ndiv.setAttribute('class', 'my-message-container');\ndocument.body.appendChild(div);\nexport default (function (_ref) {\n  var message = _ref.message,\n    type = _ref.type;\n  var timer = null;\n  //createVNode 用于创建一个虚拟节点\n  // 参数1 支持组件\n  // 参数2 表示传递给组件的选项\n  var vnode = createVNode(myMessage, {\n    message: message,\n    type: type\n  });\n  //把虚拟的节点渲染到页面的DOM中即可\n  // render函数的参数\n  //参数一：表示表示需要渲染的内容（虚拟节点）\n  // 参数二：表示渲染的目标位置 （DOM元素）\n  render(vnode, div);\n  // 希望1s后消失\n  if (timer) {\n    clearTimeout(timer);\n  }\n  timer = setTimeout(function () {\n    // 清空div里面的内容\n    render(null, div);\n  }, 2000);\n});\n// $message({ text: '登录失败', type: 'error'})", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}