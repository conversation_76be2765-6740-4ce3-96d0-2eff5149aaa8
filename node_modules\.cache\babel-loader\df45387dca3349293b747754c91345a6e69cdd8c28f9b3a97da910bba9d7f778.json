{"ast": null, "code": "import { ref } from 'vue';\nimport { caiwu } from '@/api/self/index';\nimport { useRouter } from 'vue-router';\nimport { useI18n } from 'vue-i18n';\nimport store from '@/store/index';\nimport { formatTime } from '@/api/format.js';\nexport default {\n  setup: function setup() {\n    var _store$state$baseInfo;\n    var _useRouter = useRouter(),\n      push = _useRouter.push;\n    var _useI18n = useI18n(),\n      t = _useI18n.t;\n    var currency = ref((_store$state$baseInfo = store.state.baseInfo) === null || _store$state$baseInfo === void 0 ? void 0 : _store$state$baseInfo.currency);\n    var list = ref([]);\n    var page = ref(0);\n    var type = ref('all');\n    var loading = ref(false);\n    var finished = ref(false);\n    var tabs = ref([\n    // {label: t('msg.all'),value: 'all'},\n    {\n      label: t('msg.chongzhi'),\n      value: 1\n    }, {\n      label: t('msg.tixian'),\n      value: 7\n    }, {\n      label: t('msg.jiaoyi'),\n      value: 2\n    }, {\n      label: t('msg.fanyong'),\n      value: 3\n    }, {\n      label: t('msg.tgfy'),\n      value: 5\n    }, {\n      label: t('msg.xjjyfy'),\n      value: 6\n    }]);\n    var clickLeft = function clickLeft() {\n      push('/self');\n    };\n    var clickRight = function clickRight() {\n      push('/message');\n    };\n    var getCW = function getCW(name, num) {\n      if (num) {\n        page.value = num;\n      } else {\n        ++page.value;\n      }\n      type.value = name && name != 'all' ? name : 0;\n      var json = {\n        page: page.value,\n        size: 10,\n        type: type.value\n      };\n      caiwu(json).then(function (res) {\n        loading.value = false;\n        if (res.code === 0) {\n          var _res$data, _res$data2;\n          finished.value = !((_res$data = res.data) !== null && _res$data !== void 0 && _res$data.paging);\n          list.value = list.value.concat((_res$data2 = res.data) === null || _res$data2 === void 0 ? void 0 : _res$data2.list);\n        }\n      });\n    };\n    getCW();\n    return {\n      formatTime: formatTime,\n      list: list,\n      clickLeft: clickLeft,\n      clickRight: clickRight,\n      tabs: tabs,\n      getCW: getCW,\n      loading: loading,\n      finished: finished,\n      currency: currency\n    };\n  }\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}