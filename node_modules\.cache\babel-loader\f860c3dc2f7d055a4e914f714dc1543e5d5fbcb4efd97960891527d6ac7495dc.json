{"ast": null, "code": "import { createElementVNode as _createElementVNode, createCommentVNode as _createCommentVNode, toDisplayString as _toDisplayString, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, normalizeClass as _normalizeClass, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, pushScopeId as _pushScopeId, popScopeId as _popScopeId } from \"vue\";\nvar _withScopeId = function _withScopeId(n) {\n  return _pushScopeId(\"data-v-720032fe\"), n = n(), _popScopeId(), n;\n};\nvar _hoisted_1 = {\n  class: \"footer\"\n};\nvar _hoisted_2 = [\"src\"];\nvar _hoisted_3 = {\n  key: 0\n};\nvar _hoisted_4 = {\n  class: \"lang_box\"\n};\nvar _hoisted_5 = {\n  class: \"title\"\n};\nvar _hoisted_6 = {\n  class: \"content\"\n};\nvar _hoisted_7 = {\n  class: \"langs\"\n};\nvar _hoisted_8 = [\"onClick\"];\nvar _hoisted_9 = [\"src\"];\nvar _hoisted_10 = {\n  class: \"text\"\n};\nvar _hoisted_11 = /*#__PURE__*/_withScopeId(function () {\n  return /*#__PURE__*/_createElementVNode(\"div\", {\n    class: \"btn\"\n  }, [/*#__PURE__*/_createCommentVNode(\" <van-button round block type=\\\"primary\\\" @click=\\\"submitLang\\\">\\n                            {{$t('msg.nowQh')}}\\n                        </van-button> \")], -1 /* HOISTED */);\n});\n\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_van_dialog = _resolveComponent(\"van-dialog\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"img\", {\n    src: require('@/assets/images/lang' + ($props.color == 'white' ? 1 : '') + '.png'),\n    class: \"lang\",\n    height: \"27\",\n    width: \"27\",\n    alt: \"\",\n    onClick: _cache[0] || (_cache[0] = function ($event) {\n      return $setup.showLang();\n    })\n  }, null, 8 /* PROPS */, _hoisted_2), $setup.show ? (_openBlock(), _createElementBlock(\"div\", _hoisted_3, [_createVNode(_component_van_dialog, {\n    show: $setup.show,\n    \"onUpdate:show\": _cache[1] || (_cache[1] = function ($event) {\n      return $setup.show = $event;\n    }),\n    showConfirmButton: false,\n    closeOnClickOverlay: \"\",\n    class: \"lang-dialog\"\n  }, {\n    default: _withCtx(function () {\n      return [_createElementVNode(\"div\", _hoisted_4, [_createCommentVNode(\" <img :src=\\\"require('@/assets/images/register/lang_bg.png')\\\" class=\\\"lang_bg\\\" /> \"), _createElementVNode(\"div\", _hoisted_5, _toDisplayString(_ctx.$t('msg.check_lang')), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_6, [_createCommentVNode(\" <img :src=\\\"require('@/assets/images/register/qiu.png')\\\" class=\\\"qiu\\\" /> \"), _createElementVNode(\"div\", _hoisted_7, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.langs, function (item, index) {\n        return _openBlock(), _createElementBlock(\"span\", {\n          class: _normalizeClass([\"li\", $setup.langcheck == item.link && 'check']),\n          key: index,\n          onClick: function onClick($event) {\n            return $setup.handSeletlanguages(item);\n          }\n        }, [_createElementVNode(\"img\", {\n          src: item.image_url,\n          class: \"img\",\n          height: \"18\",\n          width: \"27\",\n          alt: \"\"\n        }, null, 8 /* PROPS */, _hoisted_9), _createElementVNode(\"span\", _hoisted_10, _toDisplayString(item.name), 1 /* TEXT */)], 10 /* CLASS, PROPS */, _hoisted_8);\n      }), 128 /* KEYED_FRAGMENT */))]), _hoisted_11])])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"show\"])])) : _createCommentVNode(\"v-if\", true)]);\n}", "map": {"version": 3, "names": ["class", "_createElementVNode", "_createCommentVNode", "_createElementBlock", "_hoisted_1", "src", "require", "$props", "color", "height", "width", "alt", "onClick", "_cache", "$event", "$setup", "showLang", "show", "_hoisted_3", "_createVNode", "_component_van_dialog", "showConfirmButton", "closeOnClickOverlay", "_hoisted_4", "_hoisted_5", "_toDisplayString", "_ctx", "$t", "_hoisted_6", "_hoisted_7", "_Fragment", "_renderList", "langs", "item", "index", "_normalizeClass", "langcheck", "link", "key", "handSeletlanguages", "image_url", "_hoisted_10", "name", "_hoisted_11"], "sources": ["C:\\Users\\<USER>\\Downloads\\Telegram Desktop\\vue3_3.0\\vue3_3.0\\src\\components\\lang.vue"], "sourcesContent": ["<template>\n    <div class=\"footer\">\n        <img :src=\"require('@/assets/images/lang'+(color == 'white' ? 1 : '')+'.png')\" class=\"lang\" height=\"27\" width=\"27\" alt=\"\" @click=\"showLang()\">\n        <div v-if=\"show\">\n            <van-dialog v-model:show=\"show\" :showConfirmButton=\"false\" closeOnClickOverlay class=\"lang-dialog\">\n                <div class=\"lang_box\">\n                    <!-- <img :src=\"require('@/assets/images/register/lang_bg.png')\" class=\"lang_bg\" /> -->\n                    <div class=\"title\">{{$t('msg.check_lang')}}</div>\n                    <div class=\"content\">\n                        <!-- <img :src=\"require('@/assets/images/register/qiu.png')\" class=\"qiu\" /> -->\n                        <div class=\"langs\">\n                            <span class=\"li\" :class=\"langcheck==item.link && 'check'\" v-for=\"(item,index) in langs\" :key=\"index\"  @click=\"handSeletlanguages(item)\">\n                                <img :src=\"item.image_url\" class=\"img\" height=\"18\" width=\"27\" alt=\"\">\n                                <span class=\"text\">{{item.name}}</span>\n                            </span>\n                        </div>\n                        <div class=\"btn\">\n                        <!-- <van-button round block type=\"primary\" @click=\"submitLang\">\n                            {{$t('msg.nowQh')}}\n                        </van-button> -->\n                        </div>\n                    </div>\n                </div>\n            </van-dialog>\n        </div>\n    </div>\n</template>\n<script>\nimport { ref } from 'vue';\nimport store from '@/store/index'\nimport {vantLocales} from '@/i18n/i18n';\nimport { useI18n } from 'vue-i18n'\nexport default {\n    name: 'LanguageSwitcher',\n    props: {\n        color: String\n    },\n    setup(){\n        // 语言切换\n        const { locale } = useI18n()\n        const langcheck = ref(store.state.lang)\n        const langImg = ref('')\n        \n        langImg.value = store.state.langImg\n        const langs = ref(store.state.baseInfo?.languageList)\n\n        const show = ref(false);\n\n        const showLang = () => {\n            langcheck.value = store.state.lang\n            show.value = true\n        }\n        \n        const handSeletlanguages = (row) => {\n            langcheck.value = row.link\n            langImg.value = row.image_url\n            submitLang()\n        }\n        \n        const submitLang = () => {\n            locale.value = langcheck.value\n            store.dispatch('changelang', langcheck.value)\n            store.dispatch('changelangImg', langImg.value)\n            vantLocales(locale.value)\n            show.value = false\n        }\n      \n        submitLang()\n        return {show, submitLang, handSeletlanguages, langs, showLang, langcheck}\n    }\n}\n</script>\n<style lang=\"scss\" scoped>\n    :deep(.lang-dialog) {\n        .van-dialog__content {\n            max-height: 80vh;\n            overflow: hidden;\n        }\n    }\n    .lang_box{\n        width: 100%;\n        position: relative;\n        padding-top: 60px;\n        .lang_title {\n            margin-bottom: 40px;\n        }\n        .lang_bg{\n        width: 100%;\n        position: absolute;\n        top: 0;\n        left: 0;\n        }\n        .content{\n        position: relative;\n        z-index: 1;\n        text-align: center;\n        .qiu{\n            width: 175px;\n            border-radius: 50%;\n            box-shadow: $shadow;\n            margin-bottom: 6px;\n        }\n        .langs{\n            margin-bottom: 15px;\n            max-height: 70vh;\n            overflow-y: auto;\n            -webkit-overflow-scrolling: touch;\n            border: 1px solid #ccc;\n            margin: 24px;\n            border-radius: 24px;\n            .li{\n                padding: 24px;\n                display: block;\n                text-align: left;\n                border-bottom: 1px solid #ccc;\n                &:last-child{\n                    border-bottom: none;\n                }\n                &.ctn{\n                    padding: 24px;\n                }\n                &.check{\n                    background-color: #ccc;\n                }\n                .img{\n                    margin-right: 34px;\n                    vertical-align: middle;\n                }\n                .text{\n                    font-size: 26px;\n                    color:$textColor;\n                }\n            }\n        }\n        .btn{\n            padding: 50px 54px 50px;\n        }\n        }\n    }\n</style>"], "mappings": ";;;;;EACSA,KAAK,EAAC;AAAQ;;;;;;EAIFA,KAAK,EAAC;AAAU;;EAEZA,KAAK,EAAC;AAAO;;EACbA,KAAK,EAAC;AAAS;;EAEXA,KAAK,EAAC;AAAO;;;;EAGJA,KAAK,EAAC;AAAM;;sBAG1BC,mBAAA,CAIM;IAJDD,KAAK,EAAC;EAAK,I,aAChBE,mBAAA,6JAEiB,C;;;;;uBAlBrCC,mBAAA,CAwBM,OAxBNC,UAwBM,GAvBFH,mBAAA,CAA8I;IAAxII,GAAG,EAAEC,OAAO,2BAAyBC,MAAA,CAAAC,KAAK;IAA+BR,KAAK,EAAC,MAAM;IAACS,MAAM,EAAC,IAAI;IAACC,KAAK,EAAC,IAAI;IAACC,GAAG,EAAC,EAAE;IAAEC,OAAK,EAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAAEC,MAAA,CAAAC,QAAQ;IAAA;uCAC/HD,MAAA,CAAAE,IAAI,I,cAAfd,mBAAA,CAqBM,OAAAe,UAAA,GApBFC,YAAA,CAmBaC,qBAAA;IAnBOH,IAAI,EAAEF,MAAA,CAAAE,IAAI;;aAAJF,MAAA,CAAAE,IAAI,GAAAH,MAAA;IAAA;IAAGO,iBAAiB,EAAE,KAAK;IAAEC,mBAAmB,EAAnB,EAAmB;IAACtB,KAAK,EAAC;;sBACjF;MAAA,OAiBM,CAjBNC,mBAAA,CAiBM,OAjBNsB,UAiBM,GAhBFrB,mBAAA,wFAAuF,EACvFD,mBAAA,CAAiD,OAAjDuB,UAAiD,EAAAC,gBAAA,CAA5BC,IAAA,CAAAC,EAAE,oCACvB1B,mBAAA,CAaM,OAbN2B,UAaM,GAZF1B,mBAAA,gFAA+E,EAC/ED,mBAAA,CAKM,OALN4B,UAKM,I,kBAJF1B,mBAAA,CAGO2B,SAAA,QAAAC,WAAA,CAH0EhB,MAAA,CAAAiB,KAAK,YAApBC,IAAI,EAACC,KAAK;6BAA5E/B,mBAAA,CAGO;UAHDH,KAAK,EAAAmC,eAAA,EAAC,IAAI,EAASpB,MAAA,CAAAqB,SAAS,IAAEH,IAAI,CAACI,IAAI;UAA4CC,GAAG,EAAEJ,KAAK;UAAItB,OAAK,WAAAA,QAAAE,MAAA;YAAA,OAAEC,MAAA,CAAAwB,kBAAkB,CAACN,IAAI;UAAA;YACjIhC,mBAAA,CAAqE;UAA/DI,GAAG,EAAE4B,IAAI,CAACO,SAAS;UAAExC,KAAK,EAAC,KAAK;UAACS,MAAM,EAAC,IAAI;UAACC,KAAK,EAAC,IAAI;UAACC,GAAG,EAAC;6CAClEV,mBAAA,CAAuC,QAAvCwC,WAAuC,EAAAhB,gBAAA,CAAlBQ,IAAI,CAACS,IAAI,iB;wCAGtCC,WAIM,C"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}