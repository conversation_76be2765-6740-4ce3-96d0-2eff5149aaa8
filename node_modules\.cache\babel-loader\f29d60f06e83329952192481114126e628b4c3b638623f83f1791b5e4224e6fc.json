{"ast": null, "code": "function _createForOfIteratorHelper(o, allowArrayLike) { var it = typeof Symbol !== \"undefined\" && o[Symbol.iterator] || o[\"@@iterator\"]; if (!it) { if (Array.isArray(o) || (it = _unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === \"number\") { if (it) o = it; var i = 0; var F = function F() {}; return { s: F, n: function n() { if (i >= o.length) return { done: true }; return { done: false, value: o[i++] }; }, e: function e(_e) { throw _e; }, f: F }; } throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); } var normalCompletion = true, didErr = false, err; return { s: function s() { it = it.call(o); }, n: function n() { var step = it.next(); normalCompletion = step.done; return step; }, e: function e(_e2) { didErr = true; err = _e2; }, f: function f() { try { if (!normalCompletion && it.return != null) it.return(); } finally { if (didErr) throw err; } } }; }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nfunction _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __export = function __export(target, all) {\n  for (var name in all) __defProp(target, name, {\n    get: all[name],\n    enumerable: true\n  });\n};\nvar __copyProps = function __copyProps(to, from, except, desc) {\n  if (from && _typeof(from) === \"object\" || typeof from === \"function\") {\n    var _iterator = _createForOfIteratorHelper(__getOwnPropNames(from)),\n      _step;\n    try {\n      var _loop = function _loop() {\n        var key = _step.value;\n        if (!__hasOwnProp.call(to, key) && key !== except) __defProp(to, key, {\n          get: function get() {\n            return from[key];\n          },\n          enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable\n        });\n      };\n      for (_iterator.s(); !(_step = _iterator.n()).done;) {\n        _loop();\n      }\n    } catch (err) {\n      _iterator.e(err);\n    } finally {\n      _iterator.f();\n    }\n  }\n  return to;\n};\nvar __toCommonJS = function __toCommonJS(mod) {\n  return __copyProps(__defProp({}, \"__esModule\", {\n    value: true\n  }), mod);\n};\nvar stdin_exports = {};\n__export(stdin_exports, {\n  default: function _default() {\n    return stdin_default;\n  }\n});\nmodule.exports = __toCommonJS(stdin_exports);\nvar stdin_default = {\n  name: \"Nombre\",\n  tel: \"Tel\\xE9fono\",\n  save: \"Guardar\",\n  confirm: \"Confirmar\",\n  cancel: \"Cancelar\",\n  delete: \"Eliminar\",\n  loading: \"Cargando...\",\n  noCoupon: \"Sin cupones\",\n  nameEmpty: \"Por favor rellena el nombre\",\n  addContact: \"A\\xF1adi contacto\",\n  telInvalid: \"Tel\\xE9fono inv\\xE1lido\",\n  vanCalendar: {\n    end: \"Fin\",\n    start: \"Inicio\",\n    title: \"Calendario\",\n    weekdays: [\"Dom\", \"Lun\", \"Mar\", \"Mi\\xE9\", \"Jue\", \"Vie\", \"S\\xE1b\"],\n    monthTitle: function monthTitle(year, month) {\n      return \"\".concat(year, \"/\").concat(month);\n    },\n    rangePrompt: function rangePrompt(maxRange) {\n      return \"Elija no m\\xE1s de \".concat(maxRange, \" d\\xEDas\");\n    }\n  },\n  vanCascader: {\n    select: \"Seleccione\"\n  },\n  vanPagination: {\n    prev: \"Anterior\",\n    next: \"Siguiente\"\n  },\n  vanPullRefresh: {\n    pulling: \"Tira para recargar...\",\n    loosing: \"Suelta para recargar...\"\n  },\n  vanSubmitBar: {\n    label: \"Total:\"\n  },\n  vanCoupon: {\n    unlimited: \"Ilimitado\",\n    discount: function discount(_discount) {\n      return \"\".concat(_discount * 10, \"% de descuento\");\n    },\n    condition: function condition(_condition) {\n      return \"Al menos \".concat(_condition);\n    }\n  },\n  vanCouponCell: {\n    title: \"Cup\\xF3n\",\n    count: function count(_count) {\n      return \"You have \".concat(_count, \" coupons\");\n    }\n  },\n  vanCouponList: {\n    exchange: \"Intercambio\",\n    close: \"Cerrar\",\n    enable: \"Disponible\",\n    disabled: \"No disponible\",\n    placeholder: \"C\\xF3digo del cup\\xF3n\"\n  },\n  vanAddressEdit: {\n    area: \"\\xC1rea\",\n    postal: \"C\\xF3digo Postal\",\n    areaEmpty: \"Por favor selecciona una \\xE1rea de recogida\",\n    addressEmpty: \"La direcci\\xF3n no puede estar vacia\",\n    postalEmpty: \"C\\xF3digo postal inv\\xE1lido\",\n    addressDetail: \"Direcci\\xF3n\",\n    defaultAddress: \"Establecer como direcci\\xF3n por defecto\"\n  },\n  vanAddressList: {\n    add: \"Anadir direcci\\xF3n\"\n  }\n};", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}