{"ast": null, "code": "function _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return _typeof(key) === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (_typeof(input) !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (_typeof(res) !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\nfunction _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nfunction _iterableToArrayLimit(arr, i) { var _i = null == arr ? null : \"undefined\" != typeof Symbol && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (null != _i) { var _s, _e, _x, _r, _arr = [], _n = !0, _d = !1; try { if (_x = (_i = _i.call(arr)).next, 0 === i) { if (Object(_i) !== _i) return; _n = !1; } else for (; !(_n = (_s = _x.call(_i)).done) && (_arr.push(_s.value), _arr.length !== i); _n = !0); } catch (err) { _d = !0, _e = err; } finally { try { if (!_n && null != _i.return && (_r = _i.return(), Object(_r) !== _r)) return; } finally { if (_d) throw _e; } } return _arr; } }\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\nimport { createVNode as _createVNode } from \"vue\";\nimport { defineComponent } from \"vue\";\nimport { extend, numericProp, preventDefault, makeStringProp, createNamespace, BORDER_SURROUND } from \"../utils/index.mjs\";\nimport { useRoute, routeProps } from \"../composables/use-route.mjs\";\nimport { Icon } from \"../icon/index.mjs\";\nimport { Loading } from \"../loading/index.mjs\";\nvar _createNamespace = createNamespace(\"button\"),\n  _createNamespace2 = _slicedToArray(_createNamespace, 2),\n  name = _createNamespace2[0],\n  bem = _createNamespace2[1];\nvar buttonProps = extend({}, routeProps, {\n  tag: makeStringProp(\"button\"),\n  text: String,\n  icon: String,\n  type: makeStringProp(\"default\"),\n  size: makeStringProp(\"normal\"),\n  color: String,\n  block: Boolean,\n  plain: Boolean,\n  round: Boolean,\n  square: Boolean,\n  loading: Boolean,\n  hairline: Boolean,\n  disabled: Boolean,\n  iconPrefix: String,\n  nativeType: makeStringProp(\"button\"),\n  loadingSize: numericProp,\n  loadingText: String,\n  loadingType: String,\n  iconPosition: makeStringProp(\"left\")\n});\nvar stdin_default = defineComponent({\n  name: name,\n  props: buttonProps,\n  emits: [\"click\"],\n  setup: function setup(props, _ref) {\n    var emit = _ref.emit,\n      slots = _ref.slots;\n    var route = useRoute();\n    var renderLoadingIcon = function renderLoadingIcon() {\n      if (slots.loading) {\n        return slots.loading();\n      }\n      return _createVNode(Loading, {\n        \"size\": props.loadingSize,\n        \"type\": props.loadingType,\n        \"class\": bem(\"loading\")\n      }, null);\n    };\n    var renderIcon = function renderIcon() {\n      if (props.loading) {\n        return renderLoadingIcon();\n      }\n      if (slots.icon) {\n        return _createVNode(\"div\", {\n          \"class\": bem(\"icon\")\n        }, [slots.icon()]);\n      }\n      if (props.icon) {\n        return _createVNode(Icon, {\n          \"name\": props.icon,\n          \"class\": bem(\"icon\"),\n          \"classPrefix\": props.iconPrefix\n        }, null);\n      }\n    };\n    var renderText = function renderText() {\n      var text;\n      if (props.loading) {\n        text = props.loadingText;\n      } else {\n        text = slots.default ? slots.default() : props.text;\n      }\n      if (text) {\n        return _createVNode(\"span\", {\n          \"class\": bem(\"text\")\n        }, [text]);\n      }\n    };\n    var getStyle = function getStyle() {\n      var color = props.color,\n        plain = props.plain;\n      if (color) {\n        var style = {\n          color: plain ? color : \"white\"\n        };\n        if (!plain) {\n          style.background = color;\n        }\n        if (color.includes(\"gradient\")) {\n          style.border = 0;\n        } else {\n          style.borderColor = color;\n        }\n        return style;\n      }\n    };\n    var onClick = function onClick(event) {\n      if (props.loading) {\n        preventDefault(event);\n      } else if (!props.disabled) {\n        emit(\"click\", event);\n        route();\n      }\n    };\n    return function () {\n      var tag = props.tag,\n        type = props.type,\n        size = props.size,\n        block = props.block,\n        round = props.round,\n        plain = props.plain,\n        square = props.square,\n        loading = props.loading,\n        disabled = props.disabled,\n        hairline = props.hairline,\n        nativeType = props.nativeType,\n        iconPosition = props.iconPosition;\n      var classes = [bem([type, size, {\n        plain: plain,\n        block: block,\n        round: round,\n        square: square,\n        loading: loading,\n        disabled: disabled,\n        hairline: hairline\n      }]), _defineProperty({}, BORDER_SURROUND, hairline)];\n      return _createVNode(tag, {\n        \"type\": nativeType,\n        \"class\": classes,\n        \"style\": getStyle(),\n        \"disabled\": disabled,\n        \"onClick\": onClick\n      }, {\n        default: function _default() {\n          return [_createVNode(\"div\", {\n            \"class\": bem(\"content\")\n          }, [iconPosition === \"left\" && renderIcon(), renderText(), iconPosition === \"right\" && renderIcon()])];\n        }\n      });\n    };\n  }\n});\nexport { stdin_default as default };", "map": {"version": 3, "names": ["createVNode", "_createVNode", "defineComponent", "extend", "numericProp", "preventDefault", "makeStringProp", "createNamespace", "BORDER_SURROUND", "useRoute", "routeProps", "Icon", "Loading", "_createNamespace", "_createNamespace2", "_slicedToArray", "name", "bem", "buttonProps", "tag", "text", "String", "icon", "type", "size", "color", "block", "Boolean", "plain", "round", "square", "loading", "hairline", "disabled", "iconPrefix", "nativeType", "loadingSize", "loadingText", "loadingType", "iconPosition", "stdin_default", "props", "emits", "setup", "_ref", "emit", "slots", "route", "renderLoadingIcon", "renderIcon", "renderText", "default", "getStyle", "style", "background", "includes", "border", "borderColor", "onClick", "event", "classes", "_defineProperty", "_default"], "sources": ["C:/Users/<USER>/Downloads/Telegram Desktop/vue3_3.0/vue3_3.0/node_modules/vant/es/button/Button.mjs"], "sourcesContent": ["import { createVNode as _createVNode } from \"vue\";\nimport { defineComponent } from \"vue\";\nimport { extend, numericProp, preventDefault, makeStringProp, createNamespace, BORDER_SURROUND } from \"../utils/index.mjs\";\nimport { useRoute, routeProps } from \"../composables/use-route.mjs\";\nimport { Icon } from \"../icon/index.mjs\";\nimport { Loading } from \"../loading/index.mjs\";\nconst [name, bem] = createNamespace(\"button\");\nconst buttonProps = extend({}, routeProps, {\n  tag: makeStringProp(\"button\"),\n  text: String,\n  icon: String,\n  type: makeStringProp(\"default\"),\n  size: makeStringProp(\"normal\"),\n  color: String,\n  block: Boolean,\n  plain: Boolean,\n  round: Boolean,\n  square: Boolean,\n  loading: Boolean,\n  hairline: Boolean,\n  disabled: Boolean,\n  iconPrefix: String,\n  nativeType: makeStringProp(\"button\"),\n  loadingSize: numericProp,\n  loadingText: String,\n  loadingType: String,\n  iconPosition: makeStringProp(\"left\")\n});\nvar stdin_default = defineComponent({\n  name,\n  props: buttonProps,\n  emits: [\"click\"],\n  setup(props, {\n    emit,\n    slots\n  }) {\n    const route = useRoute();\n    const renderLoadingIcon = () => {\n      if (slots.loading) {\n        return slots.loading();\n      }\n      return _createVNode(Loading, {\n        \"size\": props.loadingSize,\n        \"type\": props.loadingType,\n        \"class\": bem(\"loading\")\n      }, null);\n    };\n    const renderIcon = () => {\n      if (props.loading) {\n        return renderLoadingIcon();\n      }\n      if (slots.icon) {\n        return _createVNode(\"div\", {\n          \"class\": bem(\"icon\")\n        }, [slots.icon()]);\n      }\n      if (props.icon) {\n        return _createVNode(Icon, {\n          \"name\": props.icon,\n          \"class\": bem(\"icon\"),\n          \"classPrefix\": props.iconPrefix\n        }, null);\n      }\n    };\n    const renderText = () => {\n      let text;\n      if (props.loading) {\n        text = props.loadingText;\n      } else {\n        text = slots.default ? slots.default() : props.text;\n      }\n      if (text) {\n        return _createVNode(\"span\", {\n          \"class\": bem(\"text\")\n        }, [text]);\n      }\n    };\n    const getStyle = () => {\n      const {\n        color,\n        plain\n      } = props;\n      if (color) {\n        const style = {\n          color: plain ? color : \"white\"\n        };\n        if (!plain) {\n          style.background = color;\n        }\n        if (color.includes(\"gradient\")) {\n          style.border = 0;\n        } else {\n          style.borderColor = color;\n        }\n        return style;\n      }\n    };\n    const onClick = (event) => {\n      if (props.loading) {\n        preventDefault(event);\n      } else if (!props.disabled) {\n        emit(\"click\", event);\n        route();\n      }\n    };\n    return () => {\n      const {\n        tag,\n        type,\n        size,\n        block,\n        round,\n        plain,\n        square,\n        loading,\n        disabled,\n        hairline,\n        nativeType,\n        iconPosition\n      } = props;\n      const classes = [bem([type, size, {\n        plain,\n        block,\n        round,\n        square,\n        loading,\n        disabled,\n        hairline\n      }]), {\n        [BORDER_SURROUND]: hairline\n      }];\n      return _createVNode(tag, {\n        \"type\": nativeType,\n        \"class\": classes,\n        \"style\": getStyle(),\n        \"disabled\": disabled,\n        \"onClick\": onClick\n      }, {\n        default: () => [_createVNode(\"div\", {\n          \"class\": bem(\"content\")\n        }, [iconPosition === \"left\" && renderIcon(), renderText(), iconPosition === \"right\" && renderIcon()])]\n      });\n    };\n  }\n});\nexport {\n  stdin_default as default\n};\n"], "mappings": ";;;;;;;;;;AAAA,SAASA,WAAW,IAAIC,YAAY,QAAQ,KAAK;AACjD,SAASC,eAAe,QAAQ,KAAK;AACrC,SAASC,MAAM,EAAEC,WAAW,EAAEC,cAAc,EAAEC,cAAc,EAAEC,eAAe,EAAEC,eAAe,QAAQ,oBAAoB;AAC1H,SAASC,QAAQ,EAAEC,UAAU,QAAQ,8BAA8B;AACnE,SAASC,IAAI,QAAQ,mBAAmB;AACxC,SAASC,OAAO,QAAQ,sBAAsB;AAC9C,IAAAC,gBAAA,GAAoBN,eAAe,CAAC,QAAQ,CAAC;EAAAO,iBAAA,GAAAC,cAAA,CAAAF,gBAAA;EAAtCG,IAAI,GAAAF,iBAAA;EAAEG,GAAG,GAAAH,iBAAA;AAChB,IAAMI,WAAW,GAAGf,MAAM,CAAC,CAAC,CAAC,EAAEO,UAAU,EAAE;EACzCS,GAAG,EAAEb,cAAc,CAAC,QAAQ,CAAC;EAC7Bc,IAAI,EAAEC,MAAM;EACZC,IAAI,EAAED,MAAM;EACZE,IAAI,EAAEjB,cAAc,CAAC,SAAS,CAAC;EAC/BkB,IAAI,EAAElB,cAAc,CAAC,QAAQ,CAAC;EAC9BmB,KAAK,EAAEJ,MAAM;EACbK,KAAK,EAAEC,OAAO;EACdC,KAAK,EAAED,OAAO;EACdE,KAAK,EAAEF,OAAO;EACdG,MAAM,EAAEH,OAAO;EACfI,OAAO,EAAEJ,OAAO;EAChBK,QAAQ,EAAEL,OAAO;EACjBM,QAAQ,EAAEN,OAAO;EACjBO,UAAU,EAAEb,MAAM;EAClBc,UAAU,EAAE7B,cAAc,CAAC,QAAQ,CAAC;EACpC8B,WAAW,EAAEhC,WAAW;EACxBiC,WAAW,EAAEhB,MAAM;EACnBiB,WAAW,EAAEjB,MAAM;EACnBkB,YAAY,EAAEjC,cAAc,CAAC,MAAM;AACrC,CAAC,CAAC;AACF,IAAIkC,aAAa,GAAGtC,eAAe,CAAC;EAClCc,IAAI,EAAJA,IAAI;EACJyB,KAAK,EAAEvB,WAAW;EAClBwB,KAAK,EAAE,CAAC,OAAO,CAAC;EAChBC,KAAK,WAAAA,MAACF,KAAK,EAAAG,IAAA,EAGR;IAAA,IAFDC,IAAI,GAAAD,IAAA,CAAJC,IAAI;MACJC,KAAK,GAAAF,IAAA,CAALE,KAAK;IAEL,IAAMC,KAAK,GAAGtC,QAAQ,CAAC,CAAC;IACxB,IAAMuC,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAA,EAAS;MAC9B,IAAIF,KAAK,CAACf,OAAO,EAAE;QACjB,OAAOe,KAAK,CAACf,OAAO,CAAC,CAAC;MACxB;MACA,OAAO9B,YAAY,CAACW,OAAO,EAAE;QAC3B,MAAM,EAAE6B,KAAK,CAACL,WAAW;QACzB,MAAM,EAAEK,KAAK,CAACH,WAAW;QACzB,OAAO,EAAErB,GAAG,CAAC,SAAS;MACxB,CAAC,EAAE,IAAI,CAAC;IACV,CAAC;IACD,IAAMgC,UAAU,GAAG,SAAbA,UAAUA,CAAA,EAAS;MACvB,IAAIR,KAAK,CAACV,OAAO,EAAE;QACjB,OAAOiB,iBAAiB,CAAC,CAAC;MAC5B;MACA,IAAIF,KAAK,CAACxB,IAAI,EAAE;QACd,OAAOrB,YAAY,CAAC,KAAK,EAAE;UACzB,OAAO,EAAEgB,GAAG,CAAC,MAAM;QACrB,CAAC,EAAE,CAAC6B,KAAK,CAACxB,IAAI,CAAC,CAAC,CAAC,CAAC;MACpB;MACA,IAAImB,KAAK,CAACnB,IAAI,EAAE;QACd,OAAOrB,YAAY,CAACU,IAAI,EAAE;UACxB,MAAM,EAAE8B,KAAK,CAACnB,IAAI;UAClB,OAAO,EAAEL,GAAG,CAAC,MAAM,CAAC;UACpB,aAAa,EAAEwB,KAAK,CAACP;QACvB,CAAC,EAAE,IAAI,CAAC;MACV;IACF,CAAC;IACD,IAAMgB,UAAU,GAAG,SAAbA,UAAUA,CAAA,EAAS;MACvB,IAAI9B,IAAI;MACR,IAAIqB,KAAK,CAACV,OAAO,EAAE;QACjBX,IAAI,GAAGqB,KAAK,CAACJ,WAAW;MAC1B,CAAC,MAAM;QACLjB,IAAI,GAAG0B,KAAK,CAACK,OAAO,GAAGL,KAAK,CAACK,OAAO,CAAC,CAAC,GAAGV,KAAK,CAACrB,IAAI;MACrD;MACA,IAAIA,IAAI,EAAE;QACR,OAAOnB,YAAY,CAAC,MAAM,EAAE;UAC1B,OAAO,EAAEgB,GAAG,CAAC,MAAM;QACrB,CAAC,EAAE,CAACG,IAAI,CAAC,CAAC;MACZ;IACF,CAAC;IACD,IAAMgC,QAAQ,GAAG,SAAXA,QAAQA,CAAA,EAAS;MACrB,IACE3B,KAAK,GAEHgB,KAAK,CAFPhB,KAAK;QACLG,KAAK,GACHa,KAAK,CADPb,KAAK;MAEP,IAAIH,KAAK,EAAE;QACT,IAAM4B,KAAK,GAAG;UACZ5B,KAAK,EAAEG,KAAK,GAAGH,KAAK,GAAG;QACzB,CAAC;QACD,IAAI,CAACG,KAAK,EAAE;UACVyB,KAAK,CAACC,UAAU,GAAG7B,KAAK;QAC1B;QACA,IAAIA,KAAK,CAAC8B,QAAQ,CAAC,UAAU,CAAC,EAAE;UAC9BF,KAAK,CAACG,MAAM,GAAG,CAAC;QAClB,CAAC,MAAM;UACLH,KAAK,CAACI,WAAW,GAAGhC,KAAK;QAC3B;QACA,OAAO4B,KAAK;MACd;IACF,CAAC;IACD,IAAMK,OAAO,GAAG,SAAVA,OAAOA,CAAIC,KAAK,EAAK;MACzB,IAAIlB,KAAK,CAACV,OAAO,EAAE;QACjB1B,cAAc,CAACsD,KAAK,CAAC;MACvB,CAAC,MAAM,IAAI,CAAClB,KAAK,CAACR,QAAQ,EAAE;QAC1BY,IAAI,CAAC,OAAO,EAAEc,KAAK,CAAC;QACpBZ,KAAK,CAAC,CAAC;MACT;IACF,CAAC;IACD,OAAO,YAAM;MACX,IACE5B,GAAG,GAYDsB,KAAK,CAZPtB,GAAG;QACHI,IAAI,GAWFkB,KAAK,CAXPlB,IAAI;QACJC,IAAI,GAUFiB,KAAK,CAVPjB,IAAI;QACJE,KAAK,GASHe,KAAK,CATPf,KAAK;QACLG,KAAK,GAQHY,KAAK,CARPZ,KAAK;QACLD,KAAK,GAOHa,KAAK,CAPPb,KAAK;QACLE,MAAM,GAMJW,KAAK,CANPX,MAAM;QACNC,OAAO,GAKLU,KAAK,CALPV,OAAO;QACPE,QAAQ,GAINQ,KAAK,CAJPR,QAAQ;QACRD,QAAQ,GAGNS,KAAK,CAHPT,QAAQ;QACRG,UAAU,GAERM,KAAK,CAFPN,UAAU;QACVI,YAAY,GACVE,KAAK,CADPF,YAAY;MAEd,IAAMqB,OAAO,GAAG,CAAC3C,GAAG,CAAC,CAACM,IAAI,EAAEC,IAAI,EAAE;QAChCI,KAAK,EAALA,KAAK;QACLF,KAAK,EAALA,KAAK;QACLG,KAAK,EAALA,KAAK;QACLC,MAAM,EAANA,MAAM;QACNC,OAAO,EAAPA,OAAO;QACPE,QAAQ,EAARA,QAAQ;QACRD,QAAQ,EAARA;MACF,CAAC,CAAC,CAAC,EAAA6B,eAAA,KACArD,eAAe,EAAGwB,QAAQ,EAC3B;MACF,OAAO/B,YAAY,CAACkB,GAAG,EAAE;QACvB,MAAM,EAAEgB,UAAU;QAClB,OAAO,EAAEyB,OAAO;QAChB,OAAO,EAAER,QAAQ,CAAC,CAAC;QACnB,UAAU,EAAEnB,QAAQ;QACpB,SAAS,EAAEyB;MACb,CAAC,EAAE;QACDP,OAAO,EAAE,SAAAW,SAAA;UAAA,OAAM,CAAC7D,YAAY,CAAC,KAAK,EAAE;YAClC,OAAO,EAAEgB,GAAG,CAAC,SAAS;UACxB,CAAC,EAAE,CAACsB,YAAY,KAAK,MAAM,IAAIU,UAAU,CAAC,CAAC,EAAEC,UAAU,CAAC,CAAC,EAAEX,YAAY,KAAK,OAAO,IAAIU,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;QAAA;MACxG,CAAC,CAAC;IACJ,CAAC;EACH;AACF,CAAC,CAAC;AACF,SACET,aAAa,IAAIW,OAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}