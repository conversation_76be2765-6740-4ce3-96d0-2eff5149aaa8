{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _CellGroup from \"./CellGroup.mjs\";\nvar CellGroup = withInstall(_CellGroup);\nvar stdin_default = CellGroup;\nexport { CellGroup, stdin_default as default };", "map": {"version": 3, "names": ["withInstall", "_CellGroup", "CellGroup", "stdin_default", "default"], "sources": ["C:/Users/<USER>/Downloads/Telegram Desktop/vue3_3.0/vue3_3.0/node_modules/vant/es/cell-group/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _CellGroup from \"./CellGroup.mjs\";\nconst CellGroup = withInstall(_CellGroup);\nvar stdin_default = CellGroup;\nexport {\n  CellGroup,\n  stdin_default as default\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,UAAU,MAAM,iBAAiB;AACxC,IAAMC,SAAS,GAAGF,WAAW,CAACC,UAAU,CAAC;AACzC,IAAIE,aAAa,GAAGD,SAAS;AAC7B,SACEA,SAAS,EACTC,aAAa,IAAIC,OAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}