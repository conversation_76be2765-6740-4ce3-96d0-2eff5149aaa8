{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, vModelText as _vModelText, createElementVNode as _createElementVNode, withDirectives as _withDirectives, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString, vShow as _vShow, pushScopeId as _pushScopeId, popScopeId as _popScopeId } from \"vue\";\nvar _withScopeId = function _withScopeId(n) {\n  return _pushScopeId(\"data-v-0cde47dc\"), n = n(), _popScopeId(), n;\n};\nvar _hoisted_1 = {\n  key: 0,\n  class: \"dropdown\"\n};\nvar _hoisted_2 = [\"name\", \"disabled\", \"placeholder\"];\nvar _hoisted_3 = {\n  class: \"dropdown-content\"\n};\nvar _hoisted_4 = [\"onMousedown\"];\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return $props.options ? (_openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createCommentVNode(\" Dropdown Input \"), _withDirectives(_createElementVNode(\"input\", {\n    class: \"dropdown-input\",\n    name: $props.name,\n    onFocus: _cache[0] || (_cache[0] = function ($event) {\n      return $options.showOptions();\n    }),\n    onBlur: _cache[1] || (_cache[1] = function ($event) {\n      return $options.exit();\n    }),\n    onKeyup: _cache[2] || (_cache[2] = function () {\n      return $options.keyMonitor && $options.keyMonitor.apply($options, arguments);\n    }),\n    \"onUpdate:modelValue\": _cache[3] || (_cache[3] = function ($event) {\n      return $data.searchFilter = $event;\n    }),\n    disabled: $props.disabled,\n    placeholder: $props.placeholder\n  }, null, 40 /* PROPS, HYDRATE_EVENTS */, _hoisted_2), [[_vModelText, $data.searchFilter]]), _createCommentVNode(\" Dropdown Menu \"), _withDirectives(_createElementVNode(\"div\", _hoisted_3, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($options.filteredOptions, function (option, index) {\n    return _openBlock(), _createElementBlock(\"div\", {\n      class: \"dropdown-item\",\n      onMousedown: function onMousedown($event) {\n        return $options.selectOption(option);\n      },\n      key: index\n    }, _toDisplayString(option.name || option.id || '-'), 41 /* TEXT, PROPS, HYDRATE_EVENTS */, _hoisted_4);\n  }), 128 /* KEYED_FRAGMENT */))], 512 /* NEED_PATCH */), [[_vShow, $data.optionsShown]])])) : _createCommentVNode(\"v-if\", true);\n}", "map": {"version": 3, "names": ["class", "$props", "options", "_createElementBlock", "_hoisted_1", "_createCommentVNode", "_createElementVNode", "name", "onFocus", "_cache", "$event", "$options", "showOptions", "onBlur", "exit", "onKeyup", "keyMonitor", "apply", "arguments", "$data", "searchFilter", "disabled", "placeholder", "_hoisted_3", "_Fragment", "_renderList", "filteredOptions", "option", "index", "onMousedown", "selectOption", "key", "id", "_hoisted_4", "optionsShown"], "sources": ["C:\\Users\\<USER>\\Downloads\\Telegram Desktop\\vue3_3.0\\vue3_3.0\\node_modules\\vue-simple-search-dropdown\\src\\Dropdown.vue"], "sourcesContent": ["<template>\n  <div class=\"dropdown\" v-if=\"options\">\n\n    <!-- Dropdown Input -->\n    <input class=\"dropdown-input\"\n      :name=\"name\"\n      @focus=\"showOptions()\"\n      @blur=\"exit()\"\n      @keyup=\"keyMonitor\"\n      v-model=\"searchFilter\"\n      :disabled=\"disabled\"\n      :placeholder=\"placeholder\" />\n\n    <!-- Dropdown Menu -->\n    <div class=\"dropdown-content\"\n      v-show=\"optionsShown\">\n      <div\n        class=\"dropdown-item\"\n        @mousedown=\"selectOption(option)\"\n        v-for=\"(option, index) in filteredOptions\"\n        :key=\"index\">\n          {{ option.name || option.id || '-' }}\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\n  export default {\n    name: 'Dropdown',\n    template: 'Dropdown',\n    props: {\n      name: {\n        type: String,\n        required: false,\n        default: 'dropdown',\n        note: 'Input name'\n      },\n      options: {\n        type: Array,\n        required: true,\n        default: [],\n        note: 'Options of dropdown. An array of options with id and name',\n      },\n      placeholder: {\n        type: String,\n        required: false,\n        default: 'Please select an option',\n        note: 'Placeholder of dropdown'\n      },\n      disabled: {\n        type: Boolean,\n        required: false,\n        default: false,\n        note: 'Disable the dropdown'\n      },\n      maxItem: {\n        type: Number,\n        required: false,\n        default: 6,\n        note: 'Max items showing'\n      }\n    },\n    data() {\n      return {\n        selected: {},\n        optionsShown: false,\n        searchFilter: ''\n      }\n    },\n    created() {\n      this.$emit('selected', this.selected);\n    },\n    computed: {\n      filteredOptions() {\n        const filtered = [];\n        const regOption = new RegExp(this.searchFilter, 'ig');\n        for (const option of this.options) {\n          if (this.searchFilter.length < 1 || option.name.match(regOption)){\n            if (filtered.length < this.maxItem) filtered.push(option);\n          }\n        }\n        return filtered;\n      }\n    },\n    methods: {\n      selectOption(option) {\n        this.selected = option;\n        this.optionsShown = false;\n        this.searchFilter = this.selected.name;\n        this.$emit('selected', this.selected);\n      },\n      showOptions(){\n        if (!this.disabled) {\n          this.searchFilter = '';\n          this.optionsShown = true;\n        }\n      },\n      exit() {\n        if (!this.selected.id) {\n          this.selected = {};\n          this.searchFilter = '';\n        } else {\n          this.searchFilter = this.selected.name;\n        }\n        this.$emit('selected', this.selected);\n        this.optionsShown = false;\n      },\n      // Selecting when pressing Enter\n      keyMonitor: function(event) {\n        if (event.key === \"Enter\" && this.filteredOptions[0])\n          this.selectOption(this.filteredOptions[0]);\n      }\n    },\n    watch: {\n      searchFilter() {\n        if (this.filteredOptions.length === 0) {\n          this.selected = {};\n        } else {\n          this.selected = this.filteredOptions[0];\n        }\n        this.$emit('filter', this.searchFilter);\n      }\n    }\n  };\n</script>\n\n\n<style lang=\"scss\" scoped>\n  .dropdown {\n    position: relative;\n    display: block;\n    margin: auto;\n    .dropdown-input {\n      background: #fff;\n      cursor: pointer;\n      border: 1px solid #e7ecf5;\n      border-radius: 3px;\n      color: #333;\n      display: block;\n      font-size: .8em;\n      padding: 6px;\n      min-width: 250px;\n      max-width: 250px;\n      &:hover {\n        background: #f8f8fa;\n      }\n    }\n    .dropdown-content {\n      position: absolute;\n      background-color: #fff;\n      min-width: 248px;\n      max-width: 248px;\n      max-height: 248px;\n      border: 1px solid #e7ecf5;\n      box-shadow: 0px -8px 34px 0px rgba(0,0,0,0.05);\n      overflow: auto;\n      z-index: 1;\n      .dropdown-item {\n        color: black;\n        font-size: .7em;\n        line-height: 1em;\n        padding: 8px;\n        text-decoration: none;\n        display: block;\n        cursor: pointer;\n        &:hover {\n          background-color: #e7ecf5;\n        }\n      }\n    }\n    .dropdown:hover .dropdowncontent {\n      display: block;\n    }\n  }\n</style>\n"], "mappings": ";;;;;;EACOA,KAAK,EAAC;;;;EAaJA,KAAK,EAAC;AAAkB;;;SAbHC,MAAA,CAAAC,OAAO,I,cAAnCC,mBAAA,CAuBM,OAvBNC,UAuBM,GArBJC,mBAAA,oBAAuB,E,gBACvBC,mBAAA,CAO+B;IAPxBN,KAAK,EAAC,gBAAgB;IAC1BO,IAAI,EAAEN,MAAA,CAAAM,IAAI;IACVC,OAAK,EAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAAEC,QAAA,CAAAC,WAAW;IAAA;IAClBC,MAAI,EAAAJ,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAAEC,QAAA,CAAAG,IAAI;IAAA;IACVC,OAAK,EAAAN,MAAA,QAAAA,MAAA;MAAA,OAAEE,QAAA,CAAAK,UAAA,IAAAL,QAAA,CAAAK,UAAA,CAAAC,KAAA,CAAAN,QAAA,EAAAO,SAAA,CAAU;IAAA;;aACTC,KAAA,CAAAC,YAAY,GAAAV,MAAA;IAAA;IACpBW,QAAQ,EAAEpB,MAAA,CAAAoB,QAAQ;IAClBC,WAAW,EAAErB,MAAA,CAAAqB;uEAFLH,KAAA,CAAAC,YAAY,E,GAIvBf,mBAAA,mBAAsB,E,gBACtBC,mBAAA,CASM,OATNiB,UASM,I,kBAPJpB,mBAAA,CAMMqB,SAAA,QAAAC,WAAA,CAHsBd,QAAA,CAAAe,eAAe,YAAjCC,MAAM,EAAEC,KAAK;yBAHvBzB,mBAAA,CAMM;MALJH,KAAK,EAAC,eAAe;MACpB6B,WAAS,WAAAA,YAAAnB,MAAA;QAAA,OAAEC,QAAA,CAAAmB,YAAY,CAACH,MAAM;MAAA;MAE9BI,GAAG,EAAEH;wBACDD,MAAM,CAACpB,IAAI,IAAIoB,MAAM,CAACK,EAAE,gDAAAC,UAAA;oEANvBd,KAAA,CAAAe,YAAY,E"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}