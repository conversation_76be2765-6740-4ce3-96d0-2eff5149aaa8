{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _Stepper from \"./Stepper.mjs\";\nvar Stepper = withInstall(_Stepper);\nvar stdin_default = Stepper;\nexport { Stepper, stdin_default as default };", "map": {"version": 3, "names": ["withInstall", "_Stepper", "Stepper", "stdin_default", "default"], "sources": ["C:/Users/<USER>/Desktop/vue3_3.0/node_modules/vant/es/stepper/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _Stepper from \"./Stepper.mjs\";\nconst Stepper = withInstall(_Stepper);\nvar stdin_default = Stepper;\nexport {\n  Stepper,\n  stdin_default as default\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,QAAQ,MAAM,eAAe;AACpC,IAAMC,OAAO,GAAGF,WAAW,CAACC,QAAQ,CAAC;AACrC,IAAIE,aAAa,GAAGD,OAAO;AAC3B,SACEA,OAAO,EACPC,aAAa,IAAIC,OAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}