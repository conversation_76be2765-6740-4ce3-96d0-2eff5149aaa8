{"ast": null, "code": "function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nfunction _iterableToArrayLimit(arr, i) { var _i = null == arr ? null : \"undefined\" != typeof Symbol && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (null != _i) { var _s, _e, _x, _r, _arr = [], _n = !0, _d = !1; try { if (_x = (_i = _i.call(arr)).next, 0 === i) { if (Object(_i) !== _i) return; _n = !1; } else for (; !(_n = (_s = _x.call(_i)).done) && (_arr.push(_s.value), _arr.length !== i); _n = !0); } catch (err) { _d = !0, _e = err; } finally { try { if (!_n && null != _i.return && (_r = _i.return(), Object(_r) !== _r)) return; } finally { if (_d) throw _e; } } return _arr; } }\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\nimport { withDirectives as _withDirectives, vShow as _vShow, createVNode as _createVNode } from \"vue\";\nimport { ref, watch, computed, nextTick, reactive, defineComponent } from \"vue\";\nimport { extend, isObject, isMobile, truthProp, numericProp, makeArrayProp, makeNumericProp, createNamespace } from \"../utils/index.mjs\";\nimport { useExpose } from \"../composables/use-expose.mjs\";\nimport { Area } from \"../area/index.mjs\";\nimport { Cell } from \"../cell/index.mjs\";\nimport { Form } from \"../form/index.mjs\";\nimport { Field } from \"../field/index.mjs\";\nimport { Popup } from \"../popup/index.mjs\";\nimport { Toast } from \"../toast/index.mjs\";\nimport { Button } from \"../button/index.mjs\";\nimport { Switch } from \"../switch/index.mjs\";\nimport AddressEditDetail from \"./AddressEditDetail.mjs\";\nvar _createNamespace = createNamespace(\"address-edit\"),\n  _createNamespace2 = _slicedToArray(_createNamespace, 3),\n  name = _createNamespace2[0],\n  bem = _createNamespace2[1],\n  t = _createNamespace2[2];\nvar DEFAULT_DATA = {\n  name: \"\",\n  tel: \"\",\n  city: \"\",\n  county: \"\",\n  country: \"\",\n  province: \"\",\n  areaCode: \"\",\n  isDefault: false,\n  postalCode: \"\",\n  addressDetail: \"\"\n};\nvar isPostal = function isPostal(value) {\n  return /^\\d{6}$/.test(value);\n};\nvar addressEditProps = {\n  areaList: Object,\n  isSaving: Boolean,\n  isDeleting: Boolean,\n  validator: Function,\n  showArea: truthProp,\n  showDetail: truthProp,\n  showDelete: Boolean,\n  showPostal: Boolean,\n  disableArea: Boolean,\n  searchResult: Array,\n  telMaxlength: numericProp,\n  showSetDefault: Boolean,\n  saveButtonText: String,\n  areaPlaceholder: String,\n  deleteButtonText: String,\n  showSearchResult: Boolean,\n  detailRows: makeNumericProp(1),\n  detailMaxlength: makeNumericProp(200),\n  areaColumnsPlaceholder: makeArrayProp(),\n  addressInfo: {\n    type: Object,\n    default: function _default() {\n      return extend({}, DEFAULT_DATA);\n    }\n  },\n  telValidator: {\n    type: Function,\n    default: isMobile\n  },\n  postalValidator: {\n    type: Function,\n    default: isPostal\n  }\n};\nvar stdin_default = defineComponent({\n  name: name,\n  props: addressEditProps,\n  emits: [\"save\", \"focus\", \"delete\", \"click-area\", \"change-area\", \"change-detail\", \"select-search\", \"change-default\"],\n  setup: function setup(props, _ref) {\n    var emit = _ref.emit,\n      slots = _ref.slots;\n    var areaRef = ref();\n    var data = reactive({});\n    var showAreaPopup = ref(false);\n    var detailFocused = ref(false);\n    var areaListLoaded = computed(function () {\n      return isObject(props.areaList) && Object.keys(props.areaList).length;\n    });\n    var areaText = computed(function () {\n      var country = data.country,\n        province = data.province,\n        city = data.city,\n        county = data.county,\n        areaCode = data.areaCode;\n      if (areaCode) {\n        var arr = [country, province, city, county];\n        if (province && province === city) {\n          arr.splice(1, 1);\n        }\n        return arr.filter(Boolean).join(\"/\");\n      }\n      return \"\";\n    });\n    var hideBottomFields = computed(function () {\n      var _a;\n      return ((_a = props.searchResult) == null ? void 0 : _a.length) && detailFocused.value;\n    });\n    var assignAreaValues = function assignAreaValues() {\n      if (areaRef.value) {\n        var detail = areaRef.value.getArea();\n        detail.areaCode = detail.code;\n        delete detail.code;\n        extend(data, detail);\n      }\n    };\n    var _onFocus = function onFocus(key) {\n      detailFocused.value = key === \"addressDetail\";\n      emit(\"focus\", key);\n    };\n    var rules = computed(function () {\n      var _validator = props.validator,\n        telValidator = props.telValidator,\n        postalValidator = props.postalValidator;\n      var makeRule = function makeRule(name2, emptyMessage) {\n        return {\n          validator: function validator(value) {\n            if (_validator) {\n              var message = _validator(name2, value);\n              if (message) {\n                return message;\n              }\n            }\n            if (!value) {\n              return emptyMessage;\n            }\n            return true;\n          }\n        };\n      };\n      return {\n        name: [makeRule(\"name\", t(\"nameEmpty\"))],\n        tel: [makeRule(\"tel\", t(\"telInvalid\")), {\n          validator: telValidator,\n          message: t(\"telInvalid\")\n        }],\n        areaCode: [makeRule(\"areaCode\", t(\"areaEmpty\"))],\n        addressDetail: [makeRule(\"addressDetail\", t(\"addressEmpty\"))],\n        postalCode: [makeRule(\"addressDetail\", t(\"postalEmpty\")), {\n          validator: postalValidator,\n          message: t(\"postalEmpty\")\n        }]\n      };\n    });\n    var onSave = function onSave() {\n      return emit(\"save\", data);\n    };\n    var onChangeDetail = function onChangeDetail(val) {\n      data.addressDetail = val;\n      emit(\"change-detail\", val);\n    };\n    var onAreaConfirm = function onAreaConfirm(values) {\n      values = values.filter(Boolean);\n      if (values.some(function (value) {\n        return !value.code;\n      })) {\n        Toast(t(\"areaEmpty\"));\n      } else {\n        showAreaPopup.value = false;\n        assignAreaValues();\n        emit(\"change-area\", values);\n      }\n    };\n    var onDelete = function onDelete() {\n      return emit(\"delete\", data);\n    };\n    var getArea = function getArea() {\n      var _a;\n      return ((_a = areaRef.value) == null ? void 0 : _a.getValues()) || [];\n    };\n    var setAreaCode = function setAreaCode(code) {\n      data.areaCode = code || \"\";\n      if (code) {\n        nextTick(assignAreaValues);\n      }\n    };\n    var onDetailBlur = function onDetailBlur() {\n      setTimeout(function () {\n        detailFocused.value = false;\n      });\n    };\n    var setAddressDetail = function setAddressDetail(value) {\n      data.addressDetail = value;\n    };\n    var renderSetDefaultCell = function renderSetDefaultCell() {\n      if (props.showSetDefault) {\n        var slots2 = {\n          \"right-icon\": function rightIcon() {\n            return _createVNode(Switch, {\n              \"modelValue\": data.isDefault,\n              \"onUpdate:modelValue\": function onUpdateModelValue($event) {\n                return data.isDefault = $event;\n              },\n              \"size\": \"24\",\n              \"onChange\": function onChange(event) {\n                return emit(\"change-default\", event);\n              }\n            }, null);\n          }\n        };\n        return _withDirectives(_createVNode(Cell, {\n          \"center\": true,\n          \"title\": t(\"defaultAddress\"),\n          \"class\": bem(\"default\")\n        }, slots2), [[_vShow, !hideBottomFields.value]]);\n      }\n    };\n    useExpose({\n      getArea: getArea,\n      setAreaCode: setAreaCode,\n      setAddressDetail: setAddressDetail\n    });\n    watch(function () {\n      return props.areaList;\n    }, function () {\n      return setAreaCode(data.areaCode);\n    });\n    watch(function () {\n      return props.addressInfo;\n    }, function (value) {\n      extend(data, DEFAULT_DATA, value);\n      setAreaCode(value.areaCode);\n    }, {\n      deep: true,\n      immediate: true\n    });\n    return function () {\n      var disableArea = props.disableArea;\n      return _createVNode(Form, {\n        \"class\": bem(),\n        \"onSubmit\": onSave\n      }, {\n        default: function _default() {\n          var _a;\n          return [_createVNode(\"div\", {\n            \"class\": bem(\"fields\")\n          }, [_createVNode(Field, {\n            \"modelValue\": data.name,\n            \"onUpdate:modelValue\": function onUpdateModelValue($event) {\n              return data.name = $event;\n            },\n            \"clearable\": true,\n            \"label\": t(\"name\"),\n            \"rules\": rules.value.name,\n            \"placeholder\": t(\"name\"),\n            \"onFocus\": function onFocus() {\n              return _onFocus(\"name\");\n            }\n          }, null), _createVNode(Field, {\n            \"modelValue\": data.tel,\n            \"onUpdate:modelValue\": function onUpdateModelValue($event) {\n              return data.tel = $event;\n            },\n            \"clearable\": true,\n            \"type\": \"tel\",\n            \"label\": t(\"tel\"),\n            \"rules\": rules.value.tel,\n            \"maxlength\": props.telMaxlength,\n            \"placeholder\": t(\"tel\"),\n            \"onFocus\": function onFocus() {\n              return _onFocus(\"tel\");\n            }\n          }, null), _withDirectives(_createVNode(Field, {\n            \"readonly\": true,\n            \"label\": t(\"area\"),\n            \"is-link\": !disableArea,\n            \"modelValue\": areaText.value,\n            \"rules\": rules.value.areaCode,\n            \"placeholder\": props.areaPlaceholder || t(\"area\"),\n            \"onFocus\": function onFocus() {\n              return _onFocus(\"areaCode\");\n            },\n            \"onClick\": function onClick() {\n              emit(\"click-area\");\n              showAreaPopup.value = !disableArea;\n            }\n          }, null), [[_vShow, props.showArea]]), _createVNode(AddressEditDetail, {\n            \"show\": props.showDetail,\n            \"rows\": props.detailRows,\n            \"rules\": rules.value.addressDetail,\n            \"value\": data.addressDetail,\n            \"focused\": detailFocused.value,\n            \"maxlength\": props.detailMaxlength,\n            \"searchResult\": props.searchResult,\n            \"showSearchResult\": props.showSearchResult,\n            \"onBlur\": onDetailBlur,\n            \"onFocus\": function onFocus() {\n              return _onFocus(\"addressDetail\");\n            },\n            \"onInput\": onChangeDetail,\n            \"onSelect-search\": function onSelectSearch(event) {\n              return emit(\"select-search\", event);\n            }\n          }, null), props.showPostal && _withDirectives(_createVNode(Field, {\n            \"modelValue\": data.postalCode,\n            \"onUpdate:modelValue\": function onUpdateModelValue($event) {\n              return data.postalCode = $event;\n            },\n            \"type\": \"tel\",\n            \"rules\": rules.value.postalCode,\n            \"label\": t(\"postal\"),\n            \"maxlength\": \"6\",\n            \"placeholder\": t(\"postal\"),\n            \"onFocus\": function onFocus() {\n              return _onFocus(\"postalCode\");\n            }\n          }, null), [[_vShow, !hideBottomFields.value]]), (_a = slots.default) == null ? void 0 : _a.call(slots)]), renderSetDefaultCell(), _withDirectives(_createVNode(\"div\", {\n            \"class\": bem(\"buttons\")\n          }, [_createVNode(Button, {\n            \"block\": true,\n            \"round\": true,\n            \"type\": \"danger\",\n            \"text\": props.saveButtonText || t(\"save\"),\n            \"class\": bem(\"button\"),\n            \"loading\": props.isSaving,\n            \"nativeType\": \"submit\"\n          }, null), props.showDelete && _createVNode(Button, {\n            \"block\": true,\n            \"round\": true,\n            \"class\": bem(\"button\"),\n            \"loading\": props.isDeleting,\n            \"text\": props.deleteButtonText || t(\"delete\"),\n            \"onClick\": onDelete\n          }, null)]), [[_vShow, !hideBottomFields.value]]), _createVNode(Popup, {\n            \"show\": showAreaPopup.value,\n            \"onUpdate:show\": function onUpdateShow($event) {\n              return showAreaPopup.value = $event;\n            },\n            \"round\": true,\n            \"teleport\": \"body\",\n            \"position\": \"bottom\",\n            \"lazyRender\": false\n          }, {\n            default: function _default() {\n              return [_createVNode(Area, {\n                \"ref\": areaRef,\n                \"value\": data.areaCode,\n                \"loading\": !areaListLoaded.value,\n                \"areaList\": props.areaList,\n                \"columnsPlaceholder\": props.areaColumnsPlaceholder,\n                \"onConfirm\": onAreaConfirm,\n                \"onCancel\": function onCancel() {\n                  showAreaPopup.value = false;\n                }\n              }, null)];\n            }\n          })];\n        }\n      });\n    };\n  }\n});\nexport { stdin_default as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}