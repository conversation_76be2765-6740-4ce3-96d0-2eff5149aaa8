{"ast": null, "code": "function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nfunction _iterableToArrayLimit(arr, i) { var _i = null == arr ? null : \"undefined\" != typeof Symbol && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (null != _i) { var _s, _e, _x, _r, _arr = [], _n = !0, _d = !1; try { if (_x = (_i = _i.call(arr)).next, 0 === i) { if (Object(_i) !== _i) return; _n = !1; } else for (; !(_n = (_s = _x.call(_i)).done) && (_arr.push(_s.value), _arr.length !== i); _n = !0); } catch (err) { _d = !0, _e = err; } finally { try { if (!_n && null != _i.return && (_r = _i.return(), Object(_r) !== _r)) return; } finally { if (_d) throw _e; } } return _arr; } }\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\nimport { createVNode as _createVNode } from \"vue\";\nimport { defineComponent } from \"vue\";\nimport { addUnit, makeArrayProp, makeStringProp, makeNumericProp, createNamespace } from \"../utils/index.mjs\";\nimport { Icon } from \"../icon/index.mjs\";\nimport { Sidebar } from \"../sidebar/index.mjs\";\nimport { SidebarItem } from \"../sidebar-item/index.mjs\";\nvar _createNamespace = createNamespace(\"tree-select\"),\n  _createNamespace2 = _slicedToArray(_createNamespace, 2),\n  name = _createNamespace2[0],\n  bem = _createNamespace2[1];\nvar treeSelectProps = {\n  max: makeNumericProp(Infinity),\n  items: makeArrayProp(),\n  height: makeNumericProp(300),\n  selectedIcon: makeStringProp(\"success\"),\n  mainActiveIndex: makeNumericProp(0),\n  activeId: {\n    type: [Number, String, Array],\n    default: 0\n  }\n};\nvar stdin_default = defineComponent({\n  name: name,\n  props: treeSelectProps,\n  emits: [\"click-nav\", \"click-item\", \"update:activeId\", \"update:mainActiveIndex\"],\n  setup: function setup(props, _ref) {\n    var emit = _ref.emit,\n      slots = _ref.slots;\n    var isActiveItem = function isActiveItem(id) {\n      return Array.isArray(props.activeId) ? props.activeId.includes(id) : props.activeId === id;\n    };\n    var renderSubItem = function renderSubItem(item) {\n      var onClick = function onClick() {\n        if (item.disabled) {\n          return;\n        }\n        var activeId;\n        if (Array.isArray(props.activeId)) {\n          activeId = props.activeId.slice();\n          var index = activeId.indexOf(item.id);\n          if (index !== -1) {\n            activeId.splice(index, 1);\n          } else if (activeId.length < props.max) {\n            activeId.push(item.id);\n          }\n        } else {\n          activeId = item.id;\n        }\n        emit(\"update:activeId\", activeId);\n        emit(\"click-item\", item);\n      };\n      return _createVNode(\"div\", {\n        \"key\": item.id,\n        \"class\": [\"van-ellipsis\", bem(\"item\", {\n          active: isActiveItem(item.id),\n          disabled: item.disabled\n        })],\n        \"onClick\": onClick\n      }, [item.text, isActiveItem(item.id) && _createVNode(Icon, {\n        \"name\": props.selectedIcon,\n        \"class\": bem(\"selected\")\n      }, null)]);\n    };\n    var onSidebarChange = function onSidebarChange(index) {\n      emit(\"update:mainActiveIndex\", index);\n    };\n    var onClickSidebarItem = function onClickSidebarItem(index) {\n      return emit(\"click-nav\", index);\n    };\n    var renderSidebar = function renderSidebar() {\n      var Items = props.items.map(function (item) {\n        return _createVNode(SidebarItem, {\n          \"dot\": item.dot,\n          \"title\": item.text,\n          \"badge\": item.badge,\n          \"class\": [bem(\"nav-item\"), item.className],\n          \"disabled\": item.disabled,\n          \"onClick\": onClickSidebarItem\n        }, null);\n      });\n      return _createVNode(Sidebar, {\n        \"class\": bem(\"nav\"),\n        \"modelValue\": props.mainActiveIndex,\n        \"onChange\": onSidebarChange\n      }, {\n        default: function _default() {\n          return [Items];\n        }\n      });\n    };\n    var renderContent = function renderContent() {\n      if (slots.content) {\n        return slots.content();\n      }\n      var selected = props.items[+props.mainActiveIndex] || {};\n      if (selected.children) {\n        return selected.children.map(renderSubItem);\n      }\n    };\n    return function () {\n      return _createVNode(\"div\", {\n        \"class\": bem(),\n        \"style\": {\n          height: addUnit(props.height)\n        }\n      }, [renderSidebar(), _createVNode(\"div\", {\n        \"class\": bem(\"content\")\n      }, [renderContent()])]);\n    };\n  }\n});\nexport { stdin_default as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}