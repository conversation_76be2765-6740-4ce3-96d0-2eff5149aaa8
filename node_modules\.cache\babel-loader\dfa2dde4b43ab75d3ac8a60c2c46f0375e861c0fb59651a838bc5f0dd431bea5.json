{"ast": null, "code": "import Lazy from \"./lazy.mjs\";\nimport LazyComponent from \"./lazy-component.mjs\";\nimport <PERSON><PERSON><PERSON>ontaine<PERSON> from \"./lazy-container.mjs\";\nimport LazyImage from \"./lazy-image.mjs\";\nvar Lazyload = {\n  install: function install(app) {\n    var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    var LazyClass = Lazy();\n    var lazy = new LazyClass(options);\n    var lazyContainer = new LazyContainer({\n      lazy: lazy\n    });\n    app.config.globalProperties.$Lazyload = lazy;\n    if (options.lazyComponent) {\n      app.component(\"LazyComponent\", LazyComponent(lazy));\n    }\n    if (options.lazyImage) {\n      app.component(\"LazyImage\", LazyImage(lazy));\n    }\n    app.directive(\"lazy\", {\n      beforeMount: lazy.add.bind(lazy),\n      updated: lazy.update.bind(lazy),\n      unmounted: lazy.remove.bind(lazy)\n    });\n    app.directive(\"lazy-container\", {\n      beforeMount: lazyContainer.bind.bind(lazyContainer),\n      updated: lazyContainer.update.bind(lazyContainer),\n      unmounted: lazyContainer.unbind.bind(lazyContainer)\n    });\n  }\n};\nexport { Lazyload };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}