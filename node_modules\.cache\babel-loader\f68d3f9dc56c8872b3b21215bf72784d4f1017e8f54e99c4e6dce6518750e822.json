{"ast": null, "code": "function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nfunction _iterableToArrayLimit(arr, i) { var _i = null == arr ? null : \"undefined\" != typeof Symbol && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (null != _i) { var _s, _e, _x, _r, _arr = [], _n = !0, _d = !1; try { if (_x = (_i = _i.call(arr)).next, 0 === i) { if (Object(_i) !== _i) return; _n = !1; } else for (; !(_n = (_s = _x.call(_i)).done) && (_arr.push(_s.value), _arr.length !== i); _n = !0); } catch (err) { _d = !0, _e = err; } finally { try { if (!_n && null != _i.return && (_r = _i.return(), Object(_r) !== _r)) return; } finally { if (_d) throw _e; } } return _arr; } }\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\nimport { mergeProps as _mergeProps, createVNode as _createVNode } from \"vue\";\nimport { ref, defineComponent } from \"vue\";\nimport { pick, extend, truthProp, preventDefault, makeStringProp, createNamespace } from \"../utils/index.mjs\";\nimport { fieldSharedProps } from \"../field/Field.mjs\";\nimport { useId } from \"../composables/use-id.mjs\";\nimport { useExpose } from \"../composables/use-expose.mjs\";\nimport { Field } from \"../field/index.mjs\";\nvar _createNamespace = createNamespace(\"search\"),\n  _createNamespace2 = _slicedToArray(_createNamespace, 3),\n  name = _createNamespace2[0],\n  bem = _createNamespace2[1],\n  t = _createNamespace2[2];\nvar searchProps = extend({}, fieldSharedProps, {\n  label: String,\n  shape: makeStringProp(\"square\"),\n  leftIcon: makeStringProp(\"search\"),\n  clearable: truthProp,\n  actionText: String,\n  background: String,\n  showAction: Boolean\n});\nvar stdin_default = defineComponent({\n  name: name,\n  props: searchProps,\n  emits: [\"blur\", \"focus\", \"clear\", \"search\", \"cancel\", \"click-input\", \"click-left-icon\", \"click-right-icon\", \"update:modelValue\"],\n  setup: function setup(props, _ref) {\n    var emit = _ref.emit,\n      slots = _ref.slots,\n      attrs = _ref.attrs;\n    var id = useId();\n    var filedRef = ref();\n    var onCancel = function onCancel() {\n      if (!slots.action) {\n        emit(\"update:modelValue\", \"\");\n        emit(\"cancel\");\n      }\n    };\n    var onKeypress = function onKeypress(event) {\n      var ENTER_CODE = 13;\n      if (event.keyCode === ENTER_CODE) {\n        preventDefault(event);\n        emit(\"search\", props.modelValue);\n      }\n    };\n    var getInputId = function getInputId() {\n      return props.id || \"\".concat(id, \"-input\");\n    };\n    var renderLabel = function renderLabel() {\n      if (slots.label || props.label) {\n        return _createVNode(\"label\", {\n          \"class\": bem(\"label\"),\n          \"for\": getInputId()\n        }, [slots.label ? slots.label() : props.label]);\n      }\n    };\n    var renderAction = function renderAction() {\n      if (props.showAction) {\n        var text = props.actionText || t(\"cancel\");\n        return _createVNode(\"div\", {\n          \"class\": bem(\"action\"),\n          \"role\": \"button\",\n          \"tabindex\": 0,\n          \"onClick\": onCancel\n        }, [slots.action ? slots.action() : text]);\n      }\n    };\n    var blur = function blur() {\n      var _a;\n      return (_a = filedRef.value) == null ? void 0 : _a.blur();\n    };\n    var focus = function focus() {\n      var _a;\n      return (_a = filedRef.value) == null ? void 0 : _a.focus();\n    };\n    var onBlur = function onBlur(event) {\n      return emit(\"blur\", event);\n    };\n    var onFocus = function onFocus(event) {\n      return emit(\"focus\", event);\n    };\n    var onClear = function onClear(event) {\n      return emit(\"clear\", event);\n    };\n    var onClickInput = function onClickInput(event) {\n      return emit(\"click-input\", event);\n    };\n    var onClickLeftIcon = function onClickLeftIcon(event) {\n      return emit(\"click-left-icon\", event);\n    };\n    var onClickRightIcon = function onClickRightIcon(event) {\n      return emit(\"click-right-icon\", event);\n    };\n    var fieldPropNames = Object.keys(fieldSharedProps);\n    var renderField = function renderField() {\n      var fieldAttrs = extend({}, attrs, pick(props, fieldPropNames), {\n        id: getInputId()\n      });\n      var onInput = function onInput(value) {\n        return emit(\"update:modelValue\", value);\n      };\n      return _createVNode(Field, _mergeProps({\n        \"ref\": filedRef,\n        \"type\": \"search\",\n        \"class\": bem(\"field\"),\n        \"border\": false,\n        \"onBlur\": onBlur,\n        \"onFocus\": onFocus,\n        \"onClear\": onClear,\n        \"onKeypress\": onKeypress,\n        \"onClick-input\": onClickInput,\n        \"onClick-left-icon\": onClickLeftIcon,\n        \"onClick-right-icon\": onClickRightIcon,\n        \"onUpdate:modelValue\": onInput\n      }, fieldAttrs), pick(slots, [\"left-icon\", \"right-icon\"]));\n    };\n    useExpose({\n      focus: focus,\n      blur: blur\n    });\n    return function () {\n      var _a;\n      return _createVNode(\"div\", {\n        \"class\": bem({\n          \"show-action\": props.showAction\n        }),\n        \"style\": {\n          background: props.background\n        }\n      }, [(_a = slots.left) == null ? void 0 : _a.call(slots), _createVNode(\"div\", {\n        \"class\": bem(\"content\", props.shape)\n      }, [renderLabel(), renderField()]), renderAction()]);\n    };\n  }\n});\nexport { stdin_default as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}