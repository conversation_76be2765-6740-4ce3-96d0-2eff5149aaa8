{"ast": null, "code": "import { ref } from 'vue';\nimport { caiwu } from '@/api/self/index';\nimport { useRouter } from 'vue-router';\nimport { useI18n } from 'vue-i18n';\nimport store from '@/store/index';\nimport { formatTime } from '@/api/format.js';\nexport default {\n  setup: function setup() {\n    var _store$state$baseInfo;\n    var _useRouter = useRouter(),\n      push = _useRouter.push;\n    var _useI18n = useI18n(),\n      t = _useI18n.t;\n    var currency = ref((_store$state$baseInfo = store.state.baseInfo) === null || _store$state$baseInfo === void 0 ? void 0 : _store$state$baseInfo.currency);\n    var list = ref([]);\n    var page = ref(0);\n    var type = ref('all');\n    var loading = ref(false);\n    var finished = ref(false);\n    var tabs = ref([\n    // {label: t('msg.all'),value: 'all'},\n    {\n      label: t('msg.chongzhi'),\n      value: 1\n    }, {\n      label: t('msg.tixian'),\n      value: 7\n    }, {\n      label: t('msg.jiaoyi'),\n      value: 2\n    }, {\n      label: t('msg.fanyong'),\n      value: 3\n    }, {\n      label: t('msg.tgfy'),\n      value: 5\n    }, {\n      label: t('msg.xjjyfy'),\n      value: 6\n    }]);\n    var clickLeft = function clickLeft() {\n      push('/self');\n    };\n    var clickRight = function clickRight() {\n      push('/message');\n    };\n    var getCW = function getCW(name, num) {\n      if (num) {\n        page.value = num;\n      } else {\n        ++page.value;\n      }\n      type.value = name && name != 'all' ? name : 0;\n      var json = {\n        page: page.value,\n        size: 10,\n        type: type.value\n      };\n      caiwu(json).then(function (res) {\n        loading.value = false;\n        if (res.code === 0) {\n          var _res$data, _res$data2;\n          finished.value = !((_res$data = res.data) !== null && _res$data !== void 0 && _res$data.paging);\n          list.value = list.value.concat((_res$data2 = res.data) === null || _res$data2 === void 0 ? void 0 : _res$data2.list);\n        }\n      });\n    };\n    getCW();\n    return {\n      formatTime: formatTime,\n      list: list,\n      clickLeft: clickLeft,\n      clickRight: clickRight,\n      tabs: tabs,\n      getCW: getCW,\n      loading: loading,\n      finished: finished,\n      currency: currency\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "caiwu", "useRouter", "useI18n", "store", "formatTime", "setup", "_store$state$baseInfo", "_useRouter", "push", "_useI18n", "t", "currency", "state", "baseInfo", "list", "page", "type", "loading", "finished", "tabs", "label", "value", "clickLeft", "clickRight", "getCW", "name", "num", "json", "size", "then", "res", "code", "_res$data", "_res$data2", "data", "paging", "concat"], "sources": ["C:\\Users\\<USER>\\Desktop\\vue3_3.0\\src\\views\\self\\components\\account_details.vue"], "sourcesContent": ["<template>\r\n    <div class=\"tel home\">\r\n        <van-nav-bar :title=\"$t('msg.zbjl')\" left-arrow @click-left=\"$router.go(-1)\">\r\n            <!-- <template #right>\r\n                <van-icon name=\"comment-o\" size=\"18\"/>\r\n            </template> -->\r\n        </van-nav-bar>\r\n        <!-- <van-tabs v-model:active=\"type\" swipeable @change=\"getCW\">\r\n            <van-tab v-for=\"(item,index) in tabs\" :key=\"index\" :title=\"item.label\" :name=\"item.value\"></van-tab>\r\n        </van-tabs> -->\r\n        <van-list\r\n            v-model:loading=\"loading\"\r\n            :finished=\"finished\"\r\n            :immediate-check=\"false\"\r\n            :offset=\"100\"\r\n            :finished-text=\"$t('msg.not_move')\"\r\n            @load=\"getCW\"\r\n            :loading-text=\"$t('msg.loading')\"\r\n            >\r\n                <div class=\"address\" v-for=\"(item,index) in list\" :key=\"index\">\r\n                    <div class=\"l\">\r\n                        <div class=\"time red\">{{$t('msg.zblx')}}：{{tabs?.find(rr=>rr.value==item.type)?.label || $t('msg.all')}}</div>\r\n                        <div class=\"time\">{{$t('msg.zqje')}}：{{currency}}{{item.balance || '0.00'}}</div>\r\n                        <div class=\"time green\">{{$t('msg.zbje')}}：{{currency}}{{item.num}}</div>\r\n                        <div class=\"time\">{{$t('msg.zhje')}}：{{currency}}{{item.status == 1 ? item.balance*1 + item.num*1 :  item.balance*1 - item.num*1}}</div>\r\n                        <div class=\"time\">{{$t('msg.zbsj')}}：{{formatTime('',item.addtime)}}</div>\r\n                    </div>\r\n                </div>\r\n        </van-list>\r\n    </div>\r\n</template>\r\n<script>\r\nimport { ref} from 'vue';\r\nimport {caiwu} from '@/api/self/index'\r\nimport { useRouter } from 'vue-router';\r\nimport { useI18n } from 'vue-i18n'\r\nimport store from '@/store/index'\r\nimport {formatTime} from '@/api/format.js'\r\nexport default {\r\n    setup(){\r\n        const { push } = useRouter();\r\n        const { t } = useI18n()\r\n        \r\n        const currency = ref(store.state.baseInfo?.currency)\r\n        const list = ref([])\r\n        const page = ref(0)\r\n        const type = ref('all')\r\n\r\n        const loading = ref(false);\r\n        const finished = ref(false);\r\n\r\n        const tabs = ref([\r\n            // {label: t('msg.all'),value: 'all'},\r\n            {label: t('msg.chongzhi'),value: 1},\r\n            {label: t('msg.tixian'),value: 7},\r\n            {label: t('msg.jiaoyi'),value: 2},\r\n            {label: t('msg.fanyong'),value: 3},\r\n            {label: t('msg.tgfy'),value: 5},\r\n            {label: t('msg.xjjyfy'),value: 6},\r\n        ])\r\n\r\n        const clickLeft = () => {\r\n            push('/self')\r\n        }\r\n        const clickRight = () => {\r\n            push('/message')\r\n        }\r\n        const getCW = (name,num) => {\r\n            if(num) {\r\n                page.value = num\r\n            } else {\r\n                ++page.value\r\n            }\r\n            type.value = name && name != 'all' ? name : 0\r\n            let json = {\r\n                page: page.value,\r\n                size: 10,\r\n                type: type.value\r\n            }\r\n            caiwu(json).then(res => {\r\n                loading.value = false\r\n                if(res.code === 0) {\r\n                    finished.value = !(res.data?.paging)\r\n                    list.value = list.value.concat(res.data?.list)\r\n                }\r\n            })\r\n        }\r\n        getCW()\r\n        return {formatTime,list,clickLeft,clickRight,tabs,getCW,loading,finished,currency}\r\n    }\r\n}\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.home{\r\n    // background-image: url('~@/assets/images/home/<USER>') !important;\r\n    background: #f5f5f5;\r\n    border-radius: 0;\r\n}\r\n.tel .van-nav-bar{\r\n    background-color: #fff !important;\r\n    color: #000 !important;\r\n}\r\n\r\n.tel{\r\n    overflow: auto;\r\n    :deep(.van-nav-bar){\r\n        background-color: $theme;\r\n        color: #fff;\r\n        .van-nav-bar__left{\r\n            .van-icon{\r\n                color: #000;\r\n            }\r\n        }\r\n        .van-nav-bar__title{\r\n            color: #000;\r\n        }\r\n        .van-nav-bar__right{\r\n            img{\r\n                height: 42px;\r\n            }\r\n            .van-icon{\r\n                color: #000;\r\n            }\r\n        }\r\n    }\r\n    :deep(.van-tabs){\r\n        padding: 20px 0;\r\n        .van-tabs__nav{\r\n            // justify-content: space-around;\r\n            .van-tab{\r\n                // flex: auto;\r\n                &.van-tab--active{\r\n                    font-weight: 600;\r\n                    color: $theme;\r\n                }\r\n            }\r\n            .van-tabs__line{\r\n                background-color: $theme;\r\n            }\r\n        }\r\n    }\r\n    :deep(.van-list){\r\n        padding-top: 24px;\r\n        \r\n        .address{\r\n            box-shadow: $shadow;\r\n            border-radius: 12px;\r\n            padding: 30px;\r\n            margin: 0 30px 40px;\r\n            text-align: left;\r\n            display: flex;\r\n            justify-content: space-between;\r\n            background-color: #fff;\r\n            .l{\r\n                .time{\r\n                    font-size: 28px;\r\n                    // font-weight: 600;\r\n                    color: #333;\r\n                    &.red{\r\n                        color: red;\r\n                    }\r\n                    &.green{\r\n                        color: green;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n</style>"], "mappings": "AAgCA,SAASA,GAAG,QAAO,KAAK;AACxB,SAAQC,KAAK,QAAO,kBAAiB;AACrC,SAASC,SAAQ,QAAS,YAAY;AACtC,SAASC,OAAM,QAAS,UAAS;AACjC,OAAOC,KAAI,MAAO,eAAc;AAChC,SAAQC,UAAU,QAAO,iBAAgB;AACzC,eAAe;EACXC,KAAK,WAAAA,MAAA,EAAE;IAAA,IAAAC,qBAAA;IACH,IAAAC,UAAA,GAAiBN,SAAS,CAAC,CAAC;MAApBO,IAAG,GAAAD,UAAA,CAAHC,IAAG;IACX,IAAAC,QAAA,GAAcP,OAAO,CAAC;MAAdQ,CAAA,GAAAD,QAAA,CAAAC,CAAA;IAER,IAAMC,QAAO,GAAIZ,GAAG,EAAAO,qBAAA,GAACH,KAAK,CAACS,KAAK,CAACC,QAAQ,cAAAP,qBAAA,uBAApBA,qBAAA,CAAsBK,QAAQ;IACnD,IAAMG,IAAG,GAAIf,GAAG,CAAC,EAAE;IACnB,IAAMgB,IAAG,GAAIhB,GAAG,CAAC,CAAC;IAClB,IAAMiB,IAAG,GAAIjB,GAAG,CAAC,KAAK;IAEtB,IAAMkB,OAAM,GAAIlB,GAAG,CAAC,KAAK,CAAC;IAC1B,IAAMmB,QAAO,GAAInB,GAAG,CAAC,KAAK,CAAC;IAE3B,IAAMoB,IAAG,GAAIpB,GAAG,CAAC;IACb;IACA;MAACqB,KAAK,EAAEV,CAAC,CAAC,cAAc,CAAC;MAACW,KAAK,EAAE;IAAC,CAAC,EACnC;MAACD,KAAK,EAAEV,CAAC,CAAC,YAAY,CAAC;MAACW,KAAK,EAAE;IAAC,CAAC,EACjC;MAACD,KAAK,EAAEV,CAAC,CAAC,YAAY,CAAC;MAACW,KAAK,EAAE;IAAC,CAAC,EACjC;MAACD,KAAK,EAAEV,CAAC,CAAC,aAAa,CAAC;MAACW,KAAK,EAAE;IAAC,CAAC,EAClC;MAACD,KAAK,EAAEV,CAAC,CAAC,UAAU,CAAC;MAACW,KAAK,EAAE;IAAC,CAAC,EAC/B;MAACD,KAAK,EAAEV,CAAC,CAAC,YAAY,CAAC;MAACW,KAAK,EAAE;IAAC,CAAC,CACpC;IAED,IAAMC,SAAQ,GAAI,SAAZA,SAAQA,CAAA,EAAU;MACpBd,IAAI,CAAC,OAAO;IAChB;IACA,IAAMe,UAAS,GAAI,SAAbA,UAASA,CAAA,EAAU;MACrBf,IAAI,CAAC,UAAU;IACnB;IACA,IAAMgB,KAAI,GAAI,SAARA,KAAIA,CAAKC,IAAI,EAACC,GAAG,EAAK;MACxB,IAAGA,GAAG,EAAE;QACJX,IAAI,CAACM,KAAI,GAAIK,GAAE;MACnB,OAAO;QACH,EAAEX,IAAI,CAACM,KAAI;MACf;MACAL,IAAI,CAACK,KAAI,GAAII,IAAG,IAAKA,IAAG,IAAK,KAAI,GAAIA,IAAG,GAAI;MAC5C,IAAIE,IAAG,GAAI;QACPZ,IAAI,EAAEA,IAAI,CAACM,KAAK;QAChBO,IAAI,EAAE,EAAE;QACRZ,IAAI,EAAEA,IAAI,CAACK;MACf;MACArB,KAAK,CAAC2B,IAAI,CAAC,CAACE,IAAI,CAAC,UAAAC,GAAE,EAAK;QACpBb,OAAO,CAACI,KAAI,GAAI,KAAI;QACpB,IAAGS,GAAG,CAACC,IAAG,KAAM,CAAC,EAAE;UAAA,IAAAC,SAAA,EAAAC,UAAA;UACff,QAAQ,CAACG,KAAI,GAAI,GAAAW,SAAA,GAAEF,GAAG,CAACI,IAAI,cAAAF,SAAA,eAARA,SAAA,CAAUG,MAAM;UACnCrB,IAAI,CAACO,KAAI,GAAIP,IAAI,CAACO,KAAK,CAACe,MAAM,EAAAH,UAAA,GAACH,GAAG,CAACI,IAAI,cAAAD,UAAA,uBAARA,UAAA,CAAUnB,IAAI;QACjD;MACJ,CAAC;IACL;IACAU,KAAK,CAAC;IACN,OAAO;MAACpB,UAAU,EAAVA,UAAU;MAACU,IAAI,EAAJA,IAAI;MAACQ,SAAS,EAATA,SAAS;MAACC,UAAU,EAAVA,UAAU;MAACJ,IAAI,EAAJA,IAAI;MAACK,KAAK,EAALA,KAAK;MAACP,OAAO,EAAPA,OAAO;MAACC,QAAQ,EAARA,QAAQ;MAACP,QAAQ,EAARA;IAAQ;EACrF;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}