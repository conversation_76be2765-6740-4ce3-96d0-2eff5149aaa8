{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _Image from \"./Image.mjs\";\nvar Image = withInstall(_Image);\nvar stdin_default = Image;\nexport { Image, stdin_default as default };", "map": {"version": 3, "names": ["withInstall", "_Image", "Image", "stdin_default", "default"], "sources": ["C:/Users/<USER>/Desktop/vue3_3.0/node_modules/vant/es/image/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _Image from \"./Image.mjs\";\nconst Image = withInstall(_Image);\nvar stdin_default = Image;\nexport {\n  Image,\n  stdin_default as default\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,MAAM,MAAM,aAAa;AAChC,IAAMC,KAAK,GAAGF,WAAW,CAACC,MAAM,CAAC;AACjC,IAAIE,aAAa,GAAGD,KAAK;AACzB,SACEA,KAAK,EACLC,aAAa,IAAIC,OAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}