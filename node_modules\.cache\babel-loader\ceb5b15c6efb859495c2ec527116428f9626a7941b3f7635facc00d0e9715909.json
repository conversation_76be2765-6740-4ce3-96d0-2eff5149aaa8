{"ast": null, "code": "import utils from '../utils.js';\nimport httpAdapter from './http.js';\nimport xhrAdapter from './xhr.js';\nimport AxiosError from \"../core/AxiosError.js\";\nvar knownAdapters = {\n  http: httpAdapter,\n  xhr: xhrAdapter\n};\nutils.forEach(knownAdapters, function (fn, value) {\n  if (fn) {\n    try {\n      Object.defineProperty(fn, 'name', {\n        value: value\n      });\n    } catch (e) {\n      // eslint-disable-next-line no-empty\n    }\n    Object.defineProperty(fn, 'adapterName', {\n      value: value\n    });\n  }\n});\nexport default {\n  getAdapter: function getAdapter(adapters) {\n    adapters = utils.isArray(adapters) ? adapters : [adapters];\n    var _adapters = adapters,\n      length = _adapters.length;\n    var nameOrAdapter;\n    var adapter;\n    for (var i = 0; i < length; i++) {\n      nameOrAdapter = adapters[i];\n      if (adapter = utils.isString(nameOrAdapter) ? knownAdapters[nameOrAdapter.toLowerCase()] : nameOrAdapter) {\n        break;\n      }\n    }\n    if (!adapter) {\n      if (adapter === false) {\n        throw new AxiosError(\"Adapter \".concat(nameOrAdapter, \" is not supported by the environment\"), 'ERR_NOT_SUPPORT');\n      }\n      throw new Error(utils.hasOwnProp(knownAdapters, nameOrAdapter) ? \"Adapter '\".concat(nameOrAdapter, \"' is not available in the build\") : \"Unknown adapter '\".concat(nameOrAdapter, \"'\"));\n    }\n    if (!utils.isFunction(adapter)) {\n      throw new TypeError('adapter is not a function');\n    }\n    return adapter;\n  },\n  adapters: knownAdapters\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}