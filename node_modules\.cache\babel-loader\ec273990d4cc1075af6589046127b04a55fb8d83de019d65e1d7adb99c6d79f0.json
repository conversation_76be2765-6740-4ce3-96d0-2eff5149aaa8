{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _Field from \"./Field.mjs\";\nvar Field = withInstall(_Field);\nvar stdin_default = Field;\nexport { Field, stdin_default as default };", "map": {"version": 3, "names": ["withInstall", "_Field", "Field", "stdin_default", "default"], "sources": ["C:/Users/<USER>/Downloads/Telegram Desktop/vue3_3.0/vue3_3.0/node_modules/vant/es/field/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _Field from \"./Field.mjs\";\nconst Field = withInstall(_Field);\nvar stdin_default = Field;\nexport {\n  Field,\n  stdin_default as default\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,MAAM,MAAM,aAAa;AAChC,IAAMC,KAAK,GAAGF,WAAW,CAACC,MAAM,CAAC;AACjC,IAAIE,aAAa,GAAGD,KAAK;AACzB,SACEA,KAAK,EACLC,aAAa,IAAIC,OAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}