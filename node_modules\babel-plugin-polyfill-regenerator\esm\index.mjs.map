{"version": 3, "file": "index.mjs", "sources": ["../src/index.ts"], "sourcesContent": ["import defineProvider from \"@babel/helper-define-polyfill-provider\";\nimport type { PluginPass } from \"@babel/core\";\n\nconst runtimeCompat = \"#__secret_key__@babel/runtime__compatibility\";\n\ntype Options = {\n  \"#__secret_key__@babel/runtime__compatibility\": void | {\n    useBabelRuntime: string;\n  };\n};\n\nexport default defineProvider<Options>(({ debug, targets, babel }, options) => {\n  if (!shallowEqual(targets, babel.targets())) {\n    throw new Error(\n      \"This plugin does not use the targets option. Only preset-env's targets\" +\n        \" or top-level targets need to be configured for this plugin to work.\" +\n        \" See https://github.com/babel/babel-polyfills/issues/36 for more\" +\n        \" details.\",\n    );\n  }\n\n  const { [runtimeCompat]: { useBabelRuntime = false } = {} } = options;\n\n  return {\n    name: \"regenerator\",\n\n    polyfills: [\"regenerator-runtime\"],\n\n    usageGlobal(meta, utils) {\n      if (isRegenerator(meta)) {\n        debug(\"regenerator-runtime\");\n        utils.injectGlobalImport(\"regenerator-runtime/runtime.js\");\n      }\n    },\n    usagePure(meta, utils, path) {\n      if (isRegenerator(meta)) {\n        let pureName = \"regenerator-runtime\";\n        if (useBabelRuntime) {\n          const runtimeName =\n            ((path.hub as any).file as PluginPass).get(\n              \"runtimeHelpersModuleName\",\n            ) ?? \"@babel/runtime\";\n          pureName = `${runtimeName}/regenerator`;\n        }\n\n        path.replaceWith(\n          utils.injectDefaultImport(pureName, \"regenerator-runtime\"),\n        );\n      }\n    },\n  };\n});\n\nconst isRegenerator = meta =>\n  meta.kind === \"global\" && meta.name === \"regeneratorRuntime\";\n\nfunction shallowEqual(obj1: any, obj2: any) {\n  return JSON.stringify(obj1) === JSON.stringify(obj2);\n}\n"], "names": ["runtimeCompat", "define<PERSON>rovider", "debug", "targets", "babel", "options", "shallowEqual", "Error", "useBabelRuntime", "name", "polyfills", "usageGlobal", "meta", "utils", "isRegenerator", "injectGlobalImport", "usagePure", "path", "pureName", "runtimeName", "hub", "file", "get", "replaceWith", "injectDefaultImport", "kind", "obj1", "obj2", "JSON", "stringify"], "mappings": ";;AAGA,MAAMA,aAAa,GAAG,8CAAtB;AAQA,YAAeC,cAAc,CAAU,CAAC;EAAEC,KAAF;EAASC,OAAT;EAAkBC;AAAlB,CAAD,EAA4BC,OAA5B,KAAwC;EAC7E,IAAI,CAACC,YAAY,CAACH,OAAD,EAAUC,KAAK,CAACD,OAAN,EAAV,CAAjB,EAA6C;IAC3C,MAAM,IAAII,KAAJ,CACJ,2EACE,sEADF,GAEE,kEAFF,GAGE,WAJE,CAAN;;;EAQF,MAAM;IAAE,CAACP,aAAD,GAAiB;MAAEQ,eAAe,GAAG;QAAU;MAAOH,OAA9D;EAEA,OAAO;IACLI,IAAI,EAAE,aADD;IAGLC,SAAS,EAAE,CAAC,qBAAD,CAHN;;IAKLC,WAAW,CAACC,IAAD,EAAOC,KAAP,EAAc;MACvB,IAAIC,aAAa,CAACF,IAAD,CAAjB,EAAyB;QACvBV,KAAK,CAAC,qBAAD,CAAL;QACAW,KAAK,CAACE,kBAAN,CAAyB,gCAAzB;;KARC;;IAWLC,SAAS,CAACJ,IAAD,EAAOC,KAAP,EAAcI,IAAd,EAAoB;MAC3B,IAAIH,aAAa,CAACF,IAAD,CAAjB,EAAyB;QACvB,IAAIM,QAAQ,GAAG,qBAAf;;QACA,IAAIV,eAAJ,EAAqB;UAAA;;UACnB,MAAMW,WAAW,WACbF,IAAI,CAACG,GAAN,CAAkBC,IAAnB,CAAuCC,GAAvC,CACE,0BADF,CADe,mBAGV,gBAHP;UAIAJ,QAAQ,GAAI,GAAEC,WAAY,cAA1B;;;QAGFF,IAAI,CAACM,WAAL,CACEV,KAAK,CAACW,mBAAN,CAA0BN,QAA1B,EAAoC,qBAApC,CADF;;;;GAtBN;AA4BD,CAxC4B,CAA7B;;AA0CA,MAAMJ,aAAa,GAAGF,IAAI,IACxBA,IAAI,CAACa,IAAL,KAAc,QAAd,IAA0Bb,IAAI,CAACH,IAAL,KAAc,oBAD1C;;AAGA,SAASH,YAAT,CAAsBoB,IAAtB,EAAiCC,IAAjC,EAA4C;EAC1C,OAAOC,IAAI,CAACC,SAAL,CAAeH,IAAf,MAAyBE,IAAI,CAACC,SAAL,CAAeF,IAAf,CAAhC;AACD;;;;"}