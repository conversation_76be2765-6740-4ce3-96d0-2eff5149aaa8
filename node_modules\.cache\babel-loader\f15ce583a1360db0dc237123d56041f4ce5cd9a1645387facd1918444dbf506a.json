{"ast": null, "code": "function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nfunction _iterableToArrayLimit(arr, i) { var _i = null == arr ? null : \"undefined\" != typeof Symbol && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (null != _i) { var _s, _e, _x, _r, _arr = [], _n = !0, _d = !1; try { if (_x = (_i = _i.call(arr)).next, 0 === i) { if (Object(_i) !== _i) return; _n = !1; } else for (; !(_n = (_s = _x.call(_i)).done) && (_arr.push(_s.value), _arr.length !== i); _n = !0); } catch (err) { _d = !0, _e = err; } finally { try { if (!_n && null != _i.return && (_r = _i.return(), Object(_r) !== _r)) return; } finally { if (_d) throw _e; } } return _arr; } }\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\nimport { withDirectives as _withDirectives, vShow as _vShow, createVNode as _createVNode } from \"vue\";\nimport { reactive, Teleport, defineComponent } from \"vue\";\nimport { truthProp, unknownProp, getZIndexStyle, createNamespace, makeArrayProp } from \"../utils/index.mjs\";\nimport { DROPDOWN_KEY } from \"../dropdown-menu/DropdownMenu.mjs\";\nimport { useParent } from \"@vant/use\";\nimport { useExpose } from \"../composables/use-expose.mjs\";\nimport { Cell } from \"../cell/index.mjs\";\nimport { Icon } from \"../icon/index.mjs\";\nimport { Popup } from \"../popup/index.mjs\";\nvar _createNamespace = createNamespace(\"dropdown-item\"),\n  _createNamespace2 = _slicedToArray(_createNamespace, 2),\n  name = _createNamespace2[0],\n  bem = _createNamespace2[1];\nvar dropdownItemProps = {\n  title: String,\n  options: makeArrayProp(),\n  disabled: Boolean,\n  teleport: [String, Object],\n  lazyRender: truthProp,\n  modelValue: unknownProp,\n  titleClass: unknownProp\n};\nvar stdin_default = defineComponent({\n  name: name,\n  props: dropdownItemProps,\n  emits: [\"open\", \"opened\", \"close\", \"closed\", \"change\", \"update:modelValue\"],\n  setup: function setup(props, _ref) {\n    var emit = _ref.emit,\n      slots = _ref.slots;\n    var state = reactive({\n      showPopup: false,\n      transition: true,\n      showWrapper: false\n    });\n    var _useParent = useParent(DROPDOWN_KEY),\n      parent = _useParent.parent,\n      index = _useParent.index;\n    if (!parent) {\n      if (process.env.NODE_ENV !== \"production\") {}\n      return;\n    }\n    var getEmitter = function getEmitter(name2) {\n      return function () {\n        return emit(name2);\n      };\n    };\n    var onOpen = getEmitter(\"open\");\n    var onClose = getEmitter(\"close\");\n    var onOpened = getEmitter(\"opened\");\n    var onClosed = function onClosed() {\n      state.showWrapper = false;\n      emit(\"closed\");\n    };\n    var onClickWrapper = function onClickWrapper(event) {\n      if (props.teleport) {\n        event.stopPropagation();\n      }\n    };\n    var toggle = function toggle() {\n      var show = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : !state.showPopup;\n      var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n      if (show === state.showPopup) {\n        return;\n      }\n      state.showPopup = show;\n      state.transition = !options.immediate;\n      if (show) {\n        state.showWrapper = true;\n      }\n    };\n    var renderTitle = function renderTitle() {\n      if (slots.title) {\n        return slots.title();\n      }\n      if (props.title) {\n        return props.title;\n      }\n      var match = props.options.find(function (option) {\n        return option.value === props.modelValue;\n      });\n      return match ? match.text : \"\";\n    };\n    var renderOption = function renderOption(option) {\n      var activeColor = parent.props.activeColor;\n      var active = option.value === props.modelValue;\n      var onClick = function onClick() {\n        state.showPopup = false;\n        if (option.value !== props.modelValue) {\n          emit(\"update:modelValue\", option.value);\n          emit(\"change\", option.value);\n        }\n      };\n      var renderIcon = function renderIcon() {\n        if (active) {\n          return _createVNode(Icon, {\n            \"class\": bem(\"icon\"),\n            \"color\": activeColor,\n            \"name\": \"success\"\n          }, null);\n        }\n      };\n      return _createVNode(Cell, {\n        \"role\": \"menuitem\",\n        \"key\": option.value,\n        \"icon\": option.icon,\n        \"title\": option.text,\n        \"class\": bem(\"option\", {\n          active: active\n        }),\n        \"style\": {\n          color: active ? activeColor : \"\"\n        },\n        \"tabindex\": active ? 0 : -1,\n        \"clickable\": true,\n        \"onClick\": onClick\n      }, {\n        value: renderIcon\n      });\n    };\n    var renderContent = function renderContent() {\n      var offset = parent.offset;\n      var _parent$props = parent.props,\n        zIndex = _parent$props.zIndex,\n        overlay = _parent$props.overlay,\n        duration = _parent$props.duration,\n        direction = _parent$props.direction,\n        closeOnClickOverlay = _parent$props.closeOnClickOverlay;\n      var style = getZIndexStyle(zIndex);\n      if (direction === \"down\") {\n        style.top = \"\".concat(offset.value, \"px\");\n      } else {\n        style.bottom = \"\".concat(offset.value, \"px\");\n      }\n      return _withDirectives(_createVNode(\"div\", {\n        \"style\": style,\n        \"class\": bem([direction]),\n        \"onClick\": onClickWrapper\n      }, [_createVNode(Popup, {\n        \"show\": state.showPopup,\n        \"onUpdate:show\": function onUpdateShow($event) {\n          return state.showPopup = $event;\n        },\n        \"role\": \"menu\",\n        \"class\": bem(\"content\"),\n        \"overlay\": overlay,\n        \"position\": direction === \"down\" ? \"top\" : \"bottom\",\n        \"duration\": state.transition ? duration : 0,\n        \"lazyRender\": props.lazyRender,\n        \"overlayStyle\": {\n          position: \"absolute\"\n        },\n        \"aria-labelledby\": \"\".concat(parent.id, \"-\").concat(index.value),\n        \"closeOnClickOverlay\": closeOnClickOverlay,\n        \"onOpen\": onOpen,\n        \"onClose\": onClose,\n        \"onOpened\": onOpened,\n        \"onClosed\": onClosed\n      }, {\n        default: function _default() {\n          var _a;\n          return [props.options.map(renderOption), (_a = slots.default) == null ? void 0 : _a.call(slots)];\n        }\n      })]), [[_vShow, state.showWrapper]]);\n    };\n    useExpose({\n      state: state,\n      toggle: toggle,\n      renderTitle: renderTitle\n    });\n    return function () {\n      if (props.teleport) {\n        return _createVNode(Teleport, {\n          \"to\": props.teleport\n        }, {\n          default: function _default() {\n            return [renderContent()];\n          }\n        });\n      }\n      return renderContent();\n    };\n  }\n});\nexport { stdin_default as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}