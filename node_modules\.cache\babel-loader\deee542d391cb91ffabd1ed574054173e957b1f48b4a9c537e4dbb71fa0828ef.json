{"ast": null, "code": "var stdin_default = {\n  name: \"\\u59D3\\u540D\",\n  tel: \"\\u7535\\u8BDD\",\n  save: \"\\u4FDD\\u5B58\",\n  confirm: \"\\u786E\\u8BA4\",\n  cancel: \"\\u53D6\\u6D88\",\n  delete: \"\\u5220\\u9664\",\n  loading: \"\\u52A0\\u8F7D\\u4E2D...\",\n  noCoupon: \"\\u6682\\u65E0\\u4F18\\u60E0\\u5238\",\n  nameEmpty: \"\\u8BF7\\u586B\\u5199\\u59D3\\u540D\",\n  addContact: \"\\u6DFB\\u52A0\\u8054\\u7CFB\\u4EBA\",\n  telInvalid: \"\\u8BF7\\u586B\\u5199\\u6B63\\u786E\\u7684\\u7535\\u8BDD\",\n  vanCalendar: {\n    end: \"\\u7ED3\\u675F\",\n    start: \"\\u5F00\\u59CB\",\n    title: \"\\u65E5\\u671F\\u9009\\u62E9\",\n    weekdays: [\"\\u65E5\", \"\\u4E00\", \"\\u4E8C\", \"\\u4E09\", \"\\u56DB\", \"\\u4E94\", \"\\u516D\"],\n    monthTitle: function monthTitle(year, month) {\n      return \"\".concat(year, \"\\u5E74\").concat(month, \"\\u6708\");\n    },\n    rangePrompt: function rangePrompt(maxRange) {\n      return \"\\u6700\\u591A\\u9009\\u62E9 \".concat(maxRange, \" \\u5929\");\n    }\n  },\n  vanCascader: {\n    select: \"\\u8BF7\\u9009\\u62E9\"\n  },\n  vanPagination: {\n    prev: \"\\u4E0A\\u4E00\\u9875\",\n    next: \"\\u4E0B\\u4E00\\u9875\"\n  },\n  vanPullRefresh: {\n    pulling: \"\\u4E0B\\u62C9\\u5373\\u53EF\\u5237\\u65B0...\",\n    loosing: \"\\u91CA\\u653E\\u5373\\u53EF\\u5237\\u65B0...\"\n  },\n  vanSubmitBar: {\n    label: \"\\u5408\\u8BA1:\"\n  },\n  vanCoupon: {\n    unlimited: \"\\u65E0\\u95E8\\u69DB\",\n    discount: function discount(_discount) {\n      return \"\".concat(_discount, \"\\u6298\");\n    },\n    condition: function condition(_condition) {\n      return \"\\u6EE1\".concat(_condition, \"\\u5143\\u53EF\\u7528\");\n    }\n  },\n  vanCouponCell: {\n    title: \"\\u4F18\\u60E0\\u5238\",\n    count: function count(_count) {\n      return \"\".concat(_count, \"\\u5F20\\u53EF\\u7528\");\n    }\n  },\n  vanCouponList: {\n    exchange: \"\\u5151\\u6362\",\n    close: \"\\u4E0D\\u4F7F\\u7528\",\n    enable: \"\\u53EF\\u7528\",\n    disabled: \"\\u4E0D\\u53EF\\u7528\",\n    placeholder: \"\\u8F93\\u5165\\u4F18\\u60E0\\u7801\"\n  },\n  vanAddressEdit: {\n    area: \"\\u5730\\u533A\",\n    postal: \"\\u90AE\\u653F\\u7F16\\u7801\",\n    areaEmpty: \"\\u8BF7\\u9009\\u62E9\\u5730\\u533A\",\n    addressEmpty: \"\\u8BF7\\u586B\\u5199\\u8BE6\\u7EC6\\u5730\\u5740\",\n    postalEmpty: \"\\u90AE\\u653F\\u7F16\\u7801\\u4E0D\\u6B63\\u786E\",\n    addressDetail: \"\\u8BE6\\u7EC6\\u5730\\u5740\",\n    defaultAddress: \"\\u8BBE\\u4E3A\\u9ED8\\u8BA4\\u6536\\u8D27\\u5730\\u5740\"\n  },\n  vanAddressList: {\n    add: \"\\u65B0\\u589E\\u5730\\u5740\"\n  }\n};\nexport { stdin_default as default };", "map": {"version": 3, "names": ["stdin_default", "name", "tel", "save", "confirm", "cancel", "delete", "loading", "noCoupon", "nameEmpty", "addContact", "telInvalid", "vanCalendar", "end", "start", "title", "weekdays", "monthTitle", "year", "month", "concat", "rangePrompt", "max<PERSON><PERSON><PERSON>", "vanCascader", "select", "vanPagination", "prev", "next", "vanPullRefresh", "pulling", "loosing", "vanSubmitBar", "label", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "unlimited", "discount", "condition", "vanCouponCell", "count", "vanCouponList", "exchange", "close", "enable", "disabled", "placeholder", "vanAddressEdit", "area", "postal", "areaEmpty", "addressEmpty", "postalEmpty", "addressDetail", "defaultAddress", "vanAddressList", "add", "default"], "sources": ["C:/Users/<USER>/Desktop/vue3_3.0/node_modules/vant/es/locale/lang/zh-CN.mjs"], "sourcesContent": ["var stdin_default = {\n  name: \"\\u59D3\\u540D\",\n  tel: \"\\u7535\\u8BDD\",\n  save: \"\\u4FDD\\u5B58\",\n  confirm: \"\\u786E\\u8BA4\",\n  cancel: \"\\u53D6\\u6D88\",\n  delete: \"\\u5220\\u9664\",\n  loading: \"\\u52A0\\u8F7D\\u4E2D...\",\n  noCoupon: \"\\u6682\\u65E0\\u4F18\\u60E0\\u5238\",\n  nameEmpty: \"\\u8BF7\\u586B\\u5199\\u59D3\\u540D\",\n  addContact: \"\\u6DFB\\u52A0\\u8054\\u7CFB\\u4EBA\",\n  telInvalid: \"\\u8BF7\\u586B\\u5199\\u6B63\\u786E\\u7684\\u7535\\u8BDD\",\n  vanCalendar: {\n    end: \"\\u7ED3\\u675F\",\n    start: \"\\u5F00\\u59CB\",\n    title: \"\\u65E5\\u671F\\u9009\\u62E9\",\n    weekdays: [\"\\u65E5\", \"\\u4E00\", \"\\u4E8C\", \"\\u4E09\", \"\\u56DB\", \"\\u4E94\", \"\\u516D\"],\n    monthTitle: (year, month) => `${year}\\u5E74${month}\\u6708`,\n    rangePrompt: (maxRange) => `\\u6700\\u591A\\u9009\\u62E9 ${maxRange} \\u5929`\n  },\n  vanCascader: {\n    select: \"\\u8BF7\\u9009\\u62E9\"\n  },\n  vanPagination: {\n    prev: \"\\u4E0A\\u4E00\\u9875\",\n    next: \"\\u4E0B\\u4E00\\u9875\"\n  },\n  vanPullRefresh: {\n    pulling: \"\\u4E0B\\u62C9\\u5373\\u53EF\\u5237\\u65B0...\",\n    loosing: \"\\u91CA\\u653E\\u5373\\u53EF\\u5237\\u65B0...\"\n  },\n  vanSubmitBar: {\n    label: \"\\u5408\\u8BA1:\"\n  },\n  vanCoupon: {\n    unlimited: \"\\u65E0\\u95E8\\u69DB\",\n    discount: (discount) => `${discount}\\u6298`,\n    condition: (condition) => `\\u6EE1${condition}\\u5143\\u53EF\\u7528`\n  },\n  vanCouponCell: {\n    title: \"\\u4F18\\u60E0\\u5238\",\n    count: (count) => `${count}\\u5F20\\u53EF\\u7528`\n  },\n  vanCouponList: {\n    exchange: \"\\u5151\\u6362\",\n    close: \"\\u4E0D\\u4F7F\\u7528\",\n    enable: \"\\u53EF\\u7528\",\n    disabled: \"\\u4E0D\\u53EF\\u7528\",\n    placeholder: \"\\u8F93\\u5165\\u4F18\\u60E0\\u7801\"\n  },\n  vanAddressEdit: {\n    area: \"\\u5730\\u533A\",\n    postal: \"\\u90AE\\u653F\\u7F16\\u7801\",\n    areaEmpty: \"\\u8BF7\\u9009\\u62E9\\u5730\\u533A\",\n    addressEmpty: \"\\u8BF7\\u586B\\u5199\\u8BE6\\u7EC6\\u5730\\u5740\",\n    postalEmpty: \"\\u90AE\\u653F\\u7F16\\u7801\\u4E0D\\u6B63\\u786E\",\n    addressDetail: \"\\u8BE6\\u7EC6\\u5730\\u5740\",\n    defaultAddress: \"\\u8BBE\\u4E3A\\u9ED8\\u8BA4\\u6536\\u8D27\\u5730\\u5740\"\n  },\n  vanAddressList: {\n    add: \"\\u65B0\\u589E\\u5730\\u5740\"\n  }\n};\nexport {\n  stdin_default as default\n};\n"], "mappings": "AAAA,IAAIA,aAAa,GAAG;EAClBC,IAAI,EAAE,cAAc;EACpBC,GAAG,EAAE,cAAc;EACnBC,IAAI,EAAE,cAAc;EACpBC,OAAO,EAAE,cAAc;EACvBC,MAAM,EAAE,cAAc;EACtBC,MAAM,EAAE,cAAc;EACtBC,OAAO,EAAE,uBAAuB;EAChCC,QAAQ,EAAE,gCAAgC;EAC1CC,SAAS,EAAE,gCAAgC;EAC3CC,UAAU,EAAE,gCAAgC;EAC5CC,UAAU,EAAE,kDAAkD;EAC9DC,WAAW,EAAE;IACXC,GAAG,EAAE,cAAc;IACnBC,KAAK,EAAE,cAAc;IACrBC,KAAK,EAAE,0BAA0B;IACjCC,QAAQ,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;IAChFC,UAAU,EAAE,SAAAA,WAACC,IAAI,EAAEC,KAAK;MAAA,UAAAC,MAAA,CAAQF,IAAI,YAAAE,MAAA,CAASD,KAAK;IAAA,CAAQ;IAC1DE,WAAW,EAAE,SAAAA,YAACC,QAAQ;MAAA,mCAAAF,MAAA,CAAiCE,QAAQ;IAAA;EACjE,CAAC;EACDC,WAAW,EAAE;IACXC,MAAM,EAAE;EACV,CAAC;EACDC,aAAa,EAAE;IACbC,IAAI,EAAE,oBAAoB;IAC1BC,IAAI,EAAE;EACR,CAAC;EACDC,cAAc,EAAE;IACdC,OAAO,EAAE,yCAAyC;IAClDC,OAAO,EAAE;EACX,CAAC;EACDC,YAAY,EAAE;IACZC,KAAK,EAAE;EACT,CAAC;EACDC,SAAS,EAAE;IACTC,SAAS,EAAE,oBAAoB;IAC/BC,QAAQ,EAAE,SAAAA,SAACA,SAAQ;MAAA,UAAAf,MAAA,CAAQe,SAAQ;IAAA,CAAQ;IAC3CC,SAAS,EAAE,SAAAA,UAACA,UAAS;MAAA,gBAAAhB,MAAA,CAAcgB,UAAS;IAAA;EAC9C,CAAC;EACDC,aAAa,EAAE;IACbtB,KAAK,EAAE,oBAAoB;IAC3BuB,KAAK,EAAE,SAAAA,MAACA,MAAK;MAAA,UAAAlB,MAAA,CAAQkB,MAAK;IAAA;EAC5B,CAAC;EACDC,aAAa,EAAE;IACbC,QAAQ,EAAE,cAAc;IACxBC,KAAK,EAAE,oBAAoB;IAC3BC,MAAM,EAAE,cAAc;IACtBC,QAAQ,EAAE,oBAAoB;IAC9BC,WAAW,EAAE;EACf,CAAC;EACDC,cAAc,EAAE;IACdC,IAAI,EAAE,cAAc;IACpBC,MAAM,EAAE,0BAA0B;IAClCC,SAAS,EAAE,gCAAgC;IAC3CC,YAAY,EAAE,4CAA4C;IAC1DC,WAAW,EAAE,4CAA4C;IACzDC,aAAa,EAAE,0BAA0B;IACzCC,cAAc,EAAE;EAClB,CAAC;EACDC,cAAc,EAAE;IACdC,GAAG,EAAE;EACP;AACF,CAAC;AACD,SACEtD,aAAa,IAAIuD,OAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}