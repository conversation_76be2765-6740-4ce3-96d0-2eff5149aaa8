{"ast": null, "code": "'use strict';\n\n/* eslint no-invalid-this: 1 */\nvar ERROR_MESSAGE = 'Function.prototype.bind called on incompatible ';\nvar slice = Array.prototype.slice;\nvar toStr = Object.prototype.toString;\nvar funcType = '[object Function]';\nmodule.exports = function bind(that) {\n  var target = this;\n  if (typeof target !== 'function' || toStr.call(target) !== funcType) {\n    throw new TypeError(ERROR_MESSAGE + target);\n  }\n  var args = slice.call(arguments, 1);\n  var bound;\n  var binder = function binder() {\n    if (this instanceof bound) {\n      var result = target.apply(this, args.concat(slice.call(arguments)));\n      if (Object(result) === result) {\n        return result;\n      }\n      return this;\n    } else {\n      return target.apply(that, args.concat(slice.call(arguments)));\n    }\n  };\n  var boundLength = Math.max(0, target.length - args.length);\n  var boundArgs = [];\n  for (var i = 0; i < boundLength; i++) {\n    boundArgs.push('$' + i);\n  }\n  bound = Function('binder', 'return function (' + boundArgs.join(',') + '){ return binder.apply(this,arguments); }')(binder);\n  if (target.prototype) {\n    var Empty = function Empty() {};\n    Empty.prototype = target.prototype;\n    bound.prototype = new Empty();\n    Empty.prototype = null;\n  }\n  return bound;\n};", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}