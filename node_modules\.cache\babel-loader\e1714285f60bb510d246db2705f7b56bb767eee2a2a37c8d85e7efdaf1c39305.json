{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _NavBar from \"./NavBar.mjs\";\nvar NavBar = withInstall(_NavBar);\nvar stdin_default = NavBar;\nexport { NavBar, stdin_default as default };", "map": {"version": 3, "names": ["withInstall", "_NavBar", "NavBar", "stdin_default", "default"], "sources": ["C:/Users/<USER>/Downloads/Telegram Desktop/vue3_3.0/vue3_3.0/node_modules/vant/es/nav-bar/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _NavBar from \"./NavBar.mjs\";\nconst NavBar = withInstall(_NavBar);\nvar stdin_default = NavBar;\nexport {\n  NavBar,\n  stdin_default as default\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,OAAO,MAAM,cAAc;AAClC,IAAMC,MAAM,GAAGF,WAAW,CAACC,OAAO,CAAC;AACnC,IAAIE,aAAa,GAAGD,MAAM;AAC1B,SACEA,MAAM,EACNC,aAAa,IAAIC,OAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}