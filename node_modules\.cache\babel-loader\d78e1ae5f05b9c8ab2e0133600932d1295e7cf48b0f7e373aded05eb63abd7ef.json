{"ast": null, "code": "function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nfunction _iterableToArrayLimit(arr, i) { var _i = null == arr ? null : \"undefined\" != typeof Symbol && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (null != _i) { var _s, _e, _x, _r, _arr = [], _n = !0, _d = !1; try { if (_x = (_i = _i.call(arr)).next, 0 === i) { if (Object(_i) !== _i) return; _n = !1; } else for (; !(_n = (_s = _x.call(_i)).done) && (_arr.push(_s.value), _arr.length !== i); _n = !0); } catch (err) { _d = !0, _e = err; } finally { try { if (!_n && null != _i.return && (_r = _i.return(), Object(_r) !== _r)) return; } finally { if (_d) throw _e; } } return _arr; } }\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\nimport { createVNode as _createVNode } from \"vue\";\nimport { ref, defineComponent } from \"vue\";\nimport { truthProp, makeStringProp, makeNumericProp, createNamespace } from \"../utils/index.mjs\";\nimport { Icon } from \"../icon/index.mjs\";\nimport { Button } from \"../button/index.mjs\";\nimport { usePlaceholder } from \"../composables/use-placeholder.mjs\";\nvar _createNamespace = createNamespace(\"submit-bar\"),\n  _createNamespace2 = _slicedToArray(_createNamespace, 3),\n  name = _createNamespace2[0],\n  bem = _createNamespace2[1],\n  t = _createNamespace2[2];\nvar submitBarProps = {\n  tip: String,\n  label: String,\n  price: Number,\n  tipIcon: String,\n  loading: Boolean,\n  currency: makeStringProp(\"\\xA5\"),\n  disabled: Boolean,\n  textAlign: String,\n  buttonText: String,\n  buttonType: makeStringProp(\"danger\"),\n  buttonColor: String,\n  suffixLabel: String,\n  placeholder: Boolean,\n  decimalLength: makeNumericProp(2),\n  safeAreaInsetBottom: truthProp\n};\nvar stdin_default = defineComponent({\n  name: name,\n  props: submitBarProps,\n  emits: [\"submit\"],\n  setup: function setup(props, _ref) {\n    var emit = _ref.emit,\n      slots = _ref.slots;\n    var root = ref();\n    var renderPlaceholder = usePlaceholder(root, bem);\n    var renderText = function renderText() {\n      var price = props.price,\n        label = props.label,\n        currency = props.currency,\n        textAlign = props.textAlign,\n        suffixLabel = props.suffixLabel,\n        decimalLength = props.decimalLength;\n      if (typeof price === \"number\") {\n        var pricePair = (price / 100).toFixed(+decimalLength).split(\".\");\n        var decimal = decimalLength ? \".\".concat(pricePair[1]) : \"\";\n        return _createVNode(\"div\", {\n          \"class\": bem(\"text\"),\n          \"style\": {\n            textAlign: textAlign\n          }\n        }, [_createVNode(\"span\", null, [label || t(\"label\")]), _createVNode(\"span\", {\n          \"class\": bem(\"price\")\n        }, [currency, _createVNode(\"span\", {\n          \"class\": bem(\"price-integer\")\n        }, [pricePair[0]]), decimal]), suffixLabel && _createVNode(\"span\", {\n          \"class\": bem(\"suffix-label\")\n        }, [suffixLabel])]);\n      }\n    };\n    var renderTip = function renderTip() {\n      var _a;\n      var tip = props.tip,\n        tipIcon = props.tipIcon;\n      if (slots.tip || tip) {\n        return _createVNode(\"div\", {\n          \"class\": bem(\"tip\")\n        }, [tipIcon && _createVNode(Icon, {\n          \"class\": bem(\"tip-icon\"),\n          \"name\": tipIcon\n        }, null), tip && _createVNode(\"span\", {\n          \"class\": bem(\"tip-text\")\n        }, [tip]), (_a = slots.tip) == null ? void 0 : _a.call(slots)]);\n      }\n    };\n    var onClickButton = function onClickButton() {\n      return emit(\"submit\");\n    };\n    var renderButton = function renderButton() {\n      if (slots.button) {\n        return slots.button();\n      }\n      return _createVNode(Button, {\n        \"round\": true,\n        \"type\": props.buttonType,\n        \"text\": props.buttonText,\n        \"class\": bem(\"button\", props.buttonType),\n        \"color\": props.buttonColor,\n        \"loading\": props.loading,\n        \"disabled\": props.disabled,\n        \"onClick\": onClickButton\n      }, null);\n    };\n    var renderSubmitBar = function renderSubmitBar() {\n      var _a, _b;\n      return _createVNode(\"div\", {\n        \"ref\": root,\n        \"class\": [bem(), {\n          \"van-safe-area-bottom\": props.safeAreaInsetBottom\n        }]\n      }, [(_a = slots.top) == null ? void 0 : _a.call(slots), renderTip(), _createVNode(\"div\", {\n        \"class\": bem(\"bar\")\n      }, [(_b = slots.default) == null ? void 0 : _b.call(slots), renderText(), renderButton()])]);\n    };\n    return function () {\n      if (props.placeholder) {\n        return renderPlaceholder(renderSubmitBar);\n      }\n      return renderSubmitBar();\n    };\n  }\n});\nexport { stdin_default as default };", "map": {"version": 3, "names": ["createVNode", "_createVNode", "ref", "defineComponent", "truthProp", "makeStringProp", "makeNumericProp", "createNamespace", "Icon", "<PERSON><PERSON>", "usePlaceholder", "_createNamespace", "_createNamespace2", "_slicedToArray", "name", "bem", "t", "submitBarProps", "tip", "String", "label", "price", "Number", "tipIcon", "loading", "Boolean", "currency", "disabled", "textAlign", "buttonText", "buttonType", "buttonColor", "suffix<PERSON>abel", "placeholder", "decimalLength", "safeAreaInsetBottom", "stdin_default", "props", "emits", "setup", "_ref", "emit", "slots", "root", "renderPlaceholder", "renderText", "pricePair", "toFixed", "split", "decimal", "concat", "renderTip", "_a", "call", "onClickButton", "renderButton", "button", "renderSubmitBar", "_b", "top", "default"], "sources": ["C:/Users/<USER>/Desktop/vue3_3.0/node_modules/vant/es/submit-bar/SubmitBar.mjs"], "sourcesContent": ["import { createVNode as _createVNode } from \"vue\";\nimport { ref, defineComponent } from \"vue\";\nimport { truthProp, makeStringProp, makeNumericProp, createNamespace } from \"../utils/index.mjs\";\nimport { Icon } from \"../icon/index.mjs\";\nimport { Button } from \"../button/index.mjs\";\nimport { usePlaceholder } from \"../composables/use-placeholder.mjs\";\nconst [name, bem, t] = createNamespace(\"submit-bar\");\nconst submitBarProps = {\n  tip: String,\n  label: String,\n  price: Number,\n  tipIcon: String,\n  loading: Boolean,\n  currency: makeStringProp(\"\\xA5\"),\n  disabled: Boolean,\n  textAlign: String,\n  buttonText: String,\n  buttonType: makeStringProp(\"danger\"),\n  buttonColor: String,\n  suffixLabel: String,\n  placeholder: Boolean,\n  decimalLength: makeNumericProp(2),\n  safeAreaInsetBottom: truthProp\n};\nvar stdin_default = defineComponent({\n  name,\n  props: submitBarProps,\n  emits: [\"submit\"],\n  setup(props, {\n    emit,\n    slots\n  }) {\n    const root = ref();\n    const renderPlaceholder = usePlaceholder(root, bem);\n    const renderText = () => {\n      const {\n        price,\n        label,\n        currency,\n        textAlign,\n        suffixLabel,\n        decimalLength\n      } = props;\n      if (typeof price === \"number\") {\n        const pricePair = (price / 100).toFixed(+decimalLength).split(\".\");\n        const decimal = decimalLength ? `.${pricePair[1]}` : \"\";\n        return _createVNode(\"div\", {\n          \"class\": bem(\"text\"),\n          \"style\": {\n            textAlign\n          }\n        }, [_createVNode(\"span\", null, [label || t(\"label\")]), _createVNode(\"span\", {\n          \"class\": bem(\"price\")\n        }, [currency, _createVNode(\"span\", {\n          \"class\": bem(\"price-integer\")\n        }, [pricePair[0]]), decimal]), suffixLabel && _createVNode(\"span\", {\n          \"class\": bem(\"suffix-label\")\n        }, [suffixLabel])]);\n      }\n    };\n    const renderTip = () => {\n      var _a;\n      const {\n        tip,\n        tipIcon\n      } = props;\n      if (slots.tip || tip) {\n        return _createVNode(\"div\", {\n          \"class\": bem(\"tip\")\n        }, [tipIcon && _createVNode(Icon, {\n          \"class\": bem(\"tip-icon\"),\n          \"name\": tipIcon\n        }, null), tip && _createVNode(\"span\", {\n          \"class\": bem(\"tip-text\")\n        }, [tip]), (_a = slots.tip) == null ? void 0 : _a.call(slots)]);\n      }\n    };\n    const onClickButton = () => emit(\"submit\");\n    const renderButton = () => {\n      if (slots.button) {\n        return slots.button();\n      }\n      return _createVNode(Button, {\n        \"round\": true,\n        \"type\": props.buttonType,\n        \"text\": props.buttonText,\n        \"class\": bem(\"button\", props.buttonType),\n        \"color\": props.buttonColor,\n        \"loading\": props.loading,\n        \"disabled\": props.disabled,\n        \"onClick\": onClickButton\n      }, null);\n    };\n    const renderSubmitBar = () => {\n      var _a, _b;\n      return _createVNode(\"div\", {\n        \"ref\": root,\n        \"class\": [bem(), {\n          \"van-safe-area-bottom\": props.safeAreaInsetBottom\n        }]\n      }, [(_a = slots.top) == null ? void 0 : _a.call(slots), renderTip(), _createVNode(\"div\", {\n        \"class\": bem(\"bar\")\n      }, [(_b = slots.default) == null ? void 0 : _b.call(slots), renderText(), renderButton()])]);\n    };\n    return () => {\n      if (props.placeholder) {\n        return renderPlaceholder(renderSubmitBar);\n      }\n      return renderSubmitBar();\n    };\n  }\n});\nexport {\n  stdin_default as default\n};\n"], "mappings": ";;;;;;AAAA,SAASA,WAAW,IAAIC,YAAY,QAAQ,KAAK;AACjD,SAASC,GAAG,EAAEC,eAAe,QAAQ,KAAK;AAC1C,SAASC,SAAS,EAAEC,cAAc,EAAEC,eAAe,EAAEC,eAAe,QAAQ,oBAAoB;AAChG,SAASC,IAAI,QAAQ,mBAAmB;AACxC,SAASC,MAAM,QAAQ,qBAAqB;AAC5C,SAASC,cAAc,QAAQ,oCAAoC;AACnE,IAAAC,gBAAA,GAAuBJ,eAAe,CAAC,YAAY,CAAC;EAAAK,iBAAA,GAAAC,cAAA,CAAAF,gBAAA;EAA7CG,IAAI,GAAAF,iBAAA;EAAEG,GAAG,GAAAH,iBAAA;EAAEI,CAAC,GAAAJ,iBAAA;AACnB,IAAMK,cAAc,GAAG;EACrBC,GAAG,EAAEC,MAAM;EACXC,KAAK,EAAED,MAAM;EACbE,KAAK,EAAEC,MAAM;EACbC,OAAO,EAAEJ,MAAM;EACfK,OAAO,EAAEC,OAAO;EAChBC,QAAQ,EAAErB,cAAc,CAAC,MAAM,CAAC;EAChCsB,QAAQ,EAAEF,OAAO;EACjBG,SAAS,EAAET,MAAM;EACjBU,UAAU,EAAEV,MAAM;EAClBW,UAAU,EAAEzB,cAAc,CAAC,QAAQ,CAAC;EACpC0B,WAAW,EAAEZ,MAAM;EACnBa,WAAW,EAAEb,MAAM;EACnBc,WAAW,EAAER,OAAO;EACpBS,aAAa,EAAE5B,eAAe,CAAC,CAAC,CAAC;EACjC6B,mBAAmB,EAAE/B;AACvB,CAAC;AACD,IAAIgC,aAAa,GAAGjC,eAAe,CAAC;EAClCW,IAAI,EAAJA,IAAI;EACJuB,KAAK,EAAEpB,cAAc;EACrBqB,KAAK,EAAE,CAAC,QAAQ,CAAC;EACjBC,KAAK,WAAAA,MAACF,KAAK,EAAAG,IAAA,EAGR;IAAA,IAFDC,IAAI,GAAAD,IAAA,CAAJC,IAAI;MACJC,KAAK,GAAAF,IAAA,CAALE,KAAK;IAEL,IAAMC,IAAI,GAAGzC,GAAG,CAAC,CAAC;IAClB,IAAM0C,iBAAiB,GAAGlC,cAAc,CAACiC,IAAI,EAAE5B,GAAG,CAAC;IACnD,IAAM8B,UAAU,GAAG,SAAbA,UAAUA,CAAA,EAAS;MACvB,IACExB,KAAK,GAMHgB,KAAK,CANPhB,KAAK;QACLD,KAAK,GAKHiB,KAAK,CALPjB,KAAK;QACLM,QAAQ,GAINW,KAAK,CAJPX,QAAQ;QACRE,SAAS,GAGPS,KAAK,CAHPT,SAAS;QACTI,WAAW,GAETK,KAAK,CAFPL,WAAW;QACXE,aAAa,GACXG,KAAK,CADPH,aAAa;MAEf,IAAI,OAAOb,KAAK,KAAK,QAAQ,EAAE;QAC7B,IAAMyB,SAAS,GAAG,CAACzB,KAAK,GAAG,GAAG,EAAE0B,OAAO,CAAC,CAACb,aAAa,CAAC,CAACc,KAAK,CAAC,GAAG,CAAC;QAClE,IAAMC,OAAO,GAAGf,aAAa,OAAAgB,MAAA,CAAOJ,SAAS,CAAC,CAAC,CAAC,IAAK,EAAE;QACvD,OAAO7C,YAAY,CAAC,KAAK,EAAE;UACzB,OAAO,EAAEc,GAAG,CAAC,MAAM,CAAC;UACpB,OAAO,EAAE;YACPa,SAAS,EAATA;UACF;QACF,CAAC,EAAE,CAAC3B,YAAY,CAAC,MAAM,EAAE,IAAI,EAAE,CAACmB,KAAK,IAAIJ,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAEf,YAAY,CAAC,MAAM,EAAE;UAC1E,OAAO,EAAEc,GAAG,CAAC,OAAO;QACtB,CAAC,EAAE,CAACW,QAAQ,EAAEzB,YAAY,CAAC,MAAM,EAAE;UACjC,OAAO,EAAEc,GAAG,CAAC,eAAe;QAC9B,CAAC,EAAE,CAAC+B,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEG,OAAO,CAAC,CAAC,EAAEjB,WAAW,IAAI/B,YAAY,CAAC,MAAM,EAAE;UACjE,OAAO,EAAEc,GAAG,CAAC,cAAc;QAC7B,CAAC,EAAE,CAACiB,WAAW,CAAC,CAAC,CAAC,CAAC;MACrB;IACF,CAAC;IACD,IAAMmB,SAAS,GAAG,SAAZA,SAASA,CAAA,EAAS;MACtB,IAAIC,EAAE;MACN,IACElC,GAAG,GAEDmB,KAAK,CAFPnB,GAAG;QACHK,OAAO,GACLc,KAAK,CADPd,OAAO;MAET,IAAImB,KAAK,CAACxB,GAAG,IAAIA,GAAG,EAAE;QACpB,OAAOjB,YAAY,CAAC,KAAK,EAAE;UACzB,OAAO,EAAEc,GAAG,CAAC,KAAK;QACpB,CAAC,EAAE,CAACQ,OAAO,IAAItB,YAAY,CAACO,IAAI,EAAE;UAChC,OAAO,EAAEO,GAAG,CAAC,UAAU,CAAC;UACxB,MAAM,EAAEQ;QACV,CAAC,EAAE,IAAI,CAAC,EAAEL,GAAG,IAAIjB,YAAY,CAAC,MAAM,EAAE;UACpC,OAAO,EAAEc,GAAG,CAAC,UAAU;QACzB,CAAC,EAAE,CAACG,GAAG,CAAC,CAAC,EAAE,CAACkC,EAAE,GAAGV,KAAK,CAACxB,GAAG,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGkC,EAAE,CAACC,IAAI,CAACX,KAAK,CAAC,CAAC,CAAC;MACjE;IACF,CAAC;IACD,IAAMY,aAAa,GAAG,SAAhBA,aAAaA,CAAA;MAAA,OAASb,IAAI,CAAC,QAAQ,CAAC;IAAA;IAC1C,IAAMc,YAAY,GAAG,SAAfA,YAAYA,CAAA,EAAS;MACzB,IAAIb,KAAK,CAACc,MAAM,EAAE;QAChB,OAAOd,KAAK,CAACc,MAAM,CAAC,CAAC;MACvB;MACA,OAAOvD,YAAY,CAACQ,MAAM,EAAE;QAC1B,OAAO,EAAE,IAAI;QACb,MAAM,EAAE4B,KAAK,CAACP,UAAU;QACxB,MAAM,EAAEO,KAAK,CAACR,UAAU;QACxB,OAAO,EAAEd,GAAG,CAAC,QAAQ,EAAEsB,KAAK,CAACP,UAAU,CAAC;QACxC,OAAO,EAAEO,KAAK,CAACN,WAAW;QAC1B,SAAS,EAAEM,KAAK,CAACb,OAAO;QACxB,UAAU,EAAEa,KAAK,CAACV,QAAQ;QAC1B,SAAS,EAAE2B;MACb,CAAC,EAAE,IAAI,CAAC;IACV,CAAC;IACD,IAAMG,eAAe,GAAG,SAAlBA,eAAeA,CAAA,EAAS;MAC5B,IAAIL,EAAE,EAAEM,EAAE;MACV,OAAOzD,YAAY,CAAC,KAAK,EAAE;QACzB,KAAK,EAAE0C,IAAI;QACX,OAAO,EAAE,CAAC5B,GAAG,CAAC,CAAC,EAAE;UACf,sBAAsB,EAAEsB,KAAK,CAACF;QAChC,CAAC;MACH,CAAC,EAAE,CAAC,CAACiB,EAAE,GAAGV,KAAK,CAACiB,GAAG,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGP,EAAE,CAACC,IAAI,CAACX,KAAK,CAAC,EAAES,SAAS,CAAC,CAAC,EAAElD,YAAY,CAAC,KAAK,EAAE;QACvF,OAAO,EAAEc,GAAG,CAAC,KAAK;MACpB,CAAC,EAAE,CAAC,CAAC2C,EAAE,GAAGhB,KAAK,CAACkB,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGF,EAAE,CAACL,IAAI,CAACX,KAAK,CAAC,EAAEG,UAAU,CAAC,CAAC,EAAEU,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9F,CAAC;IACD,OAAO,YAAM;MACX,IAAIlB,KAAK,CAACJ,WAAW,EAAE;QACrB,OAAOW,iBAAiB,CAACa,eAAe,CAAC;MAC3C;MACA,OAAOA,eAAe,CAAC,CAAC;IAC1B,CAAC;EACH;AACF,CAAC,CAAC;AACF,SACErB,aAAa,IAAIwB,OAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}