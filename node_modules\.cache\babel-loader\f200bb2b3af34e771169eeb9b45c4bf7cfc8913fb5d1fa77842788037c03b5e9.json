{"ast": null, "code": "import { reactive, ref, getCurrentInstance } from 'vue';\nimport store from '@/store/index';\nimport { useRouter, useRoute } from 'vue-router';\nimport { getdetailbyid } from '@/api/home/<USER>';\nexport default {\n  name: 'HomeView',\n  setup: function setup() {\n    var _route$query, _route$query2, _route$query3;\n    var _useRouter = useRouter(),\n      push = _useRouter.push;\n    var route = useRoute();\n    var id = (_route$query = route.query) === null || _route$query === void 0 ? void 0 : _route$query.id;\n    var title = (_route$query2 = route.query) === null || _route$query2 === void 0 ? void 0 : _route$query2.title;\n    var content = ref('');\n    getdetailbyid((_route$query3 = route.query) === null || _route$query3 === void 0 ? void 0 : _route$query3.id).then(function (res) {\n      var _res$data;\n      content.value = (_res$data = res.data) === null || _res$data === void 0 ? void 0 : _res$data.content;\n    });\n    return {\n      id: id,\n      title: title,\n      content: content\n    };\n  }\n};", "map": {"version": 3, "names": ["reactive", "ref", "getCurrentInstance", "store", "useRouter", "useRoute", "getdetailbyid", "name", "setup", "_route$query", "_route$query2", "_route$query3", "_useRouter", "push", "route", "id", "query", "title", "content", "then", "res", "_res$data", "value", "data"], "sources": ["C:\\Users\\<USER>\\Downloads\\Telegram Desktop\\vue3_3.0\\vue3_3.0\\src\\views\\index\\components\\content.vue"], "sourcesContent": ["<template>\r\n    <div class=\"home\">\r\n        <van-nav-bar :title=\"title\"  left-arrow @click-left=\"$router.go(-1)\"></van-nav-bar>\r\n\r\n        <!-- <van-nav-bar\r\n\t\t     :title=\"$t('msg.jiaoyi')\"\r\n\t\t\t background=\"#ffffff\"\r\n\t\t\t title-style=\"color: black; font-size: 16px;\"\r\n\t\t   ></van-nav-bar> -->\r\n\r\n        <div class=\"content\" v-html=\"content\"></div>\r\n    </div>\r\n</template>\r\n<script>\r\n    import { reactive, ref,getCurrentInstance } from 'vue';\r\n    import store from '@/store/index'\r\n    import { useRouter,useRoute } from 'vue-router';\r\n    import {getdetailbyid} from '@/api/home/<USER>'\r\n    export default {\r\n        name: 'HomeView',\r\n        setup() {\r\n            const { push } = useRouter();\r\n            const route = useRoute();\r\n            const id = route.query?.id\r\n            const title = route.query?.title\r\n            const content = ref('')\r\n\r\n            getdetailbyid(route.query?.id).then(res => {\r\n                content.value = res.data?.content\r\n            })\r\n\r\n            return {id,title,content}\r\n        }\r\n    }\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n@import '@/styles/theme.scss';\r\n\r\n\r\n// .content{\r\n//     background: #f5f5f5;\r\n// }\r\n\r\n.home{\r\n    background: #f5f5f5;\r\n    overflow: hidden !important;\r\n    :deep(.van-nav-bar){\r\n        background-color: #fff;\r\n        color: #000;\r\n        .van-nav-bar__left{\r\n            .van-icon{\r\n                color: #000;\r\n            }\r\n        }\r\n        .van-nav-bar__title{\r\n            color: #000;\r\n        }\r\n        .van-nav-bar__right{\r\n            img{\r\n                height: 42px;\r\n            }\r\n        }\r\n    }\r\n    .content{\r\n        margin: 40px 30px;\r\n        box-shadow: $shadow;\r\n        text-align: left;\r\n        padding: 40px 30px;\r\n        font-size: 30px;\r\n        color: #333;\r\n        line-height: 1.8;\r\n        overflow: auto;\r\n        border-radius: 12px;\r\n        background-color: #fff;\r\n        flex: 1;\r\n    }\r\n}\r\n\r\nimg{\r\n    width: 100% !important;\r\n}\r\n\r\n</style>"], "mappings": "AAcI,SAASA,QAAQ,EAAEC,GAAG,EAACC,kBAAiB,QAAS,KAAK;AACtD,OAAOC,KAAI,MAAO,eAAc;AAChC,SAASC,SAAS,EAACC,QAAO,QAAS,YAAY;AAC/C,SAAQC,aAAa,QAAO,kBAAiB;AAC7C,eAAe;EACXC,IAAI,EAAE,UAAU;EAChBC,KAAK,WAAAA,MAAA,EAAG;IAAA,IAAAC,YAAA,EAAAC,aAAA,EAAAC,aAAA;IACJ,IAAAC,UAAA,GAAiBR,SAAS,CAAC,CAAC;MAApBS,IAAG,GAAAD,UAAA,CAAHC,IAAG;IACX,IAAMC,KAAI,GAAIT,QAAQ,CAAC,CAAC;IACxB,IAAMU,EAAC,IAAAN,YAAA,GAAIK,KAAK,CAACE,KAAK,cAAAP,YAAA,uBAAXA,YAAA,CAAaM,EAAC;IACzB,IAAME,KAAI,IAAAP,aAAA,GAAII,KAAK,CAACE,KAAK,cAAAN,aAAA,uBAAXA,aAAA,CAAaO,KAAI;IAC/B,IAAMC,OAAM,GAAIjB,GAAG,CAAC,EAAE;IAEtBK,aAAa,EAAAK,aAAA,GAACG,KAAK,CAACE,KAAK,cAAAL,aAAA,uBAAXA,aAAA,CAAaI,EAAE,CAAC,CAACI,IAAI,CAAC,UAAAC,GAAE,EAAK;MAAA,IAAAC,SAAA;MACvCH,OAAO,CAACI,KAAI,IAAAD,SAAA,GAAID,GAAG,CAACG,IAAI,cAAAF,SAAA,uBAARA,SAAA,CAAUH,OAAM;IACpC,CAAC;IAED,OAAO;MAACH,EAAE,EAAFA,EAAE;MAACE,KAAK,EAALA,KAAK;MAACC,OAAO,EAAPA;IAAO;EAC5B;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}