{"ast": null, "code": "import { createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, pushScopeId as _pushScopeId, popScopeId as _popScopeId } from \"vue\";\nvar _withScopeId = function _withScopeId(n) {\n  return _pushScopeId(\"data-v-a49090ce\"), n = n(), _popScopeId(), n;\n};\nvar _hoisted_1 = {\n  class: \"homes\"\n};\nvar _hoisted_2 = /*#__PURE__*/_withScopeId(function () {\n  return /*#__PURE__*/_createElementVNode(\"div\", {\n    style: {\n      \"height\": \"1.5rem\"\n    }\n  }, null, -1 /* HOISTED */);\n});\nvar _hoisted_3 = {\n  class: \"buttons\"\n};\nvar _hoisted_4 = {\n  class: \"gore\"\n};\nvar _hoisted_5 = /*#__PURE__*/_withScopeId(function () {\n  return /*#__PURE__*/_createElementVNode(\"div\", {\n    style: {\n      \"height\": \"1rem\"\n    }\n  }, null, -1 /* HOISTED */);\n});\n\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_login_top = _resolveComponent(\"login-top\");\n  var _component_van_field = _resolveComponent(\"van-field\");\n  var _component_van_cell_group = _resolveComponent(\"van-cell-group\");\n  var _component_van_checkbox = _resolveComponent(\"van-checkbox\");\n  var _component_van_button = _resolveComponent(\"van-button\");\n  var _component_van_form = _resolveComponent(\"van-form\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_hoisted_2, _createVNode(_component_login_top), _createVNode(_component_van_form, {\n    onSubmit: $setup.onSubmit,\n    style: {\n      \"margin-top\": \"1.5rem\"\n    }\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_van_cell_group, {\n        inset: \"\",\n        style: {\n          \"width\": \"84%\",\n          \"margin\": \"0 7%\"\n        }\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_van_field, {\n            class: \"zdy\",\n            name: \"userName\",\n            id: \"userName\",\n            \"label-position\": \"right\",\n            modelValue: $setup.userName,\n            \"onUpdate:modelValue\": _cache[0] || (_cache[0] = function ($event) {\n              return $setup.userName = $event;\n            }),\n            placeholder: _ctx.$t('msg.input_username'),\n            rules: [{\n              required: true,\n              message: _ctx.$t('msg.input_username')\n            }]\n          }, null, 8 /* PROPS */, [\"modelValue\", \"placeholder\", \"rules\"]), _createVNode(_component_van_field, {\n            modelValue: $setup.pwd,\n            \"onUpdate:modelValue\": _cache[1] || (_cache[1] = function ($event) {\n              return $setup.pwd = $event;\n            }),\n            type: \"password\",\n            name: \"pwd\",\n            placeholder: _ctx.$t('msg.input_pwd'),\n            rules: [{\n              required: true,\n              message: _ctx.$t('msg.input_pwd')\n            }]\n          }, null, 8 /* PROPS */, [\"modelValue\", \"placeholder\", \"rules\"])];\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_van_checkbox, {\n        shape: \"square\",\n        modelValue: $setup.checked,\n        \"onUpdate:modelValue\": _cache[2] || (_cache[2] = function ($event) {\n          return $setup.checked = $event;\n        })\n      }, {\n        default: _withCtx(function () {\n          return [_createTextVNode(_toDisplayString(_ctx.$t('msg.j_pwd')), 1 /* TEXT */)];\n        }),\n\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"modelValue\"]), _createElementVNode(\"div\", _hoisted_3, [_createVNode(_component_van_button, {\n        round: \"5\",\n        block: \"\",\n        plain: \"\",\n        type: \"primary\",\n        \"native-type\": \"submit\"\n      }, {\n        default: _withCtx(function () {\n          return [_createTextVNode(_toDisplayString(_ctx.$t('msg.login')), 1 /* TEXT */)];\n        }),\n\n        _: 1 /* STABLE */\n      }), _createElementVNode(\"div\", _hoisted_4, [_createElementVNode(\"span\", {\n        class: \"gores\",\n        onClick: _cache[3] || (_cache[3] = function ($event) {\n          return _ctx.$router.push({\n            path: '/register'\n          });\n        })\n      }, _toDisplayString(_ctx.$t('msg.register')), 1 /* TEXT */)]), _hoisted_5])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"onSubmit\"])]);\n}", "map": {"version": 3, "names": ["class", "_createElementVNode", "style", "_createElementBlock", "_hoisted_1", "_hoisted_2", "_createVNode", "_component_login_top", "_component_van_form", "onSubmit", "$setup", "_component_van_cell_group", "inset", "_component_van_field", "name", "id", "userName", "$event", "placeholder", "_ctx", "$t", "rules", "required", "message", "pwd", "type", "_component_van_checkbox", "shape", "checked", "_hoisted_3", "_component_van_button", "round", "block", "plain", "_hoisted_4", "onClick", "_cache", "$router", "push", "path", "_hoisted_5"], "sources": ["C:\\Users\\<USER>\\Downloads\\Telegram Desktop\\vue3_3.0\\vue3_3.0\\src\\views\\login\\login.vue"], "sourcesContent": ["<template>\r\n  <div class=\"homes\" >\r\n\r\n   <div style=\"height: 1.5rem;\">\r\n\r\n   </div>\r\n    <login-top></login-top>\r\n    <van-form @submit=\"onSubmit\" style=\"margin-top: 1.5rem;\">\r\n      <van-cell-group inset style=\"width: 84%;margin: 0 7%;\">\r\n\r\n        <van-field class=\"zdy\"  name=\"userName\" id=\"userName\"  label-position=\"right\"   v-model=\"userName\" :placeholder=\"$t('msg.input_username')\"\r\n          :rules=\"[{ required: true, message: $t('msg.input_username') }]\">\r\n\r\n        </van-field>\r\n        <van-field   v-model=\"pwd\" type=\"password\" name=\"pwd\" :placeholder=\"$t('msg.input_pwd')\"\r\n          :rules=\"[{ required: true, message: $t('msg.input_pwd') }]\" />\r\n\r\n      </van-cell-group>\r\n      <van-checkbox shape=\"square\" v-model=\"checked\">{{ $t('msg.j_pwd') }}</van-checkbox>\r\n\r\n\r\n      <div class=\"buttons\">\r\n        <van-button round=\"5\" block plain type=\"primary\" native-type=\"submit\">\r\n          {{ $t('msg.login') }}\r\n        </van-button>\r\n        <div class=\"gore\">\r\n          <span class=\"gores\" @click=\"$router.push({ path: '/register' })\">{{ $t('msg.register') }}</span>\r\n        </div>\r\n\r\n        <div style=\"height: 1rem;\"></div>\r\n      </div>\r\n    </van-form>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport loginTop from './index.vue'\r\nimport { ref, getCurrentInstance } from 'vue';\r\nimport store from '@/store/index'\r\nimport { login } from '@/api/login/index.js'\r\nimport { useRouter } from 'vue-router';\r\nexport default {\r\n  name: 'HomeView',\r\n  components: { loginTop },\r\n  setup() {\r\n\r\n    const { push } = useRouter();\r\n    const { proxy } = getCurrentInstance()\r\n    const userjs = ref(store.state.user)\r\n    \r\n    const checked = ref(userjs.value?.checked || false);\r\n    const userName = ref(userjs.value?.userName || '');\r\n    const pwd = ref(userjs.value?.pwd || '');\r\n\r\n    const toDown = () => {\r\n      if (store.state.baseInfo.app_url) {\r\n        window.location.href = store.state.baseInfo.app_url\r\n      }\r\n    }\r\n\r\n    localStorage.clear()\r\n    \r\n    const onSubmit = (values) => {\r\n      \r\n      const json = { ...values }\r\n\r\n      login(json).then(res => {\r\n        \r\n        if (res.code === 0) {\r\n       \r\n          store.dispatch('changetoken', res.token)\r\n          store.dispatch('changeuserinfo', res.userinfo || {})\r\n          proxy.$Message({ type: 'success', message: res.info });\r\n          // 记住密码\r\n          if (checked.value) {\r\n            const useri = { ...json, ...{ checked: checked.value } }\r\n            store.dispatch('changeUser', useri)\r\n          } else {\r\n            store.dispatch('clearUser', '')\r\n          }\r\n          push('/')\r\n        } else {\r\n          proxy.$Message({ type: 'error', message: res.info });\r\n        }\r\n\r\n      })\r\n    };\r\n\r\n    return {\r\n      checkeds: ref(false),\r\n      checked,\r\n      userName,\r\n      pwd,\r\n      onSubmit,\r\n      toDown\r\n    };\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n@import '@/styles/theme.scss';\r\n\r\n:deep .van-radio {\r\n  margin-left: 40px;\r\n}\r\n\r\n:deep .van-radio__icon--round .van-icon {\r\n  border-radius: 0;\r\n}\r\n\r\n.gore {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  color: #009995;\r\n  margin-top: 10px;\r\n  margin: 20px 20px;\r\n}\r\n\r\n.gores {\r\n  color: #009995 !important;\r\n}\r\n\r\n:deep .van-button--plain {\r\n  background-color: #009995 !important;\r\n  color: #fff !important;\r\n  border-radius: 10px !important;\r\n  border: none !important;\r\n}\r\n\r\n:deep .van-checkbox__icon {\r\n  margin: 0 10px 0 30px !important;\r\n}\r\n\r\n.homes {\r\n  position: relative;\r\n  height: 100vh;\r\n  font-size: 30px;\r\n  box-sizing: border-box;\r\n\r\n  background-image: url('~@/assets/images/bj.png');\r\n  background-size: 100% 100%;\r\n\r\n  :deep(.van-form) {\r\n    position: relative;\r\n    background-color: #fff;\r\n    width: 90%;\r\n    margin: 0 auto;\r\n    border-radius: 20px;\r\n    padding: 10px 0 0;\r\n\r\n    .van-cell-group--inset {\r\n      padding: 20px 0px;\r\n      background-color: initial;\r\n     \r\n    }\r\n\r\n    .van-ellipsis {\r\n      color: #fff;\r\n    }\r\n\r\n    .van-cell {\r\n      margin-top: 40px;\r\n      \r\n      height: 80px;\r\n      font-size: 30px;\r\n      line-height: 55px;\r\n      border-radius: 7px;\r\n      padding: 10px 10px;\r\n      border: 1px solid #ccc;\r\n      background-color: initial;\r\n\r\n      &.zdy {\r\n        .van-field__left-icon {\r\n          margin-right: 30px;\r\n          margin-top: 10px;\r\n          \r\n        }\r\n      }\r\n\r\n      .van-field__left-icon {\r\n        margin-right: 90px;\r\n\r\n        .van-icon__image {\r\n          height: 42px;\r\n          width: auto;\r\n        }\r\n\r\n        .icon {\r\n          height: 42px;\r\n          width: auto;\r\n          vertical-align: middle;\r\n        }\r\n\r\n        display: flex;\r\n\r\n        .van-dropdown-menu {\r\n          .van-dropdown-menu__bar {\r\n            height: auto;\r\n            background: none;\r\n            box-shadow: none;\r\n          }\r\n\r\n          .van-cell {\r\n            padding: 30px 80px;\r\n            \r\n          }\r\n        }\r\n      }\r\n\r\n      .van-field__control {\r\n        font-size: 30px;\r\n\r\n      }\r\n\r\n      &::after {\r\n        display: none;\r\n      }\r\n    }\r\n\r\n    .van-checkbox {\r\n      margin: 30px 0 60px 0;\r\n\r\n      .van-checkbox__icon {\r\n        font-size: 50px;\r\n        margin-right: 80px;\r\n\r\n        &.van-checkbox__icon--checked .van-icon {\r\n          background-color: $theme;\r\n          border-color: $theme;\r\n        }\r\n      }\r\n\r\n      .van-checkbox__label {\r\n        font-size: 24px;\r\n      }\r\n    }\r\n\r\n    .buttons {\r\n      padding: 0 30px;\r\n      border-radius: 3px;\r\n      .van-button {\r\n        width: 100%;\r\n        font-size: 26px;\r\n        padding: 26px 0;\r\n        height: auto;\r\n        margin-top: 35px;\r\n\r\n        &+.van-button {\r\n          background-color: rgba(255, 255, 255, 0.2);\r\n          border: none;\r\n          color: #fff;\r\n          margin-top: 20px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n\r\n\r\n"], "mappings": ";;;;;EACOA,KAAK,EAAC;AAAO;;sBAEjBC,mBAAA,CAEM;IAFDC,KAAuB,EAAvB;MAAA;IAAA;EAAuB;AAAA;;EAkBpBF,KAAK,EAAC;AAAS;;EAIbA,KAAK,EAAC;AAAM;;sBAIjBC,mBAAA,CAAiC;IAA5BC,KAAqB,EAArB;MAAA;IAAA;EAAqB;AAAA;;;;;;;;;uBA5BhCC,mBAAA,CA+BM,OA/BNC,UA+BM,GA7BLC,UAEM,EACLC,YAAA,CAAuBC,oBAAA,GACvBD,YAAA,CAwBWE,mBAAA;IAxBAC,QAAM,EAAEC,MAAA,CAAAD,QAAQ;IAAEP,KAA2B,EAA3B;MAAA;IAAA;;sBAC3B;MAAA,OASiB,CATjBI,YAAA,CASiBK,yBAAA;QATDC,KAAK,EAAL,EAAK;QAACV,KAAgC,EAAhC;UAAA;UAAA;QAAA;;0BAEpB;UAAA,OAGY,CAHZI,YAAA,CAGYO,oBAAA;YAHDb,KAAK,EAAC,KAAK;YAAEc,IAAI,EAAC,UAAU;YAACC,EAAE,EAAC,UAAU;YAAE,gBAAc,EAAC,OAAO;wBAAYL,MAAA,CAAAM,QAAQ;;qBAARN,MAAA,CAAAM,QAAQ,GAAAC,MAAA;YAAA;YAAGC,WAAW,EAAEC,IAAA,CAAAC,EAAE;YAChHC,KAAK;cAAAC,QAAA;cAAAC,OAAA,EAA8BJ,IAAA,CAAAC,EAAE;YAAA;2EAGxCd,YAAA,CACgEO,oBAAA;wBAD1CH,MAAA,CAAAc,GAAG;;qBAAHd,MAAA,CAAAc,GAAG,GAAAP,MAAA;YAAA;YAAEQ,IAAI,EAAC,UAAU;YAACX,IAAI,EAAC,KAAK;YAAEI,WAAW,EAAEC,IAAA,CAAAC,EAAE;YACnEC,KAAK;cAAAC,QAAA;cAAAC,OAAA,EAA8BJ,IAAA,CAAAC,EAAE;YAAA;;;;UAG1Cd,YAAA,CAAmFoB,uBAAA;QAArEC,KAAK,EAAC,QAAQ;oBAAUjB,MAAA,CAAAkB,OAAO;;iBAAPlB,MAAA,CAAAkB,OAAO,GAAAX,MAAA;QAAA;;0BAAE;UAAA,OAAqB,C,kCAAlBE,IAAA,CAAAC,EAAE,8B;;;;yCAGpDnB,mBAAA,CASM,OATN4B,UASM,GARJvB,YAAA,CAEawB,qBAAA;QAFDC,KAAK,EAAC,GAAG;QAACC,KAAK,EAAL,EAAK;QAACC,KAAK,EAAL,EAAK;QAACR,IAAI,EAAC,SAAS;QAAC,aAAW,EAAC;;0BAC3D;UAAA,OAAqB,C,kCAAlBN,IAAA,CAAAC,EAAE,8B;;;;UAEPnB,mBAAA,CAEM,OAFNiC,UAEM,GADJjC,mBAAA,CAAgG;QAA1FD,KAAK,EAAC,OAAO;QAAEmC,OAAK,EAAAC,MAAA,QAAAA,MAAA,gBAAAnB,MAAA;UAAA,OAAEE,IAAA,CAAAkB,OAAO,CAACC,IAAI;YAAAC,IAAA;UAAA;QAAA;0BAA4BpB,IAAA,CAAAC,EAAE,iC,GAGxEoB,UAAiC,C"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}