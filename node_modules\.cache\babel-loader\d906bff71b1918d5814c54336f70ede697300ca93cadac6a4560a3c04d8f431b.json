{"ast": null, "code": "function _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread(); }\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _iterableToArray(iter) { if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter); }\nfunction _arrayWithoutHoles(arr) { if (Array.isArray(arr)) return _arrayLikeToArray(arr); }\nfunction _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nfunction _iterableToArrayLimit(arr, i) { var _i = null == arr ? null : \"undefined\" != typeof Symbol && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (null != _i) { var _s, _e, _x, _r, _arr = [], _n = !0, _d = !1; try { if (_x = (_i = _i.call(arr)).next, 0 === i) { if (Object(_i) !== _i) return; _n = !1; } else for (; !(_n = (_s = _x.call(_i)).done) && (_arr.push(_s.value), _arr.length !== i); _n = !0); } catch (err) { _d = !0, _e = err; } finally { try { if (!_n && null != _i.return && (_r = _i.return(), Object(_r) !== _r)) return; } finally { if (_d) throw _e; } } return _arr; } }\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\nimport { createVNode as _createVNode, mergeProps as _mergeProps } from \"vue\";\nimport { ref, watch, computed, defineComponent } from \"vue\";\nimport { pick, isDate, truthProp, numericProp, getScrollTop, makeStringProp, makeNumericProp } from \"../utils/index.mjs\";\nimport { t, bem, name, getToday, cloneDate, cloneDates, getPrevDay, getNextDay, compareDay, calcDateNum, compareMonth, getDayByOffset } from \"./utils.mjs\";\nimport { raf, useRect, onMountedOrActivated } from \"@vant/use\";\nimport { useRefs } from \"../composables/use-refs.mjs\";\nimport { useExpose } from \"../composables/use-expose.mjs\";\nimport { Popup } from \"../popup/index.mjs\";\nimport { Button } from \"../button/index.mjs\";\nimport { Toast } from \"../toast/index.mjs\";\nimport CalendarMonth from \"./CalendarMonth.mjs\";\nimport CalendarHeader from \"./CalendarHeader.mjs\";\nvar calendarProps = {\n  show: Boolean,\n  type: makeStringProp(\"single\"),\n  title: String,\n  color: String,\n  round: truthProp,\n  readonly: Boolean,\n  poppable: truthProp,\n  maxRange: makeNumericProp(null),\n  position: makeStringProp(\"bottom\"),\n  teleport: [String, Object],\n  showMark: truthProp,\n  showTitle: truthProp,\n  formatter: Function,\n  rowHeight: numericProp,\n  confirmText: String,\n  rangePrompt: String,\n  lazyRender: truthProp,\n  showConfirm: truthProp,\n  defaultDate: [Date, Array],\n  allowSameDay: Boolean,\n  showSubtitle: truthProp,\n  closeOnPopstate: truthProp,\n  showRangePrompt: truthProp,\n  confirmDisabledText: String,\n  closeOnClickOverlay: truthProp,\n  safeAreaInsetTop: Boolean,\n  safeAreaInsetBottom: truthProp,\n  minDate: {\n    type: Date,\n    validator: isDate,\n    default: getToday\n  },\n  maxDate: {\n    type: Date,\n    validator: isDate,\n    default: function _default() {\n      var now = getToday();\n      return new Date(now.getFullYear(), now.getMonth() + 6, now.getDate());\n    }\n  },\n  firstDayOfWeek: {\n    type: numericProp,\n    default: 0,\n    validator: function validator(val) {\n      return val >= 0 && val <= 6;\n    }\n  }\n};\nvar stdin_default = defineComponent({\n  name: name,\n  props: calendarProps,\n  emits: [\"select\", \"confirm\", \"unselect\", \"month-show\", \"over-range\", \"update:show\", \"click-subtitle\"],\n  setup: function setup(props, _ref) {\n    var emit = _ref.emit,\n      slots = _ref.slots;\n    var limitDateRange = function limitDateRange(date) {\n      var minDate = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : props.minDate;\n      var maxDate = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : props.maxDate;\n      if (compareDay(date, minDate) === -1) {\n        return minDate;\n      }\n      if (compareDay(date, maxDate) === 1) {\n        return maxDate;\n      }\n      return date;\n    };\n    var getInitialDate = function getInitialDate() {\n      var defaultDate = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : props.defaultDate;\n      var type = props.type,\n        minDate = props.minDate,\n        maxDate = props.maxDate,\n        allowSameDay = props.allowSameDay;\n      if (defaultDate === null) {\n        return defaultDate;\n      }\n      var now = getToday();\n      if (type === \"range\") {\n        if (!Array.isArray(defaultDate)) {\n          defaultDate = [];\n        }\n        var start = limitDateRange(defaultDate[0] || now, minDate, allowSameDay ? maxDate : getPrevDay(maxDate));\n        var end = limitDateRange(defaultDate[1] || now, allowSameDay ? minDate : getNextDay(minDate));\n        return [start, end];\n      }\n      if (type === \"multiple\") {\n        if (Array.isArray(defaultDate)) {\n          return defaultDate.map(function (date) {\n            return limitDateRange(date);\n          });\n        }\n        return [limitDateRange(now)];\n      }\n      if (!defaultDate || Array.isArray(defaultDate)) {\n        defaultDate = now;\n      }\n      return limitDateRange(defaultDate);\n    };\n    var bodyHeight;\n    var bodyRef = ref();\n    var subtitle = ref(\"\");\n    var currentDate = ref(getInitialDate());\n    var _useRefs = useRefs(),\n      _useRefs2 = _slicedToArray(_useRefs, 2),\n      monthRefs = _useRefs2[0],\n      setMonthRefs = _useRefs2[1];\n    var dayOffset = computed(function () {\n      return props.firstDayOfWeek ? +props.firstDayOfWeek % 7 : 0;\n    });\n    var months = computed(function () {\n      var months2 = [];\n      var cursor = new Date(props.minDate);\n      cursor.setDate(1);\n      do {\n        months2.push(new Date(cursor));\n        cursor.setMonth(cursor.getMonth() + 1);\n      } while (compareMonth(cursor, props.maxDate) !== 1);\n      return months2;\n    });\n    var buttonDisabled = computed(function () {\n      if (currentDate.value) {\n        if (props.type === \"range\") {\n          return !currentDate.value[0] || !currentDate.value[1];\n        }\n        if (props.type === \"multiple\") {\n          return !currentDate.value.length;\n        }\n      }\n      return !currentDate.value;\n    });\n    var getSelectedDate = function getSelectedDate() {\n      return currentDate.value;\n    };\n    var onScroll = function onScroll() {\n      var top = getScrollTop(bodyRef.value);\n      var bottom = top + bodyHeight;\n      var heights = months.value.map(function (item, index) {\n        return monthRefs.value[index].getHeight();\n      });\n      var heightSum = heights.reduce(function (a, b) {\n        return a + b;\n      }, 0);\n      if (bottom > heightSum && top > 0) {\n        return;\n      }\n      var height = 0;\n      var currentMonth;\n      var visibleRange = [-1, -1];\n      for (var i = 0; i < months.value.length; i++) {\n        var month = monthRefs.value[i];\n        var visible = height <= bottom && height + heights[i] >= top;\n        if (visible) {\n          visibleRange[1] = i;\n          if (!currentMonth) {\n            currentMonth = month;\n            visibleRange[0] = i;\n          }\n          if (!monthRefs.value[i].showed) {\n            monthRefs.value[i].showed = true;\n            emit(\"month-show\", {\n              date: month.date,\n              title: month.getTitle()\n            });\n          }\n        }\n        height += heights[i];\n      }\n      months.value.forEach(function (month, index) {\n        var visible = index >= visibleRange[0] - 1 && index <= visibleRange[1] + 1;\n        monthRefs.value[index].setVisible(visible);\n      });\n      if (currentMonth) {\n        subtitle.value = currentMonth.getTitle();\n      }\n    };\n    var scrollToDate = function scrollToDate(targetDate) {\n      raf(function () {\n        months.value.some(function (month, index) {\n          if (compareMonth(month, targetDate) === 0) {\n            if (bodyRef.value) {\n              monthRefs.value[index].scrollToDate(bodyRef.value, targetDate);\n            }\n            return true;\n          }\n          return false;\n        });\n        onScroll();\n      });\n    };\n    var scrollToCurrentDate = function scrollToCurrentDate() {\n      if (props.poppable && !props.show) {\n        return;\n      }\n      if (currentDate.value) {\n        var targetDate = props.type === \"single\" ? currentDate.value : currentDate.value[0];\n        if (isDate(targetDate)) {\n          scrollToDate(targetDate);\n        }\n      } else {\n        raf(onScroll);\n      }\n    };\n    var init = function init() {\n      if (props.poppable && !props.show) {\n        return;\n      }\n      raf(function () {\n        bodyHeight = Math.floor(useRect(bodyRef).height);\n      });\n      scrollToCurrentDate();\n    };\n    var reset = function reset() {\n      var date = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : getInitialDate();\n      currentDate.value = date;\n      scrollToCurrentDate();\n    };\n    var checkRange = function checkRange(date) {\n      var maxRange = props.maxRange,\n        rangePrompt = props.rangePrompt,\n        showRangePrompt = props.showRangePrompt;\n      if (maxRange && calcDateNum(date) > maxRange) {\n        if (showRangePrompt) {\n          Toast(rangePrompt || t(\"rangePrompt\", maxRange));\n        }\n        emit(\"over-range\");\n        return false;\n      }\n      return true;\n    };\n    var onConfirm = function onConfirm() {\n      var _a;\n      return emit(\"confirm\", (_a = currentDate.value) != null ? _a : cloneDates(currentDate.value));\n    };\n    var select = function select(date, complete) {\n      var setCurrentDate = function setCurrentDate(date2) {\n        currentDate.value = date2;\n        emit(\"select\", cloneDates(date2));\n      };\n      if (complete && props.type === \"range\") {\n        var valid = checkRange(date);\n        if (!valid) {\n          setCurrentDate([date[0], getDayByOffset(date[0], +props.maxRange - 1)]);\n          return;\n        }\n      }\n      setCurrentDate(date);\n      if (complete && !props.showConfirm) {\n        onConfirm();\n      }\n    };\n    var getDisabledDate = function getDisabledDate(disabledDays2, startDay, date) {\n      var _a;\n      return (_a = disabledDays2.find(function (day) {\n        return compareDay(startDay, day.date) === -1 && compareDay(day.date, date) === -1;\n      })) == null ? void 0 : _a.date;\n    };\n    var disabledDays = computed(function () {\n      return monthRefs.value.reduce(function (arr, ref2) {\n        var _a, _b;\n        arr.push.apply(arr, _toConsumableArray((_b = (_a = ref2.disabledDays) == null ? void 0 : _a.value) != null ? _b : []));\n        return arr;\n      }, []);\n    });\n    var onClickDay = function onClickDay(item) {\n      if (props.readonly || !item.date) {\n        return;\n      }\n      var date = item.date;\n      var type = props.type;\n      if (type === \"range\") {\n        if (!currentDate.value) {\n          select([date]);\n          return;\n        }\n        var _currentDate$value = _slicedToArray(currentDate.value, 2),\n          startDay = _currentDate$value[0],\n          endDay = _currentDate$value[1];\n        if (startDay && !endDay) {\n          var compareToStart = compareDay(date, startDay);\n          if (compareToStart === 1) {\n            var disabledDay = getDisabledDate(disabledDays.value, startDay, date);\n            if (disabledDay) {\n              var endDay2 = getPrevDay(disabledDay);\n              if (compareDay(startDay, endDay2) === -1) {\n                select([startDay, endDay2]);\n              } else {\n                select([date]);\n              }\n            } else {\n              select([startDay, date], true);\n            }\n          } else if (compareToStart === -1) {\n            select([date]);\n          } else if (props.allowSameDay) {\n            select([date, date], true);\n          }\n        } else {\n          select([date]);\n        }\n      } else if (type === \"multiple\") {\n        if (!currentDate.value) {\n          select([date]);\n          return;\n        }\n        var dates = currentDate.value;\n        var selectedIndex = dates.findIndex(function (dateItem) {\n          return compareDay(dateItem, date) === 0;\n        });\n        if (selectedIndex !== -1) {\n          var _dates$splice = dates.splice(selectedIndex, 1),\n            _dates$splice2 = _slicedToArray(_dates$splice, 1),\n            unselectedDate = _dates$splice2[0];\n          emit(\"unselect\", cloneDate(unselectedDate));\n        } else if (props.maxRange && dates.length >= props.maxRange) {\n          Toast(props.rangePrompt || t(\"rangePrompt\", props.maxRange));\n        } else {\n          select([].concat(_toConsumableArray(dates), [date]));\n        }\n      } else {\n        select(date, true);\n      }\n    };\n    var updateShow = function updateShow(value) {\n      return emit(\"update:show\", value);\n    };\n    var renderMonth = function renderMonth(date, index) {\n      var showMonthTitle = index !== 0 || !props.showSubtitle;\n      return _createVNode(CalendarMonth, _mergeProps({\n        \"ref\": setMonthRefs(index),\n        \"date\": date,\n        \"currentDate\": currentDate.value,\n        \"showMonthTitle\": showMonthTitle,\n        \"firstDayOfWeek\": dayOffset.value\n      }, pick(props, [\"type\", \"color\", \"minDate\", \"maxDate\", \"showMark\", \"formatter\", \"rowHeight\", \"lazyRender\", \"showSubtitle\", \"allowSameDay\"]), {\n        \"onClick\": onClickDay\n      }), pick(slots, [\"top-info\", \"bottom-info\"]));\n    };\n    var renderFooterButton = function renderFooterButton() {\n      if (slots.footer) {\n        return slots.footer();\n      }\n      if (props.showConfirm) {\n        var slot = slots[\"confirm-text\"];\n        var disabled = buttonDisabled.value;\n        var text = disabled ? props.confirmDisabledText : props.confirmText;\n        return _createVNode(Button, {\n          \"round\": true,\n          \"block\": true,\n          \"type\": \"danger\",\n          \"color\": props.color,\n          \"class\": bem(\"confirm\"),\n          \"disabled\": disabled,\n          \"nativeType\": \"button\",\n          \"onClick\": onConfirm\n        }, {\n          default: function _default() {\n            return [slot ? slot({\n              disabled: disabled\n            }) : text || t(\"confirm\")];\n          }\n        });\n      }\n    };\n    var renderFooter = function renderFooter() {\n      return _createVNode(\"div\", {\n        \"class\": [bem(\"footer\"), {\n          \"van-safe-area-bottom\": props.safeAreaInsetBottom\n        }]\n      }, [renderFooterButton()]);\n    };\n    var renderCalendar = function renderCalendar() {\n      return _createVNode(\"div\", {\n        \"class\": bem()\n      }, [_createVNode(CalendarHeader, {\n        \"title\": props.title,\n        \"subtitle\": subtitle.value,\n        \"showTitle\": props.showTitle,\n        \"showSubtitle\": props.showSubtitle,\n        \"firstDayOfWeek\": dayOffset.value,\n        \"onClick-subtitle\": function onClickSubtitle(event) {\n          return emit(\"click-subtitle\", event);\n        }\n      }, pick(slots, [\"title\", \"subtitle\"])), _createVNode(\"div\", {\n        \"ref\": bodyRef,\n        \"class\": bem(\"body\"),\n        \"onScroll\": onScroll\n      }, [months.value.map(renderMonth)]), renderFooter()]);\n    };\n    watch(function () {\n      return props.show;\n    }, init);\n    watch(function () {\n      return [props.type, props.minDate, props.maxDate];\n    }, function () {\n      return reset(getInitialDate(currentDate.value));\n    });\n    watch(function () {\n      return props.defaultDate;\n    }, function () {\n      var value = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : null;\n      currentDate.value = value;\n      scrollToCurrentDate();\n    });\n    useExpose({\n      reset: reset,\n      scrollToDate: scrollToDate,\n      getSelectedDate: getSelectedDate\n    });\n    onMountedOrActivated(init);\n    return function () {\n      if (props.poppable) {\n        return _createVNode(Popup, {\n          \"show\": props.show,\n          \"class\": bem(\"popup\"),\n          \"round\": props.round,\n          \"position\": props.position,\n          \"closeable\": props.showTitle || props.showSubtitle,\n          \"teleport\": props.teleport,\n          \"closeOnPopstate\": props.closeOnPopstate,\n          \"safeAreaInsetTop\": props.safeAreaInsetTop,\n          \"closeOnClickOverlay\": props.closeOnClickOverlay,\n          \"onUpdate:show\": updateShow\n        }, {\n          default: renderCalendar\n        });\n      }\n      return renderCalendar();\n    };\n  }\n});\nexport { stdin_default as default };", "map": {"version": 3, "names": ["createVNode", "_createVNode", "mergeProps", "_mergeProps", "ref", "watch", "computed", "defineComponent", "pick", "isDate", "truthProp", "numericProp", "getScrollTop", "makeStringProp", "makeNumericProp", "t", "bem", "name", "get<PERSON><PERSON>y", "cloneDate", "cloneDates", "getPrevDay", "getNextDay", "compareDay", "calcDateNum", "compareMonth", "getDayByOffset", "raf", "useRect", "onMountedOrActivated", "useRefs", "useExpose", "Popup", "<PERSON><PERSON>", "Toast", "CalendarMonth", "CalendarHeader", "calendarProps", "show", "Boolean", "type", "title", "String", "color", "round", "readonly", "poppable", "max<PERSON><PERSON><PERSON>", "position", "teleport", "Object", "showMark", "showTitle", "formatter", "Function", "rowHeight", "confirmText", "rangePrompt", "lazy<PERSON>ender", "showConfirm", "defaultDate", "Date", "Array", "allowSameDay", "showSubtitle", "closeOnPopstate", "showRangePrompt", "confirmDisabledText", "closeOnClickOverlay", "safeAreaInsetTop", "safeAreaInsetBottom", "minDate", "validator", "default", "maxDate", "_default", "now", "getFullYear", "getMonth", "getDate", "firstDayOfWeek", "val", "stdin_default", "props", "emits", "setup", "_ref", "emit", "slots", "limitDateRange", "date", "arguments", "length", "undefined", "getInitialDate", "isArray", "start", "end", "map", "bodyHeight", "bodyRef", "subtitle", "currentDate", "_useRefs", "_useRefs2", "_slicedToArray", "monthRefs", "setMonthRefs", "dayOffset", "months", "months2", "cursor", "setDate", "push", "setMonth", "buttonDisabled", "value", "getSelectedDate", "onScroll", "top", "bottom", "heights", "item", "index", "getHeight", "heightSum", "reduce", "a", "b", "height", "currentMonth", "visibleRange", "i", "month", "visible", "showed", "getTitle", "for<PERSON>ach", "setVisible", "scrollToDate", "targetDate", "some", "scrollToCurrentDate", "init", "Math", "floor", "reset", "checkRange", "onConfirm", "_a", "select", "complete", "setCurrentDate", "date2", "valid", "getDisabledDate", "disabledDays2", "startDay", "find", "day", "disabledDays", "arr", "ref2", "_b", "apply", "_toConsumableArray", "onClickDay", "_currentDate$value", "endDay", "compareToStart", "disabledDay", "endDay2", "dates", "selectedIndex", "findIndex", "dateItem", "_dates$splice", "splice", "_dates$splice2", "unselectedDate", "concat", "updateShow", "renderMonth", "showMonthTitle", "renderFooterButton", "footer", "slot", "disabled", "text", "renderFooter", "renderCalendar", "onClickSubtitle", "event"], "sources": ["C:/Users/<USER>/Downloads/Telegram Desktop/vue3_3.0/vue3_3.0/node_modules/vant/es/calendar/Calendar.mjs"], "sourcesContent": ["import { createVNode as _createVNode, mergeProps as _mergeProps } from \"vue\";\nimport { ref, watch, computed, defineComponent } from \"vue\";\nimport { pick, isDate, truthProp, numericProp, getScrollTop, makeStringProp, makeNumericProp } from \"../utils/index.mjs\";\nimport { t, bem, name, getToday, cloneDate, cloneDates, getPrevDay, getNextDay, compareDay, calcDateNum, compareMonth, getDayByOffset } from \"./utils.mjs\";\nimport { raf, useRect, onMountedOrActivated } from \"@vant/use\";\nimport { useRefs } from \"../composables/use-refs.mjs\";\nimport { useExpose } from \"../composables/use-expose.mjs\";\nimport { Popup } from \"../popup/index.mjs\";\nimport { Button } from \"../button/index.mjs\";\nimport { Toast } from \"../toast/index.mjs\";\nimport CalendarMonth from \"./CalendarMonth.mjs\";\nimport CalendarHeader from \"./CalendarHeader.mjs\";\nconst calendarProps = {\n  show: Boolean,\n  type: makeStringProp(\"single\"),\n  title: String,\n  color: String,\n  round: truthProp,\n  readonly: Boolean,\n  poppable: truthProp,\n  maxRange: makeNumericProp(null),\n  position: makeStringProp(\"bottom\"),\n  teleport: [String, Object],\n  showMark: truthProp,\n  showTitle: truthProp,\n  formatter: Function,\n  rowHeight: numericProp,\n  confirmText: String,\n  rangePrompt: String,\n  lazyRender: truthProp,\n  showConfirm: truthProp,\n  defaultDate: [Date, Array],\n  allowSameDay: Boolean,\n  showSubtitle: truthProp,\n  closeOnPopstate: truthProp,\n  showRangePrompt: truthProp,\n  confirmDisabledText: String,\n  closeOnClickOverlay: truthProp,\n  safeAreaInsetTop: Boolean,\n  safeAreaInsetBottom: truthProp,\n  minDate: {\n    type: Date,\n    validator: isDate,\n    default: getToday\n  },\n  maxDate: {\n    type: Date,\n    validator: isDate,\n    default: () => {\n      const now = getToday();\n      return new Date(now.getFullYear(), now.getMonth() + 6, now.getDate());\n    }\n  },\n  firstDayOfWeek: {\n    type: numericProp,\n    default: 0,\n    validator: (val) => val >= 0 && val <= 6\n  }\n};\nvar stdin_default = defineComponent({\n  name,\n  props: calendarProps,\n  emits: [\"select\", \"confirm\", \"unselect\", \"month-show\", \"over-range\", \"update:show\", \"click-subtitle\"],\n  setup(props, {\n    emit,\n    slots\n  }) {\n    const limitDateRange = (date, minDate = props.minDate, maxDate = props.maxDate) => {\n      if (compareDay(date, minDate) === -1) {\n        return minDate;\n      }\n      if (compareDay(date, maxDate) === 1) {\n        return maxDate;\n      }\n      return date;\n    };\n    const getInitialDate = (defaultDate = props.defaultDate) => {\n      const {\n        type,\n        minDate,\n        maxDate,\n        allowSameDay\n      } = props;\n      if (defaultDate === null) {\n        return defaultDate;\n      }\n      const now = getToday();\n      if (type === \"range\") {\n        if (!Array.isArray(defaultDate)) {\n          defaultDate = [];\n        }\n        const start = limitDateRange(defaultDate[0] || now, minDate, allowSameDay ? maxDate : getPrevDay(maxDate));\n        const end = limitDateRange(defaultDate[1] || now, allowSameDay ? minDate : getNextDay(minDate));\n        return [start, end];\n      }\n      if (type === \"multiple\") {\n        if (Array.isArray(defaultDate)) {\n          return defaultDate.map((date) => limitDateRange(date));\n        }\n        return [limitDateRange(now)];\n      }\n      if (!defaultDate || Array.isArray(defaultDate)) {\n        defaultDate = now;\n      }\n      return limitDateRange(defaultDate);\n    };\n    let bodyHeight;\n    const bodyRef = ref();\n    const subtitle = ref(\"\");\n    const currentDate = ref(getInitialDate());\n    const [monthRefs, setMonthRefs] = useRefs();\n    const dayOffset = computed(() => props.firstDayOfWeek ? +props.firstDayOfWeek % 7 : 0);\n    const months = computed(() => {\n      const months2 = [];\n      const cursor = new Date(props.minDate);\n      cursor.setDate(1);\n      do {\n        months2.push(new Date(cursor));\n        cursor.setMonth(cursor.getMonth() + 1);\n      } while (compareMonth(cursor, props.maxDate) !== 1);\n      return months2;\n    });\n    const buttonDisabled = computed(() => {\n      if (currentDate.value) {\n        if (props.type === \"range\") {\n          return !currentDate.value[0] || !currentDate.value[1];\n        }\n        if (props.type === \"multiple\") {\n          return !currentDate.value.length;\n        }\n      }\n      return !currentDate.value;\n    });\n    const getSelectedDate = () => currentDate.value;\n    const onScroll = () => {\n      const top = getScrollTop(bodyRef.value);\n      const bottom = top + bodyHeight;\n      const heights = months.value.map((item, index) => monthRefs.value[index].getHeight());\n      const heightSum = heights.reduce((a, b) => a + b, 0);\n      if (bottom > heightSum && top > 0) {\n        return;\n      }\n      let height = 0;\n      let currentMonth;\n      const visibleRange = [-1, -1];\n      for (let i = 0; i < months.value.length; i++) {\n        const month = monthRefs.value[i];\n        const visible = height <= bottom && height + heights[i] >= top;\n        if (visible) {\n          visibleRange[1] = i;\n          if (!currentMonth) {\n            currentMonth = month;\n            visibleRange[0] = i;\n          }\n          if (!monthRefs.value[i].showed) {\n            monthRefs.value[i].showed = true;\n            emit(\"month-show\", {\n              date: month.date,\n              title: month.getTitle()\n            });\n          }\n        }\n        height += heights[i];\n      }\n      months.value.forEach((month, index) => {\n        const visible = index >= visibleRange[0] - 1 && index <= visibleRange[1] + 1;\n        monthRefs.value[index].setVisible(visible);\n      });\n      if (currentMonth) {\n        subtitle.value = currentMonth.getTitle();\n      }\n    };\n    const scrollToDate = (targetDate) => {\n      raf(() => {\n        months.value.some((month, index) => {\n          if (compareMonth(month, targetDate) === 0) {\n            if (bodyRef.value) {\n              monthRefs.value[index].scrollToDate(bodyRef.value, targetDate);\n            }\n            return true;\n          }\n          return false;\n        });\n        onScroll();\n      });\n    };\n    const scrollToCurrentDate = () => {\n      if (props.poppable && !props.show) {\n        return;\n      }\n      if (currentDate.value) {\n        const targetDate = props.type === \"single\" ? currentDate.value : currentDate.value[0];\n        if (isDate(targetDate)) {\n          scrollToDate(targetDate);\n        }\n      } else {\n        raf(onScroll);\n      }\n    };\n    const init = () => {\n      if (props.poppable && !props.show) {\n        return;\n      }\n      raf(() => {\n        bodyHeight = Math.floor(useRect(bodyRef).height);\n      });\n      scrollToCurrentDate();\n    };\n    const reset = (date = getInitialDate()) => {\n      currentDate.value = date;\n      scrollToCurrentDate();\n    };\n    const checkRange = (date) => {\n      const {\n        maxRange,\n        rangePrompt,\n        showRangePrompt\n      } = props;\n      if (maxRange && calcDateNum(date) > maxRange) {\n        if (showRangePrompt) {\n          Toast(rangePrompt || t(\"rangePrompt\", maxRange));\n        }\n        emit(\"over-range\");\n        return false;\n      }\n      return true;\n    };\n    const onConfirm = () => {\n      var _a;\n      return emit(\"confirm\", (_a = currentDate.value) != null ? _a : cloneDates(currentDate.value));\n    };\n    const select = (date, complete) => {\n      const setCurrentDate = (date2) => {\n        currentDate.value = date2;\n        emit(\"select\", cloneDates(date2));\n      };\n      if (complete && props.type === \"range\") {\n        const valid = checkRange(date);\n        if (!valid) {\n          setCurrentDate([date[0], getDayByOffset(date[0], +props.maxRange - 1)]);\n          return;\n        }\n      }\n      setCurrentDate(date);\n      if (complete && !props.showConfirm) {\n        onConfirm();\n      }\n    };\n    const getDisabledDate = (disabledDays2, startDay, date) => {\n      var _a;\n      return (_a = disabledDays2.find((day) => compareDay(startDay, day.date) === -1 && compareDay(day.date, date) === -1)) == null ? void 0 : _a.date;\n    };\n    const disabledDays = computed(() => monthRefs.value.reduce((arr, ref2) => {\n      var _a, _b;\n      arr.push(...(_b = (_a = ref2.disabledDays) == null ? void 0 : _a.value) != null ? _b : []);\n      return arr;\n    }, []));\n    const onClickDay = (item) => {\n      if (props.readonly || !item.date) {\n        return;\n      }\n      const {\n        date\n      } = item;\n      const {\n        type\n      } = props;\n      if (type === \"range\") {\n        if (!currentDate.value) {\n          select([date]);\n          return;\n        }\n        const [startDay, endDay] = currentDate.value;\n        if (startDay && !endDay) {\n          const compareToStart = compareDay(date, startDay);\n          if (compareToStart === 1) {\n            const disabledDay = getDisabledDate(disabledDays.value, startDay, date);\n            if (disabledDay) {\n              const endDay2 = getPrevDay(disabledDay);\n              if (compareDay(startDay, endDay2) === -1) {\n                select([startDay, endDay2]);\n              } else {\n                select([date]);\n              }\n            } else {\n              select([startDay, date], true);\n            }\n          } else if (compareToStart === -1) {\n            select([date]);\n          } else if (props.allowSameDay) {\n            select([date, date], true);\n          }\n        } else {\n          select([date]);\n        }\n      } else if (type === \"multiple\") {\n        if (!currentDate.value) {\n          select([date]);\n          return;\n        }\n        const dates = currentDate.value;\n        const selectedIndex = dates.findIndex((dateItem) => compareDay(dateItem, date) === 0);\n        if (selectedIndex !== -1) {\n          const [unselectedDate] = dates.splice(selectedIndex, 1);\n          emit(\"unselect\", cloneDate(unselectedDate));\n        } else if (props.maxRange && dates.length >= props.maxRange) {\n          Toast(props.rangePrompt || t(\"rangePrompt\", props.maxRange));\n        } else {\n          select([...dates, date]);\n        }\n      } else {\n        select(date, true);\n      }\n    };\n    const updateShow = (value) => emit(\"update:show\", value);\n    const renderMonth = (date, index) => {\n      const showMonthTitle = index !== 0 || !props.showSubtitle;\n      return _createVNode(CalendarMonth, _mergeProps({\n        \"ref\": setMonthRefs(index),\n        \"date\": date,\n        \"currentDate\": currentDate.value,\n        \"showMonthTitle\": showMonthTitle,\n        \"firstDayOfWeek\": dayOffset.value\n      }, pick(props, [\"type\", \"color\", \"minDate\", \"maxDate\", \"showMark\", \"formatter\", \"rowHeight\", \"lazyRender\", \"showSubtitle\", \"allowSameDay\"]), {\n        \"onClick\": onClickDay\n      }), pick(slots, [\"top-info\", \"bottom-info\"]));\n    };\n    const renderFooterButton = () => {\n      if (slots.footer) {\n        return slots.footer();\n      }\n      if (props.showConfirm) {\n        const slot = slots[\"confirm-text\"];\n        const disabled = buttonDisabled.value;\n        const text = disabled ? props.confirmDisabledText : props.confirmText;\n        return _createVNode(Button, {\n          \"round\": true,\n          \"block\": true,\n          \"type\": \"danger\",\n          \"color\": props.color,\n          \"class\": bem(\"confirm\"),\n          \"disabled\": disabled,\n          \"nativeType\": \"button\",\n          \"onClick\": onConfirm\n        }, {\n          default: () => [slot ? slot({\n            disabled\n          }) : text || t(\"confirm\")]\n        });\n      }\n    };\n    const renderFooter = () => _createVNode(\"div\", {\n      \"class\": [bem(\"footer\"), {\n        \"van-safe-area-bottom\": props.safeAreaInsetBottom\n      }]\n    }, [renderFooterButton()]);\n    const renderCalendar = () => _createVNode(\"div\", {\n      \"class\": bem()\n    }, [_createVNode(CalendarHeader, {\n      \"title\": props.title,\n      \"subtitle\": subtitle.value,\n      \"showTitle\": props.showTitle,\n      \"showSubtitle\": props.showSubtitle,\n      \"firstDayOfWeek\": dayOffset.value,\n      \"onClick-subtitle\": (event) => emit(\"click-subtitle\", event)\n    }, pick(slots, [\"title\", \"subtitle\"])), _createVNode(\"div\", {\n      \"ref\": bodyRef,\n      \"class\": bem(\"body\"),\n      \"onScroll\": onScroll\n    }, [months.value.map(renderMonth)]), renderFooter()]);\n    watch(() => props.show, init);\n    watch(() => [props.type, props.minDate, props.maxDate], () => reset(getInitialDate(currentDate.value)));\n    watch(() => props.defaultDate, (value = null) => {\n      currentDate.value = value;\n      scrollToCurrentDate();\n    });\n    useExpose({\n      reset,\n      scrollToDate,\n      getSelectedDate\n    });\n    onMountedOrActivated(init);\n    return () => {\n      if (props.poppable) {\n        return _createVNode(Popup, {\n          \"show\": props.show,\n          \"class\": bem(\"popup\"),\n          \"round\": props.round,\n          \"position\": props.position,\n          \"closeable\": props.showTitle || props.showSubtitle,\n          \"teleport\": props.teleport,\n          \"closeOnPopstate\": props.closeOnPopstate,\n          \"safeAreaInsetTop\": props.safeAreaInsetTop,\n          \"closeOnClickOverlay\": props.closeOnClickOverlay,\n          \"onUpdate:show\": updateShow\n        }, {\n          default: renderCalendar\n        });\n      }\n      return renderCalendar();\n    };\n  }\n});\nexport {\n  stdin_default as default\n};\n"], "mappings": ";;;;;;;;;;AAAA,SAASA,WAAW,IAAIC,YAAY,EAAEC,UAAU,IAAIC,WAAW,QAAQ,KAAK;AAC5E,SAASC,GAAG,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,eAAe,QAAQ,KAAK;AAC3D,SAASC,IAAI,EAAEC,MAAM,EAAEC,SAAS,EAAEC,WAAW,EAAEC,YAAY,EAAEC,cAAc,EAAEC,eAAe,QAAQ,oBAAoB;AACxH,SAASC,CAAC,EAAEC,GAAG,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,UAAU,EAAEC,UAAU,EAAEC,UAAU,EAAEC,UAAU,EAAEC,WAAW,EAAEC,YAAY,EAAEC,cAAc,QAAQ,aAAa;AAC1J,SAASC,GAAG,EAAEC,OAAO,EAAEC,oBAAoB,QAAQ,WAAW;AAC9D,SAASC,OAAO,QAAQ,6BAA6B;AACrD,SAASC,SAAS,QAAQ,+BAA+B;AACzD,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,SAASC,MAAM,QAAQ,qBAAqB;AAC5C,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,OAAOC,aAAa,MAAM,qBAAqB;AAC/C,OAAOC,cAAc,MAAM,sBAAsB;AACjD,IAAMC,aAAa,GAAG;EACpBC,IAAI,EAAEC,OAAO;EACbC,IAAI,EAAE3B,cAAc,CAAC,QAAQ,CAAC;EAC9B4B,KAAK,EAAEC,MAAM;EACbC,KAAK,EAAED,MAAM;EACbE,KAAK,EAAElC,SAAS;EAChBmC,QAAQ,EAAEN,OAAO;EACjBO,QAAQ,EAAEpC,SAAS;EACnBqC,QAAQ,EAAEjC,eAAe,CAAC,IAAI,CAAC;EAC/BkC,QAAQ,EAAEnC,cAAc,CAAC,QAAQ,CAAC;EAClCoC,QAAQ,EAAE,CAACP,MAAM,EAAEQ,MAAM,CAAC;EAC1BC,QAAQ,EAAEzC,SAAS;EACnB0C,SAAS,EAAE1C,SAAS;EACpB2C,SAAS,EAAEC,QAAQ;EACnBC,SAAS,EAAE5C,WAAW;EACtB6C,WAAW,EAAEd,MAAM;EACnBe,WAAW,EAAEf,MAAM;EACnBgB,UAAU,EAAEhD,SAAS;EACrBiD,WAAW,EAAEjD,SAAS;EACtBkD,WAAW,EAAE,CAACC,IAAI,EAAEC,KAAK,CAAC;EAC1BC,YAAY,EAAExB,OAAO;EACrByB,YAAY,EAAEtD,SAAS;EACvBuD,eAAe,EAAEvD,SAAS;EAC1BwD,eAAe,EAAExD,SAAS;EAC1ByD,mBAAmB,EAAEzB,MAAM;EAC3B0B,mBAAmB,EAAE1D,SAAS;EAC9B2D,gBAAgB,EAAE9B,OAAO;EACzB+B,mBAAmB,EAAE5D,SAAS;EAC9B6D,OAAO,EAAE;IACP/B,IAAI,EAAEqB,IAAI;IACVW,SAAS,EAAE/D,MAAM;IACjBgE,OAAO,EAAEvD;EACX,CAAC;EACDwD,OAAO,EAAE;IACPlC,IAAI,EAAEqB,IAAI;IACVW,SAAS,EAAE/D,MAAM;IACjBgE,OAAO,EAAE,SAAAE,SAAA,EAAM;MACb,IAAMC,GAAG,GAAG1D,QAAQ,CAAC,CAAC;MACtB,OAAO,IAAI2C,IAAI,CAACe,GAAG,CAACC,WAAW,CAAC,CAAC,EAAED,GAAG,CAACE,QAAQ,CAAC,CAAC,GAAG,CAAC,EAAEF,GAAG,CAACG,OAAO,CAAC,CAAC,CAAC;IACvE;EACF,CAAC;EACDC,cAAc,EAAE;IACdxC,IAAI,EAAE7B,WAAW;IACjB8D,OAAO,EAAE,CAAC;IACVD,SAAS,EAAE,SAAAA,UAACS,GAAG;MAAA,OAAKA,GAAG,IAAI,CAAC,IAAIA,GAAG,IAAI,CAAC;IAAA;EAC1C;AACF,CAAC;AACD,IAAIC,aAAa,GAAG3E,eAAe,CAAC;EAClCU,IAAI,EAAJA,IAAI;EACJkE,KAAK,EAAE9C,aAAa;EACpB+C,KAAK,EAAE,CAAC,QAAQ,EAAE,SAAS,EAAE,UAAU,EAAE,YAAY,EAAE,YAAY,EAAE,aAAa,EAAE,gBAAgB,CAAC;EACrGC,KAAK,WAAAA,MAACF,KAAK,EAAAG,IAAA,EAGR;IAAA,IAFDC,IAAI,GAAAD,IAAA,CAAJC,IAAI;MACJC,KAAK,GAAAF,IAAA,CAALE,KAAK;IAEL,IAAMC,cAAc,GAAG,SAAjBA,cAAcA,CAAIC,IAAI,EAAuD;MAAA,IAArDnB,OAAO,GAAAoB,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAGR,KAAK,CAACZ,OAAO;MAAA,IAAEG,OAAO,GAAAiB,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAGR,KAAK,CAACT,OAAO;MAC5E,IAAInD,UAAU,CAACmE,IAAI,EAAEnB,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE;QACpC,OAAOA,OAAO;MAChB;MACA,IAAIhD,UAAU,CAACmE,IAAI,EAAEhB,OAAO,CAAC,KAAK,CAAC,EAAE;QACnC,OAAOA,OAAO;MAChB;MACA,OAAOgB,IAAI;IACb,CAAC;IACD,IAAMI,cAAc,GAAG,SAAjBA,cAAcA,CAAA,EAAwC;MAAA,IAApClC,WAAW,GAAA+B,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAGR,KAAK,CAACvB,WAAW;MACrD,IACEpB,IAAI,GAIF2C,KAAK,CAJP3C,IAAI;QACJ+B,OAAO,GAGLY,KAAK,CAHPZ,OAAO;QACPG,OAAO,GAELS,KAAK,CAFPT,OAAO;QACPX,YAAY,GACVoB,KAAK,CADPpB,YAAY;MAEd,IAAIH,WAAW,KAAK,IAAI,EAAE;QACxB,OAAOA,WAAW;MACpB;MACA,IAAMgB,GAAG,GAAG1D,QAAQ,CAAC,CAAC;MACtB,IAAIsB,IAAI,KAAK,OAAO,EAAE;QACpB,IAAI,CAACsB,KAAK,CAACiC,OAAO,CAACnC,WAAW,CAAC,EAAE;UAC/BA,WAAW,GAAG,EAAE;QAClB;QACA,IAAMoC,KAAK,GAAGP,cAAc,CAAC7B,WAAW,CAAC,CAAC,CAAC,IAAIgB,GAAG,EAAEL,OAAO,EAAER,YAAY,GAAGW,OAAO,GAAGrD,UAAU,CAACqD,OAAO,CAAC,CAAC;QAC1G,IAAMuB,GAAG,GAAGR,cAAc,CAAC7B,WAAW,CAAC,CAAC,CAAC,IAAIgB,GAAG,EAAEb,YAAY,GAAGQ,OAAO,GAAGjD,UAAU,CAACiD,OAAO,CAAC,CAAC;QAC/F,OAAO,CAACyB,KAAK,EAAEC,GAAG,CAAC;MACrB;MACA,IAAIzD,IAAI,KAAK,UAAU,EAAE;QACvB,IAAIsB,KAAK,CAACiC,OAAO,CAACnC,WAAW,CAAC,EAAE;UAC9B,OAAOA,WAAW,CAACsC,GAAG,CAAC,UAACR,IAAI;YAAA,OAAKD,cAAc,CAACC,IAAI,CAAC;UAAA,EAAC;QACxD;QACA,OAAO,CAACD,cAAc,CAACb,GAAG,CAAC,CAAC;MAC9B;MACA,IAAI,CAAChB,WAAW,IAAIE,KAAK,CAACiC,OAAO,CAACnC,WAAW,CAAC,EAAE;QAC9CA,WAAW,GAAGgB,GAAG;MACnB;MACA,OAAOa,cAAc,CAAC7B,WAAW,CAAC;IACpC,CAAC;IACD,IAAIuC,UAAU;IACd,IAAMC,OAAO,GAAGhG,GAAG,CAAC,CAAC;IACrB,IAAMiG,QAAQ,GAAGjG,GAAG,CAAC,EAAE,CAAC;IACxB,IAAMkG,WAAW,GAAGlG,GAAG,CAAC0F,cAAc,CAAC,CAAC,CAAC;IACzC,IAAAS,QAAA,GAAkCzE,OAAO,CAAC,CAAC;MAAA0E,SAAA,GAAAC,cAAA,CAAAF,QAAA;MAApCG,SAAS,GAAAF,SAAA;MAAEG,YAAY,GAAAH,SAAA;IAC9B,IAAMI,SAAS,GAAGtG,QAAQ,CAAC;MAAA,OAAM6E,KAAK,CAACH,cAAc,GAAG,CAACG,KAAK,CAACH,cAAc,GAAG,CAAC,GAAG,CAAC;IAAA,EAAC;IACtF,IAAM6B,MAAM,GAAGvG,QAAQ,CAAC,YAAM;MAC5B,IAAMwG,OAAO,GAAG,EAAE;MAClB,IAAMC,MAAM,GAAG,IAAIlD,IAAI,CAACsB,KAAK,CAACZ,OAAO,CAAC;MACtCwC,MAAM,CAACC,OAAO,CAAC,CAAC,CAAC;MACjB,GAAG;QACDF,OAAO,CAACG,IAAI,CAAC,IAAIpD,IAAI,CAACkD,MAAM,CAAC,CAAC;QAC9BA,MAAM,CAACG,QAAQ,CAACH,MAAM,CAACjC,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;MACxC,CAAC,QAAQrD,YAAY,CAACsF,MAAM,EAAE5B,KAAK,CAACT,OAAO,CAAC,KAAK,CAAC;MAClD,OAAOoC,OAAO;IAChB,CAAC,CAAC;IACF,IAAMK,cAAc,GAAG7G,QAAQ,CAAC,YAAM;MACpC,IAAIgG,WAAW,CAACc,KAAK,EAAE;QACrB,IAAIjC,KAAK,CAAC3C,IAAI,KAAK,OAAO,EAAE;UAC1B,OAAO,CAAC8D,WAAW,CAACc,KAAK,CAAC,CAAC,CAAC,IAAI,CAACd,WAAW,CAACc,KAAK,CAAC,CAAC,CAAC;QACvD;QACA,IAAIjC,KAAK,CAAC3C,IAAI,KAAK,UAAU,EAAE;UAC7B,OAAO,CAAC8D,WAAW,CAACc,KAAK,CAACxB,MAAM;QAClC;MACF;MACA,OAAO,CAACU,WAAW,CAACc,KAAK;IAC3B,CAAC,CAAC;IACF,IAAMC,eAAe,GAAG,SAAlBA,eAAeA,CAAA;MAAA,OAASf,WAAW,CAACc,KAAK;IAAA;IAC/C,IAAME,QAAQ,GAAG,SAAXA,QAAQA,CAAA,EAAS;MACrB,IAAMC,GAAG,GAAG3G,YAAY,CAACwF,OAAO,CAACgB,KAAK,CAAC;MACvC,IAAMI,MAAM,GAAGD,GAAG,GAAGpB,UAAU;MAC/B,IAAMsB,OAAO,GAAGZ,MAAM,CAACO,KAAK,CAAClB,GAAG,CAAC,UAACwB,IAAI,EAAEC,KAAK;QAAA,OAAKjB,SAAS,CAACU,KAAK,CAACO,KAAK,CAAC,CAACC,SAAS,CAAC,CAAC;MAAA,EAAC;MACrF,IAAMC,SAAS,GAAGJ,OAAO,CAACK,MAAM,CAAC,UAACC,CAAC,EAAEC,CAAC;QAAA,OAAKD,CAAC,GAAGC,CAAC;MAAA,GAAE,CAAC,CAAC;MACpD,IAAIR,MAAM,GAAGK,SAAS,IAAIN,GAAG,GAAG,CAAC,EAAE;QACjC;MACF;MACA,IAAIU,MAAM,GAAG,CAAC;MACd,IAAIC,YAAY;MAChB,IAAMC,YAAY,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MAC7B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGvB,MAAM,CAACO,KAAK,CAACxB,MAAM,EAAEwC,CAAC,EAAE,EAAE;QAC5C,IAAMC,KAAK,GAAG3B,SAAS,CAACU,KAAK,CAACgB,CAAC,CAAC;QAChC,IAAME,OAAO,GAAGL,MAAM,IAAIT,MAAM,IAAIS,MAAM,GAAGR,OAAO,CAACW,CAAC,CAAC,IAAIb,GAAG;QAC9D,IAAIe,OAAO,EAAE;UACXH,YAAY,CAAC,CAAC,CAAC,GAAGC,CAAC;UACnB,IAAI,CAACF,YAAY,EAAE;YACjBA,YAAY,GAAGG,KAAK;YACpBF,YAAY,CAAC,CAAC,CAAC,GAAGC,CAAC;UACrB;UACA,IAAI,CAAC1B,SAAS,CAACU,KAAK,CAACgB,CAAC,CAAC,CAACG,MAAM,EAAE;YAC9B7B,SAAS,CAACU,KAAK,CAACgB,CAAC,CAAC,CAACG,MAAM,GAAG,IAAI;YAChChD,IAAI,CAAC,YAAY,EAAE;cACjBG,IAAI,EAAE2C,KAAK,CAAC3C,IAAI;cAChBjD,KAAK,EAAE4F,KAAK,CAACG,QAAQ,CAAC;YACxB,CAAC,CAAC;UACJ;QACF;QACAP,MAAM,IAAIR,OAAO,CAACW,CAAC,CAAC;MACtB;MACAvB,MAAM,CAACO,KAAK,CAACqB,OAAO,CAAC,UAACJ,KAAK,EAAEV,KAAK,EAAK;QACrC,IAAMW,OAAO,GAAGX,KAAK,IAAIQ,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIR,KAAK,IAAIQ,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC;QAC5EzB,SAAS,CAACU,KAAK,CAACO,KAAK,CAAC,CAACe,UAAU,CAACJ,OAAO,CAAC;MAC5C,CAAC,CAAC;MACF,IAAIJ,YAAY,EAAE;QAChB7B,QAAQ,CAACe,KAAK,GAAGc,YAAY,CAACM,QAAQ,CAAC,CAAC;MAC1C;IACF,CAAC;IACD,IAAMG,YAAY,GAAG,SAAfA,YAAYA,CAAIC,UAAU,EAAK;MACnCjH,GAAG,CAAC,YAAM;QACRkF,MAAM,CAACO,KAAK,CAACyB,IAAI,CAAC,UAACR,KAAK,EAAEV,KAAK,EAAK;UAClC,IAAIlG,YAAY,CAAC4G,KAAK,EAAEO,UAAU,CAAC,KAAK,CAAC,EAAE;YACzC,IAAIxC,OAAO,CAACgB,KAAK,EAAE;cACjBV,SAAS,CAACU,KAAK,CAACO,KAAK,CAAC,CAACgB,YAAY,CAACvC,OAAO,CAACgB,KAAK,EAAEwB,UAAU,CAAC;YAChE;YACA,OAAO,IAAI;UACb;UACA,OAAO,KAAK;QACd,CAAC,CAAC;QACFtB,QAAQ,CAAC,CAAC;MACZ,CAAC,CAAC;IACJ,CAAC;IACD,IAAMwB,mBAAmB,GAAG,SAAtBA,mBAAmBA,CAAA,EAAS;MAChC,IAAI3D,KAAK,CAACrC,QAAQ,IAAI,CAACqC,KAAK,CAAC7C,IAAI,EAAE;QACjC;MACF;MACA,IAAIgE,WAAW,CAACc,KAAK,EAAE;QACrB,IAAMwB,UAAU,GAAGzD,KAAK,CAAC3C,IAAI,KAAK,QAAQ,GAAG8D,WAAW,CAACc,KAAK,GAAGd,WAAW,CAACc,KAAK,CAAC,CAAC,CAAC;QACrF,IAAI3G,MAAM,CAACmI,UAAU,CAAC,EAAE;UACtBD,YAAY,CAACC,UAAU,CAAC;QAC1B;MACF,CAAC,MAAM;QACLjH,GAAG,CAAC2F,QAAQ,CAAC;MACf;IACF,CAAC;IACD,IAAMyB,IAAI,GAAG,SAAPA,IAAIA,CAAA,EAAS;MACjB,IAAI5D,KAAK,CAACrC,QAAQ,IAAI,CAACqC,KAAK,CAAC7C,IAAI,EAAE;QACjC;MACF;MACAX,GAAG,CAAC,YAAM;QACRwE,UAAU,GAAG6C,IAAI,CAACC,KAAK,CAACrH,OAAO,CAACwE,OAAO,CAAC,CAAC6B,MAAM,CAAC;MAClD,CAAC,CAAC;MACFa,mBAAmB,CAAC,CAAC;IACvB,CAAC;IACD,IAAMI,KAAK,GAAG,SAARA,KAAKA,CAAA,EAAgC;MAAA,IAA5BxD,IAAI,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAGG,cAAc,CAAC,CAAC;MACpCQ,WAAW,CAACc,KAAK,GAAG1B,IAAI;MACxBoD,mBAAmB,CAAC,CAAC;IACvB,CAAC;IACD,IAAMK,UAAU,GAAG,SAAbA,UAAUA,CAAIzD,IAAI,EAAK;MAC3B,IACE3C,QAAQ,GAGNoC,KAAK,CAHPpC,QAAQ;QACRU,WAAW,GAET0B,KAAK,CAFP1B,WAAW;QACXS,eAAe,GACbiB,KAAK,CADPjB,eAAe;MAEjB,IAAInB,QAAQ,IAAIvB,WAAW,CAACkE,IAAI,CAAC,GAAG3C,QAAQ,EAAE;QAC5C,IAAImB,eAAe,EAAE;UACnBhC,KAAK,CAACuB,WAAW,IAAI1C,CAAC,CAAC,aAAa,EAAEgC,QAAQ,CAAC,CAAC;QAClD;QACAwC,IAAI,CAAC,YAAY,CAAC;QAClB,OAAO,KAAK;MACd;MACA,OAAO,IAAI;IACb,CAAC;IACD,IAAM6D,SAAS,GAAG,SAAZA,SAASA,CAAA,EAAS;MACtB,IAAIC,EAAE;MACN,OAAO9D,IAAI,CAAC,SAAS,EAAE,CAAC8D,EAAE,GAAG/C,WAAW,CAACc,KAAK,KAAK,IAAI,GAAGiC,EAAE,GAAGjI,UAAU,CAACkF,WAAW,CAACc,KAAK,CAAC,CAAC;IAC/F,CAAC;IACD,IAAMkC,MAAM,GAAG,SAATA,MAAMA,CAAI5D,IAAI,EAAE6D,QAAQ,EAAK;MACjC,IAAMC,cAAc,GAAG,SAAjBA,cAAcA,CAAIC,KAAK,EAAK;QAChCnD,WAAW,CAACc,KAAK,GAAGqC,KAAK;QACzBlE,IAAI,CAAC,QAAQ,EAAEnE,UAAU,CAACqI,KAAK,CAAC,CAAC;MACnC,CAAC;MACD,IAAIF,QAAQ,IAAIpE,KAAK,CAAC3C,IAAI,KAAK,OAAO,EAAE;QACtC,IAAMkH,KAAK,GAAGP,UAAU,CAACzD,IAAI,CAAC;QAC9B,IAAI,CAACgE,KAAK,EAAE;UACVF,cAAc,CAAC,CAAC9D,IAAI,CAAC,CAAC,CAAC,EAAEhE,cAAc,CAACgE,IAAI,CAAC,CAAC,CAAC,EAAE,CAACP,KAAK,CAACpC,QAAQ,GAAG,CAAC,CAAC,CAAC,CAAC;UACvE;QACF;MACF;MACAyG,cAAc,CAAC9D,IAAI,CAAC;MACpB,IAAI6D,QAAQ,IAAI,CAACpE,KAAK,CAACxB,WAAW,EAAE;QAClCyF,SAAS,CAAC,CAAC;MACb;IACF,CAAC;IACD,IAAMO,eAAe,GAAG,SAAlBA,eAAeA,CAAIC,aAAa,EAAEC,QAAQ,EAAEnE,IAAI,EAAK;MACzD,IAAI2D,EAAE;MACN,OAAO,CAACA,EAAE,GAAGO,aAAa,CAACE,IAAI,CAAC,UAACC,GAAG;QAAA,OAAKxI,UAAU,CAACsI,QAAQ,EAAEE,GAAG,CAACrE,IAAI,CAAC,KAAK,CAAC,CAAC,IAAInE,UAAU,CAACwI,GAAG,CAACrE,IAAI,EAAEA,IAAI,CAAC,KAAK,CAAC,CAAC;MAAA,EAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG2D,EAAE,CAAC3D,IAAI;IAClJ,CAAC;IACD,IAAMsE,YAAY,GAAG1J,QAAQ,CAAC;MAAA,OAAMoG,SAAS,CAACU,KAAK,CAACU,MAAM,CAAC,UAACmC,GAAG,EAAEC,IAAI,EAAK;QACxE,IAAIb,EAAE,EAAEc,EAAE;QACVF,GAAG,CAAChD,IAAI,CAAAmD,KAAA,CAARH,GAAG,EAAAI,kBAAA,CAAS,CAACF,EAAE,GAAG,CAACd,EAAE,GAAGa,IAAI,CAACF,YAAY,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGX,EAAE,CAACjC,KAAK,KAAK,IAAI,GAAG+C,EAAE,GAAG,EAAE,EAAC;QAC1F,OAAOF,GAAG;MACZ,CAAC,EAAE,EAAE,CAAC;IAAA,EAAC;IACP,IAAMK,UAAU,GAAG,SAAbA,UAAUA,CAAI5C,IAAI,EAAK;MAC3B,IAAIvC,KAAK,CAACtC,QAAQ,IAAI,CAAC6E,IAAI,CAAChC,IAAI,EAAE;QAChC;MACF;MACA,IACEA,IAAI,GACFgC,IAAI,CADNhC,IAAI;MAEN,IACElD,IAAI,GACF2C,KAAK,CADP3C,IAAI;MAEN,IAAIA,IAAI,KAAK,OAAO,EAAE;QACpB,IAAI,CAAC8D,WAAW,CAACc,KAAK,EAAE;UACtBkC,MAAM,CAAC,CAAC5D,IAAI,CAAC,CAAC;UACd;QACF;QACA,IAAA6E,kBAAA,GAAA9D,cAAA,CAA2BH,WAAW,CAACc,KAAK;UAArCyC,QAAQ,GAAAU,kBAAA;UAAEC,MAAM,GAAAD,kBAAA;QACvB,IAAIV,QAAQ,IAAI,CAACW,MAAM,EAAE;UACvB,IAAMC,cAAc,GAAGlJ,UAAU,CAACmE,IAAI,EAAEmE,QAAQ,CAAC;UACjD,IAAIY,cAAc,KAAK,CAAC,EAAE;YACxB,IAAMC,WAAW,GAAGf,eAAe,CAACK,YAAY,CAAC5C,KAAK,EAAEyC,QAAQ,EAAEnE,IAAI,CAAC;YACvE,IAAIgF,WAAW,EAAE;cACf,IAAMC,OAAO,GAAGtJ,UAAU,CAACqJ,WAAW,CAAC;cACvC,IAAInJ,UAAU,CAACsI,QAAQ,EAAEc,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE;gBACxCrB,MAAM,CAAC,CAACO,QAAQ,EAAEc,OAAO,CAAC,CAAC;cAC7B,CAAC,MAAM;gBACLrB,MAAM,CAAC,CAAC5D,IAAI,CAAC,CAAC;cAChB;YACF,CAAC,MAAM;cACL4D,MAAM,CAAC,CAACO,QAAQ,EAAEnE,IAAI,CAAC,EAAE,IAAI,CAAC;YAChC;UACF,CAAC,MAAM,IAAI+E,cAAc,KAAK,CAAC,CAAC,EAAE;YAChCnB,MAAM,CAAC,CAAC5D,IAAI,CAAC,CAAC;UAChB,CAAC,MAAM,IAAIP,KAAK,CAACpB,YAAY,EAAE;YAC7BuF,MAAM,CAAC,CAAC5D,IAAI,EAAEA,IAAI,CAAC,EAAE,IAAI,CAAC;UAC5B;QACF,CAAC,MAAM;UACL4D,MAAM,CAAC,CAAC5D,IAAI,CAAC,CAAC;QAChB;MACF,CAAC,MAAM,IAAIlD,IAAI,KAAK,UAAU,EAAE;QAC9B,IAAI,CAAC8D,WAAW,CAACc,KAAK,EAAE;UACtBkC,MAAM,CAAC,CAAC5D,IAAI,CAAC,CAAC;UACd;QACF;QACA,IAAMkF,KAAK,GAAGtE,WAAW,CAACc,KAAK;QAC/B,IAAMyD,aAAa,GAAGD,KAAK,CAACE,SAAS,CAAC,UAACC,QAAQ;UAAA,OAAKxJ,UAAU,CAACwJ,QAAQ,EAAErF,IAAI,CAAC,KAAK,CAAC;QAAA,EAAC;QACrF,IAAImF,aAAa,KAAK,CAAC,CAAC,EAAE;UACxB,IAAAG,aAAA,GAAyBJ,KAAK,CAACK,MAAM,CAACJ,aAAa,EAAE,CAAC,CAAC;YAAAK,cAAA,GAAAzE,cAAA,CAAAuE,aAAA;YAAhDG,cAAc,GAAAD,cAAA;UACrB3F,IAAI,CAAC,UAAU,EAAEpE,SAAS,CAACgK,cAAc,CAAC,CAAC;QAC7C,CAAC,MAAM,IAAIhG,KAAK,CAACpC,QAAQ,IAAI6H,KAAK,CAAChF,MAAM,IAAIT,KAAK,CAACpC,QAAQ,EAAE;UAC3Db,KAAK,CAACiD,KAAK,CAAC1B,WAAW,IAAI1C,CAAC,CAAC,aAAa,EAAEoE,KAAK,CAACpC,QAAQ,CAAC,CAAC;QAC9D,CAAC,MAAM;UACLuG,MAAM,IAAA8B,MAAA,CAAAf,kBAAA,CAAKO,KAAK,IAAElF,IAAI,EAAC,CAAC;QAC1B;MACF,CAAC,MAAM;QACL4D,MAAM,CAAC5D,IAAI,EAAE,IAAI,CAAC;MACpB;IACF,CAAC;IACD,IAAM2F,UAAU,GAAG,SAAbA,UAAUA,CAAIjE,KAAK;MAAA,OAAK7B,IAAI,CAAC,aAAa,EAAE6B,KAAK,CAAC;IAAA;IACxD,IAAMkE,WAAW,GAAG,SAAdA,WAAWA,CAAI5F,IAAI,EAAEiC,KAAK,EAAK;MACnC,IAAM4D,cAAc,GAAG5D,KAAK,KAAK,CAAC,IAAI,CAACxC,KAAK,CAACnB,YAAY;MACzD,OAAO/D,YAAY,CAACkC,aAAa,EAAEhC,WAAW,CAAC;QAC7C,KAAK,EAAEwG,YAAY,CAACgB,KAAK,CAAC;QAC1B,MAAM,EAAEjC,IAAI;QACZ,aAAa,EAAEY,WAAW,CAACc,KAAK;QAChC,gBAAgB,EAAEmE,cAAc;QAChC,gBAAgB,EAAE3E,SAAS,CAACQ;MAC9B,CAAC,EAAE5G,IAAI,CAAC2E,KAAK,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY,EAAE,cAAc,EAAE,cAAc,CAAC,CAAC,EAAE;QAC3I,SAAS,EAAEmF;MACb,CAAC,CAAC,EAAE9J,IAAI,CAACgF,KAAK,EAAE,CAAC,UAAU,EAAE,aAAa,CAAC,CAAC,CAAC;IAC/C,CAAC;IACD,IAAMgG,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAA,EAAS;MAC/B,IAAIhG,KAAK,CAACiG,MAAM,EAAE;QAChB,OAAOjG,KAAK,CAACiG,MAAM,CAAC,CAAC;MACvB;MACA,IAAItG,KAAK,CAACxB,WAAW,EAAE;QACrB,IAAM+H,IAAI,GAAGlG,KAAK,CAAC,cAAc,CAAC;QAClC,IAAMmG,QAAQ,GAAGxE,cAAc,CAACC,KAAK;QACrC,IAAMwE,IAAI,GAAGD,QAAQ,GAAGxG,KAAK,CAAChB,mBAAmB,GAAGgB,KAAK,CAAC3B,WAAW;QACrE,OAAOvD,YAAY,CAACgC,MAAM,EAAE;UAC1B,OAAO,EAAE,IAAI;UACb,OAAO,EAAE,IAAI;UACb,MAAM,EAAE,QAAQ;UAChB,OAAO,EAAEkD,KAAK,CAACxC,KAAK;UACpB,OAAO,EAAE3B,GAAG,CAAC,SAAS,CAAC;UACvB,UAAU,EAAE2K,QAAQ;UACpB,YAAY,EAAE,QAAQ;UACtB,SAAS,EAAEvC;QACb,CAAC,EAAE;UACD3E,OAAO,EAAE,SAAAE,SAAA;YAAA,OAAM,CAAC+G,IAAI,GAAGA,IAAI,CAAC;cAC1BC,QAAQ,EAARA;YACF,CAAC,CAAC,GAAGC,IAAI,IAAI7K,CAAC,CAAC,SAAS,CAAC,CAAC;UAAA;QAC5B,CAAC,CAAC;MACJ;IACF,CAAC;IACD,IAAM8K,YAAY,GAAG,SAAfA,YAAYA,CAAA;MAAA,OAAS5L,YAAY,CAAC,KAAK,EAAE;QAC7C,OAAO,EAAE,CAACe,GAAG,CAAC,QAAQ,CAAC,EAAE;UACvB,sBAAsB,EAAEmE,KAAK,CAACb;QAChC,CAAC;MACH,CAAC,EAAE,CAACkH,kBAAkB,CAAC,CAAC,CAAC,CAAC;IAAA;IAC1B,IAAMM,cAAc,GAAG,SAAjBA,cAAcA,CAAA;MAAA,OAAS7L,YAAY,CAAC,KAAK,EAAE;QAC/C,OAAO,EAAEe,GAAG,CAAC;MACf,CAAC,EAAE,CAACf,YAAY,CAACmC,cAAc,EAAE;QAC/B,OAAO,EAAE+C,KAAK,CAAC1C,KAAK;QACpB,UAAU,EAAE4D,QAAQ,CAACe,KAAK;QAC1B,WAAW,EAAEjC,KAAK,CAAC/B,SAAS;QAC5B,cAAc,EAAE+B,KAAK,CAACnB,YAAY;QAClC,gBAAgB,EAAE4C,SAAS,CAACQ,KAAK;QACjC,kBAAkB,EAAE,SAAA2E,gBAACC,KAAK;UAAA,OAAKzG,IAAI,CAAC,gBAAgB,EAAEyG,KAAK,CAAC;QAAA;MAC9D,CAAC,EAAExL,IAAI,CAACgF,KAAK,EAAE,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC,CAAC,EAAEvF,YAAY,CAAC,KAAK,EAAE;QAC1D,KAAK,EAAEmG,OAAO;QACd,OAAO,EAAEpF,GAAG,CAAC,MAAM,CAAC;QACpB,UAAU,EAAEsG;MACd,CAAC,EAAE,CAACT,MAAM,CAACO,KAAK,CAAClB,GAAG,CAACoF,WAAW,CAAC,CAAC,CAAC,EAAEO,YAAY,CAAC,CAAC,CAAC,CAAC;IAAA;IACrDxL,KAAK,CAAC;MAAA,OAAM8E,KAAK,CAAC7C,IAAI;IAAA,GAAEyG,IAAI,CAAC;IAC7B1I,KAAK,CAAC;MAAA,OAAM,CAAC8E,KAAK,CAAC3C,IAAI,EAAE2C,KAAK,CAACZ,OAAO,EAAEY,KAAK,CAACT,OAAO,CAAC;IAAA,GAAE;MAAA,OAAMwE,KAAK,CAACpD,cAAc,CAACQ,WAAW,CAACc,KAAK,CAAC,CAAC;IAAA,EAAC;IACvG/G,KAAK,CAAC;MAAA,OAAM8E,KAAK,CAACvB,WAAW;IAAA,GAAE,YAAkB;MAAA,IAAjBwD,KAAK,GAAAzB,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;MAC1CW,WAAW,CAACc,KAAK,GAAGA,KAAK;MACzB0B,mBAAmB,CAAC,CAAC;IACvB,CAAC,CAAC;IACF/G,SAAS,CAAC;MACRmH,KAAK,EAALA,KAAK;MACLP,YAAY,EAAZA,YAAY;MACZtB,eAAe,EAAfA;IACF,CAAC,CAAC;IACFxF,oBAAoB,CAACkH,IAAI,CAAC;IAC1B,OAAO,YAAM;MACX,IAAI5D,KAAK,CAACrC,QAAQ,EAAE;QAClB,OAAO7C,YAAY,CAAC+B,KAAK,EAAE;UACzB,MAAM,EAAEmD,KAAK,CAAC7C,IAAI;UAClB,OAAO,EAAEtB,GAAG,CAAC,OAAO,CAAC;UACrB,OAAO,EAAEmE,KAAK,CAACvC,KAAK;UACpB,UAAU,EAAEuC,KAAK,CAACnC,QAAQ;UAC1B,WAAW,EAAEmC,KAAK,CAAC/B,SAAS,IAAI+B,KAAK,CAACnB,YAAY;UAClD,UAAU,EAAEmB,KAAK,CAAClC,QAAQ;UAC1B,iBAAiB,EAAEkC,KAAK,CAAClB,eAAe;UACxC,kBAAkB,EAAEkB,KAAK,CAACd,gBAAgB;UAC1C,qBAAqB,EAAEc,KAAK,CAACf,mBAAmB;UAChD,eAAe,EAAEiH;QACnB,CAAC,EAAE;UACD5G,OAAO,EAAEqH;QACX,CAAC,CAAC;MACJ;MACA,OAAOA,cAAc,CAAC,CAAC;IACzB,CAAC;EACH;AACF,CAAC,CAAC;AACF,SACE5G,aAAa,IAAIT,OAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}