{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _ContactList from \"./ContactList.mjs\";\nvar ContactList = withInstall(_ContactList);\nvar stdin_default = ContactList;\nexport { ContactList, stdin_default as default };", "map": {"version": 3, "names": ["withInstall", "_ContactList", "ContactList", "stdin_default", "default"], "sources": ["C:/Users/<USER>/Desktop/vue3_3.0/node_modules/vant/es/contact-list/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _ContactList from \"./ContactList.mjs\";\nconst ContactList = withInstall(_ContactList);\nvar stdin_default = ContactList;\nexport {\n  ContactList,\n  stdin_default as default\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,YAAY,MAAM,mBAAmB;AAC5C,IAAMC,WAAW,GAAGF,WAAW,CAACC,YAAY,CAAC;AAC7C,IAAIE,aAAa,GAAGD,WAAW;AAC/B,SACEA,WAAW,EACXC,aAAa,IAAIC,OAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}