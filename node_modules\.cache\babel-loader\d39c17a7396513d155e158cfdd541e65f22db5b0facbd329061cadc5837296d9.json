{"ast": null, "code": "function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nfunction _iterableToArrayLimit(arr, i) { var _i = null == arr ? null : \"undefined\" != typeof Symbol && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (null != _i) { var _s, _e, _x, _r, _arr = [], _n = !0, _d = !1; try { if (_x = (_i = _i.call(arr)).next, 0 === i) { if (Object(_i) !== _i) return; _n = !1; } else for (; !(_n = (_s = _x.call(_i)).done) && (_arr.push(_s.value), _arr.length !== i); _n = !0); } catch (err) { _d = !0, _e = err; } finally { try { if (!_n && null != _i.return && (_r = _i.return(), Object(_r) !== _r)) return; } finally { if (_d) throw _e; } } return _arr; } }\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\nimport { createVNode as _createVNode } from \"vue\";\nimport { computed, defineComponent } from \"vue\";\nimport { extend, addUnit, numericProp, getSizeStyle, makeStringProp, createNamespace } from \"../utils/index.mjs\";\nvar _createNamespace = createNamespace(\"loading\"),\n  _createNamespace2 = _slicedToArray(_createNamespace, 2),\n  name = _createNamespace2[0],\n  bem = _createNamespace2[1];\nvar SpinIcon = Array(12).fill(null).map(function (_, index) {\n  return _createVNode(\"i\", {\n    \"class\": bem(\"line\", String(index + 1))\n  }, null);\n});\nvar CircularIcon = _createVNode(\"svg\", {\n  \"class\": bem(\"circular\"),\n  \"viewBox\": \"25 25 50 50\"\n}, [_createVNode(\"circle\", {\n  \"cx\": \"50\",\n  \"cy\": \"50\",\n  \"r\": \"20\",\n  \"fill\": \"none\"\n}, null)]);\nvar loadingProps = {\n  size: numericProp,\n  type: makeStringProp(\"circular\"),\n  color: String,\n  vertical: Boolean,\n  textSize: numericProp,\n  textColor: String\n};\nvar stdin_default = defineComponent({\n  name: name,\n  props: loadingProps,\n  setup: function setup(props, _ref) {\n    var slots = _ref.slots;\n    var spinnerStyle = computed(function () {\n      return extend({\n        color: props.color\n      }, getSizeStyle(props.size));\n    });\n    var renderText = function renderText() {\n      var _a;\n      if (slots.default) {\n        return _createVNode(\"span\", {\n          \"class\": bem(\"text\"),\n          \"style\": {\n            fontSize: addUnit(props.textSize),\n            color: (_a = props.textColor) != null ? _a : props.color\n          }\n        }, [slots.default()]);\n      }\n    };\n    return function () {\n      var type = props.type,\n        vertical = props.vertical;\n      return _createVNode(\"div\", {\n        \"class\": bem([type, {\n          vertical: vertical\n        }]),\n        \"aria-live\": \"polite\",\n        \"aria-busy\": true\n      }, [_createVNode(\"span\", {\n        \"class\": bem(\"spinner\", type),\n        \"style\": spinnerStyle.value\n      }, [type === \"spinner\" ? SpinIcon : CircularIcon]), renderText()]);\n    };\n  }\n});\nexport { stdin_default as default };", "map": {"version": 3, "names": ["createVNode", "_createVNode", "computed", "defineComponent", "extend", "addUnit", "numericProp", "getSizeStyle", "makeStringProp", "createNamespace", "_createNamespace", "_createNamespace2", "_slicedToArray", "name", "bem", "SpinIcon", "Array", "fill", "map", "_", "index", "String", "CircularIcon", "loadingProps", "size", "type", "color", "vertical", "Boolean", "textSize", "textColor", "stdin_default", "props", "setup", "_ref", "slots", "spinnerStyle", "renderText", "_a", "default", "fontSize", "value"], "sources": ["C:/Users/<USER>/Downloads/Telegram Desktop/vue3_3.0/vue3_3.0/node_modules/vant/es/loading/Loading.mjs"], "sourcesContent": ["import { createVNode as _createVNode } from \"vue\";\nimport { computed, defineComponent } from \"vue\";\nimport { extend, addUnit, numericProp, getSizeStyle, makeStringProp, createNamespace } from \"../utils/index.mjs\";\nconst [name, bem] = createNamespace(\"loading\");\nconst SpinIcon = Array(12).fill(null).map((_, index) => _createVNode(\"i\", {\n  \"class\": bem(\"line\", String(index + 1))\n}, null));\nconst CircularIcon = _createVNode(\"svg\", {\n  \"class\": bem(\"circular\"),\n  \"viewBox\": \"25 25 50 50\"\n}, [_createVNode(\"circle\", {\n  \"cx\": \"50\",\n  \"cy\": \"50\",\n  \"r\": \"20\",\n  \"fill\": \"none\"\n}, null)]);\nconst loadingProps = {\n  size: numericProp,\n  type: makeStringProp(\"circular\"),\n  color: String,\n  vertical: Boolean,\n  textSize: numericProp,\n  textColor: String\n};\nvar stdin_default = defineComponent({\n  name,\n  props: loadingProps,\n  setup(props, {\n    slots\n  }) {\n    const spinnerStyle = computed(() => extend({\n      color: props.color\n    }, getSizeStyle(props.size)));\n    const renderText = () => {\n      var _a;\n      if (slots.default) {\n        return _createVNode(\"span\", {\n          \"class\": bem(\"text\"),\n          \"style\": {\n            fontSize: addUnit(props.textSize),\n            color: (_a = props.textColor) != null ? _a : props.color\n          }\n        }, [slots.default()]);\n      }\n    };\n    return () => {\n      const {\n        type,\n        vertical\n      } = props;\n      return _createVNode(\"div\", {\n        \"class\": bem([type, {\n          vertical\n        }]),\n        \"aria-live\": \"polite\",\n        \"aria-busy\": true\n      }, [_createVNode(\"span\", {\n        \"class\": bem(\"spinner\", type),\n        \"style\": spinnerStyle.value\n      }, [type === \"spinner\" ? SpinIcon : CircularIcon]), renderText()]);\n    };\n  }\n});\nexport {\n  stdin_default as default\n};\n"], "mappings": ";;;;;;AAAA,SAASA,WAAW,IAAIC,YAAY,QAAQ,KAAK;AACjD,SAASC,QAAQ,EAAEC,eAAe,QAAQ,KAAK;AAC/C,SAASC,MAAM,EAAEC,OAAO,EAAEC,WAAW,EAAEC,YAAY,EAAEC,cAAc,EAAEC,eAAe,QAAQ,oBAAoB;AAChH,IAAAC,gBAAA,GAAoBD,eAAe,CAAC,SAAS,CAAC;EAAAE,iBAAA,GAAAC,cAAA,CAAAF,gBAAA;EAAvCG,IAAI,GAAAF,iBAAA;EAAEG,GAAG,GAAAH,iBAAA;AAChB,IAAMI,QAAQ,GAAGC,KAAK,CAAC,EAAE,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,CAACC,GAAG,CAAC,UAACC,CAAC,EAAEC,KAAK;EAAA,OAAKnB,YAAY,CAAC,GAAG,EAAE;IACxE,OAAO,EAAEa,GAAG,CAAC,MAAM,EAAEO,MAAM,CAACD,KAAK,GAAG,CAAC,CAAC;EACxC,CAAC,EAAE,IAAI,CAAC;AAAA,EAAC;AACT,IAAME,YAAY,GAAGrB,YAAY,CAAC,KAAK,EAAE;EACvC,OAAO,EAAEa,GAAG,CAAC,UAAU,CAAC;EACxB,SAAS,EAAE;AACb,CAAC,EAAE,CAACb,YAAY,CAAC,QAAQ,EAAE;EACzB,IAAI,EAAE,IAAI;EACV,IAAI,EAAE,IAAI;EACV,GAAG,EAAE,IAAI;EACT,MAAM,EAAE;AACV,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;AACV,IAAMsB,YAAY,GAAG;EACnBC,IAAI,EAAElB,WAAW;EACjBmB,IAAI,EAAEjB,cAAc,CAAC,UAAU,CAAC;EAChCkB,KAAK,EAAEL,MAAM;EACbM,QAAQ,EAAEC,OAAO;EACjBC,QAAQ,EAAEvB,WAAW;EACrBwB,SAAS,EAAET;AACb,CAAC;AACD,IAAIU,aAAa,GAAG5B,eAAe,CAAC;EAClCU,IAAI,EAAJA,IAAI;EACJmB,KAAK,EAAET,YAAY;EACnBU,KAAK,WAAAA,MAACD,KAAK,EAAAE,IAAA,EAER;IAAA,IADDC,KAAK,GAAAD,IAAA,CAALC,KAAK;IAEL,IAAMC,YAAY,GAAGlC,QAAQ,CAAC;MAAA,OAAME,MAAM,CAAC;QACzCsB,KAAK,EAAEM,KAAK,CAACN;MACf,CAAC,EAAEnB,YAAY,CAACyB,KAAK,CAACR,IAAI,CAAC,CAAC;IAAA,EAAC;IAC7B,IAAMa,UAAU,GAAG,SAAbA,UAAUA,CAAA,EAAS;MACvB,IAAIC,EAAE;MACN,IAAIH,KAAK,CAACI,OAAO,EAAE;QACjB,OAAOtC,YAAY,CAAC,MAAM,EAAE;UAC1B,OAAO,EAAEa,GAAG,CAAC,MAAM,CAAC;UACpB,OAAO,EAAE;YACP0B,QAAQ,EAAEnC,OAAO,CAAC2B,KAAK,CAACH,QAAQ,CAAC;YACjCH,KAAK,EAAE,CAACY,EAAE,GAAGN,KAAK,CAACF,SAAS,KAAK,IAAI,GAAGQ,EAAE,GAAGN,KAAK,CAACN;UACrD;QACF,CAAC,EAAE,CAACS,KAAK,CAACI,OAAO,CAAC,CAAC,CAAC,CAAC;MACvB;IACF,CAAC;IACD,OAAO,YAAM;MACX,IACEd,IAAI,GAEFO,KAAK,CAFPP,IAAI;QACJE,QAAQ,GACNK,KAAK,CADPL,QAAQ;MAEV,OAAO1B,YAAY,CAAC,KAAK,EAAE;QACzB,OAAO,EAAEa,GAAG,CAAC,CAACW,IAAI,EAAE;UAClBE,QAAQ,EAARA;QACF,CAAC,CAAC,CAAC;QACH,WAAW,EAAE,QAAQ;QACrB,WAAW,EAAE;MACf,CAAC,EAAE,CAAC1B,YAAY,CAAC,MAAM,EAAE;QACvB,OAAO,EAAEa,GAAG,CAAC,SAAS,EAAEW,IAAI,CAAC;QAC7B,OAAO,EAAEW,YAAY,CAACK;MACxB,CAAC,EAAE,CAAChB,IAAI,KAAK,SAAS,GAAGV,QAAQ,GAAGO,YAAY,CAAC,CAAC,EAAEe,UAAU,CAAC,CAAC,CAAC,CAAC;IACpE,CAAC;EACH;AACF,CAAC,CAAC;AACF,SACEN,aAAa,IAAIQ,OAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}