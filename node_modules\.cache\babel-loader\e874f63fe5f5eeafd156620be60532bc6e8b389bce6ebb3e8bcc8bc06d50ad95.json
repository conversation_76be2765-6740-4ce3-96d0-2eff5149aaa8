{"ast": null, "code": "import { ref, getCurrentInstance } from 'vue';\nimport store from '@/store/index';\nimport { do_deposit, bind_bank } from '@/api/self/index.js';\nimport { useRouter } from 'vue-router';\nimport { useI18n } from 'vue-i18n';\nimport { getdetailbyid } from '@/api/home/<USER>';\nimport { Dialog } from 'vant';\nexport default {\n  name: 'HomeView',\n  setup: function setup() {\n    var _store$state$baseInfo, _store$state$userinfo, _store$state$userinfo2, _store$state$baseInfo2;\n    var _useI18n = useI18n(),\n      t = _useI18n.t;\n    var _useRouter = useRouter(),\n      push = _useRouter.push;\n    var _getCurrentInstance = getCurrentInstance(),\n      proxy = _getCurrentInstance.proxy;\n    var paypassword = ref('');\n    var info = ref({});\n    var bankInfo = ref({});\n    var currency = ref((_store$state$baseInfo = store.state.baseInfo) === null || _store$state$baseInfo === void 0 ? void 0 : _store$state$baseInfo.currency);\n    var tel = ref((_store$state$userinfo = store.state.userinfo) === null || _store$state$userinfo === void 0 ? void 0 : _store$state$userinfo.tel);\n    var infoa = ref(store.state.objInfo);\n    var withdrawType = ref('bank'); // 默认选择银行卡提现\n    var content = ref('');\n    var bankInfoExists = ref(false);\n    var usdtInfoExists = ref(false);\n\n    // 获取用户绑定的银行卡和USDT信息\n    bind_bank().then(function (res) {\n      if (res.code === 0) {\n        bankInfo.value = res.data.info || {};\n\n        // 检查用户是否绑定了银行卡 - 同时检查新旧字段名称\n        bankInfoExists.value = !!(bankInfo.value.bank_type && bankInfo.value.username || bankInfo.value.bankname && bankInfo.value.cardnum);\n\n        // 检查用户是否绑定了USDT钱包\n        usdtInfoExists.value = !!(bankInfo.value.usdt_type && bankInfo.value.usdt_diz);\n\n        // 如果只有一种提现方式可用，则默认选择该方式\n        if (bankInfoExists.value && !usdtInfoExists.value) {\n          withdrawType.value = 'bank';\n        } else if (!bankInfoExists.value && usdtInfoExists.value) {\n          withdrawType.value = 'usdt';\n        } else if (bankInfoExists.value && usdtInfoExists.value) {\n          // 如果两种方式都可用，默认选择银行卡\n          withdrawType.value = 'bank';\n        }\n      }\n    });\n    getdetailbyid(14).then(function (res) {\n      var _res$data;\n      content.value = (_res$data = res.data) === null || _res$data === void 0 ? void 0 : _res$data.content;\n    });\n    var money_check = ref();\n    var money = ref((_store$state$userinfo2 = store.state.userinfo) === null || _store$state$userinfo2 === void 0 ? void 0 : _store$state$userinfo2.balance);\n    var moneys = ref((_store$state$baseInfo2 = store.state.baseInfo) === null || _store$state$baseInfo2 === void 0 ? void 0 : _store$state$baseInfo2.recharge_money_list);\n    var clickLeft = function clickLeft() {\n      push('/self');\n    };\n    var clickRight = function clickRight() {\n      push('/tel');\n    };\n    var goToBingBank = function goToBingBank() {\n      push('/bingbank');\n    };\n    var onSubmit = function onSubmit(values) {\n      if (!bankInfoExists.value && !usdtInfoExists.value) {\n        Dialog.confirm({\n          confirmButtonText: t('msg.queren'),\n          cancelButtonText: t('msg.quxiao'),\n          title: '',\n          message: t('msg.tjtkxx')\n        }).then(function () {\n          push('/bingbank');\n        }).catch(function () {\n          // on cancel\n        });\n        return false;\n      }\n\n      // 验证提现金额\n      if (!money_check.value) {\n        proxy.$Message({\n          type: 'error',\n          message: t('msg.input_money')\n        });\n        return false;\n      }\n\n      // 检查选择的提现方式是否有效\n      if (withdrawType.value === 'bank' && !bankInfoExists.value) {\n        proxy.$Message({\n          type: 'error',\n          message: t('msg.not_put_bank')\n        });\n        return false;\n      }\n      if (withdrawType.value === 'usdt' && !usdtInfoExists.value) {\n        proxy.$Message({\n          type: 'error',\n          message: t('msg.not_put_usdt')\n        });\n        return false;\n      }\n      var json = {\n        num: money_check.value == 0 ? money.value : money_check.value,\n        type: withdrawType.value,\n        // 使用选择的提现方式\n        paypassword: values.paypassword\n      };\n\n      // 如果是USDT提现，添加USDT地址\n      if (withdrawType.value === 'usdt') {\n        json.address = bankInfo.value.usdt_diz;\n        json.USDT_code = bankInfo.value.usdt_type;\n      }\n      do_deposit(json).then(function (res) {\n        if (res.code === 0) {\n          proxy.$Message({\n            type: 'success',\n            message: res.info\n          });\n          push('/self');\n        } else {\n          proxy.$Message({\n            type: 'error',\n            message: res.info\n          });\n        }\n      });\n    };\n    return {\n      paypassword: paypassword,\n      withdrawType: withdrawType,\n      onSubmit: onSubmit,\n      clickLeft: clickLeft,\n      clickRight: clickRight,\n      info: info,\n      bankInfo: bankInfo,\n      money: money,\n      currency: currency,\n      money_check: money_check,\n      moneys: moneys,\n      content: content,\n      infoa: infoa,\n      tel: tel,\n      bankInfoExists: bankInfoExists,\n      usdtInfoExists: usdtInfoExists,\n      goToBingBank: goToBingBank\n    };\n  }\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}