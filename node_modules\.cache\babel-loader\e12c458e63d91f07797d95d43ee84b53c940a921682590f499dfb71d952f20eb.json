{"ast": null, "code": "// src/utils.ts\nvar inBrowser = typeof window !== \"undefined\";\nvar supportsPassive = true;\nfunction raf(fn) {\n  return inBrowser ? requestAnimationFrame(fn) : -1;\n}\nfunction cancelRaf(id) {\n  if (inBrowser) {\n    cancelAnimationFrame(id);\n  }\n}\nfunction doubleRaf(fn) {\n  raf(function () {\n    return raf(fn);\n  });\n}\n\n// src/useRect/index.ts\nimport { unref } from \"vue\";\nvar isWindow = function isWindow(val) {\n  return val === window;\n};\nvar makeDOMRect = function makeDOMRect(width2, height2) {\n  return {\n    top: 0,\n    left: 0,\n    right: width2,\n    bottom: height2,\n    width: width2,\n    height: height2\n  };\n};\nvar useRect = function useRect(elementOrRef) {\n  var element = unref(elementOrRef);\n  if (isWindow(element)) {\n    var width2 = element.innerWidth;\n    var height2 = element.innerHeight;\n    return makeDOMRect(width2, height2);\n  }\n  if (element == null ? void 0 : element.getBoundingClientRect) {\n    return element.getBoundingClientRect();\n  }\n  return makeDOMRect(0, 0);\n};\n\n// src/useToggle/index.ts\nimport { ref } from \"vue\";\nfunction useToggle() {\n  var defaultValue = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;\n  var state = ref(defaultValue);\n  var toggle = function toggle() {\n    var value = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : !state.value;\n    state.value = value;\n  };\n  return [state, toggle];\n}\n\n// src/useRelation/useParent.ts\nimport { ref as ref2, inject, computed, onUnmounted, getCurrentInstance } from \"vue\";\nfunction useParent(key) {\n  var parent = inject(key, null);\n  if (parent) {\n    var instance = getCurrentInstance();\n    var link = parent.link,\n      unlink = parent.unlink,\n      internalChildren = parent.internalChildren;\n    link(instance);\n    onUnmounted(function () {\n      return unlink(instance);\n    });\n    var index = computed(function () {\n      return internalChildren.indexOf(instance);\n    });\n    return {\n      parent: parent,\n      index: index\n    };\n  }\n  return {\n    parent: null,\n    index: ref2(-1)\n  };\n}\n\n// src/useRelation/useChildren.ts\nimport { isVNode, provide, reactive, getCurrentInstance as getCurrentInstance2 } from \"vue\";\nfunction flattenVNodes(children) {\n  var result = [];\n  var traverse = function traverse(children2) {\n    if (Array.isArray(children2)) {\n      children2.forEach(function (child) {\n        var _a;\n        if (isVNode(child)) {\n          result.push(child);\n          if ((_a = child.component) == null ? void 0 : _a.subTree) {\n            result.push(child.component.subTree);\n            traverse(child.component.subTree.children);\n          }\n          if (child.children) {\n            traverse(child.children);\n          }\n        }\n      });\n    }\n  };\n  traverse(children);\n  return result;\n}\nvar findVNodeIndex = function findVNodeIndex(vnodes, vnode) {\n  var index = vnodes.indexOf(vnode);\n  if (index === -1) {\n    return vnodes.findIndex(function (item) {\n      return vnode.key !== void 0 && vnode.key !== null && item.type === vnode.type && item.key === vnode.key;\n    });\n  }\n  return index;\n};\nfunction sortChildren(parent, publicChildren, internalChildren) {\n  var vnodes = flattenVNodes(parent.subTree.children);\n  internalChildren.sort(function (a, b) {\n    return findVNodeIndex(vnodes, a.vnode) - findVNodeIndex(vnodes, b.vnode);\n  });\n  var orderedPublicChildren = internalChildren.map(function (item) {\n    return item.proxy;\n  });\n  publicChildren.sort(function (a, b) {\n    var indexA = orderedPublicChildren.indexOf(a);\n    var indexB = orderedPublicChildren.indexOf(b);\n    return indexA - indexB;\n  });\n}\nfunction useChildren(key) {\n  var publicChildren = reactive([]);\n  var internalChildren = reactive([]);\n  var parent = getCurrentInstance2();\n  var linkChildren = function linkChildren(value) {\n    var link = function link(child) {\n      if (child.proxy) {\n        internalChildren.push(child);\n        publicChildren.push(child.proxy);\n        sortChildren(parent, publicChildren, internalChildren);\n      }\n    };\n    var unlink = function unlink(child) {\n      var index = internalChildren.indexOf(child);\n      publicChildren.splice(index, 1);\n      internalChildren.splice(index, 1);\n    };\n    provide(key, Object.assign({\n      link: link,\n      unlink: unlink,\n      children: publicChildren,\n      internalChildren: internalChildren\n    }, value));\n  };\n  return {\n    children: publicChildren,\n    linkChildren: linkChildren\n  };\n}\n\n// src/useCountDown/index.ts\nimport { ref as ref3, computed as computed2, onActivated, onDeactivated, onBeforeUnmount } from \"vue\";\nvar SECOND = 1e3;\nvar MINUTE = 60 * SECOND;\nvar HOUR = 60 * MINUTE;\nvar DAY = 24 * HOUR;\nfunction parseTime(time) {\n  var days = Math.floor(time / DAY);\n  var hours = Math.floor(time % DAY / HOUR);\n  var minutes = Math.floor(time % HOUR / MINUTE);\n  var seconds = Math.floor(time % MINUTE / SECOND);\n  var milliseconds = Math.floor(time % SECOND);\n  return {\n    total: time,\n    days: days,\n    hours: hours,\n    minutes: minutes,\n    seconds: seconds,\n    milliseconds: milliseconds\n  };\n}\nfunction isSameSecond(time1, time2) {\n  return Math.floor(time1 / 1e3) === Math.floor(time2 / 1e3);\n}\nfunction useCountDown(options) {\n  var rafId;\n  var endTime;\n  var counting;\n  var deactivated;\n  var remain = ref3(options.time);\n  var current = computed2(function () {\n    return parseTime(remain.value);\n  });\n  var pause = function pause() {\n    counting = false;\n    cancelRaf(rafId);\n  };\n  var getCurrentRemain = function getCurrentRemain() {\n    return Math.max(endTime - Date.now(), 0);\n  };\n  var setRemain = function setRemain(value) {\n    var _a, _b;\n    remain.value = value;\n    (_a = options.onChange) == null ? void 0 : _a.call(options, current.value);\n    if (value === 0) {\n      pause();\n      (_b = options.onFinish) == null ? void 0 : _b.call(options);\n    }\n  };\n  var microTick = function microTick() {\n    rafId = raf(function () {\n      if (counting) {\n        setRemain(getCurrentRemain());\n        if (remain.value > 0) {\n          microTick();\n        }\n      }\n    });\n  };\n  var macroTick = function macroTick() {\n    rafId = raf(function () {\n      if (counting) {\n        var remainRemain = getCurrentRemain();\n        if (!isSameSecond(remainRemain, remain.value) || remainRemain === 0) {\n          setRemain(remainRemain);\n        }\n        if (remain.value > 0) {\n          macroTick();\n        }\n      }\n    });\n  };\n  var tick = function tick() {\n    if (!inBrowser) {\n      return;\n    }\n    if (options.millisecond) {\n      microTick();\n    } else {\n      macroTick();\n    }\n  };\n  var start = function start() {\n    if (!counting) {\n      endTime = Date.now() + remain.value;\n      counting = true;\n      tick();\n    }\n  };\n  var reset = function reset() {\n    var totalTime = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : options.time;\n    pause();\n    remain.value = totalTime;\n  };\n  onBeforeUnmount(pause);\n  onActivated(function () {\n    if (deactivated) {\n      counting = true;\n      deactivated = false;\n      tick();\n    }\n  });\n  onDeactivated(function () {\n    if (counting) {\n      pause();\n      deactivated = true;\n    }\n  });\n  return {\n    start: start,\n    pause: pause,\n    reset: reset,\n    current: current\n  };\n}\n\n// src/useClickAway/index.ts\nimport { unref as unref3 } from \"vue\";\n\n// src/useEventListener/index.ts\nimport { watch, isRef, unref as unref2, onUnmounted as onUnmounted2, onDeactivated as onDeactivated2 } from \"vue\";\n\n// src/onMountedOrActivated/index.ts\nimport { nextTick, onMounted, onActivated as onActivated2 } from \"vue\";\nfunction onMountedOrActivated(hook) {\n  var mounted;\n  onMounted(function () {\n    hook();\n    nextTick(function () {\n      mounted = true;\n    });\n  });\n  onActivated2(function () {\n    if (mounted) {\n      hook();\n    }\n  });\n}\n\n// src/useEventListener/index.ts\nfunction useEventListener(type, listener) {\n  var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n  if (!inBrowser) {\n    return;\n  }\n  var _options$target = options.target,\n    target = _options$target === void 0 ? window : _options$target,\n    _options$passive = options.passive,\n    passive = _options$passive === void 0 ? false : _options$passive,\n    _options$capture = options.capture,\n    capture = _options$capture === void 0 ? false : _options$capture;\n  var cleaned = false;\n  var attached;\n  var add = function add(target2) {\n    if (cleaned) {\n      return;\n    }\n    var element = unref2(target2);\n    if (element && !attached) {\n      element.addEventListener(type, listener, {\n        capture: capture,\n        passive: passive\n      });\n      attached = true;\n    }\n  };\n  var remove = function remove(target2) {\n    if (cleaned) {\n      return;\n    }\n    var element = unref2(target2);\n    if (element && attached) {\n      element.removeEventListener(type, listener, capture);\n      attached = false;\n    }\n  };\n  onUnmounted2(function () {\n    return remove(target);\n  });\n  onDeactivated2(function () {\n    return remove(target);\n  });\n  onMountedOrActivated(function () {\n    return add(target);\n  });\n  var stopWatch;\n  if (isRef(target)) {\n    stopWatch = watch(target, function (val, oldVal) {\n      remove(oldVal);\n      add(val);\n    });\n  }\n  return function () {\n    stopWatch == null ? void 0 : stopWatch();\n    remove(target);\n    cleaned = true;\n  };\n}\n\n// src/useClickAway/index.ts\nfunction useClickAway(target, listener) {\n  var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n  if (!inBrowser) {\n    return;\n  }\n  var _options$eventName = options.eventName,\n    eventName = _options$eventName === void 0 ? \"click\" : _options$eventName;\n  var onClick = function onClick(event) {\n    var targets = Array.isArray(target) ? target : [target];\n    var isClickAway = targets.every(function (item) {\n      var element = unref3(item);\n      return element && !element.contains(event.target);\n    });\n    if (isClickAway) {\n      listener(event);\n    }\n  };\n  useEventListener(eventName, onClick, {\n    target: document\n  });\n}\n\n// src/useWindowSize/index.ts\nimport { ref as ref4 } from \"vue\";\nvar width;\nvar height;\nfunction useWindowSize() {\n  if (!width) {\n    width = ref4(0);\n    height = ref4(0);\n    if (inBrowser) {\n      var update = function update() {\n        width.value = window.innerWidth;\n        height.value = window.innerHeight;\n      };\n      update();\n      window.addEventListener(\"resize\", update, {\n        passive: true\n      });\n      window.addEventListener(\"orientationchange\", update, {\n        passive: true\n      });\n    }\n  }\n  return {\n    width: width,\n    height: height\n  };\n}\n\n// src/useScrollParent/index.ts\nimport { ref as ref5, onMounted as onMounted2 } from \"vue\";\nvar overflowScrollReg = /scroll|auto|overlay/i;\nvar defaultRoot = inBrowser ? window : void 0;\nfunction isElement(node) {\n  var ELEMENT_NODE_TYPE = 1;\n  return node.tagName !== \"HTML\" && node.tagName !== \"BODY\" && node.nodeType === ELEMENT_NODE_TYPE;\n}\nfunction getScrollParent(el) {\n  var root = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : defaultRoot;\n  var node = el;\n  while (node && node !== root && isElement(node)) {\n    var _window$getComputedSt = window.getComputedStyle(node),\n      overflowY = _window$getComputedSt.overflowY;\n    if (overflowScrollReg.test(overflowY)) {\n      return node;\n    }\n    node = node.parentNode;\n  }\n  return root;\n}\nfunction useScrollParent(el) {\n  var root = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : defaultRoot;\n  var scrollParent = ref5();\n  onMounted2(function () {\n    if (el.value) {\n      scrollParent.value = getScrollParent(el.value, root);\n    }\n  });\n  return scrollParent;\n}\n\n// src/usePageVisibility/index.ts\nimport { ref as ref6 } from \"vue\";\nvar visibility;\nfunction usePageVisibility() {\n  if (!visibility) {\n    visibility = ref6(\"visible\");\n    if (inBrowser) {\n      var update = function update() {\n        visibility.value = document.hidden ? \"hidden\" : \"visible\";\n      };\n      update();\n      window.addEventListener(\"visibilitychange\", update);\n    }\n  }\n  return visibility;\n}\n\n// src/useCustomFieldValue/index.ts\nimport { watch as watch2, inject as inject2 } from \"vue\";\nvar CUSTOM_FIELD_INJECTION_KEY = Symbol(\"van-field\");\nfunction useCustomFieldValue(customValue) {\n  var field = inject2(CUSTOM_FIELD_INJECTION_KEY, null);\n  if (field && !field.customValue.value) {\n    field.customValue.value = customValue;\n    watch2(customValue, function () {\n      field.resetValidation();\n      field.validateWithTrigger(\"onChange\");\n    });\n  }\n}\nexport { CUSTOM_FIELD_INJECTION_KEY, cancelRaf, doubleRaf, flattenVNodes, getScrollParent, inBrowser, onMountedOrActivated, raf, sortChildren, supportsPassive, useChildren, useClickAway, useCountDown, useCustomFieldValue, useEventListener, usePageVisibility, useParent, useRect, useScrollParent, useToggle, useWindowSize };", "map": {"version": 3, "names": ["inBrowser", "window", "supportsPassive", "raf", "fn", "requestAnimationFrame", "cancelRaf", "id", "cancelAnimationFrame", "doubleRaf", "unref", "isWindow", "val", "makeDOMRect", "width2", "height2", "top", "left", "right", "bottom", "width", "height", "useRect", "elementOrRef", "element", "innerWidth", "innerHeight", "getBoundingClientRect", "ref", "useToggle", "defaultValue", "arguments", "length", "undefined", "state", "toggle", "value", "ref2", "inject", "computed", "onUnmounted", "getCurrentInstance", "useParent", "key", "parent", "instance", "link", "unlink", "internalChildren", "index", "indexOf", "isVNode", "provide", "reactive", "getCurrentInstance2", "flattenVNodes", "children", "result", "traverse", "children2", "Array", "isArray", "for<PERSON>ach", "child", "_a", "push", "component", "subTree", "findVNodeIndex", "vnodes", "vnode", "findIndex", "item", "type", "sort<PERSON><PERSON><PERSON><PERSON>", "publicC<PERSON><PERSON>n", "sort", "a", "b", "orderedPublicChildren", "map", "proxy", "indexA", "indexB", "useChildren", "linkChildren", "splice", "Object", "assign", "ref3", "computed2", "onActivated", "onDeactivated", "onBeforeUnmount", "SECOND", "MINUTE", "HOUR", "DAY", "parseTime", "time", "days", "Math", "floor", "hours", "minutes", "seconds", "milliseconds", "total", "isSameSecond", "time1", "time2", "useCountDown", "options", "rafId", "endTime", "counting", "deactivated", "remain", "current", "pause", "getCurrentRemain", "max", "Date", "now", "setRemain", "_b", "onChange", "call", "onFinish", "microTick", "macroTick", "remainRemain", "tick", "millisecond", "start", "reset", "totalTime", "unref3", "watch", "isRef", "unref2", "onUnmounted2", "onDeactivated2", "nextTick", "onMounted", "onActivated2", "onMountedOrActivated", "hook", "mounted", "useEventListener", "listener", "_options$target", "target", "_options$passive", "passive", "_options$capture", "capture", "cleaned", "attached", "add", "target2", "addEventListener", "remove", "removeEventListener", "stopWatch", "oldVal", "useClickAway", "_options$eventName", "eventName", "onClick", "event", "targets", "isClickAway", "every", "contains", "document", "ref4", "useWindowSize", "update", "ref5", "onMounted2", "overflowScrollReg", "defaultRoot", "isElement", "node", "ELEMENT_NODE_TYPE", "tagName", "nodeType", "getScrollParent", "el", "root", "_window$getComputedSt", "getComputedStyle", "overflowY", "test", "parentNode", "useScrollParent", "scrollParent", "ref6", "visibility", "usePageVisibility", "hidden", "watch2", "inject2", "CUSTOM_FIELD_INJECTION_KEY", "Symbol", "useCustomFieldValue", "customValue", "field", "resetValidation", "validateWithTrigger"], "sources": ["C:/Users/<USER>/Desktop/vue3_3.0/node_modules/@vant/use/dist/index.esm.mjs"], "sourcesContent": ["// src/utils.ts\nvar inBrowser = typeof window !== \"undefined\";\nvar supportsPassive = true;\nfunction raf(fn) {\n  return inBrowser ? requestAnimationFrame(fn) : -1;\n}\nfunction cancelRaf(id) {\n  if (inBrowser) {\n    cancelAnimationFrame(id);\n  }\n}\nfunction doubleRaf(fn) {\n  raf(() => raf(fn));\n}\n\n// src/useRect/index.ts\nimport { unref } from \"vue\";\nvar isWindow = (val) => val === window;\nvar makeDOMRect = (width2, height2) => ({\n  top: 0,\n  left: 0,\n  right: width2,\n  bottom: height2,\n  width: width2,\n  height: height2\n});\nvar useRect = (elementOrRef) => {\n  const element = unref(elementOrRef);\n  if (isWindow(element)) {\n    const width2 = element.innerWidth;\n    const height2 = element.innerHeight;\n    return makeDOMRect(width2, height2);\n  }\n  if (element == null ? void 0 : element.getBoundingClientRect) {\n    return element.getBoundingClientRect();\n  }\n  return makeDOMRect(0, 0);\n};\n\n// src/useToggle/index.ts\nimport { ref } from \"vue\";\nfunction useToggle(defaultValue = false) {\n  const state = ref(defaultValue);\n  const toggle = (value = !state.value) => {\n    state.value = value;\n  };\n  return [state, toggle];\n}\n\n// src/useRelation/useParent.ts\nimport {\n  ref as ref2,\n  inject,\n  computed,\n  onUnmounted,\n  getCurrentInstance\n} from \"vue\";\nfunction useParent(key) {\n  const parent = inject(key, null);\n  if (parent) {\n    const instance = getCurrentInstance();\n    const { link, unlink, internalChildren } = parent;\n    link(instance);\n    onUnmounted(() => unlink(instance));\n    const index = computed(() => internalChildren.indexOf(instance));\n    return {\n      parent,\n      index\n    };\n  }\n  return {\n    parent: null,\n    index: ref2(-1)\n  };\n}\n\n// src/useRelation/useChildren.ts\nimport {\n  isVNode,\n  provide,\n  reactive,\n  getCurrentInstance as getCurrentInstance2\n} from \"vue\";\nfunction flattenVNodes(children) {\n  const result = [];\n  const traverse = (children2) => {\n    if (Array.isArray(children2)) {\n      children2.forEach((child) => {\n        var _a;\n        if (isVNode(child)) {\n          result.push(child);\n          if ((_a = child.component) == null ? void 0 : _a.subTree) {\n            result.push(child.component.subTree);\n            traverse(child.component.subTree.children);\n          }\n          if (child.children) {\n            traverse(child.children);\n          }\n        }\n      });\n    }\n  };\n  traverse(children);\n  return result;\n}\nvar findVNodeIndex = (vnodes, vnode) => {\n  const index = vnodes.indexOf(vnode);\n  if (index === -1) {\n    return vnodes.findIndex(\n      (item) => vnode.key !== void 0 && vnode.key !== null && item.type === vnode.type && item.key === vnode.key\n    );\n  }\n  return index;\n};\nfunction sortChildren(parent, publicChildren, internalChildren) {\n  const vnodes = flattenVNodes(parent.subTree.children);\n  internalChildren.sort(\n    (a, b) => findVNodeIndex(vnodes, a.vnode) - findVNodeIndex(vnodes, b.vnode)\n  );\n  const orderedPublicChildren = internalChildren.map((item) => item.proxy);\n  publicChildren.sort((a, b) => {\n    const indexA = orderedPublicChildren.indexOf(a);\n    const indexB = orderedPublicChildren.indexOf(b);\n    return indexA - indexB;\n  });\n}\nfunction useChildren(key) {\n  const publicChildren = reactive([]);\n  const internalChildren = reactive([]);\n  const parent = getCurrentInstance2();\n  const linkChildren = (value) => {\n    const link = (child) => {\n      if (child.proxy) {\n        internalChildren.push(child);\n        publicChildren.push(child.proxy);\n        sortChildren(parent, publicChildren, internalChildren);\n      }\n    };\n    const unlink = (child) => {\n      const index = internalChildren.indexOf(child);\n      publicChildren.splice(index, 1);\n      internalChildren.splice(index, 1);\n    };\n    provide(\n      key,\n      Object.assign(\n        {\n          link,\n          unlink,\n          children: publicChildren,\n          internalChildren\n        },\n        value\n      )\n    );\n  };\n  return {\n    children: publicChildren,\n    linkChildren\n  };\n}\n\n// src/useCountDown/index.ts\nimport {\n  ref as ref3,\n  computed as computed2,\n  onActivated,\n  onDeactivated,\n  onBeforeUnmount\n} from \"vue\";\nvar SECOND = 1e3;\nvar MINUTE = 60 * SECOND;\nvar HOUR = 60 * MINUTE;\nvar DAY = 24 * HOUR;\nfunction parseTime(time) {\n  const days = Math.floor(time / DAY);\n  const hours = Math.floor(time % DAY / HOUR);\n  const minutes = Math.floor(time % HOUR / MINUTE);\n  const seconds = Math.floor(time % MINUTE / SECOND);\n  const milliseconds = Math.floor(time % SECOND);\n  return {\n    total: time,\n    days,\n    hours,\n    minutes,\n    seconds,\n    milliseconds\n  };\n}\nfunction isSameSecond(time1, time2) {\n  return Math.floor(time1 / 1e3) === Math.floor(time2 / 1e3);\n}\nfunction useCountDown(options) {\n  let rafId;\n  let endTime;\n  let counting;\n  let deactivated;\n  const remain = ref3(options.time);\n  const current = computed2(() => parseTime(remain.value));\n  const pause = () => {\n    counting = false;\n    cancelRaf(rafId);\n  };\n  const getCurrentRemain = () => Math.max(endTime - Date.now(), 0);\n  const setRemain = (value) => {\n    var _a, _b;\n    remain.value = value;\n    (_a = options.onChange) == null ? void 0 : _a.call(options, current.value);\n    if (value === 0) {\n      pause();\n      (_b = options.onFinish) == null ? void 0 : _b.call(options);\n    }\n  };\n  const microTick = () => {\n    rafId = raf(() => {\n      if (counting) {\n        setRemain(getCurrentRemain());\n        if (remain.value > 0) {\n          microTick();\n        }\n      }\n    });\n  };\n  const macroTick = () => {\n    rafId = raf(() => {\n      if (counting) {\n        const remainRemain = getCurrentRemain();\n        if (!isSameSecond(remainRemain, remain.value) || remainRemain === 0) {\n          setRemain(remainRemain);\n        }\n        if (remain.value > 0) {\n          macroTick();\n        }\n      }\n    });\n  };\n  const tick = () => {\n    if (!inBrowser) {\n      return;\n    }\n    if (options.millisecond) {\n      microTick();\n    } else {\n      macroTick();\n    }\n  };\n  const start = () => {\n    if (!counting) {\n      endTime = Date.now() + remain.value;\n      counting = true;\n      tick();\n    }\n  };\n  const reset = (totalTime = options.time) => {\n    pause();\n    remain.value = totalTime;\n  };\n  onBeforeUnmount(pause);\n  onActivated(() => {\n    if (deactivated) {\n      counting = true;\n      deactivated = false;\n      tick();\n    }\n  });\n  onDeactivated(() => {\n    if (counting) {\n      pause();\n      deactivated = true;\n    }\n  });\n  return {\n    start,\n    pause,\n    reset,\n    current\n  };\n}\n\n// src/useClickAway/index.ts\nimport { unref as unref3 } from \"vue\";\n\n// src/useEventListener/index.ts\nimport {\n  watch,\n  isRef,\n  unref as unref2,\n  onUnmounted as onUnmounted2,\n  onDeactivated as onDeactivated2\n} from \"vue\";\n\n// src/onMountedOrActivated/index.ts\nimport { nextTick, onMounted, onActivated as onActivated2 } from \"vue\";\nfunction onMountedOrActivated(hook) {\n  let mounted;\n  onMounted(() => {\n    hook();\n    nextTick(() => {\n      mounted = true;\n    });\n  });\n  onActivated2(() => {\n    if (mounted) {\n      hook();\n    }\n  });\n}\n\n// src/useEventListener/index.ts\nfunction useEventListener(type, listener, options = {}) {\n  if (!inBrowser) {\n    return;\n  }\n  const { target = window, passive = false, capture = false } = options;\n  let cleaned = false;\n  let attached;\n  const add = (target2) => {\n    if (cleaned) {\n      return;\n    }\n    const element = unref2(target2);\n    if (element && !attached) {\n      element.addEventListener(type, listener, {\n        capture,\n        passive\n      });\n      attached = true;\n    }\n  };\n  const remove = (target2) => {\n    if (cleaned) {\n      return;\n    }\n    const element = unref2(target2);\n    if (element && attached) {\n      element.removeEventListener(type, listener, capture);\n      attached = false;\n    }\n  };\n  onUnmounted2(() => remove(target));\n  onDeactivated2(() => remove(target));\n  onMountedOrActivated(() => add(target));\n  let stopWatch;\n  if (isRef(target)) {\n    stopWatch = watch(target, (val, oldVal) => {\n      remove(oldVal);\n      add(val);\n    });\n  }\n  return () => {\n    stopWatch == null ? void 0 : stopWatch();\n    remove(target);\n    cleaned = true;\n  };\n}\n\n// src/useClickAway/index.ts\nfunction useClickAway(target, listener, options = {}) {\n  if (!inBrowser) {\n    return;\n  }\n  const { eventName = \"click\" } = options;\n  const onClick = (event) => {\n    const targets = Array.isArray(target) ? target : [target];\n    const isClickAway = targets.every((item) => {\n      const element = unref3(item);\n      return element && !element.contains(event.target);\n    });\n    if (isClickAway) {\n      listener(event);\n    }\n  };\n  useEventListener(eventName, onClick, { target: document });\n}\n\n// src/useWindowSize/index.ts\nimport { ref as ref4 } from \"vue\";\nvar width;\nvar height;\nfunction useWindowSize() {\n  if (!width) {\n    width = ref4(0);\n    height = ref4(0);\n    if (inBrowser) {\n      const update = () => {\n        width.value = window.innerWidth;\n        height.value = window.innerHeight;\n      };\n      update();\n      window.addEventListener(\"resize\", update, { passive: true });\n      window.addEventListener(\"orientationchange\", update, { passive: true });\n    }\n  }\n  return { width, height };\n}\n\n// src/useScrollParent/index.ts\nimport { ref as ref5, onMounted as onMounted2 } from \"vue\";\nvar overflowScrollReg = /scroll|auto|overlay/i;\nvar defaultRoot = inBrowser ? window : void 0;\nfunction isElement(node) {\n  const ELEMENT_NODE_TYPE = 1;\n  return node.tagName !== \"HTML\" && node.tagName !== \"BODY\" && node.nodeType === ELEMENT_NODE_TYPE;\n}\nfunction getScrollParent(el, root = defaultRoot) {\n  let node = el;\n  while (node && node !== root && isElement(node)) {\n    const { overflowY } = window.getComputedStyle(node);\n    if (overflowScrollReg.test(overflowY)) {\n      return node;\n    }\n    node = node.parentNode;\n  }\n  return root;\n}\nfunction useScrollParent(el, root = defaultRoot) {\n  const scrollParent = ref5();\n  onMounted2(() => {\n    if (el.value) {\n      scrollParent.value = getScrollParent(el.value, root);\n    }\n  });\n  return scrollParent;\n}\n\n// src/usePageVisibility/index.ts\nimport { ref as ref6 } from \"vue\";\nvar visibility;\nfunction usePageVisibility() {\n  if (!visibility) {\n    visibility = ref6(\"visible\");\n    if (inBrowser) {\n      const update = () => {\n        visibility.value = document.hidden ? \"hidden\" : \"visible\";\n      };\n      update();\n      window.addEventListener(\"visibilitychange\", update);\n    }\n  }\n  return visibility;\n}\n\n// src/useCustomFieldValue/index.ts\nimport { watch as watch2, inject as inject2 } from \"vue\";\nvar CUSTOM_FIELD_INJECTION_KEY = Symbol(\"van-field\");\nfunction useCustomFieldValue(customValue) {\n  const field = inject2(CUSTOM_FIELD_INJECTION_KEY, null);\n  if (field && !field.customValue.value) {\n    field.customValue.value = customValue;\n    watch2(customValue, () => {\n      field.resetValidation();\n      field.validateWithTrigger(\"onChange\");\n    });\n  }\n}\nexport {\n  CUSTOM_FIELD_INJECTION_KEY,\n  cancelRaf,\n  doubleRaf,\n  flattenVNodes,\n  getScrollParent,\n  inBrowser,\n  onMountedOrActivated,\n  raf,\n  sortChildren,\n  supportsPassive,\n  useChildren,\n  useClickAway,\n  useCountDown,\n  useCustomFieldValue,\n  useEventListener,\n  usePageVisibility,\n  useParent,\n  useRect,\n  useScrollParent,\n  useToggle,\n  useWindowSize\n};\n"], "mappings": "AAAA;AACA,IAAIA,SAAS,GAAG,OAAOC,MAAM,KAAK,WAAW;AAC7C,IAAIC,eAAe,GAAG,IAAI;AAC1B,SAASC,GAAGA,CAACC,EAAE,EAAE;EACf,OAAOJ,SAAS,GAAGK,qBAAqB,CAACD,EAAE,CAAC,GAAG,CAAC,CAAC;AACnD;AACA,SAASE,SAASA,CAACC,EAAE,EAAE;EACrB,IAAIP,SAAS,EAAE;IACbQ,oBAAoB,CAACD,EAAE,CAAC;EAC1B;AACF;AACA,SAASE,SAASA,CAACL,EAAE,EAAE;EACrBD,GAAG,CAAC;IAAA,OAAMA,GAAG,CAACC,EAAE,CAAC;EAAA,EAAC;AACpB;;AAEA;AACA,SAASM,KAAK,QAAQ,KAAK;AAC3B,IAAIC,QAAQ,GAAG,SAAXA,QAAQA,CAAIC,GAAG;EAAA,OAAKA,GAAG,KAAKX,MAAM;AAAA;AACtC,IAAIY,WAAW,GAAG,SAAdA,WAAWA,CAAIC,MAAM,EAAEC,OAAO;EAAA,OAAM;IACtCC,GAAG,EAAE,CAAC;IACNC,IAAI,EAAE,CAAC;IACPC,KAAK,EAAEJ,MAAM;IACbK,MAAM,EAAEJ,OAAO;IACfK,KAAK,EAAEN,MAAM;IACbO,MAAM,EAAEN;EACV,CAAC;AAAA,CAAC;AACF,IAAIO,OAAO,GAAG,SAAVA,OAAOA,CAAIC,YAAY,EAAK;EAC9B,IAAMC,OAAO,GAAGd,KAAK,CAACa,YAAY,CAAC;EACnC,IAAIZ,QAAQ,CAACa,OAAO,CAAC,EAAE;IACrB,IAAMV,MAAM,GAAGU,OAAO,CAACC,UAAU;IACjC,IAAMV,OAAO,GAAGS,OAAO,CAACE,WAAW;IACnC,OAAOb,WAAW,CAACC,MAAM,EAAEC,OAAO,CAAC;EACrC;EACA,IAAIS,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACG,qBAAqB,EAAE;IAC5D,OAAOH,OAAO,CAACG,qBAAqB,CAAC,CAAC;EACxC;EACA,OAAOd,WAAW,CAAC,CAAC,EAAE,CAAC,CAAC;AAC1B,CAAC;;AAED;AACA,SAASe,GAAG,QAAQ,KAAK;AACzB,SAASC,SAASA,CAAA,EAAuB;EAAA,IAAtBC,YAAY,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;EACrC,IAAMG,KAAK,GAAGN,GAAG,CAACE,YAAY,CAAC;EAC/B,IAAMK,MAAM,GAAG,SAATA,MAAMA,CAAA,EAA6B;IAAA,IAAzBC,KAAK,GAAAL,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAACG,KAAK,CAACE,KAAK;IAClCF,KAAK,CAACE,KAAK,GAAGA,KAAK;EACrB,CAAC;EACD,OAAO,CAACF,KAAK,EAAEC,MAAM,CAAC;AACxB;;AAEA;AACA,SACEP,GAAG,IAAIS,IAAI,EACXC,MAAM,EACNC,QAAQ,EACRC,WAAW,EACXC,kBAAkB,QACb,KAAK;AACZ,SAASC,SAASA,CAACC,GAAG,EAAE;EACtB,IAAMC,MAAM,GAAGN,MAAM,CAACK,GAAG,EAAE,IAAI,CAAC;EAChC,IAAIC,MAAM,EAAE;IACV,IAAMC,QAAQ,GAAGJ,kBAAkB,CAAC,CAAC;IACrC,IAAQK,IAAI,GAA+BF,MAAM,CAAzCE,IAAI;MAAEC,MAAM,GAAuBH,MAAM,CAAnCG,MAAM;MAAEC,gBAAgB,GAAKJ,MAAM,CAA3BI,gBAAgB;IACtCF,IAAI,CAACD,QAAQ,CAAC;IACdL,WAAW,CAAC;MAAA,OAAMO,MAAM,CAACF,QAAQ,CAAC;IAAA,EAAC;IACnC,IAAMI,KAAK,GAAGV,QAAQ,CAAC;MAAA,OAAMS,gBAAgB,CAACE,OAAO,CAACL,QAAQ,CAAC;IAAA,EAAC;IAChE,OAAO;MACLD,MAAM,EAANA,MAAM;MACNK,KAAK,EAALA;IACF,CAAC;EACH;EACA,OAAO;IACLL,MAAM,EAAE,IAAI;IACZK,KAAK,EAAEZ,IAAI,CAAC,CAAC,CAAC;EAChB,CAAC;AACH;;AAEA;AACA,SACEc,OAAO,EACPC,OAAO,EACPC,QAAQ,EACRZ,kBAAkB,IAAIa,mBAAmB,QACpC,KAAK;AACZ,SAASC,aAAaA,CAACC,QAAQ,EAAE;EAC/B,IAAMC,MAAM,GAAG,EAAE;EACjB,IAAMC,QAAQ,GAAG,SAAXA,QAAQA,CAAIC,SAAS,EAAK;IAC9B,IAAIC,KAAK,CAACC,OAAO,CAACF,SAAS,CAAC,EAAE;MAC5BA,SAAS,CAACG,OAAO,CAAC,UAACC,KAAK,EAAK;QAC3B,IAAIC,EAAE;QACN,IAAIb,OAAO,CAACY,KAAK,CAAC,EAAE;UAClBN,MAAM,CAACQ,IAAI,CAACF,KAAK,CAAC;UAClB,IAAI,CAACC,EAAE,GAAGD,KAAK,CAACG,SAAS,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGF,EAAE,CAACG,OAAO,EAAE;YACxDV,MAAM,CAACQ,IAAI,CAACF,KAAK,CAACG,SAAS,CAACC,OAAO,CAAC;YACpCT,QAAQ,CAACK,KAAK,CAACG,SAAS,CAACC,OAAO,CAACX,QAAQ,CAAC;UAC5C;UACA,IAAIO,KAAK,CAACP,QAAQ,EAAE;YAClBE,QAAQ,CAACK,KAAK,CAACP,QAAQ,CAAC;UAC1B;QACF;MACF,CAAC,CAAC;IACJ;EACF,CAAC;EACDE,QAAQ,CAACF,QAAQ,CAAC;EAClB,OAAOC,MAAM;AACf;AACA,IAAIW,cAAc,GAAG,SAAjBA,cAAcA,CAAIC,MAAM,EAAEC,KAAK,EAAK;EACtC,IAAMrB,KAAK,GAAGoB,MAAM,CAACnB,OAAO,CAACoB,KAAK,CAAC;EACnC,IAAIrB,KAAK,KAAK,CAAC,CAAC,EAAE;IAChB,OAAOoB,MAAM,CAACE,SAAS,CACrB,UAACC,IAAI;MAAA,OAAKF,KAAK,CAAC3B,GAAG,KAAK,KAAK,CAAC,IAAI2B,KAAK,CAAC3B,GAAG,KAAK,IAAI,IAAI6B,IAAI,CAACC,IAAI,KAAKH,KAAK,CAACG,IAAI,IAAID,IAAI,CAAC7B,GAAG,KAAK2B,KAAK,CAAC3B,GAAG;IAAA,CAC5G,CAAC;EACH;EACA,OAAOM,KAAK;AACd,CAAC;AACD,SAASyB,YAAYA,CAAC9B,MAAM,EAAE+B,cAAc,EAAE3B,gBAAgB,EAAE;EAC9D,IAAMqB,MAAM,GAAGd,aAAa,CAACX,MAAM,CAACuB,OAAO,CAACX,QAAQ,CAAC;EACrDR,gBAAgB,CAAC4B,IAAI,CACnB,UAACC,CAAC,EAAEC,CAAC;IAAA,OAAKV,cAAc,CAACC,MAAM,EAAEQ,CAAC,CAACP,KAAK,CAAC,GAAGF,cAAc,CAACC,MAAM,EAAES,CAAC,CAACR,KAAK,CAAC;EAAA,CAC7E,CAAC;EACD,IAAMS,qBAAqB,GAAG/B,gBAAgB,CAACgC,GAAG,CAAC,UAACR,IAAI;IAAA,OAAKA,IAAI,CAACS,KAAK;EAAA,EAAC;EACxEN,cAAc,CAACC,IAAI,CAAC,UAACC,CAAC,EAAEC,CAAC,EAAK;IAC5B,IAAMI,MAAM,GAAGH,qBAAqB,CAAC7B,OAAO,CAAC2B,CAAC,CAAC;IAC/C,IAAMM,MAAM,GAAGJ,qBAAqB,CAAC7B,OAAO,CAAC4B,CAAC,CAAC;IAC/C,OAAOI,MAAM,GAAGC,MAAM;EACxB,CAAC,CAAC;AACJ;AACA,SAASC,WAAWA,CAACzC,GAAG,EAAE;EACxB,IAAMgC,cAAc,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EACnC,IAAML,gBAAgB,GAAGK,QAAQ,CAAC,EAAE,CAAC;EACrC,IAAMT,MAAM,GAAGU,mBAAmB,CAAC,CAAC;EACpC,IAAM+B,YAAY,GAAG,SAAfA,YAAYA,CAAIjD,KAAK,EAAK;IAC9B,IAAMU,IAAI,GAAG,SAAPA,IAAIA,CAAIiB,KAAK,EAAK;MACtB,IAAIA,KAAK,CAACkB,KAAK,EAAE;QACfjC,gBAAgB,CAACiB,IAAI,CAACF,KAAK,CAAC;QAC5BY,cAAc,CAACV,IAAI,CAACF,KAAK,CAACkB,KAAK,CAAC;QAChCP,YAAY,CAAC9B,MAAM,EAAE+B,cAAc,EAAE3B,gBAAgB,CAAC;MACxD;IACF,CAAC;IACD,IAAMD,MAAM,GAAG,SAATA,MAAMA,CAAIgB,KAAK,EAAK;MACxB,IAAMd,KAAK,GAAGD,gBAAgB,CAACE,OAAO,CAACa,KAAK,CAAC;MAC7CY,cAAc,CAACW,MAAM,CAACrC,KAAK,EAAE,CAAC,CAAC;MAC/BD,gBAAgB,CAACsC,MAAM,CAACrC,KAAK,EAAE,CAAC,CAAC;IACnC,CAAC;IACDG,OAAO,CACLT,GAAG,EACH4C,MAAM,CAACC,MAAM,CACX;MACE1C,IAAI,EAAJA,IAAI;MACJC,MAAM,EAANA,MAAM;MACNS,QAAQ,EAAEmB,cAAc;MACxB3B,gBAAgB,EAAhBA;IACF,CAAC,EACDZ,KACF,CACF,CAAC;EACH,CAAC;EACD,OAAO;IACLoB,QAAQ,EAAEmB,cAAc;IACxBU,YAAY,EAAZA;EACF,CAAC;AACH;;AAEA;AACA,SACEzD,GAAG,IAAI6D,IAAI,EACXlD,QAAQ,IAAImD,SAAS,EACrBC,WAAW,EACXC,aAAa,EACbC,eAAe,QACV,KAAK;AACZ,IAAIC,MAAM,GAAG,GAAG;AAChB,IAAIC,MAAM,GAAG,EAAE,GAAGD,MAAM;AACxB,IAAIE,IAAI,GAAG,EAAE,GAAGD,MAAM;AACtB,IAAIE,GAAG,GAAG,EAAE,GAAGD,IAAI;AACnB,SAASE,SAASA,CAACC,IAAI,EAAE;EACvB,IAAMC,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACH,IAAI,GAAGF,GAAG,CAAC;EACnC,IAAMM,KAAK,GAAGF,IAAI,CAACC,KAAK,CAACH,IAAI,GAAGF,GAAG,GAAGD,IAAI,CAAC;EAC3C,IAAMQ,OAAO,GAAGH,IAAI,CAACC,KAAK,CAACH,IAAI,GAAGH,IAAI,GAAGD,MAAM,CAAC;EAChD,IAAMU,OAAO,GAAGJ,IAAI,CAACC,KAAK,CAACH,IAAI,GAAGJ,MAAM,GAAGD,MAAM,CAAC;EAClD,IAAMY,YAAY,GAAGL,IAAI,CAACC,KAAK,CAACH,IAAI,GAAGL,MAAM,CAAC;EAC9C,OAAO;IACLa,KAAK,EAAER,IAAI;IACXC,IAAI,EAAJA,IAAI;IACJG,KAAK,EAALA,KAAK;IACLC,OAAO,EAAPA,OAAO;IACPC,OAAO,EAAPA,OAAO;IACPC,YAAY,EAAZA;EACF,CAAC;AACH;AACA,SAASE,YAAYA,CAACC,KAAK,EAAEC,KAAK,EAAE;EAClC,OAAOT,IAAI,CAACC,KAAK,CAACO,KAAK,GAAG,GAAG,CAAC,KAAKR,IAAI,CAACC,KAAK,CAACQ,KAAK,GAAG,GAAG,CAAC;AAC5D;AACA,SAASC,YAAYA,CAACC,OAAO,EAAE;EAC7B,IAAIC,KAAK;EACT,IAAIC,OAAO;EACX,IAAIC,QAAQ;EACZ,IAAIC,WAAW;EACf,IAAMC,MAAM,GAAG5B,IAAI,CAACuB,OAAO,CAACb,IAAI,CAAC;EACjC,IAAMmB,OAAO,GAAG5B,SAAS,CAAC;IAAA,OAAMQ,SAAS,CAACmB,MAAM,CAACjF,KAAK,CAAC;EAAA,EAAC;EACxD,IAAMmF,KAAK,GAAG,SAARA,KAAKA,CAAA,EAAS;IAClBJ,QAAQ,GAAG,KAAK;IAChB7G,SAAS,CAAC2G,KAAK,CAAC;EAClB,CAAC;EACD,IAAMO,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAA;IAAA,OAASnB,IAAI,CAACoB,GAAG,CAACP,OAAO,GAAGQ,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;EAAA;EAChE,IAAMC,SAAS,GAAG,SAAZA,SAASA,CAAIxF,KAAK,EAAK;IAC3B,IAAI4B,EAAE,EAAE6D,EAAE;IACVR,MAAM,CAACjF,KAAK,GAAGA,KAAK;IACpB,CAAC4B,EAAE,GAAGgD,OAAO,CAACc,QAAQ,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG9D,EAAE,CAAC+D,IAAI,CAACf,OAAO,EAAEM,OAAO,CAAClF,KAAK,CAAC;IAC1E,IAAIA,KAAK,KAAK,CAAC,EAAE;MACfmF,KAAK,CAAC,CAAC;MACP,CAACM,EAAE,GAAGb,OAAO,CAACgB,QAAQ,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGH,EAAE,CAACE,IAAI,CAACf,OAAO,CAAC;IAC7D;EACF,CAAC;EACD,IAAMiB,SAAS,GAAG,SAAZA,SAASA,CAAA,EAAS;IACtBhB,KAAK,GAAG9G,GAAG,CAAC,YAAM;MAChB,IAAIgH,QAAQ,EAAE;QACZS,SAAS,CAACJ,gBAAgB,CAAC,CAAC,CAAC;QAC7B,IAAIH,MAAM,CAACjF,KAAK,GAAG,CAAC,EAAE;UACpB6F,SAAS,CAAC,CAAC;QACb;MACF;IACF,CAAC,CAAC;EACJ,CAAC;EACD,IAAMC,SAAS,GAAG,SAAZA,SAASA,CAAA,EAAS;IACtBjB,KAAK,GAAG9G,GAAG,CAAC,YAAM;MAChB,IAAIgH,QAAQ,EAAE;QACZ,IAAMgB,YAAY,GAAGX,gBAAgB,CAAC,CAAC;QACvC,IAAI,CAACZ,YAAY,CAACuB,YAAY,EAAEd,MAAM,CAACjF,KAAK,CAAC,IAAI+F,YAAY,KAAK,CAAC,EAAE;UACnEP,SAAS,CAACO,YAAY,CAAC;QACzB;QACA,IAAId,MAAM,CAACjF,KAAK,GAAG,CAAC,EAAE;UACpB8F,SAAS,CAAC,CAAC;QACb;MACF;IACF,CAAC,CAAC;EACJ,CAAC;EACD,IAAME,IAAI,GAAG,SAAPA,IAAIA,CAAA,EAAS;IACjB,IAAI,CAACpI,SAAS,EAAE;MACd;IACF;IACA,IAAIgH,OAAO,CAACqB,WAAW,EAAE;MACvBJ,SAAS,CAAC,CAAC;IACb,CAAC,MAAM;MACLC,SAAS,CAAC,CAAC;IACb;EACF,CAAC;EACD,IAAMI,KAAK,GAAG,SAARA,KAAKA,CAAA,EAAS;IAClB,IAAI,CAACnB,QAAQ,EAAE;MACbD,OAAO,GAAGQ,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGN,MAAM,CAACjF,KAAK;MACnC+E,QAAQ,GAAG,IAAI;MACfiB,IAAI,CAAC,CAAC;IACR;EACF,CAAC;EACD,IAAMG,KAAK,GAAG,SAARA,KAAKA,CAAA,EAAiC;IAAA,IAA7BC,SAAS,GAAAzG,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAGiF,OAAO,CAACb,IAAI;IACrCoB,KAAK,CAAC,CAAC;IACPF,MAAM,CAACjF,KAAK,GAAGoG,SAAS;EAC1B,CAAC;EACD3C,eAAe,CAAC0B,KAAK,CAAC;EACtB5B,WAAW,CAAC,YAAM;IAChB,IAAIyB,WAAW,EAAE;MACfD,QAAQ,GAAG,IAAI;MACfC,WAAW,GAAG,KAAK;MACnBgB,IAAI,CAAC,CAAC;IACR;EACF,CAAC,CAAC;EACFxC,aAAa,CAAC,YAAM;IAClB,IAAIuB,QAAQ,EAAE;MACZI,KAAK,CAAC,CAAC;MACPH,WAAW,GAAG,IAAI;IACpB;EACF,CAAC,CAAC;EACF,OAAO;IACLkB,KAAK,EAALA,KAAK;IACLf,KAAK,EAALA,KAAK;IACLgB,KAAK,EAALA,KAAK;IACLjB,OAAO,EAAPA;EACF,CAAC;AACH;;AAEA;AACA,SAAS5G,KAAK,IAAI+H,MAAM,QAAQ,KAAK;;AAErC;AACA,SACEC,KAAK,EACLC,KAAK,EACLjI,KAAK,IAAIkI,MAAM,EACfpG,WAAW,IAAIqG,YAAY,EAC3BjD,aAAa,IAAIkD,cAAc,QAC1B,KAAK;;AAEZ;AACA,SAASC,QAAQ,EAAEC,SAAS,EAAErD,WAAW,IAAIsD,YAAY,QAAQ,KAAK;AACtE,SAASC,oBAAoBA,CAACC,IAAI,EAAE;EAClC,IAAIC,OAAO;EACXJ,SAAS,CAAC,YAAM;IACdG,IAAI,CAAC,CAAC;IACNJ,QAAQ,CAAC,YAAM;MACbK,OAAO,GAAG,IAAI;IAChB,CAAC,CAAC;EACJ,CAAC,CAAC;EACFH,YAAY,CAAC,YAAM;IACjB,IAAIG,OAAO,EAAE;MACXD,IAAI,CAAC,CAAC;IACR;EACF,CAAC,CAAC;AACJ;;AAEA;AACA,SAASE,gBAAgBA,CAAC5E,IAAI,EAAE6E,QAAQ,EAAgB;EAAA,IAAdtC,OAAO,GAAAjF,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EACpD,IAAI,CAAC/B,SAAS,EAAE;IACd;EACF;EACA,IAAAuJ,eAAA,GAA8DvC,OAAO,CAA7DwC,MAAM;IAANA,MAAM,GAAAD,eAAA,cAAGtJ,MAAM,GAAAsJ,eAAA;IAAAE,gBAAA,GAAuCzC,OAAO,CAA5C0C,OAAO;IAAPA,OAAO,GAAAD,gBAAA,cAAG,KAAK,GAAAA,gBAAA;IAAAE,gBAAA,GAAsB3C,OAAO,CAA3B4C,OAAO;IAAPA,OAAO,GAAAD,gBAAA,cAAG,KAAK,GAAAA,gBAAA;EACzD,IAAIE,OAAO,GAAG,KAAK;EACnB,IAAIC,QAAQ;EACZ,IAAMC,GAAG,GAAG,SAANA,GAAGA,CAAIC,OAAO,EAAK;IACvB,IAAIH,OAAO,EAAE;MACX;IACF;IACA,IAAMrI,OAAO,GAAGoH,MAAM,CAACoB,OAAO,CAAC;IAC/B,IAAIxI,OAAO,IAAI,CAACsI,QAAQ,EAAE;MACxBtI,OAAO,CAACyI,gBAAgB,CAACxF,IAAI,EAAE6E,QAAQ,EAAE;QACvCM,OAAO,EAAPA,OAAO;QACPF,OAAO,EAAPA;MACF,CAAC,CAAC;MACFI,QAAQ,GAAG,IAAI;IACjB;EACF,CAAC;EACD,IAAMI,MAAM,GAAG,SAATA,MAAMA,CAAIF,OAAO,EAAK;IAC1B,IAAIH,OAAO,EAAE;MACX;IACF;IACA,IAAMrI,OAAO,GAAGoH,MAAM,CAACoB,OAAO,CAAC;IAC/B,IAAIxI,OAAO,IAAIsI,QAAQ,EAAE;MACvBtI,OAAO,CAAC2I,mBAAmB,CAAC1F,IAAI,EAAE6E,QAAQ,EAAEM,OAAO,CAAC;MACpDE,QAAQ,GAAG,KAAK;IAClB;EACF,CAAC;EACDjB,YAAY,CAAC;IAAA,OAAMqB,MAAM,CAACV,MAAM,CAAC;EAAA,EAAC;EAClCV,cAAc,CAAC;IAAA,OAAMoB,MAAM,CAACV,MAAM,CAAC;EAAA,EAAC;EACpCN,oBAAoB,CAAC;IAAA,OAAMa,GAAG,CAACP,MAAM,CAAC;EAAA,EAAC;EACvC,IAAIY,SAAS;EACb,IAAIzB,KAAK,CAACa,MAAM,CAAC,EAAE;IACjBY,SAAS,GAAG1B,KAAK,CAACc,MAAM,EAAE,UAAC5I,GAAG,EAAEyJ,MAAM,EAAK;MACzCH,MAAM,CAACG,MAAM,CAAC;MACdN,GAAG,CAACnJ,GAAG,CAAC;IACV,CAAC,CAAC;EACJ;EACA,OAAO,YAAM;IACXwJ,SAAS,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,SAAS,CAAC,CAAC;IACxCF,MAAM,CAACV,MAAM,CAAC;IACdK,OAAO,GAAG,IAAI;EAChB,CAAC;AACH;;AAEA;AACA,SAASS,YAAYA,CAACd,MAAM,EAAEF,QAAQ,EAAgB;EAAA,IAAdtC,OAAO,GAAAjF,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EAClD,IAAI,CAAC/B,SAAS,EAAE;IACd;EACF;EACA,IAAAuK,kBAAA,GAAgCvD,OAAO,CAA/BwD,SAAS;IAATA,SAAS,GAAAD,kBAAA,cAAG,OAAO,GAAAA,kBAAA;EAC3B,IAAME,OAAO,GAAG,SAAVA,OAAOA,CAAIC,KAAK,EAAK;IACzB,IAAMC,OAAO,GAAG/G,KAAK,CAACC,OAAO,CAAC2F,MAAM,CAAC,GAAGA,MAAM,GAAG,CAACA,MAAM,CAAC;IACzD,IAAMoB,WAAW,GAAGD,OAAO,CAACE,KAAK,CAAC,UAACrG,IAAI,EAAK;MAC1C,IAAMhD,OAAO,GAAGiH,MAAM,CAACjE,IAAI,CAAC;MAC5B,OAAOhD,OAAO,IAAI,CAACA,OAAO,CAACsJ,QAAQ,CAACJ,KAAK,CAAClB,MAAM,CAAC;IACnD,CAAC,CAAC;IACF,IAAIoB,WAAW,EAAE;MACftB,QAAQ,CAACoB,KAAK,CAAC;IACjB;EACF,CAAC;EACDrB,gBAAgB,CAACmB,SAAS,EAAEC,OAAO,EAAE;IAAEjB,MAAM,EAAEuB;EAAS,CAAC,CAAC;AAC5D;;AAEA;AACA,SAASnJ,GAAG,IAAIoJ,IAAI,QAAQ,KAAK;AACjC,IAAI5J,KAAK;AACT,IAAIC,MAAM;AACV,SAAS4J,aAAaA,CAAA,EAAG;EACvB,IAAI,CAAC7J,KAAK,EAAE;IACVA,KAAK,GAAG4J,IAAI,CAAC,CAAC,CAAC;IACf3J,MAAM,GAAG2J,IAAI,CAAC,CAAC,CAAC;IAChB,IAAIhL,SAAS,EAAE;MACb,IAAMkL,MAAM,GAAG,SAATA,MAAMA,CAAA,EAAS;QACnB9J,KAAK,CAACgB,KAAK,GAAGnC,MAAM,CAACwB,UAAU;QAC/BJ,MAAM,CAACe,KAAK,GAAGnC,MAAM,CAACyB,WAAW;MACnC,CAAC;MACDwJ,MAAM,CAAC,CAAC;MACRjL,MAAM,CAACgK,gBAAgB,CAAC,QAAQ,EAAEiB,MAAM,EAAE;QAAExB,OAAO,EAAE;MAAK,CAAC,CAAC;MAC5DzJ,MAAM,CAACgK,gBAAgB,CAAC,mBAAmB,EAAEiB,MAAM,EAAE;QAAExB,OAAO,EAAE;MAAK,CAAC,CAAC;IACzE;EACF;EACA,OAAO;IAAEtI,KAAK,EAALA,KAAK;IAAEC,MAAM,EAANA;EAAO,CAAC;AAC1B;;AAEA;AACA,SAASO,GAAG,IAAIuJ,IAAI,EAAEnC,SAAS,IAAIoC,UAAU,QAAQ,KAAK;AAC1D,IAAIC,iBAAiB,GAAG,sBAAsB;AAC9C,IAAIC,WAAW,GAAGtL,SAAS,GAAGC,MAAM,GAAG,KAAK,CAAC;AAC7C,SAASsL,SAASA,CAACC,IAAI,EAAE;EACvB,IAAMC,iBAAiB,GAAG,CAAC;EAC3B,OAAOD,IAAI,CAACE,OAAO,KAAK,MAAM,IAAIF,IAAI,CAACE,OAAO,KAAK,MAAM,IAAIF,IAAI,CAACG,QAAQ,KAAKF,iBAAiB;AAClG;AACA,SAASG,eAAeA,CAACC,EAAE,EAAsB;EAAA,IAApBC,IAAI,GAAA/J,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAGuJ,WAAW;EAC7C,IAAIE,IAAI,GAAGK,EAAE;EACb,OAAOL,IAAI,IAAIA,IAAI,KAAKM,IAAI,IAAIP,SAAS,CAACC,IAAI,CAAC,EAAE;IAC/C,IAAAO,qBAAA,GAAsB9L,MAAM,CAAC+L,gBAAgB,CAACR,IAAI,CAAC;MAA3CS,SAAS,GAAAF,qBAAA,CAATE,SAAS;IACjB,IAAIZ,iBAAiB,CAACa,IAAI,CAACD,SAAS,CAAC,EAAE;MACrC,OAAOT,IAAI;IACb;IACAA,IAAI,GAAGA,IAAI,CAACW,UAAU;EACxB;EACA,OAAOL,IAAI;AACb;AACA,SAASM,eAAeA,CAACP,EAAE,EAAsB;EAAA,IAApBC,IAAI,GAAA/J,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAGuJ,WAAW;EAC7C,IAAMe,YAAY,GAAGlB,IAAI,CAAC,CAAC;EAC3BC,UAAU,CAAC,YAAM;IACf,IAAIS,EAAE,CAACzJ,KAAK,EAAE;MACZiK,YAAY,CAACjK,KAAK,GAAGwJ,eAAe,CAACC,EAAE,CAACzJ,KAAK,EAAE0J,IAAI,CAAC;IACtD;EACF,CAAC,CAAC;EACF,OAAOO,YAAY;AACrB;;AAEA;AACA,SAASzK,GAAG,IAAI0K,IAAI,QAAQ,KAAK;AACjC,IAAIC,UAAU;AACd,SAASC,iBAAiBA,CAAA,EAAG;EAC3B,IAAI,CAACD,UAAU,EAAE;IACfA,UAAU,GAAGD,IAAI,CAAC,SAAS,CAAC;IAC5B,IAAItM,SAAS,EAAE;MACb,IAAMkL,MAAM,GAAG,SAATA,MAAMA,CAAA,EAAS;QACnBqB,UAAU,CAACnK,KAAK,GAAG2I,QAAQ,CAAC0B,MAAM,GAAG,QAAQ,GAAG,SAAS;MAC3D,CAAC;MACDvB,MAAM,CAAC,CAAC;MACRjL,MAAM,CAACgK,gBAAgB,CAAC,kBAAkB,EAAEiB,MAAM,CAAC;IACrD;EACF;EACA,OAAOqB,UAAU;AACnB;;AAEA;AACA,SAAS7D,KAAK,IAAIgE,MAAM,EAAEpK,MAAM,IAAIqK,OAAO,QAAQ,KAAK;AACxD,IAAIC,0BAA0B,GAAGC,MAAM,CAAC,WAAW,CAAC;AACpD,SAASC,mBAAmBA,CAACC,WAAW,EAAE;EACxC,IAAMC,KAAK,GAAGL,OAAO,CAACC,0BAA0B,EAAE,IAAI,CAAC;EACvD,IAAII,KAAK,IAAI,CAACA,KAAK,CAACD,WAAW,CAAC3K,KAAK,EAAE;IACrC4K,KAAK,CAACD,WAAW,CAAC3K,KAAK,GAAG2K,WAAW;IACrCL,MAAM,CAACK,WAAW,EAAE,YAAM;MACxBC,KAAK,CAACC,eAAe,CAAC,CAAC;MACvBD,KAAK,CAACE,mBAAmB,CAAC,UAAU,CAAC;IACvC,CAAC,CAAC;EACJ;AACF;AACA,SACEN,0BAA0B,EAC1BtM,SAAS,EACTG,SAAS,EACT8C,aAAa,EACbqI,eAAe,EACf5L,SAAS,EACTkJ,oBAAoB,EACpB/I,GAAG,EACHuE,YAAY,EACZxE,eAAe,EACfkF,WAAW,EACXkF,YAAY,EACZvD,YAAY,EACZ+F,mBAAmB,EACnBzD,gBAAgB,EAChBmD,iBAAiB,EACjB9J,SAAS,EACTpB,OAAO,EACP8K,eAAe,EACfvK,SAAS,EACToJ,aAAa"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}