{"ast": null, "code": "function _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return _typeof(key) === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (_typeof(input) !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (_typeof(res) !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\nfunction _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nfunction _iterableToArrayLimit(arr, i) { var _i = null == arr ? null : \"undefined\" != typeof Symbol && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (null != _i) { var _s, _e, _x, _r, _arr = [], _n = !0, _d = !1; try { if (_x = (_i = _i.call(arr)).next, 0 === i) { if (Object(_i) !== _i) return; _n = !1; } else for (; !(_n = (_s = _x.call(_i)).done) && (_arr.push(_s.value), _arr.length !== i); _n = !0); } catch (err) { _d = !0, _e = err; } finally { try { if (!_n && null != _i.return && (_r = _i.return(), Object(_r) !== _r)) return; } finally { if (_d) throw _e; } } return _arr; } }\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\nimport { createVNode as _createVNode } from \"vue\";\nimport { computed, defineComponent } from \"vue\";\nimport { truthProp, makeStringProp, makeNumericProp, createNamespace } from \"../utils/index.mjs\";\nimport { useChildren } from \"@vant/use\";\nvar _createNamespace = createNamespace(\"row\"),\n  _createNamespace2 = _slicedToArray(_createNamespace, 2),\n  name = _createNamespace2[0],\n  bem = _createNamespace2[1];\nvar ROW_KEY = Symbol(name);\nvar rowProps = {\n  tag: makeStringProp(\"div\"),\n  wrap: truthProp,\n  align: String,\n  gutter: makeNumericProp(0),\n  justify: String\n};\nvar stdin_default = defineComponent({\n  name: name,\n  props: rowProps,\n  setup: function setup(props, _ref) {\n    var slots = _ref.slots;\n    var _useChildren = useChildren(ROW_KEY),\n      children = _useChildren.children,\n      linkChildren = _useChildren.linkChildren;\n    var groups = computed(function () {\n      var groups2 = [[]];\n      var totalSpan = 0;\n      children.forEach(function (child, index) {\n        totalSpan += Number(child.span);\n        if (totalSpan > 24) {\n          groups2.push([index]);\n          totalSpan -= 24;\n        } else {\n          groups2[groups2.length - 1].push(index);\n        }\n      });\n      return groups2;\n    });\n    var spaces = computed(function () {\n      var gutter = Number(props.gutter);\n      var spaces2 = [];\n      if (!gutter) {\n        return spaces2;\n      }\n      groups.value.forEach(function (group) {\n        var averagePadding = gutter * (group.length - 1) / group.length;\n        group.forEach(function (item, index) {\n          if (index === 0) {\n            spaces2.push({\n              right: averagePadding\n            });\n          } else {\n            var left = gutter - spaces2[item - 1].right;\n            var right = averagePadding - left;\n            spaces2.push({\n              left: left,\n              right: right\n            });\n          }\n        });\n      });\n      return spaces2;\n    });\n    linkChildren({\n      spaces: spaces\n    });\n    return function () {\n      var _bem;\n      var tag = props.tag,\n        wrap = props.wrap,\n        align = props.align,\n        justify = props.justify;\n      return _createVNode(tag, {\n        \"class\": bem((_bem = {}, _defineProperty(_bem, \"align-\".concat(align), align), _defineProperty(_bem, \"justify-\".concat(justify), justify), _defineProperty(_bem, \"nowrap\", !wrap), _bem))\n      }, {\n        default: function _default() {\n          var _a;\n          return [(_a = slots.default) == null ? void 0 : _a.call(slots)];\n        }\n      });\n    };\n  }\n});\nexport { ROW_KEY, stdin_default as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}