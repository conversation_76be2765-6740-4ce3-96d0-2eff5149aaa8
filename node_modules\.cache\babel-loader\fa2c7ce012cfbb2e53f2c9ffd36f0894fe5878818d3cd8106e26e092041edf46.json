{"ast": null, "code": "import utils from '../utils.js';\nimport httpAdapter from './http.js';\nimport xhrAdapter from './xhr.js';\nimport AxiosError from \"../core/AxiosError.js\";\nvar knownAdapters = {\n  http: httpAdapter,\n  xhr: xhrAdapter\n};\nutils.forEach(knownAdapters, function (fn, value) {\n  if (fn) {\n    try {\n      Object.defineProperty(fn, 'name', {\n        value: value\n      });\n    } catch (e) {\n      // eslint-disable-next-line no-empty\n    }\n    Object.defineProperty(fn, 'adapterName', {\n      value: value\n    });\n  }\n});\nexport default {\n  getAdapter: function getAdapter(adapters) {\n    adapters = utils.isArray(adapters) ? adapters : [adapters];\n    var _adapters = adapters,\n      length = _adapters.length;\n    var nameOrAdapter;\n    var adapter;\n    for (var i = 0; i < length; i++) {\n      nameOrAdapter = adapters[i];\n      if (adapter = utils.isString(nameOrAdapter) ? knownAdapters[nameOrAdapter.toLowerCase()] : nameOrAdapter) {\n        break;\n      }\n    }\n    if (!adapter) {\n      if (adapter === false) {\n        throw new AxiosError(\"Adapter \".concat(nameOrAdapter, \" is not supported by the environment\"), 'ERR_NOT_SUPPORT');\n      }\n      throw new Error(utils.hasOwnProp(knownAdapters, nameOrAdapter) ? \"Adapter '\".concat(nameOrAdapter, \"' is not available in the build\") : \"Unknown adapter '\".concat(nameOrAdapter, \"'\"));\n    }\n    if (!utils.isFunction(adapter)) {\n      throw new TypeError('adapter is not a function');\n    }\n    return adapter;\n  },\n  adapters: knownAdapters\n};", "map": {"version": 3, "names": ["utils", "httpAdapter", "xhrAdapter", "AxiosError", "knownAdapters", "http", "xhr", "for<PERSON>ach", "fn", "value", "Object", "defineProperty", "e", "getAdapter", "adapters", "isArray", "_adapters", "length", "nameOrAdapter", "adapter", "i", "isString", "toLowerCase", "concat", "Error", "hasOwnProp", "isFunction", "TypeError"], "sources": ["C:/Users/<USER>/Downloads/Telegram Desktop/vue3_3.0/vue3_3.0/node_modules/axios/lib/adapters/adapters.js"], "sourcesContent": ["import utils from '../utils.js';\nimport httpAdapter from './http.js';\nimport xhrAdapter from './xhr.js';\nimport AxiosError from \"../core/AxiosError.js\";\n\nconst knownAdapters = {\n  http: httpAdapter,\n  xhr: xhrAdapter\n}\n\nutils.forEach(knownAdapters, (fn, value) => {\n  if(fn) {\n    try {\n      Object.defineProperty(fn, 'name', {value});\n    } catch (e) {\n      // eslint-disable-next-line no-empty\n    }\n    Object.defineProperty(fn, 'adapterName', {value});\n  }\n});\n\nexport default {\n  getAdapter: (adapters) => {\n    adapters = utils.isArray(adapters) ? adapters : [adapters];\n\n    const {length} = adapters;\n    let nameOrAdapter;\n    let adapter;\n\n    for (let i = 0; i < length; i++) {\n      nameOrAdapter = adapters[i];\n      if((adapter = utils.isString(nameOrAdapter) ? knownAdapters[nameOrAdapter.toLowerCase()] : nameOrAdapter)) {\n        break;\n      }\n    }\n\n    if (!adapter) {\n      if (adapter === false) {\n        throw new AxiosError(\n          `Adapter ${nameOrAdapter} is not supported by the environment`,\n          'ERR_NOT_SUPPORT'\n        );\n      }\n\n      throw new Error(\n        utils.hasOwnProp(knownAdapters, nameOrAdapter) ?\n          `Adapter '${nameOrAdapter}' is not available in the build` :\n          `Unknown adapter '${nameOrAdapter}'`\n      );\n    }\n\n    if (!utils.isFunction(adapter)) {\n      throw new TypeError('adapter is not a function');\n    }\n\n    return adapter;\n  },\n  adapters: knownAdapters\n}\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,aAAa;AAC/B,OAAOC,WAAW,MAAM,WAAW;AACnC,OAAOC,UAAU,MAAM,UAAU;AACjC,OAAOC,UAAU,MAAM,uBAAuB;AAE9C,IAAMC,aAAa,GAAG;EACpBC,IAAI,EAAEJ,WAAW;EACjBK,GAAG,EAAEJ;AACP,CAAC;AAEDF,KAAK,CAACO,OAAO,CAACH,aAAa,EAAE,UAACI,EAAE,EAAEC,KAAK,EAAK;EAC1C,IAAGD,EAAE,EAAE;IACL,IAAI;MACFE,MAAM,CAACC,cAAc,CAACH,EAAE,EAAE,MAAM,EAAE;QAACC,KAAK,EAALA;MAAK,CAAC,CAAC;IAC5C,CAAC,CAAC,OAAOG,CAAC,EAAE;MACV;IAAA;IAEFF,MAAM,CAACC,cAAc,CAACH,EAAE,EAAE,aAAa,EAAE;MAACC,KAAK,EAALA;IAAK,CAAC,CAAC;EACnD;AACF,CAAC,CAAC;AAEF,eAAe;EACbI,UAAU,EAAE,SAAAA,WAACC,QAAQ,EAAK;IACxBA,QAAQ,GAAGd,KAAK,CAACe,OAAO,CAACD,QAAQ,CAAC,GAAGA,QAAQ,GAAG,CAACA,QAAQ,CAAC;IAE1D,IAAAE,SAAA,GAAiBF,QAAQ;MAAlBG,MAAM,GAAAD,SAAA,CAANC,MAAM;IACb,IAAIC,aAAa;IACjB,IAAIC,OAAO;IAEX,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,MAAM,EAAEG,CAAC,EAAE,EAAE;MAC/BF,aAAa,GAAGJ,QAAQ,CAACM,CAAC,CAAC;MAC3B,IAAID,OAAO,GAAGnB,KAAK,CAACqB,QAAQ,CAACH,aAAa,CAAC,GAAGd,aAAa,CAACc,aAAa,CAACI,WAAW,CAAC,CAAC,CAAC,GAAGJ,aAAa,EAAG;QACzG;MACF;IACF;IAEA,IAAI,CAACC,OAAO,EAAE;MACZ,IAAIA,OAAO,KAAK,KAAK,EAAE;QACrB,MAAM,IAAIhB,UAAU,YAAAoB,MAAA,CACPL,aAAa,2CACxB,iBACF,CAAC;MACH;MAEA,MAAM,IAAIM,KAAK,CACbxB,KAAK,CAACyB,UAAU,CAACrB,aAAa,EAAEc,aAAa,CAAC,eAAAK,MAAA,CAChCL,aAAa,2DAAAK,MAAA,CACLL,aAAa,MACrC,CAAC;IACH;IAEA,IAAI,CAAClB,KAAK,CAAC0B,UAAU,CAACP,OAAO,CAAC,EAAE;MAC9B,MAAM,IAAIQ,SAAS,CAAC,2BAA2B,CAAC;IAClD;IAEA,OAAOR,OAAO;EAChB,CAAC;EACDL,QAAQ,EAAEV;AACZ,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}