{"ast": null, "code": "function _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return _typeof(key) === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (_typeof(input) !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (_typeof(res) !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\nimport { ref, reactive } from \"vue\";\nimport { deepAssign } from \"../utils/deep-assign.mjs\";\nimport defaultMessages from \"./lang/zh-CN.mjs\";\nvar lang = ref(\"zh-CN\");\nvar _messages = reactive({\n  \"zh-CN\": defaultMessages\n});\nvar Locale = {\n  messages: function messages() {\n    return _messages[lang.value];\n  },\n  use: function use(newLang, newMessages) {\n    lang.value = newLang;\n    this.add(_defineProperty({}, newLang, newMessages));\n  },\n  add: function add() {\n    var newMessages = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    deepAssign(_messages, newMessages);\n  }\n};\nvar stdin_default = Locale;\nexport { Locale, stdin_default as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}