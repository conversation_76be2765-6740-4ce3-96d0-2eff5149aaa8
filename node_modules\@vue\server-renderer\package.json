{"name": "@vue/server-renderer", "version": "3.2.26", "description": "@vue/server-renderer", "main": "index.js", "module": "dist/server-renderer.esm-bundler.js", "types": "dist/server-renderer.d.ts", "files": ["index.js", "dist"], "buildOptions": {"name": "VueServerR<PERSON><PERSON>", "formats": ["esm-bundler", "cjs"]}, "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-next.git", "directory": "packages/server-renderer"}, "keywords": ["vue"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-next/issues"}, "homepage": "https://github.com/vuejs/vue-next/tree/master/packages/server-renderer#readme", "peerDependencies": {"vue": "3.2.26"}, "dependencies": {"@vue/shared": "3.2.26", "@vue/compiler-ssr": "3.2.26"}}