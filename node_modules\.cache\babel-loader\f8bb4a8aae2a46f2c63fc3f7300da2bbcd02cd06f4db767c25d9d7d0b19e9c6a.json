{"ast": null, "code": "function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nfunction _iterableToArrayLimit(arr, i) { var _i = null == arr ? null : \"undefined\" != typeof Symbol && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (null != _i) { var _s, _e, _x, _r, _arr = [], _n = !0, _d = !1; try { if (_x = (_i = _i.call(arr)).next, 0 === i) { if (Object(_i) !== _i) return; _n = !1; } else for (; !(_n = (_s = _x.call(_i)).done) && (_arr.push(_s.value), _arr.length !== i); _n = !0); } catch (err) { _d = !0, _e = err; } finally { try { if (!_n && null != _i.return && (_r = _i.return(), Object(_r) !== _r)) return; } finally { if (_d) throw _e; } } return _arr; } }\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\nimport { withDirectives as _withDirectives, mergeProps as _mergeProps, resolveDirective as _resolveDirective, createVNode as _createVNode } from \"vue\";\nimport { ref, watch, computed, nextTick, onBeforeUnmount, defineComponent, getCurrentInstance } from \"vue\";\nimport { isDef, addUnit, inBrowser, truthProp, numericProp, makeStringProp, createNamespace } from \"../utils/index.mjs\";\nimport { Icon } from \"../icon/index.mjs\";\nvar _createNamespace = createNamespace(\"image\"),\n  _createNamespace2 = _slicedToArray(_createNamespace, 2),\n  name = _createNamespace2[0],\n  bem = _createNamespace2[1];\nvar imageProps = {\n  src: String,\n  alt: String,\n  fit: String,\n  position: String,\n  round: Boolean,\n  block: Boolean,\n  width: numericProp,\n  height: numericProp,\n  radius: numericProp,\n  lazyLoad: Boolean,\n  iconSize: numericProp,\n  showError: truthProp,\n  errorIcon: makeStringProp(\"photo-fail\"),\n  iconPrefix: String,\n  showLoading: truthProp,\n  loadingIcon: makeStringProp(\"photo\")\n};\nvar stdin_default = defineComponent({\n  name: name,\n  props: imageProps,\n  emits: [\"load\", \"error\"],\n  setup: function setup(props, _ref) {\n    var emit = _ref.emit,\n      slots = _ref.slots;\n    var error = ref(false);\n    var loading = ref(true);\n    var imageRef = ref();\n    var $Lazyload = getCurrentInstance().proxy.$Lazyload;\n    var style = computed(function () {\n      var style2 = {\n        width: addUnit(props.width),\n        height: addUnit(props.height)\n      };\n      if (isDef(props.radius)) {\n        style2.overflow = \"hidden\";\n        style2.borderRadius = addUnit(props.radius);\n      }\n      return style2;\n    });\n    watch(function () {\n      return props.src;\n    }, function () {\n      error.value = false;\n      loading.value = true;\n    });\n    var onLoad = function onLoad(event) {\n      loading.value = false;\n      emit(\"load\", event);\n    };\n    var onError = function onError(event) {\n      error.value = true;\n      loading.value = false;\n      emit(\"error\", event);\n    };\n    var renderIcon = function renderIcon(name2, className, slot) {\n      if (slot) {\n        return slot();\n      }\n      return _createVNode(Icon, {\n        \"name\": name2,\n        \"size\": props.iconSize,\n        \"class\": className,\n        \"classPrefix\": props.iconPrefix\n      }, null);\n    };\n    var renderPlaceholder = function renderPlaceholder() {\n      if (loading.value && props.showLoading) {\n        return _createVNode(\"div\", {\n          \"class\": bem(\"loading\")\n        }, [renderIcon(props.loadingIcon, bem(\"loading-icon\"), slots.loading)]);\n      }\n      if (error.value && props.showError) {\n        return _createVNode(\"div\", {\n          \"class\": bem(\"error\")\n        }, [renderIcon(props.errorIcon, bem(\"error-icon\"), slots.error)]);\n      }\n    };\n    var renderImage = function renderImage() {\n      if (error.value || !props.src) {\n        return;\n      }\n      var attrs = {\n        alt: props.alt,\n        class: bem(\"img\"),\n        style: {\n          objectFit: props.fit,\n          objectPosition: props.position\n        }\n      };\n      if (props.lazyLoad) {\n        return _withDirectives(_createVNode(\"img\", _mergeProps({\n          \"ref\": imageRef\n        }, attrs), null), [[_resolveDirective(\"lazy\"), props.src]]);\n      }\n      return _createVNode(\"img\", _mergeProps({\n        \"src\": props.src,\n        \"onLoad\": onLoad,\n        \"onError\": onError\n      }, attrs), null);\n    };\n    var onLazyLoaded = function onLazyLoaded(_ref2) {\n      var el = _ref2.el;\n      var check = function check() {\n        if (el === imageRef.value && loading.value) {\n          onLoad();\n        }\n      };\n      if (imageRef.value) {\n        check();\n      } else {\n        nextTick(check);\n      }\n    };\n    var onLazyLoadError = function onLazyLoadError(_ref3) {\n      var el = _ref3.el;\n      if (el === imageRef.value && !error.value) {\n        onError();\n      }\n    };\n    if ($Lazyload && inBrowser) {\n      $Lazyload.$on(\"loaded\", onLazyLoaded);\n      $Lazyload.$on(\"error\", onLazyLoadError);\n      onBeforeUnmount(function () {\n        $Lazyload.$off(\"loaded\", onLazyLoaded);\n        $Lazyload.$off(\"error\", onLazyLoadError);\n      });\n    }\n    return function () {\n      var _a;\n      return _createVNode(\"div\", {\n        \"class\": bem({\n          round: props.round,\n          block: props.block\n        }),\n        \"style\": style.value\n      }, [renderImage(), renderPlaceholder(), (_a = slots.default) == null ? void 0 : _a.call(slots)]);\n    };\n  }\n});\nexport { stdin_default as default };", "map": {"version": 3, "names": ["withDirectives", "_withDirectives", "mergeProps", "_mergeProps", "resolveDirective", "_resolveDirective", "createVNode", "_createVNode", "ref", "watch", "computed", "nextTick", "onBeforeUnmount", "defineComponent", "getCurrentInstance", "isDef", "addUnit", "inBrowser", "truthProp", "numericProp", "makeStringProp", "createNamespace", "Icon", "_createNamespace", "_createNamespace2", "_slicedToArray", "name", "bem", "imageProps", "src", "String", "alt", "fit", "position", "round", "Boolean", "block", "width", "height", "radius", "lazyLoad", "iconSize", "showError", "errorIcon", "iconPrefix", "showLoading", "loadingIcon", "stdin_default", "props", "emits", "setup", "_ref", "emit", "slots", "error", "loading", "imageRef", "$Lazyload", "proxy", "style", "style2", "overflow", "borderRadius", "value", "onLoad", "event", "onError", "renderIcon", "name2", "className", "slot", "renderPlaceholder", "renderImage", "attrs", "class", "objectFit", "objectPosition", "onLazyLoaded", "_ref2", "el", "check", "onLazyLoadError", "_ref3", "$on", "$off", "_a", "default", "call"], "sources": ["C:/Users/<USER>/Downloads/Telegram Desktop/vue3_3.0/vue3_3.0/node_modules/vant/es/image/Image.mjs"], "sourcesContent": ["import { withDirectives as _withDirectives, mergeProps as _mergeProps, resolveDirective as _resolveDirective, createVNode as _createVNode } from \"vue\";\nimport { ref, watch, computed, nextTick, onBeforeUnmount, defineComponent, getCurrentInstance } from \"vue\";\nimport { isDef, addUnit, inBrowser, truthProp, numericProp, makeStringProp, createNamespace } from \"../utils/index.mjs\";\nimport { Icon } from \"../icon/index.mjs\";\nconst [name, bem] = createNamespace(\"image\");\nconst imageProps = {\n  src: String,\n  alt: String,\n  fit: String,\n  position: String,\n  round: Boolean,\n  block: Boolean,\n  width: numericProp,\n  height: numericProp,\n  radius: numericProp,\n  lazyLoad: Boolean,\n  iconSize: numericProp,\n  showError: truthProp,\n  errorIcon: makeStringProp(\"photo-fail\"),\n  iconPrefix: String,\n  showLoading: truthProp,\n  loadingIcon: makeStringProp(\"photo\")\n};\nvar stdin_default = defineComponent({\n  name,\n  props: imageProps,\n  emits: [\"load\", \"error\"],\n  setup(props, {\n    emit,\n    slots\n  }) {\n    const error = ref(false);\n    const loading = ref(true);\n    const imageRef = ref();\n    const {\n      $Lazyload\n    } = getCurrentInstance().proxy;\n    const style = computed(() => {\n      const style2 = {\n        width: addUnit(props.width),\n        height: addUnit(props.height)\n      };\n      if (isDef(props.radius)) {\n        style2.overflow = \"hidden\";\n        style2.borderRadius = addUnit(props.radius);\n      }\n      return style2;\n    });\n    watch(() => props.src, () => {\n      error.value = false;\n      loading.value = true;\n    });\n    const onLoad = (event) => {\n      loading.value = false;\n      emit(\"load\", event);\n    };\n    const onError = (event) => {\n      error.value = true;\n      loading.value = false;\n      emit(\"error\", event);\n    };\n    const renderIcon = (name2, className, slot) => {\n      if (slot) {\n        return slot();\n      }\n      return _createVNode(Icon, {\n        \"name\": name2,\n        \"size\": props.iconSize,\n        \"class\": className,\n        \"classPrefix\": props.iconPrefix\n      }, null);\n    };\n    const renderPlaceholder = () => {\n      if (loading.value && props.showLoading) {\n        return _createVNode(\"div\", {\n          \"class\": bem(\"loading\")\n        }, [renderIcon(props.loadingIcon, bem(\"loading-icon\"), slots.loading)]);\n      }\n      if (error.value && props.showError) {\n        return _createVNode(\"div\", {\n          \"class\": bem(\"error\")\n        }, [renderIcon(props.errorIcon, bem(\"error-icon\"), slots.error)]);\n      }\n    };\n    const renderImage = () => {\n      if (error.value || !props.src) {\n        return;\n      }\n      const attrs = {\n        alt: props.alt,\n        class: bem(\"img\"),\n        style: {\n          objectFit: props.fit,\n          objectPosition: props.position\n        }\n      };\n      if (props.lazyLoad) {\n        return _withDirectives(_createVNode(\"img\", _mergeProps({\n          \"ref\": imageRef\n        }, attrs), null), [[_resolveDirective(\"lazy\"), props.src]]);\n      }\n      return _createVNode(\"img\", _mergeProps({\n        \"src\": props.src,\n        \"onLoad\": onLoad,\n        \"onError\": onError\n      }, attrs), null);\n    };\n    const onLazyLoaded = ({\n      el\n    }) => {\n      const check = () => {\n        if (el === imageRef.value && loading.value) {\n          onLoad();\n        }\n      };\n      if (imageRef.value) {\n        check();\n      } else {\n        nextTick(check);\n      }\n    };\n    const onLazyLoadError = ({\n      el\n    }) => {\n      if (el === imageRef.value && !error.value) {\n        onError();\n      }\n    };\n    if ($Lazyload && inBrowser) {\n      $Lazyload.$on(\"loaded\", onLazyLoaded);\n      $Lazyload.$on(\"error\", onLazyLoadError);\n      onBeforeUnmount(() => {\n        $Lazyload.$off(\"loaded\", onLazyLoaded);\n        $Lazyload.$off(\"error\", onLazyLoadError);\n      });\n    }\n    return () => {\n      var _a;\n      return _createVNode(\"div\", {\n        \"class\": bem({\n          round: props.round,\n          block: props.block\n        }),\n        \"style\": style.value\n      }, [renderImage(), renderPlaceholder(), (_a = slots.default) == null ? void 0 : _a.call(slots)]);\n    };\n  }\n});\nexport {\n  stdin_default as default\n};\n"], "mappings": ";;;;;;AAAA,SAASA,cAAc,IAAIC,eAAe,EAAEC,UAAU,IAAIC,WAAW,EAAEC,gBAAgB,IAAIC,iBAAiB,EAAEC,WAAW,IAAIC,YAAY,QAAQ,KAAK;AACtJ,SAASC,GAAG,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,eAAe,EAAEC,eAAe,EAAEC,kBAAkB,QAAQ,KAAK;AAC1G,SAASC,KAAK,EAAEC,OAAO,EAAEC,SAAS,EAAEC,SAAS,EAAEC,WAAW,EAAEC,cAAc,EAAEC,eAAe,QAAQ,oBAAoB;AACvH,SAASC,IAAI,QAAQ,mBAAmB;AACxC,IAAAC,gBAAA,GAAoBF,eAAe,CAAC,OAAO,CAAC;EAAAG,iBAAA,GAAAC,cAAA,CAAAF,gBAAA;EAArCG,IAAI,GAAAF,iBAAA;EAAEG,GAAG,GAAAH,iBAAA;AAChB,IAAMI,UAAU,GAAG;EACjBC,GAAG,EAAEC,MAAM;EACXC,GAAG,EAAED,MAAM;EACXE,GAAG,EAAEF,MAAM;EACXG,QAAQ,EAAEH,MAAM;EAChBI,KAAK,EAAEC,OAAO;EACdC,KAAK,EAAED,OAAO;EACdE,KAAK,EAAElB,WAAW;EAClBmB,MAAM,EAAEnB,WAAW;EACnBoB,MAAM,EAAEpB,WAAW;EACnBqB,QAAQ,EAAEL,OAAO;EACjBM,QAAQ,EAAEtB,WAAW;EACrBuB,SAAS,EAAExB,SAAS;EACpByB,SAAS,EAAEvB,cAAc,CAAC,YAAY,CAAC;EACvCwB,UAAU,EAAEd,MAAM;EAClBe,WAAW,EAAE3B,SAAS;EACtB4B,WAAW,EAAE1B,cAAc,CAAC,OAAO;AACrC,CAAC;AACD,IAAI2B,aAAa,GAAGlC,eAAe,CAAC;EAClCa,IAAI,EAAJA,IAAI;EACJsB,KAAK,EAAEpB,UAAU;EACjBqB,KAAK,EAAE,CAAC,MAAM,EAAE,OAAO,CAAC;EACxBC,KAAK,WAAAA,MAACF,KAAK,EAAAG,IAAA,EAGR;IAAA,IAFDC,IAAI,GAAAD,IAAA,CAAJC,IAAI;MACJC,KAAK,GAAAF,IAAA,CAALE,KAAK;IAEL,IAAMC,KAAK,GAAG9C,GAAG,CAAC,KAAK,CAAC;IACxB,IAAM+C,OAAO,GAAG/C,GAAG,CAAC,IAAI,CAAC;IACzB,IAAMgD,QAAQ,GAAGhD,GAAG,CAAC,CAAC;IACtB,IACEiD,SAAS,GACP3C,kBAAkB,CAAC,CAAC,CAAC4C,KAAK,CAD5BD,SAAS;IAEX,IAAME,KAAK,GAAGjD,QAAQ,CAAC,YAAM;MAC3B,IAAMkD,MAAM,GAAG;QACbvB,KAAK,EAAErB,OAAO,CAACgC,KAAK,CAACX,KAAK,CAAC;QAC3BC,MAAM,EAAEtB,OAAO,CAACgC,KAAK,CAACV,MAAM;MAC9B,CAAC;MACD,IAAIvB,KAAK,CAACiC,KAAK,CAACT,MAAM,CAAC,EAAE;QACvBqB,MAAM,CAACC,QAAQ,GAAG,QAAQ;QAC1BD,MAAM,CAACE,YAAY,GAAG9C,OAAO,CAACgC,KAAK,CAACT,MAAM,CAAC;MAC7C;MACA,OAAOqB,MAAM;IACf,CAAC,CAAC;IACFnD,KAAK,CAAC;MAAA,OAAMuC,KAAK,CAACnB,GAAG;IAAA,GAAE,YAAM;MAC3ByB,KAAK,CAACS,KAAK,GAAG,KAAK;MACnBR,OAAO,CAACQ,KAAK,GAAG,IAAI;IACtB,CAAC,CAAC;IACF,IAAMC,MAAM,GAAG,SAATA,MAAMA,CAAIC,KAAK,EAAK;MACxBV,OAAO,CAACQ,KAAK,GAAG,KAAK;MACrBX,IAAI,CAAC,MAAM,EAAEa,KAAK,CAAC;IACrB,CAAC;IACD,IAAMC,OAAO,GAAG,SAAVA,OAAOA,CAAID,KAAK,EAAK;MACzBX,KAAK,CAACS,KAAK,GAAG,IAAI;MAClBR,OAAO,CAACQ,KAAK,GAAG,KAAK;MACrBX,IAAI,CAAC,OAAO,EAAEa,KAAK,CAAC;IACtB,CAAC;IACD,IAAME,UAAU,GAAG,SAAbA,UAAUA,CAAIC,KAAK,EAAEC,SAAS,EAAEC,IAAI,EAAK;MAC7C,IAAIA,IAAI,EAAE;QACR,OAAOA,IAAI,CAAC,CAAC;MACf;MACA,OAAO/D,YAAY,CAACe,IAAI,EAAE;QACxB,MAAM,EAAE8C,KAAK;QACb,MAAM,EAAEpB,KAAK,CAACP,QAAQ;QACtB,OAAO,EAAE4B,SAAS;QAClB,aAAa,EAAErB,KAAK,CAACJ;MACvB,CAAC,EAAE,IAAI,CAAC;IACV,CAAC;IACD,IAAM2B,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAA,EAAS;MAC9B,IAAIhB,OAAO,CAACQ,KAAK,IAAIf,KAAK,CAACH,WAAW,EAAE;QACtC,OAAOtC,YAAY,CAAC,KAAK,EAAE;UACzB,OAAO,EAAEoB,GAAG,CAAC,SAAS;QACxB,CAAC,EAAE,CAACwC,UAAU,CAACnB,KAAK,CAACF,WAAW,EAAEnB,GAAG,CAAC,cAAc,CAAC,EAAE0B,KAAK,CAACE,OAAO,CAAC,CAAC,CAAC;MACzE;MACA,IAAID,KAAK,CAACS,KAAK,IAAIf,KAAK,CAACN,SAAS,EAAE;QAClC,OAAOnC,YAAY,CAAC,KAAK,EAAE;UACzB,OAAO,EAAEoB,GAAG,CAAC,OAAO;QACtB,CAAC,EAAE,CAACwC,UAAU,CAACnB,KAAK,CAACL,SAAS,EAAEhB,GAAG,CAAC,YAAY,CAAC,EAAE0B,KAAK,CAACC,KAAK,CAAC,CAAC,CAAC;MACnE;IACF,CAAC;IACD,IAAMkB,WAAW,GAAG,SAAdA,WAAWA,CAAA,EAAS;MACxB,IAAIlB,KAAK,CAACS,KAAK,IAAI,CAACf,KAAK,CAACnB,GAAG,EAAE;QAC7B;MACF;MACA,IAAM4C,KAAK,GAAG;QACZ1C,GAAG,EAAEiB,KAAK,CAACjB,GAAG;QACd2C,KAAK,EAAE/C,GAAG,CAAC,KAAK,CAAC;QACjBgC,KAAK,EAAE;UACLgB,SAAS,EAAE3B,KAAK,CAAChB,GAAG;UACpB4C,cAAc,EAAE5B,KAAK,CAACf;QACxB;MACF,CAAC;MACD,IAAIe,KAAK,CAACR,QAAQ,EAAE;QAClB,OAAOvC,eAAe,CAACM,YAAY,CAAC,KAAK,EAAEJ,WAAW,CAAC;UACrD,KAAK,EAAEqD;QACT,CAAC,EAAEiB,KAAK,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC,CAACpE,iBAAiB,CAAC,MAAM,CAAC,EAAE2C,KAAK,CAACnB,GAAG,CAAC,CAAC,CAAC;MAC7D;MACA,OAAOtB,YAAY,CAAC,KAAK,EAAEJ,WAAW,CAAC;QACrC,KAAK,EAAE6C,KAAK,CAACnB,GAAG;QAChB,QAAQ,EAAEmC,MAAM;QAChB,SAAS,EAAEE;MACb,CAAC,EAAEO,KAAK,CAAC,EAAE,IAAI,CAAC;IAClB,CAAC;IACD,IAAMI,YAAY,GAAG,SAAfA,YAAYA,CAAAC,KAAA,EAEZ;MAAA,IADJC,EAAE,GAAAD,KAAA,CAAFC,EAAE;MAEF,IAAMC,KAAK,GAAG,SAARA,KAAKA,CAAA,EAAS;QAClB,IAAID,EAAE,KAAKvB,QAAQ,CAACO,KAAK,IAAIR,OAAO,CAACQ,KAAK,EAAE;UAC1CC,MAAM,CAAC,CAAC;QACV;MACF,CAAC;MACD,IAAIR,QAAQ,CAACO,KAAK,EAAE;QAClBiB,KAAK,CAAC,CAAC;MACT,CAAC,MAAM;QACLrE,QAAQ,CAACqE,KAAK,CAAC;MACjB;IACF,CAAC;IACD,IAAMC,eAAe,GAAG,SAAlBA,eAAeA,CAAAC,KAAA,EAEf;MAAA,IADJH,EAAE,GAAAG,KAAA,CAAFH,EAAE;MAEF,IAAIA,EAAE,KAAKvB,QAAQ,CAACO,KAAK,IAAI,CAACT,KAAK,CAACS,KAAK,EAAE;QACzCG,OAAO,CAAC,CAAC;MACX;IACF,CAAC;IACD,IAAIT,SAAS,IAAIxC,SAAS,EAAE;MAC1BwC,SAAS,CAAC0B,GAAG,CAAC,QAAQ,EAAEN,YAAY,CAAC;MACrCpB,SAAS,CAAC0B,GAAG,CAAC,OAAO,EAAEF,eAAe,CAAC;MACvCrE,eAAe,CAAC,YAAM;QACpB6C,SAAS,CAAC2B,IAAI,CAAC,QAAQ,EAAEP,YAAY,CAAC;QACtCpB,SAAS,CAAC2B,IAAI,CAAC,OAAO,EAAEH,eAAe,CAAC;MAC1C,CAAC,CAAC;IACJ;IACA,OAAO,YAAM;MACX,IAAII,EAAE;MACN,OAAO9E,YAAY,CAAC,KAAK,EAAE;QACzB,OAAO,EAAEoB,GAAG,CAAC;UACXO,KAAK,EAAEc,KAAK,CAACd,KAAK;UAClBE,KAAK,EAAEY,KAAK,CAACZ;QACf,CAAC,CAAC;QACF,OAAO,EAAEuB,KAAK,CAACI;MACjB,CAAC,EAAE,CAACS,WAAW,CAAC,CAAC,EAAED,iBAAiB,CAAC,CAAC,EAAE,CAACc,EAAE,GAAGhC,KAAK,CAACiC,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGD,EAAE,CAACE,IAAI,CAAClC,KAAK,CAAC,CAAC,CAAC;IAClG,CAAC;EACH;AACF,CAAC,CAAC;AACF,SACEN,aAAa,IAAIuC,OAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}