{"name": "babel-plugin-polyfill-corejs2", "version": "0.4.3", "description": "A Babel plugin to inject imports to core-js@2 polyfills", "repository": {"type": "git", "url": "https://github.com/babel/babel-polyfills.git", "directory": "packages/babel-plugin-polyfill-corejs2"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "exports": {".": [{"import": "./esm/index.mjs", "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "keywords": ["babel-plugin"], "dependencies": {"@babel/compat-data": "^7.17.7", "@babel/helper-define-polyfill-provider": "^0.4.0", "semver": "^6.1.1"}, "devDependencies": {"@babel/core": "^7.17.8", "@babel/helper-plugin-test-runner": "^7.16.7", "@babel/plugin-transform-for-of": "^7.16.7", "@babel/plugin-transform-modules-commonjs": "^7.17.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "gitHead": "476a0d277ae9bdf705fe2b2cce645125b3c774dc"}