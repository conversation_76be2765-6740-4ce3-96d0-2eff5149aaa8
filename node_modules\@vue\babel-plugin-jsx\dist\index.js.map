{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAAA,gDAAkC;AAElC,+DAAuC;AACvC,iFAAiD;AACjD,wEAAgF;AAEhF,4EAAkD;AAClD,sEAA6C;AAK7C,MAAM,MAAM,GAAG,CAAC,UAA+B,EAAE,EAAE;IACjD,IAAI,UAAU,GAAG,KAAK,CAAC;IACvB,UAAU,CAAC,QAAQ,CAAC;QAClB,UAAU,CAAC,IAAI;YACb,gBAAgB;YAChB,UAAU,GAAG,IAAI,CAAC;YAClB,IAAI,CAAC,IAAI,EAAE,CAAC;QACd,CAAC;QACD,WAAW,CAAC,IAAI;YACd,UAAU,GAAG,IAAI,CAAC;YAClB,IAAI,CAAC,IAAI,EAAE,CAAC;QACd,CAAC;KACF,CAAC,CAAC;IAEH,OAAO,UAAU,CAAC;AACpB,CAAC,CAAC;AAEF,MAAM,oBAAoB,GAAG,uBAAuB,CAAC;AAErD,kBAAe,CAAC,EAAE,KAAK,EAAoB,EAAE,EAAE,CAAC,CAAC;IAC/C,IAAI,EAAE,kBAAkB;IACxB,QAAQ,EAAE,2BAAS;IACnB,OAAO,gDACF,2BAAe,GACf,wBAAa,KAChB,OAAO,EAAE;YACP,KAAK,CAAC,IAAyB,EAAE,KAAY;gBAC3C,IAAI,MAAM,CAAC,IAAI,CAAC,EAAE;oBAChB,MAAM,WAAW,GAAG;wBAClB,aAAa;wBACb,UAAU;wBACV,kBAAkB;wBAClB,gBAAgB;wBAChB,OAAO;wBACP,cAAc;wBACd,YAAY;wBACZ,gBAAgB;wBAChB,aAAa;wBACb,YAAY;wBACZ,eAAe;wBACf,kBAAkB;wBAClB,YAAY;wBACZ,iBAAiB;wBACjB,SAAS;qBACV,CAAC;oBACF,IAAI,IAAA,gCAAQ,EAAC,IAAI,CAAC,EAAE;wBAClB,qCAAqC;wBACrC,MAAM,SAAS,GAAiC,EAAE,CAAC;wBACnD,WAAW,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;4BAC3B,KAAK,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,EAAE;gCACnB,IAAI,SAAS,CAAC,IAAI,CAAC,EAAE;oCACnB,OAAO,KAAK,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;iCACzC;gCACD,MAAM,UAAU,GAAG,IAAA,gCAAQ,EAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE;oCAC7C,mBAAmB,EAAE,IAAI;iCAC1B,CAAC,CAAC;gCACH,SAAS,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC;gCAC7B,OAAO,UAAU,CAAC;4BACpB,CAAC,CAAC,CAAC;wBACL,CAAC,CAAC,CAAC;wBACH,MAAM,EAAE,iBAAiB,GAAG,IAAI,EAAE,GAAG,KAAK,CAAC,IAAI,CAAC;wBAChD,IAAI,iBAAiB,EAAE;4BACrB,KAAK,CAAC,GAAG,CAAC,qCAAqC,EAAE,GAAG,EAAE;gCACpD,IAAI,SAAS,CAAC,aAAa,EAAE;oCAC3B,OAAO,SAAS,CAAC,aAAa,CAAC;iCAChC;gCACD,MAAM,EAAE,IAAI,EAAE,WAAW,EAAE,GAAG,KAAK,CAAC,GAAG,CACrC,SAAS,CACV,EAAkB,CAAC;gCACpB,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC;gCAC1D,MAAM,GAAG,GAAG,kBAAQ,CAAC,GAAG,CAAA;6BACX,MAAM,CAAC,IAAI;qHAC6E,WAAW;;iBAE/G,CAAC;gCACF,MAAM,UAAU,GAAI,IAAI,CAAC,GAAG,CAAC,MAAM,CAAgB;qCAChD,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,mBAAmB,EAAE,CAAC;qCACtC,GAAG,EAAE,CAAC;gCACT,IAAI,UAAU,EAAE;oCACd,UAAU,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;iCAC7B;gCACD,SAAS,CAAC,aAAa,GAAG,MAAM,CAAC;gCACjC,OAAO,MAAM,CAAC;4BAChB,CAAC,CAAC,CAAC;yBACJ;qBACF;yBAAM;wBACL,6BAA6B;wBAC7B,IAAI,UAAwB,CAAC;wBAC7B,WAAW,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;4BAC3B,KAAK,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,EAAE;gCACnB,IAAI,CAAC,UAAU,EAAE;oCACf,UAAU,GAAG,IAAA,oCAAY,EAAC,IAAI,EAAE,KAAK,EAAE;wCACrC,mBAAmB,EAAE,IAAI;qCAC1B,CAAC,CAAC;iCACJ;gCACD,OAAO,CAAC,CAAC,gBAAgB,CAAC,UAAU,EAAE,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;4BAC5D,CAAC,CAAC,CAAC;wBACL,CAAC,CAAC,CAAC;wBAEH,MAAM,OAAO,GAAiC,EAAE,CAAC;wBAEjD,MAAM,EAAE,iBAAiB,GAAG,IAAI,EAAE,GAAG,KAAK,CAAC,IAAI,CAAC;wBAChD,IAAI,iBAAiB,EAAE;4BACrB,KAAK,CAAC,GAAG,CAAC,qCAAqC,EAAE,GAAG,EAAE;gCACpD,IAAI,OAAO,CAAC,aAAa,EAAE;oCACzB,OAAO,OAAO,CAAC,aAAa,CAAC;iCAC9B;gCACD,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC;gCAC1D,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,GAAG,KAAK,CAAC,GAAG,CACtC,SAAS,CACV,EAAwB,CAAC;gCAC1B,MAAM,GAAG,GAAG,kBAAQ,CAAC,GAAG,CAAA;6BACX,MAAM,CAAC,IAAI;qHAC8E,UAA2B,CAAC,IAAI;;iBAErI,CAAC;gCAEF,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAe,CAAC;gCACjD,MAAM,UAAU,GAAG,SAAS;qCACzB,MAAM,CACL,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,qBAAqB,EAAE;uCAC3B,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CACzB,CAAC,CAAC,EAAE,EAAE,WAAC,OAAA,CAAA,MAAC,CAAC,CAAC,EAAmB,0CAAE,IAAI,MAAK,UAAU,CAAC,IAAI,CAAA,EAAA,CACxD,CACJ;qCACA,GAAG,EAAE,CAAC;gCACT,IAAI,UAAU,EAAE;oCACd,UAAU,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;iCAC7B;gCACD,OAAO,MAAM,CAAC;4BAChB,CAAC,CAAC,CAAC;yBACJ;qBACF;oBAED,MAAM,EACJ,IAAI,EAAE,EAAE,MAAM,GAAG,EAAE,EAAE,EACrB,IAAI,GACL,GAAG,KAAK,CAAC;oBAEV,IAAI,MAAM,EAAE;wBACV,KAAK,CAAC,GAAG,CAAC,aAAa,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC;qBACtD;oBAED,IAAI,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE;wBACrB,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE;4BACvC,MAAM,UAAU,GAAG,oBAAoB,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;4BAC5D,IAAI,UAAU,EAAE;gCACd,KAAK,CAAC,GAAG,CAAC,aAAa,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;6BAC7D;yBACF;qBACF;iBACF;YACH,CAAC;YACD,IAAI,CAAC,IAAyB;gBAC5B,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAe,CAAC;gBAC5C,MAAM,aAAa,GAAG,IAAI,GAAG,EAA6B,CAAC;gBAE3D,IAAI;qBACD,MAAM,CACL,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC,CAAC,mBAAmB,CAAC,QAAQ,CAAC,IAAI,CAAC;uBAC7C,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,KAAK,KAAK,CAC1C;qBACA,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE;oBACpB,MAAM,EAAE,UAAU,EAAE,GAAG,QAAQ,CAAC,IAA2B,CAAC;oBAC5D,IAAI,YAAY,GAAG,KAAK,CAAC;oBACzB,UAAU,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,EAAE;wBAC/B,IACE,CAAC,SAAS,CAAC,GAAG;+BACX,CAAC,CAAC,iBAAiB,CAAC,SAAS,CAAC;+BAC9B,CAAC,CAAC,YAAY,CAAC,SAAS,CAAC,QAAQ,CAAC,EACrC;4BACA,aAAa,CAAC,GAAG,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;4BACtD,YAAY,GAAG,IAAI,CAAC;yBACrB;oBACH,CAAC,CAAC,CAAC;oBACH,IAAI,YAAY,EAAE;wBAChB,QAAQ,CAAC,MAAM,EAAE,CAAC;qBACnB;gBACH,CAAC,CAAC,CAAC;gBAEL,MAAM,UAAU,GAAG,CAAC,GAAG,aAAa,CAAC,IAAI,EAAE,CAAC,CAAC,GAAG,CAC9C,CAAC,QAAQ,EAAE,EAAE,CAAC,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAE,CAC3C,CAAC;gBACF,IAAI,UAAU,CAAC,MAAM,EAAE;oBACrB,IAAI,CAAC,gBAAgB,CACnB,MAAM,EACN,CAAC,CAAC,iBAAiB,CAAC,UAAU,EAAE,CAAC,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CACxD,CAAC;iBACH;YACH,CAAC;SACF,GACF;CACF,CAAC,CAAC"}