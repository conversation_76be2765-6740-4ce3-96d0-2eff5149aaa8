{"ast": null, "code": "function _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return _typeof(key) === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (_typeof(input) !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (_typeof(res) !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\nimport { ref, getCurrentInstance, reactive } from 'vue';\nimport store from '@/store/index';\nimport { useI18n } from 'vue-i18n';\nimport { useRouter } from 'vue-router';\nimport { getHomeData, get_level_list, getdetailbyid } from '@/api/home/<USER>';\nimport langVue from '@/components/lang.vue';\nexport default {\n  components: {\n    langVue: langVue\n  },\n  setup: function setup() {\n    var _store$state$baseInfo, _store$state$baseInfo2, _userinfo$value, _userinfo$value2;\n    var _useRouter = useRouter(),\n      push = _useRouter.push;\n    // 语言切换\n    var _useI18n = useI18n(),\n      locale = _useI18n.locale,\n      t = _useI18n.t;\n    var a_content = ref('');\n    var showA = ref(false);\n    // 设置logo，需要网络logo就注释这个\n    // const logo = ref(store.state.baseInfo?.site_icon)\n    var logo = require('@/assets/images/home/<USER>');\n    var userinfo = ref(store.state.userinfo);\n    var currency = ref((_store$state$baseInfo = store.state.baseInfo) === null || _store$state$baseInfo === void 0 ? void 0 : _store$state$baseInfo.currency);\n    var xtTime = ref(store.state.xtTime);\n    var app_name = ref((_store$state$baseInfo2 = store.state.baseInfo) === null || _store$state$baseInfo2 === void 0 ? void 0 : _store$state$baseInfo2.app_name);\n    var noicetop = require('@/assets/images/home/<USER>');\n    if (!xtTime.value && (_userinfo$value = userinfo.value) !== null && _userinfo$value !== void 0 && _userinfo$value.tel) {\n      getdetailbyid(1).then(function (res) {\n        var _res$data;\n        a_content.value = (_res$data = res.data) === null || _res$data === void 0 ? void 0 : _res$data.content;\n        showA.value = true;\n        store.dispatch('changextTime', 'true');\n      });\n    }\n    store.dispatch('changefooCheck', 'home');\n    var setHeight = function setHeight(ref) {\n      var el = document.getElementById(ref);\n      return el !== null && el !== void 0 && el.offsetWidth ? (el === null || el === void 0 ? void 0 : el.offsetWidth) + 'px' : '';\n    };\n    // banner图轮播\n    var banner = ref([]);\n    // banner图轮播\n    var hyList = ref([]);\n    // 主info\n    var monney = ref('');\n    var mInfo = ref({});\n    var baseInfo = ref(store.state.baseInfo);\n    getHomeData().then(function (res) {\n      if (res.code === 0) {\n        banner.value = res.data.banner;\n        monney.value = res.data.balance;\n        mInfo.value = _objectSpread({}, res.data);\n      }\n    });\n    if ((_userinfo$value2 = userinfo.value) !== null && _userinfo$value2 !== void 0 && _userinfo$value2.tel) {\n      get_level_list().then(function (res) {\n        console.log(res);\n        if (res.code === 0) {\n          hyList.value = res.data;\n        }\n      });\n    }\n    var toDetails = function toDetails(id, title) {\n      push('/content?id=' + id + '&title=' + title);\n    };\n    var toRoute = function toRoute(path, param) {\n      if (path) {\n        push(path + (param ? '?param=' + param : ''));\n      }\n    };\n    var addLevel = function addLevel(row) {\n      var _mInfo$value;\n      if (row.level <= ((_mInfo$value = mInfo.value) === null || _mInfo$value === void 0 ? void 0 : _mInfo$value.level)) {\n        push('/obj');\n      } else {\n        push('/addlevel?vip=' + row.id);\n      }\n    };\n    var toDown = function toDown() {\n      console.log(baseInfo.value.app_url);\n      if (baseInfo.value.app_url) {\n        window.location.href = baseInfo.value.app_url;\n      }\n    };\n\n    // 合作伙伴链接\n    var partnerLinks = {\n      1: 'https://www.binance.com',\n      // 币安\n      2: 'https://www.okx.com',\n      // 欧易\n      3: 'https://www.obey.com',\n      // obey\n      4: 'https://www.amazon.com',\n      // Amazon\n      5: 'https://www.alibaba.com',\n      // 国际阿里巴巴\n      6: 'https://www.bestbuy.com',\n      // bestbuy\n      7: 'https://www.shopify.com',\n      // shopify\n      8: 'https://www.walmart.com',\n      // walmart\n      9: 'https://www.groupon.com',\n      // GROUPON\n      10: 'https://www.homedepot.com',\n      // thehomedepot\n      11: 'https://www.zalando.com',\n      // zalando\n      12: 'https://www.lotte.com' // lotte\n    };\n\n    // 跳转到合作伙伴网站\n    var goToPartner = function goToPartner(num) {\n      var url = partnerLinks[num];\n      if (url) {\n        window.open(url, '_blank');\n      }\n    };\n    return {\n      banner: banner,\n      monney: monney,\n      hyList: hyList,\n      mInfo: mInfo,\n      currency: currency,\n      toRoute: toRoute,\n      addLevel: addLevel,\n      toDetails: toDetails,\n      a_content: a_content,\n      showA: showA,\n      logo: logo,\n      userinfo: userinfo,\n      setHeight: setHeight,\n      app_name: app_name,\n      toDown: toDown,\n      goToPartner: goToPartner\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "getCurrentInstance", "reactive", "store", "useI18n", "useRouter", "getHomeData", "get_level_list", "getdetailbyid", "langVue", "components", "setup", "_store$state$baseInfo", "_store$state$baseInfo2", "_userinfo$value", "_userinfo$value2", "_useRouter", "push", "_useI18n", "locale", "t", "a_content", "showA", "logo", "require", "userinfo", "state", "currency", "baseInfo", "xtTime", "app_name", "noicetop", "value", "tel", "then", "res", "_res$data", "data", "content", "dispatch", "setHeight", "el", "document", "getElementById", "offsetWidth", "banner", "hyList", "<PERSON>ney", "mInfo", "code", "balance", "_objectSpread", "console", "log", "toDetails", "id", "title", "toRoute", "path", "param", "addLevel", "row", "_mInfo$value", "level", "toDown", "app_url", "window", "location", "href", "partnerLinks", "goTo<PERSON><PERSON>ner", "num", "url", "open"], "sources": ["C:\\Users\\<USER>\\Downloads\\Telegram Desktop\\vue3_3.0\\vue3_3.0\\src\\views\\index\\home.vue"], "sourcesContent": ["<template>\n    <div class=\"home\">\n        <van-nav-bar style=\"width: 100%;\">\n            <template  #left>\n                <img :src=\"logo\" class=\"logo\"  alt=\"\" style=\"width: 100%;height: 100%;\">\n            </template>\n            <template #right>\n                <!-- <img :src=\"$store.state.langImg\" class=\"lang\" height=\"18\" width=\"27\" alt=\"\"> -->\n                <!-- <lang-vue></lang-vue> -->\n            </template>\n        </van-nav-bar>\n        <!-- <div class=\"top\">\n        </div> -->\n        <div class=\"content\">\n            <!--  <div class=\"ftitle2\">\n\t\t\t  \t<img :src=\"require('@/assets/images/home/<USER>')\" width=\"18\" height=\"18\" alt=\"\" >\n\t\t\t   \n\t\t\t  </div>\n\t\t\t  -->\n\n\n            <div class=\"zq\">\n                <div class=\"text2\">\n                    <div class=\"text\" style=\"font-size: 15px;\">{{ $t('msg.get_monney') }}</div>\n                </div>\n                <div class=\"text3\">\n                    <img :src=\"require('@/assets/images/home/<USER>')\" width=\"25\" height=\"25\" alt=\"\">\n                    <span class=\"monney\">{{ mInfo.yon3 }}</span>\n                </div>\n\n            </div>\n        </div>\n\n\n        <!-- banner图 -->\n        <van-swipe class=\"my-swipe banner\" :autoplay=\"3000\" indicator-color=\"white\" round=\"25\"  v-if=\"banner.length > 0\" >\n            <van-swipe-item v-for=\"item in banner\" :key=\"item.id\" style=\"border-radius: 1rem\">\n                <img :src=\"item.image\" alt=\"\" class=\"img\" style=\"border-radius: 1rem\">\n            </van-swipe-item>\n        </van-swipe>\n\n        <!-- 交易轮播 -->\n        <div class=\"topBox\">\n            <img class=\"imgto\" :src=\"require('@/assets/images/home/<USER>')\" alt=\"\">\n\n            <vue3-seamless-scroll :list=\"mInfo.list || []\" class=\"scroll\" :waitTime=\"600\" :step=\"7\" :delay=\"2\" hover\n                :limitScrollNum=\"3\" :singleHeight=\"90\">\n                <div class=\"item\" v-for=\"(item, index) in mInfo.list || []\" :key=\"index\">\n                    <!--   <span class=\"left\">{{ item.addtime }}</span> -->\n                    <span class=\"right\">\n                        <span class=\"t\">{{ item.name.replace(/(.{0}).*(.{3})/, \"$1***$2\") }} {{ $t('msg.sryj') }}：{{ currency }} {{\n                            item.today_income }}</span>\n                        <!--   <span class=\"b\">{{item.name.replace(/(.{3}).*(.{3})/,\"$1******$2\")}}</span> -->\n                    </span>\n                </div>\n            </vue3-seamless-scroll>\n        </div>\n\n        <!-- 内容 -->\n        <div class=\"content\">\n            <!-- 开始赚钱 -->\n\n            <!--  <van-button type=\"primary\" class=\"kszq\" round block  @click=\"toRoute('/obj')\">{{$t('msg.kszq')}}</van-button> -->\n            <!-- 次导航 -->\n            <div class=\"ftitle\" style=\"font-weight: bold;\">\n\n                {{ $t('msg.wdfw') }}\n            </div>\n            <div class=\"n_nav\">\n                <div>\n                    <div class=\"li1 nav\"  @click=\"toDetails(6, $t('msg.bangz'))\" style=\"width: 100%;margin-bottom: 0;border-radius: 7px;background: #000;\">\n                        <div class=\"imge\">\n                            <img :src=\"require('@/assets/images/home/<USER>')\" width=\"12\" height=\"12\" alt=\"\"\n                                class=\"li_img\">\n                        </div>\n                        <div class=\"text\" style=\"color: #fff;\">{{ $t('msg.bangz') }}</div>\n                        <div class=\"imge1\">\n                            <!-- <img :src=\"require('@/assets/images/home/<USER>')\" width=\"15\" height=\"15\" alt=\"\"\n                                class=\"li_img\"> -->\n                                <van-icon color=\"#fff\" width=\"15\" height=\"15\" name=\"arrow\" />\n                        </div>\n                    </div>\n                    <div style=\"height: 0.5rem;\"></div>\n\n\n                    <div class=\"li2 nav\" @click=\"toDown()\" style=\"width: 100%;margin-bottom: 0;border-radius: 7px;background: #fe2c55;\">\n                        <div class=\"imge\">\n                            <img :src=\"require('@/assets/images/home/<USER>')\" width=\"12\" height=\"12\" alt=\"\" class=\"li_img\">\n                        </div>\n                        <div class=\"text\" style=\"color: #fff;\">{{ $t('msg.appDown') }}</div>\n                        <div class=\"imge1\">\n                            <van-icon color=\"#fff\" width=\"15\" height=\"15\" name=\"arrow\" />\n                        </div>\n                    </div>\n\n                \n\n                    <div style=\"height: 0.5rem;\"></div>\n                    <div class=\"li1 nav\"  @click=\"toDetails(7, $t('msg.about'))\" style=\"width: 100%;margin-bottom:0;border-radius: 7px;background: #000;\">\n                        <div class=\"imge\">\n                            <img :src=\"require('@/assets/images/home/<USER>')\" width=\"12\" height=\"12\" alt=\"\"\n                                class=\"li_img\">\n                        </div>\n                        <div class=\"text\" style=\"color: #fff;\">{{ $t('msg.about') }}</div>\n                        <div class=\"imge1\">\n                            <van-icon color=\"#fff\" width=\"15\" height=\"15\" name=\"arrow\" />\n                        </div>\n                    </div>\n\n                </div>\n\n                <div class=\"li nav servsen\" @click=\"toRoute('/tel')\" style=\"display: flex;flex-direction: column;margin-bottom: 0rem;padding:0;border-radius: 7px;\">\n                    <div style=\"height: 30%;display: flex;justify-content: center;align-items: center;color: #fff;font-weight: bold;word-break:keep-all;white-space:nowrap;\">{{ $t('msg.tel') }}  </div>\n                    <img class=\"serverimg\" :src=\"require('@/assets/images/home/<USER>')\" alt=\"\">\n\n                </div>\n\n\n            </div>\n\n           \n            <!-- 次导航 -->\n            <!-- <div class=\"earnings mt-2\" style=\"\">\n                <div class=\"earnings_Info\">\n                    <div class=\"vip_level ft-16\" v-if=\"userinfo?.tel\">\n                        <div style=\"flex: 2 1 0px;\">{{userinfo?.tel}}</div>\n                        <div style=\"flex: 1 1 0px; justify-content: center;\">{{userinfo?.invite_code}}</div>\n                    </div>\n                    <div class=\"balance mt-2 d-flex justify-between\">\n                        <span >{{$t('msg.zhye')}}</span><span >{{$t('msg.djje')}}</span>\n                    </div>\n                    <div class=\"balance-val d-flex justify-between\">\n                        <span >{{currency}}\n                            <span class=\"mm\">{{monney}}</span>\n                        </span>\n                        <span >{{currency}}\n                            <span class=\"mm\">{{mInfo.freeze_balance}}</span></span>\n                    </div>\n                    <div class=\"count-data\">\n                        <div class=\"flex-full\">\n                            <div >{{$t('msg.today_monney')}}</div>\n                            <div >{{currency}}{{mInfo.yon1}}</div>\n                        </div>\n                        <div class=\"flex-full\">\n                            <div >{{$t('msg.zrsy')}}</div>\n                            <div >{{currency}} {{mInfo.Yesterdaysearnings}}</div>\n                        </div>\n                    </div>\n                    <div class=\"count-data\">\n                        <div class=\"flex-full\">\n                            <div >{{$t('msg.get_monney')}}</div>\n                            <div class=\"two\">{{currency}}{{mInfo.yon3}}</div>\n                        </div>\n                        <div class=\"flex-full\">\n                            <div >{{$t('msg.tdsy')}}</div>\n                            <div class=\"two\">{{currency}} {{mInfo.Teambenefits}}</div>\n                        </div>\n                    </div>\n                </div>\n            </div> -->\n            <!-- banner图 -->\n            <!--  <van-row gutter=\"20\" class=\"hy_box\" v-if=\"hyList.length > 0\">\n                <van-col span=\"12\" v-for=\"item in hyList\" :key=\"item.id\" >\n                    <div class=\"box\" @click=\"addLevel(item)\">\n                        <div class=\"t\" >\n                            <img :src=\"item.pic\" class=\"img goods_img\" :id=\"'img'+item.id\" alt=\"\" :style=\"'max-height:'+ setHeight('img'+item.id)\">\n                            <div class=\"ts\">\n                                <span class=\"text\">{{item.name}}</span>\n                                <van-button type=\"primary\" class=\"txlevel\">\n                                    {{mInfo.level == item.level ? $t('msg.now_level') : mInfo.level < item.level ?  $t('msg.join') : ''}}\n                                </van-button>\n                            </div>\n                        </div>\n                        <div class=\"b\">\n                            <div class=\"sub\">{{$t('msg.sjje')}}\n                                <span class=\"span\">{{currency}}{{item.num}}</span>\n                            </div>\n                            <div class=\"sub\">{{$t('msg.yonj')}}\n                                <span class=\"span\">{{((item.bili || 0)*100).toFixed(1)}}%</span>\n                            </div>\n                        </div>\n                    </div>\n                </van-col>\n            </van-row> -->\n            <!-- 会员收益 -->\n            <!-- <div class=\"ftitle\">\n                {{$t('msg.get_monney')}}\n            </div> -->\n            <!-- <div class=\"m_nav\">\n                <div class=\"li\">\n                    <div class=\"monney\">{{currency}}{{mInfo.yon1}}</div>\n                    <div class=\"text\">{{$t('msg.today_monney')}}</div>\n                </div>\n                <div class=\"li\">\n                    <div class=\"monney\">{{currency}}{{mInfo.yon2}}</div>\n                    <div class=\"text\">{{$t('msg.today_yonj')}}</div>\n                </div>\n                <div class=\"li\">\n                    <div class=\"monney\">{{currency}}{{mInfo.yon3}}</div>\n                    <div class=\"text\">{{$t('msg.get_monney')}}</div>\n                </div>\n                <div class=\"li\">\n                    <div class=\"monney\">{{currency}}{{mInfo.yon4}}</div>\n                    <div class=\"text\">{{$t('msg.ylb')}}</div>\n                </div>\n            </div> -->\n            <!-- 会员收益 -->\n\n\n\n            <!-- 公司简介 -->\n            <!--    <div class=\"n_nav\">\n                <div class=\"li nav\" @click=\"toDetails(2,$t('msg.gsjj'))\">\n                    <img :src=\"require('@/assets/images/news/poster_1.png')\" class=\"img nav_img\" alt=\"\" >\n                    <div class=\"text\">{{$t('msg.gsjj')}}</div>\n                </div>\n                <div class=\"li nav\" @click=\"toDetails(3,$t('msg.gzms'))\">\n                    <img :src=\"require('@/assets/images/news/poster_2.png')\" class=\"img nav_img\" alt=\"\">\n                    <div class=\"text\">{{$t('msg.gzms')}}</div>\n                </div>\n                <div class=\"li nav\" @click=\"toDetails(4,$t('msg.dlhz'))\">\n                    <img :src=\"require('@/assets/images/news/poster_3.png')\" class=\"img nav_img\" alt=\"\">\n                    <div class=\"text\">{{$t('msg.dlhz')}}</div>\n                </div>\n                <div class=\"li nav\" @click=\"toDetails(12,$t('msg.qyzz'))\">\n                    <img :src=\"require('@/assets/images/news/poster_4.png')\" class=\"img nav_img\" alt=\"\">\n                    <div class=\"text\">{{$t('msg.qyzz')}}</div>\n                </div>\n            </div> -->\n            <!-- 会员收益 -->\n            <div class=\"ftitle\" style=\"font-weight: bold;\">\n                {{ $t('msg.hzhb') }}\n            </div>\n            <div class=\"hzhb\">\n                <img :src=\"require('@/assets/images/news/' + num + '.png')\" v-for=\"num in 6\" :key=\"num\" class=\"img hzhb_img\"\n                    alt=\"\" @click=\"goToPartner(num)\">\n\n            </div>\n            <div class=\"hzhb\">\n                <img :src=\"require('@/assets/images/news/img/img/' + num + '.png')\" v-for=\"num in 6\" :key=\"num+6\"\n                    class=\"img hzhb_img\" alt=\"\" @click=\"goToPartner(num+6)\">\n\n            </div>\n        </div>\n        <van-dialog v-model:show=\"showA\" width=\"90%\" :showConfirmButton=\"false\">\n            <div class=\"lang_box\">\n                <div class=\"lang_title\">{{ $t('msg.System_notification') }}</div>\n                <!-- <img :src=\"require('@/assets/images/register/lang_bg.png')\" class=\"lang_bg\" /> -->\n                <div class=\"content\">\n                    <!-- <img :src=\"require('@/assets/images/register/qiu.png')\" class=\"qiu\" /> -->\n                    <div class=\"langs\">\n                        <span class=\"li ctn\" v-html=\"a_content\"></span>\n                    </div>\n                    <div class=\"btn\">\n                        <van-button round block type=\"primary\" @click=\"showA = false\">\n                            {{ $t('msg.yes') }}\n                        </van-button>\n                    </div>\n                </div>\n            </div>\n        </van-dialog>\n    </div>\n</template>\n<script>\nimport { ref, getCurrentInstance, reactive } from 'vue';\nimport store from '@/store/index'\nimport { useI18n } from 'vue-i18n'\nimport { useRouter } from 'vue-router';\nimport { getHomeData, get_level_list, getdetailbyid } from '@/api/home/<USER>'\nimport langVue from '@/components/lang.vue'\nexport default {\n    components: { langVue },\n    setup() {\n        const { push } = useRouter();\n        // 语言切换\n        const { locale, t } = useI18n()\n        const a_content = ref('')\n        const showA = ref(false)\n        // 设置logo，需要网络logo就注释这个\n        // const logo = ref(store.state.baseInfo?.site_icon)\n        const logo = require('@/assets/images/home/<USER>')\n        const userinfo = ref(store.state.userinfo)\n        const currency = ref(store.state.baseInfo?.currency)\n        const xtTime = ref(store.state.xtTime)\n        const app_name = ref(store.state.baseInfo?.app_name)\n        const noicetop = require('@/assets/images/home/<USER>')\n        if (!xtTime.value && userinfo.value?.tel) {\n            getdetailbyid(1).then(res => {\n                a_content.value = res.data?.content\n                showA.value = true\n                store.dispatch('changextTime', 'true')\n            })\n\n        }\n\n\n        store.dispatch('changefooCheck', 'home')\n\n        const setHeight = (ref) => {\n            const el = document.getElementById(ref)\n            return el?.offsetWidth ? el?.offsetWidth + 'px' : ''\n        }\n        // banner图轮播\n        const banner = ref([])\n        // banner图轮播\n        const hyList = ref([])\n        // 主info\n        const monney = ref('')\n        const mInfo = ref({})\n        const baseInfo = ref(store.state.baseInfo)\n        getHomeData().then(res => {\n            if (res.code === 0) {\n                banner.value = res.data.banner\n                monney.value = res.data.balance\n                mInfo.value = { ...res.data }\n            }\n        })\n        if (userinfo.value?.tel) {\n            get_level_list().then(res => {\n                console.log(res)\n                if (res.code === 0) {\n                    hyList.value = res.data\n                }\n            })\n        }\n        const toDetails = (id, title) => {\n            push('/content?id=' + id + '&title=' + title)\n        }\n        const toRoute = (path, param) => {\n            if (path) {\n                push(path + (param ? '?param=' + param : ''))\n            }\n        }\n        const addLevel = (row) => {\n            if (row.level <= mInfo.value?.level) {\n                push('/obj')\n            } else {\n                push('/addlevel?vip=' + row.id)\n            }\n        }\n        const toDown = () => {\n            console.log(baseInfo.value.app_url)\n            if (baseInfo.value.app_url) {\n                window.location.href = baseInfo.value.app_url\n            }\n        }\n        \n        // 合作伙伴链接\n        const partnerLinks = {\n            1: 'https://www.binance.com', // 币安\n            2: 'https://www.okx.com', // 欧易\n            3: 'https://www.obey.com', // obey\n            4: 'https://www.amazon.com', // Amazon\n            5: 'https://www.alibaba.com', // 国际阿里巴巴\n            6: 'https://www.bestbuy.com', // bestbuy\n            7: 'https://www.shopify.com', // shopify\n            8: 'https://www.walmart.com', // walmart\n            9: 'https://www.groupon.com', // GROUPON\n            10: 'https://www.homedepot.com', // thehomedepot\n            11: 'https://www.zalando.com', // zalando\n            12: 'https://www.lotte.com' // lotte\n        }\n        \n        // 跳转到合作伙伴网站\n        const goToPartner = (num) => {\n            const url = partnerLinks[num];\n            if (url) {\n                window.open(url, '_blank');\n            }\n        }\n        \n        return { \n            banner, monney, hyList, mInfo, currency, toRoute, addLevel, toDetails, \n            a_content, showA, logo, userinfo, setHeight, app_name, toDown, goToPartner \n        }\n    }\n}\n</script>\n<style lang=\"scss\" scoped>\n.serverimg {\n    width: 96%;\n    height: 57%;\n\n}\n\n.servsen {}\n\n.logo {\n    width: 745px\n}\n\n.scrolltop {\n    width: 500px\n}\n\n.topBox {\n    position: relative\n}\n\n.imgto {\n    width: 35px !important;\n    height: 35px !important;\n    position: absolute !important;\n    top: 20px;\n    left: 62px;\n}\n\n@import '@/styles/theme.scss';\n\n.home {\n    position: relative;\n    width: 100vw;\n    overflow-x: hidden;\n    overflow-y: auto;\n    display: block !important;\n    // background-image: url('~@/assets/images/home/<USER>');\n\n    background-repeat: no-repeat;\n    background-size: 100% 100%;\n    height: 100%;\n    min-height: 100vh;\n    width: 100%;\n    padding-bottom: 95px;\n    background-color: #fff;\n\n    :deep(.van-nav-bar) {\n        // position: sticky;\n        // top: 0;\n        // background-color: #d4dff5;\n        \n        color: #333;\n        padding: 0px 0;\n\n        \n        .van-nav-bar__left {\n            width: 100%;\n            padding: 0;\n\n            .van-icon {\n                color: #fff;\n            }\n        }\n\n        .van-nav-bar__title {\n            color: #333;\n            font-weight: 600;\n            font-size: 28px;\n        }\n    }\n\n    .top {\n        height: 62px;\n        position: absolute;\n        top: 25px;\n        left: 0;\n        width: 100%;\n        display: flex;\n        justify-content: space-between;\n        padding: 0 25px;\n        z-index: 888;\n\n        .lang {\n            height: 30px;\n        }\n    }\n\n    .lang_box {\n        width: 100%;\n        position: relative;\n        padding-top: 60px;\n\n        .lang_title {\n            margin-bottom: 40px;\n        }\n\n        .lang_bg {\n            width: 100%;\n            position: absolute;\n            top: 0;\n            left: 0;\n        }\n\n        .content {\n            position: relative;\n            z-index: 1;\n            text-align: center;\n\n            .qiu {\n                width: 175px;\n                border-radius: 50%;\n                box-shadow: $shadow;\n                margin-bottom: 6px;\n            }\n\n            .langs {\n                margin-bottom: 15px;\n\n                .li {\n                    padding: 24px;\n                    display: block;\n                    text-align: left;\n                    margin-bottom: 10px;\n                    max-height: 500px;\n                    overflow: auto;\n\n                    &.ctn {\n                        padding: 24px;\n                    }\n\n                    &.check {\n                        box-shadow: $shadow;\n                    }\n\n                    .img {\n                        width: 80px;\n                        margin-right: 34px;\n                        vertical-align: middle;\n                    }\n\n                    .text {\n                        font-size: 26px;\n                        color: $textColor;\n                    }\n                }\n            }\n\n            .btn {\n                padding: 50px 54px 50px;\n            }\n        }\n    }\n\n    .hy_box {\n        width: 100%;\n        color: #333;\n        overflow: hidden;\n        position: relative;\n\n        .box {\n            // background-image: url('~@/assets/images/home/<USER>');\n            // background-size: 100% 100%;\n            // border-radius: 10px;\n            background-color: #fff;\n            border-radius: 12px;\n            margin-bottom: 20PX;\n            padding: 24px;\n        }\n\n        .t {\n            margin-bottom: 18px;\n            // height: 200px;\n            display: flex;\n            width: 100%;\n            position: relative;\n\n            .img {\n                width: 100%;\n            }\n\n            .ts {\n                width: 100%;\n                position: absolute;\n                top: 0;\n                left: 0;\n                display: flex;\n                justify-content: space-between;\n            }\n\n            .text {\n                display: inline-block;\n                border-radius: 30PX;\n                text-align: center;\n                vertical-align: middle;\n                padding: 0 15px;\n                border-radius: 15px;\n                font-weight: 700;\n                background: red;\n                color: #fff;\n                font-size: 27px;\n                height: 25PX;\n                line-height: 25PX;\n            }\n\n            .txlevel {\n                border: none;\n                background-color: initial;\n                color: red;\n                line-height: 1;\n                height: auto;\n                text-align: right;\n                width: auto;\n                font-size: 0.8rem;\n                font-weight: 600;\n\n                .van-button__content {\n                    justify-content: right;\n                }\n            }\n        }\n\n        .b {\n            font-size: 18px;\n            display: flex;\n\n            .sub {\n                color: orange;\n                font-size: 0.5rem;\n                font-weight: 600;\n\n                &:first-child {\n                    flex: 1;\n                }\n\n                &:last-child {\n                    width: 60PX;\n                    text-align: right;\n\n                    .span {\n                        width: 100%;\n                        height: 40px;\n                        line-height: 40px;\n                        font-size: 36px;\n                        background-color: green;\n                        color: #fff;\n                        text-align: center;\n                    }\n                }\n\n                .span {\n                    display: block;\n                    font-size: 24px;\n                    margin-top: 15px;\n                }\n            }\n        }\n    }\n\n    .my-swipe {\n        .van-swipe-item {\n            padding: 0 30px;\n            overflow: hidden;\n        }\n\n        .img {\n            width: 100%;\n            height: 250px;\n            border-radius: 0px;\n        }\n    }\n\n    .content {\n        padding: 0 30px;\n        text-align: left;\n\n        .zq {\n            height: 70px;\n            margin-bottom: 30px;\n            border-radius: 20px;\n            display: flex;\n            align-items: center;\n            justify-content: space-between;\n            padding: 0 30px;\n            background-color: #fe2c55;\n            font-size: 30px;\n            // margin-top: 3.333333vw;\n            box-shadow: $shadow;\n            position: relative;\n            overflow: hidden;\n            // padding: 3.333333vw 1.333333vw;\n            display: flex;\n\n\n            .text2 {\n                justify-content: center;\n                display: flex;\n                flex-direction: column;\n                align-items: center;\n                flex-direction: row;\n                padding: 0 10px;\n                height: 45px;\n                // background: rgba(18,163,192,.32);\n                border-radius: 6px;\n                margin-right: 2.933333vw;\n            }\n\n            .text3 {\n                display: flex;\n                justify-content: center;\n                align-items: center;\n\n                .monney {\n                    font-size: 4.666667vw;\n                    background-image: -webkit-linear-gradient(left, #a570fb, #2620ce);\n                    -webkit-background-clip: text;\n                    -webkit-text-fill-color: #fff;\n                    font-weight: 600;\n                    padding: 0px 3.333333vw;\n                }\n\n                img {\n                    height: 30px;\n                    width: 30px;\n                }\n\n            }\n\n            .text {\n                text-align: center;\n                white-space: nowrap;\n                font-size: 12px;\n                font-family: PingFangSC-Regular, PingFang SC;\n\n                color: #fff;\n\n                font-weight: 600;\n\n            }\n\n            &::after {\n                position: absolute;\n                content: '';\n                height: 8px;\n                width: 100%;\n                left: 0;\n                bottom: 0;\n                //background-image:linear-gradient(90deg,#a570fb,#2620ce)\n            }\n        }\n\n        .kszq {\n            margin-top: 40px;\n            font-size: 44px;\n            height: 88px;\n            line-height: 88px;\n        }\n    }\n\n    .n_nav {\n        display: flex;\n        justify-content: space-between;\n        margin-top: 40px;\n        flex-wrap: nowrap;\n\n        &.jj {\n            margin: 20px 0 60px;\n            padding: 0 38px;\n\n            .li {\n                .img {\n                    width: 40px;\n                    margin-bottom: 8px;\n                }\n\n                .text {\n                    font-size: 18px;\n                    color: $sub_theme;\n                }\n            }\n        }\n\n        .li {\n            text-align: center;\n\n            &.nav {\n                // background-image: url('~@/assets/images/home/<USER>');\n                background-color: #fe2c55 !important;\n                // background-size: 100% 100%;\n                width: 48%;\n                text-align: left;\n                margin-bottom: -9.8vw;\n                padding: 24px 0 24px 24px;\n                border-radius: 36px;\n                background-color: #fe2c55;\n                display: flex;\n\n                .text {\n                    margin-left: 15px;\n                    display: flex;\n                    flex-direction: column;\n                    justify-content: space-around;\n                    font-size: 26px;\n                }\n\n                .imge {\n                    width: 40PX;\n                    display: flex;\n                    flex-direction: column;\n                    justify-content: center;\n                }\n\n                .img {\n                    width: 40PX;\n                }\n            }\n\n            .img {\n                width: 106px;\n                vertical-align: middle;\n            }\n\n            .text {\n                // white-space: nowrap;\n                font-size: 24px;\n                color: #333;\n            }\n        }\n\n        .li1 {\n            text-align: center;\n\n            &.nav {\n                width: 45%;\n                text-align: left;\n                margin-bottom: 15.2vw;\n                padding: 2.2vw 0px 2.2vw 3.2vw;\n                border-radius: 24px;\n                background-color: #fce6e7;\n                display: flex;\n\n                .text {\n                    margin-left: 15px;\n                    display: flex;\n                    flex-direction: column;\n                    justify-content: space-around;\n                    font-size: 26px;\n                }\n\n                .imge {\n                    width: 30PX;\n                    display: flex;\n                    flex-direction: column;\n                    justify-content: center;\n                }\n\n                .imge1 {\n                    width: 30PX;\n                    margin-left: 11vw;\n                    display: flex;\n                    flex-direction: column;\n                    justify-content: center;\n\n                    img {\n                        width: 25px;\n                        height: 25px;\n                    }\n                }\n\n                .img {\n                    width: 40PX;\n                }\n            }\n\n            .img {\n                width: 106px;\n                vertical-align: middle;\n            }\n\n            .text {\n                // white-space: nowrap;\n                font-size: 24px;\n                color: #333;\n            }\n        }\n\n        .li2 {\n            text-align: center;\n\n            &.nav {\n                width: 45%;\n                text-align: left;\n                margin-bottom: 1.2vw;\n                padding: 2.2vw 0px 2.2vw 3.2vw;\n                border-radius: 24px;\n                background-color: #f6ecca;\n                display: flex;\n\n                .text {\n                    margin-left: 15px;\n                    display: flex;\n                    flex-direction: column;\n                    justify-content: space-around;\n                    font-size: 26px;\n\n                }\n\n                .imge {\n                    width: 30PX;\n                    display: flex;\n                    flex-direction: column;\n                    justify-content: center;\n                }\n\n                .imge1 {\n                    width: 30PX;\n                    margin-left: 11vw;\n                    display: flex;\n                    flex-direction: column;\n                    justify-content: center;\n\n                    img {\n                        width: 25px;\n                        height: 25px;\n                    }\n                }\n\n                .img {\n                    width: 40PX;\n                }\n            }\n\n            .img {\n                width: 106px;\n                vertical-align: middle;\n            }\n\n            .text {\n                // white-space: nowrap;\n                font-size: 24px;\n                color: #333;\n            }\n        }\n    }\n\n    .ftitle {\n        height: 4.533333vw;\n        line-height: 4.533333vw;\n        color: #000000;\n        margin: 3.333333vw 0;\n        white-space: nowrap;\n        font-size: 4.026667vw;\n        font-weight: 500;\n        // &::before{\n        //     content: '';\n        //     display: inline-block;\n        //     height: 100%;\n        //     width: 10px;\n        //     margin-right: 12px;\n        //     background-color: $theme;\n        //     vertical-align: middle;\n        // }\n    }\n\n    .ftitle2 {\n        height: 34px;\n        line-height: 21.533333vw;\n        font-size: 30px;\n        color: #333;\n        margin: 0px 0;\n        white-space: nowrap;\n        padding: 0px 1.333333vw;\n\n    }\n\n    .earnings {\n        background: url('~@/assets/images/news/balanceBG.png') no-repeat;\n        background-size: 100% 100%;\n        padding: 24px;\n        margin-bottom: 24px;\n\n        .vip_level {\n            height: 30px;\n            display: flex;\n            box-sizing: border-box;\n            font-size: 20px;\n            color: #333;\n\n            &>div {\n                flex: 3;\n                display: flex;\n                justify-content: space-between;\n                line-height: 30px;\n                padding-left: 5px;\n                font-size: 30px;\n                // font-weight: 500;\n                color: #000;\n\n                &:first-child {\n                    border-right: 1px solid #adadad;\n                    padding-right: 5px;\n                    padding-left: 0;\n                }\n            }\n        }\n\n        .balance {\n            margin: 20px 0;\n            font-size: 30px;\n            // font-weight: 500;\n            color: #000;\n            display: flex;\n            justify-content: space-between;\n        }\n\n        .balance-val {\n            margin: 10px 0 15px;\n            font-size: 50px;\n            font-family: PingFangSC-Semibold, PingFang SC;\n            font-weight: 600;\n            color: #000;\n            display: flex;\n            justify-content: space-between;\n\n            span {\n\n                // width: 60PX;\n                .mm {\n                    display: block;\n                }\n            }\n        }\n\n        .count-data {\n            display: flex;\n            margin-top: 15px;\n\n            .flex-full {\n                flex: 1;\n                color: #000;\n                font-size: 24px;\n\n                .two {\n                    display: block;\n                    font-weight: 600;\n                }\n            }\n        }\n    }\n\n    .m_nav {\n        display: flex;\n        flex-wrap: wrap;\n        justify-content: space-between;\n\n        .li {\n            width: 327px;\n            margin-bottom: 36px;\n            box-shadow: $shadow;\n            display: flex;\n            flex-direction: column;\n            justify-content: center;\n            font-size: 28px;\n            color: $textColor;\n            padding: 44px 0;\n            text-align: center;\n            border-radius: 10px;\n\n            .monney {\n                font-size: 30px;\n                background-image: -webkit-linear-gradient(left, #856cf0, #a35df1);\n                -webkit-background-clip: text;\n                -webkit-text-fill-color: transparent;\n                font-weight: 600;\n                margin-bottom: 15px;\n            }\n        }\n    }\n\n    .scroll {\n        overflow: hidden;\n        height: 33PX;\n        width: 92%;\n\n        height: 70px;\n        background-color: #e6e6e6;\n        border-radius: 10px;\n\n\n        padding: 0 10px;\n\n        box-shadow: 0 2px 10px 0 rgba(126, 148, 194, .1);\n        line-height: 28px;\n        border-radius: 9px;\n        margin: 30px auto;\n        overflow: hidden;\n\n        // display: flex;\n        .item {\n            width: 100%;\n            height: 26PX;\n            margin-top: 19PX;\n            // background-image: url('@/assets/images/home/<USER>');\n            background-size: 100% 100%;\n            display: flex;\n            // justify-content: space-between;\n            padding: 14PX 20px 12PX 0;\n            // box-shadow: $shadow;\n\n            .left {\n                line-height: 50PX;\n                padding: 0 50px;\n                font-size: 26px;\n                color: #333;\n            }\n\n            .right {\n                display: flex;\n                flex-direction: column;\n                justify-content: center;\n                padding-left: 20px;\n\n                //border-left: 15px solid #eee;\n                .t {\n                    color: #333;\n                    font-size: 26px;\n                    margin-bottom: 35px;\n                    padding: 0px 7.333333vw;\n                }\n\n                .b {\n                    color: #000000;\n                    font-size: 24px;\n                }\n            }\n        }\n    }\n\n    .hzhb {\n        display: flex;\n        justify-content: space-between;\n        flex-wrap: wrap;\n\n        .img {\n            width: 2.6rem;\n            height: 2.6rem;\n            box-shadow: $shadow;\n            border-radius: 10px;\n            margin-bottom: 30px;\n            cursor: pointer;\n            transition: all 0.3s ease;\n            \n            &:hover {\n                transform: scale(1.05);\n                box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);\n            }\n        }\n    }\n}\n\n.goods_img {\n    width: 100%;\n    height: 7rem;\n}\n\n.li_img {\n    width: 1.5rem;\n    height: 1.5rem;\n}\n\n.nav_img {\n    width: 2.2rem;\n    height: 2.2rem;\n}\n\n.hzhb_img {\n\n    height: 3.2rem;\n}</style>"], "mappings": ";;;;;;AAwQA,SAASA,GAAG,EAAEC,kBAAkB,EAAEC,QAAO,QAAS,KAAK;AACvD,OAAOC,KAAI,MAAO,eAAc;AAChC,SAASC,OAAM,QAAS,UAAS;AACjC,SAASC,SAAQ,QAAS,YAAY;AACtC,SAASC,WAAW,EAAEC,cAAc,EAAEC,aAAY,QAAS,kBAAiB;AAC5E,OAAOC,OAAM,MAAO,uBAAsB;AAC1C,eAAe;EACXC,UAAU,EAAE;IAAED,OAAM,EAANA;EAAQ,CAAC;EACvBE,KAAK,WAAAA,MAAA,EAAG;IAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,eAAA,EAAAC,gBAAA;IACJ,IAAAC,UAAA,GAAiBX,SAAS,CAAC,CAAC;MAApBY,IAAG,GAAAD,UAAA,CAAHC,IAAG;IACX;IACA,IAAAC,QAAA,GAAsBd,OAAO,CAAC;MAAtBe,MAAM,GAAAD,QAAA,CAANC,MAAM;MAAEC,CAAA,GAAAF,QAAA,CAAAE,CAAA;IAChB,IAAMC,SAAQ,GAAIrB,GAAG,CAAC,EAAE;IACxB,IAAMsB,KAAI,GAAItB,GAAG,CAAC,KAAK;IACvB;IACA;IACA,IAAMuB,IAAG,GAAIC,OAAO,CAAC,iCAAiC;IACtD,IAAMC,QAAO,GAAIzB,GAAG,CAACG,KAAK,CAACuB,KAAK,CAACD,QAAQ;IACzC,IAAME,QAAO,GAAI3B,GAAG,EAAAY,qBAAA,GAACT,KAAK,CAACuB,KAAK,CAACE,QAAQ,cAAAhB,qBAAA,uBAApBA,qBAAA,CAAsBe,QAAQ;IACnD,IAAME,MAAK,GAAI7B,GAAG,CAACG,KAAK,CAACuB,KAAK,CAACG,MAAM;IACrC,IAAMC,QAAO,GAAI9B,GAAG,EAAAa,sBAAA,GAACV,KAAK,CAACuB,KAAK,CAACE,QAAQ,cAAAf,sBAAA,uBAApBA,sBAAA,CAAsBiB,QAAQ;IACnD,IAAMC,QAAO,GAAIP,OAAO,CAAC,gCAAgC;IACzD,IAAI,CAACK,MAAM,CAACG,KAAI,KAAAlB,eAAA,GAAKW,QAAQ,CAACO,KAAK,cAAAlB,eAAA,eAAdA,eAAA,CAAgBmB,GAAG,EAAE;MACtCzB,aAAa,CAAC,CAAC,CAAC,CAAC0B,IAAI,CAAC,UAAAC,GAAE,EAAK;QAAA,IAAAC,SAAA;QACzBf,SAAS,CAACW,KAAI,IAAAI,SAAA,GAAID,GAAG,CAACE,IAAI,cAAAD,SAAA,uBAARA,SAAA,CAAUE,OAAM;QAClChB,KAAK,CAACU,KAAI,GAAI,IAAG;QACjB7B,KAAK,CAACoC,QAAQ,CAAC,cAAc,EAAE,MAAM;MACzC,CAAC;IAEL;IAGApC,KAAK,CAACoC,QAAQ,CAAC,gBAAgB,EAAE,MAAM;IAEvC,IAAMC,SAAQ,GAAI,SAAZA,SAAQA,CAAKxC,GAAG,EAAK;MACvB,IAAMyC,EAAC,GAAIC,QAAQ,CAACC,cAAc,CAAC3C,GAAG;MACtC,OAAOyC,EAAE,aAAFA,EAAE,eAAFA,EAAE,CAAEG,WAAU,GAAI,CAAAH,EAAE,aAAFA,EAAE,uBAAFA,EAAE,CAAEG,WAAU,IAAI,IAAG,GAAI,EAAC;IACvD;IACA;IACA,IAAMC,MAAK,GAAI7C,GAAG,CAAC,EAAE;IACrB;IACA,IAAM8C,MAAK,GAAI9C,GAAG,CAAC,EAAE;IACrB;IACA,IAAM+C,MAAK,GAAI/C,GAAG,CAAC,EAAE;IACrB,IAAMgD,KAAI,GAAIhD,GAAG,CAAC,CAAC,CAAC;IACpB,IAAM4B,QAAO,GAAI5B,GAAG,CAACG,KAAK,CAACuB,KAAK,CAACE,QAAQ;IACzCtB,WAAW,CAAC,CAAC,CAAC4B,IAAI,CAAC,UAAAC,GAAE,EAAK;MACtB,IAAIA,GAAG,CAACc,IAAG,KAAM,CAAC,EAAE;QAChBJ,MAAM,CAACb,KAAI,GAAIG,GAAG,CAACE,IAAI,CAACQ,MAAK;QAC7BE,MAAM,CAACf,KAAI,GAAIG,GAAG,CAACE,IAAI,CAACa,OAAM;QAC9BF,KAAK,CAAChB,KAAI,GAAAmB,aAAA,KAAShB,GAAG,CAACE,IAAG,CAAE;MAChC;IACJ,CAAC;IACD,KAAAtB,gBAAA,GAAIU,QAAQ,CAACO,KAAK,cAAAjB,gBAAA,eAAdA,gBAAA,CAAgBkB,GAAG,EAAE;MACrB1B,cAAc,CAAC,CAAC,CAAC2B,IAAI,CAAC,UAAAC,GAAE,EAAK;QACzBiB,OAAO,CAACC,GAAG,CAAClB,GAAG;QACf,IAAIA,GAAG,CAACc,IAAG,KAAM,CAAC,EAAE;UAChBH,MAAM,CAACd,KAAI,GAAIG,GAAG,CAACE,IAAG;QAC1B;MACJ,CAAC;IACL;IACA,IAAMiB,SAAQ,GAAI,SAAZA,SAAQA,CAAKC,EAAE,EAAEC,KAAK,EAAK;MAC7BvC,IAAI,CAAC,cAAa,GAAIsC,EAAC,GAAI,SAAQ,GAAIC,KAAK;IAChD;IACA,IAAMC,OAAM,GAAI,SAAVA,OAAMA,CAAKC,IAAI,EAAEC,KAAK,EAAK;MAC7B,IAAID,IAAI,EAAE;QACNzC,IAAI,CAACyC,IAAG,IAAKC,KAAI,GAAI,SAAQ,GAAIA,KAAI,GAAI,EAAE,CAAC;MAChD;IACJ;IACA,IAAMC,QAAO,GAAI,SAAXA,QAAOA,CAAKC,GAAG,EAAK;MAAA,IAAAC,YAAA;MACtB,IAAID,GAAG,CAACE,KAAI,MAAAD,YAAA,GAAKd,KAAK,CAAChB,KAAK,cAAA8B,YAAA,uBAAXA,YAAA,CAAaC,KAAK,GAAE;QACjC9C,IAAI,CAAC,MAAM;MACf,OAAO;QACHA,IAAI,CAAC,gBAAe,GAAI4C,GAAG,CAACN,EAAE;MAClC;IACJ;IACA,IAAMS,MAAK,GAAI,SAATA,MAAKA,CAAA,EAAU;MACjBZ,OAAO,CAACC,GAAG,CAACzB,QAAQ,CAACI,KAAK,CAACiC,OAAO;MAClC,IAAIrC,QAAQ,CAACI,KAAK,CAACiC,OAAO,EAAE;QACxBC,MAAM,CAACC,QAAQ,CAACC,IAAG,GAAIxC,QAAQ,CAACI,KAAK,CAACiC,OAAM;MAChD;IACJ;;IAEA;IACA,IAAMI,YAAW,GAAI;MACjB,CAAC,EAAE,yBAAyB;MAAE;MAC9B,CAAC,EAAE,qBAAqB;MAAE;MAC1B,CAAC,EAAE,sBAAsB;MAAE;MAC3B,CAAC,EAAE,wBAAwB;MAAE;MAC7B,CAAC,EAAE,yBAAyB;MAAE;MAC9B,CAAC,EAAE,yBAAyB;MAAE;MAC9B,CAAC,EAAE,yBAAyB;MAAE;MAC9B,CAAC,EAAE,yBAAyB;MAAE;MAC9B,CAAC,EAAE,yBAAyB;MAAE;MAC9B,EAAE,EAAE,2BAA2B;MAAE;MACjC,EAAE,EAAE,yBAAyB;MAAE;MAC/B,EAAE,EAAE,uBAAsB,CAAE;IAChC;;IAEA;IACA,IAAMC,WAAU,GAAI,SAAdA,WAAUA,CAAKC,GAAG,EAAK;MACzB,IAAMC,GAAE,GAAIH,YAAY,CAACE,GAAG,CAAC;MAC7B,IAAIC,GAAG,EAAE;QACLN,MAAM,CAACO,IAAI,CAACD,GAAG,EAAE,QAAQ,CAAC;MAC9B;IACJ;IAEA,OAAO;MACH3B,MAAM,EAANA,MAAM;MAAEE,MAAM,EAANA,MAAM;MAAED,MAAM,EAANA,MAAM;MAAEE,KAAK,EAALA,KAAK;MAAErB,QAAQ,EAARA,QAAQ;MAAE8B,OAAO,EAAPA,OAAO;MAAEG,QAAQ,EAARA,QAAQ;MAAEN,SAAS,EAATA,SAAS;MACrEjC,SAAS,EAATA,SAAS;MAAEC,KAAK,EAALA,KAAK;MAAEC,IAAI,EAAJA,IAAI;MAAEE,QAAQ,EAARA,QAAQ;MAAEe,SAAS,EAATA,SAAS;MAAEV,QAAQ,EAARA,QAAQ;MAAEkC,MAAM,EAANA,MAAM;MAAEM,WAAU,EAAVA;IACnE;EACJ;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}