{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, toDisplayString as _toDisplayString, createElementVNode as _createElementVNode, withCtx as _withCtx, createTextVNode as _createTextVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, pushScopeId as _pushScopeId, popScopeId as _popScopeId } from \"vue\";\nvar _withScopeId = function _withScopeId(n) {\n  return _pushScopeId(\"data-v-d33ea1f4\"), n = n(), _popScopeId(), n;\n};\nvar _hoisted_1 = {\n  class: \"homes\"\n};\nvar _hoisted_2 = {\n  class: \"select\"\n};\nvar _hoisted_3 = {\n  class: \"buttons\"\n};\nvar _hoisted_4 = {\n  class: \"gore\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_login_top = _resolveComponent(\"login-top\");\n  var _component_van_field = _resolveComponent(\"van-field\");\n  var _component_van_cell_group = _resolveComponent(\"van-cell-group\");\n  var _component_van_button = _resolveComponent(\"van-button\");\n  var _component_van_form = _resolveComponent(\"van-form\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_login_top, {\n    \"hide-lang\": \"\",\n    title: _ctx.$t('msg.register'),\n    \"left-arrow\": \"\"\n  }, null, 8, [\"title\"]), _createVNode(_component_van_form, {\n    onSubmit: $setup.onSubmit,\n    style: {\n      \"z-index\": \"11\"\n    }\n  }, {\n    default: _withCtx(function () {\n      return [_createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", {\n        onClick: _cache[0] || (_cache[0] = function ($event) {\n          return _ctx.$router.push({\n            path: '/area',\n            query: {\n              data: 2\n            }\n          });\n        }),\n        class: \"selects\",\n        name: \"qv\"\n      }, \"+\" + _toDisplayString($setup.qv), 1)]), _createVNode(_component_van_cell_group, {\n        inset: \"\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_van_field, {\n            \"label-width\": \"100\",\n            class: \"zdy ent\",\n            modelValue: $setup.tel,\n            \"onUpdate:modelValue\": _cache[1] || (_cache[1] = function ($event) {\n              return $setup.tel = $event;\n            }),\n            name: \"tel\",\n            label: _ctx.$t('msg.phone'),\n            placeholder: _ctx.$t('msg.phone'),\n            rules: [{\n              required: true,\n              message: _ctx.$t('msg.input_phone')\n            }]\n          }, null, 8, [\"modelValue\", \"label\", \"placeholder\", \"rules\"]), _createVNode(_component_van_field, {\n            \"label-width\": \"100\",\n            modelValue: $setup.invite_code,\n            \"onUpdate:modelValue\": _cache[2] || (_cache[2] = function ($event) {\n              return $setup.invite_code = $event;\n            }),\n            name: \"invite_code\",\n            label: _ctx.$t('msg.code'),\n            placeholder: _ctx.$t('msg.code'),\n            rules: [{\n              required: true,\n              message: _ctx.$t('msg.input_code')\n            }]\n          }, null, 8, [\"modelValue\", \"label\", \"placeholder\", \"rules\"]), _createVNode(_component_van_field, {\n            \"label-width\": \"100\",\n            modelValue: $setup.pwd,\n            \"onUpdate:modelValue\": _cache[3] || (_cache[3] = function ($event) {\n              return $setup.pwd = $event;\n            }),\n            type: \"password\",\n            name: \"pwd\",\n            label: _ctx.$t('msg.pwd'),\n            placeholder: _ctx.$t('msg.pwd'),\n            rules: [{\n              required: true,\n              message: _ctx.$t('msg.input_pwd')\n            }]\n          }, null, 8, [\"modelValue\", \"label\", \"placeholder\", \"rules\"]), _createVNode(_component_van_field, {\n            \"label-width\": \"100\",\n            modelValue: $setup.pwd2,\n            \"onUpdate:modelValue\": _cache[4] || (_cache[4] = function ($event) {\n              return $setup.pwd2 = $event;\n            }),\n            type: \"password\",\n            name: \"pwd2\",\n            label: _ctx.$t('msg.true_pwd'),\n            placeholder: _ctx.$t('msg.true_pwd'),\n            rules: [{\n              required: true,\n              message: _ctx.$t('msg.input_true_pwd')\n            }]\n          }, null, 8, [\"modelValue\", \"label\", \"placeholder\", \"rules\"]), _createVNode(_component_van_field, {\n            \"label-width\": \"100\",\n            modelValue: $setup.depositPwd,\n            \"onUpdate:modelValue\": _cache[5] || (_cache[5] = function ($event) {\n              return $setup.depositPwd = $event;\n            }),\n            name: \"depositPwd\",\n            label: _ctx.$t('msg.tx_pwd'),\n            placeholder: _ctx.$t('msg.tx_pwd'),\n            rules: [{\n              required: true,\n              message: _ctx.$t('msg.input_t_pwd')\n            }]\n          }, null, 8, [\"modelValue\", \"label\", \"placeholder\", \"rules\"])];\n        }),\n        _: 1\n      }), _createElementVNode(\"div\", _hoisted_3, [_createVNode(_component_van_button, {\n        round: \"5\",\n        block: \"\",\n        plain: \"\",\n        type: \"primary\",\n        \"native-type\": \"submit\",\n        style: {\n          \"border-radius\": \"10px\"\n        }\n      }, {\n        default: _withCtx(function () {\n          return [_createTextVNode(_toDisplayString(_ctx.$t('msg.register1')), 1)];\n        }),\n        _: 1\n      }), _createElementVNode(\"div\", _hoisted_4, [_createElementVNode(\"span\", {\n        class: \"gores\",\n        onClick: _cache[6] || (_cache[6] = function ($event) {\n          return _ctx.$router.push({\n            path: '/login'\n          });\n        })\n      }, _toDisplayString(_ctx.$t('msg.login')), 1)])])];\n    }),\n    _: 1\n  }, 8, [\"onSubmit\"])]);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}