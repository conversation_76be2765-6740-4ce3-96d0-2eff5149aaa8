{"ast": null, "code": "function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nfunction _iterableToArrayLimit(arr, i) { var _i = null == arr ? null : \"undefined\" != typeof Symbol && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (null != _i) { var _s, _e, _x, _r, _arr = [], _n = !0, _d = !1; try { if (_x = (_i = _i.call(arr)).next, 0 === i) { if (Object(_i) !== _i) return; _n = !1; } else for (; !(_n = (_s = _x.call(_i)).done) && (_arr.push(_s.value), _arr.length !== i); _n = !0); } catch (err) { _d = !0, _e = err; } finally { try { if (!_n && null != _i.return && (_r = _i.return(), Object(_r) !== _r)) return; } finally { if (_d) throw _e; } } return _arr; } }\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\nimport { createVNode as _createVNode } from \"vue\";\nimport { defineComponent } from \"vue\";\nimport { createNamespace, unknownProp } from \"../utils/index.mjs\";\nimport { Tag } from \"../tag/index.mjs\";\nimport { Icon } from \"../icon/index.mjs\";\nimport { Cell } from \"../cell/index.mjs\";\nimport { Radio } from \"../radio/index.mjs\";\nimport { Button } from \"../button/index.mjs\";\nimport { RadioGroup } from \"../radio-group/index.mjs\";\nvar _createNamespace = createNamespace(\"contact-list\"),\n  _createNamespace2 = _slicedToArray(_createNamespace, 3),\n  name = _createNamespace2[0],\n  bem = _createNamespace2[1],\n  t = _createNamespace2[2];\nvar contactListProps = {\n  list: Array,\n  addText: String,\n  modelValue: unknownProp,\n  defaultTagText: String\n};\nvar stdin_default = defineComponent({\n  name: name,\n  props: contactListProps,\n  emits: [\"add\", \"edit\", \"select\", \"update:modelValue\"],\n  setup: function setup(props, _ref) {\n    var emit = _ref.emit;\n    var renderItem = function renderItem(item, index) {\n      var onClick = function onClick() {\n        emit(\"update:modelValue\", item.id);\n        emit(\"select\", item, index);\n      };\n      var renderRightIcon = function renderRightIcon() {\n        return _createVNode(Radio, {\n          \"class\": bem(\"radio\"),\n          \"name\": item.id,\n          \"iconSize\": 16\n        }, null);\n      };\n      var renderEditIcon = function renderEditIcon() {\n        return _createVNode(Icon, {\n          \"name\": \"edit\",\n          \"class\": bem(\"edit\"),\n          \"onClick\": function onClick(event) {\n            event.stopPropagation();\n            emit(\"edit\", item, index);\n          }\n        }, null);\n      };\n      var renderContent = function renderContent() {\n        var nodes = [\"\".concat(item.name, \"\\uFF0C\").concat(item.tel)];\n        if (item.isDefault && props.defaultTagText) {\n          nodes.push(_createVNode(Tag, {\n            \"type\": \"danger\",\n            \"round\": true,\n            \"class\": bem(\"item-tag\")\n          }, {\n            default: function _default() {\n              return [props.defaultTagText];\n            }\n          }));\n        }\n        return nodes;\n      };\n      return _createVNode(Cell, {\n        \"key\": item.id,\n        \"isLink\": true,\n        \"center\": true,\n        \"class\": bem(\"item\"),\n        \"valueClass\": bem(\"item-value\"),\n        \"onClick\": onClick\n      }, {\n        icon: renderEditIcon,\n        value: renderContent,\n        \"right-icon\": renderRightIcon\n      });\n    };\n    return function () {\n      return _createVNode(\"div\", {\n        \"class\": bem()\n      }, [_createVNode(RadioGroup, {\n        \"modelValue\": props.modelValue,\n        \"class\": bem(\"group\")\n      }, {\n        default: function _default() {\n          return [props.list && props.list.map(renderItem)];\n        }\n      }), _createVNode(\"div\", {\n        \"class\": [bem(\"bottom\"), \"van-safe-area-bottom\"]\n      }, [_createVNode(Button, {\n        \"round\": true,\n        \"block\": true,\n        \"type\": \"danger\",\n        \"class\": bem(\"add\"),\n        \"text\": props.addText || t(\"addContact\"),\n        \"onClick\": function onClick() {\n          return emit(\"add\");\n        }\n      }, null)])]);\n    };\n  }\n});\nexport { stdin_default as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}