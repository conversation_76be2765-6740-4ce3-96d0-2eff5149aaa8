{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, toDisplayString as _toDisplayString, createElementVNode as _createElementVNode, vShow as _vShow, normalizeStyle as _normalizeStyle, withDirectives as _withDirectives, Transition as _Transition, withCtx as _withCtx, openBlock as _openBlock, createBlock as _createBlock, pushScopeId as _pushScopeId, popScopeId as _popScopeId } from \"vue\";\nvar _withScopeId = function _withScopeId(n) {\n  return _pushScopeId(\"data-v-032da2b2\"), n = n(), _popScopeId(), n;\n};\nvar _hoisted_1 = {\n  class: \"text\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_Icon = _resolveComponent(\"Icon\");\n  return _openBlock(), _createBlock(_Transition, {\n    name: \"down\"\n  }, {\n    default: _withCtx(function () {\n      return [_withDirectives(_createElementVNode(\"div\", {\n        class: \"my-message\",\n        style: _normalizeStyle($setup.style[$props.type])\n      }, [_createCommentVNode(\" 上面绑定的是样式 \"), _createCommentVNode(\" 不同提示图标会变 \"), _createCommentVNode(\" <i class=\\\"iconfont\\\" :class=\\\"[style[type].icon]\\\"></i> \"), _createVNode(_component_Icon, {\n        name: $setup.style[$props.type].icon,\n        size: \"18\"\n      }, null, 8 /* PROPS */, [\"name\"]), _createElementVNode(\"span\", _hoisted_1, _toDisplayString($props.message), 1 /* TEXT */)], 4 /* STYLE */), [[_vShow, $setup.isShow]])];\n    }),\n    _: 1 /* STABLE */\n  });\n}", "map": {"version": 3, "names": ["class", "_createBlock", "_Transition", "name", "_createElementVNode", "style", "_normalizeStyle", "$setup", "$props", "type", "_createCommentVNode", "_createVNode", "_component_Icon", "icon", "size", "_hoisted_1", "_toDisplayString", "message", "isShow"], "sources": ["C:\\Users\\<USER>\\Desktop\\vue3_3.0\\src\\components\\message.vue"], "sourcesContent": ["<template>\r\n  <Transition name=\"down\">\r\n    <div class=\"my-message\" :style=\"style[type]\" v-show='isShow'>\r\n      <!-- 上面绑定的是样式 -->\r\n      <!-- 不同提示图标会变 -->\r\n      <!-- <i class=\"iconfont\" :class=\"[style[type].icon]\"></i> -->\r\n      <Icon :name=\"style[type].icon\" size=\"18\" />\r\n      <span class=\"text\">{{message}}</span>\r\n    </div>\r\n  </Transition>\r\n</template>\r\n<script>\r\nimport { onMounted, ref } from 'vue'\r\nimport { Icon } from 'vant'\r\nexport default {\r\n  name: 'myMessage',\r\n  components:{Icon},\r\n  props: {\r\n    message: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    type: {\r\n      type: String,\r\n      // warn 警告  error 错误  success 成功\r\n      default: 'warn'\r\n    }\r\n  },\r\n  setup () {\r\n    // 定义一个对象，包含三种情况的样式，对象key就是类型字符串\r\n    const style = {\r\n      warn: {\r\n        icon: 'icon-warning',\r\n        color: '#E6A23C',\r\n        backgroundColor: 'rgb(253, 246, 236)',\r\n        borderColor: 'rgb(250, 236, 216)'\r\n      },\r\n      error: {\r\n        icon: 'clear',\r\n        color: '#F56C6C',\r\n        backgroundColor: 'rgb(254, 240, 240)',\r\n        borderColor: 'rgb(253, 226, 226)'\r\n      },\r\n      success: {\r\n        icon: 'checked',\r\n        color: '#67C23A',\r\n        backgroundColor: 'rgb(240, 249, 235)',\r\n        borderColor: 'rgb(225, 243, 216)'\r\n      }\r\n    }\r\n    // 控制动画\r\n    const isShow = ref(false)\r\n    // 组件模板渲染成功后触发\r\n    onMounted(() => {\r\n      isShow.value = true\r\n    })\r\n    return { style, isShow }\r\n  }\r\n}\r\n</script>\r\n<style scoped lang=\"scss\">\r\n.down {\r\n  &-enter {\r\n    &-from {\r\n      transform: translate3d(0, -75px, 0);\r\n      opacity: 0;\r\n    }\r\n    &-active {\r\n      transition: all 0.5s;\r\n    }\r\n    &-to {\r\n      transform: none;\r\n      opacity: 1;\r\n    }\r\n  }\r\n}\r\n.my-message {\r\n  width: 90%;\r\n  // height: 60px;\r\n  position: fixed;\r\n  z-index: 9999;\r\n  left: 50%;\r\n  top: 25px;\r\n  transform: translate(-50%);\r\n  line-height: 1.2;\r\n  padding: 12px 25px;\r\n  border: 1px solid #e4e4e4;\r\n  background: #f5f5f5;\r\n  color: #999;\r\n  border-radius: 4px;\r\n  i {\r\n    margin-right: 12px;\r\n    vertical-align: middle;\r\n  }\r\n  .text {\r\n    vertical-align: middle;\r\n  }\r\n}\r\n</style>"], "mappings": ";;;;;EAOYA,KAAK,EAAC;AAAM;;;uBANtBC,YAAA,CAQaC,WAAA;IARDC,IAAI,EAAC;EAAM;sBACrB;MAAA,OAMM,C,gBANNC,mBAAA,CAMM;QANDJ,KAAK,EAAC,YAAY;QAAEK,KAAK,EAAAC,eAAA,CAAEC,MAAA,CAAAF,KAAK,CAACG,MAAA,CAAAC,IAAI;UACxCC,mBAAA,cAAiB,EACjBA,mBAAA,cAAiB,EACjBA,mBAAA,8DAA6D,EAC7DC,YAAA,CAA2CC,eAAA;QAApCT,IAAI,EAAEI,MAAA,CAAAF,KAAK,CAACG,MAAA,CAAAC,IAAI,EAAEI,IAAI;QAAEC,IAAI,EAAC;yCACpCV,mBAAA,CAAqC,QAArCW,UAAqC,EAAAC,gBAAA,CAAhBR,MAAA,CAAAS,OAAO,iB,4BALuBV,MAAA,CAAAW,MAAM,E"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}