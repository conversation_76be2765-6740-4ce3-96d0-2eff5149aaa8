"use strict";(self["webpackChunkdeom"]=self["webpackChunkdeom"]||[]).push([[147],{147:function(e,t,r){r.r(t),r.d(t,{default:function(){return S}});var n=r(3396),l=r(7139),o={class:"homes"},u={class:"buttons"},i={class:"gore"};function a(e,t,r,a,s,d){var p=(0,n.up)("login-top"),c=(0,n.up)("van-field"),m=(0,n.up)("van-cell-group"),f=(0,n.up)("van-button"),g=(0,n.up)("van-form");return(0,n.wg)(),(0,n.iD)("div",o,[(0,n.Wm)(p,{"hide-lang":"",title:e.$t("msg.register"),"left-arrow":""},null,8,["title"]),(0,n.Wm)(g,{onSubmit:a.onSubmit,style:{"z-index":"11"}},{default:(0,n.w5)((function(){return[(0,n.Wm)(m,{inset:""},{default:(0,n.w5)((function(){return[(0,n.Wm)(c,{"label-width":"100",class:"zdy",modelValue:a.userName,"onUpdate:modelValue":t[0]||(t[0]=function(e){return a.userName=e}),name:"userName",label:e.$t("msg.username"),placeholder:e.$t("msg.input_username"),rules:[{required:!0,message:e.$t("msg.input_username")}]},null,8,["modelValue","label","placeholder","rules"]),(0,n.Wm)(c,{"label-width":"100",modelValue:a.pwd,"onUpdate:modelValue":t[1]||(t[1]=function(e){return a.pwd=e}),type:"password",name:"pwd",label:e.$t("msg.pwd"),placeholder:e.$t("msg.pwd"),rules:[{required:!0,message:e.$t("msg.input_pwd")}]},null,8,["modelValue","label","placeholder","rules"]),(0,n.Wm)(c,{"label-width":"100",modelValue:a.pwd2,"onUpdate:modelValue":t[2]||(t[2]=function(e){return a.pwd2=e}),type:"password",name:"pwd2",label:e.$t("msg.true_pwd"),placeholder:e.$t("msg.true_pwd"),rules:[{required:!0,message:e.$t("msg.input_true_pwd")}]},null,8,["modelValue","label","placeholder","rules"]),(0,n.Wm)(c,{"label-width":"100",modelValue:a.depositPwd,"onUpdate:modelValue":t[3]||(t[3]=function(e){return a.depositPwd=e}),name:"depositPwd",label:e.$t("msg.tx_pwd"),placeholder:e.$t("msg.tx_pwd"),rules:[{required:!0,message:e.$t("msg.input_t_pwd")}]},null,8,["modelValue","label","placeholder","rules"]),(0,n.Wm)(c,{"label-width":"100",modelValue:a.invite_code,"onUpdate:modelValue":t[4]||(t[4]=function(e){return a.invite_code=e}),name:"invite_code",label:e.$t("msg.code"),placeholder:e.$t("msg.code"),rules:[{required:!0,message:e.$t("msg.input_code")}]},null,8,["modelValue","label","placeholder","rules"])]})),_:1}),(0,n._)("div",u,[(0,n.Wm)(f,{round:"5",block:"",plain:"",type:"primary","native-type":"submit",style:{"border-radius":"10px"}},{default:(0,n.w5)((function(){return[(0,n.Uk)((0,l.zw)(e.$t("msg.register1")),1)]})),_:1}),(0,n._)("div",i,[(0,n._)("span",{class:"gores",onClick:t[5]||(t[5]=function(t){return e.$router.push({path:"/login"})})},(0,l.zw)(e.$t("msg.login")),1)])])]})),_:1},8,["onSubmit"])])}var s=r(1981),d=r(4870),p=r(4239),c=r(2483),m=r(4925),f=r(9733);function g(e){return g="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},g(e)}function b(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function w(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?b(Object(r),!0).forEach((function(t){v(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):b(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function v(e,t,r){return t=y(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function y(e){var t=h(e,"string");return"symbol"===g(t)?t:String(t)}function h(e,t){if("object"!==g(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==g(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}var _={name:"HomeView",components:{loginTop:s.Z},setup:function(){var e,t,r=(0,c.yj)(),l=(0,c.tv)(),o=l.push,u=(0,n.FN)(),i=u.proxy,a=(0,d.iH)("");null!==(e=r.query)&&void 0!==e&&e.invite_code&&(a.value=null===(t=r.query)||void 0===t?void 0:t.invite_code);var s=(0,d.iH)(""),g=(0,d.iH)(""),b=(0,d.iH)(""),v=(0,d.iH)(""),y=function(e){if(e.pwd!=e.pwd2)return f.F.fail("两次输入的密码不正确"),!1;var t=JSON.parse(JSON.stringify(e));(0,m.gX)(t).then((function(e){if(0===e.code){i.$Message({type:"success",message:e.info});var r={userName:s.value,pwd:g.value};(0,m.x4)(r).then((function(e){if(0===e.code){p.Z.dispatch("changetoken",e.token),p.Z.dispatch("changeuserinfo",e.userinfo||{}),i.$Message({type:"success",message:e.info});var r=w(w({},t),{checked:!0});p.Z.dispatch("changeUser",r),o("/")}else i.$Message({type:"error",message:e.info})}))}else i.$Message({type:"error",message:e.info})}))};return{userName:s,pwd:g,pwd2:b,depositPwd:v,invite_code:a,onSubmit:y}}},$=r(89);const O=(0,$.Z)(_,[["render",a],["__scopeId","data-v-2ff708ba"]]);var S=O}}]);