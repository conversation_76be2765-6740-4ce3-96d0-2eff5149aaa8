{"ast": null, "code": "function _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return _typeof(key) === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (_typeof(input) !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (_typeof(res) !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\nfunction _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nfunction _iterableToArrayLimit(arr, i) { var _i = null == arr ? null : \"undefined\" != typeof Symbol && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (null != _i) { var _s, _e, _x, _r, _arr = [], _n = !0, _d = !1; try { if (_x = (_i = _i.call(arr)).next, 0 === i) { if (Object(_i) !== _i) return; _n = !1; } else for (; !(_n = (_s = _x.call(_i)).done) && (_arr.push(_s.value), _arr.length !== i); _n = !0); } catch (err) { _d = !0, _e = err; } finally { try { if (!_n && null != _i.return && (_r = _i.return(), Object(_r) !== _r)) return; } finally { if (_d) throw _e; } } return _arr; } }\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\nimport { createVNode as _createVNode } from \"vue\";\nimport { defineComponent } from \"vue\";\nimport { truthProp, makeStringProp, createNamespace } from \"../utils/index.mjs\";\nvar _createNamespace = createNamespace(\"divider\"),\n  _createNamespace2 = _slicedToArray(_createNamespace, 2),\n  name = _createNamespace2[0],\n  bem = _createNamespace2[1];\nvar dividerProps = {\n  dashed: Boolean,\n  hairline: truthProp,\n  contentPosition: makeStringProp(\"center\")\n};\nvar stdin_default = defineComponent({\n  name: name,\n  props: dividerProps,\n  setup: function setup(props, _ref) {\n    var slots = _ref.slots;\n    return function () {\n      var _a;\n      return _createVNode(\"div\", {\n        \"role\": \"separator\",\n        \"class\": bem(_defineProperty({\n          dashed: props.dashed,\n          hairline: props.hairline\n        }, \"content-\".concat(props.contentPosition), !!slots.default))\n      }, [(_a = slots.default) == null ? void 0 : _a.call(slots)]);\n    };\n  }\n});\nexport { stdin_default as default };", "map": {"version": 3, "names": ["createVNode", "_createVNode", "defineComponent", "truthProp", "makeStringProp", "createNamespace", "_createNamespace", "_createNamespace2", "_slicedToArray", "name", "bem", "dividerProps", "dashed", "Boolean", "hairline", "contentPosition", "stdin_default", "props", "setup", "_ref", "slots", "_a", "_defineProperty", "concat", "default", "call"], "sources": ["C:/Users/<USER>/Desktop/vue3_3.0/node_modules/vant/es/divider/Divider.mjs"], "sourcesContent": ["import { createVNode as _createVNode } from \"vue\";\nimport { defineComponent } from \"vue\";\nimport { truthProp, makeStringProp, createNamespace } from \"../utils/index.mjs\";\nconst [name, bem] = createNamespace(\"divider\");\nconst dividerProps = {\n  dashed: <PERSON><PERSON><PERSON>,\n  hairline: truthProp,\n  contentPosition: makeStringProp(\"center\")\n};\nvar stdin_default = defineComponent({\n  name,\n  props: dividerProps,\n  setup(props, {\n    slots\n  }) {\n    return () => {\n      var _a;\n      return _createVNode(\"div\", {\n        \"role\": \"separator\",\n        \"class\": bem({\n          dashed: props.dashed,\n          hairline: props.hairline,\n          [`content-${props.contentPosition}`]: !!slots.default\n        })\n      }, [(_a = slots.default) == null ? void 0 : _a.call(slots)]);\n    };\n  }\n});\nexport {\n  stdin_default as default\n};\n"], "mappings": ";;;;;;;;;;AAAA,SAASA,WAAW,IAAIC,YAAY,QAAQ,KAAK;AACjD,SAASC,eAAe,QAAQ,KAAK;AACrC,SAASC,SAAS,EAAEC,cAAc,EAAEC,eAAe,QAAQ,oBAAoB;AAC/E,IAAAC,gBAAA,GAAoBD,eAAe,CAAC,SAAS,CAAC;EAAAE,iBAAA,GAAAC,cAAA,CAAAF,gBAAA;EAAvCG,IAAI,GAAAF,iBAAA;EAAEG,GAAG,GAAAH,iBAAA;AAChB,IAAMI,YAAY,GAAG;EACnBC,MAAM,EAAEC,OAAO;EACfC,QAAQ,EAAEX,SAAS;EACnBY,eAAe,EAAEX,cAAc,CAAC,QAAQ;AAC1C,CAAC;AACD,IAAIY,aAAa,GAAGd,eAAe,CAAC;EAClCO,IAAI,EAAJA,IAAI;EACJQ,KAAK,EAAEN,YAAY;EACnBO,KAAK,WAAAA,MAACD,KAAK,EAAAE,IAAA,EAER;IAAA,IADDC,KAAK,GAAAD,IAAA,CAALC,KAAK;IAEL,OAAO,YAAM;MACX,IAAIC,EAAE;MACN,OAAOpB,YAAY,CAAC,KAAK,EAAE;QACzB,MAAM,EAAE,WAAW;QACnB,OAAO,EAAES,GAAG,CAAAY,eAAA;UACVV,MAAM,EAAEK,KAAK,CAACL,MAAM;UACpBE,QAAQ,EAAEG,KAAK,CAACH;QAAQ,cAAAS,MAAA,CACZN,KAAK,CAACF,eAAe,GAAK,CAAC,CAACK,KAAK,CAACI,OAAO,CACtD;MACH,CAAC,EAAE,CAAC,CAACH,EAAE,GAAGD,KAAK,CAACI,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGH,EAAE,CAACI,IAAI,CAACL,KAAK,CAAC,CAAC,CAAC;IAC9D,CAAC;EACH;AACF,CAAC,CAAC;AACF,SACEJ,aAAa,IAAIQ,OAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}