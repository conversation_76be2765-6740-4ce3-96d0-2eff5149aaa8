{"ast": null, "code": "function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nfunction _iterableToArrayLimit(arr, i) { var _i = null == arr ? null : \"undefined\" != typeof Symbol && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (null != _i) { var _s, _e, _x, _r, _arr = [], _n = !0, _d = !1; try { if (_x = (_i = _i.call(arr)).next, 0 === i) { if (Object(_i) !== _i) return; _n = !1; } else for (; !(_n = (_s = _x.call(_i)).done) && (_arr.push(_s.value), _arr.length !== i); _n = !0); } catch (err) { _d = !0, _e = err; } finally { try { if (!_n && null != _i.return && (_r = _i.return(), Object(_r) !== _r)) return; } finally { if (_d) throw _e; } } return _arr; } }\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\nimport { createVNode as _createVNode } from \"vue\";\nimport { defineComponent } from \"vue\";\nimport { extend, numericProp, unknownProp, makeStringProp, createNamespace } from \"../utils/index.mjs\";\nimport { Popup } from \"../popup/index.mjs\";\nimport { popupSharedProps } from \"../popup/shared.mjs\";\nvar _createNamespace = createNamespace(\"notify\"),\n  _createNamespace2 = _slicedToArray(_createNamespace, 2),\n  name = _createNamespace2[0],\n  bem = _createNamespace2[1];\nvar notifyProps = extend({}, popupSharedProps, {\n  type: makeStringProp(\"danger\"),\n  color: String,\n  message: numericProp,\n  position: makeStringProp(\"top\"),\n  className: unknownProp,\n  background: String,\n  lockScroll: Boolean\n});\nvar stdin_default = defineComponent({\n  name: name,\n  props: notifyProps,\n  emits: [\"update:show\"],\n  setup: function setup(props, _ref) {\n    var emit = _ref.emit,\n      slots = _ref.slots;\n    var updateShow = function updateShow(show) {\n      return emit(\"update:show\", show);\n    };\n    return function () {\n      return _createVNode(Popup, {\n        \"show\": props.show,\n        \"class\": [bem([props.type]), props.className],\n        \"style\": {\n          color: props.color,\n          background: props.background\n        },\n        \"overlay\": false,\n        \"position\": props.position,\n        \"duration\": 0.2,\n        \"lockScroll\": props.lockScroll,\n        \"onUpdate:show\": updateShow\n      }, {\n        default: function _default() {\n          return [slots.default ? slots.default() : props.message];\n        }\n      });\n    };\n  }\n});\nexport { stdin_default as default };", "map": {"version": 3, "names": ["createVNode", "_createVNode", "defineComponent", "extend", "numericProp", "unknownProp", "makeStringProp", "createNamespace", "Popup", "popupSharedProps", "_createNamespace", "_createNamespace2", "_slicedToArray", "name", "bem", "notifyProps", "type", "color", "String", "message", "position", "className", "background", "lockScroll", "Boolean", "stdin_default", "props", "emits", "setup", "_ref", "emit", "slots", "updateShow", "show", "default", "_default"], "sources": ["C:/Users/<USER>/Desktop/vue3_3.0/node_modules/vant/es/notify/Notify.mjs"], "sourcesContent": ["import { createVNode as _createVNode } from \"vue\";\nimport { defineComponent } from \"vue\";\nimport { extend, numericProp, unknownProp, makeStringProp, createNamespace } from \"../utils/index.mjs\";\nimport { Popup } from \"../popup/index.mjs\";\nimport { popupSharedProps } from \"../popup/shared.mjs\";\nconst [name, bem] = createNamespace(\"notify\");\nconst notifyProps = extend({}, popupSharedProps, {\n  type: makeStringProp(\"danger\"),\n  color: String,\n  message: numericProp,\n  position: makeStringProp(\"top\"),\n  className: unknownProp,\n  background: String,\n  lockScroll: Boolean\n});\nvar stdin_default = defineComponent({\n  name,\n  props: notifyProps,\n  emits: [\"update:show\"],\n  setup(props, {\n    emit,\n    slots\n  }) {\n    const updateShow = (show) => emit(\"update:show\", show);\n    return () => _createVNode(Popup, {\n      \"show\": props.show,\n      \"class\": [bem([props.type]), props.className],\n      \"style\": {\n        color: props.color,\n        background: props.background\n      },\n      \"overlay\": false,\n      \"position\": props.position,\n      \"duration\": 0.2,\n      \"lockScroll\": props.lockScroll,\n      \"onUpdate:show\": updateShow\n    }, {\n      default: () => [slots.default ? slots.default() : props.message]\n    });\n  }\n});\nexport {\n  stdin_default as default\n};\n"], "mappings": ";;;;;;AAAA,SAASA,WAAW,IAAIC,YAAY,QAAQ,KAAK;AACjD,SAASC,eAAe,QAAQ,KAAK;AACrC,SAASC,MAAM,EAAEC,WAAW,EAAEC,WAAW,EAAEC,cAAc,EAAEC,eAAe,QAAQ,oBAAoB;AACtG,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,SAASC,gBAAgB,QAAQ,qBAAqB;AACtD,IAAAC,gBAAA,GAAoBH,eAAe,CAAC,QAAQ,CAAC;EAAAI,iBAAA,GAAAC,cAAA,CAAAF,gBAAA;EAAtCG,IAAI,GAAAF,iBAAA;EAAEG,GAAG,GAAAH,iBAAA;AAChB,IAAMI,WAAW,GAAGZ,MAAM,CAAC,CAAC,CAAC,EAAEM,gBAAgB,EAAE;EAC/CO,IAAI,EAAEV,cAAc,CAAC,QAAQ,CAAC;EAC9BW,KAAK,EAAEC,MAAM;EACbC,OAAO,EAAEf,WAAW;EACpBgB,QAAQ,EAAEd,cAAc,CAAC,KAAK,CAAC;EAC/Be,SAAS,EAAEhB,WAAW;EACtBiB,UAAU,EAAEJ,MAAM;EAClBK,UAAU,EAAEC;AACd,CAAC,CAAC;AACF,IAAIC,aAAa,GAAGvB,eAAe,CAAC;EAClCW,IAAI,EAAJA,IAAI;EACJa,KAAK,EAAEX,WAAW;EAClBY,KAAK,EAAE,CAAC,aAAa,CAAC;EACtBC,KAAK,WAAAA,MAACF,KAAK,EAAAG,IAAA,EAGR;IAAA,IAFDC,IAAI,GAAAD,IAAA,CAAJC,IAAI;MACJC,KAAK,GAAAF,IAAA,CAALE,KAAK;IAEL,IAAMC,UAAU,GAAG,SAAbA,UAAUA,CAAIC,IAAI;MAAA,OAAKH,IAAI,CAAC,aAAa,EAAEG,IAAI,CAAC;IAAA;IACtD,OAAO;MAAA,OAAMhC,YAAY,CAACO,KAAK,EAAE;QAC/B,MAAM,EAAEkB,KAAK,CAACO,IAAI;QAClB,OAAO,EAAE,CAACnB,GAAG,CAAC,CAACY,KAAK,CAACV,IAAI,CAAC,CAAC,EAAEU,KAAK,CAACL,SAAS,CAAC;QAC7C,OAAO,EAAE;UACPJ,KAAK,EAAES,KAAK,CAACT,KAAK;UAClBK,UAAU,EAAEI,KAAK,CAACJ;QACpB,CAAC;QACD,SAAS,EAAE,KAAK;QAChB,UAAU,EAAEI,KAAK,CAACN,QAAQ;QAC1B,UAAU,EAAE,GAAG;QACf,YAAY,EAAEM,KAAK,CAACH,UAAU;QAC9B,eAAe,EAAES;MACnB,CAAC,EAAE;QACDE,OAAO,EAAE,SAAAC,SAAA;UAAA,OAAM,CAACJ,KAAK,CAACG,OAAO,GAAGH,KAAK,CAACG,OAAO,CAAC,CAAC,GAAGR,KAAK,CAACP,OAAO,CAAC;QAAA;MAClE,CAAC,CAAC;IAAA;EACJ;AACF,CAAC,CAAC;AACF,SACEM,aAAa,IAAIS,OAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}