{"ast": null, "code": "import { createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, toDisplayString as _toDisplayString, createCommentVNode as _createCommentVNode, createTextVNode as _createTextVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, pushScopeId as _pushScopeId, popScopeId as _popScopeId } from \"vue\";\nvar _withScopeId = function _withScopeId(n) {\n  return _pushScopeId(\"data-v-7ea7349e\"), n = n(), _popScopeId(), n;\n};\nvar _hoisted_1 = {\n  class: \"home\"\n};\nvar _hoisted_2 = [\"src\"];\nvar _hoisted_3 = {\n  class: \"ktx\"\n};\nvar _hoisted_4 = {\n  class: \"b\"\n};\nvar _hoisted_5 = {\n  class: \"t\"\n};\nvar _hoisted_6 = {\n  class: \"check_money\"\n};\nvar _hoisted_7 = {\n  class: \"text\"\n};\nvar _hoisted_8 = {\n  class: \"tixian_money\"\n};\nvar _hoisted_9 = {\n  class: \"buttons\"\n};\nvar _hoisted_10 = [\"innerHTML\"];\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_van_nav_bar = _resolveComponent(\"van-nav-bar\");\n  var _component_van_radio = _resolveComponent(\"van-radio\");\n  var _component_van_radio_group = _resolveComponent(\"van-radio-group\");\n  var _component_van_field = _resolveComponent(\"van-field\");\n  var _component_van_cell_group = _resolveComponent(\"van-cell-group\");\n  var _component_van_button = _resolveComponent(\"van-button\");\n  var _component_van_form = _resolveComponent(\"van-form\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_van_nav_bar, {\n    title: _ctx.$t('msg.tikuan'),\n    \"left-arrow\": \"\",\n    onClickLeft: _cache[0] || (_cache[0] = function ($event) {\n      return _ctx.$router.go(-1);\n    }),\n    onClickRight: $setup.clickRight\n  }, {\n    right: _withCtx(function () {\n      return [_createElementVNode(\"img\", {\n        src: require('@/assets/images/self/hank/tel.png'),\n        class: \"img\",\n        alt: \"\"\n      }, null, 8 /* PROPS */, _hoisted_2)];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"title\", \"onClickRight\"]), _createVNode(_component_van_form, {\n    onSubmit: $setup.onSubmit\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_van_cell_group, {\n        inset: \"\"\n      }, {\n        default: _withCtx(function () {\n          return [_createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"div\", _hoisted_4, _toDisplayString($setup.currency) + \" \" + _toDisplayString($setup.money), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_5, _toDisplayString(_ctx.$t('msg.my_yu_e')), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_6, [_createCommentVNode(\"   <div class=\\\"text\\\">\\r\\n                  <span class=\\\"tel\\\">{{$t('msg.phone')}}</span>\\r\\n                  <span>{{tel}}</span>\\r\\n              </div> \"), _createCommentVNode(\"  <div class=\\\"text\\\">\\r\\n                  <span>{{$t('msg.input_yhxz')}}</span>\\r\\n              </div> \"), _createCommentVNode(\" <div class=\\\"text\\\">\\r\\n                <van-radio-group v-model=\\\"checked\\\">\\r\\n                    <van-radio>\\r\\n                        <div class=\\\"label\\\">{{$t('msg.khlx')}} {{bank_code}}</div>\\r\\n                    </van-radio>\\r\\n                </van-radio-group>\\r\\n              </div> \"), _createElementVNode(\"div\", _hoisted_7, [_createVNode(_component_van_radio_group, {\n            modelValue: $setup.types,\n            \"onUpdate:modelValue\": _cache[1] || (_cache[1] = function ($event) {\n              return $setup.types = $event;\n            }),\n            direction: \"horizontal\"\n          }, {\n            default: _withCtx(function () {\n              return [_createCommentVNode(\" <van-radio name=\\\"2\\\">{{$t('msg.bank_tx')}}</van-radio> \"), _createVNode(_component_van_radio, {\n                name: \"1\"\n              }, {\n                default: _withCtx(function () {\n                  return [_createTextVNode(_toDisplayString(_ctx.$t('msg.usdt_tx')), 1 /* TEXT */)];\n                }),\n\n                _: 1 /* STABLE */\n              })];\n            }),\n\n            _: 1 /* STABLE */\n          }, 8 /* PROPS */, [\"modelValue\"])]), _createCommentVNode(\"   <span class=\\\"span\\\" :class=\\\"(money_check == item&&!!money_check && 'check ') +  (!item && ' not_b')\\\" @click=\\\"function(){if(item){money_check = item}}\\\" v-for=\\\"(item,index) in moneys\\\" :key=\\\"index\\\">{{item}}</span>\\r\\n              <span class=\\\"span\\\" :class=\\\"(money_check == money && 'check not_b') \\\" @click=\\\"money_check=money\\\">{{$t('msg.all_tx')}}</span> \")]), _createElementVNode(\"div\", _hoisted_8, _toDisplayString(_ctx.$t('msg.tixian_money')), 1 /* TEXT */), _createVNode(_component_van_field, {\n            class: \"zdy\",\n            modelValue: $setup.money_check,\n            \"onUpdate:modelValue\": _cache[2] || (_cache[2] = function ($event) {\n              return $setup.money_check = $event;\n            }),\n            placeholder: _ctx.$t('msg.tixian_money')\n          }, null, 8 /* PROPS */, [\"modelValue\", \"placeholder\"]), _createVNode(_component_van_field, {\n            class: \"zdy\",\n            modelValue: $setup.paypassword,\n            \"onUpdate:modelValue\": _cache[3] || (_cache[3] = function ($event) {\n              return $setup.paypassword = $event;\n            }),\n            type: \"password\",\n            name: \"paypassword\",\n            placeholder: _ctx.$t('msg.tx_pwd'),\n            rules: [{\n              required: true,\n              message: _ctx.$t('msg.input_tx_pwd')\n            }]\n          }, null, 8 /* PROPS */, [\"modelValue\", \"placeholder\", \"rules\"])];\n        }),\n        _: 1 /* STABLE */\n      }), _createElementVNode(\"div\", _hoisted_9, [_createVNode(_component_van_button, {\n        round: \"\",\n        block: \"\",\n        type: \"primary\",\n        \"native-type\": \"submit\"\n      }, {\n        default: _withCtx(function () {\n          return [_createTextVNode(_toDisplayString(_ctx.$t('msg.true_tx')), 1 /* TEXT */)];\n        }),\n\n        _: 1 /* STABLE */\n      })]), _createElementVNode(\"div\", {\n        class: \"text_b\",\n        innerHTML: $setup.content\n      }, null, 8 /* PROPS */, _hoisted_10)];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"onSubmit\"])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_van_nav_bar", "title", "_ctx", "$t", "onClickLeft", "_cache", "$event", "$router", "go", "onClickRight", "$setup", "clickRight", "right", "_withCtx", "_createElementVNode", "src", "require", "alt", "_component_van_form", "onSubmit", "_component_van_cell_group", "inset", "_hoisted_3", "_hoisted_4", "_toDisplayString", "currency", "money", "_hoisted_5", "_hoisted_6", "_createCommentVNode", "_hoisted_7", "_component_van_radio_group", "types", "direction", "_component_van_radio", "name", "_hoisted_8", "_component_van_field", "money_check", "placeholder", "paypassword", "type", "rules", "required", "message", "_hoisted_9", "_component_van_button", "round", "block", "innerHTML", "content"], "sources": ["C:\\Users\\<USER>\\Downloads\\Telegram Desktop\\vue3_3.0\\vue3_3.0\\src\\views\\self\\components\\drawing.vue"], "sourcesContent": ["<template>\r\n  <div class=\"home\">\r\n    <van-nav-bar :title=\"$t('msg.tikuan')\" left-arrow @click-left=\"$router.go(-1)\" @click-right=\"clickRight\">\r\n        <template #right>\r\n            <img :src=\"require('@/assets/images/self/hank/tel.png')\" class=\"img\" alt=\"\">\r\n        </template>\r\n    </van-nav-bar>\r\n    <van-form @submit=\"onSubmit\">\r\n      <van-cell-group inset>\r\n          <div class=\"ktx\">\r\n              <div class=\"b\">{{currency}} {{money}}</div>\r\n              <div class=\"t\">{{$t('msg.my_yu_e')}}</div>\r\n          </div>\r\n          <div class=\"check_money\">\r\n           <!--   <div class=\"text\">\r\n                  <span class=\"tel\">{{$t('msg.phone')}}</span>\r\n                  <span>{{tel}}</span>\r\n              </div> -->\r\n            <!--  <div class=\"text\">\r\n                  <span>{{$t('msg.input_yhxz')}}</span>\r\n              </div> -->\r\n             <!-- <div class=\"text\">\r\n                <van-radio-group v-model=\"checked\">\r\n                    <van-radio>\r\n                        <div class=\"label\">{{$t('msg.khlx')}} {{bank_code}}</div>\r\n                    </van-radio>\r\n                </van-radio-group>\r\n              </div> -->\r\n              <div class=\"text\">\r\n                  <van-radio-group v-model=\"types\" direction=\"horizontal\">\r\n                   <!-- <van-radio name=\"2\">{{$t('msg.bank_tx')}}</van-radio> -->\r\n                    <van-radio name=\"1\">{{$t('msg.usdt_tx')}}</van-radio>\r\n                  </van-radio-group>\r\n              </div>\r\n           <!--   <span class=\"span\" :class=\"(money_check == item&&!!money_check && 'check ') +  (!item && ' not_b')\" @click=\"function(){if(item){money_check = item}}\" v-for=\"(item,index) in moneys\" :key=\"index\">{{item}}</span>\r\n              <span class=\"span\" :class=\"(money_check == money && 'check not_b') \" @click=\"money_check=money\">{{$t('msg.all_tx')}}</span> -->\r\n          </div>\r\n        <div class=\"tixian_money\">{{$t('msg.tixian_money')}}</div>\r\n        <van-field\r\n          class=\"zdy\"\r\n          v-model=\"money_check\"\r\n          :placeholder=\"$t('msg.tixian_money')\"\r\n        />\r\n        <van-field\r\n          class=\"zdy\"\r\n          v-model=\"paypassword\"\r\n          type=\"password\"\r\n          name=\"paypassword\"\r\n          :placeholder=\"$t('msg.tx_pwd')\"\r\n          :rules=\"[{ required: true, message: $t('msg.input_tx_pwd') }]\"\r\n        />\r\n      </van-cell-group>\r\n      <div class=\"buttons\">\r\n        <van-button round block type=\"primary\" native-type=\"submit\">\r\n          {{$t('msg.true_tx')}}\r\n        </van-button>\r\n      </div>\r\n      <div class=\"text_b\" v-html=\"content\">\r\n      </div>\r\n    </van-form>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { reactive, ref,getCurrentInstance } from 'vue';\r\nimport store from '@/store/index'\r\nimport {do_deposit, bind_bank} from '@/api/self/index.js'\r\nimport { useRouter,useRoute } from 'vue-router';\r\nimport { useI18n } from 'vue-i18n'\r\nimport {getdetailbyid} from '@/api/home/<USER>'\r\nimport { Dialog } from 'vant'\r\nexport default {\r\n  name: 'HomeView',\r\n  setup() {\r\n    const { t } = useI18n()\r\n    const { push } = useRouter();\r\n    const route = useRoute();\r\n    const {proxy} = getCurrentInstance()\r\n    const paypassword = ref('')\r\n    const info = ref({})\r\n    const currency = ref(store.state.baseInfo?.currency)\r\n    const tel = ref(store.state.userinfo?.tel)\r\n\tconst infoa = ref(store.state.objInfo)\r\n    const types = ref('1')\r\n    const content = ref('')\r\n    const bank_code = ref('')\r\n    bind_bank().then(res => {\r\n        if(res.code === 0) {\r\n            bank_code.value = res.data.info?.bank_type\r\n        }\r\n    })\r\n\r\n    getdetailbyid(14).then(res => {\r\n        content.value = res.data?.content\r\n    })\r\n\r\n    const money_check = ref()\r\n   const money = ref(store.state.userinfo?.balance)\r\n   var aa = ref(store.state.userinfo)\r\n   console.log(aa)\r\n    const moneys = ref(store.state.baseInfo?.recharge_money_list)\r\n\r\n    \r\n    const clickLeft = () => {\r\n        push('/self')\r\n    }\r\n    const clickRight = () => {\r\n        push('/tel')\r\n    }\r\n    \r\n\r\n    const onSubmit = (values) => {\r\n        console.log(values)\r\n        // if (!bank_code.value) {\r\n        // console.log(bank_code)\r\n        //     Dialog.confirm({\r\n        //         confirmButtonText:t('msg.queren'),\r\n        //         cancelButtonText:t('msg.quxiao'),\r\n        //         title: '',\r\n        //     message:\r\n        //         t('msg.tjtkxx'),\r\n        //     })\r\n        //     .then(() => {\r\n        //         // on confirm\r\n        //         push('/bingbank')\r\n        //     })\r\n        //     .catch(() => {\r\n        //         // on cancel\r\n        //     });\r\n        //     return false\r\n        // }\r\n        let json = {\r\n            num: money_check.value ==0 ? money.value : money_check.value,\r\n            type: 'bank',\r\n            paypassword: values.paypassword,\r\n            // types: types.value,\r\n        }\r\n        do_deposit(json).then(res => {\r\n            if(res.code === 0) {\r\n                proxy.$Message({ type: 'success', message:res.info});\r\n                push('/self')\r\n            } else {\r\n                proxy.$Message({ type: 'error', message:res.info});\r\n            }\r\n        })\r\n    };\r\nconsole.log(money)\r\n\r\n\r\n    return {\r\n        paypassword,\r\n        types,\r\n        onSubmit,\r\n        clickLeft,\r\n        bank_code,\r\n        clickRight,\r\n        info,\r\n        money,\r\n        currency,\r\n        money_check,\r\n        moneys,\r\n        content,\r\n\t\tinfoa,\r\n        tel\r\n    };\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n@import '@/styles/theme.scss';\r\n.home{\r\n    // background-image: url('~@/assets/images/home/<USER>') !important;\r\n    background-color: #f5f5f5;\r\n    border-radius: 0;\r\n}\r\n.home{\r\n    :deep(.van-nav-bar){\r\n        background-color: #fff;\r\n        // background-color: $theme;\r\n        color: #000;\r\n        .van-nav-bar__left{\r\n            .van-icon{\r\n                color: #000;\r\n            }\r\n        }\r\n        .van-nav-bar__title{\r\n            color: #000;\r\n            \r\n        }\r\n        .van-nav-bar__right{\r\n            img{\r\n                height: 42px;\r\n            }\r\n        }\r\n    }\r\n    :deep(.van-form){\r\n        padding: 40px 0 0;\r\n\r\n        .van-cell.van-cell--clickable{\r\n            border-left: 5px solid $theme;\r\n            padding: 32px;\r\n            text-align: left;\r\n            margin: 20px 0;\r\n            border-bottom: none;\r\n            box-shadow: $shadow;\r\n            .van-cell__right-icon{\r\n                color: $theme;\r\n            }\r\n        }\r\n        .van-cell-group--inset{\r\n            padding: 0 30px;\r\n            background-color: initial;\r\n        }\r\n        .van-cell{\r\n            padding: 23px 10px;\r\n            border-bottom: 1px solid  var(--van-cell-border-color);\r\n            &.zdy {\r\n                margin-bottom: 20px;\r\n                border-radius: 40px;\r\n                padding-left: 30px;\r\n            }\r\n            .van-field__left-icon{\r\n                width:90px;\r\n                text-align: center;\r\n                .van-icon__image{\r\n                    height: 42px;\r\n                    width: auto;\r\n                }\r\n                .icon{\r\n                    height: 42px;\r\n                    width: auto;\r\n                    vertical-align:middle;\r\n                }\r\n                .van-dropdown-menu{\r\n                  .van-dropdown-menu__bar{\r\n                    height: auto;\r\n                    background: none;\r\n                    box-shadow: none;\r\n                  }\r\n                  .van-cell{\r\n                    padding: 30px 80px;\r\n                  }\r\n                }\r\n            }\r\n            .van-field__control{\r\n                font-size: 24px;\r\n            }\r\n            &::after {\r\n                display: none;\r\n            }\r\n        }\r\n        .van-checkbox{\r\n            margin: 30px 0 60px 0;\r\n            .van-checkbox__icon{\r\n                font-size: 50px;\r\n                margin-right: 80px;\r\n                &.van-checkbox__icon--checked .van-icon{\r\n                    background-color:$theme;\r\n                    border-color:$theme;\r\n                }\r\n            }\r\n            .van-checkbox__label{\r\n                font-size: 24px;\r\n            }\r\n        }\r\n        .text_b{\r\n            margin:70px 60px 40px;\r\n            font-size: 27px;\r\n            color: #333;\r\n            text-align: left;\r\n            line-height: 1.5;\r\n            .tex{\r\n                margin-top: 20px;\r\n            }\r\n        }\r\n        .buttons{\r\n            padding: 0 76px;\r\n            .van-button{\r\n                font-size: 28px;\r\n                padding: 20px 0;\r\n                height: auto;\r\n                background: #000;\r\n                border: none;\r\n                color: #fff;\r\n            }\r\n            .van-button--plain{\r\n                margin-top: 40px;\r\n            }\r\n        }\r\n        .tixian_money{\r\n            text-align: left;\r\n            font-size: 30px;\r\n            margin-bottom: 20px;\r\n            color: #333;\r\n        }\r\n        .ktx{\r\n            width: 100%;\r\n            height: 190px;\r\n            border-radius: 20px;\r\n            padding: 24px 50px;\r\n            text-align: left;\r\n            // margin-bottom: 35px;\r\n            background-color: #fe2c55;\r\n            text-align: center;\r\n            .t{\r\n                font-size: 20px;\r\n                color: #fff;\r\n                margin-bottom: 10px;\r\n                opacity: 0.7;\r\n            }\r\n            .b{\r\n                font-size: 50px;\r\n                color: #fefefe;\r\n                margin-bottom: 20px;\r\n            }\r\n        }\r\n        .check_money{\r\n            display: flex;\r\n            flex-wrap: wrap;\r\n            margin-bottom: 40px;\r\n            background-color: #fff;\r\n            padding: 24px;\r\n            border-radius: 20px;\r\n            color: #333;\r\n            .text{\r\n                display: flex;\r\n                width: 100%;\r\n                text-align: left;\r\n                font-size: 28px;\r\n                margin-bottom: 25px;\r\n                span{\r\n                    flex: 1;\r\n                    &.tel{\r\n                        color: #999;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    :deep(.van-){\r\n        .van-dialog__content{\r\n            padding: 50px;\r\n        }\r\n        .van-dialog__footer{\r\n            .van-dialog__confirm{\r\n                color: $theme;\r\n            }\r\n        }\r\n    }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;EACOA,KAAK,EAAC;AAAM;;;EAQJA,KAAK,EAAC;AAAK;;EACPA,KAAK,EAAC;AAAG;;EACTA,KAAK,EAAC;AAAG;;EAEbA,KAAK,EAAC;AAAa;;EAefA,KAAK,EAAC;AAAM;;EASlBA,KAAK,EAAC;AAAc;;EAetBA,KAAK,EAAC;AAAS;;;;;;;;;;uBAnDxBC,mBAAA,CA2DM,OA3DNC,UA2DM,GA1DJC,YAAA,CAIcC,sBAAA;IAJAC,KAAK,EAAEC,IAAA,CAAAC,EAAE;IAAgB,YAAU,EAAV,EAAU;IAAEC,WAAU,EAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAAEJ,IAAA,CAAAK,OAAO,CAACC,EAAE;IAAA;IAAOC,YAAW,EAAEC,MAAA,CAAAC;;IAC9EC,KAAK,EAAAC,QAAA,CACZ;MAAA,OAA4E,CAA5EC,mBAAA,CAA4E;QAAtEC,GAAG,EAAEC,OAAO;QAAuCpB,KAAK,EAAC,KAAK;QAACqB,GAAG,EAAC;;;;gDAGjFlB,YAAA,CAoDWmB,mBAAA;IApDAC,QAAM,EAAET,MAAA,CAAAS;EAAQ;sBACzB;MAAA,OA2CiB,CA3CjBpB,YAAA,CA2CiBqB,yBAAA;QA3CDC,KAAK,EAAL;MAAK;0BACjB;UAAA,OAGM,CAHNP,mBAAA,CAGM,OAHNQ,UAGM,GAFFR,mBAAA,CAA2C,OAA3CS,UAA2C,EAAAC,gBAAA,CAA1Bd,MAAA,CAAAe,QAAQ,IAAE,GAAC,GAAAD,gBAAA,CAAEd,MAAA,CAAAgB,KAAK,kBACnCZ,mBAAA,CAA0C,OAA1Ca,UAA0C,EAAAH,gBAAA,CAAzBtB,IAAA,CAAAC,EAAE,gC,GAEvBW,mBAAA,CAuBM,OAvBNc,UAuBM,GAtBLC,mBAAA,kKAGa,EACZA,mBAAA,8GAEY,EACXA,mBAAA,+SAMW,EACVf,mBAAA,CAKM,OALNgB,UAKM,GAJF/B,YAAA,CAGkBgC,0BAAA;wBAHQrB,MAAA,CAAAsB,KAAK;;qBAALtB,MAAA,CAAAsB,KAAK,GAAA1B,MAAA;YAAA;YAAE2B,SAAS,EAAC;;8BAC1C;cAAA,OAA8D,CAA9DJ,mBAAA,6DAA8D,EAC7D9B,YAAA,CAAqDmC,oBAAA;gBAA1CC,IAAI,EAAC;cAAG;kCAAC;kBAAA,OAAqB,C,kCAAnBjC,IAAA,CAAAC,EAAE,gC;;;;;;;;+CAGjC0B,mBAAA,sXACkI,C,GAErIf,mBAAA,CAA0D,OAA1DsB,UAA0D,EAAAZ,gBAAA,CAA9BtB,IAAA,CAAAC,EAAE,sCAC9BJ,YAAA,CAIEsC,oBAAA;YAHAzC,KAAK,EAAC,KAAK;wBACFc,MAAA,CAAA4B,WAAW;;qBAAX5B,MAAA,CAAA4B,WAAW,GAAAhC,MAAA;YAAA;YACnBiC,WAAW,EAAErC,IAAA,CAAAC,EAAE;kEAElBJ,YAAA,CAOEsC,oBAAA;YANAzC,KAAK,EAAC,KAAK;wBACFc,MAAA,CAAA8B,WAAW;;qBAAX9B,MAAA,CAAA8B,WAAW,GAAAlC,MAAA;YAAA;YACpBmC,IAAI,EAAC,UAAU;YACfN,IAAI,EAAC,aAAa;YACjBI,WAAW,EAAErC,IAAA,CAAAC,EAAE;YACfuC,KAAK;cAAAC,QAAA;cAAAC,OAAA,EAA8B1C,IAAA,CAAAC,EAAE;YAAA;;;;UAG1CW,mBAAA,CAIM,OAJN+B,UAIM,GAHJ9C,YAAA,CAEa+C,qBAAA;QAFDC,KAAK,EAAL,EAAK;QAACC,KAAK,EAAL,EAAK;QAACP,IAAI,EAAC,SAAS;QAAC,aAAW,EAAC;;0BACjD;UAAA,OAAqB,C,kCAAnBvC,IAAA,CAAAC,EAAE,gC;;;;YAGRW,mBAAA,CACM;QADDlB,KAAK,EAAC,QAAQ;QAACqD,SAAgB,EAARvC,MAAA,CAAAwC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}