{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _Calendar from \"./Calendar.mjs\";\nvar Calendar = withInstall(_Calendar);\nvar stdin_default = Calendar;\nexport { Calendar, stdin_default as default };", "map": {"version": 3, "names": ["withInstall", "_Calendar", "Calendar", "stdin_default", "default"], "sources": ["C:/Users/<USER>/Downloads/Telegram Desktop/vue3_3.0/vue3_3.0/node_modules/vant/es/calendar/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _Calendar from \"./Calendar.mjs\";\nconst Calendar = withInstall(_Calendar);\nvar stdin_default = Calendar;\nexport {\n  Calendar,\n  stdin_default as default\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,SAAS,MAAM,gBAAgB;AACtC,IAAMC,QAAQ,GAAGF,WAAW,CAACC,SAAS,CAAC;AACvC,IAAIE,aAAa,GAAGD,QAAQ;AAC5B,SACEA,QAAQ,EACRC,aAAa,IAAIC,OAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}